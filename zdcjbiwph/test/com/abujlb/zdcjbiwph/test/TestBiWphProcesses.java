package com.abujlb.zdcjbiwph.test;

import com.abujlb.BaseTest;
import com.abujlb.zdcjbiwph.bean.BiInfo;
import com.abujlb.zdcjbiwph.bean.BiInfoOther;
import com.abujlb.zdcjbiwph.bean.BiYhdp;
import com.abujlb.zdcjbiwph.bean.DataprobiWphMsg;
import com.abujlb.zdcjbiwph.cookie.AbujlbCookieStore;
import com.abujlb.zdcjbiwph.cookie.Cjzh;
import com.abujlb.zdcjbiwph.http.DataUploader;
import com.abujlb.zdcjbiwph.http.ShopAccountHttpClient;
import com.abujlb.zdcjbiwph.oss.BiDataOssUtil;
import com.abujlb.zdcjbiwph.processes.*;
import com.abujlb.zdcjbiwph.thread.BiThreadLocalObjects;
import org.apache.commons.lang3.StringUtils;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.lang.reflect.InvocationTargetException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

public class TestBiWphProcesses extends BaseTest {


    String cookiekey = "wph_ck_c62af440495c4cd596137e42a722fbf0";
    Cjzh cjzh = null;
    BiInfo biInfo = null;
    @Autowired
    private AbujlbCookieStore abujlbCookieStore;

    @Autowired
    private DefaultBiSplbProcess defaultBiSplbProcess;

    @Autowired
    private DefaultBiCtspglProcess defaultBiCtspglProcess;

    @Autowired
    private DefaultBiBillProcess defaultBiBillProcess;

    @Autowired
    private DefaultBiYwfydProcess defaultBiYwfydProcess;

    @Autowired
    private DefaultBiSpxgProcess defaultBiSpxgProcess;

    @Autowired
    private DefaultBiWxkyjProcess defaultBiWxkyjProcess;

    @Autowired
    private DefaultBiWxkJljProcess defaultBiWxkJljProcess;

    @Autowired
    private DefaultBiWphYxpthfProcess defaultBiWphYxptProcess;

    @Autowired
    private DefaultBiZwJlyqjljProcess defaultBiZwJlyqjljProcess;

    @Autowired
    private DefaultBiZwAqyjljProcess defaultBiZwAqyjljProcess;

    @Autowired
    private DefaultBiWphYxpthfProcess defaultBiWphYxpthfProcess;

    @Autowired
    private DefaultBiTargetMaxjljProcess defaultBiTargetMaxjljProcess;

    @Autowired
    private DefaultBiZnggjljProcess defaultBiZnggjljProcess;

    @Autowired
    private BiDataOssUtil biDataOssUtil;

    @Autowired
    private ShopAccountHttpClient shopAccountHttpClient;

    @Autowired
    private DefaultBiLmkdProcess defaultBiLmkdProcess;

    @Before
    public void init() {
        cjzh = abujlbCookieStore.getCjzhForKey(cookiekey);
        if (cjzh == null) {
            return;
        }

        cjzh.setMsgType(1);
        //cjzh.setAdvertiserId("CwBrTpRl");
        // 判断是否切换店铺
        if (cjzh.getOtherdata().get("listFlag").equals("1")) {
            Map<String, String> data = cjzh.getOtherdata();
            try {
                if (data == null || !data.containsKey("storeId") || !data.containsKey("storeId")) {
                    return;
                }

                String storeId = data.get("storeId");
                String storeName = data.get("storeName");

                if (StringUtils.isBlank(storeId) || StringUtils.isBlank(storeName)) {
                    return;
                }

                String result = shopAccountHttpClient.switchStoreId(cjzh, storeId, storeName);
                if (result != null) {
                } else {
                    return;
                }
            } catch (Exception e) {
            }

        }
        biInfo = DataUploader.getBiInfoJlbdpid(cjzh.getUserid(), cjzh.getId());
        List<BiYhdp> biYhdps = DataUploader.getBiYhdpList(cjzh.getUserid() + "", biInfo.getQd());
        cjzh.setBiYhdps(biYhdps);

        // 查询营销平台账号
        List<BiInfoOther> yxptBiInfos = DataUploader.getYxptBiInfo(cjzh.getId());
        if (yxptBiInfos != null && !yxptBiInfos.isEmpty()) {
            BiInfoOther biInfoOther = yxptBiInfos.get(0);
            Cjzh yxpt = abujlbCookieStore.getYxptCjzhForUserId(biInfoOther.getUserid());
            if (null != yxpt) {
                cjzh.setYxptcjzh(yxpt);
                cjzh.setAdvertiserId(yxpt.getAdvertiserId());
            }
        }


        BiThreadLocalObjects.addCjzh(cjzh);
        biInfo.setSplb(0);
        BiThreadLocalObjects.addBiInfo(biInfo);

        DataprobiWphMsg dataprobiMsg = new DataprobiWphMsg();
        try {
            dataprobiMsg.initial();
            dataprobiMsg.setSplb(0);
        } catch (NoSuchMethodException e) {
            throw new RuntimeException(e);
        } catch (InvocationTargetException e) {
            throw new RuntimeException(e);
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        }
        BiThreadLocalObjects.addMsgBean(dataprobiMsg);


    }

    /**
     * 采集商品列表
     */
    @Test
    public void cjsplb() {
        BiThreadLocalObjects.getBiInfo().getCjrq().setSplb("2025-04-01");
        defaultBiSplbProcess.cjAll();
        System.out.println("采集商品列表数据完成");
    }

    /**
     * 采集常态商品列表
     */
    @Test
    public void cjctsplb() {
        BiThreadLocalObjects.getBiInfo().getCjrq().setCtsplb("2025-04-01");
        defaultBiCtspglProcess.cjAll();
        System.out.println("采集常态商品列表数据完成");
    }

    /**
     * 采集账单管理
     */
    @Test
    public void cjzdgl() {
        BiThreadLocalObjects.getBiInfo().getCjrq().setZdgl("2025-04-30");
        defaultBiBillProcess.cjAll();
        System.out.println("采集账单管理数据完成");
    }

    /**
     * 采集业务费用单
     */
    @Test
    public void cjywfyd() {
        BiThreadLocalObjects.getBiInfo().getCjrq().setYwfyd("2025-03-06");
        defaultBiYwfydProcess.cjAll();
        System.out.println("采集商品列表数据完成");
    }

    /**
     * 采集商品效果
     */
    @Test
    public void cjspxg() {
        BiThreadLocalObjects.getBiInfo().getCjrq().setSpxg("2025-07-22");
        BiThreadLocalObjects.getBiInfo().getCjrq().setSpxgdp("2025-07-22");
        defaultBiSpxgProcess.cjAll();
        System.out.println("采集商品列表数据完成");
    }

    /**
     * 采集唯享客佣金服务费
     */
    @Test
    public void cjwxkyj() {
        BiThreadLocalObjects.getBiInfo().getCjrq().setWxkyjyghf("2025-06-22");
        defaultBiWxkyjProcess.cjAll();
        System.out.println("采集wxk佣金服务费数据完成");
    }

    /**
     * 采集唯享客奖励金
     */
    @Test
    public void cjwxkjlj() {
        BiThreadLocalObjects.getBiInfo().getCjrq().setWxkjljdp("2025-04-20");
        defaultBiWxkJljProcess.cjAll();
        System.out.println("采集wxk佣金服务费数据完成");
    }

    /**
     * 站外广告-巨量引擎奖励金
     */
    @Test
    public void cjjlyqjlj() {
        BiThreadLocalObjects.getBiInfo().getCjrq().setZwjlyqjljdp("2025-04-20");
        defaultBiZwJlyqjljProcess.cjAll();
        System.out.println("采集站外广告-巨量引擎奖励金数据完成");
    }

    /**
     * 营销平台花费
     */
    @Test
    public void cjyxpthf() {
        BiThreadLocalObjects.getBiInfo().getCjrq().setYxpthfdp("2025-04-30");
        defaultBiWphYxpthfProcess.cjAll();
        System.out.println("采集营销平台花费数据完成");
    }

    /**
     * target-max奖励金花费
     */
    @Test
    public void cjtmhf() {
        BiThreadLocalObjects.getBiInfo().getCjrq().setTmjljdp("2025-06-31");
        defaultBiTargetMaxjljProcess.cjAll();
        System.out.println("采集target-max奖励金花费花费数据完成");
    }

    /**
     * 站内广告奖励金
     */
    @Test
    public void cjznggjlj() {
        BiThreadLocalObjects.getBiInfo().getCjrq().setZnggjljdp("2025-07-20");
        defaultBiZnggjljProcess.cjAll();
        System.out.println("站内广告奖励金数据完成");
    }

    /**
     * 站外广告-爱奇艺奖励金（收入项）
     */
    @Test
    public void cjjaqyjlj() {
        BiThreadLocalObjects.getBiInfo().getCjrq().setZwaqyjljdp("2025-04-20");
        defaultBiZwAqyjljProcess.cjAll();
        System.out.println("采集站外广告-爱奇艺奖励金数据完成");
    }

    @Test
    public void lmkd() {
        defaultBiLmkdProcess.cjAll();
    }

    /**
     * 批量删除OSS文件
     * 读取并删除oss://ecbisjson2/bi_data/day/20250101/21385/WPH/94DBD46499F9E32F83B038A384CBB2C3/下的特定文件
     * 需要删除的文件：sycm_data.db、sycm_dp.db、sycm_sp.db、ywfyd.xlsx、zdgl.xlsx
     * 日期范围从20250101到20250429
     */
    @Test
    public void batchDeleteOssFiles() {
        String baseOssPath = "bi_data/day/";
        String dpid = "21385";
        String platformType = "WPH";
        String userId = "94DBD46499F9E32F83B038A384CBB2C3";

        // 需要删除的文件列表
        String[] specificFiles = {
                "sycm_dp.db",
                "sycm_sp.db",
                "ywfyd.xlsx",
                "zdgl.xlsx"
        };

        LocalDate startDate = LocalDate.of(2025, 1, 1);
        LocalDate endDate = LocalDate.of(2025, 4, 29);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");

        LocalDate currentDate = startDate;
        int totalDeletedCount = 0;
        int processedDaysCount = 0;

        while (!currentDate.isAfter(endDate)) {
            String dateStr = currentDate.format(formatter);
            String ossDirPath = baseOssPath + dateStr + "/" + dpid + "/" + platformType + "/" + userId + "/";

            // 检查目录是否存在
            System.out.println("处理目录: " + ossDirPath);
            processedDaysCount++;

            int dayDeletedCount = 0;
            // 删除指定的文件
            for (String fileName : specificFiles) {
                String fullFilePath = ossDirPath + fileName;
                if (biDataOssUtil.exist(fullFilePath)) {
                    // biDataOssUtil.delete(fullFilePath);
                    dayDeletedCount++;
                    totalDeletedCount++;
                    System.out.println("已删除文件: " + fullFilePath);
                }
            }

            System.out.println("日期 " + dateStr + " 处理完成，删除了 " + dayDeletedCount + " 个文件");


            // 移动到下一天
            currentDate = currentDate.plusDays(1);
        }

        System.out.println("批量删除完成，共处理 " + processedDaysCount + " 天的数据，删除 " + totalDeletedCount + " 个文件");
    }
}
