package com.abujlb.zdcjbiwph.test;

import com.abujlb.AbujlbBeanFactory;
import com.abujlb.BaseTest;
import com.abujlb.zdcjbiwph.service.BiWphService;
import com.abujlb.zdcjbiwph.bean.JysjMsg;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class TestBiWphService extends BaseTest {

    @Autowired
    private AbujlbBeanFactory abujlbBeanFactory;

    @Test
    public void wph() {
        JysjMsg jysjMsg = new JysjMsg();
        jysjMsg.setCookieKey("wph_ck_e32b90542033433ea3210a505722fa97");
        //1登录/上午补发 2下午补采
        jysjMsg.setType(1);
        BiWphService biService = abujlbBeanFactory.getBean(BiWphService.class);
        biService.process(jysjMsg);
    }
}
