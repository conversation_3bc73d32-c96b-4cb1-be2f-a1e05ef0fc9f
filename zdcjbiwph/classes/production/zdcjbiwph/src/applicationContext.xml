<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	   xmlns:context="http://www.springframework.org/schema/context"
	   xmlns:mvc="http://www.springframework.org/schema/mvc" xmlns:task="http://www.springframework.org/schema/task"
	   xmlns:aop="http://www.springframework.org/schema/aop"
	   xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
        http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-4.1.xsd
        http://www.springframework.org/schema/mvc http://www.springframework.org/schema/mvc/spring-mvc-4.1.xsd
        http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-4.1.xsd
        http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd">


	<context:component-scan base-package="com.abujlb,com.abujlb.zdcjbiwph"/>
	<aop:aspectj-autoproxy proxy-target-class="true"/>
	<mvc:default-servlet-handler/>
	<task:annotation-driven/>
	<mvc:annotation-driven>
		<mvc:message-converters register-defaults="true">
			<bean class="org.springframework.http.converter.StringHttpMessageConverter">
				<constructor-arg value="UTF-8"/>
			</bean>
		</mvc:message-converters>
		<mvc:argument-resolvers>
			<ref bean="ServiceDataResolver" />
		</mvc:argument-resolvers>
	</mvc:annotation-driven>
	
	<bean class="com.abujlb.service.ServiceDataResolver" id="ServiceDataResolver" />
	
	<bean class="com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImplExporter" />
	<bean
		class="org.springframework.web.servlet.view.InternalResourceViewResolver"
		id="internalResourceViewResolver">
		<property name="prefix" value="/WEB-INF/jsp/" />
		<property name="suffix" value=".jsp" />
	</bean>
	<bean id="multipartResolver"
		class="org.springframework.web.multipart.commons.CommonsMultipartResolver">
		<property name="maxUploadSize" value="104857600" />
		<property name="maxInMemorySize" value="4096" />
		<property name="defaultEncoding" value="UTF-8"></property>
	</bean>
	<mvc:interceptors>
		<mvc:interceptor>
			<mvc:mapping path="/**" />
			<bean class="com.abujlb.interceptor.SpringContextInterceptor"></bean>
		</mvc:interceptor>
	</mvc:interceptors>
</beans>
