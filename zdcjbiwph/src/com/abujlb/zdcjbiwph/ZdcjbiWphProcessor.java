package com.abujlb.zdcjbiwph;

import com.abujlb.AbujlbBeanFactory;
import com.abujlb.SpringBeanUtil;
import com.abujlb.annotation.MqProcessor;
import com.abujlb.mq.JobMessage;
import com.abujlb.mq.MqProcessorInterface;
import com.abujlb.zdcjbiwph.bean.JysjMsg;
import com.abujlb.zdcjbiwph.service.BiWphService;
import com.abujlb.zdcjbiwph.thread.BiThreadLocalObjects;
import com.abujlb.zdcjbiwph.util.BiDateUtil;
import com.google.gson.Gson;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;


/**
 * <AUTHOR> wuzhaoniu
 * create at:  2024/8/1 11:39 上午
 * @description:
 */
@MqProcessor(type = "zdcjbiwphprocesser")
public class ZdcjbiWphProcessor implements MqProcessorInterface {
    private static final Logger log = Logger.getLogger(ZdcjbiWphProcessor.class);
    final Gson gson = new Gson();
    @Autowired
    private AbujlbBeanFactory abujlbBeanFactory;
    @Autowired
    private BiWphService biWphService;

    @Override
    public boolean process(JobMessage msg) {
        log.info("=============>>>>>zdcjbiwph");
        log.info("当前时间：" + BiDateUtil.getCurrtime());
        log.info("zdcjbiwph-->消息体：" + msg.getMsgBody());

        JysjMsg jysjMsg = null;
        try {
            jysjMsg = gson.fromJson(msg.getMsgBody(), JysjMsg.class);
        } catch (Exception e) {
            return true;
        }

        try {
            SpringBeanUtil.setBeanFactory(abujlbBeanFactory);
            return biWphService.process(jysjMsg);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            BiThreadLocalObjects.removeAll();
        }
        return true;
    }
}

