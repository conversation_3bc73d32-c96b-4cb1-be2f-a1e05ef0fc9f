package com.abujlb.zdcjbiwph.processes;

import com.abujlb.zdcjbiwph.annos.BiPro;
import com.abujlb.zdcjbiwph.auth.BiAuthResult;
import com.abujlb.zdcjbiwph.bean.BiDpConfig;
import com.abujlb.zdcjbiwph.bean.BiInfo;
import com.abujlb.zdcjbiwph.constant.BiModuleConstant;
import com.abujlb.zdcjbiwph.constant.BiProcessIndex;
import com.abujlb.zdcjbiwph.constant.CodeConst;
import com.abujlb.zdcjbiwph.cookie.Cjzh;
import com.abujlb.zdcjbiwph.http.TghfHttpClient;
import com.abujlb.zdcjbiwph.thread.BiThreadLocalObjects;
import com.abujlb.zdcjbiwph.util.BiDateUtil;
import com.abujlb.zdcjbiwph.util.XlsParse;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;


/**
 * 唯享客佣金预估花费
 */
@Component
@BiPro(value = BiProcessIndex.WXKYJ, authType = BiModuleConstant.WXKYJ)
public class DefaultBiWxkyjProcess extends AbstractBiProcess {

    private static final Logger LOG = Logger.getLogger(DefaultBiWxkyjProcess.class);
    private static final int TASK_COMPLETE_STATUS = 2;
    private static final int MAX_RETRY_COUNT = 5;
    private static final int RETRY_INTERVAL_SECONDS = 10;
    private static final int ORDER_TYPE_ZSZS = 9;
    private static final int ORDER_TYPE_PTB = 4;

    
    @Autowired
    private TghfHttpClient tghfHttpClient;


    /**
     *  需求：唯品联盟（也叫唯享客）-> 订单明细
     *
     * <ul>
     *     <li>采集地址：https://e.vip.com/upgrade.html#/promotion/alliances/wxk/order</li>
     *     <li>采集页面描述：唯品联盟（也叫唯享客）-> 订单明细，目前仅采集"普通版"、"专属招商"，</li>
     *     <li>采集方式：接口采集</li>
     *     <li>采集选项：按照每天去采集</li>
     * </ul>
     */

    @Override
    public void cj() {
        List<String> rqList = getCjzh().getCjrqMapRqList(BiModuleConstant.WXKYJ);
        if (CollectionUtils.isEmpty(rqList)) {
            return;
        }

        // 保存原始的rqList，用于后续更新处理状态
        List<String> originalRqList = new ArrayList<>(rqList);

        // 获取昨天的日期
        String yesterday = BiDateUtil.getLastday();

        // 判断最后一个日期是否是昨天且需要补充30天的数据
        if (!rqList.isEmpty() && rqList.get(rqList.size() - 1).equals(yesterday) && rqList.size() < 31) {
            // 需要补充的天数
            int needDays = 31 - rqList.size();
            // 已有的日期，防止重复添加
            for (int i = 1; i <= needDays + 2; i++) {
                String previousDay = BiDateUtil.getDayBefore(yesterday, i);
                if (!rqList.contains(previousDay)) {
                    // 添加到列表开头保持时间顺序
                    rqList.add(0, previousDay);
                    // 如果已补足所需日期
                    if (rqList.size() >= 31) {
                        break;
                    }
                }
            }
        }

        // 如果rqList长度大于1，则取第一个和最后一个rq作为开始结束时间进行批量处理
        String startDate = rqList.get(0);
        String endDate = rqList.get(rqList.size() - 1);

        if (processDateRange(startDate, endDate, rqList,originalRqList)) {
            // 批量处理成功，更新所有日期的处理状态（使用原始的rqList）
            for (String rq : originalRqList) {
                updateProcessedDate(rq);
            }
        }
    }

    /**
     * 处理日期范围数据（批量处理）
     */
    private boolean processDateRange(String startDate, String endDate, List<String> rqList,List<String> originalRqList) {
        try {
            // 下载专属招商Excel文件（使用日期范围）
            String zszsFilePath = downloadOrderExcelRange(startDate, endDate, ORDER_TYPE_ZSZS, "wxkddmx_zszs_range");
            if (zszsFilePath == null) {
                return false;
            }

            // 下载普通版Excel文件（使用日期范围）
            String ptbFilePath = downloadOrderExcelRange(startDate, endDate, ORDER_TYPE_PTB, "wxkddmx_ptb_range");
            if (ptbFilePath == null) {
                return false;
            }

            // 解析Excel并处理数据
            JSONArray zszsData = parseAndTransformData(zszsFilePath, ORDER_TYPE_ZSZS);
            JSONArray ptbData = parseAndTransformData(ptbFilePath, ORDER_TYPE_PTB);

            if (zszsData == null || ptbData == null) {
                return false;
            }

            // 合并数据
            JSONArray mergedData = new JSONArray();
            mergedData.addAll(zszsData);
            mergedData.addAll(ptbData);

            // 为每个日期上传相同的合并数据
            for (String rq : originalRqList) {
                uploadData(rq, mergedData);
            }

            return true;
        } catch (Exception e) {
            LOG.error("处理日期范围数据失败: " + startDate + " 到 " + endDate, e);
            return false;
        }
    }

    private boolean processDate(String rq) {
        try {
            // 下载专属招商Excel文件
            String zszsFilePath = downloadOrderExcel(rq, ORDER_TYPE_ZSZS, "wxkddmx_zszs");
            if (zszsFilePath == null) {
                return false;
            }

            // 下载普通版Excel文件
            String ptbFilePath = downloadOrderExcel(rq, ORDER_TYPE_PTB, "wxkddmx_ptb");
            if (ptbFilePath == null) {
                return false;
            }

            // 解析Excel并处理数据
            JSONArray zszsData = parseAndTransformData(zszsFilePath, ORDER_TYPE_ZSZS);
            JSONArray ptbData = parseAndTransformData(ptbFilePath, ORDER_TYPE_PTB);

            if (zszsData == null || ptbData == null) {
                return false;
            }

            // 合并数据
            JSONArray mergedData = new JSONArray();
            mergedData.addAll(zszsData);
            mergedData.addAll(ptbData);

            // 上传数据并更新处理日期
            uploadData(rq, mergedData);
            updateProcessedDate(rq);
            return true;
        } catch (Exception e) {
            LOG.error("处理日期数据失败: " + rq, e);
            return false;
        }
    }
    
    /**
     * 下载订单Excel文件（日期范围）
     */
    private String downloadOrderExcelRange(String startDate, String endDate, int orderType, String fileNameSuffix) throws InterruptedException {
        // 步骤1：创建导出任务（使用日期范围）
        String taskId = tghfHttpClient.createExportTask(getCjzh(), startDate, endDate, orderType);
        if (taskId == null || taskId.isEmpty()) {
            LOG.error("创建导出任务失败: " + startDate + " 到 " + endDate + ", 订单类型: " + orderType);
            return null;
        }
        LOG.info("成功创建导出任务，taskId: " + taskId + ", 日期范围: " + startDate + " 到 " + endDate + ", 订单类型: " + orderType);

        // 等待任务处理
        TimeUnit.SECONDS.sleep(RETRY_INTERVAL_SECONDS);

        // 步骤2：等待任务完成
        if (!waitForTaskCompletion(taskId)) {
            return null;
        }

        // 生成自定义文件名
        String startDateStr = startDate.replace("-", "");
        String endDateStr = endDate.replace("-", "");
        String fileName = startDateStr + "_" + endDateStr + "_" + getCjzh().getUserid() + "_" + fileNameSuffix;

        // 步骤3：下载Excel文件
        String filePath = tghfHttpClient.downloadZdExcel(getCjzh(), taskId, fileName);
        if (filePath == null || filePath.isEmpty()) {
            LOG.error("下载Excel文件失败, 订单类型: " + orderType);
            return null;
        }
        LOG.info("成功下载Excel文件: " + filePath + ", 订单类型: " + orderType);

        return filePath;
    }

    /**
     * 下载订单Excel文件
     */
    private String downloadOrderExcel(String rq, int orderType, String fileNameSuffix) throws InterruptedException {
        // 步骤1：创建导出任务
        String taskId = tghfHttpClient.createExportTask(getCjzh(), rq, orderType);
        if (taskId == null || taskId.isEmpty()) {
            LOG.error("创建导出任务失败: " + rq + ", 订单类型: " + orderType);
            return null;
        }
        LOG.info("成功创建导出任务，taskId: " + taskId + ", 订单类型: " + orderType);

        // 等待任务处理
        TimeUnit.SECONDS.sleep(RETRY_INTERVAL_SECONDS);

        // 步骤2：等待任务完成
        if (!waitForTaskCompletion(taskId)) {
            return null;
        }

        // 生成自定义文件名
        String date = rq.replace("-", "");
        String fileName = date + "_" + getCjzh().getUserid() + "_" + fileNameSuffix;

        // 步骤3：下载Excel文件
        String filePath = tghfHttpClient.downloadZdExcel(getCjzh(), taskId, fileName);
        if (filePath == null || filePath.isEmpty()) {
            LOG.error("下载Excel文件失败, 订单类型: " + orderType);
            return null;
        }
        LOG.info("成功下载Excel文件: " + filePath + ", 订单类型: " + orderType);

        return filePath;
    }
    
    /**
     * 等待任务完成
     */
    private boolean waitForTaskCompletion(String taskId) throws InterruptedException {
        int retryCount = 0;
        
        while (retryCount < MAX_RETRY_COUNT) {
            JSONArray taskList = tghfHttpClient.queryTaskList(getCjzh(), taskId);
            if (taskList == null || taskList.isEmpty()) {
                LOG.error("查询任务列表失败");
                return false;
            }
            
            JSONObject taskInfo = taskList.getJSONObject(0);
            int taskStatus = taskInfo.getIntValue("taskStatus");
            
            if (taskStatus == TASK_COMPLETE_STATUS) {
                return true;
            }
            
            LOG.info("任务未完成，状态: " + taskStatus + "，等待后重试...");
            TimeUnit.SECONDS.sleep(RETRY_INTERVAL_SECONDS);
            retryCount++;
        }
        
        LOG.error("任务未在规定时间内完成，已达到最大重试次数");
        return false;
    }
    
    /**
     * 解析Excel并转换数据
     */
    private JSONArray parseAndTransformData(String filePath, int orderType) {
        // 解析Excel文件
        JSONArray jsonArray = XlsParse.readExcelToJsonArray(filePath);
        if (jsonArray == null) {
            LOG.error("Excel解析失败, 订单类型: " + orderType);
            return null;
        }
        LOG.info("成功解析Excel，记录数: " + jsonArray.size() + ", 订单类型: " + orderType);

        // 获取商品映射关系
        Map<String, String> skuToBbidMap = getSkuToBbidMapping();
        if (skuToBbidMap.isEmpty()) {
            LOG.error("获取商品映射关系失败");
            return null;
        }

        // 转换数据格式
        JSONArray transformedArray = new JSONArray();
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject originalObj = jsonArray.getJSONObject(i);
            String mainskuid = originalObj.getString("商品Id");
            String bbid = skuToBbidMap.get(mainskuid);

            // 只有匹配上bbid的数据才处理，没匹配上的直接跳过
            if (bbid != null) {
                // 添加product_id字段
                JSONObject newObj = new JSONObject();
                newObj.put("product_id", bbid);
                newObj.put("order_id", originalObj.getString("订单号"));
                newObj.put("order_time", originalObj.getString("下单时间"));
                newObj.put("date", originalObj.getString("日期 "));
                newObj.put("update_time", originalObj.getString("更新时间 "));
                // 根据订单类型设置费用字段
                if (orderType == ORDER_TYPE_ZSZS) {
                    newObj.put("yj_cos_fee", originalObj.getDouble("推广费(元)"));
                    newObj.put("fwf_cos_fee", originalObj.getDouble("服务费(元)"));
                } else if (orderType == ORDER_TYPE_PTB) {
                    newObj.put("yj_cos_fee", originalObj.getDouble("推广费佣金(元)"));
                    // 普通版没有服务费字段
                    newObj.put("fwf_cos_fee", 0.0);
                }

                transformedArray.add(newObj);
            }
            // 没有匹配上bbid的数据不添加到结果数组中，直接跳过
        }

        // 删除处理完成的Excel文件
        deleteExcelFile(filePath);

        return transformedArray;
    }
    
    /**
     * 删除Excel文件
     */
    private void deleteExcelFile(String filePath) {
        try {
            java.io.File file = new java.io.File(filePath);
            if (file.exists()) {
                boolean deleted = file.delete();
                if (deleted) {
                    LOG.info("成功删除Excel文件: " + filePath);
                } else {
                    LOG.warn("无法删除Excel文件: " + filePath);
                }
            } else {
                LOG.warn("要删除的Excel文件不存在: " + filePath);
            }
        } catch (Exception e) {
            LOG.error("删除Excel文件时发生错误: " + filePath, e);
        }
    }
    
    /**
     * 获取SKU到BBID的映射关系
     */
    private Map<String, String> getSkuToBbidMapping() {
        Map<String, String> skuToBbidMap = new HashMap<>();
        try {
            String ctspKey = String.format("biwph/%s/ctsplb.json", getCjzh().getUserid());
            String ctspContent = biDataOssUtil.readJson(ctspKey);
            JSONArray ctspArray = JSONArray.parseArray(ctspContent);
            
            for (int i = 0; i < ctspArray.size(); i++) {
                JSONObject ctspObj = ctspArray.getJSONObject(i);
                skuToBbidMap.put(ctspObj.getString("mainskuid"), ctspObj.getString("bbid"));
            }
        } catch (Exception e) {
            LOG.error("获取商品映射关系失败", e);
        }
        return skuToBbidMap;
    }

    private void uploadData(String rq, JSONArray jsonArray) {
        String key = String.format("biwph/%s/%s/wxkhf.json", getCjzh().getUserid(), rq);
        biDataOssUtil.upload(jsonArray.toJSONString(), key);
    }

    private void updateProcessedDate(String rq) {
        BiInfo biInfo = getBiInfo();
        biInfo.getCjrq().setWxkyjyghf(rq);
        saveRPA(getBiInfo(), getCjzh(), CodeConst.WXKYJ, rq);
        BiThreadLocalObjects.getMsgBean().getWxkyjyghf().add(rq);
    }

    @Override
    public void notOpen() {
        BiInfo biInfo = BiThreadLocalObjects.getBiInfo();
        biInfo.getCjrq().setWxkyjyghf(BiDateUtil.getLastday());
        BiThreadLocalObjects.getMsgBean().getWxkyjyghf().addAll(getCjzh().getCjrqMapRqList(BiModuleConstant.WXKYJ));
    }

    @Override
    public BiAuthResult authCheck(Cjzh cjzh) {
        if (cjzh.getYxptcjzh() == null) {
            return null;
        }
        return new BiAuthResult(BiAuthResult.SUCCESS, "");
    }

    @Override
    public boolean dpconfig(BiDpConfig biDpConfig) {
        return biDpConfig.getWxkyj() == 0;
    }

    @Override
    protected boolean saveCjrq2Cjzh() {
        Cjzh cjzh = getCjzh();
        BiInfo biInfo = getBiInfo();

        List<String> rqList = BiDateUtil.getCjsj(biInfo.getStart(), biInfo.getCjrq().getWxkyjyghf());
        if (CollectionUtils.isEmpty(rqList)) {
            return false;
        }
        cjzh.putCjrqMap(BiModuleConstant.WXKYJ, rqList);

        return true;
    }
}
