package com.abujlb.zdcjbijd.test;

import com.abujlb.AbujlbBeanFactory;
import com.abujlb.BaseTest;
import com.abujlb.zdcjbijd.bean.BiInfo;
import com.abujlb.zdcjbijd.bean.BiYhdp;
import com.abujlb.zdcjbijd.bean.Dpjd;
import com.abujlb.zdcjbijd.bean.msgbean.DataprobiJdMsgBean;
import com.abujlb.zdcjbijd.bean.msgbean.ZdcjBiJdMsgBean;
import com.abujlb.zdcjbijd.processes.*;
import com.abujlb.zdcjbijd.server.DataUploader;
import com.abujlb.zdcjbijd.service.BiJdService;
import com.abujlb.zdcjbijd.tasks.*;
import com.abujlb.zdcjbijd.threads.BiThreadLocalObjects;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/12/5 14:59
 */
public class TestBiJdProcess extends BaseTest {

    @Autowired
    private AbujlbBeanFactory abujlbBeanFactory;
    @Autowired
    private BiJdService biJdService;

    Dpjd dpjd = null;
    BiInfo biInfo = null;

    @Before
    public void init() {
        String cookieKey = "jd_plug_ck_e114233204e846a9a2daa4a59a197f3d";
//        String cookieKey = "jd_plug_ck_1f42d250bfb248efa2eb27c94ceb8cb2";
        dpjd = DataUploader.getBidpByCookieKey(cookieKey);
        if (dpjd == null) {
            throw new RuntimeException("dpjd为空");
        }
        biInfo = DataUploader.getBiInfo(dpjd.getShopid(), "JD");
        if (biInfo == null) {
            throw new RuntimeException("biinfo为空");
        }
        List<BiYhdp> biYhdps = DataUploader.getBiYhdpListJst(dpjd.getShopid(), biInfo.getQd());
        if (CollectionUtils.isEmpty(biYhdps)) {
            return;
        }
        dpjd.setBiYhdpList(biYhdps);
        dpjd.setFbp(biInfo.getBiDpConfig().getDplx() == 1);

        BiThreadLocalObjects.addCjzh(dpjd);
        BiThreadLocalObjects.addBiInfo(biInfo);

        boolean testTghf = false;
        if (testTghf) {
            biJdService.authAccountCheck(dpjd);
        }
        dpjd.setMsgType(2);
        DataprobiJdMsgBean dataprobiMsg = new DataprobiJdMsgBean();
        try {
            dataprobiMsg.setUserid(biInfo.getUserid());
            dataprobiMsg.setQd(biInfo.getQd());
            dataprobiMsg.setDpmc(biInfo.getDpmc());
            dataprobiMsg.setBiinfoId(biInfo.getId());
            dataprobiMsg.setType(2);
            dataprobiMsg.initial();
            BiThreadLocalObjects.initalMsgBean(dataprobiMsg);
        } catch (Exception e) {
            throw new RuntimeException("初始化msgBean失败！");
        }
        BiThreadLocalObjects.initalMsgBean(dataprobiMsg);
    }

    @Test
    public void pj() {
        DefaultBiJdPjProcess defaultBiJdPjProcess = abujlbBeanFactory.getBean(DefaultBiJdPjProcess.class);
        BiThreadLocalObjects.getBiInfo().getCjrq().setPj("2024-11-30");
        BiThreadLocalObjects.getBiInfo().getBiDpConfig().setPj(1);
        defaultBiJdPjProcess.cjAll();
        System.out.println("pj = " + BiThreadLocalObjects.getMsgBean().getSpxg());
    }

    @Test
    public void spxg() {
        DefaultBiJdSpxgProcess defaultBiJdSpxgProcess = abujlbBeanFactory.getBean(DefaultBiJdSpxgProcess.class);
//        BiThreadLocalObjects.getBiInfo().getCjrq().setSpxg("2024-02-29");
        BiThreadLocalObjects.getBiInfo().getCjrq().setSpxgdp("2024-12-14");
        defaultBiJdSpxgProcess.cjAll();
        System.out.println("spxg = " + BiThreadLocalObjects.getMsgBean().getSpxg());
        System.out.println("spxgdp = " + BiThreadLocalObjects.getMsgBean().getSpxgdp());
    }

    @Test
    public void fbpspg() {
        DefaultBiJdzySpxgProcess defaultBiJdzySpxgProcess = abujlbBeanFactory.getBean(DefaultBiJdzySpxgProcess.class);
//        BiThreadLocalObjects.getBiInfo().getCjrq().setSpxg("2024-02-29");
        BiThreadLocalObjects.getBiInfo().getCjrq().setSpxgdp("2024-12-14");
        defaultBiJdzySpxgProcess.cjAll();
        System.out.println("spxg = " + BiThreadLocalObjects.getMsgBean().getSpxg());
        System.out.println("spxgdp = " + BiThreadLocalObjects.getMsgBean().getSpxgdp());
    }

    @Test
    public void jdou() {
        DefaultBiJdJdouProcess defaultBiJdJdouProcess = abujlbBeanFactory.getBean(DefaultBiJdJdouProcess.class);
//        BiThreadLocalObjects.getBiInfo().getCjrq().setJdou("2024-05-26");
//        BiThreadLocalObjects.getBiInfo().getBiDpConfig().setJdou(0);
        defaultBiJdJdouProcess.cjAll();
        System.out.println("jdou = " + BiThreadLocalObjects.getMsgBean().getJdou());
    }

    @Test
    public void splb() {
        DefaultBiJdSplbProcess defaultBiJdSplbProcess = abujlbBeanFactory.getBean(DefaultBiJdSplbProcess.class);
        BiThreadLocalObjects.getBiInfo().getCjrq().setSplbs("2023-06-17");
        defaultBiJdSplbProcess.cjAll();
        System.out.println("splb = " + BiThreadLocalObjects.getMsgBean().getSplbs());
    }

    @Test
    public void fcs() {
        DefaultBiJdFcsSettlementProcess defaultBiJdFcsSettlementProcess = abujlbBeanFactory.getBean(DefaultBiJdFcsSettlementProcess.class);
        BiThreadLocalObjects.getBiInfo().getCjrq().setFcs("2023-09-29");
        BiThreadLocalObjects.getCjzh().setFcs(1);
        defaultBiJdFcsSettlementProcess.cjAll();
        System.out.println("fcs = " + BiThreadLocalObjects.getMsgBean().getFcs());
    }

    @Test
    public void bt() {
        DefaultBiJdBtProcess defaultBiJdBtProcess = abujlbBeanFactory.getBean(DefaultBiJdBtProcess.class);
        BiThreadLocalObjects.getBiInfo().getCjrq().setBt("2024-03-01");
        defaultBiJdBtProcess.cjAll();
        System.out.println("bt = " + BiThreadLocalObjects.getMsgBean().getBt());
    }

    @Test
    public void bill() {
        JdBillTask jdBillTask = abujlbBeanFactory.getBean(JdBillTask.class);
        BiThreadLocalObjects.getBiInfo().getCjrq().setBill("2024-08-02");
        ZdcjBiJdMsgBean jdMsgBean = new ZdcjBiJdMsgBean();
        jdMsgBean.setType(3);
        jdMsgBean.setCookieKey("jd_ck_6c352959e89a41d38cff7a748a0be2d4");
        jdBillTask.bill(jdMsgBean);
    }

    @Test
    public void tzzp() {
        JdTzzpTask jdTzzpTask = abujlbBeanFactory.getBean(JdTzzpTask.class);
//        BiThreadLocalObjects.getBiInfo().getCjrq().setTzzp("2023-12-26");
        jdTzzpTask.tzzp();
    }

    @Test
    public void fbpdyfx() {
        DefaultBiJdFbpDyfxProcess defaultBiJdFbpDyfxProcess = abujlbBeanFactory.getBean(DefaultBiJdFbpDyfxProcess.class);
        BiThreadLocalObjects.getBiInfo().getCjrq().setFbpdyfx("2023-12-26");
        defaultBiJdFbpDyfxProcess.cjAll();
    }

    @Test
    public void fbpspmx() {
        DefaultBiJdFbpSpmxProcess defaultBiJdFbpSpmxProcess = abujlbBeanFactory.getBean(DefaultBiJdFbpSpmxProcess.class);
//        BiThreadLocalObjects.getBiInfo().getCjrq().setFbpspmx("2024-08-01");
        defaultBiJdFbpSpmxProcess.cjAll();
        System.out.println("fbpspmx = " + BiThreadLocalObjects.getMsgBean().getFbpspmx());

    }

    @Test
    public void fbpspyj() {
        DefaultBiJdFbpSpyjProcess defaultBiJdFbpSpyjProcess = abujlbBeanFactory.getBean(DefaultBiJdFbpSpyjProcess.class);
        BiThreadLocalObjects.getBiInfo().getCjrq().setFbpspyj("2024-12-03");
        defaultBiJdFbpSpyjProcess.cjAll();
        System.out.println("fbpspyj = " + BiThreadLocalObjects.getMsgBean().getFbpspyj());
    }

    @Test
    public void fbpjyzkspmx() {
        DefaultBiJdFbpJyzkSpmxProcess defaultBiJdFbpJyzkSpmxProcess = abujlbBeanFactory.getBean(DefaultBiJdFbpJyzkSpmxProcess.class);
//        BiThreadLocalObjects.getBiInfo().getCjrq().setFbpjyzkspmx("2023-11-30");
        defaultBiJdFbpJyzkSpmxProcess.cjAll();
        System.out.println("fbpjyzkspmx = " + BiThreadLocalObjects.getMsgBean().getFbpjyzkspmx());
    }

    @Test
    public void xedk() {
        JdXedkTask jdXedkTask = abujlbBeanFactory.getBean(JdXedkTask.class);
//        BiThreadLocalObjects.getBiInfo().getCjrq().setXedk("2023-11-30");
        jdXedkTask.xedk();
    }

    @Test
    public void gwcd() {
        DefaultBiJdGwcdProcess defaultBiJdGwcdProcess = abujlbBeanFactory.getBean(DefaultBiJdGwcdProcess.class);
        BiThreadLocalObjects.getBiInfo().getCjrq().setGwcd("2024-09-23");
        BiThreadLocalObjects.getBiInfo().getCjrq().setGwcddp("2024-09-23");
        defaultBiJdGwcdProcess.cjAll();
    }

    @Test
    public void jdkc() {
        DefaultBiJdJdkcProcess defaultBiJdJdkcProcess = abujlbBeanFactory.getBean(DefaultBiJdJdkcProcess.class);
//        BiThreadLocalObjects.getBiInfo().getCjrq().setJdkc("2024-03-15");
//        BiThreadLocalObjects.getBiInfo().getCjrq().setJdkcdp("2024-11-10");
        defaultBiJdJdkcProcess.cjAll();
    }

    @Test
    public void jrw() {
        DefaultBiJdJrwProcess defaultBiJdJrwProcess = abujlbBeanFactory.getBean(DefaultBiJdJrwProcess.class);
        BiThreadLocalObjects.getBiInfo().getCjrq().setJrwdp("2024-03-15");
        defaultBiJdJrwProcess.cjAll();
    }

    @Test
    public void jsk() {
        DefaultBiJdJskProcess defaultBiJdJskProcess = abujlbBeanFactory.getBean(DefaultBiJdJskProcess.class);
        BiThreadLocalObjects.getBiInfo().getCjrq().setJskdp("2024-03-15");
        defaultBiJdJskProcess.cjAll();
    }


    @Test
    public void jtk() {
        DefaultBiJdJtkProcess defaultBiJdJtkProcess = abujlbBeanFactory.getBean(DefaultBiJdJtkProcess.class);
//        BiThreadLocalObjects.getBiInfo().getCjrq().setJtk("2024-08-19");
//        BiThreadLocalObjects.getBiInfo().getCjrq().setJtkdp("2024-08-19");
        defaultBiJdJtkProcess.cjAll();
    }

    @Test
    public void jtk_backtrace() {
        BiThreadLocalObjects.getBiInfo().getBiDpConfig().setJtk_backtrace(1);
        BiThreadLocalObjects.getBiInfo().getCjrq().setJtk("2024-11-02");
        DefaultBiJdJtkBackTraceProcess defaultBiJdJtkProcess = abujlbBeanFactory.getBean(DefaultBiJdJtkBackTraceProcess.class);
//        BiThreadLocalObjects.getBiInfo().getCjrq().setJtk("2024-08-19");
//        BiThreadLocalObjects.getBiInfo().getCjrq().setJtkdp("2024-08-19");
        defaultBiJdJtkProcess.cjAll();
    }


    @Test
    public void fbp_jtk() {
        DefaultBiJdFbpJtkProcess defaultBiJdJtkProcess = abujlbBeanFactory.getBean(DefaultBiJdFbpJtkProcess.class);
        BiThreadLocalObjects.getBiInfo().getCjrq().setJtk("2024-07-31");
        BiThreadLocalObjects.getBiInfo().getCjrq().setJtkdp("2024-07-31");
        defaultBiJdJtkProcess.cjAll();
        System.out.println(BiThreadLocalObjects.getBiInfo().getCjrq().getJtk());
        System.out.println(BiThreadLocalObjects.getBiInfo().getCjrq().getJtkdp());
    }

    @Test
    public void xfjl() {
        DefaultBiJdXfjlProcess defaultBiJdXfjlProcess = abujlbBeanFactory.getBean(DefaultBiJdXfjlProcess.class);
//        BiThreadLocalObjects.getBiInfo().getCjrq().setXfjl("2024-03-16");
        defaultBiJdXfjlProcess.cjAll();
    }


    @Test
    public void qzyx() {
//        BiThreadLocalObjects.getBiInfo().getBiDpConfig().setQzyxspuid(1);
//        BiThreadLocalObjects.getBiInfo().getBiDpConfig().setQzyxspuids("14446329402");
        DefaultBiJdQzyxProcess defaultBiJdQzyxProcess = abujlbBeanFactory.getBean(DefaultBiJdQzyxProcess.class);
        BiThreadLocalObjects.getBiInfo().getCjrq().setQzyx("2024-09-11");
        defaultBiJdQzyxProcess.cjAll();
    }


    @Test
    public void bybt() {
//        BiThreadLocalObjects.getBiInfo().getCjrq().setBybt("2024-07-24");
        DefaultBiJdBybtProcess defaultBiJdBybtProcess = abujlbBeanFactory.getBean(DefaultBiJdBybtProcess.class);
        defaultBiJdBybtProcess.cjAll();
    }


    @Test
    public void jdb() {
        BiThreadLocalObjects.getBiInfo().getCjrq().setJdb("2024-05-31");
        JdbTask jdbTask = abujlbBeanFactory.getBean(JdbTask.class);
        jdbTask.jdb();
    }

    @Test
    public void orders() {
        BiThreadLocalObjects.getBiInfo().getCjrq().setOrders("2024-04-30");
        DefaultBiJdOrdersProcess defaultBiJdOrdersProcess = abujlbBeanFactory.getBean(DefaultBiJdOrdersProcess.class);
        defaultBiJdOrdersProcess.cjAll();
        System.out.println(BiThreadLocalObjects.getBiInfo().getCjrq().getOrders());
    }


    /**
     * 接待相应
     */
    @Test
    public void jdxy() {
        DefaultBiJdJdxyProcess defaultBiJdJdxyProcess = abujlbBeanFactory.getBean(DefaultBiJdJdxyProcess.class);
        BiThreadLocalObjects.getBiInfo().getCjrq().setJdxydp("2024-09-22");
        defaultBiJdJdxyProcess.cjAll();
    }

    /**
     * 客服销售
     */
    @Test
    public void kfxs() {
        DefaultBiJdKfxsProcess defaultBiJdKfxsProcess = abujlbBeanFactory.getBean(DefaultBiJdKfxsProcess.class);
        BiThreadLocalObjects.getBiInfo().getCjrq().setKfxsdp("2024-09-22");
        defaultBiJdKfxsProcess.cjAll();
    }

    /**
     * 客服排行
     */
    @Test
    public void kfph() {
        DefaultBiJdKfphProcess defaultBiJdKfphProcess = abujlbBeanFactory.getBean(DefaultBiJdKfphProcess.class);
        BiThreadLocalObjects.getBiInfo().getCjrq().setKfph("2024-09-22");
        defaultBiJdKfphProcess.cjAll();
    }

    /**
     * 促成订单
     */
    @Test
    public void ccdd() {
        DefaultBiJdCcmxProcess defaultBiJdCcmxProcess = abujlbBeanFactory.getBean(DefaultBiJdCcmxProcess.class);
        BiThreadLocalObjects.getBiInfo().getCjrq().setCcdd("2024-09-22");
        defaultBiJdCcmxProcess.cjAll();
    }


    /**
     * 违约金
     */
    @Test
    public void wyj() {
        DefaultBiJdWyjProcess defaultBiJdWyjProcess = abujlbBeanFactory.getBean(DefaultBiJdWyjProcess.class);
        BiThreadLocalObjects.getBiInfo().getCjrq().setWyj("2024-09-23");
        defaultBiJdWyjProcess.cjAll();
    }

    /**
     * 计划状态
     */
    @Test
    public void jhzt() {
        DefaultBiJdJhztProcess defaultBiJdJhztProcess = abujlbBeanFactory.getBean(DefaultBiJdJhztProcess.class);
        BiThreadLocalObjects.getBiInfo().getCjrq().setJdjhzt("2024-10-15");
        defaultBiJdJhztProcess.cjAll();
    }

    /**
     * 便宜包邮
     */
    @Test
    public void pyby() {
        BiThreadLocalObjects.getBiInfo().getCjrq().setPyby("2024-10-26");
        DefaultBiJdPybyProcess defaultBiJdPybyProcess = abujlbBeanFactory.getBean(DefaultBiJdPybyProcess.class);
        defaultBiJdPybyProcess.cjAll();
    }

    @Test
    public void zjlszd() {
        BiThreadLocalObjects.getBiInfo().getCjrq().setZjlszd("2024-10-31");
        JdZjlszdTask jdZjlszdTask = abujlbBeanFactory.getBean(JdZjlszdTask.class);
        jdZjlszdTask.zjlszd();
    }

    /**
     * 便宜包邮
     */
    @Test
    public void sjtyf() {
        BiThreadLocalObjects.getBiInfo().getCjrq().setJdsjtyf("2024-12-08");
        DefaultBiJdSjtyfProcess defaultBiJdSjtyfProcess = abujlbBeanFactory.getBean(DefaultBiJdSjtyfProcess.class);
        defaultBiJdSjtyfProcess.cjAll();

    }


}
