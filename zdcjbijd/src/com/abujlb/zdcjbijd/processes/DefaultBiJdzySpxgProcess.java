package com.abujlb.zdcjbijd.processes;

import com.abujlb.zdcjbijd.annos.BiPro;
import com.abujlb.zdcjbijd.bean.*;
import com.abujlb.zdcjbijd.constants.RqlxConst;
import com.abujlb.zdcjbijd.http.BijdzyHttpClient;
import com.abujlb.zdcjbijd.http.FbpjyzkspmxExportHttpClient;
import com.abujlb.zdcjbijd.oss.BiDataOssUtil;
import com.abujlb.zdcjbijd.threads.BiThreadLocalObjects;
import com.abujlb.zdcjbijd.util.BiDateUtil;
import com.abujlb.zdcjbijd.util.BiJdZipRead;
import com.abujlb.zdcjbijd.util.JdZySpxgXlsReader;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.List;

/**
 * FBP店铺 采集商品效果
 *
 * <AUTHOR>
 */
@Component
@BiPro(value = 2)
public class DefaultBiJdzySpxgProcess extends AbstractBiJdProcess {

    private static final Logger LOG = Logger.getLogger(DefaultBiJdzySpxgProcess.class);

    /**
     * <ul>
     *     <li>采集页面地址：https://ppzh.jd.com/brandweb/brand/view/productAnalysis/productDetail.html</li>
     *     <li>采集页面描述：京东商智（品牌版）-->商品 -->商品分析-->商品明细 </li>
     *     <li>按日下载报表,分别下载 SKU纬度、SPU纬度</li>
     *     <ul>选项：
     *         <li>品牌：全部</li>
     *         <li>品类：全部</li>
     *         <li>经营模式：全部</li>
     *         <li>终端：全部</li>
     *         <li>SPU:</li>
     *         <li>对比时间：环比</li>
     *     </ul>
     *
     * </ul>
     */
    @Override
    public void cj() {
        for (String rq : getCjzh().getCjrqMapRqList(BiModuleConstant.SPXG)) {
            if (process(rq)) {
                getBiInfo().getCjrq().setSpxg(rq);
                BiThreadLocalObjects.getMsgBean().getSpxg().add(rq);
            } else {
                break;
            }
        }
    }

    /**
     * <ul>
     *     采集项1：
     *     <ul>
     *         <li>采集页面地址：https://ppzh.jd.com/brandweb/brand/view/productAnalysis/productSummary.html</li>
     *         <li>采集页面描述：京东商智（品牌版）-->商品 -->商品概况</li>
     *         <li>按日接口请求,分别请求 SKU纬度、SPU纬度  商品概览</li>
     *     </ul>
     * <p>
     *     采集项2
     *     <ul>
     *         <li>采集页面地址：https://ppzh.jd.com/brandweb/brand/view/dealAnalysis/dealSummary.html</li>
     *         <li></li>
     *     </ul>
     * </ul>
     */
    @Override
    public void cjdp() {
        /*首次进入BI系统的店铺。需要补采24个月的店铺维度数据*/
        if (firstTimeCj() && !doFirstTimeCj()) {
            return;
        }
        for (String rq : getCjzh().getCjrqMapRqList(BiModuleConstant.SPXGDP)) {
            //商品概况SPU
            //商品概况SKU
            //交易概况
            if (processDp(rq)) {
                getBiInfo().getCjrq().setSpxgdp(rq);
                BiThreadLocalObjects.getMsgBean().getSpxgdp().add(rq);
            } else {
                break;
            }
        }
    }

    private boolean firstTimeCj() {
        return getBiInfo().getStart() == null || getBiInfo().getJysj() == 0;
    }

    private boolean doFirstTimeCj() {
        String start = null;
        String end = null;
        boolean result = true;
        try {
            for (String[] recent24Month : BiDateUtil.getRecent24Months()) {
                start = recent24Month[0];
                end = recent24Month[1];

                if (processDp(start, end, RqlxConst.MONTH)) {
                    BiThreadLocalObjects.getMsgBean().getDpMonths().add(end);
                } else {
                    result = false;
                    break;
                }
            }
            return true;
        } finally {
            if (result) {
                if (getBiInfo().getStart() == null) {
                    getBiInfo().setStart(BiDateUtil.getFirstDayOfMonthDate());
                }
                if (getBiInfo().getJysj() == 0) {
                    getBiInfo().setJysj(1);
                }
            } else {
                BiThreadLocalObjects.getMsgBean().getDpMonths().clear();
            }
        }
    }

    private boolean process(String rq) {
        //日
        if (!process(rq, rq, RqlxConst.DAY)) {
            return false;
        }
        //月
        if (BiDateUtil.isLastDayForMonth(rq) && !process(BiDateUtil.getFirstDayOfMonth(rq), rq, RqlxConst.MONTH)) {
            return false;
        }
        //周
        return !BiDateUtil.isLastDayForWeek(rq) || process(BiDateUtil.getFirstDayOfWeek(rq), rq, RqlxConst.WEEK);
    }

    private boolean process(String start, String end, String rqlx) {
        final String[] TYPES = {"SKU", "SPU"};
        for (String type : TYPES) {
            boolean allSuccess = process(start, end, rqlx, type);
            if (!allSuccess) {
                return false;
            }
        }
        return true;
    }

    private boolean process(String start, String end, String rqlx, String type) {
        try {
            // type SKU  SPU
            // 下载压缩包
            String date = null;
            if (rqlx.equals(RqlxConst.DAY)) {
                date = end;
            } else if (rqlx.equals(RqlxConst.WEEK)) {
                date = "99" + BiDateUtil.getWeekOfYear(end);
            } else if (rqlx.equals(RqlxConst.MONTH)) {
                date = end.substring(0, 7).replaceAll("-", "");
            } else {
                return false;
            }

            BijdzyHttpClient.downloadManageProDataAjax(getCjzh(), start, end, date, type);

            //获取下载任务id
            String reportId = BijdzyHttpClient.getReport(getCjzh());
            if (StringUtils.isBlank(reportId)) {
                return false;
            }

            String downloadUrl = FbpjyzkspmxExportHttpClient.getDownloadUrl(getCjzh(), reportId);
            if (StringUtils.isBlank(downloadUrl)) {
                return false;
            }

            String filepath = FbpjyzkspmxExportHttpClient.download(getCjzh(), downloadUrl);
            // 解压 上传oss
            readZipFile(rqlx, filepath, end, type.equals("SKU") ? 1 : 0);

            return true;
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
        return false;
    }

    private void readZipFile(String rqlx, String srcFilePath, String rq, int type) {
        try {
            Dpjd dpjd = getCjzh();
            File file = BiJdZipRead.read(srcFilePath, dpjd.getShopid());
            if (file == null || !file.exists()) {
                return;
            }
            String fileName = type == 1 ? "sku" : "spu";
            if (rqlx.equals(RqlxConst.WEEK)) {
                fileName = fileName + "_week";
            } else if (rqlx.equals(RqlxConst.MONTH)) {
                fileName = fileName + "_month";
            }

            // 上传oss
            BiDataOssUtil.upload(file, "bi_jd/jdsz/" + dpjd.getShopid() + "/" + rq + "/" + fileName + ".xlsx");

            if (type == 1) {
                List<JdSkuSpxg> skuSpxgList = JdZySpxgXlsReader.parseSku(file);
                if (CollectionUtils.isNotEmpty(skuSpxgList)) {
                    BiDataOssUtil.upload(skuSpxgList.toString(), "bi_jd/jdsz/" + dpjd.getShopid() + "/" + rq + "/" + fileName + ".json");
                }
            } else {
                List<JdSpuSpxg> spuSpxgList = JdZySpxgXlsReader.parseSpu(file);
                if (CollectionUtils.isNotEmpty(spuSpxgList)) {
                    BiDataOssUtil.upload(spuSpxgList.toString(), "bi_jd/jdsz/" + dpjd.getShopid() + "/" + rq + "/" + fileName + ".json");
                }
            }

            file.delete();

            File srcFile = new File(srcFilePath);
            if (srcFile.exists()) {
                srcFile.delete();
            }
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
    }

    private void deleteTempFile(String filePath) {
        File file = new File(filePath);
        if (file.exists()) {
            boolean delete = file.delete();
            System.out.println("delete = " + delete);
        }
    }

    private boolean processDp(String rq) {
        //日
        if (!processDp(rq, rq, RqlxConst.DAY)) {
            return false;
        }
        //月
        if (BiDateUtil.isLastDayForMonth(rq) && !processDp(BiDateUtil.getFirstDayOfMonth(rq), rq, RqlxConst.MONTH)) {
            return false;
        }
        //周
        return !BiDateUtil.isLastDayForWeek(rq) || processDp(BiDateUtil.getFirstDayOfWeek(rq), rq, RqlxConst.WEEK);
    }

    private boolean processDp(String start, String end, String rqlx) {
        try {
            String spu = BijdzyHttpClient.spgk(getCjzh(), start, end, "SPU", rqlx);
            if (spu == null) {
                return false;
            }
            String sku = BijdzyHttpClient.spgk(getCjzh(), start, end, "SKU", rqlx);
            if (sku == null) {
                return false;
            }
            String jygk = BijdzyHttpClient.jygk(getCjzh(), start, end, rqlx);
            if (jygk == null) {
                return false;
            }
            bijdDpTsDao.updateRowSpxg(getCjzh().getShopid(), rqlx.equals(RqlxConst.DAY) ? end : (rqlx + "_" + end), spu, sku, jygk, null);
            return true;
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
        return false;
    }


    @Override
    public BiAuthResult authCheck(Dpjd cjzh) {
        if (getBiInfo().getBiDpConfig().getDplx() == 1) {
            return defaultAuthCheck();
        }
        return null;
    }

    @Override
    protected boolean saveCjrq2Cjzh() {
        Dpjd dpjd = getCjzh();
        BiInfo biInfo = getBiInfo();
        List<String> rqList1 = BiDateUtil.getCjsj(biInfo.getStart(), biInfo.getCjrq().getSpxg());
        List<String> rqList2 = BiDateUtil.getCjsj(biInfo.getStart(), biInfo.getCjrq().getSpxgdp());
        if (CollectionUtils.isEmpty(rqList1) && CollectionUtils.isEmpty(rqList2)) {
            return false;
        }
        dpjd.putCjrqMap(BiModuleConstant.SPXG, rqList1);
        dpjd.putCjrqMap(BiModuleConstant.SPXGDP, rqList2);
        return true;
    }

    @Override
    public void notCj() {
        String rq = BiDateUtil.lastday();
        getBiInfo().getCjrq().setSpxg(rq);
        getBiInfo().getCjrq().setSpxgdp(rq);
        BiThreadLocalObjects.getMsgBean().getSpxg().add(rq);
        BiThreadLocalObjects.getMsgBean().getSpxgdp().add(rq);
    }

    @Override
    public boolean dpconfig(BiDpConfig biDpConfig) {
        return biDpConfig.getSpxg() == 0;
    }
}
