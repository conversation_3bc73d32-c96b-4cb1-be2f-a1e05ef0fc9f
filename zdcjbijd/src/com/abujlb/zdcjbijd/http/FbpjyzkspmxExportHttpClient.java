package com.abujlb.zdcjbijd.http;

import com.abujlb.CommonConfig;
import com.abujlb.zdcjbijd.bean.Dpjd;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.BasicCookieStore;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.apache.log4j.Logger;

import java.io.FileOutputStream;
import java.io.InputStream;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023-12-26
 */
public class FbpjyzkspmxExportHttpClient {

    private static final Logger log = Logger.getLogger(FbpjyzkspmxExportHttpClient.class);

    public static void createExportTask(Dpjd dpjd, String start, String end) {
        HttpGet httppost = null;
        try {
            CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(new BasicCookieStore()).build();
            RequestConfig.Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000).setConnectionRequestTimeout(20000);
            requestConfig.setCircularRedirectsAllowed(true);
            String documentCookie = dpjd.getDocumentCookie();
            String url = "https://ppzh.jd.com/brand/reportCenter/recommendReport/downloadManageProData.ajax?startDate=" + start + "&endDate=" + end + "&date=10" + end + "&brandId=all&thirdCategoryId=all&provinceId=all&cityId=all&formFlag=0&projectId=100146&secondSourceId=2002&skuId=&firstBuTypeId=&secondBuTypeId=&isRdc=0&saleChannal=1&proType=SKU&dateType=&querySearch=%7B%22startDate%22%3A%22" + start + "%22%2C%22endDate%22%3A%22" + end + "%22%2C%22date%22%3A%2210" + end + "%22%2C%22brandId%22%3A%22all%22%2C%22thirdCategoryId%22%3A%22all%22%2C%22provinceId%22%3A%22all%22%2C%22cityId%22%3A%22all%22%2C%22formFlag%22%3A%220%22%2C%22projectId%22%3A%22100146%22%2C%22secondSourceId%22%3A%222002%22%2C%22firstBuTypeId%22%3A%22%22%2C%22secondBuTypeId%22%3A%22%22%2C%22isRdc%22%3A%220%22%2C%22saleChannal%22%3A%221%22%2C%22proType%22%3A%22SKU%22%2C%22dateType%22%3A%22dayRange%22%2C%22brandReset%22%3A%7B%22pItem%22%3Anull%2C%22selectedArr%22%3A%5B%5D%7D%2C%22categoryReset%22%3A%7B%22pItem%22%3Anull%2C%22cItem%22%3Anull%2C%22cSubItem%22%3Anull%2C%22pIndex%22%3A-1%2C%22cIndex%22%3A-1%2C%22cSubIndex%22%3A-1%2C%22showName%22%3A%22%E5%85%A8%E9%83%A8%22%2C%22selectedAll%22%3A%7B%22id%22%3A%22all%22%2C%22name%22%3A%22%E5%85%A8%E9%83%A8%22%7D%2C%22selectedArr%22%3A%5B%5D%7D%2C%22provinceReset%22%3A%7B%7D%7D&uuid=" + UUID.randomUUID();
            httppost = new HttpGet(url);
//            httppost = new HttpGet("https://ppzh.jd.com/brand/reportCenter/recommendReport/downloadManageProData.ajax?startDate=" + start + "&endDate=" + end + "&date=" + end + "&brandId=all&thirdCategoryId=all&provinceId=all&cityId=all&formFlag=2&projectId=100146&secondSourceId=2002&skuId=&firstBuTypeId=&secondBuTypeId=&isRdc=0&saleChannal=1&proType=SKU&dateType=&querySearch=%7B%22startDate%22%3A%22" + start + "%22%2C%22endDate%22%3A%22" + end + "%22%2C%22date%22%3A%22" + end + "%22%2C%22brandId%22%3A%22all%22%2C%22thirdCategoryId%22%3A%22all%22%2C%22provinceId%22%3A%22all%22%2C%22cityId%22%3A%22all%22%2C%22formFlag%22%3A%222%22%2C%22projectId%22%3A%22100146%22%2C%22secondSourceId%22%3A%222002%22%2C%22firstBuTypeId%22%3A%22%22%2C%22secondBuTypeId%22%3A%22%22%2C%22isRdc%22%3A%220%22%2C%22saleChannal%22%3A%221%22%2C%22proType%22%3A%22SKU%22%2C%22dateType%22%3A%22day%22%2C%22brandReset%22%3A%7B%22pItem%22%3Anull%2C%22selectedArr%22%3A%5B%5D%7D%2C%22categoryReset%22%3A%7B%22pItem%22%3Anull%2C%22cItem%22%3Anull%2C%22cSubItem%22%3Anull%2C%22pIndex%22%3A-1%2C%22cIndex%22%3A-1%2C%22cSubIndex%22%3A-1%2C%22showName%22%3A%22%E5%85%A8%E9%83%A8%22%2C%22selectedAll%22%3A%7B%22id%22%3A%22all%22%2C%22name%22%3A%22%E5%85%A8%E9%83%A8%22%7D%2C%22selectedArr%22%3A%5B%5D%7D%2C%22provinceReset%22%3A%7B%7D%7D&uuid=" + UUID.randomUUID());
            httppost.setHeader("accept", "application/json, text/javascript, */*; q=0.01");
            httppost.setHeader("accept-encoding", "gzip, deflate, br");
            httppost.setHeader("accept-language", "zh-CN,zh;q=0.9");
            httppost.setHeader("content-type", "application/json;charset=UTF-8");
            httppost.setHeader("user-agent", "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/55.0.2883.75 Safari/537.36");
            httppost.setHeader("origin", "https://ppzh.jd.com");
            httppost.setHeader("referer", "https://ppzh.jd.com/brand/reportCenter/downLoadReport.html?reportName=managePro");
            httppost.setHeader("sec-ch-ua", "\"Google Chrome\";v=\"113\", \"Chromium\";v=\"113\", \"Not-A.Brand\";v=\"24\"");
            httppost.setHeader("sec-ch-ua-mobile", "?0");
            httppost.setHeader("sec-ch-ua-platform", "\"windows\"");
            httppost.setHeader("sec-fetch-dest", "empty");
            httppost.setHeader("sec-fetch-mode", "cors");
            httppost.setHeader("sec-fetch-site", "same-origin");
            httppost.setHeader("x-requested-with", "XMLHttpRequest");
            httppost.setHeader("Cookie", documentCookie);
            httppost.setHeader("user-mnp", "");
            httppost.setHeader("user-mup", "");
            httppost.setConfig(requestConfig.build());

            httpClient.execute(httppost);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            if (httppost != null) {
                httppost.releaseConnection();
            }
        }
    }

    public static String download(Dpjd cjzh, String downloadUrl) {
        HttpGet httppost = null;
        FileOutputStream os = null;
        InputStream is = null;
        try {
            CloseableHttpClient httpClient = HttpClients.custom().build();
            RequestConfig.Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000)
                    .setConnectionRequestTimeout(20000);
            requestConfig.setCircularRedirectsAllowed(true);
            HttpResponse httpresponse;
            String documentCookie = cjzh.getDocumentCookie();
            httppost = new HttpGet(downloadUrl);
            httppost.setHeader("accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9");
            httppost.setHeader("accept-encoding", "gzip, deflate, br");
            httppost.setHeader("accept-language", "zh-CN,zh;q=0.9");
            httppost.setHeader("cookie", documentCookie);
            httppost.setHeader("sec-ch-ua", "\"Not;A=Brand\";v=\"99\", \"Chromium\";v=\"106\"");
            httppost.setHeader("sec-ch-ua-mobile", "?0");
            httppost.setHeader("sec-ch-ua-platform", "\"Windows\"");
            httppost.setHeader("sec-fetch-dest", "document");
            httppost.setHeader("sec-fetch-mode", "navigate");
            httppost.setHeader("sec-fetch-site", "none");
            httppost.setHeader("sec-fetch-user", "?1");
            httppost.setHeader("upgrade-insecure-requests", "1");
            httppost.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
            httppost.setConfig(requestConfig.build());
            httpresponse = httpClient.execute(httppost);

            String filePath = CommonConfig.getString("tempdir");
            String fileName = cjzh.getId() + "_fbpjyzkspmx_" + System.currentTimeMillis() + "_temp.zip";

            HttpEntity entity = httpresponse.getEntity();
            is = entity.getContent();
            os = new FileOutputStream(filePath + fileName);
            byte[] b = new byte[4096];
            int count = 0;
            while ((count = is.read(b)) > 0) {
                os.write(b, 0, count);
            }
            os.flush();

            return filePath + fileName;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            if (os != null) {
                try {
                    os.close();
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            }
            if (is != null) {
                try {
                    is.close();
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            }
            if (httppost != null) {
                httppost.releaseConnection();
            }
        }
        return null;
    }

    public static String checkTaskFinish(Dpjd cjzh, String start, String end) {
        try {
            TimeUnit.SECONDS.sleep(30);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        final int max = 10;
        int curr = 1;
        while (curr < max) {
            String reportId = checkTaskFinish2(cjzh, start, end);
            if (StringUtils.isNotBlank(reportId)) {
                return reportId;
            }
            if (reportId == null) {
                break;
            }
            curr++;
            try {
                TimeUnit.SECONDS.sleep(30);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
        return null;
    }

    public static String checkTaskFinish2(Dpjd cjzh, String start, String end) {
        HttpGet httppost = null;
        try {
            CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(new BasicCookieStore()).build();
            RequestConfig.Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000).setConnectionRequestTimeout(20000);
            requestConfig.setCircularRedirectsAllowed(true);
            HttpResponse httpresponse;
            String documentCookie = cjzh.getDocumentCookie();
            httppost = new HttpGet("https://ppzh.jd.com/brand/reportCenter/myReport/getReportList.ajax?uuid=" + UUID.randomUUID());
            httppost.setHeader("accept", "application/json, text/javascript, */*; q=0.01");
            httppost.setHeader("accept-encoding", "gzip, deflate, br");
            httppost.setHeader("accept-language", "zh-CN,zh;q=0.9");
            httppost.setHeader("content-type", "application/json;charset=UTF-8");
            httppost.setHeader("user-agent", "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/55.0.2883.75 Safari/537.36");
            httppost.setHeader("origin", "https://ppzh.jd.com");
            httppost.setHeader("referer", "https://ppzh.jd.com/scbrandweb/brand/view/supplyReport/supplyChainPro.html");
            httppost.setHeader("sec-ch-ua", "\"Google Chrome\";v=\"113\", \"Chromium\";v=\"113\", \"Not-A.Brand\";v=\"24\"");
            httppost.setHeader("sec-ch-ua-mobile", "?0");
            httppost.setHeader("sec-ch-ua-platform", "\"windows\"");
            httppost.setHeader("sec-fetch-dest", "empty");
            httppost.setHeader("sec-fetch-mode", "cors");
            httppost.setHeader("sec-fetch-site", "same-origin");
            httppost.setHeader("x-requested-with", "XMLHttpRequest");
            httppost.setHeader("Cookie", documentCookie);
            httppost.setHeader("user-mnp", "");
            httppost.setHeader("user-mup", "");
            httppost.setConfig(requestConfig.build());

            httpresponse = httpClient.execute(httppost);
            String body = EntityUtils.toString(httpresponse.getEntity());
            JSONObject jsonObject = JSONObject.parseObject(body);
            int status = jsonObject.getIntValue("status");
            if (status != 0) {
                return null;
            }

            String name = "经营状况-商品明细报表-" + start + "至" + end + "-全部-全部-按天-SKU.xlsx";
            if (jsonObject.getJSONObject("content") == null || jsonObject.getJSONObject("content").isEmpty()) {
                return null;
            }

            JSONObject content = jsonObject.getJSONObject("content");
            if (!content.containsKey("data")) {
                return null;
            }
            JSONArray data = content.getJSONArray("data");
            for (int i = 0; i < data.size(); i++) {
                JSONObject record = data.getJSONObject(i);
                String reportName = record.getString("reportName");
                if (name.equals(reportName)) {
                    int recordStatus = record.getIntValue("status");
                    if (recordStatus == 1) {
                        return "";
                    } else if (recordStatus == 2) {
                        return record.getString("reportId");
                    }
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            if (httppost != null) {
                httppost.releaseConnection();
            }
        }
        return null;
    }

    public static String getDownloadUrl(Dpjd cjzh, String reportId) {
        HttpGet httppost = null;
        try {
            CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(new BasicCookieStore()).build();
            RequestConfig.Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000).setConnectionRequestTimeout(20000);
            requestConfig.setCircularRedirectsAllowed(true);
            HttpResponse httpresponse;
            String documentCookie = cjzh.getDocumentCookie();
            httppost = new HttpGet("https://ppzh.jd.com/brand/reportCenter/myReport/downLoadReport.ajax?id=" + reportId + "&uuid=" + UUID.randomUUID());
            httppost.setHeader("accept", "application/json, text/javascript, */*; q=0.01");
            httppost.setHeader("accept-encoding", "gzip, deflate, br");
            httppost.setHeader("accept-language", "zh-CN,zh;q=0.9");
            httppost.setHeader("content-type", "application/json;charset=UTF-8");
            httppost.setHeader("user-agent", "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/55.0.2883.75 Safari/537.36");
            httppost.setHeader("origin", "https://ppzh.jd.com");
            httppost.setHeader("referer", "https://ppzh.jd.com/brand/reportCenter/myReport.html");
            httppost.setHeader("sec-ch-ua", "\"Google Chrome\";v=\"113\", \"Chromium\";v=\"113\", \"Not-A.Brand\";v=\"24\"");
            httppost.setHeader("sec-ch-ua-mobile", "?0");
            httppost.setHeader("sec-ch-ua-platform", "\"windows\"");
            httppost.setHeader("sec-fetch-dest", "empty");
            httppost.setHeader("sec-fetch-mode", "cors");
            httppost.setHeader("sec-fetch-site", "same-origin");
            httppost.setHeader("x-requested-with", "XMLHttpRequest");
            httppost.setHeader("Cookie", documentCookie);
            httppost.setHeader("user-mnp", "");
            httppost.setHeader("user-mup", "");
            httppost.setConfig(requestConfig.build());

            httpresponse = httpClient.execute(httppost);
            String body = EntityUtils.toString(httpresponse.getEntity());
            JSONObject jsonObject = JSONObject.parseObject(body);
            int status = jsonObject.getIntValue("status");
            if (status != 0) {
                return null;
            }

            return jsonObject.getJSONObject("content").getString("result");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            if (httppost != null) {
                httppost.releaseConnection();
            }
        }
        return null;
    }
}
