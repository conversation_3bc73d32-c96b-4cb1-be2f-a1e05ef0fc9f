package com.abujlb.zdcjbijd.http;

import com.abujlb.util.StringUtil;
import com.abujlb.zdcjbijd.bean.Dpjd;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.BasicCookieStore;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.apache.log4j.Logger;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

public class QzyxHttpClient {

    private static final Logger LOG = Logger.getLogger(QzyxHttpClient.class);

    /**
     * @param cjzh
     * @param rq
     * @return
     */
    public static JSONArray dataList(Dpjd cjzh, String rq) {
        final int pageSize = 100;
        int pageNo = 1;
        JSONArray jsonArray = new JSONArray();
        while (true) {
            JSONArray temp = dataList2(cjzh, rq, pageNo, pageSize, false);
            if (temp == null) {
                return null;
            }
            jsonArray.addAll(temp);
            if (temp.size() < pageSize) {
                break;
            }

            try {
                TimeUnit.SECONDS.sleep(1);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            pageNo++;
        }
        return jsonArray;
    }

    private static JSONArray dataList2(Dpjd dp, String rq, int pageNo, int pageSize, boolean retry) {
        HttpPost httppost = null;
        HttpResponse httpresponse;
        boolean currIsMaster = dp.getCurrPinAccount().isMaster();
        try {
            CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(new BasicCookieStore()).build();
            RequestConfig.Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000).setConnectionRequestTimeout(20000).setCircularRedirectsAllowed(true);
            String documentCookie = dp.getDocumentCookie();
            if (dp.isFbp() && dp.getCurrPinAccount().isMaster()) {
                httppost = new HttpPost("https://jzt-api.jd.com/swa/ad/list");
            } else {
                httppost = new HttpPost("https://xnjzt-api.jd.com/swa/ad/list");
            }
            httppost.setHeader("accept", "application/json, text/plain, */*");
            httppost.setHeader("accept-encoding", "gzip, deflate, br");
            httppost.setHeader("accept-language", "zh-CN,zh;q=0.9");
            httppost.setHeader("content-type", "application/json;charset=UTF-8");
            httppost.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
            if (dp.isFbp() && dp.getCurrPinAccount().isMaster()) {
                httppost.setHeader("origin", "https://jzt.jd.com");
                httppost.setHeader("referer", "https://jzt.jd.com/");
            } else {
                httppost.setHeader("origin", "https://xnjzt.jd.com");
                httppost.setHeader("referer", "https://xnjzt.jd.com/");
            }

            httppost.setHeader("sec-ch-ua", "Not;A=Brand\";v=\"99\", \"Chromium\";v=\"106");
            httppost.setHeader("sec-ch-ua-mobile", "?0");
            httppost.setHeader("sec-ch-ua-platform", "\"windows\"");
            httppost.setHeader("sec-fetch-dest", "empty");
            httppost.setHeader("sec-fetch-mode", "cors");
            httppost.setHeader("sec-fetch-site", "same-site");
            httppost.setHeader("Cookie", documentCookie);
            StringEntity param = new StringEntity("{\"page\":" + pageNo + ",\"pageSize\":" + pageSize + ",\"platform\":\"\",\"status\":\"\",\"skuId\":\"\",\"filters\":[],\"obys\":\"\",\"product\":\"swa_dsp\",\"startDay\":\"" + rq + "\",\"endDay\":\"" + rq + "\",\"conversionCategory\":1,\"requestFrom\":0}", ContentType.APPLICATION_JSON);
            httppost.setConfig(requestConfig.build());
            httppost.setEntity(param);
            httpresponse = httpClient.execute(httppost);
            String body = EntityUtils.toString(httpresponse.getEntity());
            if (!StringUtil.isJson2(body)) {
                return null;
            }
            JSONObject obj = JSONObject.parseObject(body);
            if (obj.containsKey("code") && obj.getIntValue("code") == -3010 && !retry) {
                TimeUnit.MINUTES.sleep(1);
                return dataList2(dp, rq, pageNo, pageSize, true);
            }
            if (!obj.containsKey("code") || obj.getIntValue("code") != 1) {
                return null;
            }

            if (obj.containsKey("data")) {
                JSONObject data = obj.getJSONObject("data");
                if (null != data && data.containsKey("data")) {
                    return data.getJSONArray("data");
                }
            }
            return new JSONArray();
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        } finally {
            if (httppost != null) {
                httppost.releaseConnection();
            }
        }
        return null;
    }

    public static JSONArray itemList(Dpjd cjzh, String rq) {
        final int pageSize = 100;
        int pageNo = 1;
        JSONArray jsonArray = new JSONArray();
        while (true) {
            JSONArray temp = itemList2(cjzh, rq, pageNo, pageSize, false);
            if (temp == null) {
                return null;
            }
            jsonArray.addAll(temp);
            if (temp.size() < pageSize) {
                break;
            }

            try {
                TimeUnit.SECONDS.sleep(1);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            pageNo++;
        }
        return jsonArray;
    }


    private static JSONArray itemList2(Dpjd dp, String rq, int pageNo, int pageSize, boolean retry) {
        HttpPost httppost = null;
        HttpResponse httpresponse;
        boolean currIsMaster = dp.getCurrPinAccount().isMaster();
        try {
            CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(new BasicCookieStore()).build();
            RequestConfig.Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000).setConnectionRequestTimeout(20000).setCircularRedirectsAllowed(true);
            String documentCookie = dp.getDocumentCookie();
            if (dp.isFbp() && dp.getCurrPinAccount().isMaster()) {
                httppost = new HttpPost("https://jzt-api.jd.com/reweb/swa/account/campaign/list");
            } else {
                httppost = new HttpPost("https://xnjzt-api.jd.com/reweb/swa/account/campaign/list");
            }
            httppost.setHeader("accept", "application/json, text/plain, */*");
            httppost.setHeader("accept-encoding", "gzip, deflate, br");
            httppost.setHeader("accept-language", "zh-CN,zh;q=0.9");
            httppost.setHeader("content-type", "application/json;charset=UTF-8");
            httppost.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
            if (dp.isFbp() && dp.getCurrPinAccount().isMaster()) {
                httppost.setHeader("origin", "https://jzt.jd.com");
                httppost.setHeader("referer", "https://jzt.jd.com/");
            } else {
                httppost.setHeader("origin", "https://xnjzt.jd.com");
                httppost.setHeader("referer", "https://xnjzt.jd.com/");
            }
            httppost.setHeader("sec-ch-ua", "Not;A=Brand\";v=\"99\", \"Chromium\";v=\"106");
            httppost.setHeader("sec-ch-ua-mobile", "?0");
            httppost.setHeader("sec-ch-ua-platform", "\"windows\"");
            httppost.setHeader("sec-fetch-dest", "empty");
            httppost.setHeader("sec-fetch-mode", "cors");
            httppost.setHeader("sec-fetch-site", "same-site");
            httppost.setHeader("Cookie", documentCookie);
            StringEntity param = new StringEntity("{\"pageNum\":" + pageNo + ",\"pageSize\":" + pageSize + ",\"platform\":\"\",\"province\":\"\",\"startDay\":\"" + rq + "\",\"endDay\":\"" + rq + "\",\"orderStatus\":\"\",\"giftFlag\":\"\",\"clickOrOrderDay\":1,\"skuId\":\"\",\"isDaily\":false,\"clickOrOrderCaliber\":0,\"orderStatusCategory\":1,\"requestFrom\":0}", ContentType.APPLICATION_JSON);
            httppost.setConfig(requestConfig.build());
            httppost.setEntity(param);
            httpresponse = httpClient.execute(httppost);
            String body = EntityUtils.toString(httpresponse.getEntity());
            if (!StringUtil.isJson2(body)) {
                return null;
            }
            JSONObject obj = JSONObject.parseObject(body);
            if (obj.containsKey("code") && obj.getIntValue("code") == -3010 && !retry) {
                TimeUnit.MINUTES.sleep(1);
                return dataList2(dp, rq, pageNo, pageSize, true);
            }
            if (!obj.containsKey("code") || obj.getIntValue("code") != 1) {
                return null;
            }

            if (obj.containsKey("data")) {
                JSONObject data = obj.getJSONObject("data");
                if (null != data && data.containsKey("rows")) {
                    return data.getJSONArray("rows");
                }
            }
            return new JSONArray();
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        } finally {
            if (httppost != null) {
                httppost.releaseConnection();
            }
        }
        return null;
    }

    public static List<String> spuItemBanList(Dpjd dp, String spuid, String campaignId, String adGroupId ,boolean retry) {
        HttpPost httppost = null;
        HttpResponse httpresponse;
        List<String> skuidList = new ArrayList<>() ;
        boolean currIsMaster = dp.getCurrPinAccount().isMaster();
        try {
            CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(new BasicCookieStore()).build();
            RequestConfig.Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000).setConnectionRequestTimeout(20000).setCircularRedirectsAllowed(true);
            String documentCookie = dp.getDocumentCookie();
            if (dp.isFbp() && dp.getCurrPinAccount().isMaster()) {
                httppost = new HttpPost("https://jzt-api.jd.com/reweb/swa/account/campaign/list");
            } else {
                httppost = new HttpPost("https://xnjzt-api.jd.com/swa/forbidden/list");
            }
            httppost.setHeader("accept", "application/json, text/plain, */*");
            httppost.setHeader("accept-encoding", "gzip, deflate, br");
            httppost.setHeader("accept-language", "zh-CN,zh;q=0.9");
            httppost.setHeader("content-type", "application/json;charset=UTF-8");
            httppost.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
            if (dp.isFbp() && dp.getCurrPinAccount().isMaster()) {
                httppost.setHeader("origin", "https://jzt.jd.com");
                httppost.setHeader("referer", "https://jzt.jd.com/");
            } else {
                httppost.setHeader("origin", "https://xnjzt.jd.com");
                httppost.setHeader("referer", "https://xnjzt.jd.com/");
            }
            httppost.setHeader("sec-ch-ua", "Not;A=Brand\";v=\"99\", \"Chromium\";v=\"106");
            httppost.setHeader("sec-ch-ua-mobile", "?0");
            httppost.setHeader("sec-ch-ua-platform", "\"windows\"");
            httppost.setHeader("sec-fetch-dest", "empty");
            httppost.setHeader("sec-fetch-mode", "cors");
            httppost.setHeader("sec-fetch-site", "same-site");
            httppost.setHeader("Cookie", documentCookie);
            StringEntity param = new StringEntity("{\"spuIds\":["+spuid+"],\"businessType\":*********,\"searchType\":2,\"campaignId\":"+campaignId+",\"adGroupId\":"+adGroupId+",\"requestFrom\":0}", ContentType.APPLICATION_JSON);
            httppost.setConfig(requestConfig.build());
            httppost.setEntity(param);
            httpresponse = httpClient.execute(httppost);
            String body = EntityUtils.toString(httpresponse.getEntity());
            if (!StringUtil.isJson2(body)) {
                return null;
            }
            JSONObject obj = JSONObject.parseObject(body);
            if (obj.containsKey("code") && obj.getIntValue("code") == -3010 && !retry) {
                TimeUnit.MINUTES.sleep(1);
                return spuItemBanList(dp, spuid, campaignId, adGroupId, true);
            }
            if (!obj.containsKey("code") || obj.getIntValue("code") != 1) {
                return null;
            }

            if (obj.containsKey("data")) {
                JSONObject data = obj.getJSONObject("data");
                if (null != data && data.containsKey("spuSkuErrorMap")) {
                    JSONObject spuSkuErrorMap = data.getJSONObject("spuSkuErrorMap");
                    if(spuSkuErrorMap!=null ){
                        JSONArray jsonArray = spuSkuErrorMap.getJSONArray(spuid);
                        for (int i = 0; i < jsonArray.size(); i++) {
                            JSONObject jsonObject = jsonArray.getJSONObject(i);
                            String skuid = jsonObject.getString("skuId");
                            skuidList.add(skuid);
                        }
                    }
                }
            }
            return skuidList ;
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        } finally {
            if (httppost != null) {
                httppost.releaseConnection();
            }
        }
        return null;
    }
}
