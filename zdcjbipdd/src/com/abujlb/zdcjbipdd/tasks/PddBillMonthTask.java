package com.abujlb.zdcjbipdd.tasks;

import com.abujlb.CommonConfig;
import com.abujlb.zdcjbipdd.bean.BiDpConfig;
import com.abujlb.zdcjbipdd.bean.BiInfo;
import com.abujlb.zdcjbipdd.bean.BiZdcjDpRpa;
import com.abujlb.zdcjbipdd.bean.CjzhPdd;
import com.abujlb.zdcjbipdd.bean.msgbean.BiPddMsgBean;
import com.abujlb.zdcjbipdd.http.BiPddDzzxHttp;
import com.abujlb.zdcjbipdd.http.DownloadHttp;
import com.abujlb.zdcjbipdd.server.BiDataOssUtil;
import com.abujlb.zdcjbipdd.server.DataUploader;
import com.abujlb.zdcjbipdd.threads.BiThreadLocalObjects;
import com.abujlb.zdcjbipdd.tsdao.BiZdcjDpRpaTsDao;
import com.abujlb.zdcjbipdd.util.BiDateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 账单采集
 *
 * <AUTHOR>
 * @date 2023/4/13 9:43
 */
@Component
public class PddBillMonthTask {

    private static final Logger LOG = Logger.getLogger(PddBillMonthTask.class);
    @Autowired
    private BiZdcjDpRpaTsDao biZdcjDpRpaTsDao;

    /**
     * 凌晨采集 采集不到 会在早上登录时再次采集
     *
     * <ul>
     *     <li>采集页面地址：https://cashier.pinduoduo.com/main/bills</li>
     *     <li>采集页面描述：拼多多商家后台(https://mms.pinduoduo.com/home/<USER>/li>
     *     <li></li>
     *
     *
     * </ul>
     *
     * @param biPddMsgBean
     */
    public void bill(BiPddMsgBean biPddMsgBean) {
        final BiInfo biInfo = BiThreadLocalObjects.getBiInfo();
        final CjzhPdd cjzhPdd = BiThreadLocalObjects.getCjzh();
        try {

            if (Objects.isNull(biInfo) || Objects.isNull(cjzhPdd) || Objects.isNull(biPddMsgBean)) {
                return;
            }

            int bill = biInfo.getBiDpConfig().getBill();
            if (bill == 1 && (biInfo.getCjrq().getMonth_bill() == null || biInfo.getCjrq().getMonth_bill().compareTo(BiDateUtil.lastday()) < 0)) {
                updateRq(biInfo, cjzhPdd, BiDateUtil.lastday());
                return;
            }

            List<String> rqList = BiDateUtil.getCjsj(biInfo.getStart(), biInfo.getCjrq().getMonth_bill());
            if (CollectionUtils.isEmpty(rqList)) {
                return;
            }


            boolean needCheckAuth = false;
            String lastrq = null;
            for (String rq : rqList) {
                if (BiDateUtil.isLastDayForMonth(rq)) {
                    needCheckAuth = true;
                    break;
                }
                lastrq = rq;
            }

            if (StringUtils.isNotBlank(lastrq)) {
                updateRq(biInfo, cjzhPdd, lastrq);
            }

            if (!needCheckAuth) {
                return;
            }

            boolean success = biInfo.getBiDpConfig().getOverseas() == 1 || BiPddDzzxHttp.auth(cjzhPdd);
            if (!success) {
                return;
            }

            for (String rq : rqList) {
                if (BiDateUtil.isLastDayForMonth(rq)) {
                    if (BiDateUtil.getBetweenDate(rq, BiDateUtil.today()).size() < 3) {
                        break;
                    }
                    String start = BiDateUtil.getFirstDayOfMonth(rq);
                    String end = rq;
                    if (billtask(cjzhPdd, biInfo, start, end)) {
                        updateRq(biInfo, cjzhPdd, end);
                        saveRPA(biInfo, cjzhPdd, "MONTH_BILL", rq.substring(0, 7));
                    } else {
                        break;
                    }
                }
            }
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
    }

    /**
     * 采集拼多多账单
     *
     * @return
     */
    private boolean billtask(CjzhPdd cjzhPdd, BiInfo biInfo, String start, String end) {
        // type 1 :贷款账户 2营销账户 3：保证金账户
        BiDpConfig biDpConfig = biInfo.getBiDpConfig();
        boolean overSeas = biDpConfig.getOverseas() == 1;
        if (!overSeas) {
            BiPddDzzxHttp.index2(cjzhPdd);
        }
        final int[] types = overSeas ? new int[]{1} : new int[]{1, 2, 3};
        boolean allSuccess = true;
        for (int type : types) {
            //生成
            String downloadUrl = null;
            String localFilePath = null;

            if (BiPddDzzxHttp.bill(cjzhPdd, type, start, end, overSeas) && Objects.nonNull(downloadUrl = BiPddDzzxHttp.dkhDownloadUrl(cjzhPdd, type, overSeas)) && Objects.nonNull(localFilePath = DownloadHttp.downloadDkzh(cjzhPdd, type, downloadUrl, overSeas))) {

                File file = new File(localFilePath);
                if (file.exists()) {
                    if (type == 1) {
                        BiDataOssUtil.upload(file, "bi_data/month/" + end.substring(0, 7).replaceAll("-", "") + "/" + biInfo.getYhid() + "/" + biInfo.getQd() + "/" + cjzhPdd.getMallId() + "/货款账户.zip");
                    } else if (type == 2) {
                        BiDataOssUtil.upload(file, "bi_data/month/" + end.substring(0, 7).replaceAll("-", "") + "/" + biInfo.getYhid() + "/" + biInfo.getQd() + "/" + cjzhPdd.getMallId() + "/营销账户.zip");
                    } else if (type == 3) {
                        BiDataOssUtil.upload(file, "bi_data/month/" + end.substring(0, 7).replaceAll("-", "") + "/" + biInfo.getYhid() + "/" + biInfo.getQd() + "/" + cjzhPdd.getMallId() + "/保证金账户.zip");
                    }
                    file.delete();


                    //文件夹删除
                    File folder = new File(CommonConfig.getString("tempdir") + "/" + cjzhPdd.getMallId());
                    folder.delete();
                }
            } else {
                allSuccess = false;
                break;
            }
        }
        return allSuccess;
    }

    private void updateRq(BiInfo biInfo, CjzhPdd cjzhPdd, String rq) {
        biInfo.getCjrq().setMonth_bill(rq);
        DataUploader.updateBiInfo(biInfo);
    }

    private void saveRPA(BiInfo biInfo, CjzhPdd cjzh, String code, String month) {
        BiZdcjDpRpa rpaData = new BiZdcjDpRpa();
        rpaData.setYhid(biInfo.getYhid());
        rpaData.setQd(biInfo.getQd());
        rpaData.setUserid(biInfo.getUserid());
        rpaData.setZzhmc(cjzh.getDpmc());
        rpaData.setDpmc(biInfo.getDpmc());
        rpaData.setCode(code);
        rpaData.setCjrq(month);
        rpaData.setCreateTime(BiDateUtil.getCurrTime());
        biZdcjDpRpaTsDao.updateRows(Collections.singletonList(rpaData));
    }
}
