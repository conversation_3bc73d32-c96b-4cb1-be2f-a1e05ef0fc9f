package com.abujlb.dataprobitaskfbp.service;

import com.abujlb.dataprobitaskfbp.bean.DataprobiTaskMsg;
import com.abujlb.dataprobitaskfbp.bean.JdFbpSpCate;
import com.abujlb.dataprobitaskfbp.oss.BiDataOssUtil;
import com.abujlb.dataprobitaskfbp.sqlite.BiSqliteDb;
import com.abujlb.dataprobitaskfbp.tsdao.SpNewTsDao;
import com.abujlb.dataprobitaskfbp.tsdao.SpTsDao;
import com.abujlb.dataprobitaskfbp.utils.DataUploader;
import com.abujlb.dataprobitaskfbp.utils.DateUtil;
import com.abujlb.dataprobitaskfbp.utils.FileUtil;
import com.abujlb.dataprobitaskfbp.utils.SqliteDbUtil;
import com.abujlb.filereader.DefaultDataFormater;
import com.abujlb.sqlite.SqliteStatement;
import com.abujlb.util.StringUtil;
import com.abujlb.util.XLSXCovertCSVReader2;
import com.alibaba.fastjson.JSONArray;
import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.apache.poi.openxml4j.opc.OPCPackage;
import org.apache.poi.openxml4j.opc.PackageAccess;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.List;

@Component
public class JdFbpSpCateTaskRunnable {

    private static final Logger LOG = Logger.getLogger(JdFbpSpCateTaskRunnable.class);

    private final String initSql = "CREATE TABLE jd_fbp_sp_cate(bbid varchar(100) ,spuid varchar(100),spmc  text,sjzt text,sjsj text,cjsj text,oneLmid varchar(50),onelmmc varchar(100),twoLmid varchar(100),twolmmc varchar(100),threeLmid varchar(100),threelmmc varchar(100) ,cid varchar(100),cidmc varchar(100),lmmcs varchar(200),lmids varchar(200), PRIMARY KEY (`bbid`)) ;";
    private final String insertSql = "INSERT INTO jd_fbp_sp_cate(bbid,spuid,spmc,sjzt,oneLmid,onelmmc,twoLmid,twolmmc,threeLmid,threelmmc,cid,cidmc,lmmcs,lmids,sjsj,cjsj) values({bbid},{spuid},{spmc},{sjzt},{oneLmid},{onelmmc},{twoLmid},{twolmmc},{threeLmid},{threelmmc},{cid},{cidmc},{lmmcs},{lmids},{sjsj},{cjsj}) ;";
    private final String sql = "SELECT onelmmc,twolmmc,threelmmc from jd_fbp_sp_cate GROUP BY onelmmc,twolmmc,threelmmc ;";
    private final String sql2 = "SELECT COUNT(bbid) from jd_fbp_sp_cate ;";
    private final String updateLmSql = "UPDATE jd_fbp_sp_cate set oneLmid={oneLmid},twoLmid={twoLmid},threeLmid={threeLmid},cid={cid},lmmcs={lmmcs},lmids={lmids} where onelmmc={onelmmc} and twolmmc={twolmmc} and threelmmc={threelmmc} ;";
    private final int pageSize = 100;
    @Autowired
    private BiDataOssUtil biDataOssUtil;

    @Autowired
    private SpTsDao spTsDao;
    @Autowired
    private SpNewTsDao spNewTsDao;

    public void deal(DataprobiTaskMsg taskMsg) {
        long start = System.currentTimeMillis();
        LOG.info("渠道：" + taskMsg.getQd() + "，店铺名：" + taskMsg.getDpmc() + ">>商品类目>>" + taskMsg.getRqList() + ">>开始：" + DateUtil.getCurrentTime());
        BiSqliteDb sqliteDb = null;
        SqliteStatement<JdFbpSpCate> insert = null;
        SqliteStatement<JdFbpSpCate> selectLmStatement = null;
        SqliteStatement<JdFbpSpCate> updateLmStatment = null;
        Statement statement = null;
        OPCPackage p = null;
        File file = null;
        try {
            List<String> fbpspmx = taskMsg.getRqList();
            if (CollectionUtils.isEmpty(fbpspmx)) {
                return;
            }

            String rq = fbpspmx.get(fbpspmx.size() - 1);
            String osskey = "bi_jd/jdsz/" + taskMsg.getUserid() + "/" + rq + "/fbp_spmx.xlsx";
            if (!biDataOssUtil.exist(osskey)) {
                return;
            }
            String dbFilepath = FileUtil.createDBFilePath();
            String filepath = FileUtil.createXlsxFilePath();

            biDataOssUtil.download(osskey, filepath);
            file = new File(filepath);

            sqliteDb = new BiSqliteDb(dbFilepath, initSql, null);
            sqliteDb.startTransaction();
            insert = sqliteDb.createSqliteStatement(insertSql, JdFbpSpCate.class);

            //将fbp_spmx.xlsx中的数据 存储到sqlite中
            p = OPCPackage.open(file.getPath(), PackageAccess.READ);
            XLSXCovertCSVReader2 xlsx2csv = new XLSXCovertCSVReader2(p, "", 5000);
            xlsx2csv.process2(JdFbpSpCate.class, new DefaultDataFormater(), insert);
            sqliteDb.commit();

            //查询有多少类目，需要去调用接口获取类目id
            selectLmStatement = sqliteDb.createSqliteStatement(sql, JdFbpSpCate.class);
            List<JdFbpSpCate> jdFbpSpCates = selectLmStatement.queryForList(null, JdFbpSpCate.class);

            //类目id赋值
            for (JdFbpSpCate jdFbpSpCate : jdFbpSpCates) {
                String lmmcs = jdFbpSpCate.getOnelmmc() + ">>>" + jdFbpSpCate.getTwolmmc() + ">>>" + jdFbpSpCate.getThreelmmc();
                jdFbpSpCate.setLmmcs(lmmcs);

                String sjlmsm = DataUploader.getCategoryJdSjlmsm(jdFbpSpCate.getOnelmmc(), jdFbpSpCate.getTwolmmc(), jdFbpSpCate.getThreelmmc());
                if (!StringUtil.isJsonArray(sjlmsm)) {
                    continue;
                }
                JSONArray jsonArray = JSONArray.parseArray(sjlmsm);
                if (jsonArray != null && !jsonArray.isEmpty()) {
                    String oneLmid = jsonArray.getJSONObject(0).getString("id");
                    String twoLmid = jsonArray.getJSONObject(1).getString("id");
                    String threeLmid = jsonArray.getJSONObject(2).getString("id");
                    String lmmids = oneLmid + ">>>" + twoLmid + ">>>" + threeLmid;

                    jdFbpSpCate.setOneLmid(oneLmid);
                    jdFbpSpCate.setTwoLmid(twoLmid);
                    jdFbpSpCate.setThreeLmid(threeLmid);
                    jdFbpSpCate.setCid(threeLmid);
                    jdFbpSpCate.setLmids(lmmids);
                }
            }

            //更新类目id
            updateLmStatment = sqliteDb.createSqliteStatement(updateLmSql, JdFbpSpCate.class);
            updateLmStatment.update(jdFbpSpCates);
            sqliteDb.commit();
            jdFbpSpCates.clear();

            List<JdFbpSpCate> list = null;

            //当前页码
            int curr = 1;
            statement = sqliteDb.createStatement();
            ResultSet resultSet = statement.executeQuery(sql2);
            //总条数
            int totalCount = resultSet.getInt(1);
            resultSet.close();
            //总页码
            int pageCount = pageCount(totalCount);

            String pageQuerySql = null;
            SqliteStatement<JdFbpSpCate> selectStatment = null;
            //分页查询 保存
            while (curr <= pageCount) {
                pageQuerySql = "SELECT bbid,spuid,spmc,sjzt,sjsj,cjsj,oneLmid,onelmmc,twoLmid,twolmmc,threeLmid,threelmmc,cid,cidmc,lmmcs,lmids from jd_fbp_sp_cate  limit " + pageSize + " offset " + (curr - 1) * pageSize + ";";
                try {
                    selectStatment = sqliteDb.createSqliteStatement(pageQuerySql, JdFbpSpCate.class);
                    //当前页数据
                    list = selectStatment.queryForList(null, JdFbpSpCate.class);
                    //存储
                    spTsDao.updateJdFbpSpCate(list, taskMsg.getYhid(), taskMsg.getQd(), taskMsg.getUserid());
                    spNewTsDao.updateJdFbpSpCate(list, taskMsg.getYhid(), taskMsg.getQd(), taskMsg.getUserid());
                } finally {
                    if (selectStatment != null) {
                        selectStatment.close();
                    }
                }
                list.clear();
                curr++;
            }
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        } finally {
            //关闭资源
            SqliteDbUtil.closeSqliteStatements(updateLmStatment, selectLmStatement, insert);
            SqliteDbUtil.closeStatement(statement);
            SqliteDbUtil.close(sqliteDb);

            if (p != null) {
                try {
                    p.close();
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            }
            FileUtil.deleteFile(file);
            long end = System.currentTimeMillis();
            LOG.info("渠道：" + taskMsg.getQd() + "，店铺名：" + taskMsg.getDpmc() + ">>商品类目>>" + taskMsg.getRqList() + ">>结束：" + DateUtil.getCurrentTime() + ">>耗时：" + (end - start) / 1000 + "秒");
        }
    }

    private int pageCount(int total) {
        if (total < 1) {
            return 0;
        }
        return (total + pageSize - 1) / pageSize;
    }
}
