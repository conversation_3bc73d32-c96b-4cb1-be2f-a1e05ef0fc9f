package com.abujlb.dataprobitaskfbp.service.xkfx;

import com.abujlb.dataprobitaskfbp.bean.DataprobiTaskMsg;
import com.abujlb.dataprobitaskfbp.bean.Sp;
import com.abujlb.dataprobitaskfbp.bean.SqliteSp;
import com.abujlb.dataprobitaskfbp.bean.xkfx.FbpSpuSpxg;
import com.abujlb.dataprobitaskfbp.bean.xkfx.JdXkfxBO;
import com.abujlb.dataprobitaskfbp.bean.xkfx.SpuSpxg;
import com.abujlb.dataprobitaskfbp.bean.xkfx.XkfxCidData;
import com.abujlb.dataprobitaskfbp.constants.BiConstant;
import com.abujlb.dataprobitaskfbp.constants.RqlxConst;
import com.abujlb.dataprobitaskfbp.oss.BiDataOssUtil;
import com.abujlb.dataprobitaskfbp.sqlite.BiSqliteDb;
import com.abujlb.dataprobitaskfbp.tsdao.BiXkfxTsDao;
import com.abujlb.dataprobitaskfbp.tsdao.SpNewTsDao;
import com.abujlb.dataprobitaskfbp.utils.DateUtil;
import com.abujlb.dataprobitaskfbp.utils.FileUtil;
import com.abujlb.dataprobitaskfbp.utils.MathUtil;
import com.abujlb.dataprobitaskfbp.utils.SqliteDbUtil;
import com.abujlb.filereader.DefaultDataFormater;
import com.abujlb.filereader.XlsCsvFileReader;
import com.abujlb.sqlite.SqliteStatement;
import com.google.gson.Gson;
import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 京东选款分析
 */
@Component
public class JdXkfxTask {

    private static final Logger LOG = Logger.getLogger(JdXkfxTask.class);

    @Autowired
    private SqliteDbUtil sqliteDbUtil;

    @Autowired
    private BiXkfxTsDao biXkfxTsDao;

    @Autowired
    private SpNewTsDao spNewTsDao;

    @Autowired
    private BiDataOssUtil biDataOssUtil;

    public void process(DataprobiTaskMsg taskMsg) {
        long start = System.currentTimeMillis();
        LOG.info("渠道：" + taskMsg.getQd() + "，店铺名：" + taskMsg.getDpmc() + ">>选款分析>>" + taskMsg.getRqList() + ">>开始：" + DateUtil.getCurrentTime());
        try {
            List<String> rqList = taskMsg.getRqList();
            for (String rq : rqList) {
                //单天
                LOG.info("1-->" + DateUtil.getCurrentTime());
                day(taskMsg, rq);
                LOG.info("2-->" + DateUtil.getCurrentTime());
                //月
                if (DateUtil.isLastDayForMonth(rq)) {
                    dealRecent(taskMsg, RqlxConst.MONTH, rq.substring(0, 7), DateUtil.getFirstdayOfMonth(rq), rq);
                }
                //7天、30天
                if (rq.compareTo(DateUtil.getLastday()) == 0) {
                    biXkfxTsDao.deleteRecent(taskMsg.getYhid(), taskMsg.getQd(), taskMsg.getUserid());
                    LOG.info("3-->" + DateUtil.getCurrentTime());
                    dealRecent(taskMsg, RqlxConst.RECENT7, "", DateUtil.getDaysBeforeAsString(rq, 6), rq);
                    LOG.info("4-->" + DateUtil.getCurrentTime());
                    dealRecent(taskMsg, RqlxConst.RECENT30, "", DateUtil.getDaysBeforeAsString(rq, 29), rq);
                    LOG.info("5-->" + DateUtil.getCurrentTime());
                }
            }
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
        long end = System.currentTimeMillis();
        LOG.info("渠道：" + taskMsg.getQd() + "，店铺名：" + taskMsg.getDpmc() + ">>选款分析>>" + taskMsg.getRqList() + ">>结束：" + DateUtil.getCurrentTime() + ">>耗时：" + (end - start) / 1000 + "秒");
    }

    private void dealRecent(DataprobiTaskMsg taskMsg, String rqlx, String rq, String start, String end) {
        BiSqliteDb totalDb = null;
        PreparedStatement preparedStatement = null;
        Statement statement = null;

        try {
            totalDb = new BiSqliteDb(FileUtil.createDBFilePath(), BiConstant.JD_XKFX_INITSQL, null);
            statement = totalDb.createStatement();
            totalDb.startTransaction();
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
            SqliteDbUtil.closeStatement(statement);
            SqliteDbUtil.close(totalDb);
            return;
        }

        SqliteStatement<JdXkfxBO> sqliteStatement = null;
        try {
            final String sql = "INSERT INTO jd_xkfx  SELECT * FROM db2.jd_xkfx ;";
            List<String> rqList = DateUtil.getBetweenDate(start, end);
            for (String rq_temp : rqList) {
                String osskey = "bi_jysj/day/" + rq_temp + "/" + taskMsg.getQd() + "/" + taskMsg.getUserid() + "/jdxkfx.db";
                if (!biDataOssUtil.exist(osskey)) {
                    continue;
                }

                String temppath = FileUtil.createDBFilePath();
                biDataOssUtil.download(osskey, temppath);
                BiSqliteDb tempDb = null;
                try {
                    tempDb = new BiSqliteDb(temppath);
                    preparedStatement = totalDb.prepareStatement("ATTACH DATABASE ? AS db2;");
                    preparedStatement.setString(1, temppath);
                    preparedStatement.executeUpdate();
                    statement.executeUpdate(sql);
                    totalDb.commit();
                    // 分离数据库
                    statement.executeUpdate("DETACH DATABASE db2;");
                } catch (SQLException e) {
                    LOG.error(e.getMessage(), e);
                    SqliteDbUtil.closeStatement(statement);
                    SqliteDbUtil.close(totalDb);
                    return;
                } finally {
                    SqliteDbUtil.closeStatement(preparedStatement);
                    SqliteDbUtil.close(tempDb);
                }
            }

            final int pageSize = 1000;
            int pageNo = 1;
            while (true) {
                String page_sql = String.format(BiConstant.JD_XKFX_SUMSQL, pageSize, (pageNo - 1) * pageSize);
                sqliteStatement = totalDb.createSqliteStatement(page_sql, JdXkfxBO.class);

                List<JdXkfxBO> list = sqliteStatement.queryForList(null, JdXkfxBO.class);
                for (JdXkfxBO jdXkfxBO : list) {
                    jdXkfxBO.setBbid(jdXkfxBO.getSpuid());
                    jdXkfxBO.setQd("JD");
                    jdXkfxBO.setYhid(taskMsg.getYhid());
                    jdXkfxBO.setUserid(taskMsg.getUserid());
                    jdXkfxBO.setUvjz(MathUtil.calZhlv(jdXkfxBO.getXse(), jdXkfxBO.getFks()));
                    jdXkfxBO.setZfzhlv(MathUtil.calZhlv(jdXkfxBO.getZfmjs(), jdXkfxBO.getFks()));
                    jdXkfxBO.setSclv(MathUtil.calZhlv(jdXkfxBO.getScrs(), jdXkfxBO.getFks()));
                    jdXkfxBO.setRqlx(rqlx);
                    jdXkfxBO.setRq(rq);
                    jdXkfxBO.setJglv(MathUtil.calZhlv(jdXkfxBO.getJgrs(), jdXkfxBO.getFks()));
                    jdXkfxBO.setDjlv(MathUtil.calZhlv(jdXkfxBO.getDjcs(), jdXkfxBO.getBgl()));

                    String tghf = String.format(BiConstant.JD_XKFX_TGHF_FORMAT, jdXkfxBO.getJdkchf(), jdXkfxBO.getJdkccjje(), jdXkfxBO.getGwcdhf(), jdXkfxBO.getGwcdcjje(), jdXkfxBO.getBthf(), jdXkfxBO.getJdouhf(), jdXkfxBO.getJdzthf(), jdXkfxBO.getJdztcjje(), jdXkfxBO.getJdzwhf(), jdXkfxBO.getJdzwcjje(), jdXkfxBO.getJtkhf(), jdXkfxBO.getJtkhfZd());
                    jdXkfxBO.setTghf(tghf);


                    Sp sp = spNewTsDao.getSpBySpuid(jdXkfxBO.getBbid());
                    if (sp != null) {
                        if (sp.getCid() != null) {
                            jdXkfxBO.setCid(sp.getCid());
                        }
                        if (sp.getTid() != null) {
                            jdXkfxBO.setTid(sp.getTid());
                        }
                    }
                }

                sqliteStatement.close();
                biXkfxTsDao.updateRowsJd(list);
                boolean next = list.size() == pageSize;
                list.clear();
                if (next) {
                    pageNo++;
                } else {
                    break;
                }
            }


            if (rqlx.equals(RqlxConst.RECENT30)) {
                //https://www.tapd.cn/47708728/prong/stories/view/1147708728001008900
                //处理CID 和 fks 商品数
                ResultSet resultSet = statement.executeQuery("SELECT sum(fks) as fks,count(bbid) as sps ,cid  FROM jd_xkfx where cid is not null group by cid ; ");
                List<XkfxCidData> xkfxCidDataList = new ArrayList<>();

                while (resultSet.next()) {
                    int fks = resultSet.getInt("fks");
                    int sps = resultSet.getInt("sps");
                    String cid = resultSet.getString("cid");
                    XkfxCidData cidData = new XkfxCidData();
                    cidData.setCid(cid);
                    cidData.setFks(fks);
                    cidData.setSps(sps);
                    xkfxCidDataList.add(cidData);
                }

                String osskey = "bi_data/xkfx/" + taskMsg.getYhid() + "_" + taskMsg.getQd() + "_" + taskMsg.getUserid() + ".json";
                biDataOssUtil.upload(new Gson().toJson(xkfxCidDataList), osskey);

                resultSet.close();
                xkfxCidDataList.clear();
            }
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        } finally {
            SqliteDbUtil.closeSqliteStatements(sqliteStatement);
            SqliteDbUtil.close(totalDb);
        }
    }

    private void day(DataprobiTaskMsg taskMsg, String rq) {
        /**
         * 单天的商品sycm_sp.db的数据
         */
        String key = String.format(BiConstant.SYCM_SP_DAY, rq.replace("-", ""), taskMsg.getYhid(), "JD", taskMsg.getUserid());
        BiSqliteDb spSqliteDb = sqliteDbUtil.getSpSqliteDb(key);
        if (spSqliteDb == null) {
            return;
        }

        BiSqliteDb dayDb = null;
        try {
            dayDb = new BiSqliteDb(FileUtil.createDBFilePath(), BiConstant.JD_XKFX_INITSQL, null);
            dayDb.startTransaction();
        } catch (Exception e) {
            SqliteDbUtil.close(dayDb);
            LOG.error(e.getMessage(), e);
            return;
        }

        /**
         * 初始化表jd_xkfx 用来存储单天的sku纬度的商品选款分析数据
         */
        SqliteDbUtil.executeSql(spSqliteDb, BiConstant.JD_XKFX_INITSQL);
        //初始化表spu_spxg
        SqliteDbUtil.executeSql(spSqliteDb, BiConstant.JD_XKFX_SPU_INITSQL);

        SqliteStatement<SpuSpxg> spuInsert = null;
        SqliteStatement<FbpSpuSpxg> FBPSpuInsert = null;
        String spuLocalFilePath = null;
        try {
            spSqliteDb.startTransaction();
            /**
             * 创建
             * 1、读取spu.xlsx (fbp 报表 SPU   pop报表商品ID）
             *    fbp是spu.xls 实际上是xlsx
             *    pop是spu.xlsx
             * 2、插入spu.db
             */
            spuLocalFilePath = FileUtil.createXlsxFilePath();
            String spuOssFilePath = "bi_jd/jdsz/" + taskMsg.getUserid() + "/" + rq + "/spu.xlsx";
            String fbpSpuOssFilePath = "bi_jd/jdsz/" + taskMsg.getUserid() + "/" + rq + "/spu.xls";
            if (biDataOssUtil.exist(spuOssFilePath)) {
                //pop
                spuInsert = spSqliteDb.createSqliteStatement(BiConstant.JD_XKFX_SPU_INSERTSQL, SpuSpxg.class);
                biDataOssUtil.download(spuOssFilePath, spuLocalFilePath);
            } else if (biDataOssUtil.exist(fbpSpuOssFilePath)) {
                //fbp
                FBPSpuInsert = spSqliteDb.createSqliteStatement(BiConstant.JD_XKFX_SPU_INSERTSQL, FbpSpuSpxg.class);
                biDataOssUtil.download(fbpSpuOssFilePath, spuLocalFilePath);
            } else {
                SqliteDbUtil.close(spSqliteDb);
                SqliteDbUtil.close(dayDb);
                FileUtil.deleteFile(spuLocalFilePath);
                return;
            }

            /**
             * 将spu.xlsx写入表spu_spxg
             */
            if (spuInsert != null) {
                XlsCsvFileReader.readFile(spuLocalFilePath, SpuSpxg.class, new DefaultDataFormater(), spuInsert);
            }
            if (FBPSpuInsert != null) {
                XlsCsvFileReader.readFile(spuLocalFilePath, FbpSpuSpxg.class, new DefaultDataFormater(), FBPSpuInsert);
            }
            //删除spu.xlsx中  总计这个数据行
            SqliteDbUtil.executeSql(spSqliteDb, BiConstant.JD_XKFX_DELETE_SPUSPXG);
            SqliteDbUtil.executeSql(spSqliteDb, BiConstant.JD_XKFX_DELETE_SPUSPXG_2);

            spSqliteDb.commit();
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
            SqliteDbUtil.close(spSqliteDb);
            SqliteDbUtil.close(dayDb);
            return;
        } finally {
            SqliteDbUtil.closeSqliteStatements(spuInsert);
            SqliteDbUtil.closeSqliteStatements(FBPSpuInsert);
            FileUtil.deleteFile(spuLocalFilePath);
        }

        Map<String, String> skuid_spuid_map = spNewTsDao.initJdSpSpuid(taskMsg.getUserid());

        /**
         * 分页读取sycm_sp.db 写入jd_xkfx中
         * 1、分页读取sycm_sp.db
         * 2、转换成List<JdXkfxBO>
         * 3、去sp表获取spuid
         * 4、写入jd_xkfx
         */
        int pageNo = 1;
        final int pageSize = 1000;
        SqliteStatement<JdXkfxBO> jdXkfxBOSqliteStatement = null;
        SqliteStatement<SqliteSp> sqliteStatement = null;
        try {
            while (true) {
                sqliteStatement = spSqliteDb.createSqliteStatement(String.format(BiConstant.DAY_SYCM_SP_PAGE_SELECT_SQL, pageSize, (pageNo - 1) * pageSize), SqliteSp.class);
                jdXkfxBOSqliteStatement = spSqliteDb.createSqliteStatement(BiConstant.JD_XKFX_INSERTSQL, JdXkfxBO.class);
                List<SqliteSp> list = sqliteStatement.queryForList(null, SqliteSp.class);
                List<JdXkfxBO> jdXkfxBOList = list.stream().map(JdXkfxBO::new).collect(Collectors.toList());

                for (JdXkfxBO jdXkfxBO : jdXkfxBOList) {
                    jdXkfxBO.setRqlx(RqlxConst.DAY);
                    jdXkfxBO.setRq(rq);
                    //通过skuid获取spuid
                    if (skuid_spuid_map.containsKey(jdXkfxBO.getBbid())) {
                        jdXkfxBO.setSpuid(skuid_spuid_map.get(jdXkfxBO.getBbid()));
                    }
                }

                jdXkfxBOSqliteStatement.insert(jdXkfxBOList);
                spSqliteDb.commit();
                SqliteDbUtil.closeSqliteStatements(sqliteStatement);
                SqliteDbUtil.closeSqliteStatements(jdXkfxBOSqliteStatement);

                boolean flag = jdXkfxBOList.size() < pageSize;
                jdXkfxBOList.clear();
                list.clear();
                if (flag) {
                    break;
                }
                pageNo++;
            }
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
            SqliteDbUtil.closeSqliteStatements(sqliteStatement);
            SqliteDbUtil.closeSqliteStatements(jdXkfxBOSqliteStatement);
            SqliteDbUtil.close(spSqliteDb);
            SqliteDbUtil.close(dayDb);
            return;
        }

        SqliteDbUtil.executeSql(spSqliteDb, BiConstant.JD_XKFX_DELETE_SPUID_WRONG);
        try {
            spSqliteDb.commit();
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        pageNo = 1;
        /**
         * 汇总jd_xkfx
         * 1、按照spuid 分组求和，包括花费、经营数据（访客数、支付买家数等）
         * 2、如果spuid在spu.xlsx转化的spu_spxg表里，则用spu_spxg表里的经营数据替换掉 spuid分组求和的经营数据
         *    因为skuid纬度的相加不等于spuid纬度的数据
         * 3、写入表格存储bi_xkfx（京东选款分析是spuid纬度的，所以需要按照spuid分组求和）
         */
        Statement statement = null;
        ResultSet resultSet = null;
        SqliteStatement<JdXkfxBO> sumStatement = null;
        SqliteStatement<JdXkfxBO> dayDbInsert = null;
        try {
            statement = spSqliteDb.createStatement();
            while (true) {
                String sql = String.format(BiConstant.JD_XKFX_SUMSQL, pageSize, (pageNo - 1) * pageSize);
                sumStatement = spSqliteDb.createSqliteStatement(sql, JdXkfxBO.class);
                dayDbInsert = dayDb.createSqliteStatement(BiConstant.JD_XKFX_INSERTSQL, JdXkfxBO.class);
                List<JdXkfxBO> jdXkfxBOList = sumStatement.queryForList(null, JdXkfxBO.class);
                for (JdXkfxBO jdXkfxBO : jdXkfxBOList) {
                    jdXkfxBO.setRqlx(RqlxConst.DAY);
                    jdXkfxBO.setRq(rq);
                    jdXkfxBO.setBbid(jdXkfxBO.getSpuid());
                    //通过spuid获取采集的访客数等数据，不用sku汇总的
                    //因为sku汇总的！= 采集的spu（也就是不等于页面上显示的）
                    resultSet = statement.executeQuery("SELECT * FROM spu_spxg WHERE spuid ='" + jdXkfxBO.getBbid() + "';");
                    if (resultSet != null && resultSet.next()) {
                        jdXkfxBO.setFks(resultSet.getInt("fks"));
                        jdXkfxBO.setLll(resultSet.getInt("lll"));
                        jdXkfxBO.setScrs(resultSet.getInt("scrs"));
                        jdXkfxBO.setJgrs(resultSet.getInt("jgrs"));
                        jdXkfxBO.setJgjs(resultSet.getInt("jgjs"));
                        jdXkfxBO.setBgl(resultSet.getInt("bgl"));
                        jdXkfxBO.setZfmjs(resultSet.getInt("cjkhs"));
                        jdXkfxBO.setPvxhlv(resultSet.getDouble("pvxhlv"));
                        jdXkfxBO.setTslv(resultSet.getDouble("tslv"));
                        jdXkfxBO.setDjcs(resultSet.getInt("djcs"));
                        jdXkfxBO.setDjlv(resultSet.getDouble("djlv"));
                    }
                    if (resultSet != null) {
                        resultSet.close();
                    }

                    jdXkfxBO.setQd("JD");
                    jdXkfxBO.setYhid(taskMsg.getYhid());
                    jdXkfxBO.setUserid(taskMsg.getUserid());

                    jdXkfxBO.setUvjz(MathUtil.calZhlv(jdXkfxBO.getXse(), jdXkfxBO.getFks()));
                    jdXkfxBO.setZfzhlv(MathUtil.calZhlv(jdXkfxBO.getZfmjs(), jdXkfxBO.getFks()));
                    jdXkfxBO.setSclv(MathUtil.calZhlv(jdXkfxBO.getScrs(), jdXkfxBO.getFks()));
                    jdXkfxBO.setJglv(MathUtil.calZhlv(jdXkfxBO.getJgrs(), jdXkfxBO.getFks()));

                    String tghf = String.format(BiConstant.JD_XKFX_TGHF_FORMAT, jdXkfxBO.getJdkchf(), jdXkfxBO.getJdkccjje(), jdXkfxBO.getGwcdhf(), jdXkfxBO.getGwcdcjje(), jdXkfxBO.getBthf(), jdXkfxBO.getJdouhf(), jdXkfxBO.getJdzthf(), jdXkfxBO.getJdztcjje(), jdXkfxBO.getJdzwhf(), jdXkfxBO.getJdzwcjje(), jdXkfxBO.getJtkhf(), jdXkfxBO.getJtkhfZd());
                    jdXkfxBO.setTghf(tghf);


                    Sp sp = spNewTsDao.getSpBySpuid(jdXkfxBO.getBbid());
                    if (sp != null) {
                        if (sp.getCid() != null) {
                            jdXkfxBO.setCid(sp.getCid());
                        }
                        if (sp.getTid() != null) {
                            jdXkfxBO.setTid(sp.getTid());
                        }
                    }
                }
                dayDbInsert.insert(jdXkfxBOList);
                dayDb.commit();
                biXkfxTsDao.updateRowsJd(jdXkfxBOList);

                SqliteDbUtil.closeSqliteStatements(dayDbInsert);
                SqliteDbUtil.closeSqliteStatements(sumStatement);
                if (CollectionUtils.isEmpty(jdXkfxBOList) || jdXkfxBOList.size() < pageSize) {
                    jdXkfxBOList.clear();
                    break;
                }

                jdXkfxBOList.clear();
                jdXkfxBOList = null;
                pageNo++;
            }

            String osskey = "bi_jysj/day/" + rq + "/" + taskMsg.getQd() + "/" + taskMsg.getUserid() + "/jdxkfx.db";
            biDataOssUtil.upload(dayDb.getFileD(), osskey);
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        } finally {
            SqliteDbUtil.closeStatements(statement);
            SqliteDbUtil.closeSqliteStatements(sumStatement);
            SqliteDbUtil.close(spSqliteDb);
            SqliteDbUtil.close(dayDb);
        }
    }
}
