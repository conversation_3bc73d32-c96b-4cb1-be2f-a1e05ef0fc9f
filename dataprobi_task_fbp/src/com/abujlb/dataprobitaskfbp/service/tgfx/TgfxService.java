package com.abujlb.dataprobitaskfbp.service.tgfx;

import com.abujlb.dataprobitaskfbp.bean.DataprobiTaskMsg;
import com.abujlb.dataprobitaskfbp.bean.tgfx.TgfxReport;
import com.abujlb.dataprobitaskfbp.constants.RqlxConst;
import com.abujlb.dataprobitaskfbp.oss.BiDataOssUtil;
import com.abujlb.dataprobitaskfbp.sqlite.BiSqliteDb;
import com.abujlb.dataprobitaskfbp.sqlite.SqlProvider;
import com.abujlb.dataprobitaskfbp.tsdao.BiItemTgfxDataTsDaoV2;
import com.abujlb.dataprobitaskfbp.utils.DateUtil;
import com.abujlb.dataprobitaskfbp.utils.FileUtil;
import com.abujlb.dataprobitaskfbp.utils.SqliteDbUtil;
import com.abujlb.sqlite.SqliteStatement;
import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.List;

@Component
public class TgfxService {

    public static final Logger LOG = Logger.getLogger(TgfxService.class);
    protected static final String REG_EXP = "\\d{2,}";
    @Autowired
    protected BiDataOssUtil biDataOssUtil;
    @Autowired
    protected BiItemTgfxDataTsDaoV2 biItemTgfxDataTsDaoV2;

    /**
     * @param taskMsg
     * @param rqList
     * @param SIGN
     * @param SIGN_DESC
     */
    public final void tgfx(DataprobiTaskMsg taskMsg, List<String> rqList, String SIGN, String SIGN_DESC) {
        if (CollectionUtils.isEmpty(rqList)) {
            return;
        }
        long start = System.currentTimeMillis();
        LOG.info("渠道：" + taskMsg.getQd() + "，店铺名：" + taskMsg.getDpmc() + ">>" + SIGN_DESC + ">>" + rqList + ">>开始：" + DateUtil.getCurrentTime());
        try {
            for (String rq : rqList) {
                //处理日
                dealDay(taskMsg, rq, SIGN);

                //汇总周
                if (DateUtil.isLastDayForWeek(rq)) {
                    List<String> weekDays = DateUtil.getDaysBefore(rq, 6);
                    dealSum(taskMsg, SIGN, RqlxConst.WEEK, rq, weekDays);
                }

                //汇总月
                if (rqList.indexOf(rq) == rqList.size() - 1 || DateUtil.isLastDayForMonth(rq)) {
                    String monthRq = rq.substring(0, 7);
                    List<String> monthDays = DateUtil.getMonthDays(monthRq);
                    dealSum(taskMsg, SIGN, RqlxConst.MONTH, monthRq, monthDays);
                }

                if (DateUtil.rqIndex(rq) == 2) {
                    String monthRq = DateUtil.getLastMonthLastday(rq).substring(0, 7);
                    List<String> monthDays = DateUtil.getMonthDays(monthRq);
                    dealSum(taskMsg, SIGN, RqlxConst.MONTH, monthRq, monthDays);
                }

                //汇总7天 30天
                if (rqList.indexOf(rq) == rqList.size() - 1 && (rq.compareTo(DateUtil.getLastday()) >= 0)) {
                    delRecent(taskMsg.getUserid(), SIGN);

                    List<String> recent7Days = DateUtil.getDaysBefore(rq, 6);
                    dealSum(taskMsg, SIGN, RqlxConst.RECENT7, rq, recent7Days);

                    List<String> recent30Days = DateUtil.getDaysBefore(rq, 29);
                    dealSum(taskMsg, SIGN, RqlxConst.RECENT30, rq, recent30Days);
                }
            }
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        } catch (Error e) {
            LOG.error(taskMsg.getQd() + "->" + taskMsg.getDpmc() + "->" + SIGN + "->" + SIGN_DESC + "-->ERROR");
            LOG.error(taskMsg.getQd() + "->" + taskMsg.getDpmc() + "->" + SIGN + "->" + SIGN_DESC + e.getMessage(), e);
        }
        long end = System.currentTimeMillis();
        LOG.info("渠道：" + taskMsg.getQd() + "，店铺名：" + taskMsg.getDpmc() + ">>" + SIGN_DESC + ">>" + rqList + ">>结束：" + DateUtil.getCurrentTime() + ">>耗时：" + (end - start) / 1000 + "秒");
    }

    private void dealSum(DataprobiTaskMsg taskMsg, String sign, String rqlx, String currrq, List<String> rqList) throws SQLException {
        int state = dealSumByReport(taskMsg, rqlx, currrq, sign);
        if (state == 2 || state == -1) {
            return;
        }

        //1、解析单日数据 并汇总成DB
        BiSqliteDb biSqliteDb = null;
        try {
            biSqliteDb = new BiSqliteDb(FileUtil.createDBFilePath(), SqlProvider.readSql(SqlProvider.TGFX_INIT_SQL), null);
            biSqliteDb.startTransaction();
        } catch (SQLException e) {
            LOG.error(e.getMessage(), e);
            return;
        }

        final String sql = "INSERT INTO tgfx  SELECT * FROM db2.tgfx ;";
        PreparedStatement preparedStatement = null;
        Statement statement = null;
        for (String rq : rqList) {
            if (rq.compareTo(DateUtil.getLastday()) > 0) {
                continue;
            }
            String osskey = "bi_jysj/day/" + rq + "/" + taskMsg.getQd() + "/" + taskMsg.getUserid() + "/" + sign + ".db";
            if (!biDataOssUtil.exist(osskey)) {
                continue;
            }

            String temppath = FileUtil.createDBFilePath();
            biDataOssUtil.download(osskey, temppath);
            BiSqliteDb tempDb = null;
            try {
                tempDb = new BiSqliteDb(temppath);
                BiSqliteDb.fillNewFields(biSqliteDb, tempDb, "tgfx");
                if (statement == null) {
                    statement = biSqliteDb.createStatement();
                }
                preparedStatement = biSqliteDb.prepareStatement("ATTACH DATABASE ? AS db2;");
                preparedStatement.setString(1, temppath);
                preparedStatement.executeUpdate();
                statement.executeUpdate(sql);
                biSqliteDb.commit();
                // 分离数据库
                statement.executeUpdate("DETACH DATABASE db2;");
            } catch (SQLException e) {
                LOG.error(e.getMessage(), e);
                SqliteDbUtil.closeStatement(statement);
                return;
            } finally {
                SqliteDbUtil.closeStatement(preparedStatement);
                SqliteDbUtil.close(tempDb);
            }
        }
        SqliteDbUtil.closeStatement(statement);

        SqliteStatement<TgfxReport> sqliteStatement = null;
        final int pageSize = 100;
        int pageNo = 1;
        List<TgfxReport> tgfxReports = null;
        while (true) {
            String page_sql = SqlProvider.readSql(SqlProvider.TGFX_SUM_SQL).replace(";", "") + " LIMIT " + pageSize + " OFFSET " + (pageNo - 1) * pageSize;
            try {
                sqliteStatement = biSqliteDb.createSqliteStatement(page_sql, TgfxReport.class);
                tgfxReports = sqliteStatement.queryForList(null, TgfxReport.class);
                for (TgfxReport tgfxReport : tgfxReports) {
                    tgfxReport.setRqlx(rqlx);
                    tgfxReport.setRq(currrq);
                    tgfxReport.calZhlv();
                }
                biItemTgfxDataTsDaoV2.updateRows(tgfxReports);
                if (tgfxReports.size() < pageSize) {
                    break;
                }
                pageNo++;
            } catch (Exception e) {
                LOG.error(e.getMessage(), e);
                break;
            } finally {
                if (CollectionUtils.isNotEmpty(tgfxReports)) tgfxReports.clear();
                SqliteDbUtil.closeSqliteStatements(sqliteStatement);
            }
        }

        SqliteDbUtil.close(biSqliteDb);
    }

    /**
     * @return 0 直接汇总   1 没有汇总报表，通过单日汇总计算  2 已通过汇总报表计算过了  -1异常
     */
    protected int dealSumByReport(DataprobiTaskMsg taskMsg, String rqlx, String rq, String sign) {
        return 0;
    }

    protected void dealDay(DataprobiTaskMsg taskMsg, String rq, String sign) throws SQLException {
        //1、解析单日数据 并汇总成DB
        BiSqliteDb biSqliteDb = null;
        try {
            biSqliteDb = new BiSqliteDb(FileUtil.createDBFilePath(), SqlProvider.readSql(SqlProvider.TGFX_INIT_SQL), null);
        } catch (SQLException e) {
            LOG.error(e.getMessage(), e);
            return;
        }
        biSqliteDb.startTransaction();
        analysisData(taskMsg, biSqliteDb, rq, sign);
        biSqliteDb.commit();


        int pageNo = 1;
        int pageSize = 100;
        //2、DB分页查询 在反插表格存储
        final String sql_format = SqlProvider.readSql(SqlProvider.TGFX_SUM_SQL).replaceAll(";", "") + " LIMIT %s OFFSET %s ;";
        SqliteStatement<TgfxReport> sqliteStatement = null;
        List<TgfxReport> tgfxReports = null;
        while (true) {
            String sql = String.format(sql_format, pageSize, (pageNo - 1) * pageSize);
            try {
                sqliteStatement = biSqliteDb.createSqliteStatement(sql, TgfxReport.class);
                tgfxReports = sqliteStatement.queryForList(null, TgfxReport.class);
                for (TgfxReport tgfxReport : tgfxReports) {
                    tgfxReport.setRqlx(RqlxConst.DAY);
                    tgfxReport.setRq(rq);
                    tgfxReport.calZhlv();
                }
                biItemTgfxDataTsDaoV2.updateRows(tgfxReports);
                if (tgfxReports.size() < pageSize) {
                    break;
                }
                pageNo++;
            } catch (Exception e) {
                LOG.error(e.getMessage(), e);
                break;
            } finally {
                if (CollectionUtils.isNotEmpty(tgfxReports)) {
                    tgfxReports.clear();
                }
                SqliteDbUtil.closeSqliteStatements(sqliteStatement);
            }
        }

        //存入day
        String osskey = "bi_jysj/day/" + rq + "/" + taskMsg.getQd() + "/" + taskMsg.getUserid() + "/" + sign + ".db";
        biDataOssUtil.upload(biSqliteDb.getFileD(), osskey);

        SqliteDbUtil.close(biSqliteDb);
    }

    protected void analysisData(DataprobiTaskMsg taskMsg, BiSqliteDb biSqliteDb, String rq, String sign) {
        throw new RuntimeException("No Override Method analysisData");
    }

    protected void delRecent(String userid, String sign) {
        biItemTgfxDataTsDaoV2.delRecent(userid, sign);
    }

    protected final void save2db(BiSqliteDb biSqliteDb, List<TgfxReport> list) {
        if (biSqliteDb == null || CollectionUtils.isEmpty(list)) {
            return;
        }

        for (TgfxReport report : list) {
            report.calZhlv();
        }
        SqliteStatement<TgfxReport> sqliteStatement = null;
        try {
            String sql = SqlProvider.readSql(SqlProvider.TGFX_INSERT_SQL);
            sqliteStatement = biSqliteDb.createSqliteStatement(sql, TgfxReport.class);
            sqliteStatement.insert(list);
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        } finally {
            SqliteDbUtil.closeSqliteStatements(sqliteStatement);
            list.clear();
        }
    }
}
