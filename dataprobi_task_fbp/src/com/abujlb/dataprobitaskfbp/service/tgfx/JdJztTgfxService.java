package com.abujlb.dataprobitaskfbp.service.tgfx;


import com.abujlb.dataprobitaskfbp.bean.DataprobiTaskMsg;
import com.abujlb.dataprobitaskfbp.bean.tgfx.TgfxReport;
import com.abujlb.dataprobitaskfbp.constants.RqlxConst;
import com.abujlb.dataprobitaskfbp.sqlite.BiSqliteDb;
import com.abujlb.dataprobitaskfbp.utils.FastJSONObjAttrToNumber;
import com.abujlb.util.StringUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 京东商品推广分析
 *
 * <AUTHOR>
 * @date 2024-5-10
 */
@Component
public class JdJztTgfxService extends TgfxService {

    public static final Logger LOG = Logger.getLogger(JdJztTgfxService.class);

    public static final String JDKC_SIGN = "JDKC";
    public static final String JDKC_SIGN_DESC = "京东快车推广分析";

    public static final String JDZT_SIGN = "JDZT";
    public static final String JDZT_SIGN_DESC = "京东直投推广分析";

    public static final String JDZW_SIGN = "JDZW";
    public static final String JDZW_SIGN_DESC = "京东展位推广分析";

    public static final String JST_SIGN = "JST";
    public static final String JST_SIGN_DESC = "京速推推广分析";

    public static final String GWCD_SIGN = "GWCD";
    public static final String GWCD_SIGN_DESC = "购物触点推广分析";

    public static final String QZYX_SIGN = "JDQZYX";
    public static final String QZYX_SIGN_DESC = "全站营销推广分析";

    private static final String JDKC_NEW = "bi_jd/auth/authdata/jdkc/";
    private static final String JDZT_NEW = "bi_jd/auth/authdata/jdzt/";
    private static final String JDZW_NEW = "bi_jd/auth/authdata/jdzw/";
    private static final String JST_NEW = "bi_jd/auth/authdata/jdht/";
    private static final String GWCD_NEW = "bi_jd/auth/authdata/gwcd/";
    private static final String QZYX_NEW = "bi_jd/auth/authdata/qzyxtgfx/";
    private static final Map<String, String> map = new HashMap<>();

    static {
        map.put(JDKC_SIGN, JDKC_NEW);
        map.put(JDZT_SIGN, JDZT_NEW);
        map.put(JDZW_SIGN, JDZW_NEW);
        map.put(JST_SIGN, JST_NEW);
        map.put(GWCD_SIGN, GWCD_NEW);
        map.put(QZYX_SIGN, QZYX_NEW);
    }

    @Override
    protected void analysisData(DataprobiTaskMsg dataprobiTaskMsg, BiSqliteDb biSqliteDb, String rq, String sign) {
        String prefix = map.get(sign);
        List<String> ossKeyList = biDataOssUtil.listFiles(prefix + rq + "/" + dataprobiTaskMsg.getUserid() + "/");
        List<TgfxReport> list = new ArrayList<>();
        for (String ossKey : ossKeyList) {
            String json = biDataOssUtil.readJson(ossKey);
            if (!StringUtil.isJsonArray(json)) {
                continue;
            }
            JSONArray jsonArray = JSONArray.parseArray(json);
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                String skuId = FastJSONObjAttrToNumber.toString(jsonObject, "skuId");
                if (StringUtils.isBlank(skuId) || !skuId.matches(REG_EXP)) {
                    continue;
                }

                String campaignId = FastJSONObjAttrToNumber.toString(jsonObject, "campaignId");
                TgfxReport report = new TgfxReport();
                report.setCampaignId(campaignId);
                report.setCampaignName(FastJSONObjAttrToNumber.toString(jsonObject, "campaignName"));
                report.setItemid(skuId);
                report.setHash(report.getCampaignId());
                report.setSum(1);
                report.setUserid(dataprobiTaskMsg.getUserid());
                report.setLx(sign);
                report.setRqlx(RqlxConst.DAY);
                report.setRq(rq);

                report.setHf(FastJSONObjAttrToNumber.toDouble(jsonObject, "cost"));
                report.setZxl(FastJSONObjAttrToNumber.toInt(jsonObject, "impressions"));
                report.setDjl(FastJSONObjAttrToNumber.toInt(jsonObject, "clicks"));
                report.setCjje(FastJSONObjAttrToNumber.toDouble(jsonObject, "totalOrderSum"));
                report.setZjcjje(FastJSONObjAttrToNumber.toDouble(jsonObject, "directOrderSum"));
                report.setJjcjje(FastJSONObjAttrToNumber.toDouble(jsonObject, "indirectOrderSum"));
                report.setCjbs(FastJSONObjAttrToNumber.toInt(jsonObject, "totalOrderCnt"));
                report.setZjcjbs(FastJSONObjAttrToNumber.toInt(jsonObject, "directOrderCnt"));
                report.setJjcjbs(FastJSONObjAttrToNumber.toInt(jsonObject, "indirectOrderCnt"));
                report.setZgwcs(FastJSONObjAttrToNumber.toInt(jsonObject, "totalCartCnt"));
                report.setZjgwcs(FastJSONObjAttrToNumber.toInt(jsonObject, "directCartCnt"));
                report.setJjgwcs(FastJSONObjAttrToNumber.toInt(jsonObject, "indirectCartCnt"));
                report.setDpdyl(FastJSONObjAttrToNumber.toInt(jsonObject, "shopAttentionCnt"));

                list.add(report);

                if (list.size() == 100) {
                    save2db(biSqliteDb, list);
                    list.clear();
                }
            }
            if (CollectionUtils.isNotEmpty(list)) {
                save2db(biSqliteDb, list);
                list.clear();
            }
            jsonArray.clear();
        }
    }
}
