package com.abujlb.dataprobitaskfbp.bean;

import java.util.List;

public class DataprobiTaskMsg {

    //店铺userid
    private String userid;
    //用户id
    private int yhid;
    //渠道
    private String qd;

    private String dpmc ;

    private List<String> rqList ;

    private String taskType ;

    private String uuid;


    public String getUserid() {
        return userid;
    }

    public void setUserid(String userid) {
        this.userid = userid;
    }

    public int getYhid() {
        return yhid;
    }

    public void setYhid(int yhid) {
        this.yhid = yhid;
    }

    public String getQd() {
        return qd;
    }

    public void setQd(String qd) {
        this.qd = qd;
    }

    public List<String> getRqList() {
        return rqList;
    }

    public void setRqList(List<String> rqList) {
        this.rqList = rqList;
    }

    public String getTaskType() {
        return taskType;
    }

    public void setTaskType(String taskType) {
        this.taskType = taskType;
    }

    public String getDpmc() {
        return dpmc;
    }

    public void setDpmc(String dpmc) {
        this.dpmc = dpmc;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }
}
