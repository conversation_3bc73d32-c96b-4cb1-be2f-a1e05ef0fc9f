package com.abujlb.dataprobitaskfbp.bean;


import com.alibaba.fastjson.JSON;

import java.util.List;

public class BiSupplier {

    private String userid;
    private String qd;
    private String supplierUserid;
    private String supplierYhid;
    private int supplierZt;
    private int testSupplier;
    private String mateRule;
    private List<String> bbidList;

    public String getUserid() {
        return userid;
    }

    public void setUserid(String userid) {
        this.userid = userid;
    }

    public String getQd() {
        return qd;
    }

    public void setQd(String qd) {
        this.qd = qd;
    }

    public String getSupplierUserid() {
        return supplierUserid;
    }

    public void setSupplierUserid(String supplierUserid) {
        this.supplierUserid = supplierUserid;
    }

    public String getSupplierYhid() {
        return supplierYhid;
    }

    public void setSupplierYhid(String supplierYhid) {
        this.supplierYhid = supplierYhid;
    }

    public int getSupplierZt() {
        return supplierZt;
    }

    public void setSupplierZt(int supplierZt) {
        this.supplierZt = supplierZt;
    }

    public int getTestSupplier() {
        return testSupplier;
    }

    public void setTestSupplier(int testSupplier) {
        this.testSupplier = testSupplier;
    }

    public String getMateRule() {
        return mateRule;
    }

    public void setMateRule(String mateRule) {
        this.mateRule = mateRule;
    }

    public List<String> getBbidList() {
        return bbidList;
    }

    public void setBbidList(List<String> bbidList) {
        this.bbidList = bbidList;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
