package com.abujlb.dataprobitaskfbp.bean.tgfx;

import com.abujlb.dataprobitaskfbp.constants.RqlxConst;
import com.abujlb.dataprobitaskfbp.utils.MathUtil;
import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2024/5/11
 */
public class ZtcAdgroupReportV2 {

    //计划ID
    protected String campaignId;
    //计划名
    protected String campaignName;
    //宝贝ID
    protected String itemid;
    //日期
    protected String rq;
    //日期类型
    protected String rqlx;
    //店铺ID
    protected String userid;
    protected String userid2;
    //是否来自于报表  0汇总 1报表
    protected int sum;
    //展现量
    protected int zxl;
    //点击量
    protected int djl;
    //花费
    protected double hf;
    //点击率 = 点击量/展现量
    protected double djlv;
    //平均点击花费 = 花费/点击量
    protected double pjdjhf;
    //千次展现花费 = 花费/展现数 * 1000
    protected double qczxhf;
    //总收藏数
    protected int zscs;
    //宝贝收藏数
    protected int bbscs;
    //店铺收藏数
    protected int dpscs;
    //总购物车数
    protected int zgwcs;
    //直接购物车数
    protected int zjgwcs;
    //间接购物车数
    protected int jjgwcs;
    //加购率 =  总购物车数/点击量
    protected double jglv;
    //成交金额
    protected double cjje;
    //成交笔数
    protected int cjbs;
    //直接成交金额
    protected double zjcjje;
    //直接成交笔数
    protected int zjcjbs;
    //间接成交金额
    protected double jjcjje;
    //间接成交笔数
    protected int jjcjbs;
    //投入产出比 =   成交金额/花费
    protected double roi;
    //点击转换率 = 成交笔数/点击量
    protected double djzhlv;
    //收藏率=（总收藏数/点击量）
    protected double sclv;
    //自然流量转化金额
    protected double zrllzhje;
    //自然流量曝光率
    protected int zrllbgl;
    //成交人数
    protected int cjrs;
    //成交新客数
    protected int cjxks;
    //成交新客占比  = 成交新客数/成交人数
    protected double cjxkzb;
    //关键词
    private String keyword;
    //人群
    private String crowdName;

    //引导访问潜客数
    private int ydfwqks;
    //引导访问人数
    private int ydfwrs;
    //引导访问潜客占比
    private double ydfwqkszb;

    public String getCampaignId() {
        return campaignId;
    }

    public void setCampaignId(String campaignId) {
        this.campaignId = campaignId;
    }

    public String getCampaignName() {
        return campaignName;
    }

    public void setCampaignName(String campaignName) {
        this.campaignName = campaignName;
    }

    public String getItemid() {
        return itemid;
    }

    public void setItemid(String itemid) {
        this.itemid = itemid;
    }

    public int getZxl() {
        return zxl;
    }

    public void setZxl(int zxl) {
        this.zxl = zxl;
    }

    public int getDjl() {
        return djl;
    }

    public void setDjl(int djl) {
        this.djl = djl;
    }

    public double getHf() {
        return hf;
    }

    public void setHf(double hf) {
        this.hf = hf;
    }

    public double getDjlv() {
        return djlv;
    }

    public void setDjlv(double djlv) {
        this.djlv = djlv;
    }

    public double getPjdjhf() {
        return pjdjhf;
    }

    public void setPjdjhf(double pjdjhf) {
        this.pjdjhf = pjdjhf;
    }

    public double getQczxhf() {
        return qczxhf;
    }

    public void setQczxhf(double qczxhf) {
        this.qczxhf = qczxhf;
    }

    public int getZscs() {
        return zscs;
    }

    public void setZscs(int zscs) {
        this.zscs = zscs;
    }

    public int getBbscs() {
        return bbscs;
    }

    public void setBbscs(int bbscs) {
        this.bbscs = bbscs;
    }

    public int getDpscs() {
        return dpscs;
    }

    public void setDpscs(int dpscs) {
        this.dpscs = dpscs;
    }

    public int getZgwcs() {
        return zgwcs;
    }

    public void setZgwcs(int zgwcs) {
        this.zgwcs = zgwcs;
    }

    public int getZjgwcs() {
        return zjgwcs;
    }

    public void setZjgwcs(int zjgwcs) {
        this.zjgwcs = zjgwcs;
    }

    public int getJjgwcs() {
        return jjgwcs;
    }

    public void setJjgwcs(int jjgwcs) {
        this.jjgwcs = jjgwcs;
    }

    public double getJglv() {
        return jglv;
    }

    public void setJglv(double jglv) {
        this.jglv = jglv;
    }

    public double getCjje() {
        return cjje;
    }

    public void setCjje(double cjje) {
        this.cjje = cjje;
    }

    public int getCjbs() {
        return cjbs;
    }

    public void setCjbs(int cjbs) {
        this.cjbs = cjbs;
    }

    public double getZjcjje() {
        return zjcjje;
    }

    public void setZjcjje(double zjcjje) {
        this.zjcjje = zjcjje;
    }

    public int getZjcjbs() {
        return zjcjbs;
    }

    public void setZjcjbs(int zjcjbs) {
        this.zjcjbs = zjcjbs;
    }

    public double getJjcjje() {
        return jjcjje;
    }

    public void setJjcjje(double jjcjje) {
        this.jjcjje = jjcjje;
    }

    public int getJjcjbs() {
        return jjcjbs;
    }

    public void setJjcjbs(int jjcjbs) {
        this.jjcjbs = jjcjbs;
    }

    public double getRoi() {
        return roi;
    }

    public void setRoi(double roi) {
        this.roi = roi;
    }

    public double getDjzhlv() {
        return djzhlv;
    }

    public void setDjzhlv(double djzhlv) {
        this.djzhlv = djzhlv;
    }

    public String getRq() {
        return rq;
    }

    public void setRq(String rq) {
        this.rq = rq;
    }

    public double getSclv() {
        return sclv;
    }

    public void setSclv(double sclv) {
        this.sclv = sclv;
    }

    public int getZrllbgl() {
        return zrllbgl;
    }

    public void setZrllbgl(int zrllbgl) {
        this.zrllbgl = zrllbgl;
    }

    public double getZrllzhje() {
        return zrllzhje;
    }

    public void setZrllzhje(double zrllzhje) {
        this.zrllzhje = zrllzhje;
    }

    public int getCjrs() {
        return cjrs;
    }

    public void setCjrs(int cjrs) {
        this.cjrs = cjrs;
    }

    public int getCjxks() {
        return cjxks;
    }

    public void setCjxks(int cjxks) {
        this.cjxks = cjxks;
    }

    public double getCjxkzb() {
        return cjxkzb;
    }

    public void setCjxkzb(double cjxkzb) {
        this.cjxkzb = cjxkzb;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public String getCrowdName() {
        return crowdName;
    }

    public void setCrowdName(String crowdName) {
        this.crowdName = crowdName;
    }

    public String getRqlx() {
        return rqlx;
    }

    public void setRqlx(String rqlx) {
        this.rqlx = rqlx;
    }

    public String getUserid() {
        return userid;
    }

    public void setUserid(String userid) {
        this.userid = userid;
    }

    public int getSum() {
        return sum;
    }

    public void setSum(int sum) {
        this.sum = sum;
    }

    public int getYdfwqks() {
        return ydfwqks;
    }

    public void setYdfwqks(int ydfwqks) {
        this.ydfwqks = ydfwqks;
    }

    public int getYdfwrs() {
        return ydfwrs;
    }

    public void setYdfwrs(int ydfwrs) {
        this.ydfwrs = ydfwrs;
    }

    public double getYdfwqkszb() {
        return ydfwqkszb;
    }

    public void setYdfwqkszb(double ydfwqkszb) {
        this.ydfwqkszb = ydfwqkszb;
    }

    public void plus(ZtcAdgroupReportV2 report) {
        this.zxl += report.getZxl();
        this.djl += report.getDjl();
        this.hf = MathUtil.add(this.hf, report.getHf());
        this.djlv = MathUtil.divide(this.djl, this.zxl);
        this.pjdjhf = MathUtil.divide(this.hf, this.djl);
        this.qczxhf = MathUtil.divide(this.hf, this.zxl) * 1000;
        this.zscs += report.getZscs();
        this.bbscs += report.getBbscs();
        this.dpscs += report.getDpscs();
        this.zgwcs += report.getZgwcs();
        this.zjgwcs += report.getZjgwcs();
        this.jjgwcs += report.getJjgwcs();
        this.jglv = MathUtil.divide(this.zgwcs, this.djl);
        this.cjje = MathUtil.add(this.cjje, report.getCjje());
        this.cjbs += report.getCjbs();
        this.zjcjje = MathUtil.add(this.zjcjje, report.getZjcjje());
        this.zjcjbs += report.getZjcjbs();
        this.jjcjje = MathUtil.add(this.jjcjje, report.getJjcjje());
        this.jjcjbs += report.getJjcjbs();
        this.roi = MathUtil.divide(this.cjje, this.hf);
        this.djzhlv = MathUtil.divide(this.cjbs, this.djl);
        this.sclv = MathUtil.divide((this.bbscs + this.dpscs), this.djl, 8);
        this.zrllbgl += report.getZrllbgl();
        this.zrllzhje = MathUtil.add(this.zrllzhje, report.getZrllzhje());
        this.cjrs += report.getCjrs();
        this.cjxks += report.getCjxks();
        this.cjxkzb = MathUtil.divide(this.cjxks, this.cjrs);
        this.ydfwqks += report.getYdfwqks();
        this.ydfwrs += report.getYdfwrs();
        this.ydfwqkszb = MathUtil.divide(this.ydfwqks, this.ydfwrs);
    }

    public void calZhlv() {
        this.djlv = MathUtil.divide(this.djl, this.zxl);
        this.pjdjhf = MathUtil.divide(this.hf, this.djl);
        this.qczxhf = MathUtil.divide(this.hf, this.zxl) * 1000;
        this.jglv = MathUtil.divide(this.zgwcs, this.djl);
        this.roi = MathUtil.divide(this.cjje, this.hf);
        this.djzhlv = MathUtil.divide(this.cjbs, this.djl);
        this.sclv = MathUtil.divide((this.bbscs + this.dpscs), this.djl, 8);
        this.cjxkzb = MathUtil.divide(this.cjxks, this.cjrs);
        this.ydfwqkszb = MathUtil.divide(this.ydfwqks, this.ydfwrs);
    }

    public String getUserid2() {
        if (StringUtils.isBlank(userid2)) {
            return userid;
        } else {
            return userid2;
        }
    }

    public void setUserid2(String userid2) {
        this.userid2 = userid2;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }

    public String getRqlxRq() {
        if (rqlx.equals(RqlxConst.RECENT7) || rqlx.equals(RqlxConst.RECENT30)) {
            return rqlx;
        } else {
            return rqlx + "_" + rq;
        }
    }
}
