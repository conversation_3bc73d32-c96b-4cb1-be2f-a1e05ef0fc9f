package com.abujlb.dataprobitaskfbp.bean.tgfx;

import com.abujlb.dataprobitaskfbp.annotations.Qd;
import com.abujlb.dataprobitaskfbp.annotations.QdColumn;
import com.abujlb.dataprobitaskfbp.constants.RqlxConst;
import com.abujlb.dataprobitaskfbp.utils.MathUtil;
import com.alibaba.fastjson.JSON;

public class TgfxReport {

    @QdColumn(allQd = true)
    private String userid;
    @QdColumn(allQd = true)
    private String lx;
    @QdColumn(allQd = true)
    private String rqlx;
    @QdColumn(allQd = true)
    private String rq;
    @QdColumn(allQd = true)
    private String itemid;
    @QdColumn(allQd = true)
    private String hash;
    //0 单天求和的  1  来自报表
    @QdColumn(allQd = true)
    private int sum;
    //计划名称
    @QdColumn(allQd = true)
    private String campaignName;
    //计划ID
    @QdColumn(allQd = true)
    private String campaignId;
    //花费
    @QdColumn(allQd = true)
    private double hf;
    //展现量
    @QdColumn(allQd = true)
    private long zxl;
    //点击量
    @QdColumn(allQd = true)
    private int djl;
    //点击率 = 点击量/展现量
    @QdColumn(allQd = true)
    private double djlv;
    //点击转换率 = 成交笔数/点击量
    @QdColumn(allQd = true)
    private double djzhlv;
    //平均点击花费 = 花费/点击量
    @QdColumn(allQd = true)
    private double pjdjhf;
    //千次展现花费 = 花费/展现数 * 1000
    @QdColumn(allQd = true)
    private double qczxhf;
    //投入产出比 =   成交金额/花费
    @QdColumn(allQd = true)
    private double roi;
    //成交金额
    @QdColumn(allQd = true)
    private double cjje;
    //直接成交金额
    @QdColumn(allQd = true)
    private double zjcjje;
    //间接成交金额
    @QdColumn(allQd = true)
    private double jjcjje;
    //成交笔数
    @QdColumn(allQd = true)
    private int cjbs;
    //直接成交笔数
    @QdColumn(allQd = true)
    private int zjcjbs;
    //间接成交笔数
    @QdColumn(allQd = true)
    private int jjcjbs;
    //总购物车数
    @QdColumn(allQd = true)
    private int zgwcs;
    //直接购物车数
    @QdColumn(allQd = true)
    private int zjgwcs;
    //间接购物车数
    @QdColumn(allQd = true)
    private int jjgwcs;
    //加购率 =  总购物车数/点击量
    @QdColumn(allQd = true)
    private double jglv;
    //总收藏数
    @QdColumn(allQd = true)
    private int zscs;
    //收藏率=（总收藏数/点击量）
    @QdColumn(allQd = true)
    private double sclv;
    //店铺订阅量
    @QdColumn(allQd = true)
    private int dpdyl;
    //宝贝收藏数
    @QdColumn(allQd = true)
    private int bbscs;
    //店铺收藏数
    @QdColumn(allQd = true)
    private int dpscs;
    //点击成本 =  花费/点击量
    @QdColumn(allQd = true)
    private double djcb;

    //-------------TM TB渠道--------------------//
    @QdColumn(qd = {Qd.TB, Qd.TM})
    //计划类型
    private String campaignType;
    //计划类型名称
    @QdColumn(qd = {Qd.TB, Qd.TM})
    private String campaignTypeDesc;
    //引导访问潜客数
    @QdColumn(qd = {Qd.TB, Qd.TM})
    private int ydfwqks;
    //引导访问人数
    @QdColumn(qd = {Qd.TB, Qd.TM})
    private int ydfwrs;
    //引导访问潜客占比
    @QdColumn(qd = {Qd.TB, Qd.TM})
    private double ydfwqkszb;


    //-------------ALBB渠道---------------------------//
    //有效曝光
    @QdColumn(qd = {Qd.ALBB})
    private long yxbgl;
    //线索量
    @QdColumn(qd = {Qd.ALBB})
    private int xsl;
    //询盘量
    @QdColumn(qd = {Qd.ALBB})
    private int xpl;
    //线索转换率  =线索量/点击次数
    @QdColumn(qd = {Qd.ALBB})
    private double xszhlv;
    //线索成本 =花费/线索量
    @QdColumn(qd = {Qd.ALBB})
    private double xscb;
    //加进货单数
    @QdColumn(qd = {Qd.ALBB})
    private int jjhds;
    //点击立即订购数
    @QdColumn(qd = {Qd.ALBB})
    private int ljdgs;
    //领券数
    @QdColumn(qd = {Qd.ALBB})
    private int lqs;
    //提交订单成功数
    @QdColumn(qd = {Qd.ALBB})
    private int tjddcgs;


    //--------------JD渠道---------------//


    //---------------PDD渠道---------------//
    //关注数
    @QdColumn(qd = {Qd.PDD})
    private int gzs;
    //询单量
    @QdColumn(qd = {Qd.PDD})
    private int xdl;


    //--------------DY渠道-------------//
    //转化数
    @QdColumn(qd = {Qd.DY})
    private int zhs;
    //转化率 = 转化数/点击量
    @QdColumn(qd = {Qd.DY})
    private double zhlv;
    //转化成本 = 花费/转化数
    @QdColumn(qd = {Qd.DY})
    private double zhcb;

    //--------------XHS渠道-------------//
    //平均点击成本
    @QdColumn(qd = {Qd.XHS})
    private double pjdjcb;
    //平均千次展示费用
    @QdColumn(qd = {Qd.XHS})
    private double pjqczsfy;
    //点赞数
    @QdColumn(qd = {Qd.XHS})
    private int dzs;
    //收藏数
    @QdColumn(qd = {Qd.XHS})
    private int scs;
    //评论数
    @QdColumn(qd = {Qd.XHS})
    private int pls;
    //关注粉丝数
    @QdColumn(qd = {Qd.XHS})
    private int gzfss;
    //分享数
    @QdColumn(qd = {Qd.XHS})
    private int fxs;
    //互动数
    @QdColumn(qd = {Qd.XHS})
    private int hds;


    public String getUserid() {
        return userid;
    }

    public void setUserid(String userid) {
        this.userid = userid;
    }

    public String getLx() {
        return lx;
    }

    public void setLx(String lx) {
        this.lx = lx;
    }

    public String getRqlx() {
        return rqlx;
    }

    public void setRqlx(String rqlx) {
        this.rqlx = rqlx;
    }

    public String getRq() {
        return rq;
    }

    public void setRq(String rq) {
        this.rq = rq;
    }

    public String getItemid() {
        return itemid;
    }

    public void setItemid(String itemid) {
        this.itemid = itemid;
    }

    public String getHash() {
        return hash;
    }

    public void setHash(String hash) {
        this.hash = hash;
    }

    public int getSum() {
        return sum;
    }

    public void setSum(int sum) {
        this.sum = sum;
    }

    public String getCampaignName() {
        return campaignName;
    }

    public void setCampaignName(String campaignName) {
        this.campaignName = campaignName;
    }

    public double getHf() {
        return hf;
    }

    public void setHf(double hf) {
        this.hf = hf;
    }

    public long getZxl() {
        return zxl;
    }

    public void setZxl(long zxl) {
        this.zxl = zxl;
    }

    public int getDjl() {
        return djl;
    }

    public void setDjl(int djl) {
        this.djl = djl;
    }

    public double getDjlv() {
        return djlv;
    }

    public void setDjlv(double djlv) {
        this.djlv = djlv;
    }

    public double getDjzhlv() {
        return djzhlv;
    }

    public void setDjzhlv(double djzhlv) {
        this.djzhlv = djzhlv;
    }

    public double getPjdjhf() {
        return pjdjhf;
    }

    public void setPjdjhf(double pjdjhf) {
        this.pjdjhf = pjdjhf;
    }

    public double getQczxhf() {
        return qczxhf;
    }

    public void setQczxhf(double qczxhf) {
        this.qczxhf = qczxhf;
    }

    public double getRoi() {
        return roi;
    }

    public void setRoi(double roi) {
        this.roi = roi;
    }

    public double getCjje() {
        return cjje;
    }

    public void setCjje(double cjje) {
        this.cjje = cjje;
    }

    public double getZjcjje() {
        return zjcjje;
    }

    public void setZjcjje(double zjcjje) {
        this.zjcjje = zjcjje;
    }

    public double getJjcjje() {
        return jjcjje;
    }

    public void setJjcjje(double jjcjje) {
        this.jjcjje = jjcjje;
    }

    public int getCjbs() {
        return cjbs;
    }

    public void setCjbs(int cjbs) {
        this.cjbs = cjbs;
    }

    public int getZjcjbs() {
        return zjcjbs;
    }

    public void setZjcjbs(int zjcjbs) {
        this.zjcjbs = zjcjbs;
    }

    public int getJjcjbs() {
        return jjcjbs;
    }

    public void setJjcjbs(int jjcjbs) {
        this.jjcjbs = jjcjbs;
    }

    public int getZgwcs() {
        return zgwcs;
    }

    public void setZgwcs(int zgwcs) {
        this.zgwcs = zgwcs;
    }

    public int getZjgwcs() {
        return zjgwcs;
    }

    public void setZjgwcs(int zjgwcs) {
        this.zjgwcs = zjgwcs;
    }

    public int getJjgwcs() {
        return jjgwcs;
    }

    public void setJjgwcs(int jjgwcs) {
        this.jjgwcs = jjgwcs;
    }

    public double getJglv() {
        return jglv;
    }

    public void setJglv(double jglv) {
        this.jglv = jglv;
    }

    public int getZscs() {
        return zscs;
    }

    public void setZscs(int zscs) {
        this.zscs = zscs;
    }

    public double getSclv() {
        return sclv;
    }

    public void setSclv(double sclv) {
        this.sclv = sclv;
    }

    public int getDpdyl() {
        return dpdyl;
    }

    public void setDpdyl(int dpdyl) {
        this.dpdyl = dpdyl;
    }

    public int getBbscs() {
        return bbscs;
    }

    public void setBbscs(int bbscs) {
        this.bbscs = bbscs;
    }

    public int getDpscs() {
        return dpscs;
    }

    public void setDpscs(int dpscs) {
        this.dpscs = dpscs;
    }

    public double getDjcb() {
        return djcb;
    }

    public void setDjcb(double djcb) {
        this.djcb = djcb;
    }

    public String getCampaignType() {
        return campaignType;
    }

    public void setCampaignType(String campaignType) {
        this.campaignType = campaignType;
    }

    public String getCampaignTypeDesc() {
        return campaignTypeDesc;
    }

    public void setCampaignTypeDesc(String campaignTypeDesc) {
        this.campaignTypeDesc = campaignTypeDesc;
    }

    public long getYxbgl() {
        return yxbgl;
    }

    public void setYxbgl(long yxbgl) {
        this.yxbgl = yxbgl;
    }

    public int getXsl() {
        return xsl;
    }

    public void setXsl(int xsl) {
        this.xsl = xsl;
    }

    public int getXpl() {
        return xpl;
    }

    public void setXpl(int xpl) {
        this.xpl = xpl;
    }

    public double getXszhlv() {
        return xszhlv;
    }

    public void setXszhlv(double xszhlv) {
        this.xszhlv = xszhlv;
    }

    public double getXscb() {
        return xscb;
    }

    public void setXscb(double xscb) {
        this.xscb = xscb;
    }

    public int getJjhds() {
        return jjhds;
    }

    public void setJjhds(int jjhds) {
        this.jjhds = jjhds;
    }

    public int getLjdgs() {
        return ljdgs;
    }

    public void setLjdgs(int ljdgs) {
        this.ljdgs = ljdgs;
    }

    public int getLqs() {
        return lqs;
    }

    public void setLqs(int lqs) {
        this.lqs = lqs;
    }

    public int getTjddcgs() {
        return tjddcgs;
    }

    public void setTjddcgs(int tjddcgs) {
        this.tjddcgs = tjddcgs;
    }

    public int getGzs() {
        return gzs;
    }

    public void setGzs(int gzs) {
        this.gzs = gzs;
    }

    public int getXdl() {
        return xdl;
    }

    public void setXdl(int xdl) {
        this.xdl = xdl;
    }

    public int getZhs() {
        return zhs;
    }

    public void setZhs(int zhs) {
        this.zhs = zhs;
    }

    public double getZhlv() {
        return zhlv;
    }

    public void setZhlv(double zhlv) {
        this.zhlv = zhlv;
    }

    public double getZhcb() {
        return zhcb;
    }

    public void setZhcb(double zhcb) {
        this.zhcb = zhcb;
    }

    public String getCampaignId() {
        return campaignId;
    }

    public void setCampaignId(String campaignId) {
        this.campaignId = campaignId;
    }

    public double getPjdjcb() {
        return pjdjcb;
    }

    public void setPjdjcb(double pjdjcb) {
        this.pjdjcb = pjdjcb;
    }

    public double getPjqczsfy() {
        return pjqczsfy;
    }

    public void setPjqczsfy(double pjqczsfy) {
        this.pjqczsfy = pjqczsfy;
    }

    public int getDzs() {
        return dzs;
    }

    public void setDzs(int dzs) {
        this.dzs = dzs;
    }

    public int getScs() {
        return scs;
    }

    public void setScs(int scs) {
        this.scs = scs;
    }

    public int getPls() {
        return pls;
    }

    public void setPls(int pls) {
        this.pls = pls;
    }

    public int getGzfss() {
        return gzfss;
    }

    public void setGzfss(int gzfss) {
        this.gzfss = gzfss;
    }

    public int getFxs() {
        return fxs;
    }

    public void setFxs(int fxs) {
        this.fxs = fxs;
    }

    public int getHds() {
        return hds;
    }

    public void setHds(int hds) {
        this.hds = hds;
    }

    public int getYdfwqks() {
        return ydfwqks;
    }

    public void setYdfwqks(int ydfwqks) {
        this.ydfwqks = ydfwqks;
    }

    public double getYdfwqkszb() {
        return ydfwqkszb;
    }

    public void setYdfwqkszb(double ydfwqkszb) {
        this.ydfwqkszb = ydfwqkszb;
    }

    public int getYdfwrs() {
        return ydfwrs;
    }

    public void setYdfwrs(int ydfwrs) {
        this.ydfwrs = ydfwrs;
    }

    public String getRqlxRq() {
        if (rqlx.equals(RqlxConst.RECENT7) || rqlx.equals(RqlxConst.RECENT30)) {
            return rqlx;
        } else {
            return rqlx + "_" + rq;
        }
    }

    public void calZhlv() {
        this.setDjlv(MathUtil.divide(this.getDjl(), this.getZxl()));
        this.setDjzhlv(MathUtil.calZhlv(this.getCjbs(), this.getDjl()));
        this.setPjdjhf(MathUtil.divide(this.getHf(), this.getDjl()));
        this.setQczxhf(MathUtil.divide(this.getHf(), this.getZxl(), 8) * 1000);
        this.setRoi(MathUtil.divide(this.getCjje(), this.getHf()));
        this.setJglv(MathUtil.divide(this.getZgwcs(), this.getDjl()));
        this.setSclv(MathUtil.divide(this.getZscs(), this.getDjl()));
        this.setDjcb(MathUtil.divide(this.getHf(), this.getDjl()));
        this.setXszhlv(MathUtil.divide(this.getXsl(), this.getDjl()));
        this.setXscb(MathUtil.divide(this.getHf(), this.getXsl()));
        this.setZhlv(MathUtil.divide(this.getZhs(), this.getDjl()));
        this.setZhcb(MathUtil.divide(this.getHf(), this.getZhs()));
        this.setYdfwqkszb(MathUtil.divide(this.getYdfwqks(), this.getYdfwrs()));
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
