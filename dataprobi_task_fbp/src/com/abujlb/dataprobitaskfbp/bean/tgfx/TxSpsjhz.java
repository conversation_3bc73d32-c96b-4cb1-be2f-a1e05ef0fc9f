package com.abujlb.dataprobitaskfbp.bean.tgfx;


import com.abujlb.dataprobitaskfbp.utils.MathUtil;
import com.alibaba.fastjson.JSON;

public class TxSpsjhz {

    private String bbid;
    private int yhid;
    private String qd;
    private String rq;
    private String userid;
    private String tid;
    private String cid;
    //商品效果访客数
    private int spxgfks;
    //浏览量
    private int spxglll;
    //商品效果平均停留时长
    private double spxgpjtlsc;
    //详情页跳出率
    private double spxgxqytclv;
    //加购人数
    private int spxgjgrs;
    //收藏人数
    private int spxgscrs;
    //支付买家数
    private int spxgzfmjs;
    private int spxgssydfks;
    private int spxgssydzfmjs;
    //支付件数
    private int spxgzfjs;
    //支付金额
    private double spxgzfje;
    //退款金额
    private double spxgtkje;
    //加购件数
    private int spxgjgjs;
    //直通车花费
    private double ztchf;
    //直通车成交金额
    private double ztccjje;

    //引力魔方消耗
    private double ylmfhf;
    //引力魔方成交订单金额
    private double ylmfcjddje;
    //引力魔方成交金额 = 引力魔方成交订单金额
    private double ylmfcjje;
    //万相台花费
    private double aizthf;
    //万相台成交金额
    private double aiztcjje;

    public TxSpsjhz() {

    }

    public void plus(TxSpsjhz data) {
        this.spxgfks = this.spxgfks + data.getSpxgfks();
        this.spxglll = this.spxglll + data.getSpxglll();
        this.spxgjgrs = this.spxgjgrs + data.getSpxgjgrs();
        this.spxgscrs = this.spxgscrs + data.getSpxgscrs();
        this.spxgssydfks = this.spxgssydfks + data.getSpxgssydfks();
        this.spxgzfmjs = this.spxgzfmjs + data.getSpxgzfmjs();
        this.spxgzfjs = this.spxgzfjs + data.getSpxgzfjs();
        this.spxgzfje = MathUtil.add(this.spxgzfje, data.getSpxgzfje());
        this.spxgssydzfmjs = this.spxgssydzfmjs + data.getSpxgssydzfmjs();
        this.spxgtkje = MathUtil.add(this.spxgtkje, data.getSpxgtkje());
        this.spxgjgjs = this.spxgjgjs + data.getSpxgjgjs();
        this.ztchf = this.ztchf + data.getZtchf();
        this.ztccjje = MathUtil.add(this.ztccjje, data.getZtccjje());
        this.ylmfhf = MathUtil.add(this.ylmfhf, data.getYlmfhf());
        this.ylmfcjddje = MathUtil.add(this.ylmfcjddje, data.getYlmfcjddje());
        this.aizthf = MathUtil.add(this.aizthf, data.getAizthf());
        this.aiztcjje = MathUtil.add(this.aiztcjje, data.getAiztcjje());
    }

    public void finalCalculate() {
        this.spxgpjtlsc = MathUtil.divide(this.spxgpjtlsc, this.spxglll, 8); // 平均停留时长 = 停留总时长 / 商品总浏览量
        this.spxgxqytclv = MathUtil.divide(this.spxgxqytclv, this.spxgfks, 8); // 详情页跳出率 = 详情页跳出总人数 / 商品总访客数
    }

    public String getBbid() {
        return bbid;
    }

    public void setBbid(String bbid) {
        this.bbid = bbid;
    }

    public int getYhid() {
        return yhid;
    }

    public void setYhid(int yhid) {
        this.yhid = yhid;
    }

    public String getRq() {
        return rq;
    }

    public void setRq(String rq) {
        this.rq = rq;
    }

    public String getUserid() {
        return userid;
    }

    public void setUserid(String userid) {
        this.userid = userid;
    }

    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    public String getCid() {
        return cid;
    }

    public void setCid(String cid) {
        this.cid = cid;
    }

    public int getSpxgfks() {
        return spxgfks;
    }

    public void setSpxgfks(int spxgfks) {
        this.spxgfks = spxgfks;
    }

    public int getSpxglll() {
        return spxglll;
    }

    public void setSpxglll(int spxglll) {
        this.spxglll = spxglll;
    }

    public double getSpxgpjtlsc() {
        return spxgpjtlsc;
    }

    public void setSpxgpjtlsc(double spxgpjtlsc) {
        this.spxgpjtlsc = spxgpjtlsc;
    }

    public double getSpxgxqytclv() {
        return spxgxqytclv;
    }

    public void setSpxgxqytclv(double spxgxqytclv) {
        this.spxgxqytclv = spxgxqytclv;
    }

    public int getSpxgjgrs() {
        return spxgjgrs;
    }

    public void setSpxgjgrs(int spxgjgrs) {
        this.spxgjgrs = spxgjgrs;
    }

    public int getSpxgscrs() {
        return spxgscrs;
    }

    public void setSpxgscrs(int spxgscrs) {
        this.spxgscrs = spxgscrs;
    }

    public int getSpxgzfmjs() {
        return spxgzfmjs;
    }

    public void setSpxgzfmjs(int spxgzfmjs) {
        this.spxgzfmjs = spxgzfmjs;
    }

    public int getSpxgssydfks() {
        return spxgssydfks;
    }

    public void setSpxgssydfks(int spxgssydfks) {
        this.spxgssydfks = spxgssydfks;
    }

    public int getSpxgssydzfmjs() {
        return spxgssydzfmjs;
    }

    public void setSpxgssydzfmjs(int spxgssydzfmjs) {
        this.spxgssydzfmjs = spxgssydzfmjs;
    }

    public int getSpxgzfjs() {
        return spxgzfjs;
    }

    public void setSpxgzfjs(int spxgzfjs) {
        this.spxgzfjs = spxgzfjs;
    }

    public double getSpxgzfje() {
        return spxgzfje;
    }

    public void setSpxgzfje(double spxgzfje) {
        this.spxgzfje = spxgzfje;
    }

    public double getSpxgtkje() {
        return spxgtkje;
    }

    public void setSpxgtkje(double spxgtkje) {
        this.spxgtkje = spxgtkje;
    }

    public int getSpxgjgjs() {
        return spxgjgjs;
    }

    public void setSpxgjgjs(int spxgjgjs) {
        this.spxgjgjs = spxgjgjs;
    }

    public double getZtchf() {
        return ztchf;
    }

    public void setZtchf(double ztchf) {
        this.ztchf = ztchf;
    }

    public double getZtccjje() {
        return ztccjje;
    }

    public void setZtccjje(double ztccjje) {
        this.ztccjje = ztccjje;
    }

    public double getYlmfhf() {
        return ylmfhf;
    }

    public void setYlmfhf(double ylmfhf) {
        this.ylmfhf = ylmfhf;
    }

    public double getYlmfcjddje() {
        return ylmfcjddje;
    }

    public void setYlmfcjddje(double ylmfcjddje) {
        this.ylmfcjddje = ylmfcjddje;
    }

    public double getYlmfcjje() {
        return ylmfcjje;
    }

    public void setYlmfcjje(double ylmfcjje) {
        this.ylmfcjje = ylmfcjje;
    }

    public double getAizthf() {
        return aizthf;
    }

    public void setAizthf(double aizthf) {
        this.aizthf = aizthf;
    }

    public double getAiztcjje() {
        return aiztcjje;
    }

    public void setAiztcjje(double aiztcjje) {
        this.aiztcjje = aiztcjje;
    }

    public String getQd() {
        return qd;
    }

    public void setQd(String qd) {
        this.qd = qd;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
