package com.abujlb.dataprobitaskfbp.bean;

import com.alibaba.fastjson.JSON;

public class TxFksBean {
    //宝贝ID
    private transient String bbid;
    //日期
    private transient String rq;
    //日期类型
    private transient String rqlx;
    //用户ID
    private transient int yhid;
    //店铺ID
    private transient String userid;
    //渠道
    private transient String qd;

    //手淘搜索访客数
    private int stssfks;
    //淘宝客访客数
    private int tbkfks;
    //引力魔方访客数
    private int ylmffks;
    //直通车访客数
    private int ztcfks;
    //手淘推荐访客数
    private int sttjfks;
    //万相台访客数
    private int aiztfks;

    public TxFksBean() {
    }

    public String getBbid() {
        return bbid;
    }

    public void setBbid(String bbid) {
        this.bbid = bbid;
    }

    public String getRq() {
        return rq;
    }

    public void setRq(String rq) {
        this.rq = rq;
    }

    public String getRqlx() {
        return rqlx;
    }

    public void setRqlx(String rqlx) {
        this.rqlx = rqlx;
    }

    public int getYhid() {
        return yhid;
    }

    public void setYhid(int yhid) {
        this.yhid = yhid;
    }

    public String getUserid() {
        return userid;
    }

    public void setUserid(String userid) {
        this.userid = userid;
    }

    public String getQd() {
        return qd;
    }

    public void setQd(String qd) {
        this.qd = qd;
    }

    public int getStssfks() {
        return stssfks;
    }

    public void setStssfks(int stssfks) {
        this.stssfks = stssfks;
    }

    public int getTbkfks() {
        return tbkfks;
    }

    public void setTbkfks(int tbkfks) {
        this.tbkfks = tbkfks;
    }

    public int getYlmffks() {
        return ylmffks;
    }

    public void setYlmffks(int ylmffks) {
        this.ylmffks = ylmffks;
    }

    public int getZtcfks() {
        return ztcfks;
    }

    public void setZtcfks(int ztcfks) {
        this.ztcfks = ztcfks;
    }

    public int getSttjfks() {
        return sttjfks;
    }

    public void setSttjfks(int sttjfks) {
        this.sttjfks = sttjfks;
    }

    public int getAiztfks() {
        return aiztfks;
    }

    public void setAiztfks(int aiztfks) {
        this.aiztfks = aiztfks;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
