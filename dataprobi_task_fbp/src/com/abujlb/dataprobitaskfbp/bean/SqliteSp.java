package com.abujlb.dataprobitaskfbp.bean;

import org.apache.log4j.Logger;

/**
 * <AUTHOR>
 * @date 2022/5/12 10:03
 */
public class SqliteSp {

    public static final Logger logger = Logger.getLogger(SqliteSp.class);

    //宝贝id
    protected String bbid;
    //用户id
    protected int yhid;
    //渠道
    protected String qd;
    //店铺userid
    protected String userid;


    //------------不同渠道共有的数据--------------//
    //商品访客数
    protected int spxgfks;
    //商品浏览量
    protected int spxglll;
    //支付金额
    protected double spxgzfje;
    //支付买家数
    protected int spxgzfmjs;
    //支付转化率
    protected double spxgzfzhl;
    //退款金额
    protected double spxgtkje;
    //支付件数
    protected int spxgzfjs;

    //------------不同渠道的差异性数据字段存入JSON中--------------//
    private String other;
    //-------------------汇总部分--------------------------------//
    //推广花费  具体计算规则，参考每个渠道的汇总process里的calTghf(SqliteSp sqliteSp)方法
    private double tghf;
    //推广花费详情  参考tghf字段
    private String tghfxq;

    public SqliteSp() {

    }


    public String getBbid() {
        return bbid;
    }

    public void setBbid(String bbid) {
        this.bbid = bbid;
    }

    public int getYhid() {
        return yhid;
    }

    public void setYhid(int yhid) {
        this.yhid = yhid;
    }

    public String getUserid() {
        return userid;
    }

    public void setUserid(String userid) {
        this.userid = userid;
    }


    public String getQd() {
        return qd;
    }

    public void setQd(String qd) {
        this.qd = qd;
    }

    public int getSpxgfks() {
        return spxgfks;
    }

    public void setSpxgfks(int spxgfks) {
        this.spxgfks = spxgfks;
    }

    public int getSpxglll() {
        return spxglll;
    }

    public void setSpxglll(int spxglll) {
        this.spxglll = spxglll;
    }

    public double getSpxgzfje() {
        return spxgzfje;
    }

    public void setSpxgzfje(double spxgzfje) {
        this.spxgzfje = spxgzfje;
    }

    public int getSpxgzfmjs() {
        return spxgzfmjs;
    }

    public void setSpxgzfmjs(int spxgzfmjs) {
        this.spxgzfmjs = spxgzfmjs;
    }

    public double getSpxgzfzhl() {
        return spxgzfzhl;
    }

    public void setSpxgzfzhl(double spxgzfzhl) {
        this.spxgzfzhl = spxgzfzhl;
    }

    public double getSpxgtkje() {
        return spxgtkje;
    }

    public void setSpxgtkje(double spxgtkje) {
        this.spxgtkje = spxgtkje;
    }

    public int getSpxgzfjs() {
        return spxgzfjs;
    }

    public void setSpxgzfjs(int spxgzfjs) {
        this.spxgzfjs = spxgzfjs;
    }

    public String getOther() {
        return other == null ? "{}" : other;
    }

    public void setOther(String other) {
        this.other = other;
    }

    public double getTghf() {
        return tghf;
    }

    public void setTghf(double tghf) {
        this.tghf = tghf;
    }

    public String getTghfxq() {
        return tghfxq == null ? "{}" : tghfxq;
    }

    public void setTghfxq(String tghfxq) {
        this.tghfxq = tghfxq;
    }

    public String getOtherOrDefault() {
        return other == null ? "{}" : other;
    }
}
