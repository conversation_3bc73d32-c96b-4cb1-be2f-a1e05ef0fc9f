package com.abujlb.dataprobitaskfbp.bean;

import com.abujlb.filereader.ColumnConfig;

/**
 * <AUTHOR>
 * @date 2022/12/5 15:44
 */
public class JdSpInit {

    private int yhid;
    private String qd;
    private String userid;

    @ColumnConfig(name = "SKU")
    private String bbid;
    private String sjsj;
    private String xjsj;
    @ColumnConfig(name = "上柜时间")
    private String cjsj;

    public JdSpInit() {
    }


    public int getYhid() {
        return yhid;
    }

    public void setYhid(int yhid) {
        this.yhid = yhid;
    }

    public String getQd() {
        return qd;
    }

    public void setQd(String qd) {
        this.qd = qd;
    }

    public String getUserid() {
        return userid;
    }

    public void setUserid(String userid) {
        this.userid = userid;
    }

    public String getBbid() {
        return bbid;
    }

    public void setBbid(String bbid) {
        this.bbid = bbid;
    }

    public String getSjsj() {
        return sjsj;
    }

    public void setSjsj(String sjsj) {
        this.sjsj = sjsj;
    }

    public String getXjsj() {
        return xjsj;
    }

    public void setXjsj(String xjsj) {
        this.xjsj = xjsj;
    }

    public String getCjsj() {
        return cjsj;
    }

    public void setCjsj(String cjsj) {
        this.cjsj = cjsj;
    }

}
