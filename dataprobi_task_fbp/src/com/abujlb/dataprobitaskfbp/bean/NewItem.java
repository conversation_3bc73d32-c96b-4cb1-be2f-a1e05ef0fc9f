package com.abujlb.dataprobitaskfbp.bean;

import com.abujlb.dataprobitaskfbp.utils.FastJSONObjAttrToNumber;
import com.abujlb.dataprobitaskfbp.utils.MathUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022/12/26 10:01
 */
public class NewItem {
    private String itemid;
    private int taskFinishCount;
    private int taskCount;
    private double taskFinishRatio;
    private List<NewItemTask> taskList;

    public NewItem() {
    }

    public static NewItem create(Object obj) {
        if (obj instanceof JSONObject) {
            JSONObject jsonObject = (JSONObject) obj;

            NewItem newItem = new NewItem();

            try {
                String itemid = FastJSONObjAttrToNumber.toString(jsonObject, "base", "itemId");
                if (StringUtils.isBlank(itemid)) {
                    return null;
                }
                newItem.setItemid(itemid);
                JSONObject taskInfo = FastJSONObjAttrToNumber.toJSONObject(jsonObject, "taskInfo");
                if (taskInfo == null || taskInfo.keySet().isEmpty()) {
                    return newItem;
                }

                JSONObject taskMap = FastJSONObjAttrToNumber.toJSONObject(taskInfo, "taskMap");
                if (taskMap == null || taskMap.keySet().isEmpty()) {
                    return newItem;
                }

                List<NewItemTask> taskList = new ArrayList<>();
                Set<String> taskMapKeys = taskMap.keySet();
                for (String taskMapKey : taskMapKeys) {
                    JSONArray taskArray = taskMap.getJSONArray(taskMapKey);
                    if (CollectionUtils.isNotEmpty(taskArray)) {
                        for (int i = 0; i < taskArray.size(); i++) {
                            NewItemTask newItemTask = new NewItemTask();
                            JSONObject taskSimpleInfo = taskArray.getJSONObject(i);
                            newItemTask.setTaskName(FastJSONObjAttrToNumber.toString(taskSimpleInfo, "name"));
                            newItemTask.setTaskStatus(FastJSONObjAttrToNumber.toBoolean(taskSimpleInfo, "isComplete") ? 1 : 0);
                            taskList.add(newItemTask);
                        }
                    }
                }

                newItem.setTaskList(taskList);
                newItem.setTaskCount(taskList.size());
                int finishCount = 0;
                for (NewItemTask newItemTask : taskList) {
                    if (newItemTask.getTaskStatus() == 1) {
                        finishCount++;
                    }
                }

                newItem.setTaskFinishCount(finishCount);
                newItem.setTaskFinishRatio(MathUtil.divide(newItem.getTaskFinishCount(), newItem.getTaskCount(), 6));
                return newItem;
            } catch (Exception e) {
            }
        }
        return null;
    }

    public String getItemid() {
        return itemid;
    }

    public void setItemid(String itemid) {
        this.itemid = itemid;
    }

    public int getTaskFinishCount() {
        return taskFinishCount;
    }

    public void setTaskFinishCount(int taskFinishCount) {
        this.taskFinishCount = taskFinishCount;
    }

    public int getTaskCount() {
        return taskCount;
    }

    public void setTaskCount(int taskCount) {
        this.taskCount = taskCount;
    }

    public double getTaskFinishRatio() {
        return taskFinishRatio;
    }

    public void setTaskFinishRatio(double taskFinishRatio) {
        this.taskFinishRatio = taskFinishRatio;
    }

    public List<NewItemTask> getTaskList() {
        return taskList;
    }

    public void setTaskList(List<NewItemTask> taskList) {
        this.taskList = taskList;
    }

    public String toString() {
        return JSON.toJSONString(this);
    }
}
