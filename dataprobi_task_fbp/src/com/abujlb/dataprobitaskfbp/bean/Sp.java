package com.abujlb.dataprobitaskfbp.bean;

/**
 * 表格存储abujlb6 .sp 的简单实体
 *
 * <AUTHOR>
 * @date 2022/5/24 9:00
 */
public class Sp {
    private String tid;
    private String cid;
    //京东spuid
    private String spuid;

    public Sp() {
    }

    public Sp(String tid, String cid) {
        this.tid = tid;
        this.cid = cid;
    }

    public String getTid() {
        return tid == null ? "" : tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    public String getCid() {
        return cid == null ? "" : cid;
    }

    public void setCid(String cid) {
        this.cid = cid;
    }

    public String getSpuid() {
        return spuid;
    }

    public void setSpuid(String spuid) {
        this.spuid = spuid;
    }
}
