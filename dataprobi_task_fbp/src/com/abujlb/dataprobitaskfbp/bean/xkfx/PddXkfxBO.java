package com.abujlb.dataprobitaskfbp.bean.xkfx;


import com.abujlb.dataprobitaskfbp.annotations.Qd;
import com.abujlb.dataprobitaskfbp.bean.SqliteSp;
import com.abujlb.dataprobitaskfbp.constants.BiConstant;
import com.abujlb.dataprobitaskfbp.utils.FastJSONObjAttrToNumber;
import com.abujlb.dataprobitaskfbp.utils.MathUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;

public class PddXkfxBO {

    //宝贝id
    @JSONField(serialize = false)
    private String bbid;
    //企业id
    @JSONField(serialize = false)
    private int yhid;
    //渠道
    @JSONField(serialize = false)
    private String qd;
    //店铺id
    @JSONField(serialize = false)
    private String userid;
    //日期类型
    @JSONField(serialize = false)
    private String rqlx;
    //日期
    @JSONField(serialize = false)
    private String rq;

    //销售额
    private double xse;
    //访客数
    private int fks;
    //收藏人数
    private int scrs;
    //支付买家数
    private int zfmjs;
    //下单人数 （公式=下单人数/访客数，因后台只有下单率和访客数，所以只能通过公式反推得到近似的下单人数。）
    private int xdrs;

    //uv价值 = 销售额/访客数
    private double uvjz;
    //支付转换率 = 支付买家数/访客数
    private double zfzhlv;
    //收藏率 = 收藏人数/访客数
    private double sclv;
    //流量损失指数 （目前只支持单天）
    private double llsszs;
    //下单率= 下单人数/访客数  （单天的直接取，7天、30天、月需要自己去汇总）
    private double xdlv;


    //多多进宝花费
    private double ddjbhf;
    private double ddjbcjje;
    //全站推广花费
    private double qztghf;
    private double qztgcjje;
    //商品推广花费
    private double sptghf;
    private double sptgcjje;
    //标准推广花费
    private double bztghf;
    private double bztgcjje;

    //推广花费 {"ddcjhf":0,"ddcjcjje":0...}
    @JSONField(serialize = false)
    private String tghf;

    public PddXkfxBO() {
    }

    public PddXkfxBO(SqliteSp sqliteSp) {
        if (sqliteSp == null) {
            return;
        }

        String other = sqliteSp.getOtherOrDefault();
        JSONObject jsonObject = JSONObject.parseObject(other);

        this.bbid = sqliteSp.getBbid();
        this.yhid = sqliteSp.getYhid();
        this.qd = Qd.PDD.getQdDesc();
        this.userid = sqliteSp.getUserid();
        this.xse = sqliteSp.getSpxgzfje();
        this.fks = sqliteSp.getSpxgfks();
        this.scrs = FastJSONObjAttrToNumber.toInt(jsonObject, "spxgscrs");
        this.zfmjs = sqliteSp.getSpxgzfmjs();

        this.uvjz = MathUtil.calZhlv(this.xse, this.fks);
        this.zfzhlv = sqliteSp.getSpxgzfzhl();
        this.sclv = MathUtil.calZhlv(this.scrs, this.fks);
        this.llsszs = FastJSONObjAttrToNumber.toDouble(jsonObject, "spxgllsszs");
        this.xdlv = FastJSONObjAttrToNumber.toDouble(jsonObject, "spxgxdl");

        this.xdrs = (int) MathUtil.multipy(this.xdlv, this.fks);

        this.bztghf = FastJSONObjAttrToNumber.toDouble(jsonObject, "bztghf");
        this.bztgcjje = FastJSONObjAttrToNumber.toDouble(jsonObject, "bztgcjje");
        this.ddjbhf = FastJSONObjAttrToNumber.toDouble(jsonObject, "ddjbhf");
        this.ddjbcjje = FastJSONObjAttrToNumber.toDouble(jsonObject, "ddjbcjje");
        this.sptghf = FastJSONObjAttrToNumber.toDouble(jsonObject, "sptghf");
        this.sptgcjje = FastJSONObjAttrToNumber.toDouble(jsonObject, "sptgcjje");
        this.qztghf = FastJSONObjAttrToNumber.toDouble(jsonObject, "qztghf");
        this.qztgcjje = FastJSONObjAttrToNumber.toDouble(jsonObject, "qztgcjje");

        this.tghf = String.format(BiConstant.PDD_XKFX_TGHF_FORMAT, bztghf, bztgcjje, ddjbhf, ddjbcjje, sptghf, sptgcjje, qztghf, qztgcjje);
    }

    public String getRqlx() {
        return rqlx;
    }

    public void setRqlx(String rqlx) {
        this.rqlx = rqlx;
    }

    public String getRq() {
        return rq;
    }

    public void setRq(String rq) {
        this.rq = rq;
    }

    public String getBbid() {
        return bbid;
    }

    public void setBbid(String bbid) {
        this.bbid = bbid;
    }

    public int getYhid() {
        return yhid;
    }

    public void setYhid(int yhid) {
        this.yhid = yhid;
    }

    public String getQd() {
        return qd;
    }

    public void setQd(String qd) {
        this.qd = qd;
    }

    public String getUserid() {
        return userid;
    }

    public void setUserid(String userid) {
        this.userid = userid;
    }

    public double getXse() {
        return xse;
    }

    public void setXse(double xse) {
        this.xse = xse;
    }

    public int getFks() {
        return fks;
    }

    public void setFks(int fks) {
        this.fks = fks;
    }

    public int getScrs() {
        return scrs;
    }

    public void setScrs(int scrs) {
        this.scrs = scrs;
    }

    public int getZfmjs() {
        return zfmjs;
    }

    public void setZfmjs(int zfmjs) {
        this.zfmjs = zfmjs;
    }

    public int getXdrs() {
        return xdrs;
    }

    public void setXdrs(int xdrs) {
        this.xdrs = xdrs;
    }

    public double getUvjz() {
        return uvjz;
    }

    public void setUvjz(double uvjz) {
        this.uvjz = uvjz;
    }

    public double getZfzhlv() {
        return zfzhlv;
    }

    public void setZfzhlv(double zfzhlv) {
        this.zfzhlv = zfzhlv;
    }

    public double getSclv() {
        return sclv;
    }

    public void setSclv(double sclv) {
        this.sclv = sclv;
    }

    public double getLlsszs() {
        return llsszs;
    }

    public void setLlsszs(double llsszs) {
        this.llsszs = llsszs;
    }

    public double getXdlv() {
        return xdlv;
    }

    public void setXdlv(double xdlv) {
        this.xdlv = xdlv;
    }

    public double getDdjbhf() {
        return ddjbhf;
    }

    public void setDdjbhf(double ddjbhf) {
        this.ddjbhf = ddjbhf;
    }

    public double getQztghf() {
        return qztghf;
    }

    public void setQztghf(double qztghf) {
        this.qztghf = qztghf;
    }

    public String getTghf() {
        return tghf;
    }

    public void setTghf(String tghf) {
        this.tghf = tghf;
    }

    public double getDdjbcjje() {
        return ddjbcjje;
    }

    public void setDdjbcjje(double ddjbcjje) {
        this.ddjbcjje = ddjbcjje;
    }

    public double getQztgcjje() {
        return qztgcjje;
    }

    public void setQztgcjje(double qztgcjje) {
        this.qztgcjje = qztgcjje;
    }


    public double getSptghf() {
        return sptghf;
    }

    public void setSptghf(double sptghf) {
        this.sptghf = sptghf;
    }

    public double getSptgcjje() {
        return sptgcjje;
    }

    public void setSptgcjje(double sptgcjje) {
        this.sptgcjje = sptgcjje;
    }

    public double getBztghf() {
        return bztghf;
    }

    public void setBztghf(double bztghf) {
        this.bztghf = bztghf;
    }

    public double getBztgcjje() {
        return bztgcjje;
    }

    public void setBztgcjje(double bztgcjje) {
        this.bztgcjje = bztgcjje;
    }

    public String toString() {
        return JSON.toJSONString(this);
    }
}
