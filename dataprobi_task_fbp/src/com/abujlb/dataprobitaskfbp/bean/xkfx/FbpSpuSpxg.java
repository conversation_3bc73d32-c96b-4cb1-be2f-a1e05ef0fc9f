package com.abujlb.dataprobitaskfbp.bean.xkfx;

import com.abujlb.filereader.ColumnConfig;

public class FbpSpuSpxg {

    @ColumnConfig(name = "SPU")
    private long spuid;
    @ColumnConfig(name = "访客数")
    private long fks;
    @ColumnConfig(name = "浏览量")
    private long lll;
    @ColumnConfig(name = "曝光量")
    private long bgl;
    @ColumnConfig(name = "点击次数")
    private int djcs;
    @ColumnConfig(name = "成交人数")
    private int cjkhs;
    @ColumnConfig(name = "下单客户数")
    private int xdkhs;
    @ColumnConfig(name = "成交商品件数")
    private int cjspjs;
    @ColumnConfig(name = "下单商品件数")
    private int xdspjs;
    @ColumnConfig(name = "商品关注数")
    private int scrs;
    @ColumnConfig(name = "加购人数")
    private int jgrs;
    @ColumnConfig(name = "加购商品件数")
    private int jgjs;
    @ColumnConfig(name = "成交金额")
    private double cjje;
    @ColumnConfig(name = "下单金额")
    private double xdje;
    @ColumnConfig(name = "PV现货率")
    private double pvxhlv;
    @ColumnConfig(name = "详情页跳出率")
    private double tslv;
    @ColumnConfig(name = "点击率")
    private double djlv;

    public long getSpuid() {
        return spuid;
    }

    public void setSpuid(long spuid) {
        this.spuid = spuid;
    }

    public long getFks() {
        return fks;
    }

    public void setFks(long fks) {
        this.fks = fks;
    }

    public long getLll() {
        return lll;
    }

    public void setLll(long lll) {
        this.lll = lll;
    }

    public long getBgl() {
        return bgl;
    }

    public void setBgl(long bgl) {
        this.bgl = bgl;
    }

    public int getDjcs() {
        return djcs;
    }

    public void setDjcs(int djcs) {
        this.djcs = djcs;
    }

    public int getCjkhs() {
        return cjkhs;
    }

    public void setCjkhs(int cjkhs) {
        this.cjkhs = cjkhs;
    }

    public int getXdkhs() {
        return xdkhs;
    }

    public void setXdkhs(int xdkhs) {
        this.xdkhs = xdkhs;
    }

    public int getCjspjs() {
        return cjspjs;
    }

    public void setCjspjs(int cjspjs) {
        this.cjspjs = cjspjs;
    }

    public int getXdspjs() {
        return xdspjs;
    }

    public void setXdspjs(int xdspjs) {
        this.xdspjs = xdspjs;
    }

    public int getScrs() {
        return scrs;
    }

    public void setScrs(int scrs) {
        this.scrs = scrs;
    }

    public int getJgrs() {
        return jgrs;
    }

    public void setJgrs(int jgrs) {
        this.jgrs = jgrs;
    }

    public int getJgjs() {
        return jgjs;
    }

    public void setJgjs(int jgjs) {
        this.jgjs = jgjs;
    }

    public double getCjje() {
        return cjje;
    }

    public void setCjje(double cjje) {
        this.cjje = cjje;
    }

    public double getXdje() {
        return xdje;
    }

    public void setXdje(double xdje) {
        this.xdje = xdje;
    }

    public double getPvxhlv() {
        return pvxhlv;
    }

    public void setPvxhlv(double pvxhlv) {
        this.pvxhlv = pvxhlv;
    }

    public double getTslv() {
        return tslv;
    }

    public void setTslv(double tslv) {
        this.tslv = tslv;
    }

    public double getDjlv() {
        return djlv;
    }

    public void setDjlv(double djlv) {
        this.djlv = djlv;
    }
}
