package com.abujlb.dataprobitaskfbp.bean.xkfx;


import com.abujlb.dataprobitaskfbp.bean.SqliteSp;
import com.abujlb.dataprobitaskfbp.constants.BiConstant;
import com.abujlb.dataprobitaskfbp.utils.FastJSONObjAttrToNumber;
import com.abujlb.dataprobitaskfbp.utils.MathUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;

public class TxXkfxBO {

    //宝贝id
    @JSONField(serialize = false)
    private String bbid;
    //企业id
    @JSONField(serialize = false)
    private int yhid;
    //渠道
    @JSONField(serialize = false)
    private String qd;
    //店铺id
    @JSONField(serialize = false)
    private String userid;
    //日期类型
    @JSONField(serialize = false)
    private String rqlx;
    //日期
    @JSONField(serialize = false)
    private String rq;

    //销售额
    private double xse;
    //访客数
    private int fks;
    //浏览量
    private int lll;
    //收藏人数
    private int scrs;
    //加购人数
    private int jgrs;
    //加购件数
    private int jgjs;
    //访客平均价值
    private double fkpjjz;
    //平均停留时长
    private double pjtlsc;
    //详情页跳出率
    private double xqytclv;
    //支付买家数
    private int zfmjs;
    //支付件数
    private int zfjs;
    //退款金额
    private double tkje;
    //搜索引导访客数
    private int ssydfks;
    //搜索引导支付买家数
    private int ssydzfmjs;

    //直通车花费
    private double ztchf;
    //直通车成交金额
    private double ztccjje;
    //引力魔方花费
    private double ylmfhf;
    //引力魔方成交金额
    private double ylmfcjje;
    //万相台花费
    private double aizthf;
    //万相台成交金额
    private double aiztcjje;
    //全站推广花费
    private double qztghf;
    //全站推广成交金额
    private double qztgcjje;
    //多目标直投花费
    private double dmbzthf;
    //多目标直投成交金额
    private double dmbztcjje;

    //推广花费 {"ztchf":0,"ztccjje":0...}
    @JSONField(serialize = false)
    private String tghf;

    public TxXkfxBO() {
    }

    public TxXkfxBO(SqliteSp sqliteSp) {
        if (sqliteSp == null) {
            return;
        }

        String other = sqliteSp.getOtherOrDefault();
        JSONObject jsonObject = JSONObject.parseObject(other);

        this.bbid = sqliteSp.getBbid();
        this.yhid = sqliteSp.getYhid();
        this.qd = sqliteSp.getQd();
        this.userid = sqliteSp.getUserid();

        this.xse = sqliteSp.getSpxgzfje();
        this.fks = sqliteSp.getSpxgfks();
        this.lll = sqliteSp.getSpxglll();
        this.scrs = FastJSONObjAttrToNumber.toInt(jsonObject, "spxgscrs");
        this.jgrs = FastJSONObjAttrToNumber.toInt(jsonObject, "spxgjgrs");
        this.jgjs = FastJSONObjAttrToNumber.toInt(jsonObject, "spxgjgjs");
        this.fkpjjz = MathUtil.divide(this.xse, this.fks);
        this.xqytclv = FastJSONObjAttrToNumber.toInt(jsonObject, "spxgxqytclv");
        this.pjtlsc = FastJSONObjAttrToNumber.toInt(jsonObject, "spxgpjtlsc");

        this.zfmjs = sqliteSp.getSpxgzfmjs();
        this.zfjs = sqliteSp.getSpxgzfjs();
        this.tkje = sqliteSp.getSpxgtkje();
        this.ssydfks = FastJSONObjAttrToNumber.toInt(jsonObject, "spxgssydfks");
        this.ssydzfmjs = FastJSONObjAttrToNumber.toInt(jsonObject, "spxgssydzfmjs");

        this.ztchf = FastJSONObjAttrToNumber.toDouble(jsonObject, "ztchf");
        this.ztccjje = FastJSONObjAttrToNumber.toDouble(jsonObject, "ztccjje");
        this.ylmfhf = FastJSONObjAttrToNumber.toDouble(jsonObject, "ylmfhf");
        this.ylmfcjje = FastJSONObjAttrToNumber.toDouble(jsonObject, "ylmfcjje");
        this.aizthf = FastJSONObjAttrToNumber.toDouble(jsonObject, "aizthf");
        this.aiztcjje = FastJSONObjAttrToNumber.toDouble(jsonObject, "aiztcjje");
        this.qztghf = FastJSONObjAttrToNumber.toDouble(jsonObject, "qztghf");
        this.qztgcjje = FastJSONObjAttrToNumber.toDouble(jsonObject, "qztgcjje");
        this.dmbzthf = FastJSONObjAttrToNumber.toDouble(jsonObject, "dmbzthf");
        this.dmbztcjje = FastJSONObjAttrToNumber.toDouble(jsonObject, "dmbztcjje");

        this.tghf = String.format(BiConstant.TX_XKFX_TGHF_FORMAT, this.ztchf, this.ztccjje, this.ylmfhf, this.ylmfcjje, this.aizthf, this.aiztcjje, this.qztghf, this.qztgcjje, this.dmbzthf, this.dmbztcjje);
    }

    public String getBbid() {
        return bbid;
    }

    public void setBbid(String bbid) {
        this.bbid = bbid;
    }

    public int getYhid() {
        return yhid;
    }

    public void setYhid(int yhid) {
        this.yhid = yhid;
    }

    public String getQd() {
        return qd;
    }

    public void setQd(String qd) {
        this.qd = qd;
    }

    public String getUserid() {
        return userid;
    }

    public void setUserid(String userid) {
        this.userid = userid;
    }

    public String getRqlx() {
        return rqlx;
    }

    public void setRqlx(String rqlx) {
        this.rqlx = rqlx;
    }

    public String getRq() {
        return rq;
    }

    public void setRq(String rq) {
        this.rq = rq;
    }

    public double getXse() {
        return xse;
    }

    public void setXse(double xse) {
        this.xse = xse;
    }

    public int getFks() {
        return fks;
    }

    public void setFks(int fks) {
        this.fks = fks;
    }

    public String getTghf() {
        return tghf;
    }

    public void setTghf(String tghf) {
        this.tghf = tghf;
    }

    public double getZtchf() {
        return ztchf;
    }

    public void setZtchf(double ztchf) {
        this.ztchf = ztchf;
    }

    public double getZtccjje() {
        return ztccjje;
    }

    public void setZtccjje(double ztccjje) {
        this.ztccjje = ztccjje;
    }

    public double getYlmfhf() {
        return ylmfhf;
    }

    public void setYlmfhf(double ylmfhf) {
        this.ylmfhf = ylmfhf;
    }

    public double getYlmfcjje() {
        return ylmfcjje;
    }

    public void setYlmfcjje(double ylmfcjje) {
        this.ylmfcjje = ylmfcjje;
    }

    public double getAizthf() {
        return aizthf;
    }

    public void setAizthf(double aizthf) {
        this.aizthf = aizthf;
    }

    public double getAiztcjje() {
        return aiztcjje;
    }

    public void setAiztcjje(double aiztcjje) {
        this.aiztcjje = aiztcjje;
    }

    public int getScrs() {
        return scrs;
    }

    public void setScrs(int scrs) {
        this.scrs = scrs;
    }

    public int getJgrs() {
        return jgrs;
    }

    public void setJgrs(int jgrs) {
        this.jgrs = jgrs;
    }

    public int getJgjs() {
        return jgjs;
    }

    public void setJgjs(int jgjs) {
        this.jgjs = jgjs;
    }

    public double getFkpjjz() {
        return fkpjjz;
    }

    public void setFkpjjz(double fkpjjz) {
        this.fkpjjz = fkpjjz;
    }

    public double getPjtlsc() {
        return pjtlsc;
    }

    public void setPjtlsc(double pjtlsc) {
        this.pjtlsc = pjtlsc;
    }

    public double getXqytclv() {
        return xqytclv;
    }

    public void setXqytclv(double xqytclv) {
        this.xqytclv = xqytclv;
    }

    public int getZfmjs() {
        return zfmjs;
    }

    public void setZfmjs(int zfmjs) {
        this.zfmjs = zfmjs;
    }

    public int getZfjs() {
        return zfjs;
    }

    public void setZfjs(int zfjs) {
        this.zfjs = zfjs;
    }

    public double getTkje() {
        return tkje;
    }

    public void setTkje(double tkje) {
        this.tkje = tkje;
    }

    public int getSsydfks() {
        return ssydfks;
    }

    public void setSsydfks(int ssydfks) {
        this.ssydfks = ssydfks;
    }

    public int getSsydzfmjs() {
        return ssydzfmjs;
    }

    public void setSsydzfmjs(int ssydzfmjs) {
        this.ssydzfmjs = ssydzfmjs;
    }

    public int getLll() {
        return lll;
    }

    public void setLll(int lll) {
        this.lll = lll;
    }

    public double getQztghf() {
        return qztghf;
    }

    public void setQztghf(double qztghf) {
        this.qztghf = qztghf;
    }

    public double getQztgcjje() {
        return qztgcjje;
    }

    public void setQztgcjje(double qztgcjje) {
        this.qztgcjje = qztgcjje;
    }

    public double getDmbzthf() {
        return dmbzthf;
    }

    public void setDmbzthf(double dmbzthf) {
        this.dmbzthf = dmbzthf;
    }

    public double getDmbztcjje() {
        return dmbztcjje;
    }

    public void setDmbztcjje(double dmbztcjje) {
        this.dmbztcjje = dmbztcjje;
    }

    public String toString() {
        return JSON.toJSONString(this);
    }
}
