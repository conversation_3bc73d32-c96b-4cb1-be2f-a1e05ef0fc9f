package com.abujlb.dataprobitaskfbp.bean.xkfx;


import com.abujlb.dataprobitaskfbp.bean.SqliteSp;
import com.abujlb.dataprobitaskfbp.constants.BiConstant;
import com.abujlb.dataprobitaskfbp.utils.FastJSONObjAttrToNumber;
import com.abujlb.dataprobitaskfbp.utils.MathUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;


public class JdXkfxBO {

    private String bbid;
    private String spuid;
    @JSONField(serialize = false)
    private int yhid;
    @JSONField(serialize = false)
    private String qd;
    @JSONField(serialize = false)
    private String userid;
    @JSONField(serialize = false)
    private String rqlx;
    @JSONField(serialize = false)
    private String rq;

    private String cid;
    private String tid;
    //销售额
    private double xse;
    //访客数
    private int fks;
    //收藏人数
    private int scrs;
    //加购件数
    private int jgjs;
    //加购人数
    private int jgrs;
    //支付买家数
    private int zfmjs;
    //浏览量
    private int lll;
    //曝光量 2023-12-15日新增
    private int bgl;
    //点击次数 2023-12-15日新增
    private int djcs;

    //uv价值 = 销售额/访客数
    private double uvjz;
    //支付转换率 = 支付买家数/访客数
    private double zfzhlv;
    //收藏率 = 收藏人数/访客数
    private double sclv;
    //加购率 = 加购人数/访客数
    private double jglv;
    //跳失率
    private double tslv;
    //pv现货率
    private double pvxhlv;
    //点击率 = 点击次数/曝光量  2023-12-15日新增
    private double djlv;

    //京东快车花费
    private double jdkchf;
    //京东快车成交金额
    private double jdkccjje;
    //购物触点花费
    private double gwcdhf;
    //购物触点成交金额
    private double gwcdcjje;
    //白条花费
    private double bthf;
    //京豆花费
    private double jdouhf;
    //京东直投花费
    private double jdzthf;
    //京东直投成交金额
    private double jdztcjje;
    //京东展位花费
    private double jdzwhf;
    //京东展位成交金额
    private double jdzwcjje;
    //京挑客花费
    private double jtkhf;
    //京挑客花费账单
    private double jtkhfZd;

    @JSONField(serialize = false)
    private String tghf;

    public JdXkfxBO() {
    }

    public JdXkfxBO(SqliteSp sqliteSp) {
        if (sqliteSp == null) {
            return;
        }

        String other = sqliteSp.getOtherOrDefault();
        JSONObject otherObj = JSONObject.parseObject(other);

        this.bbid = sqliteSp.getBbid();
        this.yhid = sqliteSp.getYhid();
        this.qd = "JD";
        this.userid = sqliteSp.getUserid();
        this.xse = sqliteSp.getSpxgzfje();
        this.fks = sqliteSp.getSpxgfks();
        this.scrs = FastJSONObjAttrToNumber.toInt(otherObj, "spxggzs");
        this.zfmjs = sqliteSp.getSpxgzfmjs();
        this.jgjs = FastJSONObjAttrToNumber.toInt(otherObj, "spxgjgjs");
        this.jgrs = FastJSONObjAttrToNumber.toInt(otherObj, "spxgjgrs");
        this.lll = sqliteSp.getSpxglll();

        this.uvjz = MathUtil.calZhlv(this.xse, this.fks);
        this.zfzhlv = sqliteSp.getSpxgzfzhl();
        this.sclv = MathUtil.calZhlv(this.scrs, this.fks);
        this.jglv = MathUtil.calZhlv(this.jgrs, this.fks);
        this.tslv = FastJSONObjAttrToNumber.toDouble(otherObj, "spxgtclv");
        this.pvxhlv = FastJSONObjAttrToNumber.toDouble(otherObj, "spxgpvxhlv");

        //京东快车花费
        this.jdkchf = FastJSONObjAttrToNumber.toDouble(otherObj, "jdkchf");
        this.jdkccjje = FastJSONObjAttrToNumber.toDouble(otherObj, "jdkccjje");
        //购物触点花费
        this.gwcdhf = FastJSONObjAttrToNumber.toDouble(otherObj, "gwcdhf");
        this.gwcdcjje = FastJSONObjAttrToNumber.toDouble(otherObj, "gwcdcjje");
        //白条花费
        this.bthf = FastJSONObjAttrToNumber.toDouble(otherObj, "bthf");
        //京豆花费
        this.jdouhf = FastJSONObjAttrToNumber.toDouble(otherObj, "jdouhf");
        //京东直投花费
        this.jdzthf = FastJSONObjAttrToNumber.toDouble(otherObj, "jdzthf");
        this.jdztcjje = FastJSONObjAttrToNumber.toDouble(otherObj, "jdztcjje");
        //京东展位花费
        this.jdzwhf = FastJSONObjAttrToNumber.toDouble(otherObj, "jdzwhf");
        this.jdzwcjje = FastJSONObjAttrToNumber.toDouble(otherObj, "jdzwcjje");
        //京挑客花费
        this.jtkhf = FastJSONObjAttrToNumber.toDouble(otherObj, "jtkhf");
        //京挑客花费账单
        this.jtkhfZd = FastJSONObjAttrToNumber.toDouble(otherObj, "jtkhfZd");

        this.tghf = String.format(BiConstant.JD_XKFX_TGHF_FORMAT, this.jdkchf, this.jdkccjje, this.gwcdhf, this.gwcdcjje, this.bthf, this.jdouhf, this.jdzthf, this.jdztcjje, this.jdzwhf, this.jdzwcjje, this.jtkhf, this.jtkhfZd);
    }

    public JdXkfxBO(SpuSpxg spuSpxg) {
        this.bbid = spuSpxg.getSpuid();
        this.spuid = spuSpxg.getSpuid();
//        this.yhid = sqliteSp.getYhid();
        this.qd = "JD";
//        this.userid = sqliteSp.getUserid();
        this.xse = spuSpxg.getCjje();
        this.fks = (int) spuSpxg.getFks();
        this.scrs = spuSpxg.getScrs();
        this.zfmjs = spuSpxg.getCjkhs();
        this.jgjs = spuSpxg.getJgjs();
        this.jgrs = spuSpxg.getJgrs();
        this.lll = (int) spuSpxg.getLll();
        this.bgl = (int) spuSpxg.getBgl();
        this.djlv = spuSpxg.getDjlv();
        this.djcs = spuSpxg.getDjcs();

        this.uvjz = MathUtil.calZhlv(this.xse, this.fks);
        this.zfzhlv = MathUtil.calZhlv(this.zfmjs, this.fks);
        this.sclv = MathUtil.calZhlv(this.scrs, this.fks);
        this.jglv = MathUtil.calZhlv(this.jgrs, this.fks);
        this.tslv = spuSpxg.getTslv();
        this.pvxhlv = spuSpxg.getPvxhlv();
        this.tghf = "{}";
    }

    public String getSpuid() {
        return spuid;
    }

    public void setSpuid(String spuid) {
        this.spuid = spuid;
    }

    public String getBbid() {
        return bbid;
    }

    public void setBbid(String bbid) {
        this.bbid = bbid;
    }

    public int getYhid() {
        return yhid;
    }

    public void setYhid(int yhid) {
        this.yhid = yhid;
    }

    public String getQd() {
        return qd;
    }

    public void setQd(String qd) {
        this.qd = qd;
    }

    public String getUserid() {
        return userid;
    }

    public void setUserid(String userid) {
        this.userid = userid;
    }

    public String getRqlx() {
        return rqlx;
    }

    public void setRqlx(String rqlx) {
        this.rqlx = rqlx;
    }

    public String getRq() {
        return rq;
    }

    public void setRq(String rq) {
        this.rq = rq;
    }

    public double getXse() {
        return xse;
    }

    public void setXse(double xse) {
        this.xse = xse;
    }

    public int getFks() {
        return fks;
    }

    public void setFks(int fks) {
        this.fks = fks;
    }

    public int getScrs() {
        return scrs;
    }

    public void setScrs(int scrs) {
        this.scrs = scrs;
    }

    public int getJgjs() {
        return jgjs;
    }

    public void setJgjs(int jgjs) {
        this.jgjs = jgjs;
    }

    public int getJgrs() {
        return jgrs;
    }

    public void setJgrs(int jgrs) {
        this.jgrs = jgrs;
    }

    public int getZfmjs() {
        return zfmjs;
    }

    public void setZfmjs(int zfmjs) {
        this.zfmjs = zfmjs;
    }

    public double getUvjz() {
        return uvjz;
    }

    public void setUvjz(double uvjz) {
        this.uvjz = uvjz;
    }

    public double getZfzhlv() {
        return zfzhlv;
    }

    public void setZfzhlv(double zfzhlv) {
        this.zfzhlv = zfzhlv;
    }

    public double getSclv() {
        return sclv;
    }

    public void setSclv(double sclv) {
        this.sclv = sclv;
    }

    public double getJglv() {
        return jglv;
    }

    public void setJglv(double jglv) {
        this.jglv = jglv;
    }

    public double getTslv() {
        return tslv;
    }

    public void setTslv(double tslv) {
        this.tslv = tslv;
    }

    public double getPvxhlv() {
        return pvxhlv;
    }

    public void setPvxhlv(double pvxhlv) {
        this.pvxhlv = pvxhlv;
    }

    public double getJdkchf() {
        return jdkchf;
    }

    public void setJdkchf(double jdkchf) {
        this.jdkchf = jdkchf;
    }

    public double getGwcdhf() {
        return gwcdhf;
    }

    public void setGwcdhf(double gwcdhf) {
        this.gwcdhf = gwcdhf;
    }

    public double getBthf() {
        return bthf;
    }

    public void setBthf(double bthf) {
        this.bthf = bthf;
    }

    public double getJdouhf() {
        return jdouhf;
    }

    public void setJdouhf(double jdouhf) {
        this.jdouhf = jdouhf;
    }

    public double getJdzthf() {
        return jdzthf;
    }

    public void setJdzthf(double jdzthf) {
        this.jdzthf = jdzthf;
    }

    public double getJdzwhf() {
        return jdzwhf;
    }

    public void setJdzwhf(double jdzwhf) {
        this.jdzwhf = jdzwhf;
    }

    public double getJtkhf() {
        return jtkhf;
    }

    public void setJtkhf(double jtkhf) {
        this.jtkhf = jtkhf;
    }

    public double getJtkhfZd() {
        return jtkhfZd;
    }

    public void setJtkhfZd(double jtkhfZd) {
        this.jtkhfZd = jtkhfZd;
    }

    public double getJdkccjje() {
        return jdkccjje;
    }

    public void setJdkccjje(double jdkccjje) {
        this.jdkccjje = jdkccjje;
    }

    public double getGwcdcjje() {
        return gwcdcjje;
    }

    public void setGwcdcjje(double gwcdcjje) {
        this.gwcdcjje = gwcdcjje;
    }

    public double getJdztcjje() {
        return jdztcjje;
    }

    public void setJdztcjje(double jdztcjje) {
        this.jdztcjje = jdztcjje;
    }

    public double getJdzwcjje() {
        return jdzwcjje;
    }

    public void setJdzwcjje(double jdzwcjje) {
        this.jdzwcjje = jdzwcjje;
    }

    public String getTghf() {
        return tghf;
    }

    public void setTghf(String tghf) {
        this.tghf = tghf;
    }

    public int getLll() {
        return lll;
    }

    public void setLll(int lll) {
        this.lll = lll;
    }

    public int getBgl() {
        return bgl;
    }

    public void setBgl(int bgl) {
        this.bgl = bgl;
    }

    public double getDjlv() {
        return djlv;
    }

    public void setDjlv(double djlv) {
        this.djlv = djlv;
    }

    public int getDjcs() {
        return djcs;
    }

    public void setDjcs(int djcs) {
        this.djcs = djcs;
    }

    public String getCid() {
        return cid;
    }

    public void setCid(String cid) {
        this.cid = cid;
    }

    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    public String toString() {
        return JSON.toJSONString(this);
    }
}
