package com.abujlb.dataprobitaskfbp.bean;

import org.apache.commons.collections.CollectionUtils;

import java.util.List;

public class BiTaskLog {
    //店铺userid
    private String userid;
    //用户id
    private int yhid;
    //渠道
    private String qd;

    private String uuid;

    private String dpmc;

    private String start;
    private String end;

    private String taskType;

    private String clstart;

    private String clend;

    private int status;

    private int seconds;


    private long startTimestamp;
    private long endTimestamp;

    public BiTaskLog() {
    }

    public BiTaskLog(DataprobiTaskMsg taskMsgBean) {
        this.userid = taskMsgBean.getUserid();
        this.yhid = taskMsgBean.getYhid();
        this.qd = taskMsgBean.getQd();
        this.dpmc = taskMsgBean.getDpmc();
        List<String> rqList = taskMsgBean.getRqList();
        if (CollectionUtils.isNotEmpty(rqList)) {
            this.start = rqList.get(0);
            this.end = rqList.get(rqList.size() - 1);
        }
        this.taskType = taskMsgBean.getTaskType();
        this.uuid = taskMsgBean.getUuid();
    }


    public String getUserid() {
        return userid;
    }

    public void setUserid(String userid) {
        this.userid = userid;
    }

    public int getYhid() {
        return yhid;
    }

    public void setYhid(int yhid) {
        this.yhid = yhid;
    }

    public String getQd() {
        return qd;
    }

    public void setQd(String qd) {
        this.qd = qd;
    }

    public String getDpmc() {
        return dpmc;
    }

    public void setDpmc(String dpmc) {
        this.dpmc = dpmc;
    }

    public String getStart() {
        return start;
    }

    public void setStart(String start) {
        this.start = start;
    }

    public String getEnd() {
        return end;
    }

    public void setEnd(String end) {
        this.end = end;
    }

    public String getTaskType() {
        return taskType;
    }

    public void setTaskType(String taskType) {
        this.taskType = taskType;
    }

    public String getClstart() {
        return clstart;
    }

    public void setClstart(String clstart) {
        this.clstart = clstart;
    }

    public String getClend() {
        return clend;
    }

    public void setClend(String clend) {
        this.clend = clend;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getSeconds() {
        return seconds;
    }

    public void setSeconds(int seconds) {
        this.seconds = seconds;
    }

    public long getStartTimestamp() {
        return startTimestamp;
    }

    public void setStartTimestamp(long startTimestamp) {
        this.startTimestamp = startTimestamp;
    }

    public long getEndTimestamp() {
        return endTimestamp;
    }

    public void setEndTimestamp(long endTimestamp) {
        this.endTimestamp = endTimestamp;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }
}
