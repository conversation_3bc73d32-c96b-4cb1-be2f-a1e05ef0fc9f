package com.abujlb.dataprobitaskfbp.sqlite;

import com.abujlb.sqlite.SqliteDb;
import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;

import java.io.File;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/22 15:05
 */
public class BiSqliteDb extends SqliteDb {
    private static final Logger LOG = Logger.getLogger(BiSqliteDb.class);
    private final String file;

    public BiSqliteDb(String file) throws SQLException {
        super(file);
        this.file = file;
    }

    public BiSqliteDb(String file, String initSql, Object data) throws SQLException {
        super(file, initSql, data);
        this.file = file;
    }

    public BiSqliteDb(String file, String initSql, Object data, boolean delExist) throws SQLException {
        super(file, initSql, data, delExist);
        this.file = file;
    }

    public String getFile() {
        return file;
    }

    public File getFileD() {
        try {
            return new File(file);
        } catch (Exception e) {
            return null;
        }
    }

    public List<String> getDBColumns(String tableName) {
        Statement statement = null;
        ResultSet rs = null;
        List<String> dbColumns = new ArrayList<>();
        try {
            statement = this.createStatement();
            rs = statement.executeQuery("SELECT * FROM " + tableName + ";");
            ResultSetMetaData metaData = rs.getMetaData();
            int columnCount = metaData.getColumnCount();
            for (int i = 1; i <= columnCount; i++) {
                String columnName = metaData.getColumnName(i);
                dbColumns.add(columnName);
            }
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        } finally {
            if (statement != null) {
                try {
                    statement.close();
                } catch (SQLException e) {
                    LOG.error(e.getMessage(), e);
                }
            }
        }
        return dbColumns;
    }


    public static void fillNewFields(BiSqliteDb newDb, BiSqliteDb oldDb, String tableName) {
        List<String> dbColumnsNew = newDb.getDBColumns(tableName);
        List<String> dbColumnsOld = oldDb.getDBColumns(tableName);

        //计算中 newDb中有的  oldDb中没有的字段
        List<String> diff = new ArrayList<>();
        for (String column : dbColumnsNew) {
            if (!dbColumnsOld.contains(column)) {
                diff.add(column);
            }
        }

        if (CollectionUtils.isEmpty(diff)) {
            return;
        }
        //新增字段到olddb中
        Statement statement = null;
        try {
            statement = oldDb.createStatement();
            for (String column : diff) {
                String sql = "ALTER TABLE " + tableName + " ADD " + column + ";";
                statement.execute(sql);
            }

        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        } finally {
            if (statement != null) {
                try {
                    statement.close();
                } catch (Exception e) {
                    LOG.error(e.getMessage(), e);
                }
            }
        }
    }
}
