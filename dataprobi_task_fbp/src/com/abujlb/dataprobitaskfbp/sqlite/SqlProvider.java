package com.abujlb.dataprobitaskfbp.sqlite;

import com.abujlb.dataprobitaskfbp.constants.BiCacheConst;
import com.abujlb.util.ResourceLoader;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;

/**
 * <AUTHOR>
 * @date 2022/5/13 14:31
 */
public class SqlProvider {

    public static String INIT_DP_SQL = "com/abujlb/dataprobi/sql/initdp.sql";
    public static String INIT_SQL = "com/abujlb/dataprobi/sql/init.sql";

    public static String INSERT_SQL = "com/abujlb/dataprobi/sql/insert.sql";
    public static String INSERT_DP_SQL = "com/abujlb/dataprobi/sql/insertdp.sql";

    public static String UPDATE_SQL = "com/abujlb/dataprobi/sql/update.sql";
    public static String UPDATE_DP_SQL = "com/abujlb/dataprobi/sql/updatedp.sql";
    public static String SPSJHZ_SUM_SQL = "com/abujlb/dataprobitaskfbp/sqlite/sqls/spsjhzsum.txt";
    public static String TX_FKS_SQL = "com/abujlb/dataprobitaskfbp/sqlite/sqls/inittxfks.sql";


    public static String TGFX_INIT_SQL = "com/abujlb/dataprobitaskfbp/sqlite/sqls/TGFX_INIT.sql";
    public static String TGFX_SUM_SQL = "com/abujlb/dataprobitaskfbp/sqlite/sqls/TGFX_SUM.sql";
    public static String TGFX_INSERT_SQL = "com/abujlb/dataprobitaskfbp/sqlite/sqls/TGFX_INSERT.sql";


    public static String readSql(String path) {
        InputStream inputStream = null;
        InputStreamReader inputStreamReader = null;
        BufferedReader br = null;
        String value = BiCacheConst.getStr(path);
        try {
            if (value != null) {
                return value;
            }
            inputStream = ResourceLoader.getResourceAsStream(path);
            inputStreamReader = new InputStreamReader(inputStream);
            br = new BufferedReader(inputStreamReader);
            StringBuffer sb = new StringBuffer();
            String line = null;
            while ((line = br.readLine()) != null) {
                sb.append(line);
            }

            value = sb.toString();
            BiCacheConst.putStr(path, value);
            return value;
        } catch (Exception e) {
            return null;
        } finally {
            if (br != null) {
                try {
                    br.close();
                } catch (IOException e) {
                }
            }
            if (inputStreamReader != null) {
                try {
                    inputStreamReader.close();
                } catch (IOException e) {
                }
            }
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                }
            }
        }
    }

}
