CREATE TABLE "tgfx"
(
    "userid"           text,
    "lx"               text,
    "rqlx"             text,
    "rq"               text,
    "itemid"           text not null,
    "hash"             text,
    "sum"              text,
    "campaignName"     text,
    "campaignId"       text,
    "ydfwqks" text,
    "ydfwrs"  text,
    "hf"      text,
    "zxl"              text,
    "djl"              text,
    "djlv"             text,
    "djzhlv"           text,
    "pjdjhf"           text,
    "qczxhf"           text,
    "roi"              text,
    "cjje"             text,
    "zjcjje"           text,
    "jjcjje"           text,
    "cjbs"             text,
    "zjcjbs"           text,
    "jjcjbs"           text,
    "zgwcs"            text,
    "zjgwcs"           text,
    "jjgwcs"           text,
    "jglv"             text,
    "zscs"             text,
    "sclv"             text,
    "dpdyl"            text,
    "bbscs"            text,
    "dpscs"            text,
    "djcb"             text,
    "campaignType"     text,
    "campaignTypeDesc" text,
    "yxbgl"            text,
    "xsl"              text,
    "xpl"              text,
    "xszhlv"           text,
    "xscb"             text,
    "jjhds"            text,
    "ljdgs"            text,
    "lqs"              text,
    "tjddcgs"          text,
    "gzs"              text,
    "xdl"              text,
    "zhs"              text,
    "zhlv"             text,
    "zhcb"             text
);

CREATE INDEX ids_group ON tgfx (itemid, campaignId);