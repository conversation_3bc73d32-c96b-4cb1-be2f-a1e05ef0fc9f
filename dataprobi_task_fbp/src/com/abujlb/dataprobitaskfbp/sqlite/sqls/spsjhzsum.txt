SELECT bbid,
       SUM(spxgfks)                  as spxgfks,
       SUM(spxglll)                  as spxglll,
       SUM(spxglll * spxgpjtlsc)     as spxgpjtlsc,
       SUM(spxgfks * spxgxqytclv)    as spxgxqytclv,
       SUM(spxgjgrs)                 as spxgjgrs,
       SUM(spxgscrs)                 as spxgscrs,
       SUM(spxgssydfks)              as spxgssydfks,
       SUM(spxgxdmjs)                as spxgxdmjs,
       SUM(spxgxdjs)                 as spxgxdjs,
       SUM(spxgxdje)                 as spxgxdje,
       SUM(spxgzfmjs)                as spxgzfmjs,
       SUM(spxgzfjs)                 as spxgzfjs,
       SUM(spxgzfje)                 as spxgzfje,
       SUM(spxgzfxmjs)               as spxgzfxmjs,
       SUM(spxgzflmjs)               as spxgzflmjs,
       SUM(spxglmjzfje)              as spxglmjzfje,
       SUM(spxgjhszfje)              as spxgjhszfje,
       SUM(spxgyljzfje)              as spxgyljzfje,
       SUM(spxgnljzfje)              as spxgnljzfje,
       SUM(spxgyljzfjs)              as spxgyljzfjs,
       SUM(spxgssydzfmjs)            as spxgssydzfmjs,
       SUM(spxgtkje)                 as spxgtkje,
       SUM(spxgjgjs)                 as spxgjgjs,
       SUM(ztchf)                    as ztchf,
       SUM(ztczxl)                   as ztczxl,
       SUM(ztcdjl)                   as ztcdjl,
       SUM(ztczscs)                  as ztczscs,
       SUM(ztcscbbs)                 as ztcscbbs,
       SUM(ztcscdps)                 as ztcscdps,
       SUM(ztczgwcs)                 as ztczgwcs,
       SUM(ztczjgwcs)                as ztczjgwcs,
       SUM(ztcjjgwcs)                as ztcjjgwcs,
       SUM(ztczscjgs)                as ztczscjgs,
       SUM(ztcbbscjgs)               as ztcbbscjgs,
       SUM(ztcyscjje)                as ztcyscjje,
       SUM(ztcyscjbs)                as ztcyscjbs,
       SUM(ztczjyscjje)              as ztczjyscjje,
       SUM(ztczjyscjbs)              as ztczjyscjbs,
       SUM(ztcjjyscjje)              as ztcjjyscjje,
       SUM(ztcjjyscjbs)              as ztcjjyscjbs,
       SUM(ztccjje)                  as ztccjje,
       SUM(ztczjcjje)                as ztczjcjje,
       SUM(ztcjjcjje)                as ztcjjcjje,
       SUM(ztccjbs)                  as ztccjbs,
       SUM(ztczjcjbs)                as ztczjcjbs,
       SUM(ztcjjcjbs)                as ztcjjcjbs,
       SUM(ztcgwjczje)               as ztcgwjczje,
       SUM(ztcgwjczbs)               as ztcgwjczbs,
       SUM(ylmfhf)                   as ylmfhf,
       SUM(ylmfzxl)                  as ylmfzxl,
       SUM(ylmfdjl)                  as ylmfdjl,
       SUM(ylmfydfwl)                as ylmfydfwl,
       SUM(ylmfydfwrs)               as ylmfydfwrs,
       SUM(ylmfydfwqks)              as ylmfydfwqks,
       SUM(ylmfsdfwl)                as ylmfsdfwl,
       SUM(ylmfgzdpl)                as ylmfgzdpl,
       SUM(ylmfhsl)                  as ylmfhsl,
       SUM(ylmfhfl)                  as ylmfhfl,
       SUM(ylmfsdfwl * ylmfpjsdfwsc) as ylmfpjsdfwsc,
       SUM(ylmfxkhql)                as ylmfxkhql,
       SUM(ylmflxxh)                 as ylmflxxh,
       SUM(ylmflxcb)                 as ylmflxcb,
       SUM(ylmfscbbl)                as ylmfscbbl,
       SUM(ylmftjgwcl)               as ylmftjgwcl,
       SUM(ylmfpxddl)                as ylmfpxddl,
       SUM(ylmfpxddje)               as ylmfpxddje,
       SUM(ylmfysddl)                as ylmfysddl,
       SUM(ylmfysje)                 as ylmfysje,
       SUM(ylmfgwjczje)              as ylmfgwjczje,
       SUM(ylmfgwjczbs)              as ylmfgwjczbs,
       SUM(ylmfjgscl)                as ylmfjgscl,
       SUM(ylmfcjddl)                as ylmfcjddl,
       SUM(ylmfcjddje)               as ylmfcjddje,
       SUM(ylmfcjrs)                 as ylmfcjrs,
       SUM(aizthf)                   as aizthf,
       SUM(aiztbgl)                  as aiztbgl,
       SUM(aiztdjl)                  as aiztdjl,
       SUM(aiztzjgl)                 as aiztzjgl,
       SUM(aiztscl)                  as aiztscl,
       SUM(aiztyscjbs)               as aiztyscjbs,
       SUM(aiztyscjje)               as aiztyscjje,
       SUM(aiztcjbs)                 as aiztcjbs,
       SUM(aiztcjje)                 as aiztcjje,
       count(bbid)                   as iindex

       FROM  spsjhz   where rq >= '%s'  and rq <= '%s'  group by bbid;