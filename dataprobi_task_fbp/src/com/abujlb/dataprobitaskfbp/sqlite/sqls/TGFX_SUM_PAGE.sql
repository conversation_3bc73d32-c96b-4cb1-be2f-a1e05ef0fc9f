SELECT "userid","lx","campaignName","itemid","campaignId","hash",SUM("hf")  as hf,SUM("zxl")     as zxl,<PERSON><PERSON>("djl")     as djl,<PERSON><PERSON>("cjje")    as cjje,<PERSON><PERSON>("ydfwqks") as ydfwqks,S<PERSON>("ydfwrs")  as ydfwrs,SUM("zjcjje")  as zjcj<PERSON>,SUM("jjcjje")  as jjcj<PERSON>,SUM("cjbs")    as cjbs,SUM("zjcjbs")  as zjcj<PERSON>,SUM("jjcjbs")  as jjcj<PERSON>,SUM("zgwcs")   as zgwcs,SUM("zjgwcs")  as zjgwcs,SUM("jjgwcs")  as jjgwcs,SUM("zscs")    as zscs,SUM("dpdyl")   as dpdyl,SUM("bbscs")   as bbscs,SUM("dpscs")   as dpscs,"campaignType","campaignTypeDesc",SUM("yxbgl")   as yxbgl,<PERSON><PERSON>("xsl")     as xsl,SUM("xpl")     as xpl,SUM("jjhds")   as jjhds,SUM("ljdgs")   as ljdgs,SUM("lqs")     as lqs,SUM("tjddcgs") as tjddcgs,SUM("gzs")     as gzs,       SUM("xdl")     as xdl,SUM("zhs")     as zhs  FROM "tgfx"  GROUP BY itemid, campaignId  ORDER BY itemid, campaignId desc;