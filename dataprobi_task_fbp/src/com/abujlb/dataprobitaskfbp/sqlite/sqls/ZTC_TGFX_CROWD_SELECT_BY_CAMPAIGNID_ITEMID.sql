SELECT "campaignId", "campaignName", "itemid", "crowdName", SUM("zxl")      as zxl, S<PERSON>("djl")      as djl, S<PERSON>("hf")       as hf, SUM("zscs")     as zscs, S<PERSON>("bbscs")    as bbscs, S<PERSON>("dpscs")    as dpscs, SUM("zgwcs")    as zgwcs, SUM("zjgwcs")   as zjgwcs, SUM("jjgwcs")   as jjgwcs, SUM("cjje")     as cjje, SUM("zjcjje")   as zjcj<PERSON>, SUM("jjcjje")   as jjcj<PERSON>, <PERSON><PERSON>("cjbs")     as cjbs, SUM("zjcjbs")   as zjcj<PERSON>, SUM("jjcjbs")   as jjcjbs, SUM("zrllzhje") as zrllzhje, SUM("zrllbgl")  as zrllbgl, SUM("cjrs")     as cjrs, SUM("cjxks")    as cjxks  FROM "ztctgfx"  WHERE campaignId = %s  and itemid = %s  GROUP BY crowdName;