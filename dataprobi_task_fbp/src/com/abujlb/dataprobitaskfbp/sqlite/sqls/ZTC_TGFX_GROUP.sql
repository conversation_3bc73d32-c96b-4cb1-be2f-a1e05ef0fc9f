SELECT "campaignId", "campaignName", "itemid", "keyword", "crowdName", SUM("zxl")      as zxl, S<PERSON>("djl")      as djl, <PERSON><PERSON>("hf")       as hf, <PERSON><PERSON>("zscs")     as zscs, S<PERSON>("bbscs")    as bbscs, SUM("dpscs")    as dpscs, SUM("zgwcs")    as zgwcs, SUM("zjgwcs")   as zjgwcs, SUM("jjgwcs")   as jjgwcs, SUM("cjje")     as cjje, SUM("zjcjje")   as zjcj<PERSON>, SUM("jjcjje")   as jjcj<PERSON>, S<PERSON>("cjbs")     as cjbs, SUM("zjcjbs")   as zjcjbs, SUM("jjcjbs")   as jjcjbs, SUM("zrllzhje") as zrllzhje, SUM("zrllbgl")  as zrllbgl, S<PERSON>("cjrs")     as cjrs, SUM("cjxks")    as cjxks FROM "ztctgfx"  GROUP BY campaignId, itemid  ORDER BY campaignId, itemid  LIMIT %s  OFFSET %s ;