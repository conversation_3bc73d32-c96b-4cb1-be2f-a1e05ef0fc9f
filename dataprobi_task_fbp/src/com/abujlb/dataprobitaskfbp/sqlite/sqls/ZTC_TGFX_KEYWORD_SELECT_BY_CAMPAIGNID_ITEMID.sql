SELECT "campaignId", "campaignName ", "itemid", "keyword", SUM("zxl")    as zxl, SUM("djl")    as djl, SUM("hf")   as hf, SUM("zscs")   as zscs, SUM("bbscs")  as bbscs, S<PERSON>("dpscs")  as dpscs, SUM("zgwcs")  as zgwcs, SUM("zjgwcs") as zjgwcs, SUM("jjgwcs") as jjgwcs, SUM("cjje")  as cjje, SUM("zjcjje") as zjcj<PERSON>, SUM("jjcjje") as jjcj<PERSON>, S<PERSON>("cjbs")  as cjbs, SUM("zjcjbs") as zjcjbs, SUM("jjcjbs") as jjcjbs, SUM("zrllzhje") as zrllzhje, SUM("zrllbgl")  as zrllbgl, SUM("cjrs")  as cjrs, SUM("cjxks")  as cjxks  FROM "ztctgfx"  GROUP BY campaignId, itemid, keyword  limit %s  offset %s ;