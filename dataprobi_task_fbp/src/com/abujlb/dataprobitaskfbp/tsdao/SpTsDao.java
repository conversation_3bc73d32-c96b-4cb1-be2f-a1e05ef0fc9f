package com.abujlb.dataprobitaskfbp.tsdao;

import com.abujlb.dataprobitaskfbp.bean.JdFbpSpCate;
import com.abujlb.dataprobitaskfbp.bean.Sp;
import com.alicloud.openservices.tablestore.SyncClient;
import com.alicloud.openservices.tablestore.model.*;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/24 8:55
 */
@Component
public class SpTsDao {

    private static final String TABLE_NAME = "sp";

    public static final String COLUMN_TITLE = "title";
    public static final String COLUMN_IMG = "img";
    public static final String COLUMN_YJLMID = "oneLmid";
    public static final String COLUMN_CID = "cid";
    public static final String COLUMN_SPUID = "spuid";

    private SyncClient client = null;
    private static final Sp DEFAULT_SP = new Sp();
    @Autowired
    private TsDaoUtil tsDaoUtil;

    @PostConstruct
    public void init() {
        client = tsDaoUtil.getClient();
    }


    public void updateJdFbpSpCate(List<JdFbpSpCate> jdFbpSpCates, int yhid, String qd, String userid) {
        List<List<JdFbpSpCate>> partition = Lists.partition(jdFbpSpCates, 200);
        for (List<JdFbpSpCate> tempList : partition) {
            updateJdFbpSpCate2(tempList, yhid, qd, userid);
        }
    }

    private void updateJdFbpSpCate2(List<JdFbpSpCate> tempList, int yhid, String qd, String userid) {
        BatchWriteRowRequest batchWriteRowRequest = new BatchWriteRowRequest();
        for (JdFbpSpCate jdFbpSpCate : tempList) {
            if (jdFbpSpCate.getBbid() == 0) {
                continue;
            }

            PrimaryKeyBuilder pk1Builder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
            pk1Builder.addPrimaryKeyColumn("yhid_qd_userid_bbid", PrimaryKeyValue.fromString(yhid + "_" + qd + "_" + userid + "_" + jdFbpSpCate.getBbid()));
            RowUpdateChange rowUpdateChange = new RowUpdateChange(TABLE_NAME, pk1Builder.build());
            rowUpdateChange.put("qd_userid", ColumnValue.fromString(qd + "_" + userid));
            if (jdFbpSpCate.getSpuid() != 0) {
                rowUpdateChange.put("spuid", ColumnValue.fromString(String.valueOf(jdFbpSpCate.getSpuid())));
            }
            if (StringUtils.isNotBlank(jdFbpSpCate.getSpmc())) {
                rowUpdateChange.put("title", ColumnValue.fromString(String.valueOf(jdFbpSpCate.getSpmc())));
                rowUpdateChange.put("title2", ColumnValue.fromString(String.valueOf(jdFbpSpCate.getSpmc()).toLowerCase()));
            }
            if (jdFbpSpCate.getSjzt() != null && jdFbpSpCate.getSjzt().equals("上柜")) {
                rowUpdateChange.put("sfzs", ColumnValue.fromLong(1));
            } else if (jdFbpSpCate.getSjzt() != null && jdFbpSpCate.getSjzt().equals("下柜")) {
                rowUpdateChange.put("sfzs", ColumnValue.fromLong(0));
            } else if (jdFbpSpCate.getSjzt() != null && jdFbpSpCate.getSjzt().equals("可上柜")) {
                rowUpdateChange.put("sfzs", ColumnValue.fromLong(0));
            }

            if (StringUtils.isNotBlank(jdFbpSpCate.getSjsj())) {
                rowUpdateChange.put("sjsj", ColumnValue.fromString(jdFbpSpCate.getSjsj()));
            }
            if (StringUtils.isNotBlank(jdFbpSpCate.getCjsj())) {
                rowUpdateChange.put("cjsj", ColumnValue.fromString(jdFbpSpCate.getCjsj()));
            }

            rowUpdateChange.put("oneLmid", ColumnValue.fromString(jdFbpSpCate.getOneLmid() == null ? "" : jdFbpSpCate.getOneLmid()));
            rowUpdateChange.put("twoLmid", ColumnValue.fromString(jdFbpSpCate.getTwoLmid() == null ? "" : jdFbpSpCate.getTwoLmid()));
            rowUpdateChange.put("threeLmid", ColumnValue.fromString(jdFbpSpCate.getThreeLmid() == null ? "" : jdFbpSpCate.getThreeLmid()));
            rowUpdateChange.put("cid", ColumnValue.fromString(jdFbpSpCate.getCid() == null ? "" : jdFbpSpCate.getCid()));
            rowUpdateChange.put("lmids", ColumnValue.fromString(jdFbpSpCate.getLmids() == null ? "" : jdFbpSpCate.getLmids()));

            rowUpdateChange.put("onelmmc", ColumnValue.fromString(jdFbpSpCate.getOnelmmc() == null ? "" : jdFbpSpCate.getOnelmmc()));
            rowUpdateChange.put("twolmmc", ColumnValue.fromString(jdFbpSpCate.getTwolmmc() == null ? "" : jdFbpSpCate.getTwolmmc()));
            rowUpdateChange.put("threelmmc", ColumnValue.fromString(jdFbpSpCate.getThreelmmc() == null ? "" : jdFbpSpCate.getThreelmmc()));
            rowUpdateChange.put("cidmc", ColumnValue.fromString(jdFbpSpCate.getCidmc() == null ? "" : jdFbpSpCate.getCidmc()));
            rowUpdateChange.put("lmmcs", ColumnValue.fromString(jdFbpSpCate.getLmmcs() == null ? "" : jdFbpSpCate.getLmmcs()));

            rowUpdateChange.put("bbid", ColumnValue.fromString(String.valueOf(jdFbpSpCate.getBbid())));
            rowUpdateChange.put("qd", ColumnValue.fromString(qd));
            rowUpdateChange.put("userid", ColumnValue.fromString(userid));
            rowUpdateChange.put("yhid", ColumnValue.fromLong(yhid));

            batchWriteRowRequest.addRowChange(rowUpdateChange);
        }

        int maxRetryTime = 0;
        BatchWriteRowRequest retryRequest = null;
        while (maxRetryTime < 10) {
            BatchWriteRowResponse batchWriteRowResponse = client.batchWriteRow(retryRequest == null ? batchWriteRowRequest : retryRequest);
            if (batchWriteRowResponse.isAllSucceed()) {
                break;
            }

            retryRequest = batchWriteRowRequest.createRequestForRetry(batchWriteRowResponse.getFailedRows());
            maxRetryTime++;
        }
    }
}
