package com.abujlb.dataprobitaskfbp.tsdao;


import com.abujlb.dataprobitaskfbp.bean.DataprobiTaskMsg;
import com.abujlb.dataprobitaskfbp.bean.Sp;
import com.abujlb.dataprobitaskfbp.bean.xkfx.JdXkfxBO;
import com.abujlb.dataprobitaskfbp.bean.xkfx.PddXkfxBO;
import com.abujlb.dataprobitaskfbp.bean.xkfx.TxXkfxBO;
import com.abujlb.dataprobitaskfbp.constants.BiConstant;
import com.abujlb.dataprobitaskfbp.sqlite.BiSqliteDb;
import com.abujlb.dataprobitaskfbp.utils.SqliteDbUtil;
import com.abujlb.gson.Json2Object;
import com.alicloud.openservices.tablestore.SyncClient;
import com.alicloud.openservices.tablestore.model.*;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/30
 */
@Component
public class BiXkfxTsDao {

    private static final Logger LOG = Logger.getLogger(BiXkfxTsDao.class);
    private static final String TABLE_NAME = "bi_xkfx";

    private SyncClient client = null;

    @Autowired
    private TsDaoUtil tsDaoUtil;

    @Autowired
    private SpNewTsDao spNewTsDao;

    @PostConstruct
    public void init() {
        client = tsDaoUtil.getClient();
    }

    public void updateRowsTx(List<TxXkfxBO> list) {
        try {
            if (CollectionUtils.isEmpty(list)) {
                return;
            }
            List<List<TxXkfxBO>> all = Lists.partition(list, 200);
            for (List<TxXkfxBO> temp : all) {
                updateRowsTx2(temp);
            }
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
    }

    public void updateRowsPdd(List<PddXkfxBO> list) {
        try {
            if (CollectionUtils.isEmpty(list)) {
                return;
            }
            List<List<PddXkfxBO>> all = Lists.partition(list, 200);
            for (List<PddXkfxBO> temp : all) {
                updateRowsPdd2(temp);
            }
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
    }

    public void updateRowsJd(List<JdXkfxBO> list) {
        try {
            if (CollectionUtils.isEmpty(list)) {
                return;
            }
            List<List<JdXkfxBO>> all = Lists.partition(list, 200);
            for (List<JdXkfxBO> temp : all) {
                updateRowsJd2(temp);
            }
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
    }

    private void updateRowsPdd2(List<PddXkfxBO> list) {
        BatchWriteRowRequest batchWriteRowRequest = new BatchWriteRowRequest();

        for (PddXkfxBO data : list) {
            PrimaryKeyBuilder pk1Builder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
            pk1Builder.addPrimaryKeyColumn("yhid_qd_userid", PrimaryKeyValue.fromString(data.getYhid() + "_" + data.getQd() + "_" + data.getUserid()));
            pk1Builder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(data.getRqlx().contains("recent") ? data.getRqlx() : (data.getRqlx() + "_" + data.getRq())));
            pk1Builder.addPrimaryKeyColumn("bbid", PrimaryKeyValue.fromString(data.getBbid()));
            RowUpdateChange rowUpdateChange = new RowUpdateChange(TABLE_NAME, pk1Builder.build());

            rowUpdateChange.put("userid", ColumnValue.fromString(data.getUserid()));
            rowUpdateChange.put("qd", ColumnValue.fromString(data.getQd()));
            rowUpdateChange.put("yhid", ColumnValue.fromLong(data.getYhid()));
            rowUpdateChange.put("rqlx", ColumnValue.fromString(data.getRqlx()));
            rowUpdateChange.put("rq", ColumnValue.fromString(data.getRq()));

            rowUpdateChange.put("data", ColumnValue.fromString(data.toString()));
            rowUpdateChange.put("fks", ColumnValue.fromLong(data.getFks()));
            rowUpdateChange.put("tghf", ColumnValue.fromString(data.getTghf()));

            Sp sp = spNewTsDao.getSp(data.getYhid(), data.getQd(), data.getUserid(), data.getBbid());
            if (sp != null) {
                if (sp.getCid() != null) {
                    rowUpdateChange.put("cid", ColumnValue.fromString(sp.getCid()));
                }
                if (sp.getTid() != null) {
                    rowUpdateChange.put("tid", ColumnValue.fromString(sp.getTid()));
                }
            }

            batchWriteRowRequest.addRowChange(rowUpdateChange);
        }

        int maxRetryTime = 0;
        BatchWriteRowRequest retryRequest = null;
        while (maxRetryTime < 10) {
            BatchWriteRowResponse batchWriteRowResponse = client.batchWriteRow(retryRequest == null ? batchWriteRowRequest : retryRequest);
            if (batchWriteRowResponse.isAllSucceed()) {
                break;
            }

            retryRequest = batchWriteRowRequest.createRequestForRetry(batchWriteRowResponse.getFailedRows());
            maxRetryTime++;
        }
    }


    private void updateRowsJd2(List<JdXkfxBO> list) {
        BatchWriteRowRequest batchWriteRowRequest = new BatchWriteRowRequest();

        for (JdXkfxBO data : list) {
            PrimaryKeyBuilder pk1Builder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
            pk1Builder.addPrimaryKeyColumn("yhid_qd_userid", PrimaryKeyValue.fromString(data.getYhid() + "_" + data.getQd() + "_" + data.getUserid()));
            pk1Builder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(data.getRqlx().contains("recent") ? data.getRqlx() : (data.getRqlx() + "_" + data.getRq())));
            pk1Builder.addPrimaryKeyColumn("bbid", PrimaryKeyValue.fromString(data.getBbid()));
            RowUpdateChange rowUpdateChange = new RowUpdateChange(TABLE_NAME, pk1Builder.build());

            rowUpdateChange.put("userid", ColumnValue.fromString(data.getUserid()));
            rowUpdateChange.put("qd", ColumnValue.fromString(data.getQd()));
            rowUpdateChange.put("yhid", ColumnValue.fromLong(data.getYhid()));
            rowUpdateChange.put("rqlx", ColumnValue.fromString(data.getRqlx()));
            rowUpdateChange.put("rq", ColumnValue.fromString(data.getRq()));

            rowUpdateChange.put("data", ColumnValue.fromString(data.toString()));
            rowUpdateChange.put("fks", ColumnValue.fromLong(data.getFks()));
            rowUpdateChange.put("tghf", ColumnValue.fromString(data.getTghf()));

            Sp sp = spNewTsDao.getSpBySpuid(data.getSpuid());
            if (sp != null) {
                if (sp.getCid() != null) {
                    rowUpdateChange.put("cid", ColumnValue.fromString(sp.getCid()));
                }
                if (sp.getTid() != null) {
                    rowUpdateChange.put("tid", ColumnValue.fromString(sp.getTid()));
                }
            }

            batchWriteRowRequest.addRowChange(rowUpdateChange);
        }

        int maxRetryTime = 0;
        BatchWriteRowRequest retryRequest = null;
        while (maxRetryTime < 10) {
            BatchWriteRowResponse batchWriteRowResponse = client.batchWriteRow(retryRequest == null ? batchWriteRowRequest : retryRequest);
            if (batchWriteRowResponse.isAllSucceed()) {
                break;
            }

            retryRequest = batchWriteRowRequest.createRequestForRetry(batchWriteRowResponse.getFailedRows());
            maxRetryTime++;
        }
    }


    private void updateRowsTx2(List<TxXkfxBO> list) {
        BatchWriteRowRequest batchWriteRowRequest = new BatchWriteRowRequest();

        for (TxXkfxBO data : list) {
            PrimaryKeyBuilder pk1Builder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
            pk1Builder.addPrimaryKeyColumn("yhid_qd_userid", PrimaryKeyValue.fromString(data.getYhid() + "_" + data.getQd() + "_" + data.getUserid()));
            pk1Builder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(data.getRqlx().contains("recent") ? data.getRqlx() : (data.getRqlx() + "_" + data.getRq())));
            pk1Builder.addPrimaryKeyColumn("bbid", PrimaryKeyValue.fromString(data.getBbid()));
            RowUpdateChange rowUpdateChange = new RowUpdateChange(TABLE_NAME, pk1Builder.build());

            rowUpdateChange.put("userid", ColumnValue.fromString(data.getUserid()));
            rowUpdateChange.put("qd", ColumnValue.fromString(data.getQd()));
            rowUpdateChange.put("yhid", ColumnValue.fromLong(data.getYhid()));
            rowUpdateChange.put("rqlx", ColumnValue.fromString(data.getRqlx()));
            rowUpdateChange.put("rq", ColumnValue.fromString(data.getRq()));

            rowUpdateChange.put("data", ColumnValue.fromString(data.toString()));
            rowUpdateChange.put("fks", ColumnValue.fromLong(data.getFks()));
            rowUpdateChange.put("tghf", ColumnValue.fromString(data.getTghf()));

            Sp sp = spNewTsDao.getSp(data.getYhid(), data.getQd(), data.getUserid(), data.getBbid());
            if (sp != null) {
                if (sp.getCid() != null) {
                    rowUpdateChange.put("cid", ColumnValue.fromString(sp.getCid()));
                }
                if (sp.getTid() != null) {
                    rowUpdateChange.put("tid", ColumnValue.fromString(sp.getTid()));
                }
            }

            batchWriteRowRequest.addRowChange(rowUpdateChange);
        }

        int maxRetryTime = 0;
        BatchWriteRowRequest retryRequest = null;
        while (maxRetryTime < 10) {
            BatchWriteRowResponse batchWriteRowResponse = client.batchWriteRow(retryRequest == null ? batchWriteRowRequest : retryRequest);
            if (batchWriteRowResponse.isAllSucceed()) {
                break;
            }

            retryRequest = batchWriteRowRequest.createRequestForRetry(batchWriteRowResponse.getFailedRows());
            maxRetryTime++;
        }
    }


    public List<PddXkfxBO> getRowsPdd(DataprobiTaskMsg taskMsg, String start, String end) {
        RangeRowQueryCriteria rangeRowQueryCriteria = new RangeRowQueryCriteria(TABLE_NAME);

        //设置起始主键。
        PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
        primaryKeyBuilder.addPrimaryKeyColumn("yhid_qd_userid", PrimaryKeyValue.fromString(taskMsg.getYhid() + "_" + taskMsg.getQd() + "_" + taskMsg.getUserid()));
        primaryKeyBuilder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString("day_" + start));
        primaryKeyBuilder.addPrimaryKeyColumn("bbid", PrimaryKeyValue.INF_MIN);

        rangeRowQueryCriteria.setInclusiveStartPrimaryKey(primaryKeyBuilder.build());

        //设置结束主键。
        primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
        primaryKeyBuilder.addPrimaryKeyColumn("yhid_qd_userid", PrimaryKeyValue.fromString(taskMsg.getYhid() + "_" + taskMsg.getQd() + "_" + taskMsg.getUserid()));
        primaryKeyBuilder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString("day_" + end));
        primaryKeyBuilder.addPrimaryKeyColumn("bbid", PrimaryKeyValue.INF_MAX);

        rangeRowQueryCriteria.setExclusiveEndPrimaryKey(primaryKeyBuilder.build());
        rangeRowQueryCriteria.setMaxVersions(1);

        List<PddXkfxBO> list = new ArrayList<>();

        while (true) {
            GetRangeResponse getRangeResponse = client.getRange(new GetRangeRequest(rangeRowQueryCriteria));
            for (Row row : getRangeResponse.getRows()) {
                String data = row.getLatestColumn("data").getValue().asString();
                PddXkfxBO pddXkfxBO = Json2Object.parseObj(data, PddXkfxBO.class);
                pddXkfxBO.setBbid(row.getPrimaryKey().getPrimaryKeyColumn("bbid").getValue().asString());
                list.add(pddXkfxBO);
            }

            //如果NextStartPrimaryKey不为null，则继续读取。
            if (getRangeResponse.getNextStartPrimaryKey() != null) {
                rangeRowQueryCriteria.setInclusiveStartPrimaryKey(getRangeResponse.getNextStartPrimaryKey());
            } else {
                break;
            }
        }
        return list;
    }

    public void getRowsJd(DataprobiTaskMsg dataprobiJdMsgBean, BiSqliteDb biSqliteDb, String start, String end) {
        RangeRowQueryCriteria rangeRowQueryCriteria = new RangeRowQueryCriteria(TABLE_NAME);

        //设置起始主键。
        PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
        primaryKeyBuilder.addPrimaryKeyColumn("yhid_qd_userid", PrimaryKeyValue.fromString(dataprobiJdMsgBean.getYhid() + "_" + dataprobiJdMsgBean.getQd() + "_" + dataprobiJdMsgBean.getUserid()));
        primaryKeyBuilder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString("day_" + start));
        primaryKeyBuilder.addPrimaryKeyColumn("bbid", PrimaryKeyValue.INF_MIN);

        rangeRowQueryCriteria.setInclusiveStartPrimaryKey(primaryKeyBuilder.build());

        //设置结束主键。
        primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
        primaryKeyBuilder.addPrimaryKeyColumn("yhid_qd_userid", PrimaryKeyValue.fromString(dataprobiJdMsgBean.getYhid() + "_" + dataprobiJdMsgBean.getQd() + "_" + dataprobiJdMsgBean.getUserid()));
        primaryKeyBuilder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString("day_" + end));
        primaryKeyBuilder.addPrimaryKeyColumn("bbid", PrimaryKeyValue.INF_MAX);

        rangeRowQueryCriteria.setExclusiveEndPrimaryKey(primaryKeyBuilder.build());
        rangeRowQueryCriteria.setMaxVersions(1);

        List<String> sqls = new ArrayList<>();

        while (true) {
            GetRangeResponse getRangeResponse = client.getRange(new GetRangeRequest(rangeRowQueryCriteria));
            for (Row row : getRangeResponse.getRows()) {
                String rq = row.getLatestColumn("rq").getValue().asString();

                String data = row.getLatestColumn("data").getValue().asString();
                JdXkfxBO jdXkfxBO = Json2Object.parseObj(data, JdXkfxBO.class);
                if (jdXkfxBO != null) {
                    String sql = String.format(BiConstant.JD_XKFX_INSERTSQL, jdXkfxBO.getBbid(), jdXkfxBO.getSpuid(), rq, jdXkfxBO.getXse(),
                            jdXkfxBO.getFks(), jdXkfxBO.getDjcs(), jdXkfxBO.getBgl(), jdXkfxBO.getScrs(), jdXkfxBO.getJgjs(), jdXkfxBO.getJgrs(), jdXkfxBO.getZfmjs(), jdXkfxBO.getLll(),
                            jdXkfxBO.getPvxhlv(), jdXkfxBO.getTslv(), jdXkfxBO.getJdkchf(), jdXkfxBO.getJdkccjje(),
                            jdXkfxBO.getGwcdhf(), jdXkfxBO.getGwcdcjje(), jdXkfxBO.getBthf(), jdXkfxBO.getJdouhf(),
                            jdXkfxBO.getJdzthf(), jdXkfxBO.getJdztcjje(), jdXkfxBO.getJdzwhf(), jdXkfxBO.getJdzwcjje(),
                            jdXkfxBO.getJtkhf(), jdXkfxBO.getJtkhfZd());
                    sqls.add(sql);
                }
            }

            //如果NextStartPrimaryKey不为null，则继续读取。
            if (getRangeResponse.getNextStartPrimaryKey() != null) {
                rangeRowQueryCriteria.setInclusiveStartPrimaryKey(getRangeResponse.getNextStartPrimaryKey());
            } else {
                break;
            }
        }

        SqliteDbUtil.executeSqls(biSqliteDb, sqls);
    }


    public void getRowsTx(DataprobiTaskMsg dataprobiMsg, BiSqliteDb biSqliteDb, String start, String end) {
        RangeRowQueryCriteria rangeRowQueryCriteria = new RangeRowQueryCriteria(TABLE_NAME);

        //设置起始主键。
        PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
        primaryKeyBuilder.addPrimaryKeyColumn("yhid_qd_userid", PrimaryKeyValue.fromString(dataprobiMsg.getYhid() + "_" + dataprobiMsg.getQd() + "_" + dataprobiMsg.getUserid()));
        primaryKeyBuilder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString("day_" + start));
        primaryKeyBuilder.addPrimaryKeyColumn("bbid", PrimaryKeyValue.INF_MIN);

        rangeRowQueryCriteria.setInclusiveStartPrimaryKey(primaryKeyBuilder.build());

        //设置结束主键。
        primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
        primaryKeyBuilder.addPrimaryKeyColumn("yhid_qd_userid", PrimaryKeyValue.fromString(dataprobiMsg.getYhid() + "_" + dataprobiMsg.getQd() + "_" + dataprobiMsg.getUserid()));
        primaryKeyBuilder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString("day_" + end));
        primaryKeyBuilder.addPrimaryKeyColumn("bbid", PrimaryKeyValue.INF_MAX);

        rangeRowQueryCriteria.setExclusiveEndPrimaryKey(primaryKeyBuilder.build());
        rangeRowQueryCriteria.setMaxVersions(1);

        List<String> sqls = new ArrayList<>();

        while (true) {
            GetRangeResponse getRangeResponse = client.getRange(new GetRangeRequest(rangeRowQueryCriteria));
            for (Row row : getRangeResponse.getRows()) {
                String bbid = row.getPrimaryKey().getPrimaryKeyColumn("bbid").getValue().asString();
                String rq = row.getLatestColumn("rq").getValue().asString();

                String data = row.getLatestColumn("data").getValue().asString();
                TxXkfxBO txXkfxBO = Json2Object.parseObj(data, TxXkfxBO.class);
                if (txXkfxBO != null) {
                    String sql = String.format(BiConstant.TX_XKFX_INSERTSQL, bbid, rq, txXkfxBO.getXse(), txXkfxBO.getFks(), txXkfxBO.getLll(), txXkfxBO.getScrs(), txXkfxBO.getJgrs(), txXkfxBO.getJgjs(), txXkfxBO.getFkpjjz(), txXkfxBO.getPjtlsc(), txXkfxBO.getXqytclv(), txXkfxBO.getZfmjs(), txXkfxBO.getZfjs(), txXkfxBO.getTkje(), txXkfxBO.getSsydfks(), txXkfxBO.getSsydzfmjs(), txXkfxBO.getZtchf(), txXkfxBO.getZtccjje(), txXkfxBO.getYlmfhf(), txXkfxBO.getYlmfcjje(), txXkfxBO.getAizthf(), txXkfxBO.getAiztcjje());
                    sqls.add(sql);
                }
            }

            //如果NextStartPrimaryKey不为null，则继续读取。
            if (getRangeResponse.getNextStartPrimaryKey() != null) {
                rangeRowQueryCriteria.setInclusiveStartPrimaryKey(getRangeResponse.getNextStartPrimaryKey());
            } else {
                break;
            }
        }

        SqliteDbUtil.executeSqls(biSqliteDb, sqls);
    }

    public void deleteAll() {
        RangeRowQueryCriteria rangeRowQueryCriteria = new RangeRowQueryCriteria(TABLE_NAME);

        //设置起始主键。
        PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
        primaryKeyBuilder.addPrimaryKeyColumn("yhid_qd_userid", PrimaryKeyValue.INF_MIN);
        primaryKeyBuilder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.INF_MIN);
        primaryKeyBuilder.addPrimaryKeyColumn("bbid", PrimaryKeyValue.INF_MIN);

        rangeRowQueryCriteria.setInclusiveStartPrimaryKey(primaryKeyBuilder.build());

        //设置结束主键。
        primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
        primaryKeyBuilder.addPrimaryKeyColumn("yhid_qd_userid", PrimaryKeyValue.INF_MAX);
        primaryKeyBuilder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.INF_MAX);
        primaryKeyBuilder.addPrimaryKeyColumn("bbid", PrimaryKeyValue.INF_MAX);

        rangeRowQueryCriteria.setExclusiveEndPrimaryKey(primaryKeyBuilder.build());
        rangeRowQueryCriteria.setMaxVersions(1);

        while (true) {
            GetRangeResponse getRangeResponse = client.getRange(new GetRangeRequest(rangeRowQueryCriteria));
            delRow(getRangeResponse.getRows());

            //如果NextStartPrimaryKey不为null，则继续读取。
            if (getRangeResponse.getNextStartPrimaryKey() != null) {
                rangeRowQueryCriteria.setInclusiveStartPrimaryKey(getRangeResponse.getNextStartPrimaryKey());
            } else {
                break;
            }
        }
    }

    public void deleteRecent(int yhid, String qd, String userid) {
        final String[] recent_rqlx_arr = {"recent7", "recent30"};
        for (String rqlx : recent_rqlx_arr) {
            deleteRecent(yhid, qd, userid, rqlx);
        }
    }

    public void deleteRecent(int yhid, String qd, String userid, String rqlx) {
        RangeRowQueryCriteria rangeRowQueryCriteria = new RangeRowQueryCriteria(TABLE_NAME);

        //设置起始主键。
        PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
        primaryKeyBuilder.addPrimaryKeyColumn("yhid_qd_userid", PrimaryKeyValue.fromString(yhid + "_" + qd + "_" + userid));
        primaryKeyBuilder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(rqlx));
        primaryKeyBuilder.addPrimaryKeyColumn("bbid", PrimaryKeyValue.INF_MIN);

        rangeRowQueryCriteria.setInclusiveStartPrimaryKey(primaryKeyBuilder.build());

        //设置结束主键。
        primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
        primaryKeyBuilder.addPrimaryKeyColumn("yhid_qd_userid", PrimaryKeyValue.fromString(yhid + "_" + qd + "_" + userid));
        primaryKeyBuilder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(rqlx));
        primaryKeyBuilder.addPrimaryKeyColumn("bbid", PrimaryKeyValue.INF_MAX);

        rangeRowQueryCriteria.setExclusiveEndPrimaryKey(primaryKeyBuilder.build());
        rangeRowQueryCriteria.setMaxVersions(1);

        while (true) {
            GetRangeResponse getRangeResponse = client.getRange(new GetRangeRequest(rangeRowQueryCriteria));
            delRow(getRangeResponse.getRows());

            //如果NextStartPrimaryKey不为null，则继续读取。
            if (getRangeResponse.getNextStartPrimaryKey() != null) {
                rangeRowQueryCriteria.setInclusiveStartPrimaryKey(getRangeResponse.getNextStartPrimaryKey());
            } else {
                break;
            }
        }
    }

    public void delRow(List<Row> rows) {
        List<List<Row>> partition = Lists.partition(rows, 200);
        for (List<Row> rowList : partition) {
            BatchWriteRowRequest batchWriteRowRequest = new BatchWriteRowRequest();

            for (Row row : rowList) {
                PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();

                for (PrimaryKeyColumn primaryKeyColumn : row.getPrimaryKey().getPrimaryKeyColumns()) {
                    primaryKeyBuilder.addPrimaryKeyColumn(primaryKeyColumn.getName(), primaryKeyColumn.getValue());
                }

                RowDeleteChange rowDeleteChange = new RowDeleteChange(TABLE_NAME, primaryKeyBuilder.build());
                batchWriteRowRequest.addRowChange(rowDeleteChange);
            }

            BatchWriteRowResponse response = client.batchWriteRow(batchWriteRowRequest);
            int cscs = 0;
            while (cscs < 3 && !response.isAllSucceed()) {
                BatchWriteRowRequest retryRequest = batchWriteRowRequest.createRequestForRetry(response.getFailedRows());
                response = client.batchWriteRow(retryRequest);
                cscs++;
            }
        }
    }

}
