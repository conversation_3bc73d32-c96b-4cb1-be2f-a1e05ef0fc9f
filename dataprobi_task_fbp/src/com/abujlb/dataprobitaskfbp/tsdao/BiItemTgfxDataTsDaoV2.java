package com.abujlb.dataprobitaskfbp.tsdao;

import com.abujlb.dao.v2.TsDao2;
import com.abujlb.dataprobitaskfbp.bean.tgfx.TgfxReport;
import com.abujlb.dataprobitaskfbp.constants.RqlxConst;
import com.alicloud.openservices.tablestore.SyncClient;
import com.alicloud.openservices.tablestore.model.*;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/25 16:27
 */
@Component
public class BiItemTgfxDataTsDaoV2 {

    private static final Logger LOG = Logger.getLogger(BiItemTgfxDataTsDaoV2.class);
    private static final String TABLE_NAME = "bi_item_tgfx_data";

    private SyncClient client = null;
    private SyncClient clientTest = null;

    @Autowired
    private TsDao2 tsDao2;

    @PostConstruct
    public void init() {
        client = tsDao2.getClient("abujlb6@jushita").getClient();
        clientTest = tsDao2.getClient("abujlb6test@jushita").getClient();
    }

    public void updateRows(List<TgfxReport> list) {
        try {
            if (CollectionUtils.isEmpty(list)) {
                return;
            }

            write(list);
            writeTest(list);
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
    }

    private void write(List<TgfxReport> list) {
        BatchWriteRowRequest batchWriteRowRequest = new BatchWriteRowRequest();

        for (TgfxReport data : list) {
            if (StringUtils.isBlank(data.getItemid())) {
                continue;
            }
            PrimaryKeyBuilder pk1Builder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
            pk1Builder.addPrimaryKeyColumn("userid_lx", PrimaryKeyValue.fromString(data.getUserid() + "_" + data.getLx()));
            pk1Builder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(data.getRqlxRq()));
            pk1Builder.addPrimaryKeyColumn("itemid", PrimaryKeyValue.fromString(data.getItemid()));
            pk1Builder.addPrimaryKeyColumn("hash", PrimaryKeyValue.fromString(data.getHash()));
            RowUpdateChange rowUpdateChange = new RowUpdateChange(TABLE_NAME, pk1Builder.build());
            rowUpdateChange.put("data", ColumnValue.fromString(data.toString()));
            rowUpdateChange.put("sourceType", ColumnValue.fromLong(data.getSum()));
            batchWriteRowRequest.addRowChange(rowUpdateChange);
        }
        if (batchWriteRowRequest.getRowsCount() == 0) {
            return;
        }
        int maxRetryTime = 0;
        BatchWriteRowRequest retryRequest = null;
        while (maxRetryTime < 10) {
            BatchWriteRowResponse batchWriteRowResponse = client.batchWriteRow(retryRequest == null ? batchWriteRowRequest : retryRequest);
            if (batchWriteRowResponse.isAllSucceed()) {
                break;
            }

            retryRequest = batchWriteRowRequest.createRequestForRetry(batchWriteRowResponse.getFailedRows());
            maxRetryTime++;
        }
    }

    private void writeTest(List<TgfxReport> list) {
        BatchWriteRowRequest batchWriteRowRequest = new BatchWriteRowRequest();

        for (TgfxReport data : list) {
            if (StringUtils.isBlank(data.getItemid())) {
                continue;
            }
            PrimaryKeyBuilder pk1Builder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
            pk1Builder.addPrimaryKeyColumn("userid_lx", PrimaryKeyValue.fromString(data.getUserid() + "_" + data.getLx()));
            pk1Builder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(data.getRqlxRq()));
            pk1Builder.addPrimaryKeyColumn("itemid", PrimaryKeyValue.fromString(data.getItemid()));
            pk1Builder.addPrimaryKeyColumn("hash", PrimaryKeyValue.fromString(data.getHash()));
            RowUpdateChange rowUpdateChange = new RowUpdateChange(TABLE_NAME, pk1Builder.build());
            rowUpdateChange.put("data", ColumnValue.fromString(data.toString()));
            rowUpdateChange.put("sourceType", ColumnValue.fromLong(data.getSum()));
            batchWriteRowRequest.addRowChange(rowUpdateChange);
        }
        if (batchWriteRowRequest.getRowsCount() == 0) {
            return;
        }

        int maxRetryTime = 0;
        BatchWriteRowRequest retryRequest = null;
        while (maxRetryTime < 10) {
            BatchWriteRowResponse batchWriteRowResponse = clientTest.batchWriteRow(retryRequest == null ? batchWriteRowRequest : retryRequest);
            if (batchWriteRowResponse.isAllSucceed()) {
                break;
            }

            retryRequest = batchWriteRowRequest.createRequestForRetry(batchWriteRowResponse.getFailedRows());
            maxRetryTime++;
        }
    }


    public void delRecent(String userid, String lx) {
        final String[] recent_rqlx = {RqlxConst.RECENT7, RqlxConst.RECENT30};
        for (String rqlx : recent_rqlx) {
            delRecent(userid, lx, rqlx);
            delRecentTest(userid, lx, rqlx);
        }
    }

    public void delRecent(String userid, String lx, String rqlx) {
        //根据userid删除recent
        RangeRowQueryCriteria rangeRowQueryCriteria = new RangeRowQueryCriteria(TABLE_NAME);

        //设置起始主键。
        PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
        primaryKeyBuilder.addPrimaryKeyColumn("userid_lx", PrimaryKeyValue.fromString(userid + "_" + lx));
        primaryKeyBuilder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(rqlx));
        primaryKeyBuilder.addPrimaryKeyColumn("itemid", PrimaryKeyValue.INF_MIN);
        primaryKeyBuilder.addPrimaryKeyColumn("hash", PrimaryKeyValue.INF_MIN);
        rangeRowQueryCriteria.setInclusiveStartPrimaryKey(primaryKeyBuilder.build());

        //设置结束主键。
        primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
        primaryKeyBuilder.addPrimaryKeyColumn("userid_lx", PrimaryKeyValue.fromString(userid + "_" + lx));
        primaryKeyBuilder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(rqlx));
        primaryKeyBuilder.addPrimaryKeyColumn("itemid", PrimaryKeyValue.INF_MAX);
        primaryKeyBuilder.addPrimaryKeyColumn("hash", PrimaryKeyValue.INF_MAX);

        rangeRowQueryCriteria.setExclusiveEndPrimaryKey(primaryKeyBuilder.build());
        rangeRowQueryCriteria.setMaxVersions(1);

        while (true) {
            GetRangeResponse getRangeResponse = client.getRange(new GetRangeRequest(rangeRowQueryCriteria));
            delRow(getRangeResponse.getRows());
            //如果NextStartPrimaryKey不为null，则继续读取。
            if (getRangeResponse.getNextStartPrimaryKey() != null) {
                rangeRowQueryCriteria.setInclusiveStartPrimaryKey(getRangeResponse.getNextStartPrimaryKey());
            } else {
                break;
            }
        }
    }


    public void delRecentTest(String userid, String lx, String rqlx) {
        //根据userid删除recent
        RangeRowQueryCriteria rangeRowQueryCriteria = new RangeRowQueryCriteria(TABLE_NAME);

        //设置起始主键。
        PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
        primaryKeyBuilder.addPrimaryKeyColumn("userid_lx", PrimaryKeyValue.fromString(userid + "_" + lx));
        primaryKeyBuilder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(rqlx));
        primaryKeyBuilder.addPrimaryKeyColumn("itemid", PrimaryKeyValue.INF_MIN);
        primaryKeyBuilder.addPrimaryKeyColumn("hash", PrimaryKeyValue.INF_MIN);
        rangeRowQueryCriteria.setInclusiveStartPrimaryKey(primaryKeyBuilder.build());

        //设置结束主键。
        primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
        primaryKeyBuilder.addPrimaryKeyColumn("userid_lx", PrimaryKeyValue.fromString(userid + "_" + lx));
        primaryKeyBuilder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(rqlx));
        primaryKeyBuilder.addPrimaryKeyColumn("itemid", PrimaryKeyValue.INF_MAX);
        primaryKeyBuilder.addPrimaryKeyColumn("hash", PrimaryKeyValue.INF_MAX);

        rangeRowQueryCriteria.setExclusiveEndPrimaryKey(primaryKeyBuilder.build());
        rangeRowQueryCriteria.setMaxVersions(1);

        while (true) {
            GetRangeResponse getRangeResponse = clientTest.getRange(new GetRangeRequest(rangeRowQueryCriteria));
            delRow(getRangeResponse.getRows());
            //如果NextStartPrimaryKey不为null，则继续读取。
            if (getRangeResponse.getNextStartPrimaryKey() != null) {
                rangeRowQueryCriteria.setInclusiveStartPrimaryKey(getRangeResponse.getNextStartPrimaryKey());
            } else {
                break;
            }
        }
    }

    public void delRow(List<Row> rows) {
        List<List<Row>> partition = Lists.partition(rows, 200);
        for (List<Row> rowList : partition) {
            BatchWriteRowRequest batchWriteRowRequest = new BatchWriteRowRequest();

            for (Row row : rowList) {
                PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();

                for (PrimaryKeyColumn primaryKeyColumn : row.getPrimaryKey().getPrimaryKeyColumns()) {
                    primaryKeyBuilder.addPrimaryKeyColumn(primaryKeyColumn.getName(), primaryKeyColumn.getValue());
                }

                RowDeleteChange rowDeleteChange = new RowDeleteChange(TABLE_NAME, primaryKeyBuilder.build());
                batchWriteRowRequest.addRowChange(rowDeleteChange);
            }

            BatchWriteRowResponse response = client.batchWriteRow(batchWriteRowRequest);
            int cscs = 0;
            while (cscs < 3 && !response.isAllSucceed()) {
                BatchWriteRowRequest retryRequest = batchWriteRowRequest.createRequestForRetry(response.getFailedRows());
                response = client.batchWriteRow(retryRequest);
                cscs++;
            }
        }
    }
}
