package com.abujlb.dataprobitaskfbp.tsdao;

import com.abujlb.dataprobitaskfbp.bean.JdFbpSpCate;
import com.abujlb.dataprobitaskfbp.bean.JdSpInit;
import com.abujlb.dataprobitaskfbp.bean.Sp;
import com.abujlb.util.Md5;
import com.alicloud.openservices.tablestore.SyncClient;
import com.alicloud.openservices.tablestore.model.*;
import com.alicloud.openservices.tablestore.model.search.SearchQuery;
import com.alicloud.openservices.tablestore.model.search.SearchRequest;
import com.alicloud.openservices.tablestore.model.search.SearchResponse;
import com.alicloud.openservices.tablestore.model.search.query.BoolQuery;
import com.alicloud.openservices.tablestore.model.search.query.TermQuery;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2022/5/24 8:55
 */
@Component
public class SpNewTsDao {

    private static final String TABLE_NAME = "spnew";

    public static final String COLUMN_TITLE = "title";
    public static final String COLUMN_IMG = "img";
    public static final String COLUMN_YJLMID = "oneLmid";
    public static final String COLUMN_CID = "cid";
    public static final String COLUMN_SPUID = "spuid";

    private SyncClient client = null;
    private static final Sp DEFAULT_SP = new Sp();

    private static final String PK_1 = "md516_yhid_qd_userid_bbid";//md5_16(yhid_qd_userid_bbid)
    private static final String PK_2 = "yhid";
    @Autowired
    private TsDaoUtil tsDaoUtil;

    @PostConstruct
    public void init() {
        client = tsDaoUtil.getClient();
    }


    public Sp getSp(int yhid, String qd, String userid, String bbid) {
        try {
            PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
            String key = yhid + "_" + qd + "_" + userid + "_" + bbid;
            primaryKeyBuilder.addPrimaryKeyColumn(PK_1, PrimaryKeyValue.fromString(Md5.md516(key) + "_" + key));
            primaryKeyBuilder.addPrimaryKeyColumn(PK_2, PrimaryKeyValue.fromLong(yhid));
            PrimaryKey primaryKey = primaryKeyBuilder.build();
            SingleRowQueryCriteria criteria = new SingleRowQueryCriteria(TABLE_NAME, primaryKey);
            criteria.setMaxVersions(1);
            criteria.addColumnsToGet(Arrays.asList(COLUMN_TITLE, COLUMN_IMG, COLUMN_YJLMID, COLUMN_CID, COLUMN_SPUID));
            GetRowResponse getRowResponse = client.getRow(new GetRowRequest(criteria));
            Row row = getRowResponse.getRow();
            if (row != null) {
                Sp sp = new Sp();
                if (row.getLatestColumn(COLUMN_YJLMID) != null && row.getLatestColumn(COLUMN_YJLMID).getValue() != null) {
                    sp.setTid(row.getLatestColumn(COLUMN_YJLMID).getValue().asString());
                }
                if (row.getLatestColumn(COLUMN_CID) != null && row.getLatestColumn(COLUMN_CID).getValue() != null) {
                    sp.setCid(row.getLatestColumn(COLUMN_CID).getValue().asString());
                }
                if (row.getLatestColumn(COLUMN_SPUID) != null && row.getLatestColumn(COLUMN_SPUID).getValue() != null) {
                    sp.setSpuid(row.getLatestColumn(COLUMN_SPUID).getValue().asString());
                }
                return sp;
            }
        } catch (Exception e) {
        }
        return null;
    }

    public Sp getSpBySpuid(String spuid) {
        SearchQuery searchQuery = new SearchQuery();
        TermQuery spuidQuery = new TermQuery(); //设置查询类型为TermQuery。
        spuidQuery.setFieldName("spuid"); //设置要匹配的字段。
        spuidQuery.setTerm(ColumnValue.fromString(spuid)); //设置要匹配的值。

        BoolQuery boolQuery = new BoolQuery();
        boolQuery.setMustQueries(Collections.singletonList(spuidQuery));
        searchQuery.setQuery(boolQuery);
        searchQuery.setLimit(100);
        SearchRequest searchRequest = new SearchRequest(TABLE_NAME, "spnew_index", searchQuery);

        SearchRequest.ColumnsToGet columnsToGet = new SearchRequest.ColumnsToGet();
        columnsToGet.setColumns(Arrays.asList(COLUMN_TITLE, COLUMN_IMG, COLUMN_YJLMID, COLUMN_CID, COLUMN_SPUID));
        searchRequest.setColumnsToGet(columnsToGet);

        SearchResponse resp = client.search(searchRequest);
        if (resp.isAllSuccess()) {
            for (Row row : resp.getRows()) {
                Sp sp = new Sp();
                if (row.getLatestColumn(COLUMN_YJLMID) != null && row.getLatestColumn(COLUMN_YJLMID).getValue() != null) {
                    sp.setTid(row.getLatestColumn(COLUMN_YJLMID).getValue().asString());
                }
                if (row.getLatestColumn(COLUMN_CID) != null && row.getLatestColumn(COLUMN_CID).getValue() != null) {
                    sp.setCid(row.getLatestColumn(COLUMN_CID).getValue().asString());
                }
                if (row.getLatestColumn(COLUMN_SPUID) != null && row.getLatestColumn(COLUMN_SPUID).getValue() != null) {
                    sp.setSpuid(row.getLatestColumn(COLUMN_SPUID).getValue().asString());
                }
                return sp;
            }
        }
        return null;
    }


    public void updateJdFbpSpCate(List<JdFbpSpCate> jdFbpSpCates, int yhid, String qd, String userid) {
        List<List<JdFbpSpCate>> partition = Lists.partition(jdFbpSpCates, 200);
        for (List<JdFbpSpCate> tempList : partition) {
            updateJdFbpSpCate2(tempList, yhid, qd, userid);
        }
    }

    private void updateJdFbpSpCate2(List<JdFbpSpCate> tempList, int yhid, String qd, String userid) {
        BatchWriteRowRequest batchWriteRowRequest = new BatchWriteRowRequest();
        for (JdFbpSpCate jdFbpSpCate : tempList) {
            if (jdFbpSpCate.getBbid() == 0) {
                continue;
            }

            PrimaryKeyBuilder pk1Builder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
            pk1Builder.addPrimaryKeyColumn("yhid_qd_userid_bbid", PrimaryKeyValue.fromString(yhid + "_" + qd + "_" + userid + "_" + jdFbpSpCate.getBbid()));
            RowUpdateChange rowUpdateChange = new RowUpdateChange(TABLE_NAME, pk1Builder.build());
            rowUpdateChange.put("qd_userid", ColumnValue.fromString(qd + "_" + userid));
            if (jdFbpSpCate.getSpuid() != 0) {
                rowUpdateChange.put("spuid", ColumnValue.fromString(String.valueOf(jdFbpSpCate.getSpuid())));
            }
            if (StringUtils.isNotBlank(jdFbpSpCate.getSpmc())) {
                rowUpdateChange.put("title", ColumnValue.fromString(String.valueOf(jdFbpSpCate.getSpmc())));
                rowUpdateChange.put("title2", ColumnValue.fromString(String.valueOf(jdFbpSpCate.getSpmc()).toLowerCase()));
            }
            if (jdFbpSpCate.getSjzt() != null && jdFbpSpCate.getSjzt().equals("上柜")) {
                rowUpdateChange.put("sfzs", ColumnValue.fromLong(1));
            } else if (jdFbpSpCate.getSjzt() != null && jdFbpSpCate.getSjzt().equals("下柜")) {
                rowUpdateChange.put("sfzs", ColumnValue.fromLong(0));
            } else if (jdFbpSpCate.getSjzt() != null && jdFbpSpCate.getSjzt().equals("可上柜")) {
                rowUpdateChange.put("sfzs", ColumnValue.fromLong(0));
            }

            if (StringUtils.isNotBlank(jdFbpSpCate.getSjsj())) {
                rowUpdateChange.put("sjsj", ColumnValue.fromString(jdFbpSpCate.getSjsj()));
            }
            if (StringUtils.isNotBlank(jdFbpSpCate.getCjsj())) {
                rowUpdateChange.put("cjsj", ColumnValue.fromString(jdFbpSpCate.getCjsj()));
            }

            rowUpdateChange.put("oneLmid", ColumnValue.fromString(jdFbpSpCate.getOneLmid() == null ? "" : jdFbpSpCate.getOneLmid()));
            rowUpdateChange.put("twoLmid", ColumnValue.fromString(jdFbpSpCate.getTwoLmid() == null ? "" : jdFbpSpCate.getTwoLmid()));
            rowUpdateChange.put("threeLmid", ColumnValue.fromString(jdFbpSpCate.getThreeLmid() == null ? "" : jdFbpSpCate.getThreeLmid()));
            rowUpdateChange.put("cid", ColumnValue.fromString(jdFbpSpCate.getCid() == null ? "" : jdFbpSpCate.getCid()));
            rowUpdateChange.put("lmids", ColumnValue.fromString(jdFbpSpCate.getLmids() == null ? "" : jdFbpSpCate.getLmids()));

            rowUpdateChange.put("onelmmc", ColumnValue.fromString(jdFbpSpCate.getOnelmmc() == null ? "" : jdFbpSpCate.getOnelmmc()));
            rowUpdateChange.put("twolmmc", ColumnValue.fromString(jdFbpSpCate.getTwolmmc() == null ? "" : jdFbpSpCate.getTwolmmc()));
            rowUpdateChange.put("threelmmc", ColumnValue.fromString(jdFbpSpCate.getThreelmmc() == null ? "" : jdFbpSpCate.getThreelmmc()));
            rowUpdateChange.put("cidmc", ColumnValue.fromString(jdFbpSpCate.getCidmc() == null ? "" : jdFbpSpCate.getCidmc()));
            rowUpdateChange.put("lmmcs", ColumnValue.fromString(jdFbpSpCate.getLmmcs() == null ? "" : jdFbpSpCate.getLmmcs()));

            rowUpdateChange.put("bbid", ColumnValue.fromString(String.valueOf(jdFbpSpCate.getBbid())));
            rowUpdateChange.put("qd", ColumnValue.fromString(qd));
            rowUpdateChange.put("userid", ColumnValue.fromString(userid));
            rowUpdateChange.put("yhid", ColumnValue.fromLong(yhid));

            batchWriteRowRequest.addRowChange(rowUpdateChange);
        }

        int maxRetryTime = 0;
        BatchWriteRowRequest retryRequest = null;
        while (maxRetryTime < 10) {
            BatchWriteRowResponse batchWriteRowResponse = client.batchWriteRow(retryRequest == null ? batchWriteRowRequest : retryRequest);
            if (batchWriteRowResponse.isAllSucceed()) {
                break;
            }

            retryRequest = batchWriteRowRequest.createRequestForRetry(batchWriteRowResponse.getFailedRows());
            maxRetryTime++;
        }
    }

    public void initJdSp(List<JdSpInit> list) {
        List<List<JdSpInit>> listList = Lists.partition(list, 100);
        for (List<JdSpInit> tempList : listList) {
            initJdSp2(tempList);
        }
    }


    private void initJdSp2(List<JdSpInit> list) {
        BatchWriteRowRequest batchWriteRowRequest = new BatchWriteRowRequest();

        for (JdSpInit spInit : list) {
            if (StringUtils.isBlank(spInit.getBbid())) {
                continue;
            }
            if (StringUtils.isBlank(spInit.getSjsj()) && StringUtils.isBlank(spInit.getXjsj())
                    && StringUtils.isBlank(spInit.getCjsj())) {
                continue;
            }

            PrimaryKeyBuilder pk1Builder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
            String key = spInit.getYhid() + "_" + spInit.getQd() + "_" + spInit.getUserid() + "_" + spInit.getBbid();
            pk1Builder.addPrimaryKeyColumn(PK_1, PrimaryKeyValue.fromString(Md5.md516(key) + "_" + key));
            pk1Builder.addPrimaryKeyColumn(PK_2, PrimaryKeyValue.fromLong(spInit.getYhid()));
            RowUpdateChange rowUpdateChange = new RowUpdateChange(TABLE_NAME, pk1Builder.build());
            rowUpdateChange.put("qd_userid", ColumnValue.fromString(spInit.getQd() + "_" + spInit.getUserid()));
            if (StringUtils.isNotBlank(spInit.getCjsj())) {
                rowUpdateChange.put("cjsj", ColumnValue.fromString(spInit.getCjsj()));
            }

            if (StringUtils.isNotBlank(spInit.getSjsj())) {
                rowUpdateChange.put("sjsj", ColumnValue.fromString(spInit.getSjsj()));
            }

            if (StringUtils.isNotBlank(spInit.getXjsj())) {
                rowUpdateChange.put("xjsj", ColumnValue.fromString(spInit.getXjsj()));
            }
            batchWriteRowRequest.addRowChange(rowUpdateChange);
        }


        int maxRetryTime = 0;
        BatchWriteRowRequest retryRequest = null;
        while (maxRetryTime < 10) {
            BatchWriteRowResponse batchWriteRowResponse = client.batchWriteRow(retryRequest == null ? batchWriteRowRequest : retryRequest);
            if (batchWriteRowResponse.isAllSucceed()) {
                break;
            }

            retryRequest = batchWriteRowRequest.createRequestForRetry(batchWriteRowResponse.getFailedRows());
            maxRetryTime++;
        }
    }

    public Map<String, String> initJdSpSpuid(String userid) {
        Map<String, String> map = new HashMap<>();

        SearchQuery searchQuery = new SearchQuery();
        TermQuery useridQuery = new TermQuery(); //设置查询类型为TermQuery。
        useridQuery.setFieldName("userid"); //设置要匹配的字段。
        useridQuery.setTerm(ColumnValue.fromString(userid)); //设置要匹配的值。

        BoolQuery boolQuery = new BoolQuery();
        boolQuery.setMustQueries(Collections.singletonList(useridQuery));
        searchQuery.setQuery(boolQuery);
        searchQuery.setLimit(200);

        SearchRequest searchRequest = new SearchRequest(TABLE_NAME, "spnew_index", searchQuery);
        SearchRequest.ColumnsToGet columnsToGet = new SearchRequest.ColumnsToGet();
        columnsToGet.setColumns(Arrays.asList("bbid", "spuid"));
        searchRequest.setColumnsToGet(columnsToGet);

        while (true) {
            SearchResponse resp = client.search(searchRequest);

            for (Row row : resp.getRows()) {
                String bbid = null;
                if (row.getLatestColumn("bbid") != null && row.getLatestColumn("bbid").getValue() != null) {
                    bbid = row.getLatestColumn("bbid").getValue().asString();
                }
                if (StringUtils.isBlank(bbid)) {
                    continue;
                }
                String spuid = null;
                if (row.getLatestColumn("spuid") != null && row.getLatestColumn("spuid").getValue() != null) {
                    spuid = row.getLatestColumn("spuid").getValue().asString();
                }
                if (StringUtils.isBlank(spuid)) {
                    continue;
                }
                map.put(bbid, spuid);
            }
            byte[] next = resp.getNextToken();
            if (next != null) {
                searchRequest.getSearchQuery().setToken(next);
            } else {
                break;
            }
        }
        return map;
    }
}
