package com.abujlb.dataprobitaskfbp.tsdao;


import com.abujlb.dao.v2.TsDao2;
import com.abujlb.dataprobitaskfbp.bean.BiTaskLog;
import com.abujlb.dataprobitaskfbp.constants.BiCacheConst;
import com.abujlb.dataprobitaskfbp.constants.BiConstant;
import com.abujlb.dataprobitaskfbp.utils.DateUtil;
import com.alicloud.openservices.tablestore.SyncClient;
import com.alicloud.openservices.tablestore.model.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2024//5/28
 */
@Component
public class BiTaskLogTsDao {

    private static final Logger LOG = Logger.getLogger(BiTaskLogTsDao.class);
    private static final String TABLE_NAME = "bi_task_log";

    private SyncClient client = null;

    @Autowired
    private TsDao2 tsDao2;

    @PostConstruct
    public void init() {
        client = tsDao2.getClient("abujlb7@jushita").getClient();
    }

    public void initLog(BiTaskLog log) {
        try {
            if (log == null) {
                return;
            }
            // 构造主键
            PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
            primaryKeyBuilder.addPrimaryKeyColumn("yhid_qd_userid", PrimaryKeyValue.fromString(log.getYhid() + "_" + log.getQd() + "_" + log.getUserid()));
            primaryKeyBuilder.addPrimaryKeyColumn("tasktype", PrimaryKeyValue.fromString(log.getTaskType()));
            primaryKeyBuilder.addPrimaryKeyColumn("rq", PrimaryKeyValue.fromString(log.getStart() + "_" + log.getEnd()));
            primaryKeyBuilder.addPrimaryKeyColumn("uuid", PrimaryKeyValue.fromString(StringUtils.isBlank(log.getUuid()) ? UUID.randomUUID().toString() : log.getUuid()));
            PrimaryKey primaryKey = primaryKeyBuilder.build();
            // 设置表名
            RowUpdateChange rowUpdateChange = new RowUpdateChange(TABLE_NAME, primaryKey);
            rowUpdateChange.put(new Column("dpmc", ColumnValue.fromString(log.getDpmc())));
            rowUpdateChange.put(new Column("cjsj", ColumnValue.fromString(DateUtil.getCurrentTime())));
            rowUpdateChange.put(new Column("cjrq", ColumnValue.fromString(DateUtil.getCurrentDate())));
            rowUpdateChange.put(new Column("type", ColumnValue.fromString(BiConstant.TYPE)));
            rowUpdateChange.put(new Column("status", ColumnValue.fromLong(0)));
            rowUpdateChange.put(new Column("yhid", ColumnValue.fromLong(log.getYhid())));
            rowUpdateChange.put(new Column("qd", ColumnValue.fromString(log.getQd())));
            rowUpdateChange.put(new Column("userid", ColumnValue.fromString(log.getUserid())));
            // 更新
            client.updateRow(new UpdateRowRequest(rowUpdateChange));
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
    }


    public void start(BiTaskLog log) {
        try {
            if (log == null) {
                return;
            }
            // 构造主键
            PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
            primaryKeyBuilder.addPrimaryKeyColumn("yhid_qd_userid", PrimaryKeyValue.fromString(log.getYhid() + "_" + log.getQd() + "_" + log.getUserid()));
            primaryKeyBuilder.addPrimaryKeyColumn("tasktype", PrimaryKeyValue.fromString(log.getTaskType()));
            primaryKeyBuilder.addPrimaryKeyColumn("rq", PrimaryKeyValue.fromString(log.getStart() + "_" + log.getEnd()));
            primaryKeyBuilder.addPrimaryKeyColumn("uuid", PrimaryKeyValue.fromString(log.getUuid()));
            PrimaryKey primaryKey = primaryKeyBuilder.build();
            // 设置表名
            RowUpdateChange rowUpdateChange = new RowUpdateChange(TABLE_NAME, primaryKey);
            log.setClstart(DateUtil.getCurrentTime());
            log.setStartTimestamp(System.currentTimeMillis());
            log.setStatus(1);
            rowUpdateChange.put(new Column("dpmc", ColumnValue.fromString(log.getDpmc())));
            rowUpdateChange.put(new Column("clstart", ColumnValue.fromString(log.getClstart())));
            rowUpdateChange.put(new Column("status", ColumnValue.fromLong(log.getStatus())));
            rowUpdateChange.put(new Column("ip", ColumnValue.fromString(BiCacheConst.getStr(BiConstant.IP))));
            // 更新
            client.updateRow(new UpdateRowRequest(rowUpdateChange));
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
    }

    public void end(BiTaskLog log) {
        try {
            if (log == null) {
                return;
            }
            // 构造主键
            PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
            primaryKeyBuilder.addPrimaryKeyColumn("yhid_qd_userid", PrimaryKeyValue.fromString(log.getYhid() + "_" + log.getQd() + "_" + log.getUserid()));
            primaryKeyBuilder.addPrimaryKeyColumn("tasktype", PrimaryKeyValue.fromString(log.getTaskType()));
            primaryKeyBuilder.addPrimaryKeyColumn("rq", PrimaryKeyValue.fromString(log.getStart() + "_" + log.getEnd()));
            primaryKeyBuilder.addPrimaryKeyColumn("uuid", PrimaryKeyValue.fromString(log.getUuid()));
            PrimaryKey primaryKey = primaryKeyBuilder.build();
            // 设置表名
            RowUpdateChange rowUpdateChange = new RowUpdateChange(TABLE_NAME, primaryKey);
            log.setClend(DateUtil.getCurrentTime());
            log.setEndTimestamp(System.currentTimeMillis());
            log.setStatus(2);
            rowUpdateChange.put(new Column("clend", ColumnValue.fromString(log.getClend())));
            rowUpdateChange.put(new Column("status", ColumnValue.fromLong(log.getStatus())));
            int seconds = (int) ((log.getEndTimestamp() - log.getStartTimestamp()) / 1000);
            int millseconds = (int) (log.getEndTimestamp() - log.getStartTimestamp());
            rowUpdateChange.put(new Column("seconds", ColumnValue.fromString(seconds == 0 ? (millseconds + "毫秒") : (seconds + "秒"))));
            // 更新
            client.updateRow(new UpdateRowRequest(rowUpdateChange));
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
    }
}
