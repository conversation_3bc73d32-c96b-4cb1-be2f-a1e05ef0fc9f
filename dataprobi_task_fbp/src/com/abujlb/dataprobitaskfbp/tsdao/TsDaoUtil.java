package com.abujlb.dataprobitaskfbp.tsdao;

import com.abujlb.CommonConfig;
import com.abujlb.dao.TsDao;
import com.abujlb.dao.v2.TsDao2;
import com.alicloud.openservices.tablestore.SyncClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/4/5 9:49
 */
@Component
public class TsDaoUtil {

    public SyncClient client = null;
    public SyncClient abujlb7client = null;
    @Autowired
    private TsDao tsDao;
    @Autowired
    private TsDao2 tsDao2;

    public SyncClient getClient() {
        if (client != null) {
            return client;
        }
        if (CommonConfig.getInteger("jst") == 1) {
            client = tsDao.getClient("abujlb6");
        } else if (CommonConfig.getInteger("jst") == 2) {
            client = tsDao2.getClient("abujlb6@jushita").getClient();
        }
        return client;
    }


    public SyncClient getClientAbujlb7() {
        if (abujlb7client != null) {
            return abujlb7client;
        }
        abujlb7client = tsDao2.getClient("abujlb7@jushita").getClient();
        return abujlb7client;
    }

}
