//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.abujlb.dataprobitaskfbp.oss;

import com.abujlb.dao.v2.OssDao2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.List;

@Component
public class BiDataOssUtil {

    public static final String ABUJLBJSON = "abujlbjson";
    @Autowired
    private BiDataOss biDataOss;
    @Autowired
    private OssDao2 ossDao2;

    public void upload(String str, String key) {
        ossDao2.getClient("ecbisjson2@jushita").upload(str, key);
    }

    public void upload(File file, String key) {
        if (file == null || !file.exists()) {
            return;
        }
        ossDao2.getClient("ecbisjson2@jushita").upload(file, key);
    }

    public void upload(byte[] byteArray, String key) {
        ossDao2.getClient("ecbisjson2@jushita").upload(byteArray, key);
    }

    public void download(String key, String path) {
        ossDao2.getClient("ecbisjson2@jushita").download(key, path);
    }

    public boolean exist(String key) {
        return ossDao2.getClient("ecbisjson2@jushita").exist(key);
    }

    public List<String> listFiles(String prefix) {
        return biDataOss.listFiles(ossDao2.getClient("ecbisjson2@jushita"), prefix);
    }

    public String readJson(String ossKey) {
        if (exist(ossKey)) {
            return biDataOss.readJson(ossDao2.getClient("ecbisjson2@jushita"), ossKey);
        }
        return null;
    }

    public void delete(String key) {
        ossDao2.getClient("ecbisjson2@jushita").delete(key);
    }


    public void uploadSpecificOne(String str, String key, String name) {
        ossDao2.getClient(name).upload(str, key);
    }

    public void uploadSpecificOne(File file, String key, String name) {
        ossDao2.getClient(name).upload(file, key);
    }

    public void uploadSpecificOne(byte[] byteArray, String key, String name) {
        ossDao2.getClient(name).upload(byteArray, key);
    }

    public void downloadSpecificOne(String key, String path, String name) {
        ossDao2.getClient(name).download(key, path);
    }

    public boolean existSpecificOne(String key, String name) {
        return ossDao2.getClient(name).exist(key);
    }
}
