package com.abujlb.dataprobitaskfbp.oss;

import com.abujlb.Config;
import com.abujlb.dao.v2.oss.Oss2Client;
import com.abujlb.sts.StsResult;
import com.abujlb.sts.StsStore;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.model.*;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/16 16:02
 */
@Component
public class BiDataOss {

    private static final Logger logger = Logger.getLogger(BiDataOss.class);

    @Autowired
    private Config config;
    @Autowired
    private StsStore stsStore;

    private String accessKeyId;
    private String accessKeySecret;
    private String endpoint;
    private String authbucketName;
    private String jstbucketName;
    private String authdomain;

    public BiDataOss() {
    }

    @PostConstruct
    public void init() {
        this.accessKeyId = this.config.getString("accesskeyid");
        this.accessKeySecret = this.config.getString("accesskeysecret");
        this.endpoint = this.config.getString("oss-endpoint");
        this.authdomain = this.config.getString("auth-oss-domain");
        this.authbucketName = "ecbisjson";
        this.jstbucketName = "ecbisjson2";
    }


    public OSSClient createClient() {
        if (this.accessKeyId != null && this.accessKeySecret != null) {
            return new OSSClient(this.endpoint, this.accessKeyId, this.accessKeySecret);
        } else {
            StsResult stsRes = this.stsStore.getResult();
            return new OSSClient(this.endpoint, stsRes.getAccessKeyId(), stsRes.getAccessKeySecret(), stsRes.getSecurityToken());
        }
    }

    public String uploadauth(File file, String key) {
        OSSClient client = null;
        try {
            client = this.createClient();
            client.putObject(this.authbucketName, key, file);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        } finally {
            if (client != null) {
                client.shutdown();
            }
        }
        return this.authdomain + key;
    }


    public String uploadauth(String str, String key) {
        try {
            byte[] byteArray = str.getBytes(StandardCharsets.UTF_8);
            return this.uploadauth(byteArray, key);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return null;
        }
    }

    public String uploadauth(byte[] byteArray, String key) {
        OSSClient client = null;
        try {
            client = this.createClient();
            client.putObject(this.authbucketName, key, new ByteArrayInputStream(byteArray));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        } finally {
            if (client != null) {
                client.shutdown();
            }
        }
        return this.authdomain + key;
    }

    public void downloadauth(String key, String path) {
        OSSClient client = null;

        try {
            client = this.createClient();
            client.getObject(new GetObjectRequest(this.authbucketName, key), new File(path));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        } finally {
            if (client != null) {
                client.shutdown();
            }
        }

    }

    public boolean existAuth(String key) {
        OSSClient client = null;

        try {
            client = this.createClient();
            return client.doesObjectExist(this.authbucketName, key);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        } finally {
            if (client != null) {
                client.shutdown();
            }
        }

        return false;
    }

    public List<String> listFiles(String prefix) {
        List<String> keys = new ArrayList<>();
        OSSClient client = null;
        try {
            client = this.createClient();
            String nextMarker = null;
            ObjectListing objectListing;
            do {
                objectListing = client.listObjects(new ListObjectsRequest(this.authbucketName).withMarker(nextMarker).withMaxKeys(500).withPrefix(prefix));

                List<OSSObjectSummary> sums = objectListing.getObjectSummaries();
                for (OSSObjectSummary s : sums) {
                    keys.add(s.getKey());
                }

                nextMarker = objectListing.getNextMarker();

            } while (objectListing.isTruncated());

            return keys;
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        } finally {
            if (client != null) {
                client.shutdown();
            }
        }
        return null;
    }


    public List<String> listFiles(Oss2Client oss2Client, String prefix) {
        List<String> keys = new ArrayList<>();
        try {
            String nextMarker = null;
            ObjectListing objectListing;
            do {
                objectListing = oss2Client
                        .getOSSClient()
                        .listObjects(new ListObjectsRequest(this.jstbucketName).withMarker(nextMarker).withMaxKeys(500).withPrefix(prefix));

                List<OSSObjectSummary> sums = objectListing.getObjectSummaries();
                for (OSSObjectSummary s : sums) {
                    keys.add(s.getKey());
                }

                nextMarker = objectListing.getNextMarker();

            } while (objectListing.isTruncated());

            return keys;
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return null;
    }

    public String readJson(String fullPath) {
        OSSClient client = null;
        OSSObject ossObject = null;
        BufferedReader reader = null;
        StringBuilder sb = null;
        try {
            client = this.createClient();
            ossObject = client.getObject(this.authbucketName, fullPath);

            reader = new BufferedReader(new InputStreamReader(ossObject.getObjectContent()));
            while (true) {
                String line = reader.readLine();
                if (line == null) break;
                if (sb == null) {
                    sb = new StringBuilder();
                }
                sb.append(line);
            }
            return sb == null ? null : sb.toString();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        } finally {
            if (reader != null) {
                try {
                    reader.close();
                } catch (IOException e) {
                    logger.error(e.getMessage(), e);
                }
            }
            if (client != null) {
                client.shutdown();
            }
        }
        return null;
    }

    public String readJson(Oss2Client oss2Client ,String fullPath) {
        OSSObject ossObject = null;
        BufferedReader reader = null;
        StringBuilder sb = null;
        try {
            ossObject = oss2Client.getOSSClient().getObject(this.jstbucketName, fullPath);

            reader = new BufferedReader(new InputStreamReader(ossObject.getObjectContent()));
            while (true) {
                String line = reader.readLine();
                if (line == null) break;
                if (sb == null) {
                    sb = new StringBuilder();
                }
                sb.append(line);
            }
            return sb == null ? null : sb.toString();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        } finally {
            if (reader != null) {
                try {
                    reader.close();
                } catch (IOException e) {
                    logger.error(e.getMessage(), e);
                }
            }
        }
        return null;
    }


    public byte[] readBytes(String fullPath) {
        OSSClient client = null;
        OSSObject ossObject = null;
        ByteArrayOutputStream byteArrayOutputStream = null;
        InputStream inputStream = null;
        try {
            client = this.createClient();
            ossObject = client.getObject(this.authbucketName, fullPath);

            inputStream = ossObject.getObjectContent();
            byteArrayOutputStream = new ByteArrayOutputStream();
            byte[] temp = new byte[1024];
            int len = 0;
            while ((len = inputStream.read(temp)) != -1) {
                byteArrayOutputStream.write(temp);
            }
            return byteArrayOutputStream.toByteArray();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        } finally {
            if (byteArrayOutputStream != null) {
                try {
                    byteArrayOutputStream.close();
                } catch (IOException e) {
                    logger.error(e.getMessage(), e);
                }
            }
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    logger.error(e.getMessage(), e);
                }
            }
            if (client != null) {
                client.shutdown();
            }
        }
        return null;
    }


    public void deleteAuth(String key) {
        OSSClient client = null;

        try {
            client = this.createClient();
            client.deleteObject(this.authbucketName, key);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        } finally {
            if (client != null) {
                client.shutdown();
            }
        }
    }

}
