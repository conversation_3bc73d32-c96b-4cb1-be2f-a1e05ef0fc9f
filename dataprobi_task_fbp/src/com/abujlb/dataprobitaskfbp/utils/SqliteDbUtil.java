package com.abujlb.dataprobitaskfbp.utils;

import com.abujlb.dataprobitaskfbp.annotations.SqliteColumn;
import com.abujlb.dataprobitaskfbp.annotations.Table;
import com.abujlb.dataprobitaskfbp.bean.SqliteSp;
import com.abujlb.dataprobitaskfbp.oss.BiDataOssUtil;
import com.abujlb.dataprobitaskfbp.sqlite.BiSqliteDb;
import com.abujlb.sqlite.SqliteStatement;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.lang.reflect.Field;
import java.sql.Statement;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/3/22 10:29
 */
@Component
public class SqliteDbUtil {

    public static final Logger logger = Logger.getLogger(SqliteDbUtil.class);

    @Autowired
    private BiDataOssUtil biDataOssUtil;

    public static void closeStatement(Statement statement) {
        try {
            if (statement != null) {
                statement.close();
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
    }

    public static void closeSqliteStatements(SqliteStatement<?>... statements) {
        try {
            if (statements != null) {
                for (SqliteStatement<?> statement : statements) {
                    if (statement != null) {
                        statement.close();
                    }
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
    }

    public static void closeStatements(Statement... statements) {
        try {
            if (statements != null) {
                for (Statement statement : statements) {
                    statement.close();
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
    }

    public static void close(BiSqliteDb sqliteDb) {
        try {
            if (sqliteDb != null) {
                sqliteDb.close();
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        } finally {
            if (sqliteDb != null) {
                try {
                    File file = new File(sqliteDb.getFile());
                    if (file.exists()) {
                        boolean flag = file.delete();
                        if (!flag) {
                            logger.info("文件删除失败，路径：" + sqliteDb.getFile());
                        }
                    }
                } catch (Exception e) {
                    logger.error(e.getMessage(), e);
                } finally {
                    sqliteDb = null;
                }
            }
        }

    }


    public static void executeSql(BiSqliteDb sqliteDb, String sql) {
        Statement statement = null;
        try {
            statement = sqliteDb.createStatement();
            statement.execute(sql);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        } finally {
            if (statement != null) {
                try {
                    statement.close();
                } catch (Exception e) {
                    logger.error(e.getMessage(), e);
                }
            }
        }
    }

    public static void executeSqls(BiSqliteDb sqliteDb, List<String> sqls) {
        Statement statement = null;
        try {
            statement = sqliteDb.createStatement();
            for (String sql : sqls) {
                statement.addBatch(sql);
            }
            statement.executeBatch();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        } finally {
            if (statement != null) {
                try {
                    statement.close();
                } catch (Exception e) {
                    logger.error(e.getMessage(), e);
                }
            }
        }
    }


    public static String beanToInitialSql(Class<?> c) {
        try {
            Table table = c.getAnnotation(Table.class);
            if (table == null) {
                return null;
            }
            String tableName = table.value();
            StringBuilder initialSql = new StringBuilder();
            initialSql.append("CREATE TABLE '").append(tableName).append("' ( ");
            Field[] fields = c.getDeclaredFields();
            for (int i = 0; i < fields.length; i++) {
                Field field = fields[i];
                SqliteColumn column = field.getAnnotation(SqliteColumn.class);
                if (column != null) {
                    initialSql.append("'").append(StringUtils.isBlank(column.name()) ? field.getName() : column.name()).append("'  ").append(column.type()).append("  ").append(column.length() == 0 ? "" : ("(" + column.length() + ")")).append(" , ");
                }
            }
            initialSql.append(" ) ;");
            String sql = initialSql.toString();
            return sql.substring(0, sql.lastIndexOf(",")) + sql.substring(initialSql.lastIndexOf(",") + 1);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return null;
    }

    public static <T> String beanToInsertSql(Class<T> tClass, T t) {
        try {
            Table table = tClass.getAnnotation(Table.class);
            if (table == null) {
                return null;
            }
            String tableName = table.value();
            StringBuilder insertSql = new StringBuilder();
            insertSql.append("insert into  '").append(tableName).append("' ( ");
            Field[] fields = tClass.getDeclaredFields();
            for (int i = 0; i < fields.length; i++) {
                Field field = fields[i];
                SqliteColumn column = field.getAnnotation(SqliteColumn.class);
                if (column != null && column.insertable()) {
                    insertSql.append(field.getName()).append(",");
                }
            }
            insertSql.deleteCharAt(insertSql.lastIndexOf(","));
            insertSql.append(" ) values(");

            for (int i = 0; i < fields.length; i++) {
                Field field = fields[i];
                SqliteColumn column = field.getAnnotation(SqliteColumn.class);
                if (column != null && column.insertable()) {
                    Object value = tClass.getMethod("get" + StringUtil.strFirstCharUpper(field.getName())).invoke(t);
                    if (value != null) {
                        if (value instanceof String) {
                            insertSql.append("'").append(value).append("',");
                        } else {
                            insertSql.append(value).append(",");
                        }
                    } else {
                        insertSql.append("''").append(",");
                    }
                }
            }
            insertSql.deleteCharAt(insertSql.lastIndexOf(","));
            insertSql.append(" ) ;");
            return insertSql.toString();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return null;
    }

    public BiSqliteDb getSpSqliteDb(String key) {
        BiSqliteDb sqliteDb = null;
        String filepath = null;
        try {
            filepath = FileUtil.createDBFilePath();
            if (Objects.isNull(key)) {
                return null;
            }
            if (biDataOssUtil.exist(key)) {
                biDataOssUtil.download(key, filepath);
                sqliteDb = new BiSqliteDb(filepath);
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return sqliteDb;
    }

    public List<SqliteSp> readSpList(String key) {
        BiSqliteDb sqliteDb = null;
        SqliteStatement<SqliteSp> sqliteStatement = null;
        File file = null;
        String filepath = null;
        try {
            if (biDataOssUtil.exist(key)) {
                filepath = FileUtil.createDBFilePath();
                biDataOssUtil.download(key, filepath);

                sqliteDb = new BiSqliteDb(filepath);
                sqliteStatement = sqliteDb.createSqliteStatement("select * from sycm_sp ;", SqliteSp.class);
                return sqliteStatement.queryForList(null, SqliteSp.class);
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        } finally {
            if (sqliteStatement != null) {
                try {
                    sqliteStatement.close();
                } catch (Exception e) {
                    logger.error(e.getMessage(), e);
                }
            }
            if (sqliteDb != null) {
                try {
                    sqliteDb.close();
                } catch (Exception e) {
                    logger.error(e.getMessage(), e);
                }
            }
            if (StringUtils.isNotBlank(filepath)) {
                file = new File(filepath);
                if (file.exists()) {
                    boolean flag = file.delete();
                    if (!flag) {
                        logger.info("文件删除失败，路径：" + filepath);
                    }
                }
            }
        }
        return Collections.emptyList();
    }
}
