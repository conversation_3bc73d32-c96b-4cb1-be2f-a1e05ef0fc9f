package com.abujlb.dataprobitaskfbp.utils;

import com.abujlb.CommonConfig;
import com.abujlb.dataprobitaskfbp.bean.BiYghfdd;
import com.csvreader.CsvReader;
import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.apache.poi.ss.usermodel.Row;

import java.io.*;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;
import java.util.Map;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;

import static com.sun.xml.internal.ws.util.JAXWSUtils.getUUID;

public class RlyqXlsReader {

    private static final Logger log = Logger.getLogger(RlyqXlsReader.class);

    public synchronized static String getRowStr(Row row, int cols, Map<String, Integer> titles, String title) {
        Object index = titles.get(title);
        if (index == null) {
            return null;
        }
        int col = Integer.parseInt(index.toString());
        if (col < 0 || col >= cols) {
            return null;
        }
        return row.getCell(col).getStringCellValue();
    }

    public static List<BiYghfdd> getBiYghfdds(List<File> fileList, String rq) {
        if (CollectionUtils.isEmpty(fileList)) {
            return null;
        }
        FileInputStream fis = null;
        CsvReader cr = null;
        List<BiYghfdd> list = new ArrayList<>();
        for (int i = 0; i < fileList.size(); i++) {
            File file = fileList.get(i);
            try {
                fis = new FileInputStream(file);
                cr = new CsvReader(fis, Charset.forName("GBK"));
                cr.readHeaders();
                while (cr.readRecord()) {
                    String sj = cr.get("订单支付时间");
                    if (sj.startsWith(rq)) {
                        BiYghfdd item = new BiYghfdd();
                        item.setBbid(cr.get("商品ID").replace("\t", ""));
                        item.setYgyjzc(StringToNumber.getDouble(cr.get("预估佣金")));
                        item.setTid(cr.get("淘宝子订单号").replace("\t", ""));
                        list.add(item);
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                if (fis != null) {
                    try {
                        fis.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
                if (cr != null) {
                    cr.close();
                }
                if (file != null && file.exists()) {
                    file.delete();
                }
            }
        }
        return list;
    }

    public static List<File> read(String zipFilePath, String shopid) {
        InputStream is = null;
        FileOutputStream fos = null;
        File file = null;
        List<File> fileList = new ArrayList<>();

        try {
            file = new File(zipFilePath);
            if (!file.exists()) {
                throw new Exception(file.getPath() + "所指文件不存在");
            }
            String destDirPath = CommonConfig.getString("tempdir");
            // 创建压缩文件对象
            ZipFile zipFile = new ZipFile(file);
            // 获取所有条目
            Enumeration<? extends ZipEntry> entries = zipFile.entries();
            while (entries.hasMoreElements()) {
                ZipEntry entry = entries.nextElement();

                // 如果是目录则跳过
                if (entry.isDirectory()) {
                    continue;
                }

                // 构建目标文件的路径
                String path = destDirPath + shopid + "_" + System.currentTimeMillis() + "_temp.csv";
                File targetFile = new File(path);

                // 确保父目录存在
                if (!targetFile.getParentFile().exists()) {
                    targetFile.getParentFile().mkdirs();
                }

                // 创建目标文件
                targetFile.createNewFile();

                // 复制文件内容
                is = zipFile.getInputStream(entry);
                fos = new FileOutputStream(targetFile);

                int len;
                byte[] buf = new byte[1024];
                while ((len = is.read(buf)) != -1) {
                    fos.write(buf, 0, len);
                }
                fos.flush();

                // 关闭流
                if (fos != null) {
                    fos.close();
                }
                if (is != null) {
                    is.close();
                }
                fileList.add(targetFile);
            }

            zipFile.close();
            return fileList; // 返回目标目录或其他合适的对象
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        } finally {
            if (file != null && file.exists()) {
                file.delete();
            }
            if (fos != null) {
                try {
                    fos.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (is != null) {
                try {
                    is.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }

        }
    }

    public static String createZipFilePath() {
        return CommonConfig.getString("tempdir") + getUUID() + ".zip";
    }
}
