package com.abujlb.dataprobitaskfbp.utils;

import com.abujlb.dataprobitaskfbp.bean.BiYghfdd;
import com.abujlb.dataprobitaskfbp.bean.PpxxXlsBean;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.File;
import java.io.InputStream;
import java.nio.file.Files;
import java.util.*;

public class PpxxXlsReader {

    private static final Logger log = Logger.getLogger(PpxxXlsReader.class);

    public static List<PpxxXlsBean> parseXls(File file) {
        List<PpxxXlsBean> list = new ArrayList<PpxxXlsBean>();
        InputStream is = null;
        try {
            is = Files.newInputStream(file.toPath());
            Workbook wb = new XSSFWorkbook(is);
            Sheet sheet = wb.getSheetAt(0);
            Iterator<Row> rows = sheet.rowIterator();
            Map<String, Integer> titles = null;
            boolean start = false;
            while (rows.hasNext()) {
                Row row = rows.next();
                if (!start) {
                    titles = new HashMap<String, Integer>(row.getLastCellNum());
                    for (int i = 0; i < row.getLastCellNum(); i++) {
                        titles.put(row.getCell(i).getStringCellValue(), i);
                    }
                    start = true;
                } else {
                    int cols = row.getPhysicalNumberOfCells();
                    PpxxXlsBean item = new PpxxXlsBean();
                    item.setBbid(getRowStr(row, cols, titles, "商品ID"));
                    item.setFy(getRowDouble(row, 4));
                    list.add(item);
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        } finally {
            try {
                if (is != null) {
                    is.close();
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
            if (file != null && file.exists()) {
                file.delete();
            }
        }
        return list;
    }


    public static List<PpxxXlsBean> parseXlsTB(File file) {
        List<PpxxXlsBean> list = new ArrayList<PpxxXlsBean>();
        InputStream is = null;
        try {
            is = Files.newInputStream(file.toPath());
            Workbook wb = new XSSFWorkbook(is);
            Sheet sheet = wb.getSheetAt(0);
            Iterator<Row> rows = sheet.rowIterator();
            Map<String, Integer> titles = null;
            boolean start = false;
            while (rows.hasNext()) {
                Row row = rows.next();
                if (!start) {
                    titles = new HashMap<String, Integer>(row.getLastCellNum());
                    for (int i = 0; i < row.getLastCellNum(); i++) {
                        titles.put(row.getCell(i).getStringCellValue(), i);
                    }
                    start = true;
                } else {
                    int cols = row.getPhysicalNumberOfCells();
                    PpxxXlsBean item = new PpxxXlsBean();
                    item.setBbid(getRowStr(row, cols, titles, "商品ID"));
                    item.setFy(getRowDouble(row, 4));
                    list.add(item);
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        } finally {
            try {
                if (is != null) {
                    is.close();
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
            if (file != null && file.exists()) {
                file.delete();
            }
        }
        return list;
    }


    public synchronized static String getRowStr(Row row, int cols, Map<String, Integer> titles, String title) {
        Object index = titles.get(title);
        if (index == null) {
            return null;
        }
        int col = Integer.parseInt(index.toString());
        if (col < 0 || col >= cols) {
            return null;
        }
        return row.getCell(col).getStringCellValue();
    }


    public synchronized static double getRowDouble(Row row, int index) {
        Cell cell = row.getCell(index);
        if (cell.getCellType() == Cell.CELL_TYPE_NUMERIC) {
            return cell.getNumericCellValue();
        }
        String str = row.getCell(index).getStringCellValue();
        return StringToNumber.transform(str);
    }

    public static List<BiYghfdd> parseXls2(String temppath, String type) {
        List<BiYghfdd> list = new ArrayList<>();
        InputStream is = null;
        File file = null;
        try {
            file = new File(temppath);
            if (!file.exists()) {
                return null;
            }
            is = Files.newInputStream(file.toPath());
            Workbook wb = new XSSFWorkbook(is);
            Sheet sheet = wb.getSheetAt(0);
            Iterator<Row> rows = sheet.rowIterator();
            Map<String, Integer> titles = null;
            boolean start = false;
            while (rows.hasNext()) {
                Row row = rows.next();
                if (!start) {
                    titles = new HashMap<String, Integer>(row.getLastCellNum());
                    for (int i = 0; i < row.getLastCellNum(); i++) {
                        titles.put(row.getCell(i).getStringCellValue(), i);
                    }
                    start = true;
                } else {
                    int cols = row.getPhysicalNumberOfCells();
                    BiYghfdd item = new BiYghfdd();
                    item.setBbid(getRowStr(row, cols, titles, "商品ID"));
                    item.setYgyjzc(getRowDouble(row, 4));
                    item.setTid(getRowStr(row, cols, titles, "父订单ID"));

                    if (StringUtils.isBlank(item.getBbid()) || StringUtils.isBlank(item.getTid()) || item.getYgyjzc() == 0) {
                        continue;
                    }
                    list.add(item);
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        } finally {
            try {
                if (is != null) {
                    is.close();
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
            if (file != null && file.exists()) {
                file.delete();
            }
        }
        return list;
    }
}
