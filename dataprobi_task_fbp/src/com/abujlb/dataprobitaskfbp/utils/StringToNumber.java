package com.abujlb.dataprobitaskfbp.utils;

/**
 * 字符串转数字
 *
 * <AUTHOR>
 * @date 2020-09-18 10:04:29
 */
public class StringToNumber {

    public static long getLong(String s) {
        if (s == null || s.trim().length() == 0) {
            return 0L;
        }
        try {
            return Long.parseLong(s);
        } catch (Exception e) {
            return 0L;
        }
    }

    public static double getDouble(String s) {
        if (s == null || s.trim().length() == 0) {
            return 0D;
        }
        try {
            return Double.parseDouble(s);
        } catch (Exception e) {
            return 0D;
        }
    }

    public static int getInt(String s) {
        if (s == null || s.trim().length() == 0) {
            return 0;
        }
        try {
            return Integer.parseInt(s);
        } catch (Exception e) {
            return 0;
        }
    }

    public static double transform(String s) {
        if (s == null || s.trim().length() == 0) {
            return 0D;
        }
        try {
            s = s.trim();
            s = s.replaceAll(",", "");
            if (s.matches("\\d+\\.\\d*%|[-+]{0,1}\\d*\\.\\d+%")) {
                String s1 = s.replaceAll("%", "");
                return Double.valueOf(s1) * 0.01;
            }
            if (s.matches("\\d+\\.\\d*|[-+]{0,1}\\d*\\.\\d+")) {
                String s1 = s.replaceAll("%", "");
                return Double.valueOf(s1);
            }
            return Double.parseDouble(s);
        } catch (Exception e) {
            return 0D;
        }
    }

    public static int transformInt(String s) {
        double d = transform(s);
        return (int) d;
    }

    public static void main(String[] args) {
        int d = transformInt("3.9999999");
        System.out.println(d);
    }

    public static long transformLong(String s) {
        double d = transform(s);
        return (long) d;
    }
}
