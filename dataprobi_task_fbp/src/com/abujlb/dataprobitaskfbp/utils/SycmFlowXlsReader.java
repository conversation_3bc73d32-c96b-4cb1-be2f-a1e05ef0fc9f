package com.abujlb.dataprobitaskfbp.utils;

import com.abujlb.dataprobitaskfbp.bean.TxFksBean;
import org.apache.log4j.Logger;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;

import java.io.File;
import java.io.InputStream;
import java.nio.file.Files;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

public class SycmFlowXlsReader extends BaseXlsReader {
    private static final Logger log = Logger.getLogger(SycmFlowXlsReader.class);

    public static void parseXls2(String filePath, String fkslx, Map<String, TxFksBean> map) {
        InputStream inputStream = null;
        File file = null;
        try {
            file = new File(filePath);
            inputStream = Files.newInputStream(file.toPath());
            Workbook workbook = new HSSFWorkbook(inputStream);
            Sheet sheet = workbook.getSheetAt(0);
            Iterator<Row> rows = sheet.rowIterator();
            Map<String, Integer> titles = null;
            boolean start = false;
            while (rows.hasNext()) {
                Row row = rows.next();
                String title = row.getCell(0).getStringCellValue();
                if (!start && title.equals("统计日期")) {
                    titles = new HashMap<String, Integer>(row.getLastCellNum());
                    for (int i = 0; i < row.getLastCellNum(); i++) {
                        titles.put(row.getCell(i).getStringCellValue(), i);
                    }
                    start = true;
                    continue;
                }
                if (start) {
                    int cols = row.getPhysicalNumberOfCells();
                    String bbid = getRowStr(row, cols, titles, "商品ID");
                    TxFksBean txFksBean = null;
                    if (map.containsKey(bbid)) {
                        txFksBean = map.get(bbid);
                    } else {
                        txFksBean = new TxFksBean();
                    }
                    String rq = getRowStr(row, cols, titles, "统计日期");
                    txFksBean.setRq(rq);
                    txFksBean.setBbid(bbid);
                    if (fkslx.equalsIgnoreCase("ztc")) {
                        txFksBean.setZtcfks(getRowInt(row, cols, titles, "访客数"));
                    } else if (fkslx.equalsIgnoreCase("sttj")) {
                        txFksBean.setSttjfks(getRowInt(row, cols, titles, "访客数"));
                    } else if (fkslx.equalsIgnoreCase("stss")) {
                        txFksBean.setStssfks(getRowInt(row, cols, titles, "访客数"));
                    } else if (fkslx.equalsIgnoreCase("aizt")) {
                        txFksBean.setAiztfks(getRowInt(row, cols, titles, "访客数"));
                    } else if (fkslx.equalsIgnoreCase("ylmf")) {
                        txFksBean.setYlmffks(getRowInt(row, cols, titles, "访客数"));
                    } else if (fkslx.equalsIgnoreCase("tbk")) {
                        txFksBean.setTbkfks(getRowInt(row, cols, titles, "访客数"));
                    }
                    map.put(bbid, txFksBean);
                }
            }
        } catch (Exception ignored) {
        } finally {
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
            if (file != null && file.exists()) {
                file.delete();
            }
        }
    }
}
