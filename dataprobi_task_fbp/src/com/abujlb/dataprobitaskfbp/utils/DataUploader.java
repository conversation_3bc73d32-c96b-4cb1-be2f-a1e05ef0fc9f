package com.abujlb.dataprobitaskfbp.utils;

import com.abujlb.CommonConfig;
import com.abujlb.Result;
import com.abujlb.dataprobitaskfbp.bean.BiInfo;
import com.abujlb.dataprobitaskfbp.bean.BiJlqcAvvid;
import com.abujlb.dataprobitaskfbp.bean.BiRuleReRunTask;
import com.abujlb.dataprobitaskfbp.bean.BiSupplier;
import com.abujlb.service.ServiceClient;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;

import java.lang.reflect.Type;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class DataUploader {

    private static final ServiceClient client;
    private static final String service4Url;
    private static final String service2Url;
    private static final Logger log = Logger.getLogger(DataUploader.class);

    static {
        client = new ServiceClient(CommonConfig.getString("jyrbAppid"), CommonConfig.getString("jyrbAppkey"));
        service4Url = CommonConfig.getString("service4Url");
        service2Url = CommonConfig.getString("service2Url");
    }

    public static List<BiJlqcAvvid> getJlqcAvvids(String userid) {
        Map<String, String> map = new HashMap<>();
        map.put("userid", userid);
        String s = client.exec(service2Url + "/bijlqc/getJlqcAvvids", map);
        if (Result.isTrue(s)) {
            Type type = new TypeToken<List<BiJlqcAvvid>>() {
            }.getType();
            return Result.getValueFromJson(s, "data", type);
        }
        return null;
    }

    public static BiSupplier getSupplierRules(String userid, String qd) {
        try {
            BiSupplier biSupplier = new BiSupplier();
            biSupplier.setSupplierUserid(userid);
            biSupplier.setQd(qd);
            String result = client.exec(service2Url + "/bi2/getSupplier", biSupplier);
            if (Result.isTrue(result)) {
                return Result.getValueFromJson(result, "data", BiSupplier.class);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }

    public static void rerunTask(BiRuleReRunTask task) {
        try {
            final List<Integer> testYhid = Arrays.asList(1961, 5241);
            if (testYhid.contains(task.getYhid())) {
                return;
            }
            client.exec(service4Url + "/bi_rerun_task/addTask", task);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    public static String getCategoryJdSjlmsm(String oneLmmc, String twoLmmc, String threeLmmc) {
        try {
            if (StringUtils.isBlank(threeLmmc) || StringUtils.isBlank(oneLmmc) || StringUtils.isBlank(twoLmmc)) {
                return null;
            }
            Map<String, String> map = new HashMap<>();
            map.put("oneLmmc", oneLmmc);
            map.put("twoLmmc", twoLmmc);
            map.put("threeLmmc", threeLmmc);

            String s = client.exec(service2Url + "/category/jd2/getsjlmsm", new Gson().toJson(map));
            if (Result.isTrue(s)) {
                JSONObject jsonObj = JSONObject.parseObject(s);
                return jsonObj.getString("data");
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }

    public static String getIp() {
        String json = client.exec("https://service2.abujlb.com/ip/myip", "");
        return Result.getStringFromJson(json, "ip");
    }


    public static List<BiInfo> getAllBiInfo() {
        String s = client.exec(service2Url + "/bi2/getAllBiInfos", new Object());
        if (Result.isTrue(s)) {
            Type type = new TypeToken<List<BiInfo>>() {
            }.getType();
            return Result.getValueFromJson(s, "data", type);
        }
        return null;
    }
}
