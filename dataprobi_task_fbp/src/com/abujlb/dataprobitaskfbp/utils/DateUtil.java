package com.abujlb.dataprobitaskfbp.utils;

import org.apache.log4j.Logger;

import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/2/27
 */
public class DateUtil {

    private static final Logger LOG = Logger.getLogger(DateUtil.class);

    private static final SimpleDateFormat sdfDate = new SimpleDateFormat("yyyy-MM-dd");

    public static String getCurrentTime() {
        LocalDateTime localDateTime = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return localDateTime.format(formatter);
    }

    public static String getCurrentDate() {
        LocalDateTime localDateTime = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return localDateTime.format(formatter);
    }

    public static String getCurrentMonth() {
        LocalDateTime localDateTime = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return localDateTime.format(formatter).substring(0, 7);
    }


    public static String getLastday() {
        LocalDate localDate = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return localDate.minusDays(1).format(formatter);
    }

    public static List<String> getBetweenDate(String start, String end) {
        List<String> list = new ArrayList<String>();
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDate startLocalDate = LocalDate.parse(start, formatter);
            LocalDate endLocalDate = LocalDate.parse(end, formatter);
            while (startLocalDate.isBefore(endLocalDate) || startLocalDate.isEqual(endLocalDate)) {
                list.add(startLocalDate.format(formatter));
                startLocalDate = startLocalDate.plusDays(1);
            }
            return list;
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
        return Collections.emptyList();
    }

    public static boolean isLastDayForMonth(String rq) {
        LocalDate localDate = LocalDate.parse(rq, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        int monthDays = localDate.getMonth().length(localDate.isLeapYear());
        int value = localDate.getDayOfMonth();
        return value == monthDays;
    }

    public static String getMonth(String rq) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate localDate = LocalDate.parse(rq, dateTimeFormatter);
        return localDate.format(DateTimeFormatter.ofPattern("yyyy-MM"));
    }

    public static List<String> getDaysBefore(String rq, int diff) {
        List<String> list = new ArrayList<>(Math.abs(diff));
        try {
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDate startLocaldate = LocalDate.parse(rq, dateTimeFormatter);
            for (int i = 0; i <= Math.abs(diff); i++) {
                list.add(startLocaldate.format(dateTimeFormatter));
                startLocaldate = startLocaldate.minusDays(1);
            }
            Collections.sort(list);
            return list;
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
        return Collections.emptyList();
    }

    public static List<String> getDaysBefore2(String rq, int diff) {
        List<String> list = new ArrayList<>(Math.abs(diff));
        try {
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDate startLocaldate = LocalDate.parse(rq, dateTimeFormatter);
            for (int i = 0; i <= Math.abs(diff); i++) {
                if (i != 0) {
                    list.add(startLocaldate.format(dateTimeFormatter));
                }
                startLocaldate = startLocaldate.minusDays(1);
            }
            Collections.sort(list);
            return list;
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
        return Collections.emptyList();
    }

    public static String getDaysBeforeAsString(String rq, int diff) {
        try {
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            return LocalDate.parse(rq, dateTimeFormatter).minusDays(Math.abs(diff)).format(dateTimeFormatter);
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
        return "";
    }


    public static String getDaysAfterAsString(String rq, int diff) {
        try {
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            return LocalDate.parse(rq, dateTimeFormatter).plusDays(Math.abs(diff)).format(dateTimeFormatter);
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
        return "";
    }


    public static String getMonthBeforeAsString(String month, int diff) {
        try {
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDate localDate = LocalDate.parse(month + "-01", dateTimeFormatter).minusMonths(Math.abs(diff));
            return localDate.getYear() + "-" + (localDate.getMonthValue() < 10 ? ("0" + localDate.getMonthValue()) : localDate.getMonthValue());
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
        return "";
    }

    public static List<String> getMonthDays(String month) {
        List<String> list = new ArrayList<>();
        try {
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDate localDate = LocalDate.parse(month + "-01", dateTimeFormatter);

            int maxLength = localDate.getMonth().length(localDate.isLeapYear());
            for (int i = 0; i < maxLength; i++) {
                list.add(month + "-" + (i < 9 ? (0 + "" + (i + 1)) : (i + 1)));
            }
            return list;
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
        return Collections.emptyList();
    }

    public static boolean isLastDayForWeek(String date) {
        LocalDate localDate = LocalDate.parse(date);
        int dayOfWeek = localDate.getDayOfWeek().getValue();
        return dayOfWeek == 7;
    }


    public static List<String> getWeekDays(String week) {
        List<String> list = new ArrayList<>();
        int weekYear = Integer.parseInt(week.substring(0, 4));
        int weekTh = Integer.parseInt(week.substring(4, 6));

        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        calendar.setFirstDayOfWeek(Calendar.MONDAY);
        calendar.setWeekDate(weekYear, weekTh, Calendar.MONDAY);

        list.add(sdfDate.format(calendar.getTime()));
        for (int i = 1; i < 7; i++) {
            list.add(getDaysAfterAsString(list.get(i - 1), 1));
        }
        return list;
    }

    /**
     * 获取入参日期是一年中的第几周
     *
     * @param rq 入参格式要求：yyyy-MM-dd
     * @return 返回的格式：2022-01 2022-22  2022-52...
     */
    public static String getWeekOfYear(String rq) {
        try {
            Calendar calendar = Calendar.getInstance();
            calendar.setFirstDayOfWeek(Calendar.MONDAY);
            calendar.setTime(sdfDate.parse(rq));
            return ((calendar.getWeekYear() + "-" + (calendar.get(Calendar.WEEK_OF_YEAR) < 10 ? "0" + calendar.get(Calendar.WEEK_OF_YEAR) : String.valueOf(calendar.get(Calendar.WEEK_OF_YEAR))))).replaceAll("-", "");
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
        return null;
    }


    public static String timestampToDateTimeStr(String timestamp) {
        try {
            Instant instant = Instant.ofEpochMilli(Long.parseLong(timestamp));
            LocalDateTime localDateTime = instant.atZone(ZoneId.systemDefault()).toLocalDateTime();
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            return localDateTime.format(dateTimeFormatter);
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
        return "";
    }

    public static String timestampToDateTimeStr2(String timestamp) {
        try {
            Instant instant = Instant.ofEpochMilli(Long.parseLong(timestamp));
            LocalDateTime localDateTime = instant.atZone(ZoneId.systemDefault()).toLocalDateTime();
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            return localDateTime.format(dateTimeFormatter);
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
        return "";
    }

    public static String TimeStamp2Date(String timestampString) {
        return timestampToDateTimeStr(Long.parseLong(timestampString) * 1000 + "");
    }

    public static List<String> getRecent24Months(String start) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate localDate = LocalDate.parse(start, dateTimeFormatter);

        List<String> list = new ArrayList<>();

        for (int i = 0; i < 24; i++) {
            int monthValue = localDate.getMonthValue();
            int monthDays = monthValue == 2 ? (localDate.isLeapYear() ? 29 : 28) : localDate.getMonth().maxLength();
            localDate = localDate.withDayOfMonth(monthDays);
            list.add(localDate.format(dateTimeFormatter));
            localDate = localDate.minusMonths(1);
        }
        list.remove(0);
        return list;
    }


    public static void lastmonth(List<String> rqList, String start) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate localDate = LocalDate.parse(start, dateTimeFormatter);
        LocalDate endLocalDate = LocalDate.parse("2022-12-31", dateTimeFormatter);
        while (localDate.isBefore(endLocalDate)) {
            localDate = localDate.plusMonths(1);
            int monthValue = localDate.getMonthValue();
            int monthDays = monthValue == 2 ? (localDate.isLeapYear() ? 29 : 28) : localDate.getMonth().maxLength();
            localDate = localDate.withDayOfMonth(monthDays);

            rqList.add(localDate.format(dateTimeFormatter));
        }
    }

    /**
     * 判断是否为当前月最后一天，如果是返回当前月天数，如果不是返回-1
     *
     * @param date 格式:"yyyy-MM-dd"
     * @return 天数
     */
    public static int isLastDayOfMonth(String date) {
        LocalDate currentLocalDate = LocalDate.parse(date);
        LocalDate lastDayOfMonth = currentLocalDate.with(TemporalAdjusters.lastDayOfMonth());
        if (Period.between(currentLocalDate, lastDayOfMonth).getDays() == 0) {
            return currentLocalDate.getDayOfMonth();
        } else {
            return -1;
        }
    }

    public static void main(String[] args) {
//        List<String> recent24Months = getRecent24Months("2022-11-01");

        // lastmonth(recent24Months, recent24Months.get(0));

//        System.out.println("recent24Months = " + recent24Months);

//        boolean lastDayForWeek = isLastDayForWeek("2023-03-12");
//        System.out.println("lastDayForWeek = " + lastDayForWeek);

//        String weekOfYear = DataprobiDateUtil.getWeekOfYear("2023-04-08");
//        System.out.println("weekOfYear = " + weekOfYear);

//        String rq = "2023-08-22";
//        List<String> recent7_1 = DataprobiDateUtil.getDaysBefore(rq, -6);
//        List<String> recent7_2 = DataprobiDateUtil.getDaysBefore2(rq, -7);

//        System.out.println(recent7_1);
//        System.out.println(recent7_2);

        System.out.println("getMonthBeforeCurrMonthAsFirstday() = " + getMonthBeforeCurrMonthAsFirstday());

        System.out.println("\"2023-01-01\".compareTo(getMonthBeforeCurrMonthAsFirstday()) = " + "2023-01-01".compareTo(getMonthBeforeCurrMonthAsFirstday()));
    }

    public static int rqIndex(String rq) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate localDate = LocalDate.parse(rq, dateTimeFormatter);
        return localDate.getDayOfMonth();
    }

    public static String getLastMonthLastday(String rq) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate lastMonthFirstday = LocalDate.parse(rq, dateTimeFormatter).withDayOfMonth(1).minusMonths(1);
        int monthDays = lastMonthFirstday.getMonth().length(lastMonthFirstday.isLeapYear());
        return lastMonthFirstday.withDayOfMonth(monthDays).format(dateTimeFormatter);
    }

    public static String getFirstdayOfMonth(String rq) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate firstdayOfMonth = LocalDate.parse(rq, dateTimeFormatter).withDayOfMonth(1);
        return firstdayOfMonth.format(dateTimeFormatter);
    }

    public static String getMonthBeforeCurrMonthAsFirstday() {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate now = LocalDate.now();
        return getMonthBeforeAsString(now.format(dateTimeFormatter).substring(0, 7), 2) + "-01";
    }

    public static String getLastdayOfMonth(String month) {
        try {
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDate localDate = LocalDate.parse(month + "-01", dateTimeFormatter);

            int maxLength = localDate.getMonth().length(localDate.isLeapYear());
            return month+"-"+maxLength ;
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
        return null ;
    }
}
