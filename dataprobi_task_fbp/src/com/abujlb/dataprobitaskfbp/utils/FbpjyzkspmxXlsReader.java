package com.abujlb.dataprobitaskfbp.utils;

import org.apache.log4j.Logger;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/12/26
 */
public class FbpjyzkspmxXlsReader {

    static final Logger LOG = Logger.getLogger(FbpjyzkspmxXlsReader.class);


    public static int getRowInt(Row row, int cols, Map<String, Integer> titles, String title) {
        return (int) getRowDouble(row, cols, titles, title);
    }


    public synchronized static String getRowStr(Row row, int cols, Map<String, Integer> titles, String title) {
        Object index = titles.get(title);
        if (index == null) {
            return null;
        }
        int col = Integer.parseInt(index.toString());
        if (col < 0 || col >= cols) {
            return null;
        }
        return row.getCell(col).getStringCellValue();
    }

    public synchronized static double getRowDouble(Row row, int cols, Map<String, Integer> titles, String title) {
        Object index = titles.get(title);
        if (index == null) {
            return 0D;
        }
        int col = Integer.parseInt(index.toString());
        if (col < 0 || col >= cols) {
            return 0D;
        }
        Cell cell = row.getCell(col);
        if (cell.getCellType() == Cell.CELL_TYPE_NUMERIC) {
            return cell.getNumericCellValue();
        }
        String str = row.getCell(col).getStringCellValue();
        return StringToNumber.transform(str);
    }

    public synchronized static long getRowLong(Row row, int cols, Map<String, Integer> titles, String title) {
        Object index = titles.get(title);
        if (index == null) {
            return 0L;
        }
        int col = Integer.parseInt(index.toString());
        if (col < 0 || col >= cols) {
            return 0L;
        }
        Cell cell = row.getCell(col);
        if (cell.getCellType() == Cell.CELL_TYPE_NUMERIC) {
            return (long) cell.getNumericCellValue();
        }
        String str = row.getCell(col).getStringCellValue();
        return StringToNumber.transformLong(str);
    }

}
