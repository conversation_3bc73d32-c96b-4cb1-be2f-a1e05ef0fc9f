package com.abujlb.dataprobitaskfbp.test;

import com.abujlb.AbujlbBeanFactory;
import com.abujlb.BaseTest;
import com.abujlb.dataprobitaskfbp.bean.DataprobiTaskMsg;
import com.abujlb.dataprobitaskfbp.constants.TaskTypeConst;
import com.abujlb.dataprobitaskfbp.service.tgfx.JdJztTgfxService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;

public class TestTgfx extends BaseTest {


    @Autowired
    private AbujlbBeanFactory abujlbBeanFactory;

    @Test
    public void jdtgfx() {
        DataprobiTaskMsg taskMsg = new DataprobiTaskMsg();
        taskMsg.setDpmc("中伟京东自营专区");
        taskMsg.setQd("JD");
        taskMsg.setTaskType(TaskTypeConst.JD_TGFX_JDKC);
        taskMsg.setYhid(10008873);
        taskMsg.setUserid("1000078332");
        taskMsg.setRqList(Collections.singletonList("2024-11-28"));
        JdJztTgfxService jdJztTgfxService = abujlbBeanFactory.getBean(JdJztTgfxService.class);
        jdJztTgfxService.tgfx(taskMsg, taskMsg.getRqList(), JdJztTgfxService.GWCD_SIGN, JdJztTgfxService.GWCD_SIGN_DESC);
    }
}
