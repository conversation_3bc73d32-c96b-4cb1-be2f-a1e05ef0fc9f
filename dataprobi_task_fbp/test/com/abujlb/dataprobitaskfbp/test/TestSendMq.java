package com.abujlb.dataprobitaskfbp.test;

import com.abujlb.BaseTest;
import com.abujlb.dataprobitaskfbp.bean.BiInfo;
import com.abujlb.dataprobitaskfbp.bean.DataprobiTaskMsg;
import com.abujlb.dataprobitaskfbp.constants.TaskTypeConst;
import com.abujlb.dataprobitaskfbp.utils.DataUploader;
import com.abujlb.dataprobitaskfbp.utils.DateUtil;
import com.abujlb.mq.AliyunMq2;
import com.abujlb.mq.JobMessage;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.List;
import java.util.UUID;

public class TestSendMq extends BaseTest {

    @Autowired
    private AliyunMq2 aliyunMq2;

    @Test
    public void send() {
        DataprobiTaskMsg taskMsg = new DataprobiTaskMsg();
        taskMsg.setDpmc("中伟京东自营专区");
        taskMsg.setQd("JD");
        taskMsg.setTaskType(TaskTypeConst.JD_TGFX_GWCD);
        taskMsg.setYhid(10008873);
        taskMsg.setUserid("1000473176");
//        taskMsg.setRqList(Collections.singletonList("2024-08-26"));
        taskMsg.setRqList(DateUtil.getBetweenDate("2024-11-01", "2024-12-02"));

        aliyunMq2.send("dataprobi_task_fbp", UUID.randomUUID().toString(), new JobMessage("dataprobi_task_fbp_processor", taskMsg));
    }

    @Test
    public void sendAllTgfx() {
        List<BiInfo> allBiInfo = DataUploader.getAllBiInfo();
        if (CollectionUtils.isEmpty(allBiInfo)) {
            return;
        }

        List<String> taskTypeList = Arrays.asList(TaskTypeConst.JD_TGFX_GWCD, TaskTypeConst.JD_TGFX_JDKC, TaskTypeConst.JD_TGFX_JDZT, TaskTypeConst.JD_TGFX_JDZW, TaskTypeConst.JD_TGFX_JST, TaskTypeConst.JD_TGFX_QZYX);
        for (BiInfo biInfo : allBiInfo) {
            if (biInfo.getYhid() == 10008873 && biInfo.getQd().equals("JD")) {
                for (String taskType : taskTypeList) {
                    DataprobiTaskMsg taskMsg = new DataprobiTaskMsg();
                    taskMsg.setDpmc(biInfo.getDpmc());
                    taskMsg.setQd("JD");
                    taskMsg.setTaskType(taskType);
                    taskMsg.setYhid(10008873);
                    taskMsg.setUserid(biInfo.getUserid());
                    taskMsg.setRqList(DateUtil.getBetweenDate("2024-11-01", "2024-12-02"));
                    aliyunMq2.send("dataprobi_task_fbp", UUID.randomUUID().toString(), new JobMessage("dataprobi_task_fbp_processor", taskMsg));
                }
            }
        }
    }
}
