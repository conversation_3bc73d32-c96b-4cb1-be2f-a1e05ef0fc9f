package com.abujlb.dataprobitaskfbp.test;

import com.abujlb.BaseTest;
import com.abujlb.dataprobitaskfbp.bean.DataprobiTaskMsg;
import com.abujlb.dataprobitaskfbp.constants.TaskTypeConst;
import com.abujlb.dataprobitaskfbp.service.JdFbpSpCateTaskRunnable;
import com.abujlb.dataprobitaskfbp.service.xkfx.JdXkfxTask;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;

public class TestJd extends BaseTest {

    @Autowired
    private JdFbpSpCateTaskRunnable jdFbpSpCateTaskRunnable;

    @Autowired
    private JdXkfxTask jdXkfxTask;

    @Autowired
    private JdFbpSpCateTaskRunnable jdFbpSpCateTaskRunnable2;

    @Test
    public void spcate() {
        DataprobiTaskMsg taskMsg = new DataprobiTaskMsg();
        taskMsg.setDpmc("奈高京东自营专区");
        taskMsg.setQd("JD");
        taskMsg.setTaskType(TaskTypeConst.JD_FBP_SPCATE);
        taskMsg.setYhid(10008873);
        taskMsg.setUserid("1000088964");
        taskMsg.setRqList(Collections.singletonList("2024-10-08"));
        jdFbpSpCateTaskRunnable.deal(taskMsg);
    }

    @Test
    public void xkfx() {
        DataprobiTaskMsg taskMsg = new DataprobiTaskMsg();
        taskMsg.setDpmc("中伟京东自营专区");
        taskMsg.setQd("JD");
        taskMsg.setTaskType(TaskTypeConst.JD_XKFX);
        taskMsg.setYhid(10008873);
        taskMsg.setUserid("1000078332");
        taskMsg.setRqList(Collections.singletonList("2024-11-28"));
        jdXkfxTask.process(taskMsg);
    }

    @Test
    public void fbp_sp_cate() {
        DataprobiTaskMsg taskMsg = new DataprobiTaskMsg();
        taskMsg.setDpmc("欧宝美京东自营专区");
        taskMsg.setQd("JD");
        taskMsg.setTaskType(TaskTypeConst.JD_FBP_SPCATE);
        taskMsg.setYhid(10008873);
        taskMsg.setUserid("1000092523");
        taskMsg.setRqList(Collections.singletonList("2024-12-11"));
        jdFbpSpCateTaskRunnable2.deal(taskMsg);
    }
}
