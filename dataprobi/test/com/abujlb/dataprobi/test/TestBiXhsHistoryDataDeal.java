package com.abujlb.dataprobi.test;

import com.abujlb.AbujlbBeanFactory;
import com.abujlb.BaseTest;
import com.abujlb.dataprobi.bean.BiInfo;
import com.abujlb.dataprobi.bean.BiRuleReRunTask;
import com.abujlb.dataprobi.bean.xhs.DataprobiXhsMsg;
import com.abujlb.dataprobi.oss.BiDataOssUtil;
import com.abujlb.dataprobi.processes.xhs.*;
import com.abujlb.dataprobi.server.DataUploader;
import com.abujlb.dataprobi.service.BiXhsDataDealService;
import com.abujlb.dataprobi.threads.BiThreadLocals;
import com.abujlb.dataprobi.util.DataprobiDateUtil;
import com.abujlb.mq.AliyunMq2;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/5/7
 */
public class TestBiXhsHistoryDataDeal extends BaseTest {

    @Autowired
    private AbujlbBeanFactory abujlbBeanFactory;
    @Autowired
    private AliyunMq2 aliyunMq2;
    @Autowired
    private BiDataOssUtil biDataOssUtil;

    // biinfoIds 静态列表
    private static List<Integer> biinfoIds = new ArrayList<>();

    static {
        initializeBiinfoIds();
    }

    private static void initializeBiinfoIds() {
        ObjectMapper mapper = new ObjectMapper();
        try {
            JsonNode rootNode = mapper.readTree(new File("/Users/<USER>/听海/工作资料/prodatabicjzh.json"));
            JsonNode cjrqList = rootNode.path("cjrqList");

            if (cjrqList.isArray()) {
                for (JsonNode node : cjrqList) {
                    int id = node.path("id").asInt();
                    biinfoIds.add(id);
                }
            }
            System.out.println("biinfoIds 初始化完成: " + biinfoIds);
        } catch (IOException e) {
            e.printStackTrace();
            System.out.println("读取 JSON 文件时发生错误");
        }
    }

    @Test
    public void spxg() {
        for (int biinfoId : biinfoIds) {
            BiInfo biInfo = DataUploader.getBiinfoById(biinfoId);
            if (biInfo == null) {
                System.out.println("获取Biinfo失败，Biinfo的ID为：" + biinfoId);
                continue;
            }

            DataprobiXhsMsg dataprobiXhsMsg = initializeDataprobiXhsMsg(biInfo);
            BiThreadLocals.init(dataprobiXhsMsg);

            DefaultbiXhsSpxgProcess bean = abujlbBeanFactory.getBean(DefaultbiXhsSpxgProcess.class);
            bean.dealGoods();
            //bean.dealShop();
            System.out.println("处理商品效果数据完成，biinfoId: " + biinfoId);
        }
    }

    @Test
    public void llsj() {
        for (int biinfoId : biinfoIds) {
            BiInfo biInfo = DataUploader.getBiinfoById(biinfoId);
            if (biInfo == null) {
                System.out.println("获取Biinfo失败，Biinfo的ID为：" + biinfoId);
                continue;
            }

            DataprobiXhsMsg dataprobiXhsMsg = initializeDataprobiXhsMsg(biInfo);
            BiThreadLocals.init(dataprobiXhsMsg);

            DefaultbiXhsLlsjProcess bean = abujlbBeanFactory.getBean(DefaultbiXhsLlsjProcess.class);
            bean.dealShop();
            System.out.println("处理流量数据数据完成，biinfoId: " + biinfoId);
        }
    }

    @Test
    public void jgptbj() {
        for (int biinfoId : biinfoIds) {
            BiInfo biInfo = DataUploader.getBiinfoById(biinfoId);
            if (biInfo == null) {
                System.out.println("获取Biinfo失败，Biinfo的ID为：" + biinfoId);
                continue;
            }

            DataprobiXhsMsg dataprobiXhsMsg = initializeDataprobiXhsMsg(biInfo);
            BiThreadLocals.init(dataprobiXhsMsg);

            DefaultbiXhsJgptBitgProcess bean = abujlbBeanFactory.getBean(DefaultbiXhsJgptBitgProcess.class);
            bean.dealShop();
            System.out.println("处理聚光平台笔记数据完成，biinfoId: " + biinfoId);
        }
    }

    @Test
    public void jgptzb() {
        for (int biinfoId : biinfoIds) {
            BiInfo biInfo = DataUploader.getBiinfoById(biinfoId);
            if (biInfo == null) {
                System.out.println("获取Biinfo失败，Biinfo的ID为：" + biinfoId);
                continue;
            }

            DataprobiXhsMsg dataprobiXhsMsg = initializeDataprobiXhsMsg(biInfo);
            BiThreadLocals.init(dataprobiXhsMsg);

            DefaultbiXhsJgptZbfxProcess bean = abujlbBeanFactory.getBean(DefaultbiXhsJgptZbfxProcess.class);
            bean.dealShop();
            System.out.println("处理聚光平台直播数据完成，biinfoId: " + biinfoId);
        }
    }

    @Test
    public void qfbj() {
        for (int biinfoId : biinfoIds) {
            BiInfo biInfo = DataUploader.getBiinfoById(biinfoId);
            if (biInfo == null) {
                System.out.println("获取Biinfo失败，Biinfo的ID为：" + biinfoId);
                continue;
            }

            DataprobiXhsMsg dataprobiXhsMsg = initializeDataprobiXhsMsg(biInfo);
            BiThreadLocals.init(dataprobiXhsMsg);

            DefaultbiXhsQfbjyxProcess bean = abujlbBeanFactory.getBean(DefaultbiXhsQfbjyxProcess.class);
            bean.dealGoods();
            System.out.println("处理千帆笔记数据完成，biinfoId: " + biinfoId);
        }
    }

    @Test
    public void qfzb() {
        for (int biinfoId : biinfoIds) {
            BiInfo biInfo = DataUploader.getBiinfoById(biinfoId);
            if (biInfo == null) {
                System.out.println("获取Biinfo失败，Biinfo的ID为：" + biinfoId);
                continue;
            }

            DataprobiXhsMsg dataprobiXhsMsg = initializeDataprobiXhsMsg(biInfo);
            BiThreadLocals.init(dataprobiXhsMsg);

            DefaultbiXhsQfzbyxProcess bean = abujlbBeanFactory.getBean(DefaultbiXhsQfzbyxProcess.class);
            bean.dealShop();
            System.out.println("处理千帆直播营销数据完成，biinfoId: " + biinfoId);
        }
    }

    @Test
    public void cfsp() {
        for (int biinfoId : biinfoIds) {
            BiInfo biInfo = DataUploader.getBiinfoById(biinfoId);
            if (biInfo == null) {
                System.out.println("获取Biinfo失败，Biinfo的ID为：" + biinfoId);
                continue;
            }

            DataprobiXhsMsg dataprobiXhsMsg = initializeDataprobiXhsMsg(biInfo);
            BiThreadLocals.init(dataprobiXhsMsg);
            DefaultbiXhsCfspTgProcess bean = abujlbBeanFactory.getBean(DefaultbiXhsCfspTgProcess.class);
            bean.dealGoods();
            System.out.println("处理乘风商品营销数据完成");
        }
    }

    @Test
    public void cfzb() {
        for (int biinfoId : biinfoIds) {
            BiInfo biInfo = DataUploader.getBiinfoById(biinfoId);
            if (biInfo == null) {
                System.out.println("获取Biinfo失败，Biinfo的ID为：" + biinfoId);
                continue;
            }

            DataprobiXhsMsg dataprobiXhsMsg = initializeDataprobiXhsMsg(biInfo);
            BiThreadLocals.init(dataprobiXhsMsg);
            DefaultbiXhsCfzbTgProcess bean = abujlbBeanFactory.getBean(DefaultbiXhsCfzbTgProcess.class);
            bean.dealGoods();
            System.out.println("处理乘风直播营销数据完成");
        }
    }

    private DataprobiXhsMsg initializeDataprobiXhsMsg(BiInfo biInfo) {
        DataprobiXhsMsg dataprobiXhsMsg = new DataprobiXhsMsg();
        dataprobiXhsMsg.setUserid(biInfo.getUserid());
        dataprobiXhsMsg.setYhid(biInfo.getYhid());
        dataprobiXhsMsg.setQd(biInfo.getQd());
        dataprobiXhsMsg.setDpmc(biInfo.getDpmc());
        dataprobiXhsMsg.setBiinfoId(biInfo.getId());
        dataprobiXhsMsg.setType(1);
        dataprobiXhsMsg.setSplb(0);

        List<String> rqList = DataprobiDateUtil.getBetweenDate("2024-10-01", "2024-11-06");
        dataprobiXhsMsg.setSpxg(rqList);
        dataprobiXhsMsg.setSpxgdp(rqList);
        dataprobiXhsMsg.setLlsjdp(rqList);
        dataprobiXhsMsg.setJgptbjtg(rqList);
        dataprobiXhsMsg.setJgptzbfxdp(rqList);
        dataprobiXhsMsg.setQfbjyx(rqList);
        dataprobiXhsMsg.setQfzbyxdp(rqList);
        dataprobiXhsMsg.setJgptbjtgdp(rqList);
        dataprobiXhsMsg.setCfzbtg(rqList);
        dataprobiXhsMsg.setCfzbtgdp(rqList);
        dataprobiXhsMsg.setCfsptgdp(rqList);
        dataprobiXhsMsg.setCfsptg(rqList);

        return dataprobiXhsMsg;
    }

    @Test
    public void runLocal() {
        for (int biinfoId : biinfoIds) {
            BiInfo biInfo = DataUploader.getBiinfoById(biinfoId);
            if (biInfo == null) {
                System.out.println("获取Biinfo失败，Biinfo的ID为：" + biinfoId);
                continue;
            }

            DataprobiXhsMsg dataprobiXhsMsg = initializeDataprobiXhsMsg(biInfo);
            BiThreadLocals.init(dataprobiXhsMsg);

            BiXhsDataDealService biXhsDataDealService = abujlbBeanFactory.getBean(BiXhsDataDealService.class);
            biXhsDataDealService.process();
            System.out.println("本地处理完成，biinfoId: " + biinfoId);
        }
    }


    @Test
    public void commitRerunTask() {
        // 获取所有的 BiInfo 对象
        List<BiInfo> biInfoList1 = DataUploader.getAllBiInfo();
        // 过滤出 qd 等于 "XHS" 的 BiInfo 对象
//        List<BiInfo> filteredBiInfoList = biInfoList1.stream()
//                .filter(biInfo -> "XHS".equals(biInfo.getQd()))
//                .collect(Collectors.toList());
//        List<BiInfo> filteredBiInfoList = biInfoList1.stream()
//                .filter(biInfo -> "XHS".equals(biInfo.getQd()) && "22790".equals(biInfo.getUserid()))
//                .collect(Collectors.toList());
        final int biinfoId = 7231;
        BiInfo biInfo1 = DataUploader.getBiinfoById(biinfoId);
        List<BiInfo> filteredBiInfoList = new ArrayList<>();
        filteredBiInfoList.add(biInfo1);
        for (BiInfo biInfo : filteredBiInfoList) {
            //BiInfo biInfo = DataUploader.getBiinfoById(biinfoId);
            BiRuleReRunTask task = new BiRuleReRunTask();
            task.setId("sycm_spu_" + UUID.randomUUID());
            task.setYhid(biInfo.getYhid());
            task.setQd(biInfo.getQd());
            task.setUserid(biInfo.getUserid());
            task.setKsrq("2024-10-01");
            task.setJsrq("2024-10-31");
            task.setCjsj(DataprobiDateUtil.getCurrentTime());
            task.setZt(0);
            task.setType(16);
            task.setCjrxm("聚光笔记推广店铺数据");
            task.setReason("补采重算");
            if (task.getKsrq().compareTo(task.getJsrq()) <= 0) {
                DataUploader.rerunTask(task);
            }

        }

    }


    /**
     * biInfo.getYhid();
     * biInfo.getUserid();
     * biInfo.getQd();
     */
    @Test
    public void getSpuUrl2Json() {
        // 获取所有的 BiInfo 对象
        List<BiInfo> biInfoList1 = DataUploader.getAllBiInfo();
        // 过滤出 qd 等于 "XHS" 的 BiInfo 对象
        List<BiInfo> filteredBiInfoList = biInfoList1.stream()
                .filter(biInfo -> "XHS".equals(biInfo.getQd()))
                .collect(Collectors.toList());
        // SYCM_SPU_DAY 模板路径
        String SYCM_SPU_DAY = "bi_data/day/%s/%s/%s/%s/sycm_spu.db";

        // 存放生成的所有路径
        List<String> spuUrls = new ArrayList<>();
        // 定义日期格式
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");

        // 设置起始日期和结束日期
        Calendar startDate = Calendar.getInstance();
        startDate.set(2024, Calendar.AUGUST, 1); // 设置为 2024 年 8 月 1 日
        Calendar endDate = Calendar.getInstance();
        endDate.set(2024, Calendar.SEPTEMBER, 17); // 设置为 2024 年 9 月 17 日

        // 遍历 BiInfo 列表
        for (BiInfo biInfo : filteredBiInfoList) {
            // 遍历日期范围
            Calendar currentDate = (Calendar) startDate.clone();
            while (!currentDate.after(endDate)) {
                // 格式化当前日期为 yyyyMMdd
                String dateStr = dateFormat.format(currentDate.getTime());
                // 根据模板生成路径
                String spuUrl = String.format(SYCM_SPU_DAY, dateStr, biInfo.getYhid(), biInfo.getQd(), biInfo.getUserid());
                // 将生成的路径添加到列表中
                spuUrls.add(spuUrl);
                // 日期加 1 天
                currentDate.add(Calendar.DAY_OF_MONTH, 1);
            }
        }

        // 将生成的路径列表写入本地 JSON 文件
        writeUrlsToJson(spuUrls, "/Users/<USER>/听海/工作资料/spuurl.json");
    }

    // 将路径列表写入 JSON 文件的方法
    private void writeUrlsToJson(List<String> urls, String filePath) {
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            // 将列表写入指定路径的 JSON 文件
            objectMapper.writeValue(new File(filePath), urls);
            System.out.println("SPU URLs have been written to " + filePath);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    @Test
    public void getkfdbUrl2Json() {
        // 获取所有 BiInfo 对象
        List<BiInfo> biInfoList1 = DataUploader.getAllBiInfo();

        // 过滤出 qd 等于 "PDD" 的对象
        List<BiInfo> filteredBiInfoList = biInfoList1.stream()
                .filter(biInfo ->
                        "PDD".equals(biInfo.getQd())
                )
                .collect(Collectors.toList());


        // 新的路径模板：bi_jysj/day/{日期}/PDD/{userId}/sycm_performance_customer_reception_pdd_data.db
        String ossKeyTemplate = "bi_jysj/day/%s/PDD/%s/sycm_performance_customer_reception_pdd_data.db";

        List<String> spuUrls = new ArrayList<>();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

        // 设置起始日期和结束日期
        Calendar startDate = Calendar.getInstance();
        startDate.set(2025, Calendar.JANUARY, 1); // 2025-01-01
        Calendar endDate = Calendar.getInstance();
        endDate.set(2025, Calendar.MAY, 19); // 2025-05-19

        // 循环所有 BiInfo 对象
        for (BiInfo biInfo : filteredBiInfoList) {
            Calendar currentDate = (Calendar) startDate.clone();
            while (!currentDate.after(endDate)) {
                // 格式化日期
                String dateStr = dateFormat.format(currentDate.getTime());

                // 构造 URL，注意只替换两个值：日期和 userId
                String spuUrl = String.format(ossKeyTemplate, dateStr, biInfo.getUserid());

                spuUrls.add(spuUrl);
                currentDate.add(Calendar.DAY_OF_MONTH, 1);
            }
        }

        // 写入本地 JSON 文件
        String outputPath = "D:\\Tinghai\\资料\\kfdb.json";
        writeUrlsToJson1(spuUrls, outputPath);
    }

    // 写入 JSON 文件的方法
    private void writeUrlsToJson1(List<String> urls, String filePath) {
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            File file = new File(filePath);

            // 自动创建目录（重要）
            File parentDir = file.getParentFile();
            if (!parentDir.exists()) {
                parentDir.mkdirs();
            }

            objectMapper.writeValue(file, urls);
            System.out.println("kfdb URLs have been written to " + filePath);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


}
