package com.abujlb.dataprobi.test;

import com.abujlb.BaseTest;
import com.abujlb.CommonConfig;
import com.abujlb.dataprobi.bean.BiInfo;
import com.abujlb.dataprobi.bean.SqliteDp;
import com.abujlb.dataprobi.bean.SqliteSp;
import com.abujlb.dataprobi.oss.BiDataOssUtil;
import com.abujlb.dataprobi.server.DataUploader;
import com.abujlb.dataprobi.util.DataprobiDateUtil;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.abujlb.dataprobi.util.MathUtil;
import com.abujlb.dataprobi.util.SqliteDbUtil;
import com.abujlb.service.ServiceClient;
import com.alibaba.fastjson.JSONObject;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

public class TestCheckhf extends BaseTest {

    private static final ServiceClient serviceClient;
    private static final String serviceUrl;

    static {
        serviceClient = new ServiceClient(CommonConfig.getString("jyrbAppid"), CommonConfig.getString("jyrbAppkey"));
        serviceUrl = CommonConfig.getString("jyrbServiceUrl");
    }

    @Autowired
    BiDataOssUtil biDataOssUtil;
    @Autowired
    private SqliteDbUtil sqliteDbUtil;

    @Test
    public void check() {
        //qytgtsp_znyhqcjje，日期：2025-02-26--->1716.91
        //qytgtsphf，日期：2025-02-26--->20294.46
        //qytgtspcjje，日期：2025-02-26--->93212.75
        int biInfoId = 5275 ;
        String rq = "2025-06-04";
        String zd = "qztghf";
        //15252.410000000058
//        String zd = "jsthf";
//        String zd = "qytgtsphf";
//        String zd = "qytghf";
//        String zd = "jdkchf";
        //302049.16
        BiInfo biInfo = DataUploader.getBiinfoById(biInfoId);
        if (biInfo == null) {
            return;
        }
        String key = "bi_data/day/" + rq.replace("-", "") + "/" + biInfo.getYhid() + "/" + biInfo.getQd() + "/" + biInfo.getUserid() + "/sycm_dp.db";
        SqliteDp sqliteDp = sqliteDbUtil.readDp(key);
        if (sqliteDp == null) {
            return;
        }

        String otherOrDefault = sqliteDp.getOtherOrDefault();
        JSONObject otherObj = JSONObject.parseObject(otherOrDefault);
        double zdHf = FastJSONObjAttrToNumber.toDouble(otherObj, zd);
        System.out.println(biInfo.getDpmc() + "-->" + zd + "，日期：" + rq + "--->" + zdHf);
//        double spxgtkje = sqliteDp.getSpxgzfmjs();
//        System.out.println(biInfo.getDpmc() + "-->" + zd + "，日期：" + rq + "--->" + spxgtkje);
    }


    @Test
    public void checkList() {
        List<String> rqList = DataprobiDateUtil.getBetweenDate("2025-06-01", "2025-06-22");
        int biInfoId = 13303;
        String zd = "spxgAllZfje";
//        String zd = "udzhtcjje";
//        String zd = "qytg_znyhqcjje";
        BiInfo biInfo = DataUploader.getBiinfoById(biInfoId);
        if (biInfo == null) {
            return;
        }
        double sum = 0;
        for (String rq : rqList) {
            String key = "bi_data/day/" + rq.replace("-", "") + "/" + biInfo.getYhid() + "/" + biInfo.getQd() + "/" + biInfo.getUserid() + "/sycm_dp.db";
            SqliteDp sqliteDp = sqliteDbUtil.readDp(key);
            if (sqliteDp == null) {
                continue;
            }

            String otherOrDefault = sqliteDp.getOtherOrDefault();
            JSONObject otherObj = JSONObject.parseObject(otherOrDefault);
            double zdHf = FastJSONObjAttrToNumber.toDouble(otherObj, zd);
            System.out.println(biInfo.getDpmc() + "-->" + zd + "，日期：" + rq + "--->" + zdHf);
            sum = MathUtil.add(sum, zdHf);
        }
        System.out.println("总计：" + sum);
    }

    @Test
    public void checkList2() {
        List<String> rqList = DataprobiDateUtil.getBetweenDate("2025-04-01", "2025-04-28");
        int biInfoId = 321;
        String zd = "spxgzfmjs（支付买家数）";
//        String zd = "udzhtcjje";
//        String zd = "qytg_znyhqcjje";
        BiInfo biInfo = DataUploader.getBiinfoById(biInfoId);
        if (biInfo == null) {
            return;
        }
        double sum = 0;
        for (String rq : rqList) {
            String key = "bi_data/day/" + rq.replace("-", "") + "/" + biInfo.getYhid() + "/" + biInfo.getQd() + "/" + biInfo.getUserid() + "/sycm_dp.db";
            SqliteDp sqliteDp = sqliteDbUtil.readDp(key);
            if (sqliteDp == null) {
                continue;
            }

            int spxgfks = sqliteDp.getSpxgzfmjs();
            System.out.println(biInfo.getDpmc() + "-->" + zd + "，日期：" + rq + "--->" + spxgfks);
//            System.out.println(biInfo.getDpmc() + "-->" + zd + "，日期：" + rq + "--->" + sqliteDp.getSpxgtkje());
            sum = MathUtil.add(sum, spxgfks);
        }
        System.out.println("总计：" + sum);
    }

    @Test
    public void checkSp() {
        int biInfoId = 12281;
        String bbid = "936470812943";
        String rq = "2025-06-18";
        String zd = "sptg_ysjm_hf";
        BiInfo biInfo = DataUploader.getBiinfoById(biInfoId);
        if (biInfo == null) {
            return;
        }
        String key = "bi_data/day/" + rq.replace("-", "") + "/" + biInfo.getYhid() + "/" + biInfo.getQd() + "/" + biInfo.getUserid() + "/sycm_sp.db";
        List<SqliteSp> sqliteSpList = sqliteDbUtil.readSpList(key);
        if (sqliteSpList == null) {
            return;
        }

        for (SqliteSp sqliteSp : sqliteSpList) {
            if (!sqliteSp.getBbid().equalsIgnoreCase(bbid)) {
                continue;
            }
            String otherOrDefault = sqliteSp.getOtherOrDefault();
            JSONObject otherObj = JSONObject.parseObject(otherOrDefault);
            double zdHf = FastJSONObjAttrToNumber.toDouble(otherObj, zd);
            System.out.println(biInfo.getDpmc() + "-->" + bbid + "---->" + zd + "，日期：" + rq + "--->" + zdHf);
        }
    }

    @Test
    public void checkSpDayAll() {
        int biInfoId = 50;
        String rq = "2025-06-12";
        String zd = "tyyhq";
        BiInfo biInfo = DataUploader.getBiinfoById(biInfoId);
        if (biInfo == null) {
            return;
        }
        String key = "bi_data/day/" + rq.replace("-", "") + "/" + biInfo.getYhid() + "/" + biInfo.getQd() + "/" + biInfo.getUserid() + "/sycm_sp.db";
        List<SqliteSp> sqliteSpList = sqliteDbUtil.readSpList(key);
        if (sqliteSpList == null) {
            return;
        }

        double sum = 0;
        for (SqliteSp sqliteSp : sqliteSpList) {
            String otherOrDefault = sqliteSp.getOtherOrDefault();
            JSONObject otherObj = JSONObject.parseObject(otherOrDefault);
            double zdHf = FastJSONObjAttrToNumber.toDouble(otherObj, zd);
            sum = MathUtil.addAbsScale(sum, zdHf, 8);
            if (zdHf != 0) {
                System.out.println(sqliteSp.getBbid() + "--->" + zdHf);
            }
        }
        System.out.println(biInfo.getDpmc() + "-->" + zd + "，日期：" + rq + "--->" + sum);
    }


    @Test
    public void checkSpRqListAll() {
        int biInfoId = 7989;
        List<String> rqlist = DataprobiDateUtil.getBetweenDate("2025-06-01", "2025-06-30");
        BiInfo biInfo = DataUploader.getBiinfoById(biInfoId);
        if (biInfo == null) {
            return;
        }
        String zd = "dsphf";
        double sum = 0;
        for (String rq : rqlist) {
            double dayhf = 0;
            String key = "bi_data/day/" + rq.replace("-", "") + "/" + biInfo.getYhid() + "/" + biInfo.getQd() + "/" + biInfo.getUserid() + "/sycm_sp.db";
            List<SqliteSp> sqliteSpList = sqliteDbUtil.readSpList(key);
            if (sqliteSpList == null) {
                return;
            }

            for (SqliteSp sqliteSp : sqliteSpList) {
                if(!sqliteSp.getBbid().equals("830642235050")){
                    continue;
                }
                String otherOrDefault = sqliteSp.getOtherOrDefault();
                JSONObject otherObj = JSONObject.parseObject(otherOrDefault);
                double zdHf = FastJSONObjAttrToNumber.toDouble(otherObj, zd);
                if (zdHf > 0) {
                    System.out.println(sqliteSp.getBbid() + "--->" + rq + "--->" + zdHf);
                }
                sum = MathUtil.addAbsScale(sum, zdHf, 8);
                dayhf = MathUtil.addAbsScale(dayhf, zdHf, 8);
            }
            System.out.println(biInfo.getDpmc() + "-->" + zd + "，日期：" + rq + "--->" + dayhf);
        }

        System.out.println(biInfo.getDpmc() + "-->" + zd + "--->" + sum);
    }


    @Test
    public void checkSpRqListAll2() {
        int biInfoId = 522;
        List<String> rqlist = DataprobiDateUtil.getBetweenDate("2025-03-01", "2025-03-10");
        BiInfo biInfo = DataUploader.getBiinfoById(biInfoId);
        if (biInfo == null) {
            return;
        }
        String bbid = "3724835966385520799";
        String zd = "spxgZydspcjdds";
        double sum = 0;
        for (String rq : rqlist) {
            double dayhf = 0;
            String key = "bi_data/day/" + rq.replace("-", "") + "/" + biInfo.getYhid() + "/" + biInfo.getQd() + "/" + biInfo.getUserid() + "/sycm_sp.db";
            List<SqliteSp> sqliteSpList = sqliteDbUtil.readSpList(key);
            if (sqliteSpList == null) {
                return;
            }

            for (SqliteSp sqliteSp : sqliteSpList) {
                if (!sqliteSp.getBbid().equalsIgnoreCase(bbid)) {
                    continue;
                }
                String otherOrDefault = sqliteSp.getOtherOrDefault();
                JSONObject otherObj = JSONObject.parseObject(otherOrDefault);
                double zdHf = FastJSONObjAttrToNumber.toDouble(otherObj, zd);
                sum = MathUtil.add(sum, zdHf);
                dayhf = MathUtil.add(dayhf, zdHf);
            }
            System.out.println(biInfo.getDpmc() + "-->" + zd + "，日期：" + rq + "--->" + dayhf);
        }

        System.out.println(biInfo.getDpmc() + "-->" + zd + "--->" + sum);
    }


    @Test
    public void checkQd() {
        String rq = "2025-05-18";
        String zd = "qytghf";
//        String zd = "dsphf";
        List<BiInfo> allBiInfo = DataUploader.getAllBiInfo();
        for (BiInfo biInfo : allBiInfo) {
            if (biInfo.getQd().equals("DY") ) {
                String key = "bi_data/day/" + rq.replace("-", "") + "/" + biInfo.getYhid() + "/" + biInfo.getQd() + "/" + biInfo.getUserid() + "/sycm_dp.db";
                SqliteDp sqliteDp = sqliteDbUtil.readDp(key);
                if (sqliteDp != null) {

                    String otherOrDefault = sqliteDp.getOtherOrDefault();
//                    double zdHf = sqliteDp.getSpxgzfje();
                    JSONObject otherObj = JSONObject.parseObject(otherOrDefault);
                    double zdHf = FastJSONObjAttrToNumber.toDouble(otherObj, zd);
                    if (zdHf > 0) {
                        System.out.println(biInfo.getDpmc() + "-->" + zd + "，日期：" + rq + "--->" + zdHf);
                    }
                }
            }
        }
    }
}
