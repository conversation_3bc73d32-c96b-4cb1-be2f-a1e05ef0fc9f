package com.abujlb.dataprobi.test;

import com.abujlb.AbujlbBeanFactory;
import com.abujlb.BaseTest;
import com.abujlb.dataprobi.bean.BiInfo;
import com.abujlb.dataprobi.bean.pdd.DataprobiPddMsgBean;
import com.abujlb.dataprobi.processes.pdd.*;
import com.abujlb.dataprobi.server.DataUploader;
import com.abujlb.dataprobi.service.BiPddDataDealService;
import com.abujlb.dataprobi.threads.BiThreadLocals;
import com.abujlb.dataprobi.util.DataprobiDateUtil;
import com.abujlb.mq.AliyunMq2;
import com.abujlb.mq.JobMessage;
import com.aliyun.openservices.ons.api.SendResult;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/9/28 10:38
 */
public class TestBiPddProcess extends BaseTest {

    @Autowired
    private AbujlbBeanFactory abujlbBeanFactory;

    @Autowired
    private AliyunMq2 aliyunMq2;

    private final DataprobiPddMsgBean dataprobiPddMsgBean;

    {
        //10944
        final int biinfoId = 3382;
        BiInfo biInfo = DataUploader.getBiinfoById(biinfoId);
        if (biInfo == null) {
            throw new RuntimeException("获取Biinfo失败，Biinfo的ID为：" + biinfoId);
        }
        dataprobiPddMsgBean = new DataprobiPddMsgBean();
        dataprobiPddMsgBean.setUserid(biInfo.getUserid());
        dataprobiPddMsgBean.setYhid(biInfo.getYhid());
        dataprobiPddMsgBean.setQd(biInfo.getQd());
        dataprobiPddMsgBean.setDpmc(biInfo.getDpmc());
        dataprobiPddMsgBean.setBiinfoId(biInfo.getId());
        dataprobiPddMsgBean.setType(1);
        dataprobiPddMsgBean.setSplb(1);
        dataprobiPddMsgBean.setDpMonths(Collections.emptyList());

        List<String> rqList = Collections.emptyList();
//        List<String> rqList = Collections.singletonList("2024-11-27");
        List<String> rqList2 = DataprobiDateUtil.getBetweenDate("2025-03-01", "2025-05-28");
//        List<String> rqList2 = DataprobiDateUtil.getBetweenDate("2024-02-01", "2024-02-26");
        //List<String> rqList2 = Collections.singletonList("2025-01-15");
        dataprobiPddMsgBean.setSpxgdp(rqList);
        dataprobiPddMsgBean.setSpxg(rqList);
        dataprobiPddMsgBean.setDdjbdp(rqList);
        dataprobiPddMsgBean.setDdjb(rqList);
        dataprobiPddMsgBean.setDdss(rqList);
        dataprobiPddMsgBean.setDdssdp(rqList);
        dataprobiPddMsgBean.setDdcj(rqList);
        dataprobiPddMsgBean.setDdcjdp(rqList);
        dataprobiPddMsgBean.setQztg(rqList);
        dataprobiPddMsgBean.setQztgtgfx(rqList);
        dataprobiPddMsgBean.setQztgdp(rqList);
        dataprobiPddMsgBean.setMxdp(rqList);
        dataprobiPddMsgBean.setZbtg(rqList);
        dataprobiPddMsgBean.setSppj(rqList);
        dataprobiPddMsgBean.setSptg(rqList);
        dataprobiPddMsgBean.setSptgdp(rqList);
        dataprobiPddMsgBean.setPjyl(rqList);
        dataprobiPddMsgBean.setZhyxdp(rqList);
        dataprobiPddMsgBean.setBztg(rqList);
        dataprobiPddMsgBean.setBztgtgfx(rqList);
        dataprobiPddMsgBean.setBztgdp(rqList);
        dataprobiPddMsgBean.setXkfx(rqList);
        dataprobiPddMsgBean.setJlgg(rqList);
        dataprobiPddMsgBean.setBztgtgfx(rqList);
        dataprobiPddMsgBean.setQztgtgfx(rqList);
        dataprobiPddMsgBean.setSptgtgfx(rqList);
        dataprobiPddMsgBean.setHbdk(rqList);
        dataprobiPddMsgBean.setKfjxfx(rqList2);
        BiThreadLocals.init(dataprobiPddMsgBean);
    }

    /**
     * 多多场景单元测试
     */
    @Test
    public void ddcj() {
        DefaultBiPddDdcjDealProcess bean = abujlbBeanFactory.getBean(DefaultBiPddDdcjDealProcess.class);
        bean.dealGoods();
        bean.dealShop();
    }

    /**
     * 多多进宝单元测试
     */
    @Test
    public void ddjb() {
        DefaultBiPddDdjbDealProcess bean = abujlbBeanFactory.getBean(DefaultBiPddDdjbDealProcess.class);
        bean.dealGoods();
        bean.dealShop();
    }

    /**
     * 多多搜索单元测试
     */
    @Test
    public void ddss() {
        DefaultBiPddDdssDealProcess bean = abujlbBeanFactory.getBean(DefaultBiPddDdssDealProcess.class);
        bean.dealGoods();
        bean.dealShop();
    }

    /**
     * 明星店铺单元测试
     */
    @Test
    public void mxdp() {
        DefaultBiPddMxdpDealProcess bean = abujlbBeanFactory.getBean(DefaultBiPddMxdpDealProcess.class);
        bean.dealShop();
    }

    /**
     * 全站推广单元测试
     */
    @Test
    public void qztg() {
        DefaultBiPddQztgDealProcess bean = abujlbBeanFactory.getBean(DefaultBiPddQztgDealProcess.class);
        bean.dealGoods();
        bean.dealShop();
    }

    /**
     * 商品列表单元测试
     */
    @Test
    public void splb() {
        BiThreadLocals.getMsgBean().setSplb(1);
        DefaultBiPddSplbDealProcess bean = abujlbBeanFactory.getBean(DefaultBiPddSplbDealProcess.class);
        bean.dealGoods();
    }

    /**
     * 商品效果单元测试
     */
    @Test
    public void spxg() {
        DefaultBiPddSpxgDealProcess bean = abujlbBeanFactory.getBean(DefaultBiPddSpxgDealProcess.class);
        bean.dealGoods();
        bean.dealShop();
    }

    /**
     * 直播推广单元测试
     */
    @Test
    public void zbtg() {
        DefaultBiPddZbtgDealProcess bean = abujlbBeanFactory.getBean(DefaultBiPddZbtgDealProcess.class);
        bean.dealShop();
    }

    @Test
    public void zhyx() {
        DefaultBiPddZhyxDealProcess bean = abujlbBeanFactory.getBean(DefaultBiPddZhyxDealProcess.class);
        bean.dealShop();
    }


    @Test
    public void jlgg() {
        ((DataprobiPddMsgBean) BiThreadLocals.getMsgBean()).setJlgg(Collections.singletonList("2023-09-01"));
        DefaultBiPddJlggDealProcess bean = abujlbBeanFactory.getBean(DefaultBiPddJlggDealProcess.class);
        bean.dealShop();
    }

    @Test
    public void bztg() {
        DefaultBiPddBztgDealProcess defaultBiPddBztgDealProcess = abujlbBeanFactory.getBean(DefaultBiPddBztgDealProcess.class);
        defaultBiPddBztgDealProcess.dealGoods();
        defaultBiPddBztgDealProcess.dealShop();
    }

    @Test
    public void sptg() {
        DefaultBiPddSptgDealProcess defaultBiPddSptgDealProcess = abujlbBeanFactory.getBean(DefaultBiPddSptgDealProcess.class);
        defaultBiPddSptgDealProcess.dealGoods();
//        defaultBiPddSptgDealProcess.dealShop();
    }

    @Test
    public void pjyl() {
        DefaultBiPddPjylProcess defaultBiPddPjylProcess = abujlbBeanFactory.getBean(DefaultBiPddPjylProcess.class);
        defaultBiPddPjylProcess.dealGoods();
    }

    @Test
    public void hbdk() {
        DefaultBiPddHbdkDealProcess defaultBiPddHbdkDealProcess = abujlbBeanFactory.getBean(DefaultBiPddHbdkDealProcess.class);
        defaultBiPddHbdkDealProcess.dealShop();
    }
    /**
     * 本地测试BiPddDataDealService
     */
    @Test
    public void runLocal() {
        BiPddDataDealService biPddDataDealService = abujlbBeanFactory.getBean(BiPddDataDealService.class);
        biPddDataDealService.process();
    }


    /**
     * dataprobi消息发送
     */
    @Test
    public void sendMq() {
        SendResult sendResult = aliyunMq2.send("dataprobiv2_jst", "", new JobMessage("dataprobi_v2_pdd_processor", dataprobiPddMsgBean));
        System.out.println(dataprobiPddMsgBean.getDpmc() + "dataprobi消息发送成功，msgId：" + sendResult.getMessageId());
    }
}
