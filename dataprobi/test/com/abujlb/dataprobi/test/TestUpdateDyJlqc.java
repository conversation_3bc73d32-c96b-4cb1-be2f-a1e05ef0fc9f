package com.abujlb.dataprobi.test;

import com.abujlb.BaseTest;
import com.abujlb.CommonConfig;
import com.abujlb.Result;
import com.abujlb.service.ServiceClient;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;

import java.util.Arrays;
import java.util.Iterator;
import java.util.List;

public class TestUpdateDyJlqc extends BaseTest {

    private static final ServiceClient serviceClient;
    private static final String serviceUrl;

    static {
        serviceClient = new ServiceClient(CommonConfig.getString("jyrbAppid"), CommonConfig.getString("jyrbAppkey"));
        serviceUrl = CommonConfig.getString("jyrbServiceUrl");
    }

    @Test
    public void test() {
        String s = serviceClient.exec(serviceUrl + "/bi2/getAllBiInfos", new Object());
        if (Result.isTrue(s)) {
            JSONObject jsonObject = JSONObject.parseObject(s);
            JSONArray data = jsonObject.getJSONArray("data");
//            updateBiinfo(data);
            updateBiinfoByDp(data);
        }
    }

    private void updateBiinfo(JSONArray jsonArray) {
        Iterator<Object> iterator = jsonArray.stream().iterator();

        while (iterator.hasNext()) {
            JSONObject biInfo = (JSONObject) iterator.next();
            if (biInfo.getString("qd").equals("DY")) {
                String rqjson = biInfo.getString("rqjson");
                if (StringUtils.isBlank(rqjson)) {
                    continue;
                }
                JSONObject jsonObject = JSONObject.parseObject(rqjson);

                int yhid = biInfo.getIntValue("yhid");
                if (yhid != 7981) {
                    continue;
                }
                if (jsonObject.containsKey("dsp")) {
                    jsonObject.put("dsp", "2023-10-31");
                }
                if (jsonObject.containsKey("dr_zy_ppdp")) {
                    jsonObject.put("dr_zy_ppdp", "2023-10-31");
                }
                if (jsonObject.containsKey("zbdp")) {
                    jsonObject.put("zbdp", "2023-10-31");
                }
                if (jsonObject.containsKey("qytgdp")) {
                    jsonObject.put("qytgdp", "2023-10-31");
                }
                if (jsonObject.containsKey("dr_zy_qytgdp")) {
                    jsonObject.put("dr_zy_qytgdp", "2023-10-31");
                }
                if (jsonObject.containsKey("dr_zy_tzbjdp")) {
                    jsonObject.put("dr_zy_tzbjdp", "2023-10-31");
                }
                if (jsonObject.containsKey("dr_zy_tspdp")) {
                    jsonObject.put("dr_zy_tspdp", "2023-10-31");
                }
                if (jsonObject.containsKey("cwjldp")) {
                    jsonObject.put("cwjldp", "2023-10-31");
                }
                if (jsonObject.containsKey("ppdp")) {
                    jsonObject.put("ppdp", "2023-10-31");
                }
                if (jsonObject.containsKey("dspdp")) {
                    jsonObject.put("dspdp", "2023-10-31");
                }
                System.out.println("update bi_info set rqjson='" + jsonObject.toJSONString() + "'  where id = " + biInfo.getString("id") + "; ");
            }
        }
    }


    private void updateBiinfoByDp(JSONArray jsonArray) {
        Iterator<Object> iterator = jsonArray.stream().iterator();

        List<String> list = Arrays.asList("113209207", "28324209");
        while (iterator.hasNext()) {
            JSONObject biInfo = (JSONObject) iterator.next();
            if (biInfo.getString("qd").equals("DY")) {
                String rqjson = biInfo.getString("rqjson");
                if (StringUtils.isBlank(rqjson)) {
                    continue;
                }
                JSONObject jsonObject = JSONObject.parseObject(rqjson);

                String userid = biInfo.getString("userid");
                if (!list.contains(userid)) {
                    continue;
                }
                if (jsonObject.containsKey("dsp")) {
                    jsonObject.put("dsp", "2023-12-17");
                }
                if (jsonObject.containsKey("dr_zy_ppdp")) {
                    jsonObject.put("dr_zy_ppdp", "2023-12-17");
                }
                if (jsonObject.containsKey("zbdp")) {
                    jsonObject.put("zbdp", "2023-12-17");
                }
                if (jsonObject.containsKey("qytgdp")) {
                    jsonObject.put("qytgdp", "2023-12-17");
                }
                if (jsonObject.containsKey("dr_zy_qytgdp")) {
                    jsonObject.put("dr_zy_qytgdp", "2023-12-17");
                }
                if (jsonObject.containsKey("dr_zy_tzbjdp")) {
                    jsonObject.put("dr_zy_tzbjdp", "2023-12-17");
                }
                if (jsonObject.containsKey("dr_zy_tspdp")) {
                    jsonObject.put("dr_zy_tspdp", "2023-12-17");
                }
                if (jsonObject.containsKey("cwjldp")) {
                    jsonObject.put("cwjldp", "2023-12-17");
                }
                if (jsonObject.containsKey("ppdp")) {
                    jsonObject.put("ppdp", "2023-12-17");
                }
                if (jsonObject.containsKey("dspdp")) {
                    jsonObject.put("dspdp", "2023-12-17");
                }
                System.out.println("update bi_info set rqjson='" + jsonObject.toJSONString() + "'  where id = " + biInfo.getString("id") + "; ");
            }
        }
    }

}
