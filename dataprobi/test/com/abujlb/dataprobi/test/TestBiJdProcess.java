package com.abujlb.dataprobi.test;

import com.abujlb.AbujlbBeanFactory;
import com.abujlb.BaseTest;
import com.abujlb.dataprobi.bean.BiInfo;
import com.abujlb.dataprobi.bean.BiYhdp;
import com.abujlb.dataprobi.bean.jd.DataprobiJdMsgBean;
import com.abujlb.dataprobi.processes.jd.*;
import com.abujlb.dataprobi.server.DataUploader;
import com.abujlb.dataprobi.service.BiJdDataDealService;
import com.abujlb.dataprobi.threads.BiThreadLocals;
import com.abujlb.dataprobi.util.DataprobiDateUtil;
import com.abujlb.mq.AliyunMq2;
import com.abujlb.mq.JobMessage;
import com.aliyun.openservices.ons.api.SendResult;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.lang.reflect.InvocationTargetException;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/9/28 10:38
 */
public class TestBiJdProcess extends BaseTest {

    @Autowired
    private AbujlbBeanFactory abujlbBeanFactory;

    @Autowired
    private AliyunMq2 aliyunMq2;

    private final DataprobiJdMsgBean dataprobiJdMsgBean;

    {
        final int biinfoId = 1495;
        BiInfo biInfo = DataUploader.getBiinfoById(biinfoId);
        if (biInfo == null) {
            throw new RuntimeException("获取Biinfo失败，Biinfo的ID为：" + biinfoId);
        }


        dataprobiJdMsgBean = new DataprobiJdMsgBean();
        dataprobiJdMsgBean.setUserid(biInfo.getUserid());
        dataprobiJdMsgBean.setYhid(biInfo.getYhid());
        dataprobiJdMsgBean.setQd(biInfo.getQd());
        dataprobiJdMsgBean.setDpmc(biInfo.getDpmc());
        dataprobiJdMsgBean.setBiinfoId(biInfo.getId());
        dataprobiJdMsgBean.setType(1);

//        List<String> rqList = Collections.singletonList("2025-05-29");
        List<String> rqList = Collections.emptyList();
//        List<String> rqList = DataprobiDateUtil.getBetweenDate("2025-03-01", "2025-03-16");
//        List<String> rqList2 =  DataprobiDateUtil.getBetweenDate("2025-05-01", "2025-06-03");
//        List<String> rqList2 = Collections.singletonList("2025-05-29");
        List<String> rqList2 = Collections.emptyList();
//        List<String> rqList2 = Arrays.asList("2024-04-12", "2024-04-22");


//        List<String> rqList3 = Collections.singletonList("2025-05-29");
//        List<String> rqList3 = Collections.singletonList("2024-06-01");
//        List<String> rqList3 = Collections.singletonList("2024-06-30");
//        List<String> rqList3 = Collections.singletonList("2024-06-04");
//        List<String> rqList3 = Arrays.asList("2024-05-31", "2024-06-30","2024-07-04");
//        List<String> rqList3 = Arrays.asList("2024-06-01", "2024-06-02");
//        List<String> rqList3 = Collections.singletonList("2025-03-11");
        List<String> rqList3 =DataprobiDateUtil.getBetweenDate("2025-05-01", "2025-06-03");
//        List<String> rqList3 = Collections.emptyList();
//        List<String> rqList4 = Collections.singletonList("2024-12-03");
//        List<String> rqList4 = DataprobiDateUtil.getBetweenDate("2024-09-01", "2024-10-13");
//        dataprobiJdMsgBean.setSpxg(rqList);
        dataprobiJdMsgBean.setSpxgdp(rqList);
        dataprobiJdMsgBean.setJdou(rqList);
        dataprobiJdMsgBean.setBt(rqList);
        dataprobiJdMsgBean.setSplbs(rqList);
        dataprobiJdMsgBean.setFcs(rqList);
        dataprobiJdMsgBean.setFbporder(rqList);
        dataprobiJdMsgBean.setFbpspmx(rqList);
        dataprobiJdMsgBean.setFbpspyj(rqList);
        dataprobiJdMsgBean.setXkfx(rqList);
        dataprobiJdMsgBean.setTgfx(rqList);
        dataprobiJdMsgBean.setFbpjyzkspmx(rqList);
        dataprobiJdMsgBean.setOrders(rqList);
        dataprobiJdMsgBean.setWyj(rqList);
        dataprobiJdMsgBean.setPyby(rqList);

        dataprobiJdMsgBean.setJdkc(rqList);
        dataprobiJdMsgBean.setJdkcdp(rqList);
        dataprobiJdMsgBean.setJdhtdp(rqList2);
        dataprobiJdMsgBean.setJdht(rqList2);
        dataprobiJdMsgBean.setJtk(rqList3);
        dataprobiJdMsgBean.setJtkdp(rqList3);
        dataprobiJdMsgBean.setGwcddp(rqList2);
        dataprobiJdMsgBean.setGwcd(rqList2);
        dataprobiJdMsgBean.setJdzwdp(rqList2);
        dataprobiJdMsgBean.setJdzw(rqList2);
        dataprobiJdMsgBean.setJdztdp(rqList2);
        dataprobiJdMsgBean.setJdzt(rqList2);
        dataprobiJdMsgBean.setJskdp(rqList2);
        dataprobiJdMsgBean.setJrwdp(rqList2);
        dataprobiJdMsgBean.setXfjl(rqList2);
        dataprobiJdMsgBean.setQzyx(rqList2);
        dataprobiJdMsgBean.setQzyxtgfx(rqList2);
        dataprobiJdMsgBean.setNrgg(rqList);
        dataprobiJdMsgBean.setNrggdp(rqList);


        try {
            dataprobiJdMsgBean.fillNullList();
        } catch (InvocationTargetException e) {
            throw new RuntimeException(e);
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        }

        BiThreadLocals.init(dataprobiJdMsgBean);

        BiThreadLocals.addBiInfo(biInfo);
    }


    @Test
    public void spxg() {
        DefaultBiJdSpxgProcess bean = abujlbBeanFactory.getBean(DefaultBiJdSpxgProcess.class);
//        bean.dealGoods();
        bean.dealShop();
    }

    @Test
    public void bt() {
        DefaultBiJdBtProcess bean = abujlbBeanFactory.getBean(DefaultBiJdBtProcess.class);
        bean.dealGoods();
    }

    @Test
    public void gwcd() {
        DefaultBiJdGwcdProcess bean = abujlbBeanFactory.getBean(DefaultBiJdGwcdProcess.class);
        bean.dealGoods();
        bean.dealShop();
    }

    @Test
    public void jdkc() {
        DefaultBiJdJdkcProcess bean = abujlbBeanFactory.getBean(DefaultBiJdJdkcProcess.class);
        bean.dealGoods();
        bean.dealShop();
    }

    @Test
    public void jdou() {
        DefaultBiJdJdouProcess bean = abujlbBeanFactory.getBean(DefaultBiJdJdouProcess.class);
        bean.dealGoods();
    }

    @Test
    public void jdzt() {
        DefaultBiJdJdztProcess bean = abujlbBeanFactory.getBean(DefaultBiJdJdztProcess.class);
        bean.dealGoods();
        bean.dealShop();
    }

    @Test
    public void jdzw() {
        DefaultBiJdJdzwProcess bean = abujlbBeanFactory.getBean(DefaultBiJdJdzwProcess.class);
        bean.dealGoods();
        bean.dealShop();
    }

    @Test
    public void jrw() {
        DefaultBiJdJrwProcess bean = abujlbBeanFactory.getBean(DefaultBiJdJrwProcess.class);
        bean.dealShop();
    }

    @Test
    public void jsk() {
        DefaultBiJdJskProcess bean = abujlbBeanFactory.getBean(DefaultBiJdJskProcess.class);
        bean.dealShop();
    }

    @Test
    public void jst() {
        DefaultBiJdJstProcess bean = abujlbBeanFactory.getBean(DefaultBiJdJstProcess.class);
        bean.dealGoods();
        bean.dealShop();
    }

    @Test
    public void spyj_cgj() {
        DefaultBiJdCgjProcess bean = abujlbBeanFactory.getBean(DefaultBiJdCgjProcess.class);
        bean.dealGoods();
//        System.out.println(bean.compareGoods());
    }

    @Test
    public void jtkZd() {
        DefaultBiJdJtkProcess bean = abujlbBeanFactory.getBean(DefaultBiJdJtkProcess.class);
        bean.dealGoods();
//        bean.dealShop();
    }

    @Test
    public void jtkYg() {
        DefaultBiJdJtkygProcess bean = abujlbBeanFactory.getBean(DefaultBiJdJtkygProcess.class);
//        bean.dealGoods();
        bean.dealShop();
    }


//    @Test
//    public void xfjl() {
//        DefaultBiJdXfjlProcess defaultBiJdXfjlProcess = abujlbBeanFactory.getBean(DefaultBiJdXfjlProcess.class);
//        defaultBiJdXfjlProcess.dealShop();
////        defaultBiJdXfjlProcess.compareShop();
//    }

    @Test
    public void jyzkspmx() {
        DefaultBiJdSpmxProcess defaultBiJdSpmxProcess = abujlbBeanFactory.getBean(DefaultBiJdSpmxProcess.class);
        defaultBiJdSpmxProcess.dealGoods();
    }

    @Test
    public void djl() {
        DefaultBiJdJztDjlProcess defaultBiJdJztDjlProcess = abujlbBeanFactory.getBean(DefaultBiJdJztDjlProcess.class);
        defaultBiJdJztDjlProcess.dealGoods();
    }

    @Test
    public void qzyx() {
        DefaultBiJdQzyxProcess defaultBiJdQzyxProcess = abujlbBeanFactory.getBean(DefaultBiJdQzyxProcess.class);
        defaultBiJdQzyxProcess.dealGoods();
//        defaultBiJdQzyxProcess.compareGoods();
    }

    @Test
    public void qzyxSpu() {
        DefaultBiJdQzyxSpuProcess defaultBiJdQzyxProcess = abujlbBeanFactory.getBean(DefaultBiJdQzyxSpuProcess.class);
        defaultBiJdQzyxProcess.dealGoods();
    }

    @Test
    public void qzyxDp() {
        DefaultBiJdQzyxDpProcess defaultBiJdQzyxProcess = abujlbBeanFactory.getBean(DefaultBiJdQzyxDpProcess.class);
        defaultBiJdQzyxProcess.dealShop();
//        defaultBiJdQzyxProcess.compareShop();
    }

    @Test
    public void qzyxSpubanskuid() {
        DefaultBiJdQzyxSpuBanSkuidsProcess defaultBiJdQzyxSpuBanSkuidsProcess = abujlbBeanFactory.getBean(DefaultBiJdQzyxSpuBanSkuidsProcess.class);
        defaultBiJdQzyxSpuBanSkuidsProcess.dealGoods();
    }

    @Test
    public void nrgg() {
        DefaultBiJdNrggProcess defaultBiJdNrggProcess = abujlbBeanFactory.getBean(DefaultBiJdNrggProcess.class);
        defaultBiJdNrggProcess.dealGoods();
        defaultBiJdNrggProcess.dealShop();
    }

    @Test
    public void runLocal() {
        BiThreadLocals.getMsgBean().setType(1);
        BiJdDataDealService biJdDataDealService = abujlbBeanFactory.getBean(BiJdDataDealService.class);
        biJdDataDealService.process();
    }

    @Test
    public void sendMq() {
        SendResult sendResult = aliyunMq2.send("dataprobiv2_jst", dataprobiJdMsgBean.getUuid(), new JobMessage("dataprobi_v2_jd_processor", dataprobiJdMsgBean));
        System.out.println(dataprobiJdMsgBean.getDpmc() + "消息发送成功，消息msgId：" + sendResult.getMessageId());
    }

    public static void main(String[] args) {
        List<BiInfo> allBiInfo = DataUploader.getAllBiInfo();
        if (CollectionUtils.isEmpty(allBiInfo)) {
            return;
        }
        for (BiInfo biInfo : allBiInfo) {
            if (biInfo == null) {
                return;
            }
            if (DataprobiDateUtil.getLastday().equals(biInfo.getSycmcljzrq())) {
                continue;
            }

            BiYhdp biyhdp = DataUploader.getBiYhdpByYhidAndUseridQd(biInfo.getUserid(), biInfo.getQd(), biInfo.getYhid(), 0);
            if (biyhdp != null && biyhdp.getSycmcljzrq() != null && biyhdp.getSycmcljzrq().equals(DataprobiDateUtil.getLastday())) {
                biInfo.setSycmcljzrq(DataprobiDateUtil.getLastday());
                DataUploader.updateBiInfoSycmcljzrq(biInfo);
            }
        }
    }

}
