package com.abujlb.dataprobi.test;

import com.abujlb.AbujlbBeanFactory;
import com.abujlb.BaseTest;
import com.abujlb.dataprobi.bean.BiInfo;
import com.abujlb.dataprobi.bean.tb.DataprobiMsg;
import com.abujlb.dataprobi.oss.BiDataOssUtil;
import com.abujlb.dataprobi.processes.tb.*;
import com.abujlb.dataprobi.server.DataUploader;
import com.abujlb.dataprobi.service.BiDataDealService;
import com.abujlb.dataprobi.threads.BiThreadLocals;
import com.abujlb.dataprobi.util.DataprobiDateUtil;
import com.abujlb.mq.AliyunMq2;
import com.abujlb.mq.JobMessage;
import com.aliyun.openservices.ons.api.SendResult;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2022/8/31 9:15
 */
public class TestBiProcess extends BaseTest {

    @Autowired
    private AbujlbBeanFactory abujlbBeanFactory;

    @Autowired
    private BiDataDealService biDataDealService;

    @Autowired
    private AliyunMq2 aliyunMq2;

    @Autowired
    private BiDataOssUtil biDataOssUtil;

    private final DataprobiMsg dataprobiMsg;

    {
        final int biinfoId = 13157;
        BiInfo biInfo = DataUploader.getBiinfoById(biinfoId);
        if (biInfo == null) {
            throw new RuntimeException("获取Biinfo失败，Biinfo的ID为：" + biinfoId);
        }

        dataprobiMsg = new DataprobiMsg();
        dataprobiMsg.setUserid(biInfo.getUserid());
        dataprobiMsg.setYhid(biInfo.getYhid());
        dataprobiMsg.setQd(biInfo.getQd());
        dataprobiMsg.setDpmc(biInfo.getDpmc());
        dataprobiMsg.setBiinfoId(biInfo.getId());
        dataprobiMsg.setType(2);
        dataprobiMsg.setAuto(0);
        dataprobiMsg.setDpMonths(Collections.emptyList());
//        dataprobiMsg.setDpMonths(Collections.singletonList("2023-12-31"));
        dataprobiMsg.setSplb(0);
        dataprobiMsg.setAiztone(1);

        List<String> rqList = Collections.emptyList();
//        List<String> rqList = new ArrayList<>();
//        rqList.add("2025-06-08") ;
//        List<String> rqList = Collections.singletonList("2025-01-14");
//        List<String> rqList = Collections.singletonList("2024-12-12");
//        List<String> rqList = DataprobiDateUtil.getBetweenDate("2024-12-01","2025-01-10");
//        List<String> rqList2 =Collections.singletonList("2024-12-18");
//        List<String> rqList2 =Collections.emptyList();
        List<String> rqList2 =DataprobiDateUtil.getBetweenDate("2025-07-08","2025-07-09");
//        List<String> rqList2 = Collections.singletonList("2024-07-06");
//        List<String> rqList3 =Collections.singletonList("2025-05-01");
//        List<String> rqList3 = DataprobiDateUtil.getBetweenDate("2025-05-01", "2025-06-18");
        List<String> rqList3 =new ArrayList<>();
        rqList3.add("2025-06-02");
//        rqList3.add("2025-05-10");
//        List<String> rqList3 = DataprobiDateUtil.getBetweenDate("2025-05-01","2025-05-28");


        dataprobiMsg.setSpxg(rqList);
        dataprobiMsg.setSpxgdp(rqList);
        dataprobiMsg.setQztg(rqList);
        dataprobiMsg.setQztg_tgfx(rqList);
        dataprobiMsg.setZtc(rqList);
        dataprobiMsg.setAizt(rqList);
        dataprobiMsg.setYlmf(rqList);
        dataprobiMsg.setAizt_item(rqList);
        dataprobiMsg.setPxb(rqList);
        dataprobiMsg.setRlyq(rqList);
        dataprobiMsg.setRlyqdp(rqList);
        dataprobiMsg.setXedk(rqList);
        dataprobiMsg.setPpxx(rqList);
        dataprobiMsg.setPpxxdp(rqList);
        dataprobiMsg.setNewitem(rqList);
        dataprobiMsg.setYhq(rqList);
        dataprobiMsg.setJhs(rqList);
        dataprobiMsg.setTbxjhb(rqList);
        dataprobiMsg.setNryxdp(rqList3);
        dataprobiMsg.setAizt_tgfx(rqList);
        dataprobiMsg.setYlmf_tgfx(rqList);
        dataprobiMsg.setSpsjhz(rqList);
        dataprobiMsg.setZtc_tgfx(rqList);
        dataprobiMsg.setZtc_crowd_tgfx(rqList);
        dataprobiMsg.setZtc_keyword_tgfx(rqList);
        dataprobiMsg.setXkfx(rqList);
        dataprobiMsg.setDmbzt(rqList);
        dataprobiMsg.setFwCustomer(rqList);
        dataprobiMsg.setNryxdsp(rqList);
        dataprobiMsg.setTbk(rqList);
        dataprobiMsg.setTbkdp(rqList);
        dataprobiMsg.setSycmlevel(rqList);
        dataprobiMsg.setSycmflow(rqList);
        dataprobiMsg.setLlzhymfx(rqList);
        dataprobiMsg.setPpxxYghf(rqList);
        dataprobiMsg.setDmbzt_tgfx(rqList);
        dataprobiMsg.setDpmhpdcqddplb(rqList);
        dataprobiMsg.setPjyl(rqList);
        dataprobiMsg.setAiztoneztcitemdjlv(rqList);
        dataprobiMsg.setPpxxYghf(rqList);
        dataprobiMsg.setQndpzhtyf(rqList2);
        dataprobiMsg.setXstg(rqList);
        dataprobiMsg.setSycmghtwdp(rqList);
        dataprobiMsg.setSycmghtw(rqList);
        dataprobiMsg.setSycmghsp(rqList);
        dataprobiMsg.setSycmghspdp(rqList);
        dataprobiMsg.setAiztone_dpsummary(rqList);
        dataprobiMsg.setUdzht(rqList);
        dataprobiMsg.setPpxx_yxtg_xp(rqList);
        dataprobiMsg.setPpxx_yxtg_xk(rqList);
        dataprobiMsg.setQyyxtg(rqList);
        dataprobiMsg.setYlmf_cxjh(rqList);
        dataprobiMsg.setVideoItemRela(rqList);
        dataprobiMsg.setNryx_cjzb_qztg(rqList);


        dataprobiMsg.setAizt_shop_dpzd(rqList);
        dataprobiMsg.setAizt_shop(rqList);

//        dataprobiMsg.setMsyqJlgg(Lists.newArrayList("2024-09-25"));
        try {
            dataprobiMsg.fillNullList();
        } catch (InvocationTargetException e) {
            throw new RuntimeException(e);
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        }
        BiThreadLocals.init(dataprobiMsg);
        BiThreadLocals.addBiInfo(biInfo);
    }

    @Test
    public void aizt() {
        dataprobiMsg.setAiztone(1);
        DefaultBiAdbrainOneAiztProcess bean = abujlbBeanFactory.getBean(DefaultBiAdbrainOneAiztProcess.class);
        bean.dealGoods();
//        bean.dealShop();
    }

    @Test
    public void pxb() {
        DefaultBiPxbDealProcess bean = abujlbBeanFactory.getBean(DefaultBiPxbDealProcess.class);
        bean.dealGoods();
    }


    @Test
    public void spxg() {
        DefaultBiSpxgDealProcess bean = abujlbBeanFactory.getBean(DefaultBiSpxgDealProcess.class);
//        bean.dealGoods();
//        bean.dealShop();
        bean.compareShop();
    }

    public static void main(String[] args) {

//        [{"cyId":"36001781236","type":0,"goodsConfig":[{"goodsId":"************","ratio":0.3},{"goodsId":"************","ratio":0.3},{"goodsId":"************","ratio":0.4}]},{"cyId":"30622063513","type":0,"goodsConfig":[{"goodsId":"************","ratio":0.3},{"goodsId":"************","ratio":0.3},{"goodsId":"************","ratio":0.4}]},{"cyId":"30622019563","type":0,"goodsConfig":[{"goodsId":"************","ratio":0.3},{"goodsId":"************","ratio":0.3},{"goodsId":"************","ratio":0.4}]}]

        System.out.println(UUID.randomUUID().toString());
        System.out.println();
    }

    @Test
    public void ylmf() {
        DefaultBiAdbrainOneYlmfProcess bean = abujlbBeanFactory.getBean(DefaultBiAdbrainOneYlmfProcess.class);
        bean.dealGoods();
//        bean.dealShop();
    }

    @Test
    public void ztc() {
        dataprobiMsg.setAiztone(1);
        DefaultBiAdbrainOneZtcProcess bean = abujlbBeanFactory.getBean(DefaultBiAdbrainOneZtcProcess.class);
//        bean.dealGoods();
//        bean.dealShop();
        bean.compareGoods();
    }

    @Test
    public void rlyq() {
        DefaultBiRlyqDealProcess bean = abujlbBeanFactory.getBean(DefaultBiRlyqDealProcess.class);
        bean.dealGoods();
        bean.dealShop();
    }

    @Test
    public void jhs() {
        DefaultBiJhsDealProcess bean = abujlbBeanFactory.getBean(DefaultBiJhsDealProcess.class);
        bean.dealGoods();
    }

    @Test
    public void tbxjhb() {
        DefaultbiTbxjhbDealProcess bean = abujlbBeanFactory.getBean(DefaultbiTbxjhbDealProcess.class);
        bean.dealGoods();
    }

    @Test
    public void xedk() {
        DefaultBiXedkDealProcess bean = abujlbBeanFactory.getBean(DefaultBiXedkDealProcess.class);
        bean.dealGoods();
    }

    @Test
    public void ppxx() {
        DefaultBiPpxxDealProcess defaultBiPpxxDealProcess = abujlbBeanFactory.getBean(DefaultBiPpxxDealProcess.class);
        defaultBiPpxxDealProcess.dealGoods();
//        defaultBiPpxxDealProcess.dealShop();

//        defaultBiPpxxDealProcess.compareGoods();
    }

    @Test
    public void yhq() {
        dataprobiMsg.setAiztone(1);
        DefaultBiAdbrainOneYhqProcess defaultBiYhqDealProcess = abujlbBeanFactory.getBean(DefaultBiAdbrainOneYhqProcess.class);
//        defaultBiYhqDealProcess.dealShop();
        defaultBiYhqDealProcess.compareGoods();
    }

    @Test
    public void nryx() {
        DefaultBiAdbrainOneNryxProcess defaultbiAiztoneNryxDealProcess = abujlbBeanFactory.getBean(DefaultBiAdbrainOneNryxProcess.class);
        defaultbiAiztoneNryxDealProcess.dealShop();
    }

    @Test
    public void qdzxl() {
        DefaultBiSycmFwCustomerSummaryDealProcess defaultBiSycmFwCustomerSummaryDealProcess = abujlbBeanFactory.getBean(DefaultBiSycmFwCustomerSummaryDealProcess.class);
        defaultBiSycmFwCustomerSummaryDealProcess.dealShop();
    }

    @Test
    public void nryxDsp() {
        DefaultbiAdbrainOneNryxDspProcess defaultbiAiztNryxDspDealProcess = abujlbBeanFactory.getBean(DefaultbiAdbrainOneNryxDspProcess.class);
        defaultbiAiztNryxDspDealProcess.dealGoods();
    }

    @Test
    public void dmbzt() {
        String lastday = DataprobiDateUtil.getLastday();
        String osskey = "bi_jysj/" + BiThreadLocals.getBiInfo().getUserid() + "/" + lastday + "/bi_aiztone_aizt.csv";
        if (biDataOssUtil.exist(osskey)) {
            dataprobiMsg.setAiztone(1);
        }

        DefaultBiAdbrainOneDmbztProcess defaultBiDmbztDealProcess = abujlbBeanFactory.getBean(DefaultBiAdbrainOneDmbztProcess.class);
        defaultBiDmbztDealProcess.dealGoods();
    }

    @Test
    public void tbk() {
        DefaultBiTbkDealProcess defaultBiTbkDealProcess = abujlbBeanFactory.getBean(DefaultBiTbkDealProcess.class);
        defaultBiTbkDealProcess.dealGoods();
        defaultBiTbkDealProcess.dealShop();
    }

    @Test
    public void sycmlevel() {
        DefaultBiSycmlevelDealProcess defaultBiTbkDealProcess = abujlbBeanFactory.getBean(DefaultBiSycmlevelDealProcess.class);
        defaultBiTbkDealProcess.dealShop();
    }

    @Test
    public void aiztoneZhlv() {
        String lastday = DataprobiDateUtil.getLastday();
        String osskey = "bi_jysj/" + BiThreadLocals.getBiInfo().getUserid() + "/" + lastday + "/bi_aiztone_aizt.csv";
        if (biDataOssUtil.exist(osskey)) {
            dataprobiMsg.setAiztone(1);
        }

        DefaultBiAiztoneZhlvDealProcess defaultBiAiztoneZhlvDealProcess = abujlbBeanFactory.getBean(DefaultBiAiztoneZhlvDealProcess.class);
        defaultBiAiztoneZhlvDealProcess.dealGoods();
//        defaultBiAiztoneZhlvDealProcess.dealShop();
    }

    @Test
    public void spxgpjsss() {
        DefaultbiSpxgPjsssDealProcess defaultbiSpxgPjsssDealProcess = abujlbBeanFactory.getBean(DefaultbiSpxgPjsssDealProcess.class);
        defaultbiSpxgPjsssDealProcess.dealGoods();
    }

    @Test
    public void xstg() {
        DefaultBiAdbrainOneXstgProcess defaultBiAdbrainOneXstgProcess = abujlbBeanFactory.getBean(DefaultBiAdbrainOneXstgProcess.class);
        defaultBiAdbrainOneXstgProcess.dealGoods();
    }

    @Test
    public void llzhymfx() {
        DefaultBiSycmLlzhYmfxDealProcess defaultBiSycmLlzhYmfxDealProcess = abujlbBeanFactory.getBean(DefaultBiSycmLlzhYmfxDealProcess.class);
        defaultBiSycmLlzhYmfxDealProcess.dealGoods();
    }

    @Test
    public void aiztoneztcitemdjlv() {
        DefaultBiAiztoneZtcItemDjlvDealProcess defaultBiAiztoneZtcItemDjlvDealProcess = abujlbBeanFactory.getBean(DefaultBiAiztoneZtcItemDjlvDealProcess.class);
        defaultBiAiztoneZtcItemDjlvDealProcess.dealGoods();
    }

    @Test
    public void splb() {
        BiThreadLocals.getMsgBean().setSplb(1);
        DefaultBiSplbDealProcess bean = abujlbBeanFactory.getBean(DefaultBiSplbDealProcess.class);
        bean.dealGoods();
    }

    private void aiztone(String rq) {
        String osskey = "bi_jysj/" + BiThreadLocals.getBiInfo().getUserid() + "/" + rq + "/bi_aiztone_aizt.csv";
        if (biDataOssUtil.exist(osskey)) {
            ((DataprobiMsg) BiThreadLocals.getMsgBean()).setAiztone(1);
        }
    }

    private void aiztone() {
        String lastday = DataprobiDateUtil.getLastday();
        String osskey = "bi_jysj/" + BiThreadLocals.getBiInfo().getUserid() + "/" + lastday + "/bi_aiztone_aizt.csv";
        if (biDataOssUtil.exist(osskey)) {
            ((DataprobiMsg) BiThreadLocals.getMsgBean()).setAiztone(1);
        }
    }

    @Test
    public void dpzhtyf() {
        DefaultbiDpZhtyfProcess defaultbiDpZhtyfProcess = abujlbBeanFactory.getBean(DefaultbiDpZhtyfProcess.class);
        defaultbiDpZhtyfProcess.dealShop();
        System.out.println("店铺综合体验分采集完成");
    }

    @Test
    public void msyq() {
        DefaultBiMsqDealProcess bean = abujlbBeanFactory.getBean(DefaultBiMsqDealProcess.class);
        bean.dealGoods();
        System.out.println("麦斯引擎花费采集完成");
    }

    @Test
    public void ud() {
        DefaultBiUDDealProcess bean = abujlbBeanFactory.getBean(DefaultBiUDDealProcess.class);
        bean.dealGoods();
    }

    //-----------------------------无界--------------------------//
    @Test
    public void aiztone_ait_item() {
        DefaultBiAdbrainOneAiztItemProcess defaultBiAdbrainOneAiztItemProcess = abujlbBeanFactory.getBean(DefaultBiAdbrainOneAiztItemProcess.class);
        defaultBiAdbrainOneAiztItemProcess.dealGoods();
    }

    @Test
    public void aiztone_aizt() {
        DefaultBiAdbrainOneAiztProcess defaultBiAdbrainOneAiztProcess = abujlbBeanFactory.getBean(DefaultBiAdbrainOneAiztProcess.class);
        defaultBiAdbrainOneAiztProcess.dealGoods();
    }

    @Test
    public void aiztone_aizt_shop() {
        DefaultBiAdbrainOneAiztShopProcess defaultBiAdbrainOneAiztProcess = abujlbBeanFactory.getBean(DefaultBiAdbrainOneAiztShopProcess.class);
        defaultBiAdbrainOneAiztProcess.dealGoods();
        defaultBiAdbrainOneAiztProcess.dealShop();
    }


    @Test
    public void aiztone_dmbzt() {
        DefaultBiAdbrainOneDmbztProcess defaultBiAdbrainOneDmbztProcess = abujlbBeanFactory.getBean(DefaultBiAdbrainOneDmbztProcess.class);
        defaultBiAdbrainOneDmbztProcess.dealGoods();
    }

    @Test
    public void aiztone_qztg() {
        DefaultBiAdbrainOneQztgProcess defaultBiAdbrainOneQztgProcess = abujlbBeanFactory.getBean(DefaultBiAdbrainOneQztgProcess.class);
        defaultBiAdbrainOneQztgProcess.dealGoods();
    }

    @Test
    public void aiztone_xstg() {
        DefaultBiAdbrainOneXstgProcess defaultBiAdbrainOneXstgProcess = abujlbBeanFactory.getBean(DefaultBiAdbrainOneXstgProcess.class);
        defaultBiAdbrainOneXstgProcess.dealGoods();
    }

    @Test
    public void aiztone_ylmf() {
        DefaultBiAdbrainOneYlmfProcess defaultBiAdbrainOneYlmfProcess = abujlbBeanFactory.getBean(DefaultBiAdbrainOneYlmfProcess.class);
        defaultBiAdbrainOneYlmfProcess.dealGoods();
    }

    @Test
    public void aiztone_ztc() {
        DefaultBiAdbrainOneZtcProcess defaultBiAdbrainOneZtcProcess = abujlbBeanFactory.getBean(DefaultBiAdbrainOneZtcProcess.class);
        defaultBiAdbrainOneZtcProcess.dealGoods();
    }

    @Test
    public void aiztone_ylmf_cxjh() {
        DefaultBiAdbrainOneYlmfCxjhProcess defaultBiAdbrainOneZtcProcess = abujlbBeanFactory.getBean(DefaultBiAdbrainOneYlmfCxjhProcess.class);
        defaultBiAdbrainOneZtcProcess.dealGoods();
    }

    @Test
    public void nryx_cjzb_qztg() {
        DefaultBiAdbrainOneNryxCjzbQztgProcess defaultBiAdbrainOneNryxCjzbQztgProcess = abujlbBeanFactory.getBean(DefaultBiAdbrainOneNryxCjzbQztgProcess.class);
        defaultBiAdbrainOneNryxCjzbQztgProcess.dealShop();
    }


    @Test
    public void aiztone_dpyy_dpzd() {
        DefaultBiAdbrainOneAiztShopDpztProcess defaultBiAdbrainOneAiztShopDpztProcess = abujlbBeanFactory.getBean(DefaultBiAdbrainOneAiztShopDpztProcess.class);
        defaultBiAdbrainOneAiztShopDpztProcess.dealGoods();
    }


    @Test
    public void aiztone_dpyy_qdzt() {
        DefaultBiAdbrainOneAiztShopQdztProcess defaultBiAdbrainOneAiztShopQdztProcess = abujlbBeanFactory.getBean(DefaultBiAdbrainOneAiztShopQdztProcess.class);
        defaultBiAdbrainOneAiztShopQdztProcess.dealGoods();
    }
    //-----------------------------无界end------------------------//


    @Test
    public void qyyxtg() {
        DefaultBiMySellerQyyxtgProcess defaultBiMySellerQyyxtgProcess = abujlbBeanFactory.getBean(DefaultBiMySellerQyyxtgProcess.class);
        defaultBiMySellerQyyxtgProcess.dealShop();
    }

    @Test
    public void runLocal() {
        ((DataprobiMsg) BiThreadLocals.getMsgBean()).setAiztone(1);
        biDataDealService.process();
    }

    @Test
    public void sendMqJst() {
        dataprobiMsg.setAiztone(1);
        SendResult sendResult = aliyunMq2.send("dataprobiv2_jst", dataprobiMsg.getUuid(), new JobMessage("dataprobi_v2_tx_processor", dataprobiMsg));
        System.out.println(dataprobiMsg.getDpmc() + "消息发送成功，消息msgId：" + sendResult.getMessageId());
    }

}
