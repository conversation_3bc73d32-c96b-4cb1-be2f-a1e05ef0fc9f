package com.abujlb.dataprobi.test;

import com.abujlb.BaseTest;
import com.abujlb.dataprobi.bean.BiInfoChangeMsgBean;
import com.abujlb.dataprobi.service.BiInfoChangeService;
import com.abujlb.mq.AliyunMq2;
import com.abujlb.mq.JobMessage;
import com.aliyun.openservices.ons.api.SendResult;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class TestBiInfoChangeService extends BaseTest {

    @Autowired
    private BiInfoChangeService biInfoChangeService ;

    @Autowired
    private AliyunMq2 aliyunMq2 ;

    @Test
    public void change(){
        BiInfoChangeMsgBean msgBean = new BiInfoChangeMsgBean();
        msgBean.setNewInfoId(10485);
        msgBean.setOriginInfoId(7516);
        biInfoChangeService.process(msgBean);

        SendResult sendResult = aliyunMq2.send("dataprobiv2_jst", "", new JobMessage("dataprobi_v2_newinfo_processor", msgBean));
    }
}
