package com.abujlb.dataprobi.test;

import com.abujlb.AbujlbBeanFactory;
import com.abujlb.BaseTest;
import com.abujlb.CommonConfig;
import com.abujlb.dataprobi.bean.BiInfo;
import com.abujlb.dataprobi.bean.BiRuleReRunTask;
import com.abujlb.dataprobi.bean.SqliteDp;
import com.abujlb.dataprobi.bean.xhs.DataprobiXhsMsg;
import com.abujlb.dataprobi.oss.BiDataOssUtil;
import com.abujlb.dataprobi.processes.xhs.*;
import com.abujlb.dataprobi.server.DataUploader;
import com.abujlb.dataprobi.service.BiWxsphDataDealService;
import com.abujlb.dataprobi.service.BiXhsDataDealService;
import com.abujlb.dataprobi.threads.BiThreadLocals;
import com.abujlb.dataprobi.util.DataprobiDateUtil;
import com.abujlb.dataprobi.util.SqliteDbUtil;
import com.abujlb.mq.AliyunMq2;
import com.abujlb.mq.JobMessage;
import com.abujlb.service.ServiceClient;
import com.abujlb.util.StringUtil;
import com.aliyun.openservices.ons.api.SendResult;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import com.alibaba.fastjson.JSONObject;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2024/5/7
 */
public class TestBiXhsProcess extends BaseTest {



    private final DataprobiXhsMsg dataprobiXhsMsg;
    @Autowired
    private AbujlbBeanFactory abujlbBeanFactory;
    @Autowired
    private AliyunMq2 aliyunMq2;
    @Autowired
    private BiDataOssUtil biDataOssUtil;

    @Autowired
    private SqliteDbUtil sqliteDbUtil;

    private void getBiinfo() throws Exception {
        List<BiInfo> allBiInfo = DataUploader.getAllBiInfo();
        ServiceClient client = new ServiceClient(CommonConfig.getString("jyrbAppid"), CommonConfig.getString("jyrbAppkey"));
        for (BiInfo biInfo : allBiInfo) {
            if (biInfo.getQd().equals("XHS")) {
                biInfo.getId();
            }
        }
    }

    {
        final int biinfoId = 8096;
        BiInfo biInfo = DataUploader.getBiinfoById(biinfoId);
        if (biInfo == null) {
            throw new RuntimeException("获取Biinfo失败，Biinfo的ID为：" + biinfoId);
        }
        dataprobiXhsMsg = new DataprobiXhsMsg();
        dataprobiXhsMsg.setUserid(biInfo.getUserid());
        dataprobiXhsMsg.setYhid(biInfo.getYhid());
        dataprobiXhsMsg.setQd(biInfo.getQd());
        dataprobiXhsMsg.setDpmc(biInfo.getDpmc());
        dataprobiXhsMsg.setBiinfoId(biInfo.getId());
        dataprobiXhsMsg.setType(1);
        dataprobiXhsMsg.setSplb(1);

        List<String> rqList2 = Collections.singletonList("2025-05-26");
        List<String> rqList = Collections.emptyList();
        List<String> rqList1 = DataprobiDateUtil.getBetweenDate("2025-04-01", "2025-04-09");
//        List<String> rqList2 = DataprobiDateUtil.getBetweenDate("2024-05-01", "2024-06-12");
       //List<String> rqList = Collections.singletonList("2025-04-04");

        dataprobiXhsMsg.setSpxg(rqList);
        dataprobiXhsMsg.setSpxgdp(rqList);
        dataprobiXhsMsg.setLlsjdp(rqList);
        dataprobiXhsMsg.setJgptbjtg(rqList);
        dataprobiXhsMsg.setJgptbjtgdp(rqList);
        dataprobiXhsMsg.setJgptzbfxdp(rqList);
        dataprobiXhsMsg.setQfbjyx(rqList);
        dataprobiXhsMsg.setQfzbyxdp(rqList);
        dataprobiXhsMsg.setCfsptg(rqList1);
        dataprobiXhsMsg.setCfsptgdp(rqList1);
        dataprobiXhsMsg.setCfzbtg(rqList1);
        dataprobiXhsMsg.setCfzbtgdp(rqList1);

        dataprobiXhsMsg.setPgybjbg(rqList);
        dataprobiXhsMsg.setPgybjbgdp(rqList);

        BiThreadLocals.init(dataprobiXhsMsg);
    }


    @Test
    public void spxg() {
        DefaultbiXhsSpxgProcess bean = abujlbBeanFactory.getBean(DefaultbiXhsSpxgProcess.class);
        bean.dealGoods();
        bean.dealShop();
        System.out.println("处理商品效果数据完成");
    }

    @Test
    public void llsj() {
        DefaultbiXhsLlsjProcess bean = abujlbBeanFactory.getBean(DefaultbiXhsLlsjProcess.class);
        //bean.dealGoods();
        bean.dealShop();
        System.out.println("处理流量数据数据完成");
    }

    @Test
    public void jgptbj() {
        DefaultbiXhsJgptBitgProcess bean = abujlbBeanFactory.getBean(DefaultbiXhsJgptBitgProcess.class);
        bean.dealShop();
        bean.dealGoods();
        System.out.println("处理流聚光平台笔记数据完成");
    }

    @Test
    public void jgptzb() {
        DefaultbiXhsJgptZbfxProcess bean = abujlbBeanFactory.getBean(DefaultbiXhsJgptZbfxProcess.class);
        bean.dealShop();
        System.out.println("处理流聚光平台直播数据完成");
    }

    @Test
    public void qfbj() {
        DefaultbiXhsQfbjyxProcess bean = abujlbBeanFactory.getBean(DefaultbiXhsQfbjyxProcess.class);
        bean.dealGoods();
        System.out.println("处理千帆笔记数据完成");
    }

    @Test
    public void qfzb() {
        DefaultbiXhsQfzbyxProcess bean = abujlbBeanFactory.getBean(DefaultbiXhsQfzbyxProcess.class);
        bean.dealShop();
        System.out.println("处理千帆直播营销数据完成");
    }

    @Test
    public void cfsp() {
        DefaultbiXhsCfspTgProcess bean = abujlbBeanFactory.getBean(DefaultbiXhsCfspTgProcess.class);
        bean.dealShop();
        bean.dealGoods();
        System.out.println("处理乘风商品营销数据完成");
    }

    @Test
    public void cfzb() {
        DefaultbiXhsCfzbTgProcess bean = abujlbBeanFactory.getBean(DefaultbiXhsCfzbTgProcess.class);
        bean.dealShop();
        bean.dealGoods();
        System.out.println("处理乘风直播营销数据完成");
    }

    @Test
    public void cf() {
        DefaultbiXhsCfspTgProcess bean = abujlbBeanFactory.getBean(DefaultbiXhsCfspTgProcess.class);
        bean.dealShop();
        bean.dealGoods();
        System.out.println("处理乘风商品营销数据完成");

        DefaultbiXhsCfzbTgProcess bean1 = abujlbBeanFactory.getBean(DefaultbiXhsCfzbTgProcess.class);
        bean1.dealShop();
        bean1.dealGoods();
        System.out.println("处理乘风直播营销数据完成");
    }

    @Test
    public void pgyhf() {
        DefaultbiXhsPgyhfProcess bean = abujlbBeanFactory.getBean(DefaultbiXhsPgyhfProcess.class);
        bean.dealShop();
        bean.dealGoods();
        System.out.println("处理蒲公英花费数据完成");
    }


    @Test
    public void runLocal() {
        BiThreadLocals.getMsgBean().setType(1);
        BiXhsDataDealService biXhsDataDealService = abujlbBeanFactory.getBean(BiXhsDataDealService.class);
        biXhsDataDealService.process();
    }

    @Test
    public void runLocalForAllXhsShops() throws Exception {
        // 获取所有BiInfo
        List<BiInfo> allBiInfo = DataUploader.getAllBiInfo();
        System.out.println("获取到总店铺数: " + allBiInfo.size());
        
        // 筛选小红书平台的店铺
        List<BiInfo> xhsShops = new ArrayList<>();
        for (BiInfo biInfo : allBiInfo) {
            if ("XHS".equals(biInfo.getQd())) {
                xhsShops.add(biInfo);
            }
        }
        System.out.println("获取到小红书店铺数: " + xhsShops.size());
        
        // 遍历处理每个小红书店铺
        for (BiInfo biInfo : xhsShops) {
            int biinfoId = biInfo.getId();
            System.out.println("开始处理小红书店铺: " + biInfo.getDpmc() + ", ID: " + biinfoId);
            
            // 初始化消息对象
            DataprobiXhsMsg currentMsg = new DataprobiXhsMsg();
            currentMsg.setUserid(biInfo.getUserid());
            currentMsg.setYhid(biInfo.getYhid());
            currentMsg.setQd(biInfo.getQd());
            currentMsg.setDpmc(biInfo.getDpmc());
            currentMsg.setBiinfoId(biinfoId);
            currentMsg.setType(1);
            currentMsg.setSplb(1);
            
            // 设置日期范围，这里使用与原代码相同的日期设置
            List<String> rqList1 = DataprobiDateUtil.getBetweenDate("2025-07-01", "2025-07-02");
            List<String> rqList = Collections.emptyList();

            // 设置各种日期字段
            // 决策理由：保持与原代码一致的日期设置，但使用cfzbtg和cfzbtgdp作为示例
            currentMsg.setSpxg(rqList);
            currentMsg.setSpxgdp(rqList);
            currentMsg.setLlsjdp(rqList);
            currentMsg.setJgptbjtg(rqList);
            currentMsg.setJgptbjtgdp(rqList);
            currentMsg.setJgptzbfxdp(rqList);
            currentMsg.setQfbjyx(rqList);
            currentMsg.setQfzbyxdp(rqList);
            currentMsg.setCfsptg(rqList);
            currentMsg.setCfsptgdp(rqList);
            currentMsg.setCfzbtg(rqList);
            currentMsg.setCfzbtgdp(rqList);

            currentMsg.setPgybjbg(rqList);
            currentMsg.setPgybjbgdp(rqList1);
            
            // 初始化线程本地变量
            BiThreadLocals.init(currentMsg);
            
            // 调用处理服务
            BiXhsDataDealService biXhsDataDealService = abujlbBeanFactory.getBean(BiXhsDataDealService.class);
            biXhsDataDealService.process();
            
            System.out.println("完成处理小红书店铺: " + biInfo.getDpmc() + ", ID: " + biinfoId);
        }
        
        System.out.println("所有小红书店铺处理完成");
    }

//    @Test
//    public void sendMq() {
//        SendResult sendResult = aliyunMq2.send("dataprobiv2_jst", dataprobiXhsMsg.getUuid(), new JobMessage("dataprobi_v2_xhs_processor", dataprobiXhsMsg));
//        System.out.println("小红书：" + dataprobiXhsMsg.getDpmc() + "消息发送成功，消息msgId：" + sendResult.getMessageId());
//    }

    @Test
    public void testReadSqliteDbFiles() {
        // Date range from 2025-01-01 to 2025-01-28
        List<String> dateRange = DataprobiDateUtil.getBetweenDate("2025-02-01", "2025-02-28");

        
        System.out.println("Starting to read SQLite DB files for date range: 2025-01-01 to 2025-01-28");
        
        for (String date : dateRange) {
            // Format the date for OSS path (remove hyphens)
            String formattedDate = date.replaceAll("-", "");
            
            // Construct the OSS path for the SQLite DB file
            String ossPath = String.format("bi_data/day/%s/17981/XHS/6386ca372ae0520001545e84/sycm_dp.db", formattedDate);
            
            System.out.println("\n--- Processing date: " + date + " ---");
            System.out.println("OSS Path: " + ossPath);
            
            // Check if the file exists in OSS
            if (!biDataOssUtil.exist(ossPath)) {
                System.out.println("DB file does not exist for date: " + date);
                continue;
            }
            
            // Read the SQLite DB file
            SqliteDp sqliteDp = sqliteDbUtil.readDp(ossPath);
            
            if (sqliteDp == null) {
                System.out.println("Failed to read DB file or no data found for date: " + date);
                continue;
            }
            
            // Print the tghf field
            System.out.println("TGHF: " + sqliteDp.getTghf());
            
            // Print the other field
            String other = sqliteDp.getOtherOrDefault();
            System.out.println("Other: " + other);
            
            // Parse and print the other field as JSON for better readability
            try {
                JSONObject otherJson = JSONObject.parseObject(other);
                System.out.println("Other (parsed):");
                for (Map.Entry<String, Object> entry : otherJson.entrySet()) {
                    System.out.println("  " + entry.getKey() + ": " + entry.getValue());
                }
            } catch (Exception e) {
                System.out.println("Failed to parse 'other' field as JSON: " + e.getMessage());
            }
        }
        
        System.out.println("\nCompleted reading SQLite DB files");
    }

}