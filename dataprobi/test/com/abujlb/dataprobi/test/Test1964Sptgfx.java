package com.abujlb.dataprobi.test;

import com.abujlb.BaseTest;
import com.abujlb.CommonConfig;
import com.abujlb.Result;
import com.abujlb.dao.v2.TsDao2;
import com.abujlb.dataprobi.util.DataprobiDateUtil;
import com.abujlb.service.ServiceClient;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alicloud.openservices.tablestore.SyncClient;
import com.alicloud.openservices.tablestore.model.*;
import com.alicloud.openservices.tablestore.model.search.SearchQuery;
import com.alicloud.openservices.tablestore.model.search.SearchRequest;
import com.alicloud.openservices.tablestore.model.search.SearchResponse;
import com.alicloud.openservices.tablestore.model.search.query.BoolQuery;
import com.alicloud.openservices.tablestore.model.search.query.TermQuery;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class Test1964Sptgfx extends BaseTest {

    JSONArray jsonArray = null;
    private final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private SyncClient formalClient = null;
    private SyncClient testClient = null;
    private static final ServiceClient serviceClient;
    private static final String serviceUrl;
    @Autowired
    private TsDao2 tsDao2;

    static {
        serviceClient = new ServiceClient(CommonConfig.getString("jyrbAppid"), CommonConfig.getString("jyrbAppkey"));
        serviceUrl = CommonConfig.getString("jyrbServiceUrl");
    }

    @Before
    public void init() {
        formalClient = tsDao2.getClient("abujlb6@jushita").getClient();
        testClient = tsDao2.getClient("abujlb6test@jushita").getClient();

        String s = serviceClient.exec(serviceUrl + "/bi2/getAllBiInfos", new Object());
        if (Result.isTrue(s)) {
            JSONObject jsonObject = JSONObject.parseObject(s);
            jsonArray = jsonObject.getJSONArray("data");
        }
    }

    /**
     * 将1964 10月份的 商品推广分析数据
     * 同步一份到测试服
     */
    @Test
    public void doTransfer() {
        final String start = "2025-05-01";
        final String end = "2025-06-02";
        List<String> rqList = DataprobiDateUtil.getBetweenDate(start, end);
        final int yhid = 1741;
        List<String> txUseridList = new ArrayList<>();
        List<String> allUseridList = new ArrayList<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            if (jsonObject.getIntValue("yhid") != yhid) {
                continue;
            }
            if (!jsonObject.getString("userid").equals("2889741150")) {
                continue;
            }
            if (jsonObject.getString("qd").equals("TM") || jsonObject.getString("qd").equals("TB")) {
                txUseridList.add(jsonObject.getString("userid"));
            }
            allUseridList.add(jsonObject.getString("userid"));
        }

        //同步abujlb6.bi_ztc_item_campaign_data 直通车计划
        syncZtcItemCampaignData(txUseridList, rqList);

        //同步abujlb6.bi_ztc_item_campaign_keyword_data  直通车关键词
        syncZtcItemCampaignKeywordData(txUseridList, rqList);

        //同步abujlb6.bi_item_tgfx_data
//        syncTgfx(allUseridList, rqList);
    }

    private void syncTgfx(List<String> jdUseridList, List<String> rqList) {
        String[] lxArr = {"JDKC", "JDZT", "JDZW", "JST", "GWCD", "DYTSP", "MNAIZT", "MNYLMF", "ALBBJJFA", "ALBBZNTF", "ALBBZZTF", "ALBBZXB", "PDDQZTG", "PDDBZTG", "PDDSPTG", "AIZT", "YLMF", "DMBZT", "TXQZTG", "TXAIZTITEM", "TXAIZTSHOP", "TXYLMFCXJH"};
        System.out.println("abujlb6.bi_item_tgfx_data 同步开始---》" + LocalDateTime.now().format(dateTimeFormatter));
        for (String userid : jdUseridList) {
            for (String lx : lxArr) {
                for (String rq : rqList) {
                    System.out.println("userid：" + userid + "，lx：" + lx + "，rq：" + rq + "，开始---->" + LocalDateTime.now().format(dateTimeFormatter));
                    syncItemTgfxData(userid, lx, rq);
                    System.out.println("userid：" + userid + "，lx：" + lx + "，rq：" + rq + "，结束---->" + LocalDateTime.now().format(dateTimeFormatter));
                }
            }
        }
        System.out.println("abujlb6.bi_item_tgfx_data 同步结束---》" + LocalDateTime.now().format(dateTimeFormatter));
    }

    private void syncZtcItemCampaignKeywordData(List<String> txUseridList, List<String> rqList) {
        System.out.println("abujlb6.bi_ztc_item_campaign_keyword_data 同步开始---》" + LocalDateTime.now().format(dateTimeFormatter));
        for (String userid : txUseridList) {

            for (String rq : rqList) {
                System.out.println("abujlb6.bi_ztc_item_campaign_keyword_data " + "userid开始：" + userid + "，rq：" + rq + "----->" + LocalDateTime.now().format(dateTimeFormatter));
                syncZtcItemCampaignKeywordDataUserid(userid, rq);
                System.out.println("abujlb6.bi_ztc_item_campaign_keyword_data " + "userid结束：" + userid + "，rq：" + rq + "----->" + LocalDateTime.now().format(dateTimeFormatter));
            }
            syncZtcItemCampaignKeywordDataUserid(userid, "recent7");
            syncZtcItemCampaignKeywordDataUserid(userid, "recent30");
        }
        System.out.println("abujlb6.bi_ztc_item_campaign_keyword_data 同步结束---》" + LocalDateTime.now().format(dateTimeFormatter));
    }

    private void syncZtcItemCampaignData(List<String> txUseridList, List<String> rqList) {
        System.out.println("abujlb6.bi_ztc_item_campaign_data 同步开始---》" + LocalDateTime.now().format(dateTimeFormatter));
        for (String userid : txUseridList) {
            for (String rq : rqList) {
                System.out.println("abujlb6.bi_ztc_item_campaign_data " + "userid：" + userid + "，rq：" + rq + "开始----->" + LocalDateTime.now().format(dateTimeFormatter));
                syncZtcItemCampaignDataUserid(userid, rq);
                System.out.println("abujlb6.bi_ztc_item_campaign_data " + "userid：" + userid + "，rq：" + rq + "结束----->" + LocalDateTime.now().format(dateTimeFormatter));
            }

            syncZtcItemCampaignDataUserid(userid, "recent7");
            syncZtcItemCampaignDataUserid(userid, "recent30");
        }

        System.out.println("abujlb6.bi_ztc_item_campaign_data 同步结束---》" + LocalDateTime.now().format(dateTimeFormatter));
    }

    private void syncZtcItemCampaignDataUserid(String userid, String rq) {
        RangeRowQueryCriteria rangeRowQueryCriteria = new RangeRowQueryCriteria("bi_ztc_item_campaign_data");
        //设置起始主键。
        PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
        primaryKeyBuilder.addPrimaryKeyColumn("userid", PrimaryKeyValue.fromString(userid));
        if (rq.equals("recent7") || rq.equals("recent30")) {
            primaryKeyBuilder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(rq));
        } else {
            primaryKeyBuilder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString("day_" + rq));
        }
        primaryKeyBuilder.addPrimaryKeyColumn("itemid", PrimaryKeyValue.INF_MIN);
        primaryKeyBuilder.addPrimaryKeyColumn("campaignId", PrimaryKeyValue.INF_MIN);
        rangeRowQueryCriteria.setInclusiveStartPrimaryKey(primaryKeyBuilder.build());

        //设置结束主键。
        primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
        primaryKeyBuilder.addPrimaryKeyColumn("userid", PrimaryKeyValue.fromString(userid));
        if (rq.equals("recent7") || rq.equals("recent30")) {
            primaryKeyBuilder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(rq));
        } else {
            primaryKeyBuilder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString("day_" + rq));
        }
        primaryKeyBuilder.addPrimaryKeyColumn("itemid", PrimaryKeyValue.INF_MAX);
        primaryKeyBuilder.addPrimaryKeyColumn("campaignId", PrimaryKeyValue.INF_MAX);

        rangeRowQueryCriteria.setExclusiveEndPrimaryKey(primaryKeyBuilder.build());
        rangeRowQueryCriteria.setMaxVersions(1);
        rangeRowQueryCriteria.setLimit(100);

        while (true) {
            GetRangeResponse getRangeResponse = formalClient.getRange(new GetRangeRequest(rangeRowQueryCriteria));

            BatchWriteRowRequest batchWriteRowRequest = new BatchWriteRowRequest();
            for (Row row : getRangeResponse.getRows()) {
                PrimaryKeyBuilder pk1Builder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
                pk1Builder.addPrimaryKeyColumn("userid", row.getPrimaryKey().getPrimaryKeyColumn("userid").getValue());
                pk1Builder.addPrimaryKeyColumn("rqlx_rq", row.getPrimaryKey().getPrimaryKeyColumn("rqlx_rq").getValue());
                pk1Builder.addPrimaryKeyColumn("itemid", row.getPrimaryKey().getPrimaryKeyColumn("itemid").getValue());
                pk1Builder.addPrimaryKeyColumn("campaignId", row.getPrimaryKey().getPrimaryKeyColumn("campaignId").getValue());
                //设置数据表名称。
                RowUpdateChange rowUpdateChange = new RowUpdateChange("bi_ztc_item_campaign_data", pk1Builder.build());
                //添加一些列。
                for (Column column : row.getColumns()) {
                    rowUpdateChange.put(new Column(column.getName(), column.getValue()));
                }

                //添加到batch操作中。
                batchWriteRowRequest.addRowChange(rowUpdateChange);
            }
            if (batchWriteRowRequest.getRowsCount() != 0) {
                testClient.batchWriteRow(batchWriteRowRequest);
            }

            //如果NextStartPrimaryKey不为null，则继续读取。
            if (getRangeResponse.getNextStartPrimaryKey() != null) {
                rangeRowQueryCriteria.setInclusiveStartPrimaryKey(getRangeResponse.getNextStartPrimaryKey());
            } else {
                break;
            }
        }
    }

    private void syncZtcItemCampaignKeywordDataUserid(String userid, String rq) {
        SearchQuery searchQuery = new SearchQuery();
        TermQuery spuidQuery = new TermQuery(); //设置查询类型为TermQuery。
        spuidQuery.setFieldName("userid"); //设置要匹配的字段。
        spuidQuery.setTerm(ColumnValue.fromString(userid)); //设置要匹配的值。

        TermQuery rqQuery = new TermQuery(); //设置查询类型为TermQuery。
        rqQuery.setFieldName("rqlx_rq"); //设置要匹配的字段。
        if (rq.equals("recent7") || rq.equals("recent30")) {
            rqQuery.setTerm(ColumnValue.fromString(rq)); //设置要匹配的值。
        } else {
            rqQuery.setTerm(ColumnValue.fromString("day_" + rq)); //设置要匹配的值。
        }

        BoolQuery boolQuery = new BoolQuery();
        boolQuery.setMustQueries(Arrays.asList(spuidQuery, rqQuery));
        searchQuery.setQuery(boolQuery);
        searchQuery.setLimit(100);
        SearchRequest searchRequest = new SearchRequest("bi_ztc_item_campaign_keyword_data", "bi_ztc_item_campaign_keyword_data_index", searchQuery);

        SearchRequest.ColumnsToGet columnsToGet = new SearchRequest.ColumnsToGet();
        columnsToGet.setReturnAll(true);
        searchRequest.setColumnsToGet(columnsToGet);

        byte[] next = null;
        while (true) {
            SearchResponse resp = formalClient.search(searchRequest);

            BatchWriteRowRequest batchWriteRowRequest = new BatchWriteRowRequest();
            for (Row row : resp.getRows()) {
                PrimaryKeyBuilder pk1Builder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
                pk1Builder.addPrimaryKeyColumn("userid_rqlx_rq", row.getPrimaryKey().getPrimaryKeyColumn("userid_rqlx_rq").getValue());
                pk1Builder.addPrimaryKeyColumn("itemid", row.getPrimaryKey().getPrimaryKeyColumn("itemid").getValue());
                pk1Builder.addPrimaryKeyColumn("campaignId", row.getPrimaryKey().getPrimaryKeyColumn("campaignId").getValue());
                pk1Builder.addPrimaryKeyColumn("keyword", row.getPrimaryKey().getPrimaryKeyColumn("keyword").getValue());
                //设置数据表名称。
                RowUpdateChange rowUpdateChange = new RowUpdateChange("bi_ztc_item_campaign_keyword_data", pk1Builder.build());
                //添加一些列。
                for (Column column : row.getColumns()) {
                    rowUpdateChange.put(new Column(column.getName(), column.getValue()));
                }
                //添加到batch操作中。
                batchWriteRowRequest.addRowChange(rowUpdateChange);
            }

            if (batchWriteRowRequest.getRowsCount() != 0) {
                testClient.batchWriteRow(batchWriteRowRequest);
            }

            next = resp.getNextToken();
            if (next != null) {
                searchRequest.getSearchQuery().setToken(next);
            } else {
                break;
            }
        }
    }

    private void syncItemTgfxData(String userid, String lx, String rq) {
        RangeRowQueryCriteria rangeRowQueryCriteria = new RangeRowQueryCriteria("bi_item_tgfx_data");
        //设置起始主键。
        PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
        primaryKeyBuilder.addPrimaryKeyColumn("userid_lx", PrimaryKeyValue.fromString(userid + "_" + lx));
        primaryKeyBuilder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString("day_" + rq));
        primaryKeyBuilder.addPrimaryKeyColumn("itemid", PrimaryKeyValue.INF_MIN);
        primaryKeyBuilder.addPrimaryKeyColumn("hash", PrimaryKeyValue.INF_MIN);
        rangeRowQueryCriteria.setInclusiveStartPrimaryKey(primaryKeyBuilder.build());

        //设置结束主键。
        primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
        primaryKeyBuilder.addPrimaryKeyColumn("userid_lx", PrimaryKeyValue.fromString(userid + "_" + lx));
        primaryKeyBuilder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString("day_" + rq));
        primaryKeyBuilder.addPrimaryKeyColumn("itemid", PrimaryKeyValue.INF_MAX);
        primaryKeyBuilder.addPrimaryKeyColumn("hash", PrimaryKeyValue.INF_MAX);

        rangeRowQueryCriteria.setExclusiveEndPrimaryKey(primaryKeyBuilder.build());
        rangeRowQueryCriteria.setMaxVersions(1);
        rangeRowQueryCriteria.setLimit(100);

        while (true) {
            GetRangeResponse getRangeResponse = formalClient.getRange(new GetRangeRequest(rangeRowQueryCriteria));

            BatchWriteRowRequest batchWriteRowRequest = new BatchWriteRowRequest();
            for (Row row : getRangeResponse.getRows()) {
                PrimaryKeyBuilder pk1Builder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
                pk1Builder.addPrimaryKeyColumn("userid_lx", row.getPrimaryKey().getPrimaryKeyColumn("userid_lx").getValue());
                pk1Builder.addPrimaryKeyColumn("rqlx_rq", row.getPrimaryKey().getPrimaryKeyColumn("rqlx_rq").getValue());
                pk1Builder.addPrimaryKeyColumn("itemid", row.getPrimaryKey().getPrimaryKeyColumn("itemid").getValue());
                pk1Builder.addPrimaryKeyColumn("hash", row.getPrimaryKey().getPrimaryKeyColumn("hash").getValue());
                //设置数据表名称。
                RowUpdateChange rowUpdateChange = new RowUpdateChange("bi_item_tgfx_data", pk1Builder.build());
                //添加一些列。
                for (Column column : row.getColumns()) {
                    rowUpdateChange.put(new Column(column.getName(), column.getValue()));
                }
                //添加到batch操作中。
                batchWriteRowRequest.addRowChange(rowUpdateChange);
            }
            if (batchWriteRowRequest.getRowsCount() != 0) {
                testClient.batchWriteRow(batchWriteRowRequest);
            }

            //如果NextStartPrimaryKey不为null，则继续读取。
            if (getRangeResponse.getNextStartPrimaryKey() != null) {
                rangeRowQueryCriteria.setInclusiveStartPrimaryKey(getRangeResponse.getNextStartPrimaryKey());
            } else {
                break;
            }
        }
    }

}
