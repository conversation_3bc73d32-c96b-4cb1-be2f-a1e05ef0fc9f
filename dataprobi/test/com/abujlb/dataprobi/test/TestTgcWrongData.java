package com.abujlb.dataprobi.test;

import com.abujlb.BaseTest;
import com.abujlb.CommonConfig;
import com.abujlb.dataprobi.constant.DataprobiConst;
import com.abujlb.dataprobi.oss.BiDataOssUtil;
import com.abujlb.dataprobi.util.DataprobiDateUtil;
import com.abujlb.dataprobi.util.SqliteDbUtil;
import com.abujlb.service.ServiceClient;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

public class TestTgcWrongData extends BaseTest {

    private static final ServiceClient serviceClient;
    private static final String serviceUrl;

    @Autowired
    private BiDataOssUtil biDataOssUtil;

    @Autowired
    private SqliteDbUtil sqliteDbUtil;

    String rq = "2024-11-30";
    List<String> rqList = DataprobiDateUtil.getBetweenDate(rq, "2025-02-06");

    static {
        serviceClient = new ServiceClient(CommonConfig.getString("jyrbAppid"), CommonConfig.getString("jyrbAppkey"));
        serviceUrl = CommonConfig.getString("jyrbServiceUrl");
    }

    @Test
    public void test() throws Exception {
        List<String> rqList = DataprobiDateUtil.getBetweenDate("2024-12-01", "2025-02-16");
        int yhid = 6121;
        String qd = "TGC";
        String userid = "2218502028852";
        for (String rq : rqList) {
            String ossPath1 = String.format(DataprobiConst.SYCM_SP_DAY, rq.replaceAll("-", ""), yhid, qd, userid);
            if (biDataOssUtil.exist(ossPath1)) {
                biDataOssUtil.delete(ossPath1);
            }

            String ossPath2 = String.format(DataprobiConst.SYCM_DP_DAY, rq.replaceAll("-", ""), yhid, qd, userid);
            if (biDataOssUtil.exist(ossPath2)) {
                biDataOssUtil.delete(ossPath2);
            }
        }
    }
}
