package com.abujlb.dataprobi.test;

import com.abujlb.BaseTest;
import com.abujlb.CommonConfig;
import com.abujlb.Result;
import com.abujlb.dataprobi.bean.BiInfo;
import com.abujlb.dataprobi.bean.DataprobiBaseMsgBean2;
import com.abujlb.dataprobi.bean.DataprobiTaskMsg;
import com.abujlb.dataprobi.bean.albb.Dataprobi1688Msg;
import com.abujlb.dataprobi.bean.dy.DataprobiDyMsgBean;
import com.abujlb.dataprobi.bean.jd.DataprobiJdMsgBean;
import com.abujlb.dataprobi.bean.mn.DataprobiMnMsg;
import com.abujlb.dataprobi.bean.pdd.DataprobiPddMsgBean;
import com.abujlb.dataprobi.bean.tb.DataprobiMsg;
import com.abujlb.dataprobi.bean.tgc.DataprobiTgcMsg;
import com.abujlb.dataprobi.bean.xhs.DataprobiXhsMsg;
import com.abujlb.dataprobi.constant.DataprobiConst;
import com.abujlb.dataprobi.constant.TaskTypeConst;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.oss.BiDataOssUtil;
import com.abujlb.dataprobi.server.DataUploader;
import com.abujlb.dataprobi.util.DataprobiDateUtil;
import com.abujlb.mq.AliyunMq2;
import com.abujlb.mq.JobMessage;
import com.abujlb.service.ServiceClient;
import com.abujlb.util.StringUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.ons.api.SendResult;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2022/5/27 9:37
 */
public class TestBatchSendDataprobimsg2 extends BaseTest {

    private static final ServiceClient serviceClient;
    private static final String serviceUrl;

    static {
        serviceClient = new ServiceClient(CommonConfig.getString("jyrbAppid"), CommonConfig.getString("jyrbAppkey"));
        serviceUrl = CommonConfig.getString("jyrbServiceUrl");
    }

    JSONArray jsonArray = null;
    @Autowired
    private AliyunMq2 aliyunMq2;
    @Autowired
    private BiDataOssUtil biDataOssUtil;

    @Before
    public void init() {
        try {
            String s = serviceClient.exec(serviceUrl + "/bi2/getAllBiInfos", new Object());
            if (Result.isTrue(s)) {
                JSONObject jsonObject = JSONObject.parseObject(s);
                jsonArray = jsonObject.getJSONArray("data");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void sendTxByYhid() {
        List<BiInfo> allBiInfo = DataUploader.getAllBiInfo();
        if (CollectionUtils.isEmpty(allBiInfo)) {
            return;
        }
        List<String> rqList = DataprobiDateUtil.getBetweenDate("2025-06-01", "2025-06-17");
        for (BiInfo biInfo : allBiInfo) {
            if (biInfo.getQd().equals(Qd.TM.getQdDesc()) || biInfo.getQd().equals(Qd.TB.getQdDesc())) {
                DataprobiMsg dataprobiMsg = new DataprobiMsg();

                dataprobiMsg.setBiinfoId(biInfo.getId());
                dataprobiMsg.setUserid(biInfo.getUserid());
                dataprobiMsg.setYhid(biInfo.getYhid());
                dataprobiMsg.setQd(biInfo.getQd());
                dataprobiMsg.setDpmc(biInfo.getDpmc());
                dataprobiMsg.setSplb(0);
                dataprobiMsg.setType(2);
                dataprobiMsg.setAuto(0);
                dataprobiMsg.setDpMonths(Collections.emptyList());
                dataprobiMsg.setYhq(rqList);
                SendResult sendResult = aliyunMq2.send("dataprobiv2_jst", dataprobiMsg.getUuid(), new JobMessage("dataprobi_v2_tx_processor", dataprobiMsg));
                System.out.println("店铺：" + biInfo.getDpmc() + "发送成功，msgid：" + sendResult.getMessageId());
            }
        }
    }

    @Test
    public void sendPddByYhid() {
        List<BiInfo> allBiInfo = DataUploader.getAllBiInfo();
//        List<String> rqList = DataprobiDateUtil.getBetweenDate("2024-05-01", "2024-06-03");
        List<String> rqList = Collections.singletonList("2024-11-27");
        for (BiInfo biInfo : allBiInfo) {
            if (!biInfo.getQd().equals(Qd.PDD.getQdDesc())) {
                continue;
            }

            DataprobiPddMsgBean dataprobiPddMsgBean = new DataprobiPddMsgBean();
            dataprobiPddMsgBean.setBiinfoId(biInfo.getId());
            dataprobiPddMsgBean.setUserid(biInfo.getUserid());
            dataprobiPddMsgBean.setYhid(biInfo.getYhid());
            dataprobiPddMsgBean.setQd("PDD");
            dataprobiPddMsgBean.setDpmc(biInfo.getDpmc());
            dataprobiPddMsgBean.setSplb(0);
            dataprobiPddMsgBean.setDpMonths(Collections.emptyList());
            dataprobiPddMsgBean.setType(1);
            dataprobiPddMsgBean.setAuto(0);
            dataprobiPddMsgBean.setBztg(rqList);
            dataprobiPddMsgBean.setBztgdp(rqList);
            dataprobiPddMsgBean.setQztg(rqList);
            dataprobiPddMsgBean.setQztgdp(rqList);

            SendResult sendResult = aliyunMq2.send("dataprobiv2_jst", "", new JobMessage("dataprobi_v2_pdd_processor", dataprobiPddMsgBean));
            System.out.println(biInfo.getDpmc() + "--->" + sendResult.getMessageId());
        }
    }


    @Test
    public void sendDy() {
        List<BiInfo> allBiInfo = DataUploader.getAllBiInfo();
        if (CollectionUtils.isEmpty(allBiInfo)) {
            return;
        }
        List<String> rqList = Collections.singletonList("2025-03-31");
        for (BiInfo biInfo : allBiInfo) {
            if (!biInfo.getQd().equals(Qd.DY.getQdDesc())) {
                continue;
            }
            DataprobiDyMsgBean dataprobiDyMsgBean = new DataprobiDyMsgBean();
            dataprobiDyMsgBean.setBiinfoId(biInfo.getId());
            dataprobiDyMsgBean.setUserid(biInfo.getUserid());
            dataprobiDyMsgBean.setYhid(biInfo.getYhid());
            dataprobiDyMsgBean.setQd(biInfo.getQd());
            dataprobiDyMsgBean.setDpmc(biInfo.getDpmc());
            dataprobiDyMsgBean.setSplb(0);
            dataprobiDyMsgBean.setDpMonths(Collections.emptyList());
            dataprobiDyMsgBean.setType(1);
            dataprobiDyMsgBean.setAuto(0);
            dataprobiDyMsgBean.setJxlm_tzfwf(rqList);
            SendResult sendResult = aliyunMq2.send("dataprobiv2_jst", dataprobiDyMsgBean.getUuid(), new JobMessage("dataprobi_v2_dy_processor", dataprobiDyMsgBean));
            System.out.println(biInfo.getDpmc() + "--->" + sendResult.getMessageId());
        }
    }

    @Test
    public void sendAlbbByYhid() {
        List<BiInfo> allBiInfo = DataUploader.getAllBiInfo();
        List<String> rqList = DataprobiDateUtil.getBetweenDate("2024-05-01", "2024-06-02");
        for (BiInfo biInfo : allBiInfo) {
            if (!biInfo.getQd().equals(Qd.ALBB.getQdDesc())) {
                continue;
            }
            Dataprobi1688Msg dataprobi1688Msg = new Dataprobi1688Msg();
            dataprobi1688Msg.setBiinfoId(biInfo.getId());
            dataprobi1688Msg.setUserid(biInfo.getUserid());
            dataprobi1688Msg.setYhid(biInfo.getYhid());
            dataprobi1688Msg.setQd(Qd.ALBB.getQdDesc());
            dataprobi1688Msg.setDpmc(biInfo.getDpmc());
            dataprobi1688Msg.setSplb(0);
            dataprobi1688Msg.setDpMonths(Collections.emptyList());
            dataprobi1688Msg.setType(2);
            dataprobi1688Msg.setAuto(0);
            SendResult sendResult = aliyunMq2.send("dataprobiv2_jst", "", new JobMessage("dataprobi_v2_albb_processor", dataprobi1688Msg));
            System.out.println(biInfo.getDpmc() + "--->" + sendResult.getMessageId());
        }
    }


    @Test
    public void sendMNByYhid() {
        List<BiInfo> allBiInfo = DataUploader.getAllBiInfo();
        List<String> rqList = DataprobiDateUtil.getBetweenDate("2024-05-01", "2024-06-03");
        for (BiInfo biInfo : allBiInfo) {
            if (!biInfo.getQd().equals(Qd.MN.getQdDesc())) {
                continue;
            }
            DataprobiMnMsg dataprobiMnMsg = new DataprobiMnMsg();
            dataprobiMnMsg.setBiinfoId(biInfo.getId());
            dataprobiMnMsg.setUserid(biInfo.getUserid());
            dataprobiMnMsg.setYhid(biInfo.getYhid());
            dataprobiMnMsg.setQd(Qd.MN.getQdDesc());
            dataprobiMnMsg.setDpmc(biInfo.getDpmc());
            dataprobiMnMsg.setSplb(0);
            dataprobiMnMsg.setDpMonths(Collections.emptyList());
            dataprobiMnMsg.setType(2);
            dataprobiMnMsg.setAuto(0);
            dataprobiMnMsg.setZtctgfx(rqList);
            dataprobiMnMsg.setYlmftgfx(rqList);
            dataprobiMnMsg.setAizttgfx(rqList);
            dataprobiMnMsg.setDmbzttgfx(rqList);
            dataprobiMnMsg.setQztgtgfx(rqList);
            SendResult sendResult = aliyunMq2.send("dataprobiv2_jst", "", new JobMessage("dataprobi_v2_mn_processor", dataprobiMnMsg));
            System.out.println(biInfo.getDpmc() + "--->" + sendResult.getMessageId());
        }
    }


    @Test
    public void sendTXTgfxByYhid() {
        List<BiInfo> allBiInfo = DataUploader.getAllBiInfo();
        List<String> rqList = DataprobiDateUtil.getBetweenDate("2025-05-01", "2025-05-26");
        for (BiInfo biInfo : allBiInfo) {
            if (biInfo.getYhid() != 19403) {
                continue;
            }
            if (biInfo.getQd().equals(Qd.TM.getQdDesc()) || biInfo.getQd().equals(Qd.TB.getQdDesc())) {
                DataprobiMsg dataprobiMsg = new DataprobiMsg();
                dataprobiMsg.setBiinfoId(biInfo.getId());
                dataprobiMsg.setUserid(biInfo.getUserid());
                dataprobiMsg.setYhid(biInfo.getYhid());
                dataprobiMsg.setQd(biInfo.getQd());
                dataprobiMsg.setDpmc(biInfo.getDpmc());
                dataprobiMsg.setSplb(0);
                dataprobiMsg.setDpMonths(Collections.emptyList());
                dataprobiMsg.setType(2);
                dataprobiMsg.setAuto(0);
                dataprobiMsg.setZtc_tgfx(rqList);
                dataprobiMsg.setZtc_crowd_tgfx(rqList);
                dataprobiMsg.setZtc_keyword_tgfx(rqList);
                dataprobiMsg.setYlmf_tgfx(rqList);
                dataprobiMsg.setAizt_tgfx(rqList);
                dataprobiMsg.setDmbzt_tgfx(rqList);
                dataprobiMsg.setAizt_item_tgfx(rqList);
                dataprobiMsg.setAizt_shop_tgfx(rqList);
                dataprobiMsg.setXstg_tgfx(rqList);
                SendResult sendResult = aliyunMq2.send("dataprobiv2_jst", "", new JobMessage("dataprobi_v2_tx_processor", dataprobiMsg));
                System.out.println(biInfo.getDpmc() + "--->" + sendResult.getMessageId());
            }
        }
    }


    @Test
    public void sendJd() {
        Objects.requireNonNull(jsonArray);
        List<BiInfo> allBiInfo = DataUploader.getAllBiInfo();
        int yhid = 23063;
        List<String> rqList1 = DataprobiDateUtil.getBetweenDate("2024-09-01", "2025-01-07");
        List<String> rqList2 = DataprobiDateUtil.getBetweenDate("2024-12-01", "2025-01-07");
        for (BiInfo biInfo : allBiInfo) {
            if (!biInfo.getQd().equals(Qd.JD.getQdDesc())) {
                continue;
            }

            DataprobiJdMsgBean dataprobiJdMsgBean = new DataprobiJdMsgBean();
            dataprobiJdMsgBean.setBiinfoId(biInfo.getId());
            dataprobiJdMsgBean.setUserid(biInfo.getUserid());
            dataprobiJdMsgBean.setYhid(biInfo.getYhid());
            dataprobiJdMsgBean.setQd("JD");
            dataprobiJdMsgBean.setDpmc(biInfo.getDpmc());
            dataprobiJdMsgBean.setSplb(0);
            dataprobiJdMsgBean.setDpMonths(Collections.emptyList());
            dataprobiJdMsgBean.setType(1);
            if (biInfo.getYhid() == yhid) {
                dataprobiJdMsgBean.setWyj(rqList1);
            } else {
                dataprobiJdMsgBean.setWyj(rqList2);
            }

            SendResult sendResult = aliyunMq2.send("dataprobiv2_jst", dataprobiJdMsgBean.getUuid(), new JobMessage("dataprobi_v2_jd_processor", dataprobiJdMsgBean));
            System.out.println(biInfo.getDpmc() + "--->" + sendResult.getMessageId());
        }
    }


    @Test
    public void sendAlbb() {
        Objects.requireNonNull(jsonArray);
        List<BiInfo> allBiInfo = DataUploader.getAllBiInfo();

        List<String> zeroList = Arrays.asList("969669240", "983027274", "2212398971175", "2208680587000", "2212343318350", "2214805863517");
//        List<String> rqList = Collections.singletonList("2024-09-10");
        List<String> rqList = Arrays.asList("2024-12-23", "2024-12-24", "2024-12-25");
        for (BiInfo biInfo : allBiInfo) {
            if (!biInfo.getQd().equals(Qd.ALBB.getQdDesc())) {
                continue;
            }

            Dataprobi1688Msg dataprobi1688Msg = new Dataprobi1688Msg();
            dataprobi1688Msg.setBiinfoId(biInfo.getId());
            dataprobi1688Msg.setUserid(biInfo.getUserid());
            dataprobi1688Msg.setYhid(biInfo.getYhid());
            dataprobi1688Msg.setQd("ALBB");
            dataprobi1688Msg.setDpmc(biInfo.getDpmc());
            dataprobi1688Msg.setSplb(0);
            dataprobi1688Msg.setDpMonths(Collections.emptyList());
            dataprobi1688Msg.setType(1);


            dataprobi1688Msg.setSpxg(rqList);
            dataprobi1688Msg.setSpxgdp(rqList);
            dataprobi1688Msg.setSkazsdzfa(rqList);
            dataprobi1688Msg.setSkazsdzfadp(rqList);
            dataprobi1688Msg.setQztd(rqList);
            dataprobi1688Msg.setQztddp(rqList);
            dataprobi1688Msg.setSkajsc(rqList);
            dataprobi1688Msg.setSkajscdp(rqList);
            dataprobi1688Msg.setGcsyjsjh(rqList);
            dataprobi1688Msg.setGcsyjsjhdp(rqList);
            dataprobi1688Msg.setHxsjczjh(rqList);
            dataprobi1688Msg.setHxsjczjhdp(rqList);
            dataprobi1688Msg.setXfpjjfa(rqList);
            dataprobi1688Msg.setXfpjjfadp(rqList);
            dataprobi1688Msg.setGypjjfa(rqList);
            dataprobi1688Msg.setGypjjfadp(rqList);
            dataprobi1688Msg.setDwtgdp(rqList);
            dataprobi1688Msg.setMxgcdp(rqList);
            dataprobi1688Msg.setPpzqdp(rqList);
            dataprobi1688Msg.setQdyx(rqList);
            dataprobi1688Msg.setQdyxdp(rqList);
            dataprobi1688Msg.setQzxp(rqList);
            dataprobi1688Msg.setQzxpdp(rqList);
            dataprobi1688Msg.setSszbdp(rqList);
            dataprobi1688Msg.setSwzsdp(rqList);
            dataprobi1688Msg.setZgcjjfa(rqList);
            dataprobi1688Msg.setZgcjjfadp(rqList);
            dataprobi1688Msg.setYxhb(rqList);
            dataprobi1688Msg.setCjtf(rqList);
            dataprobi1688Msg.setCjtfdp(rqList);
//            if (zeroList.contains(biInfo.getUserid())) {
//                dataprobi1688Msg.setDzyxjjfa(0);
//            } else {
//                dataprobi1688Msg.setDzyxjjfa(1);
//            }

            SendResult sendResult = aliyunMq2.send("dataprobiv2_jst", dataprobi1688Msg.getUuid(), new JobMessage("dataprobi_v2_albb_processor", dataprobi1688Msg));
            System.out.println(biInfo.getDpmc() + "--->" + sendResult.getMessageId());
        }
    }

    @Test
    public void sendAlbbForYXhb() {
        String json = "{\"2212398971175\":0,\"2218532550897\":1,\"976596893\":1,\"2216401084051\":1,\"2217748139298\":1,\"2216132386528\":1,\"2215731762722\":1,\"2218415931613\":1,\"2208819872353\":1,\"2215932285547\":1,\"2201205083924\":1,\"2217754483703\":1,\"4249631038\":1,\"2207624164417\":1,\"2217739941445\":1,\"3649867756\":1,\"2208344476568\":1,\"2208680587000\":0,\"2216391418164\":1,\"2215867548650\":1,\"2213251209448\":1,\"2217952086777\":1,\"2212989608416\":1,\"2207693986849\":1,\"995440904\":1,\"2209060440997\":1,\"2210073390783\":1,\"2209835317871\":1,\"2212343318350\":0,\"2923763882\":1,\"2217981468871\":1,\"2218770162001\":1,\"2217747015254\":1,\"2218139580493\":1,\"1110836804\":1,\"2200696665166\":1,\"2209471835102\":1,\"2216218680684\":1,\"2200656842459\":1,\"1987222253\":1,\"2216609345871\":1,\"2218267593332\":1,\"2207955654433\":1,\"2209512774067\":1,\"2218610595699\":1,\"2218297560386\":1,\"2218405418791\":1,\"957543758\":1,\"2213085040531\":1,\"2215730169298\":1,\"2212455550485\":1,\"1731562815\":1,\"2218153659542\":1,\"2350212842\":1,\"3827668552\":1,\"2201408120617\":1,\"2214258766192\":1,\"2231458407\":1,\"2218194821630\":1,\"2218333491989\":1,\"2216196554178\":1,\"2859570483\":1,\"2202564068828\":1,\"2206824905429\":1,\"2215273644941\":1,\"2200540232280\":1,\"2218010861973\":1,\"2217430795693\":1,\"2217985286048\":1,\"2212703193155\":1,\"2213255056333\":1,\"2208194636752\":1,\"2217484077637\":1,\"2218503519034\":1,\"3893524655\":1,\"2213116203808\":1,\"2216766044221\":1,\"2200715439594\":1,\"2212507230976\":1,\"2215947161212\":1,\"2217554037396\":1,\"3071826652\":1,\"2210142036853\":1,\"936046705\":1,\"2699257379\":1,\"2214912790621\":1,\"4231843955\":1,\"2215305166978\":1,\"1001536613\":1,\"969102533\":1,\"3841274829\":1,\"2218541816363\":1,\"2207257472699\":1,\"2201440495483\":1,\"2211082910238\":1,\"2217132386341\":1,\"2218998429571\":1,\"2508889521\":1,\"2216009849808\":1,\"2214808441\":1,\"2206529751731\":1,\"2564738854\":1,\"2217660769899\":1,\"2215596423445\":1,\"2998650072\":1,\"2217080371361\":1,\"2218584152478\":1,\"2218076921781\":1,\"3387323147\":1,\"2210363107627\":1,\"2216179098976\":1,\"2212869963183\":1,\"2217161048236\":1,\"2293539989\":1,\"2211983301997\":1,\"2217858466432\":1,\"2217294727366\":1,\"3843345790\":1,\"2458533573\":1,\"3549360317\":1,\"2218384801443\":1,\"4232419424\":1,\"2217339334102\":1,\"2210325910918\":1,\"2217357255654\":1,\"1809484049\":1,\"2217796096548\":1,\"2529752771\":1,\"2216420752256\":1,\"3356314861\":1,\"2211986825199\":1,\"994248217\":1,\"2217641498667\":1,\"2218861181450\":1,\"2217057002272\":1,\"2206658996985\":1,\"1861417161\":1,\"2217488672164\":1,\"2217472353521\":1,\"3999938813\":1,\"2211842590308\":1,\"4256656661\":1,\"2218090456120\":1,\"2217969578652\":1,\"2209091856347\":1,\"2214613541775\":1,\"2218876803600\":1,\"2217253021741\":1,\"2218325658425\":1,\"2217153047268\":1,\"2212392875958\":1,\"2218459626634\":1,\"2215930734940\":1,\"2211363428815\":1,\"2831443953\":1,\"2200773439930\":1,\"2216454198849\":1,\"2511321453\":1,\"2200780408595\":1,\"2209172756824\":1,\"2211421823185\":1,\"2209373229863\":1,\"2218152909968\":1,\"1739268946\":1,\"2209099583410\":1,\"2547901752\":1,\"2214120663591\":1,\"2649803219\":1,\"2216755936486\":1,\"2132619875\":1,\"2215743452431\":1,\"2207868443477\":1,\"2208620584265\":1,\"2217503998128\":1,\"969669240\":0,\"2215935948911\":1,\"2216870299974\":1,\"2218860857456\":1,\"983027274\":0,\"2214805863517\":0,\"2216743968049\":1,\"2200695772816\":1,\"2218047570184\":1,\"1586144057\":1,\"2217707831252\":1,\"2212585355801\":1}";
        JSONObject jsonObject = JSONObject.parseObject(json);
        Objects.requireNonNull(jsonArray);
        List<BiInfo> allBiInfo = DataUploader.getAllBiInfo();

        List<String> rqList = DataprobiDateUtil.getBetweenDate("2024-12-01", "2025-01-06");
        for (BiInfo biInfo : allBiInfo) {
            if (!biInfo.getQd().equals(Qd.ALBB.getQdDesc())) {
                continue;
            }
            Dataprobi1688Msg dataprobi1688Msg = new Dataprobi1688Msg();
            dataprobi1688Msg.setBiinfoId(biInfo.getId());
            dataprobi1688Msg.setUserid(biInfo.getUserid());
            dataprobi1688Msg.setYhid(biInfo.getYhid());
            dataprobi1688Msg.setQd("ALBB");
            dataprobi1688Msg.setDpmc(biInfo.getDpmc());
            dataprobi1688Msg.setSplb(0);
            dataprobi1688Msg.setDpMonths(Collections.emptyList());
            dataprobi1688Msg.setType(1);
            dataprobi1688Msg.setYxhb(rqList);
            int intValue = jsonObject.getIntValue(biInfo.getUserid());
            //dataprobi1688Msg.setDzyxjjfa(intValue);
            SendResult sendResult = aliyunMq2.send("dataprobiv2_jst", dataprobi1688Msg.getUuid(), new JobMessage("dataprobi_v2_albb_processor", dataprobi1688Msg));
            System.out.println(biInfo.getDpmc() + "--->" + sendResult.getMessageId());
        }
    }


    @Test
    public void sendTxByInfoid() {
        List<BiInfo> allBiInfo = DataUploader.getAllBiInfo();
        List<String> rqList = DataprobiDateUtil.getBetweenDate("2024-12-01", "2024-12-25");
        for (BiInfo biInfo : allBiInfo) {
            if (biInfo.getQd().equals(Qd.TM.getQdDesc()) || biInfo.getQd().equals(Qd.TB.getQdDesc())) {
                if (biInfo.getId() <= 9619) {
                    continue;
                }
                DataprobiMsg dataprobiMsg = new DataprobiMsg();
                dataprobiMsg.setBiinfoId(biInfo.getId());
                dataprobiMsg.setUserid(biInfo.getUserid());
                dataprobiMsg.setYhid(biInfo.getYhid());
                dataprobiMsg.setQd(biInfo.getQd());
                dataprobiMsg.setDpmc(biInfo.getDpmc());
                dataprobiMsg.setSplb(0);
                dataprobiMsg.setType(1);
                dataprobiMsg.setAiztone(1);
                dataprobiMsg.setAuto(0);
                dataprobiMsg.setDpMonths(Collections.emptyList());
                dataprobiMsg.setSpxg(rqList);
                dataprobiMsg.setSpxgdp(rqList);
                dataprobiMsg.setQztg(rqList);
                dataprobiMsg.setQztg_tgfx(rqList);
                dataprobiMsg.setZtc(rqList);
                dataprobiMsg.setAizt(rqList);
                dataprobiMsg.setYlmf(rqList);
                dataprobiMsg.setAizt_item(rqList);
                dataprobiMsg.setAizt_shop(rqList);
                dataprobiMsg.setPxb(rqList);
                dataprobiMsg.setRlyq(rqList);
                dataprobiMsg.setRlyqdp(rqList);
                dataprobiMsg.setXedk(rqList);
                dataprobiMsg.setPpxx(rqList);
                dataprobiMsg.setPpxxdp(rqList);
                dataprobiMsg.setNewitem(rqList);
                dataprobiMsg.setYhq(rqList);
                dataprobiMsg.setJhs(rqList);
                dataprobiMsg.setTbxjhb(rqList);
                dataprobiMsg.setNryxdp(rqList);
                dataprobiMsg.setAizt_tgfx(rqList);
                dataprobiMsg.setYlmf_tgfx(rqList);
                dataprobiMsg.setSpsjhz(rqList);
                dataprobiMsg.setZtc_tgfx(rqList);
                dataprobiMsg.setZtc_crowd_tgfx(rqList);
                dataprobiMsg.setZtc_keyword_tgfx(rqList);
                dataprobiMsg.setXkfx(rqList);
                dataprobiMsg.setDmbzt(rqList);
                dataprobiMsg.setFwCustomer(rqList);
                dataprobiMsg.setNryxdsp(rqList);
                dataprobiMsg.setTbk(rqList);
                dataprobiMsg.setTbkdp(rqList);
                dataprobiMsg.setSycmlevel(rqList);
                dataprobiMsg.setSycmflow(rqList);
                dataprobiMsg.setLlzhymfx(rqList);
                dataprobiMsg.setPpxxYghf(rqList);
                dataprobiMsg.setDmbzt_tgfx(rqList);
                dataprobiMsg.setDpmhpdcqddplb(rqList);
                dataprobiMsg.setPjyl(rqList);
                dataprobiMsg.setAiztoneztcitemdjlv(rqList);
                dataprobiMsg.setPpxxYghf(rqList);
                dataprobiMsg.setQndpzhtyf(rqList);
                dataprobiMsg.setXstg(rqList);
                dataprobiMsg.setSycmghtwdp(rqList);
                dataprobiMsg.setSycmghtw(rqList);
                dataprobiMsg.setSycmghsp(rqList);
                dataprobiMsg.setSycmghspdp(rqList);
                dataprobiMsg.setAiztone_dpsummary(rqList);
                dataprobiMsg.setUdzht(rqList);
                dataprobiMsg.setPpxx_yxtg_xp(rqList);
                dataprobiMsg.setPpxx_yxtg_xk(rqList);
                SendResult sendResult = aliyunMq2.send("dataprobiv2_jst", dataprobiMsg.getUuid(), new JobMessage("dataprobi_v2_tx_processor", dataprobiMsg));
                System.out.println("店铺：" + biInfo.getDpmc() + "发送成功，msgid：" + sendResult.getMessageId());
            }
        }
    }


    @Test
    public void sendTX2() {
        List<Integer> idList = Arrays.asList(773, 1598, 2672, 2806, 3184, 4742, 5298, 7395, 9853, 9856, 10397, 11893, 12045, 12066, 12581, 13453, 13562, 14170, 14452, 14931, 14934, 15147, 15170, 15271, 15727, 15775, 15869, 15870, 16038);
        List<BiInfo> allBiInfo = DataUploader.getAllBiInfo();
        List<String> rqList = Collections.singletonList("2025-05-23");
        for (BiInfo biInfo : allBiInfo) {
            if (!idList.contains(biInfo.getId())) {
                continue;
            }
            if (biInfo.getQd().equals(Qd.TM.getQdDesc()) || biInfo.getQd().equals(Qd.TB.getQdDesc())) {
                DataprobiMsg dataprobiMsg = new DataprobiMsg();
                dataprobiMsg.setBiinfoId(biInfo.getId());
                dataprobiMsg.setUserid(biInfo.getUserid());
                dataprobiMsg.setYhid(biInfo.getYhid());
                dataprobiMsg.setQd(biInfo.getQd());
                dataprobiMsg.setDpmc(biInfo.getDpmc());
                dataprobiMsg.setSplb(0);
                dataprobiMsg.setType(1);
                dataprobiMsg.setAiztone(1);
                dataprobiMsg.setAuto(0);
                dataprobiMsg.setDpMonths(Collections.emptyList());
                dataprobiMsg.setPxb(rqList);
                SendResult sendResult = aliyunMq2.send("dataprobiv2_jst", dataprobiMsg.getUuid(), new JobMessage("dataprobi_v2_tx_processor", dataprobiMsg));
                System.out.println("店铺：" + biInfo.getDpmc() + "发送成功，msgid：" + sendResult.getMessageId());
            }
        }
    }

    @Test
    public void sendTGC() {
        List<BiInfo> allBiInfo = DataUploader.getAllBiInfo();
        List<String> rqList = Collections.singletonList("2025-05-11");
//        List<String> rqList = DataprobiDateUtil.getBetweenDate("2025-03-01", "2025-03-26");
        for (BiInfo biInfo : allBiInfo) {
            if (biInfo.getQd().equals(Qd.TGC.getQdDesc())) {
                DataprobiTgcMsg dataprobiTgcMsg = new DataprobiTgcMsg();
                dataprobiTgcMsg.setBiinfoId(biInfo.getId());
                dataprobiTgcMsg.setUserid(biInfo.getUserid());
                dataprobiTgcMsg.setYhid(biInfo.getYhid());
                dataprobiTgcMsg.setQd(biInfo.getQd());
                dataprobiTgcMsg.setDpmc(biInfo.getDpmc());
                dataprobiTgcMsg.setSplb(0);
                dataprobiTgcMsg.setType(1);
                dataprobiTgcMsg.setAuto(0);
                dataprobiTgcMsg.setDpMonths(Collections.emptyList());
                dataprobiTgcMsg.setSpxg(rqList);
//                dataprobiTgcMsg.setSpxgdp(rqList);
//                dataprobiTgcMsg.setSpyytg(rqList);
//                dataprobiTgcMsg.setSptg(rqList);
                SendResult sendResult = aliyunMq2.send("dataprobiv2_jst", dataprobiTgcMsg.getUuid(), new JobMessage("dataprobi_v2_tgc_processor", dataprobiTgcMsg));
                System.out.println("店铺：" + biInfo.getDpmc() + "发送成功，msgid：" + sendResult.getMessageId());
            }
        }
    }

    @Test
    public void sendPDD() {
        List<BiInfo> allBiInfo = DataUploader.getAllBiInfo();
//        List<String> rqList = Collections.singletonList("2024-09-01");
        List<String> rqList = DataprobiDateUtil.getBetweenDate("2024-12-01", "2024-12-25");
        for (BiInfo biInfo : allBiInfo) {
            if (biInfo.getQd().equals(Qd.PDD.getQdDesc())) {
                DataprobiPddMsgBean dataprobiPddMsgBean = new DataprobiPddMsgBean();
                if (biInfo.getId() <= 9619) {
                    continue;
                }
                dataprobiPddMsgBean.setBiinfoId(biInfo.getId());
                dataprobiPddMsgBean.setUserid(biInfo.getUserid());
                dataprobiPddMsgBean.setYhid(biInfo.getYhid());
                dataprobiPddMsgBean.setQd(biInfo.getQd());
                dataprobiPddMsgBean.setDpmc(biInfo.getDpmc());
                dataprobiPddMsgBean.setSplb(0);
                dataprobiPddMsgBean.setType(1);
                dataprobiPddMsgBean.setAuto(0);
                dataprobiPddMsgBean.setDpMonths(Collections.emptyList());
                dataprobiPddMsgBean.setSpxgdp(rqList);
                dataprobiPddMsgBean.setSpxg(rqList);
                dataprobiPddMsgBean.setDdjbdp(rqList);
                dataprobiPddMsgBean.setDdjb(rqList);
                dataprobiPddMsgBean.setDdss(rqList);
                dataprobiPddMsgBean.setDdssdp(rqList);
                dataprobiPddMsgBean.setDdcj(rqList);
                dataprobiPddMsgBean.setDdcjdp(rqList);
                dataprobiPddMsgBean.setQztg(rqList);
                dataprobiPddMsgBean.setQztgtgfx(rqList);
                dataprobiPddMsgBean.setQztgdp(rqList);
                dataprobiPddMsgBean.setMxdp(rqList);
                dataprobiPddMsgBean.setZbtg(rqList);
                dataprobiPddMsgBean.setSppj(rqList);
                dataprobiPddMsgBean.setSptg(rqList);
                dataprobiPddMsgBean.setSptgdp(rqList);
                dataprobiPddMsgBean.setPjyl(rqList);
                dataprobiPddMsgBean.setZhyxdp(rqList);
                dataprobiPddMsgBean.setBztg(rqList);
                dataprobiPddMsgBean.setBztgtgfx(rqList);
                dataprobiPddMsgBean.setBztgdp(rqList);
                dataprobiPddMsgBean.setXkfx(rqList);
                dataprobiPddMsgBean.setJlgg(rqList);
                dataprobiPddMsgBean.setBztgtgfx(rqList);
                dataprobiPddMsgBean.setQztgtgfx(rqList);
                dataprobiPddMsgBean.setSptgtgfx(rqList);
                dataprobiPddMsgBean.setHbdk(rqList);
                SendResult sendResult = aliyunMq2.send("dataprobiv2_jst", dataprobiPddMsgBean.getUuid(), new JobMessage("dataprobi_v2_pdd_processor", dataprobiPddMsgBean));
                System.out.println("店铺：" + biInfo.getDpmc() + "发送成功，msgid：" + sendResult.getMessageId());
            }
        }
    }

    @Test
    public void sendPDD2() {
        List<Integer> list = Arrays.asList(14062, 14110, 14150, 14773);
        List<String> rqList = Collections.singletonList("2025-05-06");
        for (Integer infoId : list) {
            BiInfo biInfo = DataUploader.getBiinfoById(infoId);
            if (biInfo == null) {
                continue;
            }
            if (biInfo.getQd().equals(Qd.PDD.getQdDesc())) {
                DataprobiPddMsgBean dataprobiPddMsgBean = new DataprobiPddMsgBean();
                dataprobiPddMsgBean.setBiinfoId(biInfo.getId());
                dataprobiPddMsgBean.setUserid(biInfo.getUserid());
                dataprobiPddMsgBean.setYhid(biInfo.getYhid());
                dataprobiPddMsgBean.setQd(biInfo.getQd());
                dataprobiPddMsgBean.setDpmc(biInfo.getDpmc());
                dataprobiPddMsgBean.setSplb(0);
                dataprobiPddMsgBean.setType(1);
                dataprobiPddMsgBean.setAuto(0);
                dataprobiPddMsgBean.setDpMonths(Collections.emptyList());
                dataprobiPddMsgBean.setSpxgdp(rqList);
                dataprobiPddMsgBean.setSpxg(rqList);
                dataprobiPddMsgBean.setDdjbdp(rqList);
                dataprobiPddMsgBean.setDdjb(rqList);
                dataprobiPddMsgBean.setDdss(rqList);
                dataprobiPddMsgBean.setDdssdp(rqList);
                dataprobiPddMsgBean.setDdcj(rqList);
                dataprobiPddMsgBean.setDdcjdp(rqList);
                dataprobiPddMsgBean.setQztg(rqList);
                dataprobiPddMsgBean.setQztgtgfx(rqList);
                dataprobiPddMsgBean.setQztgdp(rqList);
                dataprobiPddMsgBean.setMxdp(rqList);
                dataprobiPddMsgBean.setZbtg(rqList);
                dataprobiPddMsgBean.setSppj(rqList);
                dataprobiPddMsgBean.setSptg(rqList);
                dataprobiPddMsgBean.setSptgdp(rqList);
                dataprobiPddMsgBean.setPjyl(rqList);
                dataprobiPddMsgBean.setZhyxdp(rqList);
                dataprobiPddMsgBean.setBztg(rqList);
                dataprobiPddMsgBean.setBztgtgfx(rqList);
                dataprobiPddMsgBean.setBztgdp(rqList);
                dataprobiPddMsgBean.setXkfx(rqList);
                dataprobiPddMsgBean.setJlgg(rqList);
                dataprobiPddMsgBean.setBztgtgfx(rqList);
                dataprobiPddMsgBean.setQztgtgfx(rqList);
                dataprobiPddMsgBean.setSptgtgfx(rqList);
                dataprobiPddMsgBean.setHbdk(rqList);
                dataprobiPddMsgBean.setBlacktag(rqList);
                SendResult sendResult = aliyunMq2.send("dataprobiv2_jst", dataprobiPddMsgBean.getUuid(), new JobMessage("dataprobi_v2_pdd_processor", dataprobiPddMsgBean));
                System.out.println("店铺：" + biInfo.getDpmc() + "发送成功，msgid：" + sendResult.getMessageId());
            }
        }
    }

    @Test
    public void sendJdJzt() {
        List<String> rqList3 = DataprobiDateUtil.getBetweenDate("2024-08-01", "2024-10-08");

        Objects.requireNonNull(jsonArray);
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            String qd = jsonObject.getString("qd");
            if (qd.equals("JD")) {
                DataprobiJdMsgBean dataprobiJdMsgBean = new DataprobiJdMsgBean();
                dataprobiJdMsgBean.setBiinfoId(jsonObject.getIntValue("id"));
                dataprobiJdMsgBean.setUserid(jsonObject.getString("userid"));
                dataprobiJdMsgBean.setYhid(jsonObject.getIntValue("yhid"));
                dataprobiJdMsgBean.setQd("JD");
                dataprobiJdMsgBean.setDpmc(jsonObject.getString("dpmc"));
                dataprobiJdMsgBean.setSplb(0);
                dataprobiJdMsgBean.setDpMonths(Collections.emptyList());
                dataprobiJdMsgBean.setType(2);

                String rqjson = jsonObject.getString("rqjson");
                if (!StringUtil.isJson2(rqjson)) {
                    continue;
                }

                JSONObject rqjsonObject = JSONObject.parseObject(rqjson);
                boolean flag = rqjsonObject.containsKey("xfjl") && rqjsonObject.getString("xfjl").compareTo("2024-08-01") >= 0;
                if (!flag) {
                    continue;
                }
                dataprobiJdMsgBean.setXfjl(rqList3);

                SendResult sendResult = aliyunMq2.send("dataprobiv2_jst", dataprobiJdMsgBean.getUuid(), new JobMessage("dataprobi_v2_jd_processor", dataprobiJdMsgBean));
                System.out.println(jsonObject.getString("dpmc") + "--->" + sendResult.getMessageId());
            }
        }
    }


    @Test
    public void sendMN() {
        List<BiInfo> allBiInfo = DataUploader.getAllBiInfo();
        List<String> rqList = DataprobiDateUtil.getBetweenDate("2024-08-01", "2024-10-16");
        for (BiInfo biInfo : allBiInfo) {
            if (biInfo.getQd().equals(Qd.MN.getQdDesc())) {
                DataprobiMnMsg dataprobiMsg = new DataprobiMnMsg();

                dataprobiMsg.setBiinfoId(biInfo.getId());
                dataprobiMsg.setUserid(biInfo.getUserid());
                dataprobiMsg.setYhid(biInfo.getYhid());
                dataprobiMsg.setQd(biInfo.getQd());
                dataprobiMsg.setDpmc(biInfo.getDpmc());
                dataprobiMsg.setSplb(0);
                dataprobiMsg.setType(2);
                dataprobiMsg.setAuto(0);
                dataprobiMsg.setDpMonths(Collections.emptyList());
                dataprobiMsg.setZtc(rqList);
                dataprobiMsg.setYlmf(rqList);
                dataprobiMsg.setAizt(rqList);
                dataprobiMsg.setDmbzt(rqList);
                dataprobiMsg.setQztg(rqList);
                SendResult sendResult = aliyunMq2.send("dataprobiv2_jst", dataprobiMsg.getUuid(), new JobMessage("dataprobi_v2_mn_processor", dataprobiMsg));
                System.out.println("店铺：" + biInfo.getDpmc() + "发送成功，msgid：" + sendResult.getMessageId());
            }
        }
    }

    @Test
    public void sendJD() {
        List<BiInfo> allBiInfo = DataUploader.getAllBiInfo();
        List<String> rqList = DataprobiDateUtil.getBetweenDate("2025-03-01", "2025-04-27");
        for (BiInfo biInfo : allBiInfo) {
            if (biInfo.getYhid() != 5142) {
                continue;
            }
            if (biInfo.getQd().equals(Qd.JD.getQdDesc())) {
                DataprobiJdMsgBean dataprobiJdMsgBean = new DataprobiJdMsgBean();
                dataprobiJdMsgBean.setBiinfoId(biInfo.getId());
                dataprobiJdMsgBean.setUserid(biInfo.getUserid());
                dataprobiJdMsgBean.setYhid(biInfo.getYhid());
                dataprobiJdMsgBean.setQd(biInfo.getQd());
                dataprobiJdMsgBean.setDpmc(biInfo.getDpmc());
                dataprobiJdMsgBean.setSplb(0);
                dataprobiJdMsgBean.setType(2);
                dataprobiJdMsgBean.setAuto(0);
                dataprobiJdMsgBean.setDpMonths(Collections.emptyList());

                dataprobiJdMsgBean.setWyj(rqList);
                SendResult sendResult = aliyunMq2.send("dataprobiv2_jst", dataprobiJdMsgBean.getUuid(), new JobMessage("dataprobi_v2_jd_processor", dataprobiJdMsgBean));
                System.out.println("店铺：" + biInfo.getDpmc() + "发送成功，msgid：" + sendResult.getMessageId());
            }
        }
    }


    @Test
    public void sendTxDataprobiCompoent() {
        List<BiInfo> allBiInfo = DataUploader.getAllBiInfo();
        List<String> rqList = DataprobiDateUtil.getBetweenDate("2025-01-01", "2025-04-07");
        for (BiInfo biInfo : allBiInfo) {
            if (biInfo.getQd().equals(Qd.TM.getQdDesc()) || biInfo.getQd().equals(Qd.TB.getQdDesc())) {
                DataprobiMsg dataprobiMsg = new DataprobiMsg();

                dataprobiMsg.setBiinfoId(biInfo.getId());
                dataprobiMsg.setUserid(biInfo.getUserid());
                dataprobiMsg.setYhid(biInfo.getYhid());
                dataprobiMsg.setQd(biInfo.getQd());
                dataprobiMsg.setDpmc(biInfo.getDpmc());
                dataprobiMsg.setSplb(0);
                dataprobiMsg.setType(2);
                dataprobiMsg.setAuto(0);
                dataprobiMsg.setDpMonths(Collections.emptyList());


                dataprobiMsg.setZtc(rqList);
                dataprobiMsg.setYlmf(rqList);
                dataprobiMsg.setAizt(rqList);
                SendResult sendResult = aliyunMq2.send("dataprobi_compoent", UUID.randomUUID().toString(), new JobMessage("dataprobi_compoent_processor", dataprobiMsg));
                System.out.println("店铺：" + biInfo.getDpmc() + "发送成功，msgid：" + sendResult.getMessageId());
            }
        }
    }


    @Test
    public void sendXHS() {
        List<BiInfo> allBiInfo = DataUploader.getAllBiInfo();
        List<String> rqList = DataprobiDateUtil.getBetweenDate("2024-12-01", "2024-12-25");
        for (BiInfo biInfo : allBiInfo) {
            if (biInfo.getQd().equals(Qd.XHS.getQdDesc())) {
                if (biInfo.getId() <= 9619) {
                    continue;
                }
                DataprobiXhsMsg dataprobiXhsMsg = new DataprobiXhsMsg();
                dataprobiXhsMsg.setUserid(biInfo.getUserid());
                dataprobiXhsMsg.setYhid(biInfo.getYhid());
                dataprobiXhsMsg.setQd(biInfo.getQd());
                dataprobiXhsMsg.setDpmc(biInfo.getDpmc());
                dataprobiXhsMsg.setBiinfoId(biInfo.getId());
                dataprobiXhsMsg.setType(1);
                dataprobiXhsMsg.setSplb(0);

                dataprobiXhsMsg.setSpxg(rqList);
                dataprobiXhsMsg.setSpxgdp(rqList);
                dataprobiXhsMsg.setLlsjdp(rqList);
                dataprobiXhsMsg.setJgptbjtg(rqList);
                dataprobiXhsMsg.setJgptbjtgdp(rqList);
                dataprobiXhsMsg.setJgptzbfxdp(rqList);
                dataprobiXhsMsg.setQfbjyx(rqList);
                dataprobiXhsMsg.setQfzbyxdp(rqList);
                dataprobiXhsMsg.setCfsptg(rqList);
                dataprobiXhsMsg.setCfsptgdp(rqList);
                dataprobiXhsMsg.setCfzbtg(rqList);
                dataprobiXhsMsg.setCfzbtgdp(rqList);
                SendResult sendResult = aliyunMq2.send("dataprobiv2_jst", dataprobiXhsMsg.getUuid(), new JobMessage("dataprobi_v2_xhs_processor", dataprobiXhsMsg));
                System.out.println("小红书：" + dataprobiXhsMsg.getDpmc() + "消息发送成功，消息msgId：" + sendResult.getMessageId());
            }
        }
    }

    @Test
    public void testForTx() {
        List<BiInfo> allBiInfo = DataUploader.getAllBiInfo();
        List<String> rqList = DataprobiDateUtil.getBetweenDate("2025-05-01", "2025-05-26");
        for (BiInfo biInfo : allBiInfo) {
            if (biInfo.getQd().equals("TB") || biInfo.getQd().equals("TM")) {
                DataprobiTaskMsg taskMsg = new DataprobiTaskMsg();
                taskMsg.setDpmc(biInfo.getDpmc());
                taskMsg.setQd(biInfo.getQd());
                taskMsg.setTaskType(TaskTypeConst.TX_YGHF_BYBT);
                taskMsg.setYhid(biInfo.getYhid());
                taskMsg.setUserid(biInfo.getUserid());
                taskMsg.setType(DataprobiConst.TYPE_DATAPROBITASK);
                taskMsg.setRqList(rqList);
                aliyunMq2.send("dataprobi_task", UUID.randomUUID().toString(), new JobMessage("dataprobi_task_processor", taskMsg));
            }
        }
    }

    @Test
    public void sendDyKf() {
        List<BiInfo> allBiInfo = DataUploader.getAllBiInfo();
        List<String> rqList = DataprobiDateUtil.getBetweenDate("2025-01-01", "2025-01-14");
        int i = 0;
        for (BiInfo biInfo : allBiInfo) {
            if (!biInfo.getQd().equals(Qd.DY.getQdDesc())) {
                continue;
            }
            i++;
            DataprobiTaskMsg taskMsg = new DataprobiTaskMsg();
            taskMsg.setDpmc(biInfo.getDpmc());
            taskMsg.setTaskType(TaskTypeConst.DY_TGFX_TSP);
            taskMsg.setQd(biInfo.getQd());
            taskMsg.setYhid(biInfo.getYhid());
            taskMsg.setRqList(rqList);
            taskMsg.setUserid(biInfo.getUserid());
            taskMsg.setType(DataprobiConst.TYPE_DATAPROBITASK);
            taskMsg.setUuid(UUID.randomUUID().toString());
            aliyunMq2.send("dataprobi_task", UUID.randomUUID().toString(), new JobMessage("dataprobi_task_processor", taskMsg));
        }
        System.out.println(i);
    }


    @Test
    public void sendDyBztgTgfx() {
        List<BiInfo> allBiInfo = DataUploader.getAllBiInfo();
        List<String> rqList = DataprobiDateUtil.getBetweenDate("2025-04-01", "2025-04-07");
        for (BiInfo biInfo : allBiInfo) {
            if (!biInfo.getQd().equals(Qd.DY.getQdDesc())) {
                continue;
            }
            DataprobiTaskMsg taskMsg = new DataprobiTaskMsg();
            taskMsg.setDpmc(biInfo.getDpmc());
            taskMsg.setTaskType(TaskTypeConst.DY_YGHF_JXLM_TZFWF);
            taskMsg.setQd(biInfo.getQd());
            taskMsg.setYhid(biInfo.getYhid());
            taskMsg.setRqList(rqList);
            taskMsg.setUserid(biInfo.getUserid());
            taskMsg.setType(DataprobiConst.TYPE_DATAPROBITASK);
            taskMsg.setUuid(UUID.randomUUID().toString());
            aliyunMq2.send("dataprobi_task", UUID.randomUUID().toString(), new JobMessage("dataprobi_task_processor", taskMsg));
        }
    }

    @Test
    public void sendTxZtcTgfx() {
        List<BiInfo> allBiInfo = DataUploader.getAllBiInfo();
        List<String> rqList = DataprobiDateUtil.getBetweenDate("2025-02-01", "2025-02-24");
        for (BiInfo biInfo : allBiInfo) {
            if (biInfo.getQd().equals(Qd.TM.getQdDesc()) || biInfo.getQd().equals(Qd.TB.getQdDesc())) {
                DataprobiTaskMsg taskMsg = new DataprobiTaskMsg();
                taskMsg.setDpmc(biInfo.getDpmc());
                taskMsg.setTaskType(TaskTypeConst.TX_TGFX_ZTC);
                taskMsg.setQd(biInfo.getQd());
                taskMsg.setYhid(biInfo.getYhid());
                taskMsg.setRqList(rqList);
                taskMsg.setUserid(biInfo.getUserid());
                taskMsg.setUuid(UUID.randomUUID().toString());
                aliyunMq2.send("dataprobi_tgfx", UUID.randomUUID().toString(), new JobMessage("dataprobi_tgfx_processor", taskMsg));

                taskMsg.setTaskType(TaskTypeConst.TX_TGFX_ZTC_KEYWORD);
                aliyunMq2.send("dataprobi_tgfx", UUID.randomUUID().toString(), new JobMessage("dataprobi_tgfx_processor", taskMsg));

                taskMsg.setTaskType(TaskTypeConst.TX_TGFX_ZTC_CROWD);
                aliyunMq2.send("dataprobi_tgfx", UUID.randomUUID().toString(), new JobMessage("dataprobi_tgfx_processor", taskMsg));
            }
        }
    }


    @Test
    public void sendJdQzyx() {
        List<BiInfo> allBiInfo = DataUploader.getAllBiInfo();
        List<String> rqList1 = DataprobiDateUtil.getBetweenDate("2025-01-01", DataprobiDateUtil.getLastday());
        for (BiInfo biInfo : allBiInfo) {
            if (biInfo.getQd().equals(Qd.JD.getQdDesc())) {
                DataprobiTaskMsg taskMsg = new DataprobiTaskMsg();
                taskMsg.setDpmc(biInfo.getDpmc());
                taskMsg.setTaskType(TaskTypeConst.JD_TGFX_QZYX);
                taskMsg.setQd(biInfo.getQd());
                taskMsg.setYhid(biInfo.getYhid());
                taskMsg.setRqList(rqList1);
                taskMsg.setUserid(biInfo.getUserid());
                taskMsg.setUuid(UUID.randomUUID().toString());
                aliyunMq2.send("dataprobi_tgfx", UUID.randomUUID().toString(), new JobMessage("dataprobi_tgfx_processor", taskMsg));
            }
        }
    }

    @Test
    public void sendTghfConfig() {
        BiInfo biInfo = DataUploader.getBiinfoById(3);
        if (biInfo == null) {
            return;
        }
        List<String> rqList = DataprobiDateUtil.getBetweenDate("2025-03-01", "2025-03-02");
        DataprobiBaseMsgBean2 msgBean = new DataprobiBaseMsgBean2();
        msgBean.setUserid(biInfo.getUserid());
        msgBean.setYhid(biInfo.getYhid());
        msgBean.setQd(biInfo.getQd());
        msgBean.setDpmc(biInfo.getDpmc());
        msgBean.setBiinfoId(biInfo.getId());
        //0：无变动  1、品销宝 2、万相台  3、dou+   4、千川全域
        msgBean.setLx(1);
        msgBean.setRqList(rqList);
        SendResult sendResult = aliyunMq2.send("dataprobiv2_jst", msgBean.getUuid(), new JobMessage("dataprobi_v2_tghf_config_processor", msgBean));
        System.out.println("推广花费设置重算：" + msgBean.getDpmc() + "消息发送成功，消息msgId：" + sendResult.getMessageId());
    }
}
