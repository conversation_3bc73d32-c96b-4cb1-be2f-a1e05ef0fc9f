package com.abujlb.dataprobi.test;

import com.abujlb.BaseTest;
import com.abujlb.dataprobi.bean.BiInfo;
import com.abujlb.dataprobi.bean.DataprobiBaseMsgBean2;
import com.abujlb.dataprobi.msg.TghfConfigAliyunMsgProcessor;
import com.abujlb.dataprobi.server.DataUploader;
import com.abujlb.mq.AliyunMq2;
import com.abujlb.mq.JobMessage;
import com.aliyun.openservices.ons.api.SendResult;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.lang.reflect.InvocationTargetException;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2022/8/31 9:15
 */
public class TestBiTghfConfigProcesses extends BaseTest {

    @Autowired
    private AliyunMq2 aliyunMq2;

    @Autowired
    private TghfConfigAliyunMsgProcessor tghfConfigAliyunMsgProcessor;

    @Test
    public void runLocal() throws InvocationTargetException, IllegalAccessException {
        BiInfo biInfo = DataUploader.getBiinfoById(2);
        if (biInfo == null) {
            return;
        }

        DataprobiBaseMsgBean2 msgBean = new DataprobiBaseMsgBean2();
        msgBean.setUserid(biInfo.getUserid());
        msgBean.setYhid(biInfo.getYhid());
        msgBean.setQd(biInfo.getQd());
        msgBean.setBiinfoId(biInfo.getId());
        msgBean.setDpmc(biInfo.getDpmc());
        msgBean.setRqList(Arrays.asList("2025-03-03", "2025-03-04"));
        msgBean.setLx(1);
        tghfConfigAliyunMsgProcessor.process(msgBean);
    }

    @Test
    public void sendMqJst() {
        BiInfo biInfo = DataUploader.getBiinfoById(2);
        if (biInfo == null) {
            return;
        }

        DataprobiBaseMsgBean2 msgBean = new DataprobiBaseMsgBean2();
        msgBean.setUserid(biInfo.getUserid());
        msgBean.setYhid(biInfo.getYhid());
        msgBean.setQd(biInfo.getQd());
        msgBean.setBiinfoId(biInfo.getId());
        msgBean.setDpmc(biInfo.getDpmc());
        msgBean.setRqList(Arrays.asList("2025-03-03", "2025-03-04"));
        msgBean.setLx(1);
        SendResult sendResult = aliyunMq2.send("dataprobiv2_jst", msgBean.getUuid(), new JobMessage("dataprobi_v2_tghf_config_processor", msgBean));
        System.out.println("推广花费设置重算：" + msgBean.getDpmc() + "消息发送成功，消息msgId：" + sendResult.getMessageId());
    }
}
