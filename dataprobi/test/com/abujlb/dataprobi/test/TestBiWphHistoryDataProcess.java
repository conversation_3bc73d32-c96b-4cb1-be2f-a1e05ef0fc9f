package com.abujlb.dataprobi.test;

import com.abujlb.AbujlbBeanFactory;
import com.abujlb.BaseTest;
import com.abujlb.dataprobi.bean.BiInfo;
import com.abujlb.dataprobi.bean.BiRuleReRunTask;
import com.abujlb.dataprobi.bean.SqliteSp;
import com.abujlb.dataprobi.bean.wph.DataprobiWphMsg;
import com.abujlb.dataprobi.constant.DataprobiConst;
import com.abujlb.dataprobi.oss.BiDataOssUtil;
import com.abujlb.dataprobi.processes.wph.*;
import com.abujlb.dataprobi.server.DataUploader;
import com.abujlb.dataprobi.service.BiWxsphDataDealService;
import com.abujlb.dataprobi.sql.BiSqliteDb;
import com.abujlb.dataprobi.threads.BiThreadLocals;
import com.abujlb.dataprobi.util.DataprobiDateUtil;
import com.abujlb.dataprobi.util.FileUtil;
import com.abujlb.mq.AliyunMq2;
import com.abujlb.mq.JobMessage;
import com.abujlb.sqlite.SqliteStatement;
import com.aliyun.openservices.ons.api.SendResult;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.File;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2024/5/7
 */
public class TestBiWphHistoryDataProcess extends BaseTest {

    private final DataprobiWphMsg dataprobiWphMsg;
    @Autowired
    private AbujlbBeanFactory abujlbBeanFactory;
    @Autowired
    private AliyunMq2 aliyunMq2;
    @Autowired
    private BiDataOssUtil biDataOssUtil;

    {
        final int biinfoId = 12015;
        BiInfo biInfo = DataUploader.getBiinfoById(biinfoId);
        if (biInfo == null) {
            throw new RuntimeException("获取Biinfo失败，Biinfo的ID为：" + biinfoId);
        }
        dataprobiWphMsg = new DataprobiWphMsg();
        dataprobiWphMsg.setUserid(biInfo.getUserid());
        dataprobiWphMsg.setYhid(biInfo.getYhid());
        dataprobiWphMsg.setQd(biInfo.getQd());
        dataprobiWphMsg.setDpmc(biInfo.getDpmc());
        dataprobiWphMsg.setBiinfoId(biInfo.getId());
        dataprobiWphMsg.setType(1);
        dataprobiWphMsg.setSplb(1);

        List<String> rqList1 = Collections.singletonList("2025-02-01");
        List<String> rqList2 = Collections.emptyList();
        List<String> rqList = DataprobiDateUtil.getBetweenDate("2025-01-01", "2025-03-06");

        dataprobiWphMsg.setSpxg(rqList);
        dataprobiWphMsg.setSpxgdp(rqList2);
        dataprobiWphMsg.setCtsplb(rqList2);
        dataprobiWphMsg.setWxkjljdp(rqList2);
        dataprobiWphMsg.setWxkyjyghf(rqList2);
        //dataprobiWphMsg.setZwaqyhfdp(rqList2);
        dataprobiWphMsg.setZwaqyjljdp(rqList2);
        dataprobiWphMsg.setZwjlyqjljdp(rqList2);
        //dataprobiWphMsg.setZwjlyqhfdp(rqList2);

        BiThreadLocals.init(dataprobiWphMsg);
    }

    /**
     * 更新数据库文件，删除bbid为空的记录
     */
    @Test
    public void testUpdateDb() {
        String startDate = "2025-02-01";
        int processedFiles = 0;
        
        // 获取当前线程的消息Bean
        DataprobiWphMsg msgBean = (DataprobiWphMsg)BiThreadLocals.getMsgBean();
        if (msgBean == null) {
            System.out.println("消息Bean为空，无法执行更新操作");
            return;
        }
        
        int yhid = msgBean.getYhid();
        String qd = msgBean.getQd();
        String userid = msgBean.getUserid();
        
        // 获取从开始日期到当前日期的所有日期
        List<String> dateList = DataprobiDateUtil.getBetweenDate(startDate, LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        System.out.println("开始处理日期范围: " + startDate + " 到 " + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        System.out.println("总日期数: " + dateList.size());
        
        for (String date : dateList) {
            // 构建OSS路径
            String formattedDate = date.replace("-", "");
            String ossPath = String.format("bi_data/day/%s/%s/%s/%s/sycm_sp.db", formattedDate, yhid, qd, userid);
            
            // 检查文件是否存在
            if (!biDataOssUtil.exist(ossPath)) {
                System.out.println("文件不存在，跳过: " + ossPath);
                continue;
            }
            
            System.out.println("处理文件: " + ossPath);
            
            // 下载数据库文件
            String dbFilePath = FileUtil.createDBFilePath();
            BiSqliteDb sqliteDb = null;
            SqliteStatement<SqliteSp> selectStatement = null;
            
            try {
                // 下载文件
                biDataOssUtil.download(ossPath, dbFilePath);
                sqliteDb = new BiSqliteDb(dbFilePath);
                
                // 查询所有记录
                selectStatement = sqliteDb.createSqliteStatement("SELECT * FROM sycm_sp;", SqliteSp.class);
                List<SqliteSp> spList = selectStatement.queryForList(null, SqliteSp.class);
                
                if (spList == null || spList.isEmpty()) {
                    System.out.println("文件中没有记录，跳过: " + ossPath);
                    continue;
                }
                
                // 找出bbid为空的记录
                List<SqliteSp> emptyBbidList = new ArrayList<>();
                for (SqliteSp sp : spList) {
                    if (StringUtils.isBlank(sp.getBbid())) {
                        emptyBbidList.add(sp);
                        System.out.println("空bbid记录: " + sp.getQd_userid_bbid());
                    }
                }
                
                if (emptyBbidList.isEmpty()) {
                    System.out.println("没有bbid为空的记录，跳过: " + ossPath);
                    continue;
                }
                
                System.out.println("找到 " + emptyBbidList.size() + " 条bbid为空的记录，准备删除");
                
                // 开始事务
                sqliteDb.startTransaction();
                
                // 使用主键qd_userid_bbid来删除记录
                for (SqliteSp sp : emptyBbidList) {
                    SqliteStatement<SqliteSp> deleteStatement = null;
                    try {
                        // 使用主键qd_userid_bbid作为删除条件
                        deleteStatement = sqliteDb.createSqliteStatement("DELETE FROM sycm_sp WHERE qd_userid_bbid={qd_userid_bbid};", SqliteSp.class);
                        deleteStatement.delete(sp);
                    } finally {
                        if (deleteStatement != null) {
                            try {
                                deleteStatement.close();
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        }
                    }
                }
                
                System.out.println("成功删除 " + emptyBbidList.size() + " 条记录");
                
                // 提交事务
                sqliteDb.commit();
                
                // 上传修改后的文件
                File file = new File(dbFilePath);
                if (file.exists()) {
                    biDataOssUtil.upload(file, ossPath);
                    System.out.println("成功上传修改后的文件: " + ossPath);
                    processedFiles++;
                }
            } catch (Exception e) {
                System.err.println("处理文件时出错: " + ossPath);
                e.printStackTrace();
                
                // 回滚事务
                if (sqliteDb != null) {
                    try {
                        sqliteDb.rollback();
                    } catch (Exception rollbackEx) {
                        rollbackEx.printStackTrace();
                    }
                }
            } finally {
                // 关闭语句
                if (selectStatement != null) {
                    try {
                        selectStatement.close();
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
                
                // 关闭数据库连接
                if (sqliteDb != null) {
                    try {
                        sqliteDb.close();
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
                
                // 删除临时文件
                File tempFile = new File(dbFilePath);
                if (tempFile.exists()) {
                    boolean deleted = tempFile.delete();
                    if (!deleted) {
                        System.out.println("无法删除临时文件: " + dbFilePath);
                    }
                }
            }
        }
        
        System.out.println("处理完成，共处理 " + processedFiles + " 个文件");
    }

    @Test
    public void commitRerunTask() {
        // 获取所有的 BiInfo 对象
        //List<BiInfo> biInfoList1 = DataUploader.getAllBiInfo();
        // 过滤出 qd 等于 "XHS" 的 BiInfo 对象
//        List<BiInfo> filteredBiInfoList = biInfoList1.stream()
//                .filter(biInfo -> "XHS".equals(biInfo.getQd()))
//                .collect(Collectors.toList());
//        List<BiInfo> filteredBiInfoList = biInfoList1.stream()
//                .filter(biInfo -> "XHS".equals(biInfo.getQd()) && "22790".equals(biInfo.getUserid()))
//                .collect(Collectors.toList());
        final int biinfoId = 12015;
        BiInfo biInfo1 = DataUploader.getBiinfoById(biinfoId);
        List<BiInfo> filteredBiInfoList = new ArrayList<>();
        filteredBiInfoList.add(biInfo1);
        for (BiInfo biInfo : filteredBiInfoList) {
            //BiInfo biInfo = DataUploader.getBiinfoById(biinfoId);
            BiRuleReRunTask task = new BiRuleReRunTask();
            task.setId("sycm_dp_" + UUID.randomUUID());
            task.setYhid(biInfo.getYhid());
            task.setQd(biInfo.getQd());
            task.setUserid(biInfo.getUserid());
            task.setKsrq("2025-02-01");
            task.setJsrq("2025-03-06");
            task.setCjsj(DataprobiDateUtil.getCurrentTime());
            task.setZt(0);
            task.setType(16);
            task.setCjrxm("唯品会商品效果数据");
            task.setReason("补采重算");
            if (task.getKsrq().compareTo(task.getJsrq()) <= 0) {
                DataUploader.rerunTask(task);
            }

        }

    }



    /**
     * 批量删除OSS文件
     * 读取并删除oss://ecbisjson2/bi_data/day/20250101/21385/WPH/94DBD46499F9E32F83B038A384CBB2C3/下的特定文件
     * 需要删除的文件：sycm_data.db、sycm_dp.db、sycm_sp.db、ywfyd.xlsx、zdgl.xlsx
     * 日期范围从20250101到20250429
     */
    @Test
    public void batchDeleteOssFiles() {
        String baseOssPath = "bi_data/day/";
        String dpid = "21385";
        String platformType = "WPH";
        String userId = "94DBD46499F9E32F83B038A384CBB2C3";

        // 需要删除的文件列表
        String[] specificFiles = {
                "sycm_dp.db",
                "sycm_sp.db",
                "ywfyd.xlsx",
                "zdgl.xlsx"
        };

        LocalDate startDate = LocalDate.of(2025, 1, 1);
        LocalDate endDate = LocalDate.of(2025, 4, 29);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");

        LocalDate currentDate = startDate;
        int totalDeletedCount = 0;
        int processedDaysCount = 0;

        while (!currentDate.isAfter(endDate)) {
            String dateStr = currentDate.format(formatter);
            String ossDirPath = baseOssPath + dateStr + "/" + dpid + "/" + platformType + "/" + userId + "/";

            // 检查目录是否存在
            System.out.println("处理目录: " + ossDirPath);
            processedDaysCount++;

            int dayDeletedCount = 0;
            // 删除指定的文件
            for (String fileName : specificFiles) {
                String fullFilePath = ossDirPath + fileName;
                if (biDataOssUtil.exist(fullFilePath)) {
                    biDataOssUtil.delete(fullFilePath);
                    dayDeletedCount++;
                    totalDeletedCount++;
                    System.out.println("已删除文件: " + fullFilePath);
                }
            }

            System.out.println("日期 " + dateStr + " 处理完成，删除了 " + dayDeletedCount + " 个文件");


            // 移动到下一天
            currentDate = currentDate.plusDays(1);
        }

        System.out.println("批量删除完成，共处理 " + processedDaysCount + " 天的数据，删除 " + totalDeletedCount + " 个文件");
    }
}