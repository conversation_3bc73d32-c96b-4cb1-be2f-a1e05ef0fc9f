package com.abujlb.dataprobi.test;

import com.abujlb.AbujlbBeanFactory;
import com.abujlb.BaseTest;
import com.abujlb.dataprobi.bean.BiInfo;
import com.abujlb.dataprobi.bean.dw.DataprobiDwMsg;
import com.abujlb.dataprobi.constant.DataprobiConst;
import com.abujlb.dataprobi.oss.BiDataOssUtil;
import com.abujlb.dataprobi.processes.dw.*;
import com.abujlb.dataprobi.server.DataUploader;
import com.abujlb.dataprobi.service.BiDwDataDealService;
import com.abujlb.dataprobi.threads.BiThreadLocals;
import com.abujlb.dataprobi.util.DataprobiDateUtil;
import com.abujlb.mq.AliyunMq2;
import com.abujlb.mq.JobMessage;
import com.aliyun.openservices.ons.api.SendResult;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/7
 */
public class TestBiDwProcess extends BaseTest {
    private final DataprobiDwMsg dataprobiDwMsg;
    @Autowired
    private AbujlbBeanFactory abujlbBeanFactory;
    @Autowired
    private AliyunMq2 aliyunMq2;
    @Autowired
    private BiDataOssUtil biDataOssUtil;


    {
        final int biinfoId = 16619;
        BiInfo biInfo = DataUploader.getBiinfoById(biinfoId);
        if (biInfo == null) {
            throw new RuntimeException("获取Biinfo失败，Biinfo的ID为：" + biinfoId);
        }


//        BiInfo biInfo = new BiInfo();
//        biInfo.setId(1);
//        biInfo.setUserid("8198145");
//        biInfo.setQd("DW");
//        biInfo.setDpmc("台州市暖物家居用品有限公司");
//        biInfo.setYhid(1);
        dataprobiDwMsg = new DataprobiDwMsg();
        dataprobiDwMsg.setUserid(biInfo.getUserid());
        dataprobiDwMsg.setYhid(biInfo.getYhid());
        dataprobiDwMsg.setQd(biInfo.getQd());
        dataprobiDwMsg.setDpmc(biInfo.getDpmc());
        dataprobiDwMsg.setBiinfoId(biInfo.getId());
        dataprobiDwMsg.setType(2);
        dataprobiDwMsg.setSplb(0);



//        Lists.newArrayList("2023-05-22", "2023-06-30", "2023-09-04", "2023-09-27", "2024-06-12", "2024-07-05")
//        List<String> rqList = DataprobiDateUtil.getBetweenDate("2024-10-01", "2024-11-28");
        List<String> rqList =Collections.emptyList() ;
        List<String> rqList2 = Collections.singletonList("2025-06-15") ;
        dataprobiDwMsg.setSjspfx(rqList2);
        dataprobiDwMsg.setSjspfxdp(rqList2);
        dataprobiDwMsg.setYxdwttgsj(rqList2);
        dataprobiDwMsg.setYxdwttgsjdp(rqList2);
        dataprobiDwMsg.setYxzbtg(rqList2);
        dataprobiDwMsg.setXqxhmx(rqList2);
        dataprobiDwMsg.setYxdwtcwls(rqList2);
        dataprobiDwMsg.setYltfCzjl(rqList2);
        dataprobiDwMsg.setYltfXhjl(rqList2);
        dataprobiDwMsg.setYxnryypjyx(rqList2);


        BiThreadLocals.init(dataprobiDwMsg);
    }


    @Test
    public void spxg() {
        DefaultBiDwSjSpxgProcess bean = abujlbBeanFactory.getBean(DefaultBiDwSjSpxgProcess.class);
        bean.dealGoods();
        bean.dealShop();
        System.out.println("处理商品效果数据完成");
    }

    @Test
    public void dwtTgsj() {
        DefaultBiDwYxDwtTgsjProcess bean = abujlbBeanFactory.getBean(DefaultBiDwYxDwtTgsjProcess.class);
        bean.dealGoods();
        bean.dealShop();
        System.out.println("得物推广数据完成");
    }


    @Test
    public void yxzbt() {
        DefaultBiDwYxZbtgProcess bean = abujlbBeanFactory.getBean(DefaultBiDwYxZbtgProcess.class);
        bean.dealGoods();
        System.out.println("营销直播通数据完成");
    }

    @Test
    public void xqxhmx() {
        DefaultBiDwXqXhmxProcess bean = abujlbBeanFactory.getBean(DefaultBiDwXqXhmxProcess.class);
        bean.dealShop();
        System.out.println("星桥消耗明细数据处理完成");
    }
    @Test
    public void dwtcwls() {
        DefaultBiDwYxDwtCwlsProcess bean = abujlbBeanFactory.getBean(DefaultBiDwYxDwtCwlsProcess.class);
        bean.dealShop();
        System.out.println("得物推财务流水数据处理完成");
    }
    @Test
    public void pjyx() {
        DefaultBiYxNryyPjyxProcess bean = abujlbBeanFactory.getBean(DefaultBiYxNryyPjyxProcess.class);
        bean.dealShop();
        System.out.println("评价营销数据处理完成");
    }
    @Test
    public void yltfCzjl() {
        DefaultBiDwYltfCzjlProcess bean = abujlbBeanFactory.getBean(DefaultBiDwYltfCzjlProcess.class);
        bean.dealShop();
        System.out.println("引力投放充值记录数据处理完成");
    }
    @Test
    public void yltfXhjl() {
        DefaultBiDwYltfXhjlProcess bean = abujlbBeanFactory.getBean(DefaultBiDwYltfXhjlProcess.class);
        bean.dealShop();
        System.out.println("引力投放消耗记录数据处理完成");
    }

    /**
     * @param : return void
     * <AUTHOR>
     * @date 2024/10/18
     * @description 本地跑
     */
    @Test
    public void runLocal() {
        BiThreadLocals.getMsgBean().setType(1);
        BiDwDataDealService dwDataDealService = abujlbBeanFactory.getBean(BiDwDataDealService.class);
        dwDataDealService.process();
    }

    @Test
    public void deldata() {
        List<String> rqList = DataprobiDateUtil.getBetweenDate("2024-10-01", "2024-10-31");
        for (String rq : rqList){
            biDataOssUtil.delete(String.format(DataprobiConst.SYCM_SP_DAY, rq.replaceAll("-", ""), 9841, "DW", 6060526));
            biDataOssUtil.delete(String.format(DataprobiConst.SYCM_DP_DAY, rq.replaceAll("-", ""), 9841, "DW", 6060526));
        }

    }




    /**
     * @param : return void
     * <AUTHOR>
     * @date 2024/10/18
     * @description 发送消息服务器跑
     */
    @Test
    public void sendMq() {
        SendResult sendResult = aliyunMq2.send("dataprobiv2_jst", dataprobiDwMsg.getUuid(), new JobMessage("dataprobi_v2_dw_processor", dataprobiDwMsg));
        System.out.println("得物：" + dataprobiDwMsg.getDpmc() + "消息发送成功，消息msgId：" + sendResult.getMessageId());
    }
}