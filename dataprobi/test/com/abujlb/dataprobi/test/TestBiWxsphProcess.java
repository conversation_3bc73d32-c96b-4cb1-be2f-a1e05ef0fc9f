package com.abujlb.dataprobi.test;

import com.abujlb.AbujlbBeanFactory;
import com.abujlb.BaseTest;
import com.abujlb.dataprobi.bean.BiInfo;
import com.abujlb.dataprobi.bean.wxsph.DataprobiWxsphMsg;
import com.abujlb.dataprobi.bean.xhs.DataprobiXhsMsg;
import com.abujlb.dataprobi.processes.wxsph.*;
import com.abujlb.dataprobi.processes.xhs.*;
import com.abujlb.dataprobi.server.DataUploader;
import com.abujlb.dataprobi.service.BiWxsphDataDealService;
import com.abujlb.dataprobi.service.BiXhsDataDealService;
import com.abujlb.dataprobi.threads.BiThreadLocals;
import com.abujlb.dataprobi.util.DataprobiDateUtil;
import com.abujlb.mq.AliyunMq2;
import com.abujlb.mq.JobMessage;
import com.aliyun.openservices.ons.api.SendResult;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/7
 */
public class TestBiWxsphProcess extends BaseTest {



    private final DataprobiWxsphMsg dataprobiWxsphMsg;
    @Autowired
    private AbujlbBeanFactory abujlbBeanFactory;
    @Autowired
    private AliyunMq2 aliyunMq2;

    {
        //9082,8247,8246,7544,6971,6959,6958,6957,6956,6955,6939,6584
        final int biinfoId = 6956;
        BiInfo biInfo = DataUploader.getBiinfoById(biinfoId);
        if (biInfo == null) {
            throw new RuntimeException("获取Biinfo失败，Biinfo的ID为：" + biinfoId);
        }
        dataprobiWxsphMsg = new DataprobiWxsphMsg();
        dataprobiWxsphMsg.setUserid(biInfo.getUserid());
        dataprobiWxsphMsg.setYhid(biInfo.getYhid());
        dataprobiWxsphMsg.setQd(biInfo.getQd());
        dataprobiWxsphMsg.setDpmc(biInfo.getDpmc());
        dataprobiWxsphMsg.setBiinfoId(biInfo.getId());
        dataprobiWxsphMsg.setType(1);
        dataprobiWxsphMsg.setSplb(1);


        List<String> rqList1 = Collections.singletonList("2024-11-21");
//        List<String> rqList = Collections.emptyList();
        List<String> rqList = DataprobiDateUtil.getBetweenDate("2025-01-06", "2025-01-14");

        dataprobiWxsphMsg.setSpxg(rqList);
        dataprobiWxsphMsg.setSpxgdp(rqList);
        dataprobiWxsphMsg.setXsqgdp(rqList);
        dataprobiWxsphMsg.setYhqdp(rqList);
        dataprobiWxsphMsg.setCxztdspdp(rqList);
        dataprobiWxsphMsg.setCxztzbjdp(rqList);
        dataprobiWxsphMsg.setCxztdsp(rqList);




        BiThreadLocals.init(dataprobiWxsphMsg);
    }


    @Test
    public void spxg() {
        DefaultbiWxsphSpxgProcess bean = abujlbBeanFactory.getBean(DefaultbiWxsphSpxgProcess.class);
        bean.dealGoods();
        bean.dealShop();
        System.out.println("处理商品效果数据完成");
    }

    @Test
    public void splb() {
        DefaultbiWxsphSplbProcess bean = abujlbBeanFactory.getBean(DefaultbiWxsphSplbProcess.class);
        bean.dealGoods();
        System.out.println("处理商品列表数据完成");
    }


    @Test
    public void yhq() {
        DefaultbiWxsphYhqProcess bean = abujlbBeanFactory.getBean(DefaultbiWxsphYhqProcess.class);
        bean.dealShop();
        System.out.println("处理优惠券数据完成");
    }

    @Test
    public void xsqg() {
        DefaultbiWxsphXsqgProcess bean = abujlbBeanFactory.getBean(DefaultbiWxsphXsqgProcess.class);
        bean.dealShop();
        System.out.println("处理流限时抢购数据完成");
    }


    @Test
    public void cxztzbj() {
        DefaultbiWxsphCxztZbjProcess bean = abujlbBeanFactory.getBean(DefaultbiWxsphCxztZbjProcess.class);
        bean.dealShop();
        System.out.println("处理超雄智投直播间数据完成");

//        DefaultbiWxsphCxztDspProcess bean1 = abujlbBeanFactory.getBean(DefaultbiWxsphCxztDspProcess.class);
//        bean1.dealShop();
//        bean1.dealGoods();
//        System.out.println("处理超雄智投短视频数据完成");
    }

    @Test
    public void cxztdsp() {
        DefaultbiWxsphCxztDspProcess bean = abujlbBeanFactory.getBean(DefaultbiWxsphCxztDspProcess.class);
        bean.dealShop();
        bean.dealGoods();
        System.out.println("处理超雄智投短视频数据完成");
    }

    @Test
    public void runLocal() {
        BiThreadLocals.getMsgBean().setType(1);
        BiWxsphDataDealService biWxsphDataDealService = abujlbBeanFactory.getBean(BiWxsphDataDealService.class);
        biWxsphDataDealService.process();
    }

    @Test
    public void sendMq() {
        SendResult sendResult = aliyunMq2.send("dataprobiv2_jst", dataprobiWxsphMsg.getUuid(), new JobMessage("dataprobi_v2_wxsph_processor", dataprobiWxsphMsg));
        System.out.println("微信视频号：" + dataprobiWxsphMsg.getDpmc() + "消息发送成功，消息msgId：" + sendResult.getMessageId());
    }
}