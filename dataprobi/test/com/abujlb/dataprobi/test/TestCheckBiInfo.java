package com.abujlb.dataprobi.test;

import com.abujlb.BaseTest;
import com.abujlb.CommonConfig;
import com.abujlb.Result;
import com.abujlb.dataprobi.util.DataprobiDateUtil;
import com.abujlb.mq.AliyunMq2;
import com.abujlb.service.ServiceClient;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

public class TestCheckBiInfo extends BaseTest {


    private static final ServiceClient serviceClient;
    private static final String serviceUrl;

    static {
        serviceClient = new ServiceClient(CommonConfig.getString("jyrbAppid"), CommonConfig.getString("jyrbAppkey"));
        serviceUrl = CommonConfig.getString("jyrbServiceUrl");
    }


    @Autowired
    private AliyunMq2 aliyunMq2;

    @Test
    public void test() {
        String s = serviceClient.exec(serviceUrl + "/bi2/getAllBiInfos", new Object());
        if (Result.isTrue(s)) {
            JSONObject jsonObject = JSONObject.parseObject(s);
            JSONArray data = jsonObject.getJSONArray("data");
            check2(data);
//            check3(data);
            System.out.println("-------------------------------------");
            System.out.println("-------------------------------------");
            System.out.println("-------------------------------------");
        }
    }

    private void check2(JSONArray jsonArray) {
        Iterator<Object> iterator = jsonArray.stream().iterator();
        Map<String, Integer> map = new HashMap<>();
        while (iterator.hasNext()) {
            JSONObject biInfo = (JSONObject) iterator.next();
            if (biInfo.getString("qd").equals("TM") || biInfo.getString("qd").equals("TB")) {
                String rqjson = biInfo.getString("rqjson");
                if (StringUtils.isBlank(rqjson)) {
                    continue;
                }
                JSONObject jsonObject = JSONObject.parseObject(rqjson);
                String sycm_spcecial_rea = jsonObject.getString("videoItemRela");
                if (sycm_spcecial_rea != null) {
                    System.out.println(biInfo.getString("dpmc") + "-->" + sycm_spcecial_rea);
                    map.put(sycm_spcecial_rea, map.getOrDefault(sycm_spcecial_rea, 0) + 1);
                } else {
                    map.put("null", map.getOrDefault("null", 0) + 1);
                }
            }
        }
        System.out.println(map);
    }


    private void check3(JSONArray jsonArray) {
        Iterator<Object> iterator = jsonArray.stream().iterator();
        Map<String, Integer> map = new HashMap<>();

        List<String> rqList = DataprobiDateUtil.getBetweenDate("2025-03-01", "2025-03-25");
        while (iterator.hasNext()) {
            JSONObject biInfo = (JSONObject) iterator.next();
            if (biInfo.getString("qd").equals("TGC")) {
                String rqjson = biInfo.getString("rqjson");
                if (StringUtils.isBlank(rqjson)) {
                    continue;
                }
                JSONObject jsonObject = JSONObject.parseObject(rqjson);

                String refund = jsonObject.getString("refund");
                System.out.println(biInfo.getString("dpmc")+"-->refund = " + refund);
                if (StringUtils.isNotBlank(refund)) {
                    map.put(refund,map.getOrDefault(refund, 0) + 1);
                }
            }
        }
        System.out.println(map);
    }
}
