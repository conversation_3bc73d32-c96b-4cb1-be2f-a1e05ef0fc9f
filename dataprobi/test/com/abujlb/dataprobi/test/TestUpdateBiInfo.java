package com.abujlb.dataprobi.test;

import com.abujlb.BaseTest;
import com.abujlb.CommonConfig;
import com.abujlb.dataprobi.bean.BiInfo;
import com.abujlb.dataprobi.bean.dy.jlqc.BiJlqcAvvid;
import com.abujlb.dataprobi.server.DataUploader;
import com.abujlb.service.ServiceClient;
import com.abujlb.util.StringUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Test;

import java.util.List;
import java.util.stream.Collectors;

public class TestUpdateBiInfo extends BaseTest {

    private static final ServiceClient serviceClient;
    private static final String serviceUrl;

    static {
        serviceClient = new ServiceClient(CommonConfig.getString("jyrbAppid"), CommonConfig.getString("jyrbAppkey"));
        serviceUrl = CommonConfig.getString("jyrbServiceUrl");
    }

    @Test
    public void test() throws Exception {
          //updateBiinfo126TX();//TM TB 百亿补贴一本账  小额打款、保证金、聚合账户结算明细 月账单日期
//        updateBiinfo126TGC(); //TGC 保证金、聚合账户结算明细 月账单采集
          //updateBiinfo126PDD();//PDD  月账单采集
//        updateBiinfo126JD();//JD  账单、资金流水明细、台账直赔、小额打款 月账单采集
//        updateBiinfo126DY();//DY 账单、保证金 月账单采集
          updateBiinfo127PDD();//pdd

    }


    private void updateBiinfo126TX() throws Exception {
        List<BiInfo> allBiInfo = DataUploader.getAllBiInfo();
        String rq = "2025-04-30";
        ServiceClient client = new ServiceClient(CommonConfig.getString("jyrbAppid"), CommonConfig.getString("jyrbAppkey"));
        for (BiInfo biInfo : allBiInfo) {
            if (biInfo.getQd().equals("TM") || biInfo.getQd().equals("TB")) {
                if(biInfo.getYhid()!=2441){
                    continue;
                }
                String rqjson = biInfo.getRqjson();
                if (StringUtil.isJson2(rqjson)) {
                    JSONObject jsonObject2 = JSONObject.parseObject(rqjson);
                    jsonObject2.put("videoItemRela", rq);
                    biInfo.setAllownull(1);
                    biInfo.setRqjson(jsonObject2.toJSONString());
                    client.exec(CommonConfig.getString("service2Url") + "/bi2/updateBiInfoById", biInfo);
                }
            }
        }
    }

    private void updateBiinfo126TGC() throws Exception {
        List<BiInfo> allBiInfo = DataUploader.getAllBiInfo();
        String rq = "2025-03-31";
        ServiceClient client = new ServiceClient(CommonConfig.getString("jyrbAppid"), CommonConfig.getString("jyrbAppkey"));
        for (BiInfo biInfo : allBiInfo) {
            if (biInfo.getQd().equals("TGC")) {
                String rqjson = biInfo.getRqjson();
                if (StringUtil.isJson2(rqjson)) {
                    JSONObject jsonObject2 = JSONObject.parseObject(rqjson);
                    jsonObject2.put("month_union_pay_bill", rq);
                    jsonObject2.put("month_bzj", rq);
                    biInfo.setAllownull(1);
                    biInfo.setRqjson(jsonObject2.toJSONString());
                    client.exec(CommonConfig.getString("service2Url") + "/bi2/updateBiInfoById", biInfo);
                }
            }
        }
    }

    private void updateBiinfo126PDD() throws Exception {
        List<BiInfo> allBiInfo = DataUploader.getAllBiInfo();
        String rq = "2025-03-31";
        ServiceClient client = new ServiceClient(CommonConfig.getString("jyrbAppid"), CommonConfig.getString("jyrbAppkey"));
        for (BiInfo biInfo : allBiInfo) {
            if (biInfo.getQd().equals("PDD")) {
                String rqjson = biInfo.getRqjson();
                if (StringUtil.isJson2(rqjson)) {
                    JSONObject jsonObject2 = JSONObject.parseObject(rqjson);
                    jsonObject2.put("jxsj", rq);
                    biInfo.setAllownull(1);
                    biInfo.setRqjson(jsonObject2.toJSONString());
                    client.exec(CommonConfig.getString("service2Url") + "/bi2/updateBiInfoById", biInfo);
                }
            }
        }
    }

    private void updateBiinfo127PDD() throws Exception {
        List<BiInfo> allBiInfo = DataUploader.getAllBiInfo();

        // 先过滤出 qd == "PDD" 的 biInfo 列表
        List<BiInfo> pddBiInfoList = allBiInfo.stream()
                .filter(biInfo -> "PDD".equals(biInfo.getQd()))
                .collect(Collectors.toList());

        String rq = "2025-03-31";
        ServiceClient client = new ServiceClient(
                CommonConfig.getString("jyrbAppid"),
                CommonConfig.getString("jyrbAppkey")
        );

        for (BiInfo biInfo : pddBiInfoList) {
            String rqjson = biInfo.getRqjson();
            if (StringUtil.isJson2(rqjson)) {
                JSONObject jsonObject2 = JSONObject.parseObject(rqjson);
                jsonObject2.put("jxsj", rq);
                biInfo.setAllownull(1);
                biInfo.setRqjson(jsonObject2.toJSONString());
                client.exec(CommonConfig.getString("service2Url") + "/bi2/updateBiInfoById", biInfo);
            }
        }
    }


    private void updateBiinfo126JD() throws Exception {
        List<BiInfo> allBiInfo = DataUploader.getAllBiInfo();
        String rq = "2025-03-31";
        ServiceClient client = new ServiceClient(CommonConfig.getString("jyrbAppid"), CommonConfig.getString("jyrbAppkey"));
        for (BiInfo biInfo : allBiInfo) {
            if (biInfo.getQd().equals("JD")) {
                String rqjson = biInfo.getRqjson();
                if (StringUtil.isJson2(rqjson)) {
                    JSONObject jsonObject2 = JSONObject.parseObject(rqjson);
                    jsonObject2.put("month_bill", rq);
                    jsonObject2.put("month_zjlszd", rq);
                    jsonObject2.put("month_xedk", rq);
                    jsonObject2.put("month_tzzp", rq);
                    biInfo.setAllownull(1);
                    biInfo.setRqjson(jsonObject2.toJSONString());
                    client.exec(CommonConfig.getString("service2Url") + "/bi2/updateBiInfoById", biInfo);
                }
            }
        }
    }

    private void updateBiinfo126DY() throws Exception {
        List<BiInfo> allBiInfo = DataUploader.getAllBiInfo();
        String rq = "2025-03-31";
        ServiceClient client = new ServiceClient(CommonConfig.getString("jyrbAppid"), CommonConfig.getString("jyrbAppkey"));
        for (BiInfo biInfo : allBiInfo) {
            if (biInfo.getQd().equals("DY")) {
                String rqjson = biInfo.getRqjson();
                if (StringUtil.isJson2(rqjson)) {
                    JSONObject jsonObject2 = JSONObject.parseObject(rqjson);
                    jsonObject2.put("month_bill", rq);
                    jsonObject2.put("month_dybzj", rq);
                    biInfo.setAllownull(1);
                    biInfo.setRqjson(jsonObject2.toJSONString());
                    client.exec(CommonConfig.getString("service2Url") + "/bi2/updateBiInfoById", biInfo);
                }
            }
        }
    }

    private void updateBiinfo125() {
        String rq = "2025-02-28";
        String rq2 = "2025-03-31";
        List<BiInfo> allBiInfo = DataUploader.getAllBiInfo();
        ServiceClient client = new ServiceClient(CommonConfig.getString("jyrbAppid"), CommonConfig.getString("jyrbAppkey"));
        for (BiInfo biInfo : allBiInfo) {
            if (biInfo.getQd().equals("DY")) {
                String rqjson = biInfo.getRqjson();
                if (!StringUtil.isJson2(rqjson)) {
                    continue;
                }
                JSONObject jsonObject2 = JSONObject.parseObject(rqjson);
                if (biInfo.getYhid() == 4921) {
                    jsonObject2.put("cwjldp2", rq);
                } else {
                    jsonObject2.put("cwjldp2", rq2);
                }
                biInfo.setAllownull(1);
                biInfo.setRqjson(jsonObject2.toJSONString());
                client.exec(CommonConfig.getString("service2Url") + "/bi2/updateBiInfoById", biInfo);


                String userid = biInfo.getUserid();
                List<BiJlqcAvvid> jlqcAvvids = DataUploader.getJlqcAvvids(userid);
                if (CollectionUtils.isEmpty(jlqcAvvids)) {
                    continue;
                }

                for (BiJlqcAvvid jlqcAvvid : jlqcAvvids) {
                    String jlqc_rqjson = jlqcAvvid.getRqjson();
                    if (!StringUtil.isJson2(jlqc_rqjson)) {
                        continue;
                    }
                    JSONObject jlqcRqObj = JSONObject.parseObject(jlqc_rqjson);
                    if (biInfo.getYhid() == 4921) {
                        jlqcRqObj.put("cwjl2", rq);
                    } else {
                        jlqcRqObj.put("cwjl2", rq2);
                    }
                    jlqcAvvid.setRqjson(new Gson().toJson(jlqcRqObj));
                    serviceClient.exec(serviceUrl + "/bijlqc/updateBiJlqcAvvidCjrq", jlqcAvvid);
                }
            }
        }
    }
}
