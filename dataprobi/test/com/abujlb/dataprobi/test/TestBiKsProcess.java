package com.abujlb.dataprobi.test;

import com.abujlb.BaseTest;
import com.abujlb.dataprobi.bean.BiInfo;
import com.abujlb.dataprobi.bean.ks.DataprobiKsMsg;
import com.abujlb.dataprobi.processes.ks.DefaultBiKsSplbDealProcess;
import com.abujlb.dataprobi.processes.ks.DefaultBiKsTgxgDealProcess;
import com.abujlb.dataprobi.processes.ks.DefaultbiKsSpxgDealProcess;
import com.abujlb.dataprobi.server.DataUploader;
import com.abujlb.dataprobi.service.BiKsDataDealService;
import com.abujlb.dataprobi.threads.BiThreadLocals;
import com.abujlb.mq.AliyunMq2;
import com.abujlb.mq.JobMessage;
import com.aliyun.openservices.ons.api.SendResult;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.lang.reflect.InvocationTargetException;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/31 9:15
 */
public class TestBiKsProcess extends BaseTest {


    private final DataprobiKsMsg dataprobiKsMsg;
    @Autowired
    private AliyunMq2 aliyunMq2;
    @Autowired
    private DefaultBiKsSplbDealProcess defaultBiKsSplbDealProcess;
    @Autowired
    private DefaultbiKsSpxgDealProcess defaultbiKsSpxgDealProcess;
    @Autowired
    private DefaultBiKsTgxgDealProcess defaultBiKsTgxgDealProcess;
    @Autowired
    private BiKsDataDealService biKsDataDealService;


    {
        final int biinfoId = 8499;
        BiInfo biInfo = DataUploader.getBiinfoById(biinfoId);
        if (biInfo == null) {
            throw new RuntimeException("获取Biinfo失败，Biinfo的ID为：" + biinfoId);
        }
        dataprobiKsMsg = new DataprobiKsMsg();
        dataprobiKsMsg.setUserid(biInfo.getUserid());
        dataprobiKsMsg.setYhid(biInfo.getYhid());
        dataprobiKsMsg.setQd(biInfo.getQd());
        dataprobiKsMsg.setDpmc(biInfo.getDpmc());
        dataprobiKsMsg.setBiinfoId(biInfo.getId());
        dataprobiKsMsg.setType(1);
        dataprobiKsMsg.setSplb(0);
        dataprobiKsMsg.setDpMonths(Collections.emptyList());

        List<String> rqList = Collections.singletonList("2024-11-28");
        List<String> rqList2 = Collections.singletonList("2024-11-27");

        dataprobiKsMsg.setSpxg(rqList);
        dataprobiKsMsg.setSpxgdp(rqList);
        dataprobiKsMsg.setTgxg(rqList2);

        try {
            dataprobiKsMsg.fillNullList();
        } catch (InvocationTargetException e) {
            throw new RuntimeException(e);
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        }
        BiThreadLocals.init(dataprobiKsMsg);
    }


    @Test
    public void splb() {
        defaultBiKsSplbDealProcess.dealGoods();
    }

    @Test
    public void tgxg() {
        defaultBiKsTgxgDealProcess.dealGoods();
    }
    @Test
    public void spxg() {
        defaultbiKsSpxgDealProcess.dealGoods();
        defaultbiKsSpxgDealProcess.dealShop();
    }

    @Test
    public void sendMq() {
        SendResult sendResult = aliyunMq2.send("dataprobiv2_jst", dataprobiKsMsg.getUuid(), new JobMessage("dataprobi_v2_ks_processor", dataprobiKsMsg));
        System.out.println("ks：" + dataprobiKsMsg.getDpmc() + "消息发送成功，消息msgId：" + sendResult.getMessageId());
    }

    @Test
    public void run(){
        biKsDataDealService.process();
    }

}
