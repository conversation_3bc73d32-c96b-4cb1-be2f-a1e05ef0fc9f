package com.abujlb.dataprobi.test;

import com.abujlb.AbujlbBeanFactory;
import com.abujlb.BaseTest;
import com.abujlb.dataprobi.bean.BiInfo;
import com.abujlb.dataprobi.bean.dw.DataprobiDwMsg;
import com.abujlb.dataprobi.bean.ymx.DataprobiYmxMsg;
import com.abujlb.dataprobi.constant.DataprobiConst;
import com.abujlb.dataprobi.oss.BiDataOssUtil;
import com.abujlb.dataprobi.processes.dw.*;
import com.abujlb.dataprobi.processes.ymx.DefaultBiYmxSptgProcess;
import com.abujlb.dataprobi.server.DataUploader;
import com.abujlb.dataprobi.service.BiDwDataDealService;
import com.abujlb.dataprobi.threads.BiThreadLocals;
import com.abujlb.dataprobi.util.DataprobiDateUtil;
import com.abujlb.mq.AliyunMq2;
import com.abujlb.mq.JobMessage;
import com.aliyun.openservices.ons.api.SendResult;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/7
 */
public class TestBiYmxProcess extends BaseTest {
    private final DataprobiYmxMsg dataprobiYmxMsg;
    @Autowired
    private AbujlbBeanFactory abujlbBeanFactory;
    @Autowired
    private AliyunMq2 aliyunMq2;
    @Autowired
    private BiDataOssUtil biDataOssUtil;


    {
//        final int biinfoId = 16619;
//        BiInfo biInfo = DataUploader.getBiinfoById(biinfoId);
//        if (biInfo == null) {
//            throw new RuntimeException("获取Biinfo失败，Biinfo的ID为：" + biinfoId);
//        }
        dataprobiYmxMsg = new DataprobiYmxMsg();
        dataprobiYmxMsg.setUserid("AQVKH63AJQP4Z");
        dataprobiYmxMsg.setYhid(125487);
        dataprobiYmxMsg.setQd("YMX");
        dataprobiYmxMsg.setDpmc("");
        dataprobiYmxMsg.setBiinfoId(111111111);
        dataprobiYmxMsg.setType(0);
        dataprobiYmxMsg.setSplb(0);
        dataprobiYmxMsg.setSptg(Lists.newArrayList("2025-06-27"));
        BiThreadLocals.init(dataprobiYmxMsg);
    }


    @Test
    public void sptg() {
        DefaultBiYmxSptgProcess bean = abujlbBeanFactory.getBean(DefaultBiYmxSptgProcess.class);
        bean.dealGoods();
        System.out.println("处理商品推广数据完成");
    }


    /**
     * @param : return void
     * <AUTHOR>
     * @date 2024/10/18
     * @description 本地跑
     */
    @Test
    public void runLocal() {
        BiThreadLocals.getMsgBean().setType(1);
        BiDwDataDealService dwDataDealService = abujlbBeanFactory.getBean(BiDwDataDealService.class);
        dwDataDealService.process();
    }

    /**
     * @param : return void
     * <AUTHOR>
     * @date 2024/10/18
     * @description 发送测试消息服务器跑
     */
    @Test
    public void sendMqtest() {
        SendResult sendResult = aliyunMq2.send("dataprobiv2_test", dataprobiYmxMsg.getUuid(), new JobMessage("dataprobi_v2_ymx_processor", dataprobiYmxMsg));
        System.out.println("亚马逊：" + dataprobiYmxMsg.getDpmc() + "消息发送成功，消息msgId：" + sendResult.getMessageId());
    }

    /**
     * @param : return void
     * <AUTHOR>
     * @date 2024/10/18
     * @description 发送消息服务器跑
     */
    @Test
    public void sendMq() {
        SendResult sendResult = aliyunMq2.send("dataprobiv2_jst", dataprobiYmxMsg.getUuid(), new JobMessage("dataprobi_v2_ymx_processor", dataprobiYmxMsg));
        System.out.println("亚马逊：" + dataprobiYmxMsg.getDpmc() + "消息发送成功，消息msgId：" + sendResult.getMessageId());
    }

}