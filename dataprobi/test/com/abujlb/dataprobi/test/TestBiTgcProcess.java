package com.abujlb.dataprobi.test;

import com.abujlb.AbujlbBeanFactory;
import com.abujlb.BaseTest;
import com.abujlb.dataprobi.bean.BiInfo;
import com.abujlb.dataprobi.bean.tgc.BiTgcAiztone;
import com.abujlb.dataprobi.bean.tgc.DataprobiTgcMsg;
import com.abujlb.dataprobi.processes.tgc.*;
import com.abujlb.dataprobi.server.DataUploader;
import com.abujlb.dataprobi.service.BiTgcDataDealService;
import com.abujlb.dataprobi.threads.BiThreadLocals;
import com.abujlb.mq.AliyunMq2;
import com.abujlb.mq.JobMessage;
import com.aliyun.openservices.ons.api.SendResult;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2024/5/7
 */
public class TestBiTgcProcess extends BaseTest {


    private final DataprobiTgcMsg dataprobiTgcMsg;
    @Autowired
    private AbujlbBeanFactory abujlbBeanFactory;
    @Autowired
    private AliyunMq2 aliyunMq2;

    {
        final int biinfoId = 15358;
        BiInfo biInfo = DataUploader.getBiinfoById(biinfoId);
        if (biInfo == null) {
            throw new RuntimeException("获取Biinfo失败，Biinfo的ID为：" + biinfoId);
        }
        dataprobiTgcMsg = new DataprobiTgcMsg();
        dataprobiTgcMsg.setUserid(biInfo.getUserid());
        dataprobiTgcMsg.setYhid(biInfo.getYhid());
        dataprobiTgcMsg.setQd(biInfo.getQd());
        dataprobiTgcMsg.setDpmc(biInfo.getDpmc());
        dataprobiTgcMsg.setBiinfoId(biInfo.getId());
        dataprobiTgcMsg.setType(1);
        dataprobiTgcMsg.setSplb(0);

//        List<String> rqList = Collections.singletonList("2024-10-06");
//        List<String> rqList =Collections.singletonList("2024-11-26");
        List<String> rqList = Collections.singletonList("2025-04-23");
//        List<String> rqList = DataprobiDateUtil.getBetweenDate("2024-10-06", "2024-10-08");
//        List<String> rqList2 = DataprobiDateUtil.getBetweenDate("2024-05-01", "2024-06-12");
        List<String> rqList2 = Collections.singletonList("2025-05-18");

        dataprobiTgcMsg.setSpxg(rqList2);
        dataprobiTgcMsg.setSpxgdp(rqList2);

        dataprobiTgcMsg.setDmbzt(rqList);
        dataprobiTgcMsg.setAizt(rqList);
        dataprobiTgcMsg.setYlmf(rqList);
        dataprobiTgcMsg.setZtc(rqList);

        dataprobiTgcMsg.setSpyytg(rqList2);
        dataprobiTgcMsg.setSptg(rqList2);
        dataprobiTgcMsg.setCpgl(rqList);
        dataprobiTgcMsg.setTktg(rqList2);
        BiThreadLocals.init(dataprobiTgcMsg);

        List<BiTgcAiztone> list = DataUploader.getTgcAiztoneList(BiThreadLocals.getMsgBean().getUserid());
        if (CollectionUtils.isNotEmpty(list)) {
            Map<String, BiTgcAiztone> map = new HashMap<>();
            for (BiTgcAiztone biTgcAiztone : list) {
                if (!map.containsKey(biTgcAiztone.getAiztoneid())) {
                    map.put(biTgcAiztone.getAiztoneid(), biTgcAiztone);
                }
            }
            BiThreadLocals.addTgcAiztoneList(new ArrayList<>(map.values()));
        }
    }

    @Test
    public void spxg() {
        DefaultbiTgcSpxgProcess bean = abujlbBeanFactory.getBean(DefaultbiTgcSpxgProcess.class);
//        bean.dealGoods();
//        bean.dealShop();
        bean.compareShop();
        System.out.println("处理商品效果数据完成");
    }

    @Test
    public void splb() {
        DefaultbiTgcSplbProcess bean = abujlbBeanFactory.getBean(DefaultbiTgcSplbProcess.class);
        bean.dealGoods();
    }

    @Test
    public void ztc() {
        DefaultbiTgcZtcProcess defaultbiTgcZtcProcess = abujlbBeanFactory.getBean(DefaultbiTgcZtcProcess.class);
        defaultbiTgcZtcProcess.dealGoods();
    }

    @Test
    public void ylmf() {
        DefaultbiTgcYlmfProcess defaultbiTgcYlmfProcess = abujlbBeanFactory.getBean(DefaultbiTgcYlmfProcess.class);
        defaultbiTgcYlmfProcess.dealGoods();
    }

    @Test
    public void aizt() {
        DefaultbiTgcAiztProcess defaultbiTgcAiztProcess = abujlbBeanFactory.getBean(DefaultbiTgcAiztProcess.class);
        defaultbiTgcAiztProcess.dealGoods();
    }

    @Test
    public void spyytg() {
        DefaultbiTgcSpyytgProcess defaultbiTgcSpyytgProcess = abujlbBeanFactory.getBean(DefaultbiTgcSpyytgProcess.class);
        defaultbiTgcSpyytgProcess.dealGoods();
    }

    @Test
    public void sptg() {
        DefaultbiTgcSptgProcess defaultbiTgcSptgProcess = abujlbBeanFactory.getBean(DefaultbiTgcSptgProcess.class);
        defaultbiTgcSptgProcess.dealGoods();
//        defaultbiTgcSptgProcess.compareGoods();
    }

    @Test
    public void tktg() {
        DefaultbiTgcTktgProcess defaultbiTgcTktgProcess = abujlbBeanFactory.getBean(DefaultbiTgcTktgProcess.class);
        defaultbiTgcTktgProcess.dealGoods();
    }

    @Test
    public void runLocal() {
        BiThreadLocals.getMsgBean().setType(1);
        BiTgcDataDealService biTgcDataDealService = abujlbBeanFactory.getBean(BiTgcDataDealService.class);
        biTgcDataDealService.process();
    }

    @Test
    public void sendMq() {
        SendResult sendResult = aliyunMq2.send("dataprobiv2_jst", dataprobiTgcMsg.getUuid(), new JobMessage("dataprobi_v2_tgc_processor", dataprobiTgcMsg));
        System.out.println("淘工厂：" + dataprobiTgcMsg.getDpmc() + "消息发送成功，消息msgId：" + sendResult.getMessageId());
    }
}
