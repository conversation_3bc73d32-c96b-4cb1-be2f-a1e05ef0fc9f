package com.abujlb.dataprobi.test;

import com.abujlb.BaseTest;
import com.abujlb.dataprobi.bean.BiInfo;
import com.abujlb.dataprobi.bean.albb.Dataprobi1688Msg;
import com.abujlb.dataprobi.processes.albb.*;
import com.abujlb.dataprobi.server.DataUploader;
import com.abujlb.dataprobi.service.BiAlbbDataDealService;
import com.abujlb.dataprobi.threads.BiThreadLocals;
import com.abujlb.mq.AliyunMq2;
import com.abujlb.mq.JobMessage;
import com.aliyun.openservices.ons.api.SendResult;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.lang.reflect.InvocationTargetException;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/31 9:15
 */
public class TestBiAlbbProcess extends BaseTest {


    private final Dataprobi1688Msg dataprobi1688Msg;
    @Autowired
    private AliyunMq2 aliyunMq2;
    //    @Autowired
//    private DefaultBiAlbbJjfaDealProcess defaultBiAlbbJjfaDealProcess;
//    @Autowired
//    private DefaultBiAlbbPpyxDealProcess defaultBiAlbbPpyxDealProcess;
    @Autowired
    private DefaultBiAlbbSpxgDealProcess defaultBiAlbbSpxgDealProcess;

    @Autowired
    private DefaultBiAlbbDwtgDealProcess defaultBiAlbbDwtgDealProcess;
    @Autowired
    private DefaultBiAlbbSszbDealProcess defaultBiAlbbSszbDealProcess;
    @Autowired
    private DefaultBiAlbbPpzqDealProcess defaultBiAlbbPpzqDealProcess;
    @Autowired
    private DefaultBiAlbbQztdDealProcess defaultBiAlbbQztdDealProcess;
    @Autowired
    private DefaultBiAlbbCjtfDealProcess defaultBiAlbbCjtfDealProcess;
    @Autowired
    private DefaultBiAlbbSkazsdzfaDealProcess defaultBiAlbbSkazsdzfaDealProcess;
    @Autowired
    private DefaultBiAlbbSkajscDealProcess defaultBiAlbbSkajscDealProcess;
    @Autowired
    private DefaultBiAlbbGcsyjsjhDealProcess defaultBiAlbbGcsyjsjhDealProcess;
    @Autowired
    private DefaultBiAlbbYxhbDealProcess defaultBiAlbbYxhbDealProcess;

    @Autowired
    private DefaultBiAlbbHxsjczjhDealProcess defaultBiAlbbHxsjczjhDealProcess;
    @Autowired
    private DefaultBiAlbbXfpjjfaDealProcess defaultBiAlbbXfpjjfaDealProcess;

    @Autowired
    private BiAlbbDataDealService biAlbbDataDealService;


    {
        final int biinfoId = 5604;
        BiInfo biInfo = DataUploader.getBiinfoById(biinfoId);
        if (biInfo == null) {
            throw new RuntimeException("获取Biinfo失败，Biinfo的ID为：" + biinfoId);
        }
        dataprobi1688Msg = new Dataprobi1688Msg();
        dataprobi1688Msg.setUserid(biInfo.getUserid());
        dataprobi1688Msg.setYhid(biInfo.getYhid());
        dataprobi1688Msg.setQd(biInfo.getQd());
        dataprobi1688Msg.setDpmc(biInfo.getDpmc());
        dataprobi1688Msg.setBiinfoId(biInfo.getId());
        dataprobi1688Msg.setType(1);
        dataprobi1688Msg.setSplb(0);
        dataprobi1688Msg.setDpMonths(Collections.emptyList());
//        List<String> rqList = DataprobiDateUtil.getBetweenDate("2024-04-01", "2024-04-25");
//        List<String> rqList = Collections.emptyList();
//        List<String> rqList = Arrays.asList("2024-10-01", "2024-10-02");
//        List<String> rqList2 = Collections.singletonList("2024-04-10");

        List<String> rqList = Collections.singletonList("2025-04-05");
//        List<String> rqList2 = Collections.singletonList("2024-05-01");
//        List<String> rqList2 = Collections.emptyList();
        dataprobi1688Msg.setSpxg(rqList);
        dataprobi1688Msg.setSpxgdp(rqList);
        dataprobi1688Msg.setSkazsdzfa(rqList);
        dataprobi1688Msg.setSkazsdzfadp(rqList);
        dataprobi1688Msg.setQztd(rqList);
        dataprobi1688Msg.setQztddp(rqList);
        dataprobi1688Msg.setSkajsc(rqList);
        dataprobi1688Msg.setSkajscdp(rqList);
        dataprobi1688Msg.setGcsyjsjh(rqList);
        dataprobi1688Msg.setGcsyjsjhdp(rqList);
        dataprobi1688Msg.setHxsjczjh(rqList);
        dataprobi1688Msg.setHxsjczjhdp(rqList);
        dataprobi1688Msg.setXfpjjfa(rqList);
        dataprobi1688Msg.setXfpjjfadp(rqList);
        dataprobi1688Msg.setGypjjfa(rqList);
        dataprobi1688Msg.setGypjjfadp(rqList);
        dataprobi1688Msg.setDwtgdp(rqList);
        dataprobi1688Msg.setMxgcdp(rqList);
        dataprobi1688Msg.setPpzqdp(rqList);
        dataprobi1688Msg.setQdyx(rqList);
        dataprobi1688Msg.setQdyxdp(rqList);
        dataprobi1688Msg.setQzxp(rqList);
        dataprobi1688Msg.setQzxpdp(rqList);
        dataprobi1688Msg.setSszbdp(rqList);
        dataprobi1688Msg.setSwzsdp(rqList);
        dataprobi1688Msg.setZgcjjfa(rqList);
        dataprobi1688Msg.setZgcjjfadp(rqList);

//        dataprobi1688Msg.setJjfa(rqList2);
//        dataprobi1688Msg.setJjfadp(rqList2);
//        dataprobi1688Msg.setPpyxdp(rqList2);
//        dataprobi1688Msg.setSwzsprodp(rqList2);
//        dataprobi1688Msg.setZntf(rqList2);
//        dataprobi1688Msg.setZntfdp(rqList2);
//        dataprobi1688Msg.setZztf(rqList2);
//        dataprobi1688Msg.setZztfdp(rqList2);
//        dataprobi1688Msg.setZxb(rqList2);
//        dataprobi1688Msg.setZxbdp(rqList2);
        dataprobi1688Msg.setYxhb(rqList);

//        dataprobi1688Msg.setJjfatgfx(rqList2);
//        dataprobi1688Msg.setZntftgfx(rqList2);
//        dataprobi1688Msg.setZztftgfx(rqList2);
//        dataprobi1688Msg.setZxbtgfx(rqList2);

//        dataprobi1688Msg.setDwtgdp(Collections.singletonList("2024-06-03"));
//
//        dataprobi1688Msg.setSszbdp(Collections.singletonList("2024-06-03"));
//
//        dataprobi1688Msg.setPpzqdp(Collections.singletonList("2024-05-09"));
//        dataprobi1688Msg.setQztf(rqList);
//        dataprobi1688Msg.setQztfdp(rqList);
        dataprobi1688Msg.setCjtf(rqList);
        dataprobi1688Msg.setCjtfdp(rqList);
        //dataprobi1688Msg.setDzyxjjfa(1);
        try {
            dataprobi1688Msg.fillNullList();
        } catch (InvocationTargetException e) {
            throw new RuntimeException(e);
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        }
        BiThreadLocals.init(dataprobi1688Msg);
    }

//    @Test
//    public void jjaf() {
//        defaultBiAlbbJjfaDealProcess.dealGoods();
//        defaultBiAlbbJjfaDealProcess.dealShop();
//    }
//
//    @Test
//    public void ppyx() {
//        defaultBiAlbbPpyxDealProcess.dealShop();
//    }

    @Test
    public void spxg() {
        defaultBiAlbbSpxgDealProcess.dealGoods();
        defaultBiAlbbSpxgDealProcess.dealShop();
    }
//
//    @Test
//    public void swzspro() {
//        defaultBiAlbbSwzsproDealProcess.dealShop();
//    }
//
//    @Test
//    public void zntf() {
//        defaultBiAlbbZntfDealProcess.dealGoods();
//        defaultBiAlbbZntfDealProcess.dealShop();
//    }
//
//    @Test
//    public void zxb() {
//        defaultBiAlbbZxbDealProcess.dealGoods();
//        defaultBiAlbbZxbDealProcess.dealShop();
//    }
//
//    @Test
//    public void zztf() {
//        defaultBiAlbbZztfDealProcess.dealGoods();
//        defaultBiAlbbZztfDealProcess.dealShop();
//    }
//
    @Test
    public void yxhb() {
        defaultBiAlbbYxhbDealProcess.dealShop();
    }


    @Test//测试通过
    public void dwtg() {
        defaultBiAlbbDwtgDealProcess.dealShop();
    }
    @Test
    public void sszb() {
        defaultBiAlbbSszbDealProcess.dealShop();
    }
    @Test
    public void ppzq() {
        defaultBiAlbbPpzqDealProcess.dealShop();
    }
    @Test
    public void qztd() {
        defaultBiAlbbQztdDealProcess.dealShop();
        defaultBiAlbbQztdDealProcess.dealGoods();
    }

    @Test
    public void cjtf() {
        defaultBiAlbbCjtfDealProcess.dealShop();
        defaultBiAlbbCjtfDealProcess.dealGoods();
    }

    @Test
    public void skazsdzfa() {
        defaultBiAlbbSkazsdzfaDealProcess.dealShop();
        defaultBiAlbbSkazsdzfaDealProcess.dealGoods();
    }
    @Test
    public void skajsc() {
        defaultBiAlbbSkajscDealProcess.dealShop();
        defaultBiAlbbSkajscDealProcess.dealGoods();
    }
    @Test
    public void gcsyjsjh() {
        defaultBiAlbbGcsyjsjhDealProcess.dealShop();
        defaultBiAlbbGcsyjsjhDealProcess.dealGoods();
    }

    @Test
    public void hxsjczjh() {
        defaultBiAlbbHxsjczjhDealProcess.dealShop();
        defaultBiAlbbHxsjczjhDealProcess.dealGoods();
    }

    @Test
    public void xfpjjfa() {
        defaultBiAlbbXfpjjfaDealProcess.dealShop();
        defaultBiAlbbXfpjjfaDealProcess.dealGoods();
    }

    @Test
    public void runLocal() {
        BiThreadLocals.getMsgBean().setType(1);
        biAlbbDataDealService.process();
    }

    @Test
    public void sendMq() {
        SendResult sendResult = aliyunMq2.send("dataprobiv2_jst", dataprobi1688Msg.getUuid(), new JobMessage("dataprobi_v2_albb_processor", dataprobi1688Msg));
        System.out.println("1688：" + dataprobi1688Msg.getDpmc() + "消息发送成功，消息msgId：" + sendResult.getMessageId());
    }

}
