package com.abujlb.dataprobi.test;

import com.abujlb.BaseTest;
import com.abujlb.dataprobi.bean.BiInfo;
import com.abujlb.dataprobi.bean.tb.DataprobiMsg;
import com.abujlb.dataprobi.server.DataUploader;
import com.abujlb.dataprobi.service.BiDataDealService;
import com.abujlb.dataprobi.threads.BiThreadLocals;
import com.abujlb.mq.AliyunMq2;
import com.abujlb.mq.JobMessage;
import com.abujlb.util.DateUtil;
import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.SendResult;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * TestBiProcess2 发消息处理
 * 
 * <AUTHOR>
 * @date 2025/06/08 11:57
 */
public class TestBiProcess3 extends BaseTest {


    @Autowired
    private BiDataDealService biDataDealService;

    private final DataprobiMsg dataprobiMsg;
    {
        final int biinfoId = 4630;
        BiInfo biInfo = DataUploader.getBiinfoById(biinfoId);
        if (biInfo == null) {
            throw new RuntimeException("获取Biinfo失败，Biinfo的ID为：" + biinfoId);
        }
        String json = "{\"spxg\":[],\"spxgdp\":[],\"ztc\":[],\"ztc_tgfx\":[],\"ztc_crowd_tgfx\":[],\"ztc_keyword_tgfx\":[],\"ylmf\":[],\"ylmf_cxjh\":[],\"ylmf_tgfx\":[],\"dmbzt\":[],\"dmbzt_tgfx\":[],\"qztg\":[],\"qztg_tgfx\":[],\"aizt\":[],\"aiztone_dpsummary\":[],\"aizt_tgfx\":[],\"aizt_item\":[],\"aizt_item_tgfx\":[],\"aizt_shop\":[],\"aizt_shop_dpzd\":[],\"aizt_shop_qdzt\":[],\"aizt_shop_tgfx\":[],\"xstg\":[],\"xstg_tgfx\":[],\"nryxdp\":[],\"nryxdsp\":[],\"nryx_cjzb_qztg\":[],\"yhq\":[],\"aiztonejhzt\":[],\"aiztoneztcitemdjlv\":[],\"aiztcybb\":[],\"aiztunitstatus\":[],\"pxb\":[],\"udzht\":[],\"rlyq\":[],\"rlyqdp\":[],\"newitem\":[],\"spsjhz\":[],\"sycmflow\":[],\"jhs\":[],\"tbxjhb\":[],\"xedk\":[],\"ppxx\":[],\"ppxxdp\":[],\"ppxx_yxtg_xk\":[],\"ppxx_yxtg_xp\":[],\"xkfx\":[],\"fwCustomer\":[],\"tbk\":[],\"tbkdp\":[],\"sycmlevel\":[],\"ppxxYghf\":[],\"dpmhpdcqddplb\":[],\"llzhymfx\":[],\"pjyl\":[],\"qndpzhtyf\":[],\"sycmghsp\":[],\"sycmghspdp\":[],\"sycmghtw\":[],\"sycmghtwdp\":[],\"sppxi\":[],\"dptyf\":[],\"sycm_performance_core_monitor\":[],\"sycm_performance_core_monitor_dp\":[],\"sycm_user_sale_summary\":[],\"sycm_performance_shop_sale_summary\":[],\"sycm_performance_refund\":[],\"sycm_spcecial_cra\":[],\"sycm_performance_duty\":[],\"msyqJlgg\":[],\"jlgg\":[\"2025-06-30\"],\"pj\":[],\"money\":[],\"qyyxtg\":[],\"sycm_performance_inquiiry_order\":[],\"sycm_spcecial_rea\":[],\"sycmbybtybz\":[],\"videoItemRela\":[],\"aiztone\":0,\"t\":\"1751332471315\",\"uuid\":\"ca6e0735-ec0f-43b7-9ca6-d3d3e8aa2c5b\",\"userid\":\"2213754159402\",\"yhid\":20403,\"qd\":\"TM\",\"biinfoId\":4630,\"dpmc\":\"SecretWorld旗舰店\",\"splb\":0,\"type\":1,\"auto\":0}  \n";
        dataprobiMsg = JSON.parseObject(json, DataprobiMsg.class);

        try {
            dataprobiMsg.fillNullList();
        } catch (InvocationTargetException e) {
            throw new RuntimeException(e);
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        }
        BiThreadLocals.init(dataprobiMsg);
        BiThreadLocals.addBiInfo(biInfo);
    }


    @Test
    public void runLocal() {
        ((DataprobiMsg) BiThreadLocals.getMsgBean()).setAiztone(1);
        biDataDealService.process();
    }
}
