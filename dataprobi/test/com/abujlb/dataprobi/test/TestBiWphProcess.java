package com.abujlb.dataprobi.test;

import com.abujlb.AbujlbBeanFactory;
import com.abujlb.BaseTest;
import com.abujlb.dataprobi.bean.BiInfo;
import com.abujlb.dataprobi.bean.wph.DataprobiWphMsg;
import com.abujlb.dataprobi.processes.wph.*;
import com.abujlb.dataprobi.server.DataUploader;
import com.abujlb.dataprobi.service.BiWxsphDataDealService;
import com.abujlb.dataprobi.threads.BiThreadLocals;
import com.abujlb.dataprobi.tsdao.SpnewTsDao;
import com.abujlb.dataprobi.util.DataprobiDateUtil;
import com.abujlb.mq.AliyunMq2;
import com.abujlb.mq.JobMessage;
import com.aliyun.openservices.ons.api.SendResult;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/7
 */
public class TestBiWphProcess extends BaseTest {



    private final DataprobiWphMsg dataprobiWphMsg;
    @Autowired
    private AbujlbBeanFactory abujlbBeanFactory;
    @Autowired
    private AliyunMq2 aliyunMq2;
    @Autowired
    private SpnewTsDao spnewTsDao;

    {
        final int biinfoId = 16109;
        BiInfo biInfo = DataUploader.getBiinfoById(biinfoId);
        if (biInfo == null) {
            throw new RuntimeException("获取Biinfo失败，Biinfo的ID为：" + biinfoId);
        }
        dataprobiWphMsg = new DataprobiWphMsg();
        dataprobiWphMsg.setUserid("**********");
        dataprobiWphMsg.setYhid(biInfo.getYhid());
        dataprobiWphMsg.setQd(biInfo.getQd());
        dataprobiWphMsg.setDpmc(biInfo.getDpmc());
        dataprobiWphMsg.setBiinfoId(biInfo.getId());
        dataprobiWphMsg.setType(1);
        dataprobiWphMsg.setSplb(1);


        List<String> rqList1 = Collections.singletonList("2025-06-08");
        List<String> rqList = Collections.emptyList();
        List<String> rqList2 = DataprobiDateUtil.getBetweenDate("2025-05-01", "2025-05-20");

        dataprobiWphMsg.setSpxg(rqList);
        dataprobiWphMsg.setSpxgdp(rqList);
        dataprobiWphMsg.setCtsplb(rqList1);
        dataprobiWphMsg.setWxkjljdp(rqList);
        dataprobiWphMsg.setWxkyjyghf(rqList);
        dataprobiWphMsg.setTmjljdp(rqList);
        dataprobiWphMsg.setZwaqyjljdp(rqList);
        dataprobiWphMsg.setZwjlyqjljdp(rqList);
        dataprobiWphMsg.setTmjljdp(rqList);
        dataprobiWphMsg.setZnggjljdp(rqList);
        dataprobiWphMsg.setYxpthfdp(rqList);




        BiThreadLocals.init(dataprobiWphMsg);
    }


    @Test
    public void spxg() {
        DefaultbiWphSpxgProcess bean = abujlbBeanFactory.getBean(DefaultbiWphSpxgProcess.class);
        bean.dealGoods();
        bean.dealShop();
        System.out.println("处理商品效果数据完成");
    }

    /**
     * 常态商品插入spnew处理
     */
    @Test
    public void ctsplb() {
        DefaultBiWphSplbDealProcess bean = abujlbBeanFactory.getBean(DefaultBiWphSplbDealProcess.class);
        bean.dealGoods();
        System.out.println("处理商品列表数据完成");
    }

    /**
     * 唯享客奖励金
     */
    @Test
    public void wxkjlj() {
        DefaultbiWxkjljProcess bean = abujlbBeanFactory.getBean(DefaultbiWxkjljProcess.class);
        bean.dealShop();
        System.out.println("处理唯享客奖励金数据完成");
    }



    /**
     * 爱奇艺奖励金
     */
    @Test
    public void aqyjlj() {
        DefaultbiZwaqyjljProcess bean = abujlbBeanFactory.getBean(DefaultbiZwaqyjljProcess.class);
        bean.dealShop();
        System.out.println("处理爱奇艺奖励金数据完成");
    }


    /**
     * 巨量引擎奖励金
     */
    @Test
    public void jlyqjlj() {
        DefaultbiZwjlyqjljProcess bean = abujlbBeanFactory.getBean(DefaultbiZwjlyqjljProcess.class);
        bean.dealShop();
        System.out.println("处理巨量引擎奖励金数据完成");
    }


    /**
     * 营销平台花费
     */
    @Test
    public void yxpthf() {
        DefaultbiYxpthfProcess bean = abujlbBeanFactory.getBean(DefaultbiYxpthfProcess.class);
        bean.dealShop();
        System.out.println("处理营销平台花费数据完成");
    }

    /**
     * 站内广告奖励金花费
     */
    @Test
    public void znggjlj() {
        DefaultbiZnggjljProcess bean = abujlbBeanFactory.getBean(DefaultbiZnggjljProcess.class);
        bean.dealShop();
        System.out.println("处理站内广告奖励金花费数据完成");
    }

    /**
     * target-max奖励金
     */
    @Test
    public void tmjlj() {
        DefaultbiTmjljProcess bean = abujlbBeanFactory.getBean(DefaultbiTmjljProcess.class);
        bean.dealShop();
        System.out.println("处理target-max奖励金数据完成");
    }

    @Test
    public void runLocal() {
        BiThreadLocals.getMsgBean().setType(1);
        BiWxsphDataDealService biWxsphDataDealService = abujlbBeanFactory.getBean(BiWxsphDataDealService.class);
        biWxsphDataDealService.process();
    }

    @Test
    public void sendMq() {
        SendResult sendResult = aliyunMq2.send("dataprobiv2_jst", dataprobiWphMsg.getUuid(), new JobMessage("dataprobi_v2_wph_processor", dataprobiWphMsg));
        System.out.println("唯品会：" + dataprobiWphMsg.getDpmc() + "消息发送成功，消息msgId：" + sendResult.getMessageId());
    }
    
    /**
     * 根据userid批量删除唯品会商品数据
     * @param
     */
    @Test
    public void batchDeleteWphDataByUserid() {
        String userid = dataprobiWphMsg.getUserid();
        int yhid = dataprobiWphMsg.getYhid();
        String qd = "WPH";
        
        List<String> bbidList = spnewTsDao.getWPHbbidList(userid);
        if (bbidList == null || bbidList.isEmpty()) {
            System.out.println("未找到用户" + userid + "的唯品会商品数据");
            return;
        }
        
        int count = 0;
        for (String bbid : bbidList) {
            spnewTsDao.delRow(yhid, qd, userid, bbid);
            count++;
        }
        
        System.out.println("成功删除用户" + userid + "的唯品会商品数据，共" + count + "条");
    }
}