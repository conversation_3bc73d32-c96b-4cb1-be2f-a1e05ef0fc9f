package com.abujlb.dataprobi.test;

import com.abujlb.AbujlbBeanFactory;
import com.abujlb.BaseTest;
import com.abujlb.dataprobi.bean.BiInfo;
import com.abujlb.dataprobi.bean.mn.BiSupplier;
import com.abujlb.dataprobi.bean.mn.DataprobiMnMsg;
import com.abujlb.dataprobi.processes.mn.DefaultBiMNAiztDealProcess;
import com.abujlb.dataprobi.processes.mn.DefaultBiMNYlmfDealProcess;
import com.abujlb.dataprobi.processes.mn.DefaultBiMNZtcDealProcess;
import com.abujlb.dataprobi.server.DataUploader;
import com.abujlb.dataprobi.service.BiMNDataDealService;
import com.abujlb.dataprobi.threads.BiThreadLocals;
import com.abujlb.dataprobi.tsdao.SpnewTsDao;
import com.abujlb.dataprobi.util.DataprobiDateUtil;
import com.abujlb.mq.AliyunMq2;
import com.abujlb.mq.JobMessage;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.ons.api.SendResult;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/31 9:15
 */
public class TestBiMNProcess extends BaseTest {

    @Autowired
    private AbujlbBeanFactory abujlbBeanFactory;

    @Autowired
    private BiMNDataDealService biMNDataDealService;

    @Autowired
    private AliyunMq2 aliyunMq2;

    @Autowired
    private SpnewTsDao spTsDao;

    private final DataprobiMnMsg dataprobiMnMsg;

    {
        final int biinfoId = 11105; // bi_info表的id
        BiInfo biInfo = DataUploader.getBiinfoById(biinfoId);
        if (biInfo == null) {
            throw new RuntimeException("获取Biinfo失败，Biinfo的ID为：" + biinfoId);
        }
        dataprobiMnMsg = new DataprobiMnMsg();
        dataprobiMnMsg.setUserid(biInfo.getUserid());
        dataprobiMnMsg.setYhid(biInfo.getYhid());
        dataprobiMnMsg.setQd(biInfo.getQd());
        dataprobiMnMsg.setDpmc(biInfo.getDpmc());
        dataprobiMnMsg.setBiinfoId(biInfo.getId());
        dataprobiMnMsg.setType(1);
        dataprobiMnMsg.setAuto(0);
        dataprobiMnMsg.setSplb(0);
        dataprobiMnMsg.setDpMonths(Collections.emptyList());

//        List<String> rqList2 = DataprobiDateUtil.getBetweenDate("2024-10-01", "2024-11-04");
        List<String> rqList2 = new ArrayList<>() ;
//        List<String> rqList =
//        List<String> rqList =Collections.singletonList("2024-12-03");
        List<String> rqList = DataprobiDateUtil.getBetweenDate("2025-05-01", "2025-06-11");
//        List<String> rqList = Collections.singletonList("2025-05-13");
        dataprobiMnMsg.setZtc(rqList);
        dataprobiMnMsg.setAizt(rqList2);
        dataprobiMnMsg.setYlmf(rqList2);
        dataprobiMnMsg.setDmbzt(rqList2);
        dataprobiMnMsg.setQztg(rqList2);

        dataprobiMnMsg.setZtctgfx(rqList2);
        dataprobiMnMsg.setYlmftgfx(rqList2);
        dataprobiMnMsg.setAizttgfx(rqList2);
        dataprobiMnMsg.setDmbzttgfx(rqList2);
        dataprobiMnMsg.setQztgtgfx(rqList2);
        BiThreadLocals.init(dataprobiMnMsg);

        //查询supplier
        BiSupplier mn = DataUploader.getSupplierRules(biInfo.getUserid(), "MN");
        if (mn != null) {
            BiThreadLocals.addSupplier(mn);
            BiThreadLocals.addMnMetaRule(JSONObject.parseObject(mn.getMateRule()));
        }
    }

    @Test
    public void aizt() {
        //查询当前MN店铺的所有宝贝
        List<String> bbidList = spTsDao.getMNbbidList(dataprobiMnMsg.getUserid());
        BiThreadLocals.getSupplier().setBbidList(bbidList);

        DefaultBiMNAiztDealProcess bean = abujlbBeanFactory.getBean(DefaultBiMNAiztDealProcess.class);
        bean.dealGoods();
    }

    @Test
    public void ylmf() {
        //查询当前MN店铺的所有宝贝
        List<String> bbidList = spTsDao.getMNbbidList(dataprobiMnMsg.getUserid());
        BiThreadLocals.getSupplier().setBbidList(bbidList);

        DefaultBiMNYlmfDealProcess bean = abujlbBeanFactory.getBean(DefaultBiMNYlmfDealProcess.class);
        bean.dealGoods();
    }

    @Test
    public void ztc() {
        DefaultBiMNZtcDealProcess bean = abujlbBeanFactory.getBean(DefaultBiMNZtcDealProcess.class);
        bean.dealGoods();
    }

    @Test
    public void runLocal() {
        biMNDataDealService.process();
        System.out.println("************ 完成！");
    }

    @Test
    public void sendMqJst() {
        SendResult sendResult = aliyunMq2.send("dataprobiv2_jst", dataprobiMnMsg.getUuid(), new JobMessage("dataprobi_v2_mn_processor", dataprobiMnMsg));
        System.out.println(dataprobiMnMsg.getDpmc() + "消息发送成功，消息msgId：" + sendResult.getMessageId());
    }
}
