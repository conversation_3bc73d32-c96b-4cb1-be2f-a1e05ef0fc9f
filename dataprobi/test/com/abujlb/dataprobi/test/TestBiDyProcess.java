package com.abujlb.dataprobi.test;

import com.abujlb.AbujlbBeanFactory;
import com.abujlb.BaseTest;
import com.abujlb.dataprobi.bean.BiInfo;
import com.abujlb.dataprobi.bean.dy.DataprobiDyMsgBean;
import com.abujlb.dataprobi.bean.dy.jlqc.BiJlqcAvvid;
import com.abujlb.dataprobi.processes.dy.*;
import com.abujlb.dataprobi.server.DataUploader;
import com.abujlb.dataprobi.service.BiDyDataDealService;
import com.abujlb.dataprobi.threads.BiThreadLocals;
import com.abujlb.dataprobi.util.DataprobiDateUtil;
import com.abujlb.mq.AliyunMq2;
import com.abujlb.mq.JobMessage;
import com.aliyun.openservices.ons.api.SendResult;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/31 9:15
 */
public class TestBiDyProcess extends BaseTest {


    @Autowired
    private AbujlbBeanFactory abujlbBeanFactory;

    @Autowired
    private AliyunMq2 aliyunMq2;

    private final DataprobiDyMsgBean dataprobiDyMsgBean;

    {
        final int biinfoId = 1830;
        BiInfo biInfo = DataUploader.getBiinfoById(biinfoId);
        if (biInfo == null) {
            throw new RuntimeException("获取Biinfo失败，Biinfo的ID为：" + biinfoId);
        }
        dataprobiDyMsgBean = new DataprobiDyMsgBean();
        dataprobiDyMsgBean.setUserid(biInfo.getUserid());
        dataprobiDyMsgBean.setYhid(biInfo.getYhid());
        dataprobiDyMsgBean.setQd(biInfo.getQd());
        dataprobiDyMsgBean.setDpmc(biInfo.getDpmc());
        dataprobiDyMsgBean.setBiinfoId(biInfo.getId());
        dataprobiDyMsgBean.setType(1);
        dataprobiDyMsgBean.setSplb(0);
//         List<String> rqList = Arrays.asList("2023-03-18","2023-03-19");
//        List<String> rqList = Collections.singletonList("2025-03-29");
        List<String> rqList = Collections.emptyList();
//        List<String> rqList = DataprobiDateUtil.getBetweenDate("2025-03-01", "2025-03-30");
//        List<String> rqList = Collections.singletonList("2024-05-23");
//        List<String> rqList2 =  DataprobiDateUtil.getBetweenDate("2025-03-01", "2025-03-12");
//        List<String> rqList2 = Collections.singletonList("2025-03-11");
//        List<String> rqList2 = DataprobiDateUtil.getBetweenDate("2024-08-01", "2024-08-06");
//        List<String> rqList = DataprobiDateUtil.getBetweenDate("2024-02-01", "2024-02-20");
//        List<String> rqList2 = DataprobiDateUtil.getBetweenDate("2024-02-01", "2024-02-26");
//        List<String> rqList3 = DataprobiDateUtil.getBetweenDate("2024-04-01", "2024-04-11");
//        List<String> rqList3 =  DataprobiDateUtil.getBetweenDate("2024-09-10", "2024-09-19");
        List<String> rqList3 =  DataprobiDateUtil.getBetweenDate("2025-05-01",DataprobiDateUtil.getLastday());
//        List<String> rqList3 = Collections.singletonList("2025-06-13");
//        List<String> rqList3 = Collections.emptyList();
//        List<String> rqList5 = Collections.singletonList("2024-01-04");

        dataprobiDyMsgBean.setSpxg(rqList);
        dataprobiDyMsgBean.setSpxgdp(rqList);
        dataprobiDyMsgBean.setDsp(rqList);
        dataprobiDyMsgBean.setDspdp(rqList);
        dataprobiDyMsgBean.setZbdp(rqList3);
        dataprobiDyMsgBean.setPpdp(rqList);
        dataprobiDyMsgBean.setJxlm(rqList);
        dataprobiDyMsgBean.setJxlmdp(rqList);
        dataprobiDyMsgBean.setQytgdp(rqList);
        dataprobiDyMsgBean.setCwjldp(rqList);
        dataprobiDyMsgBean.setDou(rqList);
        dataprobiDyMsgBean.setDpMonths(Collections.emptyList());
        dataprobiDyMsgBean.setQytgtsp(rqList);
        dataprobiDyMsgBean.setDr_zy_ppdp(rqList);
        dataprobiDyMsgBean.setDr_zy_qytgdp(rqList);
        dataprobiDyMsgBean.setDr_zy_spxg(rqList);
        dataprobiDyMsgBean.setDr_zy_spxgdp(rqList);
        dataprobiDyMsgBean.setDr_zy_tspdp(rqList);
        dataprobiDyMsgBean.setDr_zy_tzbjdp(rqList);
        dataprobiDyMsgBean.setFreeCommissionSpxg(rqList);
        dataprobiDyMsgBean.setTsptgfx(rqList);
        dataprobiDyMsgBean.setJxlmdd(rqList);
        dataprobiDyMsgBean.setQytg_dyh(rqList);
        dataprobiDyMsgBean.setBztg_tzbj_dyh(rqList);
        dataprobiDyMsgBean.setJxlm_tzfwf(rqList);
        dataprobiDyMsgBean.setCwjldp2(rqList);
        BiThreadLocals.init(dataprobiDyMsgBean);

        //获取千川list
        //获取当前店铺的当前时间下的所有千川。
        List<BiJlqcAvvid> jlqcAvvids = DataUploader.getJlqcAvvids(BiThreadLocals.getMsgBean().getUserid());
        if (CollectionUtils.isNotEmpty(jlqcAvvids)) {
            BiThreadLocals.addDyJlqcAvvidList(jlqcAvvids);
        }
    }

    @Test
    public void cwjl() {
        DefaultBiDyJqlcCwjlDealProcess bean = abujlbBeanFactory.getBean(DefaultBiDyJqlcCwjlDealProcess.class);
        bean.dealShop();
    }

    @Test
    public void cwjl2() {
        DefaultBiDyJqlcCwjl2DealProcess bean = abujlbBeanFactory.getBean(DefaultBiDyJqlcCwjl2DealProcess.class);
        bean.dealShop();
    }

    @Test
    public void dsp() {
        DefaultBiDyJqlcDspDealProcess bean = abujlbBeanFactory.getBean(DefaultBiDyJqlcDspDealProcess.class);
//        bean.dealGoods();
        bean.dealShop();
    }

    @Test
    public void pp() {
        DefaultBiDyJqlcPpDealProcess bean = abujlbBeanFactory.getBean(DefaultBiDyJqlcPpDealProcess.class);
        bean.dealShop();
    }

    @Test
    public void zb() {
        DefaultBiDyJqlcZbDealProcess bean = abujlbBeanFactory.getBean(DefaultBiDyJqlcZbDealProcess.class);
//        bean.dealShop();
        bean.dealGoods();
//        bean.compareGoods();
    }

    @Test
    public void jxlm() {
        DefaultBiDyJxlmDealProcess bean = abujlbBeanFactory.getBean(DefaultBiDyJxlmDealProcess.class);
        bean.dealGoods();
        bean.dealShop();
    }

    @Test
    public void splb() {
        DefaultBiDySplbDealProcess bean = abujlbBeanFactory.getBean(DefaultBiDySplbDealProcess.class);
        bean.dealGoods();
    }

    @Test
    public void spxg() {
        DefaultBiDySpxgDealProcess bean = abujlbBeanFactory.getBean(DefaultBiDySpxgDealProcess.class);
        bean.dealGoods();
//        bean.dealShop();
    }

    @Test
    public void dou() {
        DefaultBiDyDouDealProcess bean = abujlbBeanFactory.getBean(DefaultBiDyDouDealProcess.class);
        bean.dealGoods();
    }

    @Test
    public void qytg() {
        DefaultBiDyJqlcQytgDealProcess bean = abujlbBeanFactory.getBean(DefaultBiDyJqlcQytgDealProcess.class);
        bean.dealGoods();
    }

    @Test
    public void dr_zy_spxg() {
        DefaultBiDrZySpxgProcess bean = abujlbBeanFactory.getBean(DefaultBiDrZySpxgProcess.class);
        bean.dealGoods();
//        bean.dealShop();
    }

    @Test
    public void dr_zy_qytg() {
        DefaultBiDrZyJlqcQytgProcess bean = abujlbBeanFactory.getBean(DefaultBiDrZyJlqcQytgProcess.class);
        bean.dealShop();
    }

    @Test
    public void dr_zy_pp() {
        DefaultBiDrZyJlqcPpProcess bean = abujlbBeanFactory.getBean(DefaultBiDrZyJlqcPpProcess.class);
        bean.dealShop();
    }

    @Test
    public void dr_zy_tsp() {
        DefaultBiDrZyJlqcTspProcess bean = abujlbBeanFactory.getBean(DefaultBiDrZyJlqcTspProcess.class);
        bean.dealShop();
    }

    @Test
    public void dr_zy_tzbj() {
        DefaultBiDrZyJlqcTzbjProcess bean = abujlbBeanFactory.getBean(DefaultBiDrZyJlqcTzbjProcess.class);
        bean.dealShop();
    }

    @Test
    public void freeCommissionSpxg() {
        DefaultBiDyFreeCommissionSpxgDealProcess bean = abujlbBeanFactory.getBean(DefaultBiDyFreeCommissionSpxgDealProcess.class);
//        bean.dealGoods();
        bean.compareGoods();
    }

    @Test
    public void qytgtsp() {
        DefaultBiJlqcQytgTspProcess bean = abujlbBeanFactory.getBean(DefaultBiJlqcQytgTspProcess.class);
        bean.dealGoods();
    }

    @Test
    public void qytg_dyh() {
        DefaultBiJlqcQytgTzbDyhDataProcess bean = abujlbBeanFactory.getBean(DefaultBiJlqcQytgTzbDyhDataProcess.class);
//        bean.dealGoods();
        bean.compareGoods() ;
    }

    @Test
    public void bztg_tzbj_dyh() {
        DefaultBiJlqcBztgTzbjDyhDataProcess bean = abujlbBeanFactory.getBean(DefaultBiJlqcBztgTzbjDyhDataProcess.class);
        bean.dealGoods();
//        bean.compareGoods() ;
    }

    @Test
    public void jxlm_tzfwf(){
        DefaultBiDyJxlmTzfwfDealProcess bean = abujlbBeanFactory.getBean(DefaultBiDyJxlmTzfwfDealProcess.class);
        bean.dealGoods();
    }

    @Test
    public void runLocal() throws InterruptedException {
//        BiThreadLocals.getMsgBean().setType(2);
        BiDyDataDealService biDyDataDealService = abujlbBeanFactory.getBean(BiDyDataDealService.class);
        biDyDataDealService.process();
    }

    @Test
    public void sendMq() {
        SendResult sendResult = aliyunMq2.send("dataprobiv2_jst", dataprobiDyMsgBean.getUuid(), new JobMessage("dataprobi_v2_dy_processor", dataprobiDyMsgBean));
        System.out.println("抖店：" + dataprobiDyMsgBean.getDpmc() + "消息发送成功，消息msgId：" + sendResult.getMessageId());
    }
}
