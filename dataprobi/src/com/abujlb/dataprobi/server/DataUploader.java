package com.abujlb.dataprobi.server;

import com.abujlb.CommonConfig;
import com.abujlb.Result;
import com.abujlb.dataprobi.annotations.ClrqIgnore;
import com.abujlb.dataprobi.bean.*;
import com.abujlb.dataprobi.bean.albb.Dataprobi1688Msg;
import com.abujlb.dataprobi.bean.dw.DataprobiDwMsg;
import com.abujlb.dataprobi.bean.dy.DataprobiDyMsgBean;
import com.abujlb.dataprobi.bean.dy.jlqc.BiJlqcAvvid;
import com.abujlb.dataprobi.bean.jd.DataprobiJdMsgBean;
import com.abujlb.dataprobi.bean.ks.DataprobiKsMsg;
import com.abujlb.dataprobi.bean.mn.BiSupplier;
import com.abujlb.dataprobi.bean.mn.DataprobiMnMsg;
import com.abujlb.dataprobi.bean.pdd.DataprobiPddMsgBean;
import com.abujlb.dataprobi.bean.tb.DataprobiMsg;
import com.abujlb.dataprobi.bean.tgc.BiTgcAiztone;
import com.abujlb.dataprobi.bean.tgc.DataprobiTgcMsg;
import com.abujlb.dataprobi.bean.wph.DataprobiWphMsg;
import com.abujlb.dataprobi.bean.wxsph.DataprobiWxsphMsg;
import com.abujlb.dataprobi.bean.xhs.DataprobiXhsMsg;
import com.abujlb.dataprobi.bean.ymx.DataprobiYmxMsg;
import com.abujlb.dataprobi.constant.DataprobiConst;
import com.abujlb.dataprobi.util.DataprobiDateUtil;
import com.abujlb.dataprobi.util.DataprobiStringUtil;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.abujlb.service.ServiceClient;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;

import java.lang.reflect.Field;
import java.lang.reflect.Type;
import java.util.*;
import java.util.stream.Collectors;

public class DataUploader {

    private static final Logger log = Logger.getLogger(DataUploader.class);

    private static final ServiceClient client;
    private static final String service4Url;
    private static final String service2Url;
    private static final HashMap<Class<?>, List<String>> map = new HashMap<>();

    //"spxg", "ztc", "ylmf", "aizt", "pxb", "ztctgxgfx", "spxgdp", "ztcdp", "ylmfdp", "aiztdp", "pxbdp", "rlyq", "rlyqdp"
    private static final List<String> TX = new ArrayList<>();
    //{"spmx", "spmxdp", "ddjb", "ddjbdp", "ddcj", "ddcjdp", "ddss", "ddssdp", "qztg", "qztgdp", "mxdp", "zbtg"};
    private static final List<String> PDD = new ArrayList<>();
    // {"spxg", "spxgdp", "yhq", "yhqdp", "jdou", "jdoudp", "bt", "btdp", "cxbt", "cxbtdp", "jdkc", "jdkcdp", "jdhtdp", "splbs", "jtk", "jtkdp", "gwcd", "gwcddp", "jdzwdp", "jdztdp", "jskdp", "jrwdp"};
    private static final List<String> JD = new ArrayList<>();
    //{"spxg", "spxgdp", "yjzc", "yjzcdp", "dsp", "dspdp", "tzfwf", "zbsjdp", "ppztsjdp"};
    private static final List<String> DY = new ArrayList<>();
    // {"ylmf","ztc","aizt"}
    private static final List<String> MN = new ArrayList<>();
    private static final List<String> ALBB = new ArrayList<>();
    private static final List<String> TGC = new ArrayList<>();
    private static final List<String> XHS = new ArrayList<>();
    private static final List<String> WXSPH = new ArrayList<>();
    private static final List<String> DW = new ArrayList<>();
    private static final List<String> KS = new ArrayList<>();
    private static final List<String> WPH = new ArrayList<>();
    private static final List<String> YMX = new ArrayList<>();

    static {
        client = new ServiceClient(CommonConfig.getString("jyrbAppid"), CommonConfig.getString("jyrbAppkey"));
        service4Url = CommonConfig.getString("service4Url");
        service2Url = CommonConfig.getString("service2Url");

        Field[] txFields = DataprobiMsg.class.getDeclaredFields();
        for (Field txField : txFields) {
            if (txField.getAnnotation(ClrqIgnore.class) == null) {
                TX.add(txField.getName());
            }
        }

        Field[] pddFields = DataprobiPddMsgBean.class.getDeclaredFields();
        for (Field field : pddFields) {
            if (field.getAnnotation(ClrqIgnore.class) == null) {
                PDD.add(field.getName());
            }
        }

        Field[] jdFields = DataprobiJdMsgBean.class.getDeclaredFields();
        for (Field field : jdFields) {
            if (field.getAnnotation(ClrqIgnore.class) == null) {
                JD.add(field.getName());
            }
        }

        Field[] dyFields = DataprobiDyMsgBean.class.getDeclaredFields();
        for (Field field : dyFields) {
            if (field.getAnnotation(ClrqIgnore.class) == null) {
                DY.add(field.getName());
            }
        }

        Field[] mnFields = DataprobiMnMsg.class.getDeclaredFields();
        for (Field field : mnFields) {
            if (field.getAnnotation(ClrqIgnore.class) == null) {
                MN.add(field.getName());
            }
        }

        Field[] albbFields = Dataprobi1688Msg.class.getDeclaredFields();
        for (Field field : albbFields) {
            if (field.getAnnotation(ClrqIgnore.class) == null) {
                ALBB.add(field.getName());
            }
        }


        Field[] tgcFields = DataprobiTgcMsg.class.getDeclaredFields();
        for (Field field : tgcFields) {
            if (field.getAnnotation(ClrqIgnore.class) == null) {
                TGC.add(field.getName());
            }
        }

        Field[] xhsFields = DataprobiXhsMsg.class.getDeclaredFields();
        for (Field field : xhsFields) {
            if (field.getAnnotation(ClrqIgnore.class) == null) {
                XHS.add(field.getName());
            }
        }

        Field[] wxsphFields = DataprobiWxsphMsg.class.getDeclaredFields();
        for (Field field : wxsphFields) {
            if (field.getAnnotation(ClrqIgnore.class) == null) {
                WXSPH.add(field.getName());
            }
        }

        Field[] dwFields = DataprobiDwMsg.class.getDeclaredFields();
        for (Field field : dwFields) {
            if (field.getAnnotation(ClrqIgnore.class) == null) {
                DW.add(field.getName());
            }
        }

        Field[] ksFields = DataprobiKsMsg.class.getDeclaredFields();
        for (Field field : ksFields) {
            if (field.getAnnotation(ClrqIgnore.class) == null) {
                KS.add(field.getName());
            }
        }

        Field[] wphFields = DataprobiWphMsg.class.getDeclaredFields();
        for (Field field : wphFields) {
            if (field.getAnnotation(ClrqIgnore.class) == null) {
                WPH.add(field.getName());
            }
        }
        Field[] ymxFields = DataprobiYmxMsg.class.getDeclaredFields();
        for (Field field : ymxFields) {
            if (field.getAnnotation(ClrqIgnore.class) == null) {
                YMX.add(field.getName());
            }
        }


        map.put(DataprobiPddMsgBean.class, PDD);
        map.put(DataprobiJdMsgBean.class, JD);
        map.put(DataprobiMsg.class, TX);
        map.put(DataprobiDyMsgBean.class, DY);
        map.put(DataprobiMnMsg.class, MN);
        map.put(Dataprobi1688Msg.class, ALBB);
        map.put(DataprobiTgcMsg.class, TGC);
        map.put(DataprobiXhsMsg.class, XHS);
        map.put(DataprobiWxsphMsg.class, WXSPH);
        map.put(DataprobiDwMsg.class, DW);
        map.put(DataprobiKsMsg.class, KS);
        map.put(DataprobiWphMsg.class, WPH);
        map.put(DataprobiYmxMsg.class, YMX);
    }

    public static <T extends DataprobiBaseMsgBean> void updateBiInfoClrq(BiInfo biInfo, T t) {
        try {
            if (biInfo == null) {
                return;
            }
            if (t.getSplb() == 1) {
                biInfo.setSplb(1);
            }

            String clrqjson = biInfo.getClrqjson();
            if (StringUtils.isBlank(clrqjson))
                clrqjson = "{}";

            JSONObject jsonObject = JSONObject.parseObject(clrqjson);
            List<String> types = map.get(t.getClass());
            if (types == null || types.size() == 0) {
                return;
            }

            for (String type : types) {
                Object value = t.getClass().getMethod("get" + DataprobiStringUtil.strFirstCharUpper(type)).invoke(t);
                if (value instanceof List) {
                    String max = max((List<String>) value);
                    if (!jsonObject.containsKey(type) && StringUtils.isBlank(max)) {
                        jsonObject.put(type, max);
                        continue;
                    }
                    if (FastJSONObjAttrToNumber.toString(jsonObject, type).compareTo(max) < 0) {
                        jsonObject.put(type, max);
                    }
                }
            }

            biInfo.setClrqjson(jsonObject.toJSONString());
            client.exec(service2Url + "/bi2/updateBiInfoById", biInfo);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    public static BiInfo getBiinfoById(int biInfoId) {
        try {
            BiInfo biInfo = new BiInfo();
            biInfo.setId(biInfoId);
            String result = client.exec(service2Url + "/bi2/getBiinfoById", biInfo);
            if (Result.isTrue(result)) {
                BiInfo info = Result.getValueFromJson(result, "data", BiInfo.class);
                if (info != null) {
                    JSONObject jsonObject = JSONObject.parseObject(result);
                    JSONObject data = jsonObject.getJSONObject("data");
                    long startLong = data.getLongValue("start");
                    String start = DataprobiDateUtil.timestampToDateTimeStr2(startLong + "");
                    info.setStart(start);
                }
                return info;
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }

    public static BiYhdp getBiYhdpByYhidAndUseridQd(String userid, String qd, int yhid, int sfty) {
        try {
            BiYhdp param = new BiYhdp();
            param.setSfty(sfty);
            param.setQd(qd);
            param.setUserid(userid);
            param.setYhid(yhid);
            String url = service4Url + "/bi2/getBiYhdpByYhidAndUseridQd";
            String result = client.exec(url, param);
            if (Result.isTrue(result)) {
                return Result.getValueFromJson(result, "data", BiYhdp.class);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }

    public static BiYhdp getBiYhdpByYhidAndUseridQd(String userid, String qd, int yhid) {
        return getBiYhdpByYhidAndUseridQd(userid, qd, yhid, 0);
    }

    public static void updateBiYhdp(BiYhdp biYhdp) {
        if (biYhdp == null) {
            return;
        }
        try {
            int cscs = 1;
            int max = 3;
            while (true) {
                String result = client.exec(service4Url + "/bi2/updateBiYhdpById", biYhdp);
                if (Result.isTrue(result)) {
                    break;
                }
                log.error("update bi_yhdp fail-->" + new Gson().toJson(biYhdp) + "--->" + result);
                cscs++;
                if (cscs > max) {
                    break;
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    private static String max(List<String> list) {
        try {
            if (CollectionUtils.isEmpty(list)) {
                return "";
            }

            List<String> temp = list.stream().sorted().collect(Collectors.toList());
            Collections.reverse(temp);
            return temp.get(0);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return "";
    }

    public static BiSupplier getSupplierRules(String userid, String qd) {
        try {
            BiSupplier biSupplier = new BiSupplier();
            biSupplier.setSupplierUserid(userid);
            biSupplier.setQd(qd);
            String result = client.exec(service2Url + "/bi2/getSupplier", biSupplier);
            if (Result.isTrue(result)) {
                return Result.getValueFromJson(result, "data", BiSupplier.class);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }


    public static boolean testYh(String qd, String userid, int yhid) {
        try {
            Map<String, String> map = new HashMap<>();
            map.put("qd", qd);
            map.put("userid", userid);
            map.put("yhid", String.valueOf(yhid));
            String result = client.exec(service4Url + "/bi2/checkTestYh", map);
            if (Result.isTrue(result)) {
                return Result.getValueFromJson(result, "data", Boolean.class);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return false;
    }

    public static List<BiInfoOther> getAllUserid(int biinfoId, String qd) {
        try {
            BiInfoOther param = new BiInfoOther();
            param.setInfoId(biinfoId);
            param.setQd(qd);
            String s = client.exec(service2Url + "/bi_info_other/getListByInfoIdAndQd", param);
            if (Result.isTrue(s)) {
                Type type = new TypeToken<List<BiInfoOther>>() {
                }.getType();
                return Result.getValueFromJson(s, "data", type);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }


    public static String getCategoryJdSjlmsm(String oneLmmc, String twoLmmc, String threeLmmc) {
        try {
            if (StringUtils.isBlank(threeLmmc) || StringUtils.isBlank(oneLmmc) || StringUtils.isBlank(twoLmmc)) {
                return null;
            }
            Map<String, String> map = new HashMap<>();
            map.put("oneLmmc", oneLmmc);
            map.put("twoLmmc", twoLmmc);
            map.put("threeLmmc", threeLmmc);

            String s = client.exec(service2Url + "/category/jd2/getsjlmsm", DataprobiConst.GSON.toJson(map));
            if (Result.isTrue(s)) {
                JSONObject jsonObj = JSONObject.parseObject(s);
                return jsonObj.getString("data");
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }


    public static List<BiInfoOther> getBiInfoOtherByUserid(String userid, String otherQd) {
        try {
            BiInfoOther param = new BiInfoOther();
            param.setUserid(Long.parseLong(userid));
            param.setQd(otherQd);
            String s = client.exec(service2Url + "/bi_info_other/getListByUserid", param);
            if (Result.isTrue(s)) {
                Type type = new TypeToken<List<BiInfoOther>>() {
                }.getType();
                return Result.getValueFromJson(s, "data", type);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }


    public static List<BiYhdp> getBiYhdpListJst(String userid, String qd) {
        BiYhdp biYhdp = new BiYhdp();
        biYhdp.setUserid(userid);
        biYhdp.setQd(qd);
        try {
            String s = client.exec(service4Url + "/bi2/getBiYhdpList", biYhdp);
            if (Result.isTrue(s)) {
                Type type = new TypeToken<List<BiYhdp>>() {
                }.getType();
                return Result.getValueFromJson(s, "data", type);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return Collections.emptyList();

    }

    public static void rerunTask(BiRuleReRunTask biRuleReRunTask) {
        try {
            final List<Integer> testYhid = Arrays.asList(1961, 5241);
            if (testYhid.contains(biRuleReRunTask.getYhid())) {
                return;
            }
            client.exec(service4Url + "/bi_rerun_task/addTask", biRuleReRunTask);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }


    public static List<BiInfoOther> getBiInfoOtherListByBiInfoIdAndOtherQd(int biinfoId, String otherQd) {
        try {
            BiInfoOther param = new BiInfoOther();
            param.setInfoId(biinfoId);
            param.setQd(otherQd);
            String s = client.exec(service2Url + "/bi_info_other/getListByInfoIdAndQd", param);
            if (Result.isTrue(s)) {
                Type type = new TypeToken<List<BiInfoOther>>() {
                }.getType();
                return Result.getValueFromJson(s, "data", type);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }


    public static List<BiJlqcAvvid> getJlqcAvvids(String userid) {
        Map<String, String> map = new HashMap<>();
        map.put("userid", userid);
        String s = client.exec(service2Url + "/bijlqc/getJlqcAvvids", map);
        if (Result.isTrue(s)) {
            Type type = new TypeToken<List<BiJlqcAvvid>>() {
            }.getType();
            return Result.getValueFromJson(s, "data", type);
        }
        return null;
    }

    public static List<BiInfo> getAllBiInfo() {
        String s = client.exec(service2Url + "/bi2/getAllBiInfos", new Object());
        if (Result.isTrue(s)) {
            Type type = new TypeToken<List<BiInfo>>() {
            }.getType();
            return Result.getValueFromJson(s, "data", type);
        }
        return null;
    }

    public static List<BiTgcAiztone> getTgcAiztoneList(String userid) {
        String s = client.exec(service2Url + "/bitgc/getTgcAiztoneByUserid", userid);
        if (Result.isTrue(s)) {
            Type type = new TypeToken<List<BiTgcAiztone>>() {
            }.getType();
            return Result.getValueFromJson(s, "data", type);
        }
        return null;
    }


    public static String getIp() {
        String json = client.exec("https://service2.abujlb.com/ip/myip", "");
        return Result.getStringFromJson(json, "ip");
    }

    public static void updateBiInfoSycmcljzrq(BiInfo newInfo) {
        client.exec(service2Url + "/bi2/updateBiInfoSycmcljzrq2", newInfo);
    }

    public static void updateBiYhdpAlipayrq(String userId, String qd, String zdksrq, String zdjsrq) {
        Map<String, String> map = new HashMap<>();
        map.put("qd", qd);
        map.put("userid", userId);
        map.put("zdjsrq", zdjsrq);
        map.put("zdksrq", zdksrq);
        client.exec(service4Url + "/bi2/updateBiYhdpAlipayrq", map);
    }

    public static String queryCurrencyRate(String currency, String rq) {
        Map<String, String> map = new HashMap<>();
        map.put("currency", currency);
        map.put("rq", rq);
//        return client.exec("http://localhost:8080/service4//currencyRate/queryRate", map);
        return client.exec(service4Url + "/currencyRate/queryRate", map);
    }
}
