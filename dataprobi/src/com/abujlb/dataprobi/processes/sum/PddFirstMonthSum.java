package com.abujlb.dataprobi.processes.sum;

import com.abujlb.dataprobi.annotations.BiSumPro;
import com.abujlb.dataprobi.bean.SqliteDp;
import com.abujlb.dataprobi.bean.pdd.SqlitePddSpxgDp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.threads.BiThreadLocals;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/3/27 15:32
 */
@Component
@BiSumPro(qd = Qd.PDD, order = 5)
public class PddFirstMonthSum extends FirstMonthSum {

    private final String[] COLUMNS = {
            BiSycmDpDataTsDao.PDD_COLUMN_SPXG,
            BiSycmDpDataTsDao.PDD_COLUMN_SPXG_JYSJ,
            BiSycmDpDataTsDao.PDD_COLUMN_SPXG_SHSJ,
            BiSycmDpDataTsDao.PDD_COLUMN_SPXG_JYGK,
    };

    @Override
    protected void getSqliteDp(String lastdayOfMonth) {
        SqlitePddSpxgDp sqlitePddSpxgDp = biUtil.dpObject(SqlitePddSpxgDp.class, lastdayOfMonth + _MONTH, COLUMNS);
        if (sqlitePddSpxgDp == null) {
            return;
        }
        SqliteDp sqliteDp = new SqliteDp(sqlitePddSpxgDp);
        sqliteDp.setDpmc(BiThreadLocals.getMsgBean().getDpmc());
        sqliteDp.setRq(lastdayOfMonth.substring(0, 7));
        super.firstTimeJysj(lastdayOfMonth, sqliteDp);
    }

}
