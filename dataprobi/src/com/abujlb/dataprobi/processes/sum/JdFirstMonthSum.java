package com.abujlb.dataprobi.processes.sum;

import com.abujlb.dataprobi.annotations.BiSumPro;
import com.abujlb.dataprobi.bean.SqliteDp;
import com.abujlb.dataprobi.bean.jd.SqliteJdSpxgDp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.threads.BiThreadLocals;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/3/27 15:32
 */
@Component
@BiSumPro(qd = Qd.JD, order = 5)
public class JdFirstMonthSum extends FirstMonthSum {

    private final String[] COLUMNS = {
            BiSycmDpDataTsDao.JD_COLUMN_SPU,
            BiSycmDpDataTsDao.JD_COLUMN_SKU,
            BiSycmDpDataTsDao.JD_COLUMN_JYGK,
            BiSycmDpDataTsDao.JD_COLUMN_REFUND
    };

    @Override
    protected void getSqliteDp(String lastdayOfMonth) {
        SqliteJdSpxgDp sqliteJdSpxgDp = biUtil.dpObject(SqliteJdSpxgDp.class, lastdayOfMonth + _MONTH, COLUMNS);
        if (Objects.isNull(sqliteJdSpxgDp)) {
            return;
        }

        SqliteDp sqliteDp = new SqliteDp(sqliteJdSpxgDp);
        sqliteDp.setDpmc(BiThreadLocals.getMsgBean().getDpmc());
        sqliteDp.setRq(lastdayOfMonth.substring(0, 7));
        super.firstTimeJysj(lastdayOfMonth, sqliteDp);
    }

}
