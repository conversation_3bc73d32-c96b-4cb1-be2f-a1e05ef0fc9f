package com.abujlb.dataprobi.processes.sum;

import com.abujlb.dataprobi.bean.BiYhdp;
import com.abujlb.dataprobi.bean.DataprobiBaseMsgBean;
import com.abujlb.dataprobi.bean.SqliteDp;
import com.abujlb.dataprobi.constant.DataprobiConst;
import com.abujlb.dataprobi.oss.BiDataOssUtil;
import com.abujlb.dataprobi.sql.BiSqliteDb;
import com.abujlb.dataprobi.threads.BiThreadLocals;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import com.abujlb.dataprobi.util.BiUtil;
import com.abujlb.dataprobi.util.DataprobiDateUtil;
import com.abujlb.dataprobi.util.SqliteDbUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/3/23 11:20
 */
@Component
public abstract class FirstMonthSum implements BiSum {
    protected final String _MONTH = "_month";
    private static final Logger logger = Logger.getLogger(FirstMonthSum.class);
    @Autowired
    protected BiSycmDpDataTsDao biSycmDpDataTsDao;

    @Autowired
    protected BiUtil biUtil;

    @Autowired
    private BiDataOssUtil biDataOssUtil;

    @Autowired
    private SqliteDbUtil sqliteDbUtil;

    @Override
    public void sum(BiYhdp biYhdp, List<String> rqList) {
        List<String> dpMonths = BiThreadLocals.getMsgBean().getDpMonths();
        if (Objects.isNull(biYhdp) || CollectionUtils.isEmpty(dpMonths)) {
            return;
        }
        DataprobiBaseMsgBean msgBean = BiThreadLocals.getMsgBean();
        logger.info(msgBean.getDpmc() + "FirstMonthSum.sum开始:" + DataprobiDateUtil.getCurrentTime());
        for (String lastdayOfMonth : dpMonths) {
            getSqliteDp(lastdayOfMonth);
        }
        logger.info(BiThreadLocals.getMsgBean().getDpmc() + "FirstMonthSum.sum结束:" + DataprobiDateUtil.getCurrentTime());
    }

    protected abstract void getSqliteDp(String lastdayOfMonth);

    protected void firstTimeJysj(String lastdayOfMonth, SqliteDp sqliteDp) {
        if (Objects.isNull(sqliteDp)) {
            return;
        }
        DataprobiBaseMsgBean msgBean = BiThreadLocals.getMsgBean();
        sqliteDp.calTghf();
        BiSqliteDb biSqliteDb = sqliteDbUtil.getDpSqliteDb(null);
        SqliteDbUtil.executeSqls(biSqliteDb, Collections.singletonList(sqliteDp.toInsertSql()));
        File file = new File(biSqliteDb.getFile());
        biDataOssUtil.upload(file, String.format(DataprobiConst.SYCM_DP_MONTH, lastdayOfMonth.substring(0, 7).replaceAll("-", ""), msgBean.getYhid(), msgBean.getQd(), msgBean.getUserid()));
        SqliteDbUtil.close(biSqliteDb);
    }
}
