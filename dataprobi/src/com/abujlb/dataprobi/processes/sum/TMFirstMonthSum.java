package com.abujlb.dataprobi.processes.sum;

import com.abujlb.dataprobi.annotations.BiSumPro;
import com.abujlb.dataprobi.bean.SqliteDp;
import com.abujlb.dataprobi.bean.tb.SqliteSpxgDp;
import com.abujlb.dataprobi.bean.tb.dp.SpxgJysjDp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.threads.BiThreadLocals;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/3/27 15:32
 */
@Component
@BiSumPro(qd = {Qd.TM, Qd.TB}, order = 5)
public class TMFirstMonthSum extends FirstMonthSum {

    private final String[] COLUMNS = {BiSycmDpDataTsDao.TX_COLUMN_SPXG, BiSycmDpDataTsDao.TX_COLUMN_SPXG_JYSJ};
    private static final Logger logger = Logger.getLogger(TMFirstMonthSum.class);

    @Override
    protected void getSqliteDp(String lastdayOfMonth) {
        try {
            String[] columnValue = biSycmDpDataTsDao.getRow(BiThreadLocals.getMsgBean().getQd() + "_" + BiThreadLocals.getMsgBean().getUserid(), lastdayOfMonth + _MONTH, COLUMNS);
            SqliteSpxgDp sqliteSpxgDp = new SqliteSpxgDp(BiThreadLocals.getMsgBean(), columnValue[0], columnValue[1]);
            SqliteDp sqliteDp = new SqliteDp(sqliteSpxgDp);
            sqliteDp.setRq(lastdayOfMonth.substring(0, 7));

            if (StringUtils.isNotBlank(columnValue[1])) {
                //生意经采集没有此项
                SpxgJysjDp spxgJysjDp = new SpxgJysjDp(columnValue[1]);

                String otherOrDefault = sqliteDp.getOtherOrDefault();
                JSONObject jsonObject = JSONObject.parseObject(otherOrDefault);
                jsonObject.put("ztchf", spxgJysjDp.getE());
                jsonObject.put("ylmfhf", spxgJysjDp.getG());
                jsonObject.put("aizthf", spxgJysjDp.getF());
                sqliteDp.setOther(jsonObject.toJSONString());
            }

            super.firstTimeJysj(lastdayOfMonth, sqliteDp);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
    }

}
