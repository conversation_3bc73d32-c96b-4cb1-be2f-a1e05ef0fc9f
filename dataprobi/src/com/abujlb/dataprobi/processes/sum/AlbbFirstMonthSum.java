package com.abujlb.dataprobi.processes.sum;

import com.abujlb.dataprobi.annotations.BiSumPro;
import com.abujlb.dataprobi.bean.SqliteDp;
import com.abujlb.dataprobi.bean.albb.SqliteAlbbSpxgDp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.threads.BiThreadLocals;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/8/10
 */
@Component
@BiSumPro(qd = Qd.ALBB, order = 5)
public class AlbbFirstMonthSum extends FirstMonthSum {

    private final String[] COLUMNS = {BiSycmDpDataTsDao.ALBB_COLUMN_SPXG1, BiSycmDpDataTsDao.ALBB_COLUMN_SPXG2};

    @Override
    protected void getSqliteDp(String lastdayOfMonth) {
        SqliteAlbbSpxgDp sqliteAlbbSpxgDp = biUtil.dpObject(SqliteAlbbSpxgDp.class, lastdayOfMonth + _MONTH, COLUMNS);
        if (sqliteAlbbSpxgDp == null) {
            return;
        }
        SqliteDp sqliteDp = new SqliteDp(sqliteAlbbSpxgDp);
        sqliteDp.setRq(lastdayOfMonth.substring(0, 7));
        sqliteDp.setDpmc(BiThreadLocals.getMsgBean().getDpmc());
        super.firstTimeJysj(lastdayOfMonth, sqliteDp);
    }

}
