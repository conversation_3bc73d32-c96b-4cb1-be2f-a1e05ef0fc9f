package com.abujlb.dataprobi.processes.sum;

import com.abujlb.SpringBeanUtil;
import com.abujlb.dataprobi.annotations.BiSumPro;
import com.abujlb.dataprobi.bean.BiYhdp;
import com.abujlb.dataprobi.bean.DataprobiBaseMsgBean;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.threads.BiThreadLocals;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/3/28 10:14
 */
@Component
public class BiSumStrategy {

    private final Map<Qd, List<BiSum>> map = new HashMap<>();

    private final Lock lock = new ReentrantLock();

    public void sumFirst() {
        BiYhdp biYhdp = BiThreadLocals.getBiYhdp();
        if (biYhdp == null) {
            return;
        }

        DataprobiBaseMsgBean msgBean = BiThreadLocals.getMsgBean();
        Qd qd = Qd.valueOf(msgBean.getQd());
        if (CollectionUtils.isEmpty(map.get(qd))) {
            init(qd);
        }
        List<BiSum> biSums = map.getOrDefault(qd, Collections.emptyList());
        for (BiSum biSum : biSums) {
            if (biSum instanceof FirstMonthSum) {
                biSum.sum(biYhdp, new ArrayList<>(BiThreadLocals.getCsAllDates()));
            }
        }
    }

    private void init(Qd qd) {
        try {
            lock.lock();

            Map<String, Object> biSumPros = SpringBeanUtil.getContext().getSpringContext().getBeansWithAnnotation(BiSumPro.class);

            List<BiSum> list = biSumPros.values()
                    .stream()
                    .filter(bean -> bean instanceof BiSum)
                    .filter(bean -> {
                        BiSumPro biSumPro = AnnotationUtils.findAnnotation(bean.getClass(), BiSumPro.class);
                        if (biSumPro == null) {
                            return false;
                        }
                        for (Qd temp : biSumPro.qd()) {
                            if (temp == qd) {
                                return true;
                            }
                        }
                        return false;
                    })
                    .sorted((a, b) -> {
                        BiSumPro biPro1 = AnnotationUtils.findAnnotation(a.getClass(), BiSumPro.class);
                        BiSumPro biPro2 = AnnotationUtils.findAnnotation(b.getClass(), BiSumPro.class);
                        return biPro1.order() - biPro2.order();
                    })
                    .map(obj -> (BiSum) obj)
                    .collect(Collectors.toList());
            map.put(qd, list);
        } finally {
            lock.unlock();
        }
    }
}
