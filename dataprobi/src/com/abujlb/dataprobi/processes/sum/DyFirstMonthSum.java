package com.abujlb.dataprobi.processes.sum;

import com.abujlb.dataprobi.annotations.BiSumPro;
import com.abujlb.dataprobi.bean.SqliteDp;
import com.abujlb.dataprobi.bean.dy.SqliteDySpxgDp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.threads.BiThreadLocals;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/3/27 15:32
 */
@Component
@BiSumPro(qd = Qd.DY, order = 5)
public class DyFirstMonthSum extends FirstMonthSum {

    private final String[] COLUMNS = {BiSycmDpDataTsDao.DY_COLUMN_SPXG, BiSycmDpDataTsDao.DY_COLUMN_SPXG2};

    @Override
    protected void getSqliteDp(String lastdayOfMonth) {
        SqliteDySpxgDp sqliteDySpxgDp = biUtil.dpObject(SqliteDySpxgDp.class, lastdayOfMonth + _MONTH, COLUMNS);
        if (Objects.isNull(sqliteDySpxgDp)) {
            return;
        }

        SqliteDp sqliteDp = new SqliteDp(sqliteDySpxgDp);
        sqliteDp.setDpmc(BiThreadLocals.getMsgBean().getDpmc());
        sqliteDp.setRq(lastdayOfMonth.substring(0, 7));
        super.firstTimeJysj(lastdayOfMonth, sqliteDp);
    }

}
