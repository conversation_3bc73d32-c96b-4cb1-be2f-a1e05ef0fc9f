package com.abujlb.dataprobi.processes.jd;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.jd.DataprobiJdMsgBean;
import com.abujlb.dataprobi.bean.jd.SqliteJdJdztDp;
import com.abujlb.dataprobi.bean.jd.SqliteJdJdztSp;
import com.abujlb.dataprobi.enums.Qd;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/6/29 10:05
 */
@Component
@BiPro(order = 8, qd = Qd.JD)
public class DefaultBiJdJdztProcess extends AbstractBiJdProcess {

    private final String newPrefixSp = "bi_jd/auth/authdata/jdzt/";
    private final String newPrefixDp = "bi_jd/auth/authdata/jdztdp/";

    @Override
    public void dealGoods() {
        super.dealGoodsJd(SqliteJdJdztSp.class, newPrefixSp, ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getJdzt());
    }

    @Override
    public void dealShop() {
        super.dealShopJd(SqliteJdJdztDp.class, newPrefixDp, ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getJdztdp());
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoodsJd(SqliteJdJdztSp.class, newPrefixSp, ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getJdzt());
    }

    @Override
    public boolean compareShop() {
        return super.compareShopJd(SqliteJdJdztDp.class, newPrefixDp, ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getJdztdp());
    }

    @Override
    protected <T extends AbstractSqliteSp> List<T> getAll(Class<T> mClass, String prefix, String rq) {
        return super.getAll(mClass, newPrefixSp, rq);
    }
}
