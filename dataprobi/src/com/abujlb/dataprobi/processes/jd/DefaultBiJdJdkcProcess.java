package com.abujlb.dataprobi.processes.jd;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.jd.DataprobiJdMsgBean;
import com.abujlb.dataprobi.bean.jd.SqliteJdJdkcDp;
import com.abujlb.dataprobi.bean.jd.SqliteJdJdkcSp;
import com.abujlb.dataprobi.enums.Qd;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/6/29 10:05
 */
@Component
@BiPro(order = 6, qd = Qd.JD)
public class DefaultBiJdJdkcProcess extends AbstractBiJdProcess {

    private final String newPrefixSp = "bi_jd/auth/authdata/jdkc/";
    private final String newPrefixDp = "bi_jd/auth/authdata/jdkcdp/";

    @Override
    public void dealGoods() {
        super.dealGoodsJd(SqliteJdJdkcSp.class, newPrefixSp, ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getJdkc());
    }

    @Override
    public void dealShop() {
        super.dealShopJd(SqliteJdJdkcDp.class, newPrefixDp, ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getJdkcdp());
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoodsJd(SqliteJdJdkcSp.class, newPrefixSp, ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getJdkc());
    }

    @Override
    public boolean compareShop() {
        return super.compareShopJd(SqliteJdJdkcDp.class, newPrefixDp, ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getJdkcdp());
    }

    @Override
    protected <T extends AbstractSqliteSp> List<T> getAll(Class<T> mClass, String prefix, String rq) {
        return super.getAll(mClass, newPrefixSp, rq);
    }
}
