package com.abujlb.dataprobi.processes.jd;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.jd.DataprobiJdMsgBean;
import com.abujlb.dataprobi.bean.jd.SqliteJdGwcdDp;
import com.abujlb.dataprobi.bean.jd.SqliteJdGwcdSp;
import com.abujlb.dataprobi.enums.Qd;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/6/29 10:05
 */
@Component
@BiPro(order = 4, qd = Qd.JD)
public class DefaultBiJdGwcdProcess extends AbstractBiJdProcess {

    private final String newPrefixSp = "bi_jd/auth/authdata/gwcd/";
    private final String newPrefixDp = "bi_jd/auth/authdata/gwcddp/";

    @Override
    public void dealGoods() {
        super.dealGoodsJd(SqliteJdGwcdSp.class, newPrefixSp, ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getGwcd());
    }

    @Override
    public void dealShop() {
        super.dealShopJd(SqliteJdGwcdDp.class, newPrefixDp, ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getGwcddp());
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoodsJd(SqliteJdGwcdSp.class, newPrefixSp, ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getGwcd());
    }

    @Override
    public boolean compareShop() {
        return super.compareShopJd(SqliteJdGwcdDp.class, newPrefixDp, ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getGwcddp());
    }

    @Override
    protected <T extends AbstractSqliteSp> List<T> getAll(Class<T> mClass, String prefix, String rq) {
        return super.getAll(mClass, newPrefixSp, rq);
    }
}
