package com.abujlb.dataprobi.processes.jd;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.jd.*;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.util.MathUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * https://www.tapd.cn/47708728/prong/stories/view/1147708728001004994
 * <p>
 * <p>
 * 商品展现量 = 京东快车展现量+ 购物触点展现量 + 京东直投(站外广告)展现量 + 京东展位(互动广告)展现量 +  竞速推(智能投放)展现量
 * 商品点击数 = 京东快车点击数+ 购物触点点击数 + 京东直投(站外广告)点击数 + 京东展位(互动广告)点击数 +  竞速推(智能投放)点击数
 * 商品点击率 = 商品点击数 /商品展现量
 * <p>
 * 店铺展现量 = 商品展现量汇总
 * 店铺点击数 = 商品点击数汇总
 * 店铺点击率 = 店铺点击数/店铺展现量
 *
 * <AUTHOR>
 * @date 2024/4/18
 */
@Component
@BiPro(order = 20, qd = Qd.JD)
public class DefaultBiJdJztDjlProcess extends AbstractBiJdProcess {

    @Autowired
    private DefaultBiJdJdzwProcess defaultBiJdJdzwProcess;
    @Autowired
    private DefaultBiJdJdkcProcess defaultBiJdJdkcProcess;
    @Autowired
    private DefaultBiJdGwcdProcess defaultBiJdGwcdProcess;
    @Autowired
    private DefaultBiJdJdztProcess defaultBiJdJdztProcess;
    @Autowired
    private DefaultBiJdJstProcess defaultBiJdJstProcess;

    @Override
    public void dealGoods() {
        List<String> rqList = new ArrayList<>();
        List<String> jdzt = ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getJdzt();
        List<String> jdzw = ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getJdzw();
        List<String> jdkc = ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getJdkc();
        List<String> gwcd = ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getGwcd();
        List<String> jst = ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getJdht();
        rqList.addAll(jdzt);
        rqList.addAll(jdzw);
        rqList.addAll(jdkc);
        rqList.addAll(gwcd);
        rqList.addAll(jst);
        rqList = rqList.stream().distinct().collect(Collectors.toList());
        super.dealGoodsJd(SqliteJdJztDjlSp.class, null, rqList);
    }

    @Override
    public boolean compareGoods() {
        List<String> rqList = new ArrayList<>();
        List<String> jdzt = ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getJdzt();
        List<String> jdzw = ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getJdzw();
        List<String> jdkc = ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getJdkc();
        List<String> gwcd = ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getGwcd();
        List<String> jst = ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getJdht();
        rqList.addAll(jdzt);
        rqList.addAll(jdzw);
        rqList.addAll(jdkc);
        rqList.addAll(gwcd);
        rqList.addAll(jst);
        rqList = rqList.stream().distinct().collect(Collectors.toList());
        return super.compareGoodsJd(SqliteJdJztDjlSp.class, null, rqList);
    }

    @Override
    protected <T extends AbstractSqliteSp> void dealShopByGoods(String rq, List<T> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        SqliteJdJztDjlDp sqliteJdJztDjlDp = new SqliteJdJztDjlDp();
        sqliteJdJztDjlDp.setQd_userid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid());
        sqliteJdJztDjlDp.setRq(rq);
        sqliteJdJztDjlDp.setDpmc(getDataprobiBaseMsg().getDpmc());
        sqliteJdJztDjlDp.setUserid(getDataprobiBaseMsg().getUserid());
        sqliteJdJztDjlDp.setYhid(getDataprobiBaseMsg().getYhid());
        sqliteJdJztDjlDp.setQd(getDataprobiBaseMsg().getQd());

        for (T t : list) {
            if (t instanceof SqliteJdJztDjlSp) {
                sqliteJdJztDjlDp.setSpdjl(sqliteJdJztDjlDp.getSpdjl() + ((SqliteJdJztDjlSp) t).getSpdjl());
                sqliteJdJztDjlDp.setSpzxl(sqliteJdJztDjlDp.getSpzxl() + ((SqliteJdJztDjlSp) t).getSpzxl());
            }
        }
        sqliteJdJztDjlDp.setSpdjlv(MathUtil.calZhlv(sqliteJdJztDjlDp.getSpdjl(), sqliteJdJztDjlDp.getSpzxl()));
        processSycmDpOTS(rq, sqliteJdJztDjlDp);
    }

    @Override
    protected <T extends AbstractSqliteSp> List<T> getAll(Class<T> tClass, String prefix, String rq) {
        Map<String, SqliteJdJztDjlSp> map = new HashMap<>();
        jdkc(map, rq);
        jdzt(map, rq);
        gwcd(map, rq);
        jdzw(map, rq);
        jst(map, rq);
        if (map.isEmpty()) {
            return null;
        }
        for (SqliteJdJztDjlSp value : map.values()) {
            value.setSpdjlv(MathUtil.calZhlv(value.getSpdjl(), value.getSpzxl()));
        }
        List<T> list = new ArrayList<>();
        for (SqliteJdJztDjlSp value : map.values()) {
            try {
                if (value.getSpdjl() == 0 && value.getSpzxl() == 0) {
                    continue;
                }
                T t = tClass.newInstance();
                BeanUtils.copyProperties(value, t);
                list.add(t);
            } catch (Exception e) {
                logger.error(e.getMessage(), e);
            }
        }
        return list;
    }


    private void jdkc(Map<String, SqliteJdJztDjlSp> map, String rq) {
        List<SqliteJdJdkcSp> jdkcList = defaultBiJdJdkcProcess.getAll(SqliteJdJdkcSp.class, null, rq);
        for (SqliteJdJdkcSp sqliteJdJdkcSp : jdkcList) {
            if (map.containsKey(sqliteJdJdkcSp.getBbid())) {
                map.get(sqliteJdJdkcSp.getBbid()).plusDjl(sqliteJdJdkcSp.getJdkcdjl());
                map.get(sqliteJdJdkcSp.getBbid()).plusZxl(sqliteJdJdkcSp.getJdkczxl());
            } else {
                SqliteJdJztDjlSp sqliteJdJztDjlSp = new SqliteJdJztDjlSp();
                sqliteJdJztDjlSp.setQd_userid_bbid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid() + "_" + sqliteJdJdkcSp.getBbid());
                sqliteJdJztDjlSp.setRq(rq);
                sqliteJdJztDjlSp.setYhid(getDataprobiBaseMsg().getYhid());
                sqliteJdJztDjlSp.setQd(getDataprobiBaseMsg().getQd());
                sqliteJdJztDjlSp.setUserid(getDataprobiBaseMsg().getUserid());
                sqliteJdJztDjlSp.setSpdjl(sqliteJdJdkcSp.getJdkcdjl());
                sqliteJdJztDjlSp.setSpzxl(sqliteJdJdkcSp.getJdkczxl());
                sqliteJdJztDjlSp.setBbid(sqliteJdJdkcSp.getBbid());
                map.put(sqliteJdJdkcSp.getBbid(), sqliteJdJztDjlSp);
            }
        }
    }

    private void jdzt(Map<String, SqliteJdJztDjlSp> map, String rq) {
        List<SqliteJdJdztSp> jdztList = defaultBiJdJdztProcess.getAll(SqliteJdJdztSp.class, null, rq);
        for (SqliteJdJdztSp sqliteJdJdztSp : jdztList) {
            if (map.containsKey(sqliteJdJdztSp.getBbid())) {
                map.get(sqliteJdJdztSp.getBbid()).plusDjl(sqliteJdJdztSp.getJdztdjl());
                map.get(sqliteJdJdztSp.getBbid()).plusZxl(sqliteJdJdztSp.getJdztzxl());
            } else {
                SqliteJdJztDjlSp sqliteJdJztDjlSp = new SqliteJdJztDjlSp();
                sqliteJdJztDjlSp.setQd_userid_bbid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid() + "_" + sqliteJdJdztSp.getBbid());
                sqliteJdJztDjlSp.setRq(rq);
                sqliteJdJztDjlSp.setYhid(getDataprobiBaseMsg().getYhid());
                sqliteJdJztDjlSp.setQd(getDataprobiBaseMsg().getQd());
                sqliteJdJztDjlSp.setUserid(getDataprobiBaseMsg().getUserid());
                sqliteJdJztDjlSp.setSpdjl(sqliteJdJdztSp.getJdztdjl());
                sqliteJdJztDjlSp.setSpzxl(sqliteJdJdztSp.getJdztzxl());
                sqliteJdJztDjlSp.setBbid(sqliteJdJdztSp.getBbid());
                map.put(sqliteJdJdztSp.getBbid(), sqliteJdJztDjlSp);
            }
        }
    }

    private void gwcd(Map<String, SqliteJdJztDjlSp> map, String rq) {
        List<SqliteJdGwcdSp> gwcdList = defaultBiJdGwcdProcess.getAll(SqliteJdGwcdSp.class, null, rq);
        for (SqliteJdGwcdSp sqliteJdGwcdSp : gwcdList) {
            if (map.containsKey(sqliteJdGwcdSp.getBbid())) {
                map.get(sqliteJdGwcdSp.getBbid()).plusDjl(sqliteJdGwcdSp.getGwcddjl());
                map.get(sqliteJdGwcdSp.getBbid()).plusZxl(sqliteJdGwcdSp.getGwcdzxl());
            } else {
                SqliteJdJztDjlSp sqliteJdJztDjlSp = new SqliteJdJztDjlSp();
                sqliteJdJztDjlSp.setQd_userid_bbid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid() + "_" + sqliteJdGwcdSp.getBbid());
                sqliteJdJztDjlSp.setRq(rq);
                sqliteJdJztDjlSp.setYhid(getDataprobiBaseMsg().getYhid());
                sqliteJdJztDjlSp.setQd(getDataprobiBaseMsg().getQd());
                sqliteJdJztDjlSp.setUserid(getDataprobiBaseMsg().getUserid());
                sqliteJdJztDjlSp.setSpdjl(sqliteJdGwcdSp.getGwcddjl());
                sqliteJdJztDjlSp.setSpzxl(sqliteJdGwcdSp.getGwcdzxl());
                sqliteJdJztDjlSp.setBbid(sqliteJdGwcdSp.getBbid());
                map.put(sqliteJdGwcdSp.getBbid(), sqliteJdJztDjlSp);
            }
        }
    }

    private void jdzw(Map<String, SqliteJdJztDjlSp> map, String rq) {
        List<SqliteJdJdzwSp> jdzwList = defaultBiJdJdzwProcess.getAll(SqliteJdJdzwSp.class, null, rq);
        for (SqliteJdJdzwSp sqliteJdJdzwSp : jdzwList) {
            if (map.containsKey(sqliteJdJdzwSp.getBbid())) {
                map.get(sqliteJdJdzwSp.getBbid()).plusDjl(sqliteJdJdzwSp.getJdzwdjl());
                map.get(sqliteJdJdzwSp.getBbid()).plusZxl(sqliteJdJdzwSp.getJdzwzxl());
            } else {
                SqliteJdJztDjlSp sqliteJdJztDjlSp = new SqliteJdJztDjlSp();
                sqliteJdJztDjlSp.setQd_userid_bbid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid() + "_" + sqliteJdJdzwSp.getBbid());
                sqliteJdJztDjlSp.setRq(rq);
                sqliteJdJztDjlSp.setYhid(getDataprobiBaseMsg().getYhid());
                sqliteJdJztDjlSp.setQd(getDataprobiBaseMsg().getQd());
                sqliteJdJztDjlSp.setUserid(getDataprobiBaseMsg().getUserid());
                sqliteJdJztDjlSp.setSpdjl(sqliteJdJdzwSp.getJdzwdjl());
                sqliteJdJztDjlSp.setSpzxl(sqliteJdJdzwSp.getJdzwzxl());
                sqliteJdJztDjlSp.setBbid(sqliteJdJdzwSp.getBbid());
                map.put(sqliteJdJdzwSp.getBbid(), sqliteJdJztDjlSp);
            }
        }
    }

    private void jst(Map<String, SqliteJdJztDjlSp> map, String rq) {
        List<SqliteJdJstSp> jstList = defaultBiJdJstProcess.getAll(SqliteJdJstSp.class, null, rq);
        for (SqliteJdJstSp sqliteJdJstSp : jstList) {
            if (map.containsKey(sqliteJdJstSp.getBbid())) {
                map.get(sqliteJdJstSp.getBbid()).plusDjl(sqliteJdJstSp.getJstdjl());
                map.get(sqliteJdJstSp.getBbid()).plusZxl(sqliteJdJstSp.getJstzxl());
            } else {
                SqliteJdJztDjlSp sqliteJdJztDjlSp = new SqliteJdJztDjlSp();
                sqliteJdJztDjlSp.setQd_userid_bbid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid() + "_" + sqliteJdJstSp.getBbid());
                sqliteJdJztDjlSp.setRq(rq);
                sqliteJdJztDjlSp.setYhid(getDataprobiBaseMsg().getYhid());
                sqliteJdJztDjlSp.setQd(getDataprobiBaseMsg().getQd());
                sqliteJdJztDjlSp.setUserid(getDataprobiBaseMsg().getUserid());
                sqliteJdJztDjlSp.setSpdjl(sqliteJdJstSp.getJstdjl());
                sqliteJdJztDjlSp.setSpzxl(sqliteJdJstSp.getJstzxl());
                sqliteJdJztDjlSp.setBbid(sqliteJdJstSp.getBbid());
                map.put(sqliteJdJstSp.getBbid(), sqliteJdJztDjlSp);
            }
        }
    }
}
