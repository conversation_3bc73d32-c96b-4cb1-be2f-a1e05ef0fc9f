package com.abujlb.dataprobi.processes.jd;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.jd.DataprobiJdMsgBean;
import com.abujlb.dataprobi.bean.jd.SqliteJdCgjDp;
import com.abujlb.dataprobi.bean.jd.SqliteJdCgjSp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.threads.BiThreadLocals;
import com.abujlb.dataprobi.util.MathUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/23
 */
@Component
@BiPro(order = 10, qd = Qd.JD)
public class DefaultBiJdCgjProcess extends AbstractBiJdProcess {

    private static final String OSS_PATTERN = "bi_jd/jdsz/%s/%s/fbp_spyj.json";

    @Override
    public void dealGoods() {
        for (String rq : ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getFbpspyj()) {
            sqliteDbUtil.clearSpData(BiThreadLocals.getBiYhdp(), rq, SqliteJdCgjSp.class);
            sqliteDbUtil.clearDpData(BiThreadLocals.getBiYhdp(), rq, SqliteJdCgjDp.class);
        }

        super.dealGoods(
                SqliteJdCgjSp.class,
                OSS_PATTERN,
                ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getFbpspyj()
        );
    }

    @Override
    public boolean compareGoods() {
        for (String rq : ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getFbpspyj()) {
            sqliteDbUtil.clearSpData(BiThreadLocals.getBiYhdp(), rq, SqliteJdCgjSp.class);
            sqliteDbUtil.clearDpData(BiThreadLocals.getBiYhdp(), rq, SqliteJdCgjDp.class);
        }

        return super.compareGoods(
                SqliteJdCgjSp.class,
                OSS_PATTERN,
                ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getFbpspyj()
        );
    }

    @Override
    protected <T extends AbstractSqliteSp> void dealShopByGoods(String rq, List<T> list) {
        dealShop(rq, list);
    }

    private <T extends AbstractSqliteSp> void dealShop(String rq, List<T> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        dealShopByGoodsDay(rq, list);
    }

    private <T extends AbstractSqliteSp> void dealShopByGoodsDay(String rq, List<T> list) {
        double jdzycgj = 0;
        double jdzysfje = 0;

        for (T t : list) {
            if (t instanceof SqliteJdCgjSp) {
                jdzycgj = MathUtil.add(jdzycgj, ((SqliteJdCgjSp) t).getJdzycgj());
                jdzysfje = MathUtil.add(jdzysfje, ((SqliteJdCgjSp) t).getJdzysfje());
            }
        }

        SqliteJdCgjDp sqliteJdCgjDp = new SqliteJdCgjDp(getDataprobiBaseMsg(), rq);
        sqliteJdCgjDp.setJdzycgj(jdzycgj);
        sqliteJdCgjDp.setJdzysfje(jdzysfje);

        processSycmDpOTS(rq, sqliteJdCgjDp);
    }
}
