package com.abujlb.dataprobi.processes.jd;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.jd.DataprobiJdMsgBean;
import com.abujlb.dataprobi.bean.jd.SqliteJdJtkDp;
import com.abujlb.dataprobi.bean.jd.SqliteJdJtkSp;
import com.abujlb.dataprobi.enums.Qd;
import org.springframework.stereotype.Component;

/**
 * 预估京挑客花费
 *
 * <AUTHOR>
 * @date 2023-7-12
 */
@Component
@BiPro(order = 14, qd = Qd.JD)
public class DefaultBiJdJtkygProcess extends AbstractBiJdProcess {

    private final String newPrefixSp = "bi_jd/auth/authdata/jtkxdddmx2/";
    private final String newPrefixDp = "bi_jd/auth/authdata/jtkdp/";

    @Override
    public void dealGoods() {
        for (String rq : ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getJtk()) {
            sqliteDbUtil.clearSpData(null, rq, SqliteJdJtkSp.class);
        }

        super.dealGoodsJd(SqliteJdJtkSp.class, newPrefixSp, ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getJtk());
    }

    @Override
    public void dealShop() {
        for (String rq : ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getJtkdp()) {
            sqliteDbUtil.clearDpData(null, rq, SqliteJdJtkDp.class);
        }

        super.dealShopJd(SqliteJdJtkDp.class, newPrefixDp, ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getJtkdp());
    }

    @Override
    public boolean compareGoods() {
        for (String rq : ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getJtk()) {
            sqliteDbUtil.clearSpData(null, rq, SqliteJdJtkSp.class);
        }
        return super.compareGoodsJd(SqliteJdJtkSp.class, newPrefixSp, ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getJtk());
    }

    @Override
    public boolean compareShop() {
        for (String rq : ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getJtkdp()) {
            sqliteDbUtil.clearDpData(null, rq, SqliteJdJtkDp.class);
        }
        return super.compareShopJd(SqliteJdJtkDp.class, newPrefixDp, ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getJtkdp());
    }
}
