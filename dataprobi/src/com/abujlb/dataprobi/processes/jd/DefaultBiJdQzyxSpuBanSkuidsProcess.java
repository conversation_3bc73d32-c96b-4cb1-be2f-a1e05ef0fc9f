package com.abujlb.dataprobi.processes.jd;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.jd.DataprobiJdMsgBean;
import com.abujlb.dataprobi.bean.jd.SqliteJdQzyxBanSkuidSp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.util.StringUtil;
import com.google.gson.Gson;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/09/13
 * 处理全站营销-SPU维度-禁用SKUID
 */
@Component
@BiPro(order = 15, qd = Qd.JD)
public class DefaultBiJdQzyxSpuBanSkuidsProcess extends AbstractBiJdProcess {

    private final String newPrefixSp = "bi_jd/auth/authdata/qzyx/%s/%s/qzyx_skuban.json";

    @Override
    public void dealGoods() {
        super.dealGoodsJd(SqliteJdQzyxBanSkuidSp.class, newPrefixSp, ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getQzyx_skuban(), true);
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoodsJd(SqliteJdQzyxBanSkuidSp.class, newPrefixSp, ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getQzyx_skuban(), true);
    }

    @Override
    protected <T extends AbstractSqliteSp> List<T> getAll(Class<T> mClass, String prefix, String rq) {
        String osskey = String.format(newPrefixSp, rq, getDataprobiBaseMsg().getUserid());
        return spList(mClass, osskey, rq);
    }

    @Override
    protected <T extends AbstractSqliteSp> List<T> spList(Class<T> tClass, String ossKey, String rq) {
        String json = biDataOssUtil.readJson(ossKey);
        if (!StringUtil.isJson2(json)) {
            return Collections.emptyList();
        }

        Gson gson = new Gson();
        Map<String, List<String>> map = gson.fromJson(json, Map.class);
        if (map.isEmpty()) {
            return Collections.emptyList();
        }

        List<SqliteJdQzyxBanSkuidSp> list = new ArrayList<>();
        for (Map.Entry<String, List<String>> entry : map.entrySet()) {
            List<String> value = entry.getValue();
            if (CollectionUtils.isEmpty(value)) {
                continue;
            }
            SqliteJdQzyxBanSkuidSp sqliteJdQzyxBanSkuidSp = new SqliteJdQzyxBanSkuidSp();
            sqliteJdQzyxBanSkuidSp.setBbid(entry.getKey());
            sqliteJdQzyxBanSkuidSp.setQd_userid_bbid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid() + "_" + entry.getKey());
            sqliteJdQzyxBanSkuidSp.setRq(rq);
            sqliteJdQzyxBanSkuidSp.setYhid(getDataprobiBaseMsg().getYhid());
            sqliteJdQzyxBanSkuidSp.setQd(getDataprobiBaseMsg().getQd());
            sqliteJdQzyxBanSkuidSp.setUserid(getDataprobiBaseMsg().getUserid());
            sqliteJdQzyxBanSkuidSp.setTcskuids(String.join(",", value));
            list.add(sqliteJdQzyxBanSkuidSp);
        }
        return (List<T>) list;
    }
}
