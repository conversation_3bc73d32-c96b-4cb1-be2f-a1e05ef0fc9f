package com.abujlb.dataprobi.processes.jd;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteDp;
import com.abujlb.dataprobi.bean.jd.*;
import com.abujlb.dataprobi.bean.jd.xfjl.JdPlugXfjlXjVO;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.util.FileUtil;
import com.abujlb.dataprobi.util.JdPlugXfjlXlsReader;
import com.abujlb.dataprobi.util.MathUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.*;

/**
 * 京准通-B端营销-消费记录-红包
 *
 * <AUTHOR>
 * @date 2025/1/8
 */
@Component
@BiPro(order = 10, qd = Qd.JD)
public class DefaultBiJdBXfjlProcess extends AbstractBiJdProcess {

    private final String OSS_PATTERN = "bi_jd/auth/authdata/bcwjl/%s/%s/xfjl_hb.xls";

    private final List<String> JDKC = Arrays.asList("快车扣费", "快车扣费-展示服务", "搜索快车扣费", "搜索快车扣费-展示服务");
    private final List<String> JDZT = Arrays.asList("站外广告-秒杀推广扣费", "站外广告-腾讯媒体扣费", "站外广告-定向计划扣", "站外广告-头条扣费", "站外广告-腾讯扣费", "站外广告-京东标签/百度标签计划扣费", "站外广告扣费", "站外广告-定向计划扣费");
    private final List<String> GWCD = Arrays.asList("推荐广告扣费—购物触点", "推荐广告扣费—购物触点展示服务");
    private final List<String> JST = Arrays.asList("智能投放全店推广扣费-精准定向", "智能投放商品扣费-精准定向", "智能投放直播扣费-精准定向", "智能投放场景扣费-精准定向", "智能投放全店推广扣费-展示服务", "智能投放商品扣费-展示服务", "智能投放直播扣费-展示服务", "智能投放场景扣费-展示服务");


    @Autowired
    private DefaultBiJdBJdkcProcess defaultBiJdBJdkcProcess;

    @Autowired
    private DefaultBiJdBGwcdProcess defaultBiJdBGwcdProcess;

    @Autowired
    private DefaultBiJdBJstProcess defaultBiJdBJstProcess;

    @Autowired
    private DefaultBiJdBJdztProcess defaultBiJdBJdztProcess;


    @Override
    public void dealShop() {
        super.dealShopJd(SqliteJdBXfjlDp.class, null, ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getBxfjl());
    }


    @Override
    public boolean compareShop() {
        return super.compareShopJd(SqliteJdBXfjlDp.class, null, ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getBxfjl());
    }

    @Override
    protected <T extends AbstractSqliteDp> T getAllDp(Class<T> tClass, String prefix, String rq) {
        SqliteJdBXfjlDp sqliteJdBXfjlDp = xfjlhb(rq);
        if (sqliteJdBXfjlDp == null) {
            return null;
        }

        dealGoodsByShare(sqliteJdBXfjlDp, rq);

        return (T) sqliteJdBXfjlDp;
    }

    /**
     * 处理消费记录
     *
     * @param rq
     */
    private SqliteJdBXfjlDp xfjlhb(String rq) {
        String hb_osskey = String.format(OSS_PATTERN, rq, getDataprobiBaseMsg().getUserid());
        if (!biDataOssUtil.exist(hb_osskey)) {
            return null;
        }

        String hb_temppath = FileUtil.createXlsFilePath();
        biDataOssUtil.download(hb_osskey, hb_temppath);

        File hb_file = new File(hb_temppath);

        List<JdPlugXfjlXjVO> hb_xfjlList = JdPlugXfjlXlsReader.readXfjlHb(hb_file);
        if (CollectionUtils.isEmpty(hb_xfjlList)) {
            return null;
        }
        List<JdPlugXfjlXjVO> xfjlList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(hb_xfjlList)) {
            xfjlList.addAll(hb_xfjlList);
        }

        SqliteJdBXfjlDp sqliteJdBXfjlDp = new SqliteJdBXfjlDp();
        sqliteJdBXfjlDp.setQd_userid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid());
        sqliteJdBXfjlDp.setRq(rq);
        sqliteJdBXfjlDp.setDpmc(getDataprobiBaseMsg().getDpmc());
        sqliteJdBXfjlDp.setUserid(getDataprobiBaseMsg().getUserid());
        sqliteJdBXfjlDp.setYhid(getDataprobiBaseMsg().getYhid());
        sqliteJdBXfjlDp.setQd(getDataprobiBaseMsg().getQd());

        for (JdPlugXfjlXjVO jdPlugXfjlXjVO : xfjlList) {
            if (!rq.equals(jdPlugXfjlXjVO.getTgrq())) {
                continue;
            }
            if (JDKC.contains(jdPlugXfjlXjVO.getJylx())) {
                sqliteJdBXfjlDp.setBjdkchb(MathUtil.add(sqliteJdBXfjlDp.getBjdkchb(), jdPlugXfjlXjVO.getZc()));
            } else if (JDZT.contains(jdPlugXfjlXjVO.getJylx())) {
                sqliteJdBXfjlDp.setBjdzthb(MathUtil.add(sqliteJdBXfjlDp.getBjdzthb(), jdPlugXfjlXjVO.getZc()));
            } else if (GWCD.contains(jdPlugXfjlXjVO.getJylx())) {
                sqliteJdBXfjlDp.setBgwcdhb(MathUtil.add(sqliteJdBXfjlDp.getBgwcdhb(), jdPlugXfjlXjVO.getZc()));
            } else if (JST.contains(jdPlugXfjlXjVO.getJylx())) {
                sqliteJdBXfjlDp.setBjsthb(MathUtil.add(sqliteJdBXfjlDp.getBjsthb(), jdPlugXfjlXjVO.getZc()));
            }
        }
        return sqliteJdBXfjlDp;
    }


    private void dealGoodsByShare(SqliteJdBXfjlDp sqliteJdBXfjlDp, String rq) {
        //处理单品
        Map<String, SqliteJdBXfjlSp> map = new HashMap<>();
        if (sqliteJdBXfjlDp.getBjdkchb() > 0) {
            //京东快车单品
            processJdkc(map, sqliteJdBXfjlDp, rq);
            sqliteJdBXfjlDp.setBjdkchb(-sqliteJdBXfjlDp.getBjdkchb());
        }
        if (sqliteJdBXfjlDp.getBgwcdhb() > 0) {
            //购物触点单品
            processGwcd(map, sqliteJdBXfjlDp, rq);
            sqliteJdBXfjlDp.setBgwcdhb(-sqliteJdBXfjlDp.getBgwcdhb());
        }
        if (sqliteJdBXfjlDp.getBjsthb() > 0) {
            //京速推单品
            processJst(map, sqliteJdBXfjlDp, rq);
            sqliteJdBXfjlDp.setBjsthb(-sqliteJdBXfjlDp.getBjsthb());
        }
        if (sqliteJdBXfjlDp.getBjdzthb() > 0) {
            //京东直投单品
            processJdzt(map, sqliteJdBXfjlDp, rq);
            sqliteJdBXfjlDp.setBjdzthb(-sqliteJdBXfjlDp.getBjdzthb());
        }

        if (!map.isEmpty()) {
            ArrayList<SqliteJdBXfjlSp> sqliteJdBXfjlSps = new ArrayList<>(map.values());
            for (SqliteJdBXfjlSp sqliteJdBXfjlSp : sqliteJdBXfjlSps) {
                sqliteJdBXfjlSp.setBjdkchb(-sqliteJdBXfjlSp.getBjdkchb());
                sqliteJdBXfjlSp.setBgwcdhb(-sqliteJdBXfjlSp.getBgwcdhb());
                sqliteJdBXfjlSp.setBjsthb(-sqliteJdBXfjlSp.getBjsthb());
                sqliteJdBXfjlSp.setBjdzthb(-sqliteJdBXfjlSp.getBjdzthb());
            }
            processSycmSpOTS(rq, sqliteJdBXfjlSps);
        }
    }

    private void processJdzt(Map<String, SqliteJdBXfjlSp> map, SqliteJdBXfjlDp sqliteJdBXfjlDp, String rq) {
        List<SqliteJdBJdztSp> jdztList = defaultBiJdBJdztProcess.getAll(SqliteJdBJdztSp.class, null, rq);
        double total = 0;
        for (SqliteJdBJdztSp sqliteJdBJdztSp : jdztList) {
            total = MathUtil.add(total, sqliteJdBJdztSp.getBjdzthf());
        }
        for (SqliteJdBJdztSp sqliteJdBJdztSp : jdztList) {
            //单品所占总花费的比例
            double ratio = MathUtil.divide(sqliteJdBJdztSp.getBjdzthf(), total, 8);
            //单品所占花费比例 对应的 红包金额
            double hb = MathUtil.multipy(sqliteJdBXfjlDp.getBjdzthb(), ratio, 4);
            SqliteJdBXfjlSp sqliteJdBXfjlSp = map.getOrDefault(sqliteJdBJdztSp.getBbid(), new SqliteJdBXfjlSp());
            //set一些值
            sqliteJdBXfjlSp.setQd(sqliteJdBJdztSp.getQd());
            sqliteJdBXfjlSp.setQd_userid_bbid(sqliteJdBJdztSp.getQd_userid_bbid());
            sqliteJdBXfjlSp.setBbid(sqliteJdBJdztSp.getBbid());
            sqliteJdBXfjlSp.setRq(rq);
            sqliteJdBXfjlSp.setYhid(sqliteJdBJdztSp.getYhid());
            sqliteJdBXfjlSp.setUserid(sqliteJdBJdztSp.getUserid());
            sqliteJdBXfjlSp.setBjdzthb(MathUtil.add(hb, sqliteJdBXfjlSp.getBjdzthb()));
            map.put(sqliteJdBJdztSp.getBbid(), sqliteJdBXfjlSp);
        }
    }

    private void processJst(Map<String, SqliteJdBXfjlSp> map, SqliteJdBXfjlDp sqliteJdBXfjlDp, String rq) {
        List<SqliteJdBJstSp> jstList = defaultBiJdBJstProcess.getAll(SqliteJdBJstSp.class, null, rq);
        double total = 0;
        for (SqliteJdBJstSp sqliteJdBJstSp : jstList) {
            total = MathUtil.add(total, sqliteJdBJstSp.getBjsthf());
        }
        for (SqliteJdBJstSp sqliteJdBJstSp : jstList) {
            double ratio = MathUtil.divide(sqliteJdBJstSp.getBjsthf(), total, 8);
            double hb = MathUtil.multipy(sqliteJdBXfjlDp.getBjsthb(), ratio, 4);
            SqliteJdBXfjlSp sqliteJdBXfjlSp = map.getOrDefault(sqliteJdBJstSp.getBbid(), new SqliteJdBXfjlSp());
            //set一些值
            sqliteJdBXfjlSp.setQd(sqliteJdBJstSp.getQd());
            sqliteJdBXfjlSp.setQd_userid_bbid(sqliteJdBJstSp.getQd_userid_bbid());
            sqliteJdBXfjlSp.setBbid(sqliteJdBJstSp.getBbid());
            sqliteJdBXfjlSp.setRq(rq);
            sqliteJdBXfjlSp.setYhid(sqliteJdBJstSp.getYhid());
            sqliteJdBXfjlSp.setUserid(sqliteJdBJstSp.getUserid());
            sqliteJdBXfjlSp.setBjsthb(MathUtil.add(hb, sqliteJdBXfjlSp.getBjsthb()));
            map.put(sqliteJdBJstSp.getBbid(), sqliteJdBXfjlSp);
        }
    }

    private void processGwcd(Map<String, SqliteJdBXfjlSp> map, SqliteJdBXfjlDp sqliteJdBXfjlDp, String rq) {
        List<SqliteJdBGwcdSp> gwcdList = defaultBiJdBGwcdProcess.getAll(SqliteJdBGwcdSp.class, null, rq);
        double total = 0;
        for (SqliteJdBGwcdSp sqliteJdBGwcdSp : gwcdList) {
            total = MathUtil.add(total, sqliteJdBGwcdSp.getBgwcdhf());
        }
        for (SqliteJdBGwcdSp sqliteJdBGwcdSp : gwcdList) {
            double ratio = MathUtil.divide(sqliteJdBGwcdSp.getBgwcdhf(), total, 8);
            double hb = MathUtil.multipy(sqliteJdBXfjlDp.getBgwcdhb(), ratio, 4);
            SqliteJdBXfjlSp sqliteJdBXfjlSp = map.getOrDefault(sqliteJdBGwcdSp.getBbid(), new SqliteJdBXfjlSp());
            //set一些值
            sqliteJdBXfjlSp.setQd(sqliteJdBGwcdSp.getQd());
            sqliteJdBXfjlSp.setQd_userid_bbid(sqliteJdBGwcdSp.getQd_userid_bbid());
            sqliteJdBXfjlSp.setBbid(sqliteJdBGwcdSp.getBbid());
            sqliteJdBXfjlSp.setRq(rq);
            sqliteJdBXfjlSp.setYhid(sqliteJdBGwcdSp.getYhid());
            sqliteJdBXfjlSp.setUserid(sqliteJdBGwcdSp.getUserid());
            sqliteJdBXfjlSp.setBgwcdhb(MathUtil.add(hb, sqliteJdBXfjlSp.getBgwcdhb()));
            map.put(sqliteJdBGwcdSp.getBbid(), sqliteJdBXfjlSp);
        }
    }

    private void processJdkc(Map<String, SqliteJdBXfjlSp> map, SqliteJdBXfjlDp sqliteJdBXfjlDp, String rq) {
        List<SqliteJdBJdkcSp> jdkcList = defaultBiJdBJdkcProcess.getAll(SqliteJdBJdkcSp.class, null, rq);
        double total = 0;
        for (SqliteJdBJdkcSp sqliteJdBJdkcSp : jdkcList) {
            total = MathUtil.add(total, sqliteJdBJdkcSp.getBjdkchf());
        }
        for (SqliteJdBJdkcSp sqliteJdBJdkcSp : jdkcList) {
            double ratio = MathUtil.divide(sqliteJdBJdkcSp.getBjdkchf(), total, 8);
            double hb = MathUtil.multipy(sqliteJdBXfjlDp.getBjdkchb(), ratio, 4);
            SqliteJdBXfjlSp sqliteJdBXfjlSp = map.getOrDefault(sqliteJdBJdkcSp.getBbid(), new SqliteJdBXfjlSp());
            //set一些值
            sqliteJdBXfjlSp.setQd(sqliteJdBJdkcSp.getQd());
            sqliteJdBXfjlSp.setQd_userid_bbid(sqliteJdBJdkcSp.getQd_userid_bbid());
            sqliteJdBXfjlSp.setBbid(sqliteJdBJdkcSp.getBbid());
            sqliteJdBXfjlSp.setRq(rq);
            sqliteJdBXfjlSp.setYhid(sqliteJdBJdkcSp.getYhid());
            sqliteJdBXfjlSp.setUserid(sqliteJdBJdkcSp.getUserid());
            sqliteJdBXfjlSp.setBjdkchb(MathUtil.add(hb, sqliteJdBXfjlSp.getBjdkchb()));
            map.put(sqliteJdBJdkcSp.getBbid(), sqliteJdBXfjlSp);
        }
    }

}
