package com.abujlb.dataprobi.processes.jd;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.jd.DataprobiJdMsgBean;
import com.abujlb.dataprobi.bean.jd.SqliteJdFcsDp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/6/29 10:05
 */
@Component
@BiPro(order = 14, qd = Qd.JD)
public class DefaultBiJdFcsProcess extends AbstractBiJdProcess {

    private final String[] COLUMNS = {
            BiSycmDpDataTsDao.JD_COLUMN_FCS
    };

    @Override
    public void dealShop() {
        super.dealShop(
                SqliteJdFcsDp.class,
                ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getFcs(),
                COLUMNS
        );
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqliteJdFcsDp.class,
                ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getFcs(),
                COLUMNS
        );
    }
}
