package com.abujlb.dataprobi.processes.jd;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.jd.DataprobiJdMsgBean;
import com.abujlb.dataprobi.bean.jd.SqliteJdJstDp;
import com.abujlb.dataprobi.bean.jd.SqliteJdJstSp;
import com.abujlb.dataprobi.enums.Qd;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/6/29 10:05
 */
@Component
@BiPro(order = 12, qd = Qd.JD)
public class DefaultBiJdJstProcess extends AbstractBiJdProcess {

    private final String newPrefixSp = "bi_jd/auth/authdata/jdht/";
    private final String newPrefixDp = "bi_jd/auth/authdata/jdhtdp/";

    @Override
    public void dealGoods() {
        super.dealGoodsJd(SqliteJdJstSp.class, newPrefixSp, ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getJdht());
    }

    @Override
    public void dealShop() {
        super.dealShopJd(SqliteJdJstDp.class, newPrefixDp, ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getJdhtdp());
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoodsJd(SqliteJdJstSp.class, newPrefixSp, ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getJdht());
    }

    @Override
    public boolean compareShop() {
        return super.compareShopJd(SqliteJdJstDp.class, newPrefixDp, ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getJdhtdp());
    }

    @Override
    protected <T extends AbstractSqliteSp> List<T> getAll(Class<T> mClass, String prefix, String rq) {
        return super.getAll(mClass, newPrefixSp, rq);
    }
}
