package com.abujlb.dataprobi.processes.jd;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.jd.DataprobiJdMsgBean;
import com.abujlb.dataprobi.bean.jd.SqliteJdBtDp;
import com.abujlb.dataprobi.bean.jd.SqliteJdBtSp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.util.MathUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/6/29 10:05
 */
@Component
@BiPro(order = 2, qd = Qd.JD)
public class DefaultBiJdBtProcess extends AbstractBiJdProcess {

    String ossKey = "bi_jd/jdsz/%s/%s/bt.json";

    @Override
    public void dealGoods() {
        super.dealGoods(
                SqliteJdBtSp.class,
                ossKey,
                ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getBt()
        );
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoods(
                SqliteJdBtSp.class,
                ossKey,
                ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getBt()
        );
    }

    @Override
    protected <T extends AbstractSqliteSp> void dealShopByGoods(String rq, List<T> list) {
        dealShop(rq, list);
    }

    private <T extends AbstractSqliteSp> void dealShop(String rq, List<T> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        double hf = 0;
        for (T t : list) {
            if (t instanceof SqliteJdBtSp) {
                hf = MathUtil.add(hf, ((SqliteJdBtSp) t).getBthf());
            }
        }

        if (hf != 0) {
            SqliteJdBtDp sqliteJdBtDp = new SqliteJdBtDp(getDataprobiBaseMsg(), rq, hf);
            processSycmDpOTS(rq, sqliteJdBtDp);
        }
    }
}
