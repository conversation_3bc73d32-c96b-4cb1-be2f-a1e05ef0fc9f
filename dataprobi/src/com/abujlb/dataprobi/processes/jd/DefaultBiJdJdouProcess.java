package com.abujlb.dataprobi.processes.jd;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.jd.DataprobiJdMsgBean;
import com.abujlb.dataprobi.bean.jd.SqliteJdJdouDp;
import com.abujlb.dataprobi.bean.jd.SqliteJdJdouSp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.util.MathUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/6/29 10:05
 */
@Component
@BiPro(order = 7, qd = Qd.JD)
public class DefaultBiJdJdouProcess extends AbstractBiJdProcess {

    private final String ossKey = "bi_jd/jdsz/%s/%s/jdou.json";

    @Override
    public void dealGoods() {
        super.dealGoods(
                SqliteJdJdouSp.class,
                ossKey,
                ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getJdou()
        );
    }

    @Override
    protected <T extends AbstractSqliteSp> boolean compareGoods(Class<T> tClass, String ossKeyFormat, List<String> rqList) {
        return super.compareGoods(
                SqliteJdJdouSp.class,
                ossKey,
                ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getJdou()
        );
    }

    @Override
    protected <T extends AbstractSqliteSp> void dealShopByGoods(String rq, List<T> list) {
        dealShop(rq, list);
    }

    private <T extends AbstractSqliteSp> void dealShop(String rq, List<T> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        double hf = 0;
        for (T t : list) {
            if (t instanceof SqliteJdJdouSp) {
                hf = MathUtil.add(hf, ((SqliteJdJdouSp) t).getJdouhf());
            }
        }

        if (hf != 0) {
            SqliteJdJdouDp sqliteJdJdouDp = new SqliteJdJdouDp(getDataprobiBaseMsg(), rq, hf);
            processSycmDpOTS(rq, sqliteJdJdouDp);
        }
    }
}
