package com.abujlb.dataprobi.processes.jd;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.jd.DataprobiJdMsgBean;
import com.abujlb.dataprobi.bean.jd.SqliteJdBJdztDp;
import com.abujlb.dataprobi.bean.jd.SqliteJdBJdztSp;
import com.abujlb.dataprobi.enums.Qd;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/8
 */
@Component
@BiPro(order = 8, qd = Qd.JD)
public class DefaultBiJdBJdztProcess extends AbstractBiJdProcess {

    private final String newPrefixSp = "bi_jd/auth/authdata/bjdzt/";
    private final String newPrefixDp = "bi_jd/auth/authdata/bjdztdp/";

    @Override
    public void dealGoods() {
        super.dealGoodsJd(SqliteJdBJdztSp.class, newPrefixSp, ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getBjdzt());
    }

    @Override
    public void dealShop() {
        super.dealShopJd(SqliteJdBJdztDp.class, newPrefixDp, ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getBjdztdp());
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoodsJd(SqliteJdBJdztSp.class, newPrefixSp, ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getBjdzt());
    }

    @Override
    public boolean compareShop() {
        return super.compareShopJd(SqliteJdBJdztDp.class, newPrefixDp, ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getBjdztdp());
    }

    @Override
    protected <T extends AbstractSqliteSp> List<T> getAll(Class<T> mClass, String prefix, String rq) {
        return super.getAll(mClass, newPrefixSp, rq);
    }
}
