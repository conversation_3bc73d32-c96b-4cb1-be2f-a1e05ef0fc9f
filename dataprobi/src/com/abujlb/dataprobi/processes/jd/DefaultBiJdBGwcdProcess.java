package com.abujlb.dataprobi.processes.jd;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.jd.DataprobiJdMsgBean;
import com.abujlb.dataprobi.bean.jd.SqliteJdBGwcdDp;
import com.abujlb.dataprobi.bean.jd.SqliteJdBGwcdSp;
import com.abujlb.dataprobi.enums.Qd;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/8
 */
@Component
@BiPro(order = 4, qd = Qd.JD)
public class DefaultBiJdBGwcdProcess extends AbstractBiJdProcess {

    private final String newPrefixSp = "bi_jd/auth/authdata/bgwcd/";
    private final String newPrefixDp = "bi_jd/auth/authdata/bgwcddp/";

    @Override
    public void dealGoods() {
        super.dealGoodsJd(SqliteJdBGwcdSp.class, newPrefixSp, ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getBgwcd());
    }

    @Override
    public void dealShop() {
        super.dealShopJd(SqliteJdBGwcdDp.class, newPrefixDp, ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getBgwcddp());
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoodsJd(SqliteJdBGwcdSp.class, newPrefixSp, ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getBgwcd());
    }

    @Override
    public boolean compareShop() {
        return super.compareShopJd(SqliteJdBGwcdDp.class, newPrefixDp, ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getBgwcddp());
    }

    @Override
    protected <T extends AbstractSqliteSp> List<T> getAll(Class<T> mClass, String prefix, String rq) {
        return super.getAll(mClass, newPrefixSp, rq);
    }
}
