package com.abujlb.dataprobi.processes.jd;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.jd.DataprobiJdMsgBean;
import com.abujlb.dataprobi.bean.jd.SqliteJdJdzwDp;
import com.abujlb.dataprobi.bean.jd.SqliteJdJdzwSp;
import com.abujlb.dataprobi.enums.Qd;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/6/29 10:05
 */
@Component
@BiPro(order = 9, qd = Qd.JD)
public class DefaultBiJdJdzwProcess extends AbstractBiJdProcess {

    private final String newPrefixSp = "bi_jd/auth/authdata/jdzw/";
    private final String newPrefixDp = "bi_jd/auth/authdata/jdzwdp/";

    @Override
    public void dealGoods() {
        super.dealGoodsJd(SqliteJdJdzwSp.class, newPrefixSp, ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getJdzw());
    }

    @Override
    public void dealShop() {
        super.dealShopJd(SqliteJdJdzwDp.class, newPrefixDp, ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getJdzwdp());
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoodsJd(SqliteJdJdzwSp.class, newPrefixSp, ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getJdzw());
    }

    @Override
    public boolean compareShop() {
        return super.compareShopJd(SqliteJdJdzwDp.class, newPrefixDp, ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getJdzwdp());
    }

    @Override
    protected <T extends AbstractSqliteSp> List<T> getAll(Class<T> mClass, String prefix, String rq) {
        return super.getAll(mClass, newPrefixSp, rq);
    }
}
