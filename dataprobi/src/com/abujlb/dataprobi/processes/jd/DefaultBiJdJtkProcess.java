package com.abujlb.dataprobi.processes.jd;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.DataprobiBaseMsgBean;
import com.abujlb.dataprobi.bean.jd.DataprobiJdMsgBean;
import com.abujlb.dataprobi.bean.jd.SqliteJdJtkZdDp;
import com.abujlb.dataprobi.bean.jd.SqliteJdJtkZdSp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.util.FileUtil;
import com.abujlb.dataprobi.util.JtkXlsReader;
import com.abujlb.dataprobi.util.MathUtil;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 账单京挑客花费
 *
 * <AUTHOR>
 * @date 2022/6/29 10:05
 */
@Component
@BiPro(order = 13, qd = Qd.JD)
public class DefaultBiJdJtkProcess extends AbstractBiJdProcess {

    private final String spdataosspath_zd_2 = "bi_jd/auth/authdata/jtk/%s/%s/jtk.xlsx";
    private final String spdataosspath_zd_path_2 = "bi_jd/auth/authdata/jtk/%s/%s/";

    @Override
    public void dealGoods() {
        super.dealGoods(SqliteJdJtkZdSp.class, spdataosspath_zd_2, ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getJtk());
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoods(SqliteJdJtkZdSp.class, spdataosspath_zd_2, ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getJtk());
    }

    @Override
    protected <T extends AbstractSqliteSp> List<T> spList(Class<T> tClass, String ossKey, String rq) {
        String ossfolder = String.format(spdataosspath_zd_path_2, rq, getDataprobiBaseMsg().getUserid());
        List<String> osskeyList = biDataOssUtil.listFiles(ossfolder);
        Map<String, SqliteJdJtkZdSp> map = new HashMap<>();
        for (String osskey : osskeyList) {
            String temppath = FileUtil.createXlsxFilePath();
            biDataOssUtil.download(osskey, temppath);
            List<SqliteJdJtkZdSp> list = JtkXlsReader.read(temppath);
            if (CollectionUtils.isEmpty(list)) {
                continue;
            }

            for (SqliteJdJtkZdSp sqliteJdJtkZdSp : list) {
                SqliteJdJtkZdSp temp = null;
                if (map.containsKey(sqliteJdJtkZdSp.getBbid())) {
                    temp = map.get(sqliteJdJtkZdSp.getBbid());
                    temp.plus(sqliteJdJtkZdSp);
                } else {
                    temp = new SqliteJdJtkZdSp();
                    temp.setBbid(sqliteJdJtkZdSp.getBbid());
                    temp.setJtkhfZd(sqliteJdJtkZdSp.getJtkhfZd());
                    map.put(sqliteJdJtkZdSp.getBbid(), temp);
                }
            }
        }

        List<SqliteJdJtkZdSp> list = new ArrayList<>(map.values());
        for (SqliteJdJtkZdSp sqliteJdJtkZdSp : list) {
            sqliteJdJtkZdSp.setQd(getDataprobiBaseMsg().getQd());
            sqliteJdJtkZdSp.setYhid(getDataprobiBaseMsg().getYhid());
            sqliteJdJtkZdSp.setUserid(getDataprobiBaseMsg().getUserid());
            sqliteJdJtkZdSp.setRq(rq);
            sqliteJdJtkZdSp.setQd_userid_bbid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid() + "_" + sqliteJdJtkZdSp.getBbid());
        }
        return (List<T>) list;
    }

    @Override
    protected <T extends AbstractSqliteSp> void dealShopByGoods(String rq, List<T> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        double jtkDpHf = 0;
        for (T t : list) {
            if (t instanceof SqliteJdJtkZdSp) {
                jtkDpHf = MathUtil.add(jtkDpHf, ((SqliteJdJtkZdSp) t).getJtkhfZd());
            }
        }

        if (jtkDpHf != 0) {
            DataprobiBaseMsgBean dataprobiBaseMsg = getDataprobiBaseMsg();
            SqliteJdJtkZdDp sqliteJdJtkDp = new SqliteJdJtkZdDp();
            sqliteJdJtkDp.setQd_userid(dataprobiBaseMsg.getQd() + "_" + dataprobiBaseMsg.getUserid());
            sqliteJdJtkDp.setRq(rq);
            sqliteJdJtkDp.setDpmc(dataprobiBaseMsg.getDpmc());
            sqliteJdJtkDp.setUserid(dataprobiBaseMsg.getUserid());
            sqliteJdJtkDp.setYhid(dataprobiBaseMsg.getYhid());
            sqliteJdJtkDp.setQd(dataprobiBaseMsg.getQd());
            sqliteJdJtkDp.setJtkhfZd(jtkDpHf);
            processSycmDpOTS(rq, sqliteJdJtkDp);
        }
    }
}
