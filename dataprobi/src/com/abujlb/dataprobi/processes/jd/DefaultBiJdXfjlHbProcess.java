package com.abujlb.dataprobi.processes.jd;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteDp;
import com.abujlb.dataprobi.bean.jd.*;
import com.abujlb.dataprobi.bean.jd.xfjl.JdPlugXfjlXjVO;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.util.FileUtil;
import com.abujlb.dataprobi.util.JdPlugXfjlXlsReader;
import com.abujlb.dataprobi.util.MathUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.*;

/**
 * 消费记录-红包
 *
 * <AUTHOR>
 * @date 2023/9/25
 */
@Component
@BiPro(order = 10, qd = Qd.JD)
public class DefaultBiJdXfjlHbProcess extends AbstractBiJdProcess {

    private final String OSS_PATTERN = "bi_jd/auth/authdata/cwjl/%s/%s/xfjl_hb.xls";

    private final List<String> JDKC = Arrays.asList("快车扣费", "快车扣费-展示服务", "搜索快车扣费", "搜索快车扣费-展示服务");
    private final List<String> JDZW = Arrays.asList("京东展位扣费", "互动广告（原展位）扣费");
    private final List<String> JDZT = Arrays.asList("站外广告-秒杀推广扣费", "站外广告-腾讯媒体扣费", "站外广告-定向计划扣", "站外广告-头条扣费", "站外广告-腾讯扣费", "站外广告-京东标签/百度标签计划扣费", "站外广告扣费", "站外广告-定向计划扣费");
    private final List<String> GWCD = Arrays.asList("推荐广告扣费—购物触点", "推荐广告扣费—购物触点展示服务");
    private final List<String> JSK = Collections.singletonList("站外广告-百度搜索扣费");
    private final List<String> JST = Arrays.asList("智能投放全店推广扣费-精准定向", "智能投放商品扣费-精准定向", "智能投放直播扣费-精准定向", "智能投放场景扣费-精准定向", "智能投放全店推广扣费-展示服务", "智能投放商品扣费-展示服务", "智能投放直播扣费-展示服务", "智能投放场景扣费-展示服务");
    private final List<String> JTK = Arrays.asList("京挑客扣费", "京挑客活动扣费", "京东联盟扣费", "京东联盟推广费", "京东联盟活动扣费");
    private final List<String> QZYX = Collections.singletonList("全站营销扣费");
    private final List<String> NRGG = Arrays.asList("内容营销扣费(站外)", "内容营销扣费(站内)", "代投放_内容营销扣费（站外）", "代投放_内容营销扣费（站内）", "内容制作扣费(短视频)", "内容制作扣费(直播)", "内容投放_视频计划扣费", "内容投放_视频计划展示服务扣费", "内容投放_直播计划扣费", "内容投放_直播计划展示服务扣费");


    @Autowired
    private DefaultBiJdJdzwProcess defaultBiJdJdzwProcess;

    @Autowired
    private DefaultBiJdJdkcProcess defaultBiJdJdkcProcess;

    @Autowired
    private DefaultBiJdGwcdProcess defaultBiJdGwcdProcess;

    @Autowired
    private DefaultBiJdJstProcess defaultBiJdJstProcess;

    @Autowired
    private DefaultBiJdJtkProcess defaultBiJdJtkProcess;

    @Autowired
    private DefaultBiJdJdztProcess defaultBiJdJdztProcess;

    @Autowired
    private DefaultBiJdQzyxProcess defaultBiJdQzyxProcess;

    @Autowired
    private DefaultBiJdNrggProcess defaultBiJdNrggProcess;

    @Override
    public void dealShop() {
        super.dealShopJd(SqliteJdXfjlHbDp.class, null, ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getXfjl());
    }


    @Override
    public boolean compareShop() {
        return super.compareShopJd(SqliteJdXfjlHbDp.class, null, ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getXfjl());
    }

    @Override
    protected <T extends AbstractSqliteDp> T getAllDp(Class<T> tClass, String prefix, String rq) {
        SqliteJdXfjlHbDp sqliteJdXfjlHbDp = xfjlhb(rq);
        if (sqliteJdXfjlHbDp == null) {
            return null;
        }

        dealGoodsByShare(sqliteJdXfjlHbDp, rq);
        sqliteJdXfjlHbDp.setNrgghb(-sqliteJdXfjlHbDp.getNrgghb());
        return (T) sqliteJdXfjlHbDp;
    }

    /**
     * 处理消费记录
     *
     * @param rq
     */
    private SqliteJdXfjlHbDp xfjlhb(String rq) {
        String hb_osskey = String.format(OSS_PATTERN, rq, getDataprobiBaseMsg().getUserid());
        if (!biDataOssUtil.exist(hb_osskey)) {
            return null;
        }

        String hb_temppath = FileUtil.createXlsFilePath();
        biDataOssUtil.download(hb_osskey, hb_temppath);

        File hb_file = new File(hb_temppath);

        List<JdPlugXfjlXjVO> hb_xfjlList = JdPlugXfjlXlsReader.readXfjlHb(hb_file);
        if (CollectionUtils.isEmpty(hb_xfjlList)) {
            return null;
        }
        SqliteJdXfjlHbDp sqliteJdXfjlHbDp = new SqliteJdXfjlHbDp();
        sqliteJdXfjlHbDp.setQd_userid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid());
        sqliteJdXfjlHbDp.setRq(rq);
        sqliteJdXfjlHbDp.setDpmc(getDataprobiBaseMsg().getDpmc());
        sqliteJdXfjlHbDp.setUserid(getDataprobiBaseMsg().getUserid());
        sqliteJdXfjlHbDp.setYhid(getDataprobiBaseMsg().getYhid());
        sqliteJdXfjlHbDp.setQd(getDataprobiBaseMsg().getQd());

        for (JdPlugXfjlXjVO jdPlugXfjlXjVO : hb_xfjlList) {
            if (!rq.equals(jdPlugXfjlXjVO.getTgrq())) {
                continue;
            }
            if (JDKC.contains(jdPlugXfjlXjVO.getJylx())) {
                sqliteJdXfjlHbDp.setJdkchb(MathUtil.add(sqliteJdXfjlHbDp.getJdkchb(), jdPlugXfjlXjVO.getZc()));
            } else if (JDZW.contains(jdPlugXfjlXjVO.getJylx())) {
                sqliteJdXfjlHbDp.setJdzwhb(MathUtil.add(sqliteJdXfjlHbDp.getJdzwhb(), jdPlugXfjlXjVO.getZc()));
            } else if (JDZT.contains(jdPlugXfjlXjVO.getJylx())) {
                sqliteJdXfjlHbDp.setJdzthb(MathUtil.add(sqliteJdXfjlHbDp.getJdzthb(), jdPlugXfjlXjVO.getZc()));
            } else if (GWCD.contains(jdPlugXfjlXjVO.getJylx())) {
                sqliteJdXfjlHbDp.setGwcdhb(MathUtil.add(sqliteJdXfjlHbDp.getGwcdhb(), jdPlugXfjlXjVO.getZc()));
            } else if (JSK.contains(jdPlugXfjlXjVO.getJylx())) {
                sqliteJdXfjlHbDp.setJskhb(MathUtil.add(sqliteJdXfjlHbDp.getJskhb(), jdPlugXfjlXjVO.getZc()));
            } else if (JST.contains(jdPlugXfjlXjVO.getJylx())) {
                sqliteJdXfjlHbDp.setJsthb(MathUtil.add(sqliteJdXfjlHbDp.getJsthb(), jdPlugXfjlXjVO.getZc()));
            } else if (JTK.contains(jdPlugXfjlXjVO.getJylx())) {
                sqliteJdXfjlHbDp.setJtkhb(MathUtil.add(sqliteJdXfjlHbDp.getJtkhb(), jdPlugXfjlXjVO.getZc()));
            } else if (QZYX.contains(jdPlugXfjlXjVO.getJylx())) {
                sqliteJdXfjlHbDp.setQzyxhb(MathUtil.add(sqliteJdXfjlHbDp.getQzyxhb(), jdPlugXfjlXjVO.getZc()));
            } else if (NRGG.contains(jdPlugXfjlXjVO.getJylx())) {
                sqliteJdXfjlHbDp.setNrgghb(MathUtil.add(sqliteJdXfjlHbDp.getNrgghb(), jdPlugXfjlXjVO.getZc()));
            }
        }
        return sqliteJdXfjlHbDp;
    }


    private void dealGoodsByShare(SqliteJdXfjlHbDp sqliteJdXfjlHbDp, String rq) {
        //处理单品
        Map<String, SqliteJdXfjlHbSp> map = new HashMap<>();
        if (sqliteJdXfjlHbDp.getJdkchb() > 0) {
            //京东快车单品
            processJdkc(map, sqliteJdXfjlHbDp, rq);
        }
        if (sqliteJdXfjlHbDp.getJdzwhb() > 0) {
            //京东展位单品
            processJdzw(map, sqliteJdXfjlHbDp, rq);
        }
        if (sqliteJdXfjlHbDp.getGwcdhb() > 0) {
            //购物触点单品
            processGwcd(map, sqliteJdXfjlHbDp, rq);
        }
        if (sqliteJdXfjlHbDp.getJsthb() > 0) {
            //京速推单品
            processJst(map, sqliteJdXfjlHbDp, rq);
        }
        if (sqliteJdXfjlHbDp.getJtkhb() > 0) {
            //京挑客单品
            processJtk(map, sqliteJdXfjlHbDp, rq);
        }
        if (sqliteJdXfjlHbDp.getJdzthb() > 0) {
            //京东直投单品
            processJdzt(map, sqliteJdXfjlHbDp, rq);
        }
        if (sqliteJdXfjlHbDp.getQzyxhb() > 0) {
            //全站营销单品
            processQzyx(map, sqliteJdXfjlHbDp, rq);
        }

        if (sqliteJdXfjlHbDp.getNrgghb() > 0) {
            //内容广告单品
            processNrgg(map, sqliteJdXfjlHbDp, rq);
        }

        if (map.isEmpty()) {
            return;
        }

        ArrayList<SqliteJdXfjlHbSp> sqliteJdXfjlHbSps = new ArrayList<>(map.values());
        for (SqliteJdXfjlHbSp sqliteJdXfjlHbSp : sqliteJdXfjlHbSps) {
            if (sqliteJdXfjlHbSp.getNrgghb() > 0) {
                sqliteJdXfjlHbSp.setNrgghb(-sqliteJdXfjlHbSp.getNrgghb());
            }
        }

        processSycmSpOTS(rq, sqliteJdXfjlHbSps);
        sqliteJdXfjlHbSps.clear();
    }

    private void processNrgg(Map<String, SqliteJdXfjlHbSp> map, SqliteJdXfjlHbDp sqliteJdXfjlHbDp, String rq) {
        List<SqliteJdNrggSp> list = defaultBiJdNrggProcess.getAll(SqliteJdNrggSp.class, null, rq);
        double total = 0;
        for (SqliteJdNrggSp sqliteJdNrggSp : list) {
            total = MathUtil.add(total, sqliteJdNrggSp.getNrgghf());
        }
        for (SqliteJdNrggSp sqliteJdNrggSp : list) {
            //单品所占总花费的比例
            double ratio = MathUtil.divide(sqliteJdNrggSp.getNrgghf(), total, 8);
            //单品所占花费比例 对应的 红包金额
            double hb = MathUtil.multipy(sqliteJdXfjlHbDp.getNrgghb(), ratio, 4);
            SqliteJdXfjlHbSp sqliteJdXfjlHbSp = map.getOrDefault(sqliteJdNrggSp.getBbid(), new SqliteJdXfjlHbSp());
            //set一些值
            sqliteJdXfjlHbSp.setQd(sqliteJdNrggSp.getQd());
            sqliteJdXfjlHbSp.setQd_userid_bbid(sqliteJdNrggSp.getQd_userid_bbid());
            sqliteJdXfjlHbSp.setBbid(sqliteJdNrggSp.getBbid());
            sqliteJdXfjlHbSp.setRq(rq);
            sqliteJdXfjlHbSp.setYhid(sqliteJdNrggSp.getYhid());
            sqliteJdXfjlHbSp.setUserid(sqliteJdNrggSp.getUserid());
            sqliteJdXfjlHbSp.setNrgghb(MathUtil.add(hb, sqliteJdXfjlHbSp.getNrgghb()));
            map.put(sqliteJdNrggSp.getBbid(), sqliteJdXfjlHbSp);
        }
    }

    private void processQzyx(Map<String, SqliteJdXfjlHbSp> map, SqliteJdXfjlHbDp sqliteJdXfjlHbDp, String rq) {
        List<SqliteJdQzyxSp> qzyxSpList = defaultBiJdQzyxProcess.getAll(SqliteJdQzyxSp.class, null, rq);
        double total = 0;
        for (SqliteJdQzyxSp sqliteJdQzyxSp : qzyxSpList) {
            total = MathUtil.add(total, sqliteJdQzyxSp.getQzyxhf());
        }
        for (SqliteJdQzyxSp sqliteJdQzyxSp : qzyxSpList) {
            //单品所占总花费的比例
            double ratio = MathUtil.divide(sqliteJdQzyxSp.getQzyxhf(), total, 8);
            //单品所占花费比例 对应的 红包金额
            double hb = MathUtil.multipy(sqliteJdXfjlHbDp.getQzyxhb(), ratio, 4);
            SqliteJdXfjlHbSp sqliteJdXfjlHbSp = map.getOrDefault(sqliteJdQzyxSp.getBbid(), new SqliteJdXfjlHbSp());
            //set一些值
            sqliteJdXfjlHbSp.setQd(sqliteJdQzyxSp.getQd());
            sqliteJdXfjlHbSp.setQd_userid_bbid(sqliteJdQzyxSp.getQd_userid_bbid());
            sqliteJdXfjlHbSp.setBbid(sqliteJdQzyxSp.getBbid());
            sqliteJdXfjlHbSp.setRq(rq);
            sqliteJdXfjlHbSp.setYhid(sqliteJdQzyxSp.getYhid());
            sqliteJdXfjlHbSp.setUserid(sqliteJdQzyxSp.getUserid());
            sqliteJdXfjlHbSp.setQzyxhb(MathUtil.add(hb, sqliteJdXfjlHbSp.getQzyxhb()));
            map.put(sqliteJdQzyxSp.getBbid(), sqliteJdXfjlHbSp);
        }
    }

    private void processJdzt(Map<String, SqliteJdXfjlHbSp> map, SqliteJdXfjlHbDp sqliteJdXfjlHbDp, String rq) {
        List<SqliteJdJdztSp> jdztList = defaultBiJdJdztProcess.getAll(SqliteJdJdztSp.class, null, rq);
        double total = 0;
        for (SqliteJdJdztSp sqliteJdJdztSp : jdztList) {
            total = MathUtil.add(total, sqliteJdJdztSp.getJdzthf());
        }
        for (SqliteJdJdztSp sqliteJdJdztSp : jdztList) {
            //单品所占总花费的比例
            double ratio = MathUtil.divide(sqliteJdJdztSp.getJdzthf(), total, 8);
            //单品所占花费比例 对应的 红包金额
            double hb = MathUtil.multipy(sqliteJdXfjlHbDp.getJdzthb(), ratio, 4);
            SqliteJdXfjlHbSp sqliteJdXfjlHbSp = map.getOrDefault(sqliteJdJdztSp.getBbid(), new SqliteJdXfjlHbSp());
            //set一些值
            sqliteJdXfjlHbSp.setQd(sqliteJdJdztSp.getQd());
            sqliteJdXfjlHbSp.setQd_userid_bbid(sqliteJdJdztSp.getQd_userid_bbid());
            sqliteJdXfjlHbSp.setBbid(sqliteJdJdztSp.getBbid());
            sqliteJdXfjlHbSp.setRq(rq);
            sqliteJdXfjlHbSp.setYhid(sqliteJdJdztSp.getYhid());
            sqliteJdXfjlHbSp.setUserid(sqliteJdJdztSp.getUserid());
            sqliteJdXfjlHbSp.setJdzthb(MathUtil.add(hb, sqliteJdXfjlHbSp.getJdzthb()));
            map.put(sqliteJdJdztSp.getBbid(), sqliteJdXfjlHbSp);
        }
    }

    private void processJtk(Map<String, SqliteJdXfjlHbSp> map, SqliteJdXfjlHbDp sqliteJdXfjlHbDp, String rq) {
        List<SqliteJdJtkZdSp> jtkList = defaultBiJdJtkProcess.spList(SqliteJdJtkZdSp.class, null, rq);
        double total = 0;
        for (SqliteJdJtkZdSp sqliteJdJtkZdSp : jtkList) {
            total = MathUtil.add(total, sqliteJdJtkZdSp.getJtkhfZd());
        }
        for (SqliteJdJtkZdSp sqliteJdJtkZdSp : jtkList) {
            double ratio = MathUtil.divide(sqliteJdJtkZdSp.getJtkhfZd(), total, 8);
            double hb = MathUtil.multipy(sqliteJdXfjlHbDp.getJtkhb(), ratio, 4);
            SqliteJdXfjlHbSp sqliteJdXfjlHbSp = map.getOrDefault(sqliteJdJtkZdSp.getBbid(), new SqliteJdXfjlHbSp());
            //set一些值
            sqliteJdXfjlHbSp.setQd(sqliteJdJtkZdSp.getQd());
            sqliteJdXfjlHbSp.setQd_userid_bbid(sqliteJdJtkZdSp.getQd_userid_bbid());
            sqliteJdXfjlHbSp.setBbid(sqliteJdJtkZdSp.getBbid());
            sqliteJdXfjlHbSp.setRq(rq);
            sqliteJdXfjlHbSp.setYhid(sqliteJdJtkZdSp.getYhid());
            sqliteJdXfjlHbSp.setUserid(sqliteJdJtkZdSp.getUserid());
            sqliteJdXfjlHbSp.setJtkhb(MathUtil.add(hb, sqliteJdXfjlHbSp.getJtkhb()));
            map.put(sqliteJdJtkZdSp.getBbid(), sqliteJdXfjlHbSp);
        }
    }

    private void processJst(Map<String, SqliteJdXfjlHbSp> map, SqliteJdXfjlHbDp sqliteJdXfjlHbDp, String rq) {
        List<SqliteJdJstSp> jstList = defaultBiJdJstProcess.getAll(SqliteJdJstSp.class, null, rq);
        double total = 0;
        for (SqliteJdJstSp sqliteJdJstSp : jstList) {
            total = MathUtil.add(total, sqliteJdJstSp.getJsthf());
        }
        for (SqliteJdJstSp sqliteJdJstSp : jstList) {
            double ratio = MathUtil.divide(sqliteJdJstSp.getJsthf(), total, 8);
            double hb = MathUtil.multipy(sqliteJdXfjlHbDp.getJsthb(), ratio, 4);
            SqliteJdXfjlHbSp sqliteJdXfjlHbSp = map.getOrDefault(sqliteJdJstSp.getBbid(), new SqliteJdXfjlHbSp());
            //set一些值
            sqliteJdXfjlHbSp.setQd(sqliteJdJstSp.getQd());
            sqliteJdXfjlHbSp.setQd_userid_bbid(sqliteJdJstSp.getQd_userid_bbid());
            sqliteJdXfjlHbSp.setBbid(sqliteJdJstSp.getBbid());
            sqliteJdXfjlHbSp.setRq(rq);
            sqliteJdXfjlHbSp.setYhid(sqliteJdJstSp.getYhid());
            sqliteJdXfjlHbSp.setUserid(sqliteJdJstSp.getUserid());
            sqliteJdXfjlHbSp.setJsthb(MathUtil.add(hb, sqliteJdXfjlHbSp.getJsthb()));
            map.put(sqliteJdJstSp.getBbid(), sqliteJdXfjlHbSp);
        }
    }

    private void processGwcd(Map<String, SqliteJdXfjlHbSp> map, SqliteJdXfjlHbDp sqliteJdXfjlHbDp, String rq) {
        List<SqliteJdGwcdSp> gwcdList = defaultBiJdGwcdProcess.getAll(SqliteJdGwcdSp.class, null, rq);
        double total = 0;
        for (SqliteJdGwcdSp sqliteJdGwcdSp : gwcdList) {
            total = MathUtil.add(total, sqliteJdGwcdSp.getGwcdhf());
        }
        for (SqliteJdGwcdSp sqliteJdGwcdSp : gwcdList) {
            double ratio = MathUtil.divide(sqliteJdGwcdSp.getGwcdhf(), total, 8);
            double hb = MathUtil.multipy(sqliteJdXfjlHbDp.getGwcdhb(), ratio, 4);
            SqliteJdXfjlHbSp sqliteJdXfjlHbSp = map.getOrDefault(sqliteJdGwcdSp.getBbid(), new SqliteJdXfjlHbSp());
            //set一些值
            sqliteJdXfjlHbSp.setQd(sqliteJdGwcdSp.getQd());
            sqliteJdXfjlHbSp.setQd_userid_bbid(sqliteJdGwcdSp.getQd_userid_bbid());
            sqliteJdXfjlHbSp.setBbid(sqliteJdGwcdSp.getBbid());
            sqliteJdXfjlHbSp.setRq(rq);
            sqliteJdXfjlHbSp.setYhid(sqliteJdGwcdSp.getYhid());
            sqliteJdXfjlHbSp.setUserid(sqliteJdGwcdSp.getUserid());
            sqliteJdXfjlHbSp.setGwcdhb(MathUtil.add(hb, sqliteJdXfjlHbSp.getGwcdhb()));
            map.put(sqliteJdGwcdSp.getBbid(), sqliteJdXfjlHbSp);
        }
    }

    private void processJdkc(Map<String, SqliteJdXfjlHbSp> map, SqliteJdXfjlHbDp sqliteJdXfjlHbDp, String rq) {
        List<SqliteJdJdkcSp> jdkcList = defaultBiJdJdkcProcess.getAll(SqliteJdJdkcSp.class, null, rq);
        double total = 0;
        for (SqliteJdJdkcSp sqliteJdJdkcSp : jdkcList) {
            total = MathUtil.add(total, sqliteJdJdkcSp.getJdkchf());
        }
        for (SqliteJdJdkcSp sqliteJdJdkcSp : jdkcList) {
            double ratio = MathUtil.divide(sqliteJdJdkcSp.getJdkchf(), total, 8);
            double hb = MathUtil.multipy(sqliteJdXfjlHbDp.getJdkchb(), ratio, 4);
            SqliteJdXfjlHbSp sqliteJdXfjlHbSp = map.getOrDefault(sqliteJdJdkcSp.getBbid(), new SqliteJdXfjlHbSp());
            //set一些值
            sqliteJdXfjlHbSp.setQd(sqliteJdJdkcSp.getQd());
            sqliteJdXfjlHbSp.setQd_userid_bbid(sqliteJdJdkcSp.getQd_userid_bbid());
            sqliteJdXfjlHbSp.setBbid(sqliteJdJdkcSp.getBbid());
            sqliteJdXfjlHbSp.setRq(rq);
            sqliteJdXfjlHbSp.setYhid(sqliteJdJdkcSp.getYhid());
            sqliteJdXfjlHbSp.setUserid(sqliteJdJdkcSp.getUserid());
            sqliteJdXfjlHbSp.setJdkchb(MathUtil.add(hb, sqliteJdXfjlHbSp.getJdkchb()));
            map.put(sqliteJdJdkcSp.getBbid(), sqliteJdXfjlHbSp);
        }
    }

    private void processJdzw(Map<String, SqliteJdXfjlHbSp> map, SqliteJdXfjlHbDp sqliteJdXfjlHbDp, String rq) {
        List<SqliteJdJdzwSp> jdzwList = defaultBiJdJdzwProcess.getAll(SqliteJdJdzwSp.class, null, rq);
        double total = 0;
        for (SqliteJdJdzwSp sqliteJdJdzwSp : jdzwList) {
            total = MathUtil.add(total, sqliteJdJdzwSp.getJdzwhf());
        }
        for (SqliteJdJdzwSp sqliteJdJdzwSp : jdzwList) {
            double ratio = MathUtil.divide(sqliteJdJdzwSp.getJdzwhf(), total, 8);
            double hb = MathUtil.multipy(sqliteJdXfjlHbDp.getJdzwhb(), ratio, 4);
            SqliteJdXfjlHbSp sqliteJdXfjlHbSp = map.getOrDefault(sqliteJdJdzwSp.getBbid(), new SqliteJdXfjlHbSp());
            //set一些值
            sqliteJdXfjlHbSp.setQd(sqliteJdJdzwSp.getQd());
            sqliteJdXfjlHbSp.setQd_userid_bbid(sqliteJdJdzwSp.getQd_userid_bbid());
            sqliteJdXfjlHbSp.setBbid(sqliteJdJdzwSp.getBbid());
            sqliteJdXfjlHbSp.setRq(rq);
            sqliteJdXfjlHbSp.setYhid(sqliteJdJdzwSp.getYhid());
            sqliteJdXfjlHbSp.setUserid(sqliteJdJdzwSp.getUserid());
            sqliteJdXfjlHbSp.setJdzwhb(MathUtil.add(hb, sqliteJdXfjlHbSp.getJdzwhb()));
            map.put(sqliteJdJdzwSp.getBbid(), sqliteJdXfjlHbSp);
        }
    }
}
