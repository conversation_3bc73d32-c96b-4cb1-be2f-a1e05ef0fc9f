package com.abujlb.dataprobi.processes.jd;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteDp;
import com.abujlb.dataprobi.bean.jd.DataprobiJdMsgBean;
import com.abujlb.dataprobi.bean.jd.SqliteJdWyjDp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.server.DataUploader;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.abujlb.dataprobi.util.MathUtil;
import com.abujlb.util.StringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * JD违约金
 *
 * <AUTHOR>
 * @date 2024/9/25
 */
@Component
@BiPro(order = 2, qd = Qd.JD)
public class DefaultBiJdWyjProcess extends AbstractBiJdProcess {

    String oss_pattern = "bi_jd/jdsz/%s/%s/wyj.json";

    @Override
    public void dealShop() {
        super.dealShop(
                SqliteJdWyjDp.class,
                ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getWyj()
        );
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqliteJdWyjDp.class,
                ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getWyj()
        );
    }

    @Override
    protected <T extends AbstractSqliteDp> T dpObject(Class<T> tClass, String rq, String... colummns) {
        String osskey = String.format(oss_pattern, getDataprobiBaseMsg().getUserid(), rq);
        String json = biDataOssUtil.readJson(osskey);
        if (!StringUtil.isJsonArray2(json)) {
            return null;
        }
        String wyj_name = "违约金";
        String popcxtgfwf_name = "pop促销推广服务费";

        //取 状态不是《财务驳回》《业务驳回》的
        List<String> filterStatus = Arrays.asList("4", "22");
        double wyj = 0;
        double popcxtgfwf = 0;

        JSONArray jsonArray = JSONArray.parseArray(json);
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            String billTypeName = FastJSONObjAttrToNumber.toString(jsonObject, "billTypeName");
            String status = FastJSONObjAttrToNumber.toString(jsonObject, "status");
            String currency = FastJSONObjAttrToNumber.toString(jsonObject, "currency");
            if (billTypeName == null || (!billTypeName.equals(wyj_name) && !billTypeName.equals(popcxtgfwf_name))) {
                continue;
            }

            double je = 0;
            if (status != null && !filterStatus.contains(status)) {
                je = FastJSONObjAttrToNumber.toDouble(jsonObject, "amount");
                if (je != 0 && "USD".equals(currency)) {
                    String currencyStr = DataUploader.queryCurrencyRate("USD", rq);
                    LOG.info(currencyStr);
                    if (JSON.parseObject(currencyStr).containsKey("data") && JSON.parseObject(currencyStr).getJSONObject("data") != null) {
                        Double val = JSON.parseObject(currencyStr).getJSONObject("data").getDouble("val");
                        je = MathUtil.multipy(je, val);
                    } else {//未成功获取汇率直接返回
                        LOG.error(getDataprobiBaseMsg().getDpmc() + "-->获取汇率失败，时间：" + rq);
                        return null;
                    }
                }
            }

            if (je == 0) {
                continue;
            }

            if (billTypeName.equals(wyj_name)) {
                wyj = MathUtil.add(wyj, je);
            } else {
                popcxtgfwf = MathUtil.add(popcxtgfwf, je);
            }
        }

        if (wyj == 0 && popcxtgfwf == 0) {
            return null;
        }

        return (T) new SqliteJdWyjDp(getDataprobiBaseMsg(), rq, wyj, popcxtgfwf);
    }
}
