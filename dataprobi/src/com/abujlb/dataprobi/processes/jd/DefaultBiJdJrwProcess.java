package com.abujlb.dataprobi.processes.jd;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.enums.Qd;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/6/29 10:05
 */
@Component
@BiPro(order = 10, qd = Qd.JD)
public class DefaultBiJdJrwProcess extends AbstractBiJdProcess {

    private final String newPrefixDp = "bi_jd/auth/authdata/jrwdp/";

    @Override
    public void dealShop() {
//        super.dealShopJd(SqliteJdJrwDp.class, newPrefixDp, ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getJrwdp());
    }

}