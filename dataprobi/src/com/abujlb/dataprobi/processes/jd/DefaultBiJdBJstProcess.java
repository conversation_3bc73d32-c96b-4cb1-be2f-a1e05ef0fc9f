package com.abujlb.dataprobi.processes.jd;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.jd.DataprobiJdMsgBean;
import com.abujlb.dataprobi.bean.jd.SqliteJdBJstDp;
import com.abujlb.dataprobi.bean.jd.SqliteJdBJstSp;
import com.abujlb.dataprobi.enums.Qd;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/8
 */
@Component
@BiPro(order = 12, qd = Qd.JD)
public class DefaultBiJdBJstProcess extends AbstractBiJdProcess {

    private final String newPrefixSp = "bi_jd/auth/authdata/bjdht/";
    private final String newPrefixDp = "bi_jd/auth/authdata/bjdhtdp/";

    @Override
    public void dealGoods() {
        super.dealGoodsJd(SqliteJdBJstSp.class, newPrefixSp, ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getBjdht());
    }

    @Override
    public void dealShop() {
        super.dealShopJd(SqliteJdBJstDp.class, newPrefixDp, ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getBjdhtdp());
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoodsJd(SqliteJdBJstSp.class, newPrefixSp, ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getBjdht());
    }

    @Override
    public boolean compareShop() {
        return super.compareShopJd(SqliteJdBJstDp.class, newPrefixDp, ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getBjdhtdp());
    }

    @Override
    protected <T extends AbstractSqliteSp> List<T> getAll(Class<T> mClass, String prefix, String rq) {
        return super.getAll(mClass, newPrefixSp, rq);
    }
}
