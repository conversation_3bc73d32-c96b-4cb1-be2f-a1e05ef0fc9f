package com.abujlb.dataprobi.processes.jd;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.jd.DataprobiJdMsgBean;
import com.abujlb.dataprobi.bean.jd.SqliteJdNrggDp;
import com.abujlb.dataprobi.bean.jd.SqliteJdNrggSp;
import com.abujlb.dataprobi.enums.Qd;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/8
 */
@Component
@BiPro(order = 6, qd = Qd.JD)
public class DefaultBiJdNrggProcess extends AbstractBiJdProcess {

    private final String newPrefixSp = "bi_jd/auth/authdata/nrgg/";
    private final String newPrefixDp = "bi_jd/auth/authdata/nrggdp/";

    @Override
    public void dealGoods() {
        super.dealGoodsJd(SqliteJdNrggSp.class, newPrefixSp, ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getNrgg());
    }

    @Override
    public void dealShop() {
        super.dealShopJd(SqliteJdNrggDp.class, newPrefixDp, ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getNrggdp());
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoodsJd(SqliteJdNrggSp.class, newPrefixSp, ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getNrgg());
    }

    @Override
    public boolean compareShop() {
        return super.compareShopJd(SqliteJdNrggDp.class, newPrefixDp, ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getNrggdp());
    }

    @Override
    protected <T extends AbstractSqliteSp> List<T> getAll(Class<T> mClass, String prefix, String rq) {
        return super.getAll(mClass, newPrefixSp, rq);
    }
}
