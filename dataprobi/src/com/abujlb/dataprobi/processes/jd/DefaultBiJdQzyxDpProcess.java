package com.abujlb.dataprobi.processes.jd;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteDp;
import com.abujlb.dataprobi.bean.jd.DataprobiJdMsgBean;
import com.abujlb.dataprobi.bean.jd.SqliteJdQzyxDp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.abujlb.dataprobi.util.MathUtil;
import com.abujlb.util.StringUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/10
 * 处理全站营销-店铺维度
 */
@Component
@BiPro(order = 15, qd = Qd.JD)
public class DefaultBiJdQzyxDpProcess extends AbstractBiJdProcess {

    private final String newPrefixSp = "bi_jd/auth/authdata/qzyx/";

    @Override
    public void dealShop() {
        super.dealShopJd(SqliteJdQzyxDp.class, newPrefixSp, ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getQzyx());
    }

    @Override
    public boolean compareShop() {
        return super.compareShopJd(SqliteJdQzyxDp.class, newPrefixSp, ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getQzyx());
    }

    @Override
    protected <T extends AbstractSqliteDp> T getAllDp(Class<T> tClass, String prefix, String rq) {
        List<String> ossKeyList = listFiles(newPrefixSp, rq);
        if (CollectionUtils.isEmpty(ossKeyList)) {
            return null;
        }


        //全站营销花费
        double qzyxhf = 0;
        //全站营销成交金额
        double qzyxcjje = 0;
        //全站营销订单行
        int qzyxdds = 0;
        //全站营销展现量
        int qzyxzxl = 0;
        //全站营销点击量
        int qzyxdjl = 0;

        for (String ossKey : ossKeyList) {
            String json = biDataOssUtil.readJson(ossKey);
            if (!StringUtil.isJsonArray2(json)) {
                continue;
            }

            JSONArray jsonArray = JSONArray.parseArray(json);
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                int qzyxzxl2 = FastJSONObjAttrToNumber.toInt(jsonObject, "impressions");
                int qzyxdjl2 = FastJSONObjAttrToNumber.toInt(jsonObject, "clicks");
                int qzyxdds2 = FastJSONObjAttrToNumber.toInt(jsonObject, "totalOrderCnt");
                double qzyxcjje2 = FastJSONObjAttrToNumber.toDouble(jsonObject, "totalOrderSum");
                double qzyxhf2 = FastJSONObjAttrToNumber.toDouble(jsonObject, "cost");

                qzyxhf = MathUtil.add(qzyxhf2, qzyxhf);
                qzyxcjje = MathUtil.add(qzyxcjje2, qzyxcjje);
                qzyxzxl += qzyxzxl2;
                qzyxdjl += qzyxdjl2;
                qzyxdds += qzyxdds2;
            }
        }

        SqliteJdQzyxDp sqliteJdQzyxDp = new SqliteJdQzyxDp();
        sqliteJdQzyxDp.setQd_userid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid());
        sqliteJdQzyxDp.setDpmc(getDataprobiBaseMsg().getDpmc());
        sqliteJdQzyxDp.setUserid(getDataprobiBaseMsg().getUserid());
        sqliteJdQzyxDp.setYhid(getDataprobiBaseMsg().getYhid());
        sqliteJdQzyxDp.setQd(getDataprobiBaseMsg().getQd());
        sqliteJdQzyxDp.setRq(rq);

        sqliteJdQzyxDp.setQzyxhf(qzyxhf);
        sqliteJdQzyxDp.setQzyxcjje(qzyxcjje);
        sqliteJdQzyxDp.setQzyxdds(qzyxdds);
        sqliteJdQzyxDp.setQzyxdjl(qzyxdjl);
        sqliteJdQzyxDp.setQzyxzxl(qzyxzxl);


        return (T) sqliteJdQzyxDp;
    }
}
