package com.abujlb.dataprobi.processes.jd;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.DataprobiBaseMsgBean;
import com.abujlb.dataprobi.bean.jd.DataprobiJdMsgBean;
import com.abujlb.dataprobi.bean.jd.SqliteJdQzyxSp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.threads.BiThreadLocals;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.abujlb.util.StringUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/10
 * 处理全站营销-SPU维度
 */
@Component
@BiPro(order = 15, qd = Qd.JD)
public class DefaultBiJdQzyxSpuProcess extends AbstractBiJdProcess {

    private final String newPrefixSp = "bi_jd/auth/authdata/qzyx/";

    @Override
    public void dealGoods() {
        super.dealGoodsJd(SqliteJdQzyxSp.class, newPrefixSp, ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getQzyx(), true);
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoodsJd(SqliteJdQzyxSp.class, newPrefixSp, ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getQzyx(), true);
    }

    @Override
    protected <T extends AbstractSqliteSp> List<T> getAll(Class<T> mClass, String prefix, String rq) {
        return super.getAll(mClass, newPrefixSp, rq);
    }

    @Override
    protected <T extends AbstractSqliteSp> List<T> spList(Class<T> tClass, String ossKey, String rq) {
        String json = biDataOssUtil.readJson(ossKey);
        if (!StringUtil.isJsonArray2(json)) {
            return Collections.emptyList();
        }

        List<SqliteJdQzyxSp> list = new ArrayList<>();
        DataprobiBaseMsgBean msgBean = BiThreadLocals.getMsgBean();
        JSONArray jsonArray = JSONArray.parseArray(json);
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);

            boolean spu = false;
            if (jsonObject.containsKey("materialType")) {
                int materialType = FastJSONObjAttrToNumber.toInt(jsonObject, "materialType");
                if (materialType != 1) {
                    continue;
                }

                spu = true;
            }

            if (!spu && jsonObject.containsKey("sxuType")) {
                int sxuType = FastJSONObjAttrToNumber.toInt(jsonObject, "sxuType");
                if (sxuType != 1) {
                    continue;
                }

                spu = true;
            }

            if (!spu) {
                continue;
            }

            String spuId = FastJSONObjAttrToNumber.toString(jsonObject, "sxuId");
            SqliteJdQzyxSp sqliteJdQzyxSp = new SqliteJdQzyxSp();
            sqliteJdQzyxSp.setQzyxzxl(FastJSONObjAttrToNumber.toInt(jsonObject, "impressions"));
            sqliteJdQzyxSp.setQzyxdjl(FastJSONObjAttrToNumber.toInt(jsonObject, "clicks"));
            sqliteJdQzyxSp.setQzyxdds(FastJSONObjAttrToNumber.toInt(jsonObject, "totalOrderCnt"));
            sqliteJdQzyxSp.setQzyxcjje(FastJSONObjAttrToNumber.toDouble(jsonObject, "totalOrderSum"));
            sqliteJdQzyxSp.setQzyxhf(FastJSONObjAttrToNumber.toDouble(jsonObject, "cost"));
            sqliteJdQzyxSp.setBbid(spuId);

            sqliteJdQzyxSp.setQd_userid_bbid(msgBean.getQd() + "_" + msgBean.getUserid() + "_" + sqliteJdQzyxSp.getBbid());
            sqliteJdQzyxSp.setRq(rq);
            sqliteJdQzyxSp.setYhid(msgBean.getYhid());
            sqliteJdQzyxSp.setQd(msgBean.getQd());
            sqliteJdQzyxSp.setUserid(msgBean.getUserid());
            list.add(sqliteJdQzyxSp);
        }
        return (List<T>) list;
    }
}
