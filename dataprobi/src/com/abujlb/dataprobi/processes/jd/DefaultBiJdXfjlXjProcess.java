package com.abujlb.dataprobi.processes.jd;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteDp;
import com.abujlb.dataprobi.bean.jd.DataprobiJdMsgBean;
import com.abujlb.dataprobi.bean.jd.SqliteJdXfjlXjDp;
import com.abujlb.dataprobi.bean.jd.xfjl.JdPlugXfjlXjVO;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.util.FileUtil;
import com.abujlb.dataprobi.util.JdPlugXfjlXlsReader;
import com.abujlb.dataprobi.util.MathUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 消费记录-现金
 *
 * <AUTHOR>
 * @date 2025/5/9
 */
@Component
@BiPro(order = 10, qd = Qd.JD)
public class DefaultBiJdXfjlXjProcess extends AbstractBiJdProcess {

    private final String OSS_PATTERN_XJ = "bi_jd/auth/authdata/cwjl/%s/%s/xfjl_xj.xls";

    private final List<String> LHHDKF_XJ = Collections.singletonList("联合活动扣费");
    private final List<String> NRYX_XJ = Arrays.asList("内容营销扣费（站内）", "内容营销扣费(站外)", "代投放_内容营销扣费（站外）", "代投放_内容营销扣费（站内）", "内容制作扣费(短视频)", "内容制作扣费(直播)");
    private final List<String> XJ_LIST = new ArrayList<>();

    {
        XJ_LIST.addAll(NRYX_XJ);
        XJ_LIST.addAll(LHHDKF_XJ);
    }

    @Override
    public void dealShop() {
        super.dealShopJd(SqliteJdXfjlXjDp.class, null, ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getXfjl());
    }


    @Override
    public boolean compareShop() {
        return super.compareShopJd(SqliteJdXfjlXjDp.class, null, ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getXfjl());
    }

    @Override
    protected <T extends AbstractSqliteDp> T getAllDp(Class<T> tClass, String prefix, String rq) {
        return (T) xfjlhb(rq);
    }

    /**
     * 处理消费记录
     *
     * @param rq
     */
    private SqliteJdXfjlXjDp xfjlhb(String rq) {
        String xj_osskey = String.format(OSS_PATTERN_XJ, rq, getDataprobiBaseMsg().getUserid());
        if (!biDataOssUtil.exist(xj_osskey)) {
            return null;
        }

        String xj_temppath = FileUtil.createXlsFilePath();
        biDataOssUtil.download(xj_osskey, xj_temppath);

        File xj_file = new File(xj_temppath);

        List<JdPlugXfjlXjVO> xj_xfjlList = JdPlugXfjlXlsReader.readXfjlXj(xj_file, XJ_LIST);
        if (CollectionUtils.isEmpty(xj_xfjlList)) {
            return null;
        }

        SqliteJdXfjlXjDp sqliteJdXfjlXjDp = new SqliteJdXfjlXjDp();
        sqliteJdXfjlXjDp.setQd_userid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid());
        sqliteJdXfjlXjDp.setRq(rq);
        sqliteJdXfjlXjDp.setDpmc(getDataprobiBaseMsg().getDpmc());
        sqliteJdXfjlXjDp.setUserid(getDataprobiBaseMsg().getUserid());
        sqliteJdXfjlXjDp.setYhid(getDataprobiBaseMsg().getYhid());
        sqliteJdXfjlXjDp.setQd(getDataprobiBaseMsg().getQd());

        for (JdPlugXfjlXjVO jdPlugXfjlXjVO : xj_xfjlList) {
            if (!rq.equals(jdPlugXfjlXjVO.getTgrq())) {
                continue;
            }
            if (LHHDKF_XJ.contains(jdPlugXfjlXjVO.getJylx())) {
                sqliteJdXfjlXjDp.setLhhdkf(MathUtil.add(sqliteJdXfjlXjDp.getLhhdkf(), jdPlugXfjlXjVO.getZc()));
            } else if (NRYX_XJ.contains(jdPlugXfjlXjVO.getJylx())) {
                sqliteJdXfjlXjDp.setNryxhf(MathUtil.add(sqliteJdXfjlXjDp.getNryxhf(), jdPlugXfjlXjVO.getZc()));
            }
        }
        return sqliteJdXfjlXjDp;
    }
}
