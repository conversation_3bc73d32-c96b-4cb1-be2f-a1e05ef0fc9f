package com.abujlb.dataprobi.processes.jd;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.jd.DataprobiJdMsgBean;
import com.abujlb.dataprobi.bean.jd.SqliteJdSpxgDp;
import com.abujlb.dataprobi.bean.jd.SqliteJdSpxgSp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/6/22 15:11
 */
@Component
@BiPro(order = 1, qd = Qd.JD)
public class DefaultBiJdSpxgProcess extends AbstractBiJdProcess {

    private final String OSSKEY_PATTERN = "bi_jd/jdsz/%s/%s/sku.json";
    private final String[] COLUMNS = {
            BiSycmDpDataTsDao.JD_COLUMN_SPU,
            BiSycmDpDataTsDao.JD_COLUMN_SKU,
            BiSycmDpDataTsDao.JD_COLUMN_JYGK,
            BiSycmDpDataTsDao.JD_COLUMN_REFUND
    };

    @Override
    public void dealGoods() {
        super.dealGoods(
                SqliteJdSpxgSp.class,
                OSSKEY_PATTERN,
                ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getSpxg()
        );
    }

    @Override
    public void dealShop() {
        super.dealShop(
                SqliteJdSpxgDp.class,
                ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getSpxgdp(),
                COLUMNS
        );
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoods(
                SqliteJdSpxgSp.class,
                OSSKEY_PATTERN,
                ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getSpxg()
        );
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqliteJdSpxgDp.class,
                ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getSpxgdp(),
                COLUMNS
        );
    }
}
