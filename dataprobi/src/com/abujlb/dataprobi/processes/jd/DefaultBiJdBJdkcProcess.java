package com.abujlb.dataprobi.processes.jd;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.jd.DataprobiJdMsgBean;
import com.abujlb.dataprobi.bean.jd.SqliteJdBJdkcDp;
import com.abujlb.dataprobi.bean.jd.SqliteJdBJdkcSp;
import com.abujlb.dataprobi.enums.Qd;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/6/29 10:05
 */
@Component
@BiPro(order = 6, qd = Qd.JD)
public class DefaultBiJdBJdkcProcess extends AbstractBiJdProcess {

    private final String newPrefixSp = "bi_jd/auth/authdata/bjdkc/";
    private final String newPrefixDp = "bi_jd/auth/authdata/bjdkcdp/";

    @Override
    public void dealGoods() {
        super.dealGoodsJd(SqliteJdBJdkcSp.class, newPrefixSp, ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getBjdkc());
    }

    @Override
    public void dealShop() {
        super.dealShopJd(SqliteJdBJdkcDp.class, newPrefixDp, ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getBjdkcdp());
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoodsJd(SqliteJdBJdkcSp.class, newPrefixSp, ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getBjdkc());
    }

    @Override
    public boolean compareShop() {
        return super.compareShopJd(SqliteJdBJdkcDp.class, newPrefixDp, ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getBjdkcdp());
    }

    @Override
    protected <T extends AbstractSqliteSp> List<T> getAll(Class<T> mClass, String prefix, String rq) {
        return super.getAll(mClass, newPrefixSp, rq);
    }
}
