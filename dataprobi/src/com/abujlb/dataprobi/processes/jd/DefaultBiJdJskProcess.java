package com.abujlb.dataprobi.processes.jd;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.jd.DataprobiJdMsgBean;
import com.abujlb.dataprobi.bean.jd.SqliteJdJskDp;
import com.abujlb.dataprobi.enums.Qd;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/6/29 10:05
 */
@Component
@BiPro(order = 11, qd = Qd.JD)
public class DefaultBiJdJskProcess extends AbstractBiJdProcess {

    private final String newPrefixDp = "bi_jd/auth/authdata/jskdp/";

    @Override
    public void dealShop() {
        super.dealShopJd(SqliteJdJskDp.class, newPrefixDp, ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getJskdp());
    }

    @Override
    public boolean compareShop() {
        return super.compareShopJd(SqliteJdJskDp.class, newPrefixDp, ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getJskdp());
    }
}
