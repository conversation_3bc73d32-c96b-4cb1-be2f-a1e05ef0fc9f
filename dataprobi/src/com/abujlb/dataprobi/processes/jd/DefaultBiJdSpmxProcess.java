package com.abujlb.dataprobi.processes.jd;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.jd.DataprobiJdMsgBean;
import com.abujlb.dataprobi.bean.jd.SqliteJdSpmxDp;
import com.abujlb.dataprobi.bean.jd.SqliteJdSpmxSp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.util.FbpjyzkspmxXlsReader;
import com.abujlb.dataprobi.util.FileUtil;
import com.abujlb.dataprobi.util.MathUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.List;

/**
 * https://www.tapd.cn/47708728/prong/stories/view/1147708728001002331
 *
 * <AUTHOR>
 * @date 2023/12/26
 */
@Component
@BiPro(order = 3, qd = Qd.JD)
public class DefaultBiJdSpmxProcess extends AbstractBiJdProcess {

    private final String OSSKEY_PATTERN = "bi_jd/jdsz/%s/%s/sku.xlsx";

    @Override
    public void dealGoods() {
        super.dealGoods(
                SqliteJdSpmxSp.class,
                OSSKEY_PATTERN,
                ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getFbpjyzkspmx()
        );
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoods(
                SqliteJdSpmxSp.class,
                OSSKEY_PATTERN,
                ((DataprobiJdMsgBean) getDataprobiBaseMsg()).getFbpjyzkspmx()
        );
    }

    @Override
    protected <T extends AbstractSqliteSp> List<T> spList(Class<T> tClass, String ossKey, String rq) {
        if (!biDataOssUtil.exist(ossKey)) {
            return null;
        }

        String path = FileUtil.createXlsxFilePath();
        biDataOssUtil.download(ossKey, path);

        File file = new File(path);
        List<SqliteJdSpmxSp> sqliteJdSpmxSpList = FbpjyzkspmxXlsReader.parseSku(file, getDataprobiBaseMsg().getYhid(), getDataprobiBaseMsg().getUserid(), "JD", rq);
        if (CollectionUtils.isEmpty(sqliteJdSpmxSpList)) {
            return null;
        }

        return (List<T>) sqliteJdSpmxSpList;
    }

    @Override
    protected <T extends AbstractSqliteSp> void dealShopByGoods(String rq, List<T> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        //商品明细-浏览量
        int spmxlll = 0;
        //商品明细-访客数
        int spmxfks = 0;
        //商品明细-成交金额
        double spmxcjje = 0;
        //商品明细-支付人数
        int spmxzfrs = 0;

        for (T t : list) {
            if (t instanceof SqliteJdSpmxSp) {
                spmxlll += ((SqliteJdSpmxSp) t).getSpmxlll();
                spmxfks += ((SqliteJdSpmxSp) t).getSpmxfks();
                spmxcjje = MathUtil.add(spmxcjje, ((SqliteJdSpmxSp) t).getSpmxcjje());
                spmxzfrs += ((SqliteJdSpmxSp) t).getSpmxzfrs();
            }
        }

        SqliteJdSpmxDp sqliteJdSpmxDp = new SqliteJdSpmxDp();
        sqliteJdSpmxDp.setQd_userid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid());
        sqliteJdSpmxDp.setDpmc(getDataprobiBaseMsg().getDpmc());
        sqliteJdSpmxDp.setUserid(getDataprobiBaseMsg().getUserid());
        sqliteJdSpmxDp.setYhid(getDataprobiBaseMsg().getYhid());
        sqliteJdSpmxDp.setQd(getDataprobiBaseMsg().getQd());
        sqliteJdSpmxDp.setRq(rq);

        sqliteJdSpmxDp.setSpmxfks(spmxfks);
        sqliteJdSpmxDp.setSpmxcjje(spmxcjje);
        sqliteJdSpmxDp.setSpmxlll(spmxlll);
        sqliteJdSpmxDp.setSpmxcjkdj(MathUtil.divide(spmxcjje, spmxzfrs));
        sqliteJdSpmxDp.setSpmxrjlll(MathUtil.divide(spmxlll, spmxfks));
        sqliteJdSpmxDp.setSpmxzfrs(spmxzfrs);

        processSycmDpOTS(rq, sqliteJdSpmxDp);
    }
}
