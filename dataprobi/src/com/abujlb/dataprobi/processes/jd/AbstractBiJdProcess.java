package com.abujlb.dataprobi.processes.jd;

import com.abujlb.dataprobi.bean.AbstractSqliteDp;
import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.SqliteSp;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.util.FileToJson;
import com.abujlb.dataprobi.util.FileUtil;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/6/29 9:38
 */
@Component
public class AbstractBiJdProcess extends AbstractBiprocess {

    protected final <T extends AbstractSqliteSp> void dealGoodsJd(Class<T> tClass, String prefix, List<String> rqList) {
        dealGoodsJd(tClass, prefix, rqList, false);
    }

    protected final <T extends AbstractSqliteSp> void dealGoodsJd(Class<T> tClass, String prefix, List<String> rqList, boolean spu) {
        if (CollectionUtils.isEmpty(rqList)) {
            return;
        }
        for (String rq : rqList) {
            List<T> list = getAll(tClass, prefix, rq);
            if (spu) {
                processSycmSpuOTS(rq, list);
            } else {
                processSycmSpOTS(rq, list);
            }

            dealShopByGoods(rq, list);
            if (!CollectionUtils.isEmpty(list)) {
                list.clear();
            }
        }
    }

    protected final <T extends AbstractSqliteDp> void dealShopJd(Class<T> tClass, String prefix, List<String> rqList) {
        if (CollectionUtils.isEmpty(rqList)) {
            return;
        }
        for (String rq : rqList) {
            T t = getAllDp(tClass, prefix, rq);
            processSycmDpOTS(rq, t);
        }
    }

    protected <T extends AbstractSqliteDp> boolean compareShopJd(Class<T> tClass, String prefix, List<String> rqList) {
        if (CollectionUtils.isEmpty(rqList)) {
            return true;
        }
        boolean result = true;
        for (String rq : rqList) {
            T t = getAllDp(tClass, prefix, rq);
            if (!result) {
                processSycmDpOTS(rq, t);
                continue;
            }
            if (t != null) {
                boolean flag = t.compare(getSycmDp(rq));
                if (!flag) {
                    result = false;
                    processSycmDpOTS(rq, t);
                }
            }
        }
        return result;
    }

    protected <T extends AbstractSqliteSp> boolean compareGoodsJd(Class<T> tClass, String prefix, List<String> rqList) {
        return compareGoodsJd(tClass, prefix, rqList, false);
    }

    protected <T extends AbstractSqliteSp> boolean compareGoodsJd(Class<T> tClass, String prefix, List<String> rqList, boolean spu) {
        boolean result = true;
        for (String rq : rqList) {
            List<T> list = getAll(tClass, prefix, rq);
            if (CollectionUtils.isEmpty(list)) {
                continue;
            }
            if (!result) {
                if (spu) {
                    processSycmSpuOTS(rq, list);
                } else {
                    processSycmSpOTS(rq, list);
                }
                dealShopByGoods(rq, list);

                list.clear();
                continue;
            }

            Map<String, SqliteSp> map = spu ? getSycmSpSpuMapByBbid(rq) : getSycmSpMapByBbid(rq);
            boolean flag = true;
            for (T t : list) {
                flag = t.compare(map.get(t.getBbid()));
                if (!flag) {
                    result = false;
                    break;
                }
            }
            map.clear();
            if (!flag) {
                if (spu) {
                    processSycmSpuOTS(rq, list);
                } else {
                    processSycmSpOTS(rq, list);
                }
                dealShopByGoods(rq, list);
            }
            list.clear();
        }
        return result;
    }

    protected List<String> listFiles(String prefix, String rq) {
        return biDataOssUtil.listFiles(prefix + rq + "/" + getDataprobiBaseMsg().getUserid() + "/");
    }

    protected <T extends AbstractSqliteSp> List<T> getAll(Class<T> mClass, String prefix, String rq) {
        List<T> list = new ArrayList<>();
        List<String> keyList = listFiles(prefix, rq);
        for (String key : keyList) {
            List<T> l = spList(mClass, key, rq);
            list.addAll(l);
        }
        keyList.clear();
        return union(list);
    }

    protected <T extends AbstractSqliteDp> T getAllDp(Class<T> tClass, String prefix, String rq) {
        List<T> list = new ArrayList<>();

        List<String> keyList = listFiles(prefix, rq);
        for (String key : keyList) {
            T t = downloadAndConvertToDp(key, rq, tClass);
            list.add(t);
        }
        keyList.clear();
        return unionDp(list);
    }

    protected <T extends AbstractSqliteSp> List<T> union(List<T> list) {
        if (CollectionUtils.isEmpty(list)) {
            return list;
        }
        Map<String, T> map = new HashMap<>();
        for (T m : list) {
            if (map.containsKey(m.getBbid())) {
                map.get(m.getBbid()).plus(m);
            } else {
                map.put(m.getBbid(), m);
            }
        }

        ArrayList<T> ts = new ArrayList<>(map.values());
        return ts.stream().
                filter(t ->
                        StringUtils.isNotBlank(t.getBbid()) && !StringUtils.equalsIgnoreCase("0", t.getBbid())
                )
                .collect(Collectors.toList());
    }

    private <T extends AbstractSqliteDp> T unionDp(List<T> list) {
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        T t = list.get(0);
        for (int i = 1; i < list.size(); i++) {
            t.plus(list.get(i));
        }

        return t;
    }

    private <T extends AbstractSqliteDp> T downloadAndConvertToDp(String key, String rq, Class<T> tClass) {
        String filePath = FileUtil.createJSONFilePath();
        biDataOssUtil.download(key, filePath);

        File file = new File(filePath);
        String s = FileToJson.toJson2(file);

        try {
            T t = tClass.newInstance();
            JSONObject temp = JSONObject.parseObject(s);
            t.setQd_userid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid());
            t.setRq(rq);
            t.setDpmc(getDataprobiBaseMsg().getDpmc());
            t.setUserid(getDataprobiBaseMsg().getUserid());
            t.setYhid(getDataprobiBaseMsg().getYhid());
            t.setQd(getDataprobiBaseMsg().getQd());
            t.setValues(temp);
            return t;
        } catch (InstantiationException | IllegalAccessException | ClassCastException e) {
            logger.error(e.getMessage(), e);
        }
        return null;
    }
}
