package com.abujlb.dataprobi.processes;

import com.abujlb.dataprobi.threads.BiThreadLocals;
import com.abujlb.dataprobi.util.DataprobiDateUtil;
import org.apache.log4j.Logger;

/**
 * <AUTHOR>
 * @date 2022/5/11 15:34
 */
public interface BiDealProcess {

    Logger LOG = Logger.getLogger(BiDealProcess.class);

    default void dealAll(int type) {
        if (type != 2) {
            LOG.info(BiThreadLocals.getMsgBean().getDpmc() + "," + this.getClass().getName() + "开始：" + DataprobiDateUtil.getCurrentTime());
            dealGoods();
            dealShop();
            LOG.info(BiThreadLocals.getMsgBean().getDpmc() + "," + this.getClass().getName() + "结束：" + DataprobiDateUtil.getCurrentTime());
        }
    }

    default void dealGoods() {

    }

    default void dealShop() {

    }

    /**
     * 返回true 说明 数据相等 不需要重算
     * <p>
     * 返回false 说明 数据发生了变化  需要重算
     *
     * @return
     */
    default boolean compareGoods() {
        return true;
    }

    default boolean compareShop() {
        return true;
    }
}
