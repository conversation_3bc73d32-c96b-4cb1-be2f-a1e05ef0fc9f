package com.abujlb.dataprobi.processes.mn;

import com.abujlb.CommonConfig;
import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.mn.DataprobiMnMsg;
import com.abujlb.dataprobi.bean.mn.SqliteMNDmbztDp;
import com.abujlb.dataprobi.bean.mn.SqliteMNDmbztSp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.threads.BiThreadLocals;
import com.abujlb.dataprobi.util.MNAiztOneCSVReader;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/5/16
 */
@Component
@BiPro(order = 5, qd = Qd.MN)
public class DefaultBiMNDmbztDealProcess extends AbstractBiMNprocess {

    private final String OSSKEY_PATTERN = "bi_jysj/%s/%s/bi_aiztone_dmbzt.csv";

    @Override
    public void dealGoods() {
        super.dealGoods(SqliteMNDmbztSp.class,
                OSSKEY_PATTERN,
                ((DataprobiMnMsg) BiThreadLocals.getMsgBean()).getDmbzt()
        );
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoods(SqliteMNDmbztSp.class,
                OSSKEY_PATTERN,
                ((DataprobiMnMsg) BiThreadLocals.getMsgBean()).getDmbzt()
        );
    }

    @Override
    protected <T extends AbstractSqliteSp> void dealShopByGoods(String rq, List<T> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        final DataprobiMnMsg dataprobiMsg = (DataprobiMnMsg) getDataprobiBaseMsg();
        SqliteMNDmbztDp sqliteMNDmbztDp = new SqliteMNDmbztDp();

        sqliteMNDmbztDp.setQd(dataprobiMsg.getQd());
        sqliteMNDmbztDp.setQd_userid(dataprobiMsg.getQd() + "_" + dataprobiMsg.getUserid());
        sqliteMNDmbztDp.setRq(rq);
        sqliteMNDmbztDp.setYhid(dataprobiMsg.getYhid());
        sqliteMNDmbztDp.setUserid(dataprobiMsg.getUserid());

        int djl = list.stream().mapToInt(obj -> ((SqliteMNDmbztSp) obj).getDmbztdjl()).sum();
        int zxl = list.stream().mapToInt(obj -> ((SqliteMNDmbztSp) obj).getDmbztzxl()).sum();
        int cjbs = list.stream().mapToInt(obj -> ((SqliteMNDmbztSp) obj).getDmbztcjbs()).sum();
        double hf = list.stream().mapToDouble(obj -> ((SqliteMNDmbztSp) obj).getDmbzthf()).sum();
        double cjje = list.stream().mapToDouble(obj -> ((SqliteMNDmbztSp) obj).getDmbztcjje()).sum();

        sqliteMNDmbztDp.setDmbztzxl(zxl);
        sqliteMNDmbztDp.setDmbztdjl(djl);
        sqliteMNDmbztDp.setDmbztcjbs(cjbs);
        sqliteMNDmbztDp.setDmbzthf(hf);
        sqliteMNDmbztDp.setDmbztcjje(cjje);

        processSycmDpOTS(rq, sqliteMNDmbztDp);
    }

    @Override
    protected <T extends AbstractSqliteSp> List<T> spList(Class<T> tClass, String ossKey, String rq) {
        ossKey = String.format(OSSKEY_PATTERN, BiThreadLocals.getSupplier().getUserid(), rq);
        if (!biDataOssUtil.exist(ossKey)) {
            return Collections.emptyList();
        }

        JSONObject rule = BiThreadLocals.getMnMetaRule();
        if (rule == null || !rule.containsKey("dmbzt")) {
            return Collections.emptyList();
        }

        JSONObject dmbztRule = rule.getJSONObject("dmbzt");
        //1 按照名称匹配   2 按照计划id匹配 3 按照宝贝ID
        int ruleType = dmbztRule.getIntValue("type");
        //1 存储的是计划名称或者计划名称的部分   2 存储的是精确的计划id
        List<String> ruleVals = ruleType == 3 ? Collections.emptyList() : dmbztRule.getJSONArray("vals").stream().map(o -> (String) o).collect(Collectors.toList());
        if ((ruleType == 1 || ruleType == 2) && ruleVals.isEmpty()) {
            return Collections.emptyList();
        }

        String temppath = CommonConfig.getString("tempdir") + UUID.randomUUID() + ".csv";
        biDataOssUtil.download(ossKey, temppath);

        List<SqliteMNDmbztSp> currSupplier = MNAiztOneCSVReader.parseDmbzt(temppath, rq, ruleType, ruleVals);
        if (currSupplier.isEmpty()) {
            return Collections.emptyList();
        }

        return (List<T>) filterSpReturnNewList(currSupplier);
    }
}
