package com.abujlb.dataprobi.processes.mn;

import com.abujlb.CommonConfig;
import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.mn.DataprobiMnMsg;
import com.abujlb.dataprobi.bean.mn.SqliteMNAiztDp;
import com.abujlb.dataprobi.bean.mn.SqliteMNAiztSp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.threads.BiThreadLocals;
import com.abujlb.dataprobi.util.MNAiztOneCSVReader;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/7/4
 */
@Component
@BiPro(order = 1, qd = Qd.MN)
public class DefaultBiMNAiztDealProcess extends AbstractBiMNprocess {

    private final String OSSKEY_PATTERN = "bi_jysj/%s/%s/bi_aiztone_aizt.csv";

    @Override
    public void dealGoods() {
        super.dealGoods(SqliteMNAiztSp.class,
                OSSKEY_PATTERN,
                ((DataprobiMnMsg) BiThreadLocals.getMsgBean()).getAizt()
        );
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoods(SqliteMNAiztSp.class,
                OSSKEY_PATTERN,
                ((DataprobiMnMsg) BiThreadLocals.getMsgBean()).getAizt()
        );
    }

    @Override
    protected <T extends AbstractSqliteSp> void dealShopByGoods(String rq, List<T> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        final DataprobiMnMsg dataprobiMsg = (DataprobiMnMsg) getDataprobiBaseMsg();
        SqliteMNAiztDp sqliteMNAiztDp = new SqliteMNAiztDp();

        sqliteMNAiztDp.setQd(dataprobiMsg.getQd());
        sqliteMNAiztDp.setQd_userid(dataprobiMsg.getQd() + "_" + dataprobiMsg.getUserid());
        sqliteMNAiztDp.setRq(rq);
        sqliteMNAiztDp.setYhid(dataprobiMsg.getYhid());
        sqliteMNAiztDp.setUserid(dataprobiMsg.getUserid());

        int djl = list.stream().mapToInt(obj -> ((SqliteMNAiztSp) obj).getAiztdjl()).sum();
        int zxl = list.stream().mapToInt(obj -> ((SqliteMNAiztSp) obj).getAiztzxl()).sum();
        int cjbs = list.stream().mapToInt(obj -> ((SqliteMNAiztSp) obj).getAiztcjbs()).sum();
        double hf = list.stream().mapToDouble(obj -> ((SqliteMNAiztSp) obj).getAizthf()).sum();
        double cjje = list.stream().mapToDouble(obj -> ((SqliteMNAiztSp) obj).getAiztcjje()).sum();
        sqliteMNAiztDp.setAiztzxl(zxl);
        sqliteMNAiztDp.setAiztdjl(djl);
        sqliteMNAiztDp.setAiztcjbs(cjbs);
        sqliteMNAiztDp.setAizthf(hf);
        sqliteMNAiztDp.setAiztcjje(cjje);

        processSycmDpOTS(rq, sqliteMNAiztDp);
    }

    @Override
    protected <T extends AbstractSqliteSp> List<T> spList(Class<T> tClass, String ossKey, String rq) {
        ossKey = String.format(OSSKEY_PATTERN, BiThreadLocals.getSupplier().getUserid(), rq);
        if (!biDataOssUtil.exist(ossKey)) {
            return Collections.emptyList();
        }

        JSONObject rule = BiThreadLocals.getMnMetaRule();
        if (rule == null || !rule.containsKey("aizt")) {
            return Collections.emptyList();
        }

        JSONObject aiztRule = rule.getJSONObject("aizt");
        //1 按照名称匹配   2 按照计划id匹配 3 按照宝贝ID
        int ruleType = aiztRule.getIntValue("type");

        //1 存储的是计划名称或者计划名称的部分   2 存储的是精确的计划id
        List<String> ruleVals = ruleType == 3 ? Collections.emptyList() : aiztRule.getJSONArray("vals").stream().map(o -> (String) o).collect(Collectors.toList());
        if (((ruleType == 1 || ruleType == 2) && ruleVals.isEmpty()) || (ruleType == 3 && CollectionUtils.isEmpty(BiThreadLocals.getSupplier().getBbidList()))) {
            return Collections.emptyList();
        }


        String temppath = CommonConfig.getString("tempdir") + UUID.randomUUID() + ".csv";
        biDataOssUtil.download(ossKey, temppath);

        List<SqliteMNAiztSp> currSupplier = MNAiztOneCSVReader.parseAizt(temppath, rq, ruleType, ruleVals);
        if (currSupplier.isEmpty()) {
            return Collections.emptyList();
        }

        return (List<T>) filterSpReturnNewList(currSupplier);
    }
}
