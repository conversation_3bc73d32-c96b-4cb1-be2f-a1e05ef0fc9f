package com.abujlb.dataprobi.processes.mn;

import com.abujlb.dataprobi.bean.SqliteSp;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.threads.BiThreadLocals;
import com.abujlb.dataprobi.tsdao.SpnewTsDao;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Iterator;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/13 14:46
 */
@Component
public abstract class AbstractBiMNprocess extends AbstractBiprocess {

    @Autowired
    private SpnewTsDao sptsdao;

    protected final <T extends SqliteSp> List<T> filterSpReturnNewList(List<T> originList) {
        if (CollectionUtils.isEmpty(originList)) {
            return Collections.emptyList();
        }

        Iterator<T> iterator = originList.iterator();
        while (iterator.hasNext()) {
            //如果猫宁商品获取userid后不是当前店铺
            T next = iterator.next();
            String userid = sptsdao.getMNUseridByBbid(next.getBbid());
            if (StringUtils.isBlank(userid)
                    || !userid.equals(BiThreadLocals.getMsgBean().getUserid())) {
                iterator.remove();
                continue;
            }
        }
        return originList;
    }
}
