package com.abujlb.dataprobi.processes.mn;

import com.abujlb.CommonConfig;
import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.mn.DataprobiMnMsg;
import com.abujlb.dataprobi.bean.mn.SqliteMNZtcDp;
import com.abujlb.dataprobi.bean.mn.SqliteMNZtcSp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.threads.BiThreadLocals;
import com.abujlb.dataprobi.util.MNAiztOneCSVReader;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/7/4
 */
@Component
@BiPro(order = 3, qd = Qd.MN)
public class DefaultBiMNZtcDealProcess extends AbstractBiMNprocess {

    private final String OSSKEY_PATTERN = "bi_jysj/%s/%s/bi_aiztone_ztc.csv";

    @Override
    public void dealGoods() {
        super.dealGoods(SqliteMNZtcSp.class,
                OSSKEY_PATTERN,
                ((DataprobiMnMsg) BiThreadLocals.getMsgBean()).getZtc()
        );
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoods(SqliteMNZtcSp.class,
                OSSKEY_PATTERN,
                ((DataprobiMnMsg) BiThreadLocals.getMsgBean()).getZtc()
        );
    }

    @Override
    protected <T extends AbstractSqliteSp> void dealShopByGoods(String rq, List<T> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        final DataprobiMnMsg dataprobiMnMsg = (DataprobiMnMsg) getDataprobiBaseMsg();
        SqliteMNZtcDp sqliteMNZtcDp = new SqliteMNZtcDp();

        sqliteMNZtcDp.setQd(dataprobiMnMsg.getQd());
        sqliteMNZtcDp.setQd_userid(dataprobiMnMsg.getQd() + "_" + dataprobiMnMsg.getUserid());
        sqliteMNZtcDp.setRq(rq);
        sqliteMNZtcDp.setYhid(dataprobiMnMsg.getYhid());
        sqliteMNZtcDp.setUserid(dataprobiMnMsg.getUserid());

        int djl = list.stream().mapToInt(obj -> ((SqliteMNZtcSp) obj).getZtcdjl()).sum();
        int zxl = list.stream().mapToInt(obj -> ((SqliteMNZtcSp) obj).getZtczxl()).sum();
        int cjbs = list.stream().mapToInt(obj -> ((SqliteMNZtcSp) obj).getZtccjbs()).sum();
        double hf = list.stream().mapToDouble(obj -> ((SqliteMNZtcSp) obj).getZtchf()).sum();
        double cjje = list.stream().mapToDouble(obj -> ((SqliteMNZtcSp) obj).getZtccjje()).sum();
        sqliteMNZtcDp.setZtczxl(zxl);
        sqliteMNZtcDp.setZtcdjl(djl);
        sqliteMNZtcDp.setZtccjbs(cjbs);
        sqliteMNZtcDp.setZtchf(hf);
        sqliteMNZtcDp.setZtccjje(cjje);

        processSycmDpOTS(rq, sqliteMNZtcDp);
    }

    @Override
    protected <T extends AbstractSqliteSp> List<T> spList(Class<T> tClass, String ossKey, String rq) {
        ossKey = String.format(OSSKEY_PATTERN, BiThreadLocals.getSupplier().getUserid(), rq);
        if (!biDataOssUtil.exist(ossKey)) {
            return Collections.emptyList();
        }

        JSONObject rule = BiThreadLocals.getMnMetaRule();
        if (rule == null || !rule.containsKey("ztc")) {
            return Collections.emptyList();
        }

        JSONObject ztcRule = rule.getJSONObject("ztc");
        //1 按照名称匹配   2 按照计划id匹配 3 按照宝贝ID
        int ruleType = ztcRule.getIntValue("type");
        //1 存储的是计划名称或者计划名称的部分   2 存储的是精确的计划id
        List<String> ruleVals = ruleType == 3 ? Collections.emptyList() : ztcRule.getJSONArray("vals").stream().map(o -> (String) o).collect(Collectors.toList());
        if ((ruleType == 1 || ruleType == 2) && ruleVals.isEmpty()) {
            return Collections.emptyList();
        }

        String temppath = CommonConfig.getString("tempdir") + UUID.randomUUID() + ".csv";
        biDataOssUtil.download(ossKey, temppath);

        List<SqliteMNZtcSp> currSupplier = MNAiztOneCSVReader.parseZtc(temppath, rq, ruleType, ruleVals);
        if (CollectionUtils.isEmpty(currSupplier)) {
            return Collections.emptyList();
        }

        return (List<T>) filterSpReturnNewList(currSupplier);
    }
}
