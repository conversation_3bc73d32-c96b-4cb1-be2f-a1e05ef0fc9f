package com.abujlb.dataprobi.processes.mn;

import com.abujlb.CommonConfig;
import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.mn.DataprobiMnMsg;
import com.abujlb.dataprobi.bean.mn.SqliteMNYlmfDp;
import com.abujlb.dataprobi.bean.mn.SqliteMNYlmfSp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.threads.BiThreadLocals;
import com.abujlb.dataprobi.util.MNAiztOneCSVReader;
import com.abujlb.dataprobi.util.MathUtil;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/7/4
 */
@Component
@BiPro(order = 2, qd = Qd.MN)
public class DefaultBiMNYlmfDealProcess extends AbstractBiMNprocess {

    private final String OSSKEY_PATTERN = "bi_jysj/%s/%s/bi_aiztone_ylmf.csv";

    @Override
    public void dealGoods() {
        super.dealGoods(SqliteMNYlmfSp.class,
                OSSKEY_PATTERN,
                ((DataprobiMnMsg) BiThreadLocals.getMsgBean()).getYlmf()
        );
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoods(SqliteMNYlmfSp.class,
                OSSKEY_PATTERN,
                ((DataprobiMnMsg) BiThreadLocals.getMsgBean()).getYlmf()
        );
    }

    @Override
    protected <T extends AbstractSqliteSp> void dealShopByGoods(String rq, List<T> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        final DataprobiMnMsg dataprobiMsg = (DataprobiMnMsg) getDataprobiBaseMsg();
        SqliteMNYlmfDp sqliteMNYlmfDp = new SqliteMNYlmfDp();

        sqliteMNYlmfDp.setQd(dataprobiMsg.getQd());
        sqliteMNYlmfDp.setQd_userid(dataprobiMsg.getQd() + "_" + dataprobiMsg.getUserid());
        sqliteMNYlmfDp.setRq(rq);
        sqliteMNYlmfDp.setYhid(dataprobiMsg.getYhid());
        sqliteMNYlmfDp.setUserid(dataprobiMsg.getUserid());

        int djl = list.stream().mapToInt(obj -> ((SqliteMNYlmfSp) obj).getYlmfdjl()).sum();
        int zxl = list.stream().mapToInt(obj -> ((SqliteMNYlmfSp) obj).getYlmfzxl()).sum();
        int cjbs = list.stream().mapToInt(obj -> ((SqliteMNYlmfSp) obj).getYlmfcjbs()).sum();
        double hf = list.stream().mapToDouble(obj -> ((SqliteMNYlmfSp) obj).getYlmfhf()).sum();
        double cjje = list.stream().mapToDouble(obj -> ((SqliteMNYlmfSp) obj).getYlmfcjje()).sum();
        long zrllbgl = list.stream().mapToLong(obj -> ((SqliteMNYlmfSp) obj).getYlmfzrllbgl()).sum();
        double zrllzhje = list.stream().mapToDouble(obj -> ((SqliteMNYlmfSp) obj).getYlmfzrllzhje()).sum();
        double gyzhje = list.stream().mapToDouble(obj -> ((SqliteMNYlmfSp) obj).getYlmfgyzhje()).sum();
        double gyroi = MathUtil.calZhlv(gyzhje, hf);

        sqliteMNYlmfDp.setYlmfzxl(zxl);
        sqliteMNYlmfDp.setYlmfdjl(djl);
        sqliteMNYlmfDp.setYlmfcjbs(cjbs);
        sqliteMNYlmfDp.setYlmfhf(hf);
        sqliteMNYlmfDp.setYlmfcjje(cjje);
        sqliteMNYlmfDp.setYlmfzrllbgl(zrllbgl);
        sqliteMNYlmfDp.setYlmfzrllzhje(zrllzhje);
        sqliteMNYlmfDp.setYlmfgyzhje(gyzhje);
        sqliteMNYlmfDp.setYlmfgyroi(gyroi);

        processSycmDpOTS(rq, sqliteMNYlmfDp);
    }

    @Override
    protected <T extends AbstractSqliteSp> List<T> spList(Class<T> tClass, String ossKey, String rq) {
        ossKey = String.format(OSSKEY_PATTERN, BiThreadLocals.getSupplier().getUserid(), rq);
        if (!biDataOssUtil.exist(ossKey)) {
            return Collections.emptyList();
        }

        JSONObject rule = BiThreadLocals.getMnMetaRule();
        if (rule == null || !rule.containsKey("ylmf")) {
            return Collections.emptyList();
        }

        JSONObject ylmfRule = rule.getJSONObject("ylmf");
        //1 按照名称匹配   2 按照计划id匹配 3 按照宝贝ID
        int ruleType = ylmfRule.getIntValue("type");
        //1 存储的是计划名称或者计划名称的部分   2 存储的是精确的计划id
        List<String> ruleVals = ruleType == 3 ? Collections.emptyList() : ylmfRule.getJSONArray("vals").stream().map(o -> (String) o).collect(Collectors.toList());
        if ((ruleType == 1 || ruleType == 2) && ruleVals.isEmpty()) {
            return Collections.emptyList();
        }

        String temppath = CommonConfig.getString("tempdir") + UUID.randomUUID() + ".csv";
        biDataOssUtil.download(ossKey, temppath);

        List<SqliteMNYlmfSp> currSupplier = MNAiztOneCSVReader.parseYlmf(temppath, rq, ruleType, ruleVals);
        if (currSupplier.isEmpty()) {
            return Collections.emptyList();
        }

        return (List<T>) filterSpReturnNewList(currSupplier);
    }
}
