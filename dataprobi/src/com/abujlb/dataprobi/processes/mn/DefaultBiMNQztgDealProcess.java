package com.abujlb.dataprobi.processes.mn;

import com.abujlb.CommonConfig;
import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.mn.DataprobiMnMsg;
import com.abujlb.dataprobi.bean.mn.SqliteMNQztgDp;
import com.abujlb.dataprobi.bean.mn.SqliteMNQztgSp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.threads.BiThreadLocals;
import com.abujlb.dataprobi.util.MNAiztOneCSVReader;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/5/16
 */
@Component
@BiPro(order = 4, qd = Qd.MN)
public class DefaultBiMNQztgDealProcess extends AbstractBiMNprocess {

    private final String OSSKEY_PATTERN = "bi_jysj/%s/%s/bi_aiztone_qztg.csv";

    @Override
    public void dealGoods() {
        super.dealGoods(SqliteMNQztgSp.class,
                OSSKEY_PATTERN,
                ((DataprobiMnMsg) BiThreadLocals.getMsgBean()).getQztg()
        );
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoods(SqliteMNQztgSp.class,
                OSSKEY_PATTERN,
                ((DataprobiMnMsg) BiThreadLocals.getMsgBean()).getQztg()
        );
    }

    @Override
    protected <T extends AbstractSqliteSp> void dealShopByGoods(String rq, List<T> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        final DataprobiMnMsg dataprobiMsg = (DataprobiMnMsg) getDataprobiBaseMsg();
        SqliteMNQztgDp sqliteMNQztgDp = new SqliteMNQztgDp();

        sqliteMNQztgDp.setQd(dataprobiMsg.getQd());
        sqliteMNQztgDp.setQd_userid(dataprobiMsg.getQd() + "_" + dataprobiMsg.getUserid());
        sqliteMNQztgDp.setRq(rq);
        sqliteMNQztgDp.setYhid(dataprobiMsg.getYhid());
        sqliteMNQztgDp.setUserid(dataprobiMsg.getUserid());

        int djl = list.stream().mapToInt(obj -> ((SqliteMNQztgSp) obj).getQztgdjl()).sum();
        int zxl = list.stream().mapToInt(obj -> ((SqliteMNQztgSp) obj).getQztgzxl()).sum();
        int cjbs = list.stream().mapToInt(obj -> ((SqliteMNQztgSp) obj).getQztgcjbs()).sum();
        double hf = list.stream().mapToDouble(obj -> ((SqliteMNQztgSp) obj).getQztghf()).sum();
        double cjje = list.stream().mapToDouble(obj -> ((SqliteMNQztgSp) obj).getQztgcjje()).sum();

        sqliteMNQztgDp.setQztgzxl(zxl);
        sqliteMNQztgDp.setQztgdjl(djl);
        sqliteMNQztgDp.setQztgcjbs(cjbs);
        sqliteMNQztgDp.setQztghf(hf);
        sqliteMNQztgDp.setQztgcjje(cjje);

        processSycmDpOTS(rq, sqliteMNQztgDp);
    }

    @Override
    protected <T extends AbstractSqliteSp> List<T> spList(Class<T> tClass, String ossKey, String rq) {
        ossKey = String.format(OSSKEY_PATTERN, BiThreadLocals.getSupplier().getUserid(), rq);
        if (!biDataOssUtil.exist(ossKey)) {
            return Collections.emptyList();
        }

        JSONObject rule = BiThreadLocals.getMnMetaRule();
        if (rule == null || !rule.containsKey("qztg")) {
            return Collections.emptyList();
        }

        JSONObject qztgRule = rule.getJSONObject("qztg");
        //1 按照名称匹配   2 按照计划id匹配 3 按照宝贝ID
        int ruleType = qztgRule.getIntValue("type");
        //1 存储的是计划名称或者计划名称的部分   2 存储的是精确的计划id
        List<String> ruleVals = ruleType == 3 ? Collections.emptyList() : qztgRule.getJSONArray("vals").stream().map(o -> (String) o).collect(Collectors.toList());
        if ((ruleType == 1 || ruleType == 2) && ruleVals.isEmpty()) {
            return Collections.emptyList();
        }

        String temppath = CommonConfig.getString("tempdir") + UUID.randomUUID() + ".csv";
        biDataOssUtil.download(ossKey, temppath);

        List<SqliteMNQztgSp> currSupplier = MNAiztOneCSVReader.parseQztg(temppath, rq, ruleType, ruleVals);
        if (currSupplier.isEmpty()) {
            return Collections.emptyList();
        }

        return (List<T>) filterSpReturnNewList(currSupplier);
    }
}
