package com.abujlb.dataprobi.processes.dy;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.dy.DataprobiDyMsgBean;
import com.abujlb.dataprobi.bean.dy.SqliteDyDrZyQytgDp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import org.springframework.stereotype.Component;

/**
 * 抖音自营-达人-巨量千川-全域推广处理
 *
 * <AUTHOR>
 * @date 2023/12/5
 */
@Component
@BiPro(order = 11, qd = Qd.DY)
public class DefaultBiDrZyJlqcQytgProcess extends AbstractBiDyProcess {

    private final String[] COLUMNS = {BiSycmDpDataTsDao.DY_COLUMN_DR_ZY_QYTGDP_ZY, BiSycmDpDataTsDao.DY_COLUMN_DR_ZY_QYTGDP_DR};

    @Override
    public void dealShop() {
        super.dealShop(
                SqliteDyDrZyQytgDp.class,
                ((DataprobiDyMsgBean) getDataprobiBaseMsg()).getDr_zy_qytgdp(),
                COLUMNS
        );
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqliteDyDrZyQytgDp.class,
                ((DataprobiDyMsgBean) getDataprobiBaseMsg()).getDr_zy_qytgdp(),
                COLUMNS
        );
    }
}
