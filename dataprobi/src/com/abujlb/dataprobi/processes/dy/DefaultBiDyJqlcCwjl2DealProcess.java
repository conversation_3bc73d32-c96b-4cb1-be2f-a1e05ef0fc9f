package com.abujlb.dataprobi.processes.dy;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteDp;
import com.abujlb.dataprobi.bean.dy.DataprobiDyMsgBean;
import com.abujlb.dataprobi.bean.dy.SqliteDyJlqcCwjl2Dp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/4/15
 *
 */
@Component
@BiPro(order = 7, qd = Qd.DY)
public class DefaultBiDyJqlcCwjl2DealProcess extends AbstractBiDyProcess {

    private final String[] COLUMNS = {BiSycmDpDataTsDao.DY_COLUMN_JLQC_CWJL2};

    @Override
    public void dealShop() {
        super.dealShop(SqliteDyJlqcCwjl2Dp.class, ((DataprobiDyMsgBean) getDataprobiBaseMsg()).getCwjldp2(), COLUMNS);
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(SqliteDyJlqcCwjl2Dp.class, ((DataprobiDyMsgBean) getDataprobiBaseMsg()).getCwjldp2(), COLUMNS);
    }

    @Override
    protected <T extends AbstractSqliteDp> T dpObject(Class<T> tClass, String rq, String... columns) {
        T t = super.dpObject(tClass, rq, columns);
        if (t instanceof SqliteDyJlqcCwjl2Dp) {
            SqliteDyJlqcCwjl2Dp sqliteDyJlqcCwjlDp = (SqliteDyJlqcCwjl2Dp) t;
            sqliteDyJlqcCwjlDp.setQcfy(-sqliteDyJlqcCwjlDp.getQcfy());
        }
        return t;
    }
}
