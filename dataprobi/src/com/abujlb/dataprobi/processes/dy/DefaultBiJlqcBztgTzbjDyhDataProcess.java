package com.abujlb.dataprobi.processes.dy;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.dy.DataprobiDyMsgBean;
import com.abujlb.dataprobi.bean.dy.jlqc.BiJlqcAvvid;
import com.abujlb.dataprobi.bean.dy.jlqc.BiJlqcBztgTzbjDyh;
import com.abujlb.dataprobi.bean.dy.jlqc.BiJlqcBztgTzbjDyhData;
import com.abujlb.dataprobi.constant.DataprobiConst;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.sql.BiSqliteDb;
import com.abujlb.dataprobi.threads.BiThreadLocals;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.abujlb.dataprobi.util.FileUtil;
import com.abujlb.dataprobi.util.SqliteDbUtil;
import com.abujlb.sqlite.SqliteStatement;
import com.abujlb.util.StringUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 千川-标准推广-推直播间-抖音号 明细处理
 *
 * <AUTHOR>
 * @date 2025/2/14
 */
@Component
@BiPro(order = 99, qd = Qd.DY)
public class DefaultBiJlqcBztgTzbjDyhDataProcess extends AbstractBiDyProcess {


    final String init_sql = "CREATE TABLE `dy_jlqc_bztg_tzbj_dyh` (`qcid` TEXT,`dyh` TEXT,`data` TEXT) ;";
    final String insert_sql = "insert into dy_jlqc_bztg_tzbj_dyh(`qcid`,`dyh`,`data`) values({qcid},{dyh},{data}) ;";

    @Override
    public void dealGoods() {
        List<String> rqList = ((DataprobiDyMsgBean) getDataprobiBaseMsg()).getBztg_tzbj_dyh();
        for (String rq : rqList) {
            List<BiJlqcBztgTzbjDyh> list = getBztgTzbjDyhList(rq);
            if (CollectionUtils.isEmpty(list)) {
                continue;
            }

            savedb(list, rq);
        }
    }

    @Override
    public boolean compareGoods() {
        boolean result = true;
        List<String> rqList = ((DataprobiDyMsgBean) getDataprobiBaseMsg()).getBztg_tzbj_dyh();
        for (String rq : rqList) {
            String dbKey = "bi_data/day/" + rq.replace("-", "") + "/" + getDataprobiBaseMsg().getYhid() + "/DY/" + getDataprobiBaseMsg().getUserid() + "/dy_jlqc_bztg_tzbj_dyh.db";
            List<BiJlqcBztgTzbjDyh> list = getBztgTzbjDyhList(rq);
            if (CollectionUtils.isEmpty(list)) {
                if (biDataOssUtil.exist(dbKey)) {
                    biDataOssUtil.delete(dbKey);
                    if (result) {
                        result = false;
                    }
                }
                continue;
            }

            if (!biDataOssUtil.exist(dbKey)) {
                if (result) {
                    result = false;
                }
                savedb(list, rq);
                continue;
            }

            if (!result) {
                if (biDataOssUtil.exist(dbKey)) {
                    biDataOssUtil.delete(dbKey);
                }
                savedb(list, rq);
                continue;
            }

            String db_path = FileUtil.createDBFilePath();
            biDataOssUtil.download(dbKey, db_path);

            BiSqliteDb sqliteDb = null;
            SqliteStatement<BiJlqcBztgTzbjDyh> sqliteStatement = null;
            try {
                sqliteDb = new BiSqliteDb(db_path);
                sqliteStatement = sqliteDb.createSqliteStatement("select * from dy_jlqc_bztg_tzbj_dyh ;", BiJlqcBztgTzbjDyh.class);
                List<BiJlqcBztgTzbjDyh> list2 = sqliteStatement.queryForList(null, BiJlqcBztgTzbjDyh.class);
                for (BiJlqcBztgTzbjDyh biJlqcBztgTzbjDyh : list2) {
                    biJlqcBztgTzbjDyh.setDataObj(DataprobiConst.GSON.fromJson(biJlqcBztgTzbjDyh.getData(), BiJlqcBztgTzbjDyhData.class));
                }

                for (BiJlqcBztgTzbjDyh biJlqcBztgTzbjDyh : list) {
                    Optional<BiJlqcBztgTzbjDyh> any = list2.stream().filter(temp -> temp.getQcid().equals(biJlqcBztgTzbjDyh.getQcid()) && temp.getDyh().equals(biJlqcBztgTzbjDyh.getDyh())).findAny();
                    if (!any.isPresent()) {
                        result = false;
                        break;
                    }


                    BiJlqcBztgTzbjDyh temp = any.get();
                    BiJlqcBztgTzbjDyhData dataObj = temp.getDataObj();
                    if (dataChange(biJlqcBztgTzbjDyh.getDataObj(), dataObj)) {
                        result = false;
                        break;
                    }
                }

                if (result) {
                    for (BiJlqcBztgTzbjDyh biJlqcBztgTzbjDyh : list2) {
                        Optional<BiJlqcBztgTzbjDyh> any = list.stream().filter(temp -> temp.getQcid().equals(biJlqcBztgTzbjDyh.getQcid()) && temp.getDyh().equals(biJlqcBztgTzbjDyh.getDyh())).findAny();
                        if (!any.isPresent()) {
                            result = false;
                            break;
                        }


                        BiJlqcBztgTzbjDyh temp = any.get();
                        BiJlqcBztgTzbjDyhData dataObj = temp.getDataObj();
                        if (dataChange(biJlqcBztgTzbjDyh.getDataObj(), dataObj)) {
                            result = false;
                            break;
                        }
                    }
                }

                if (!result) {
                    biDataOssUtil.delete(dbKey);
                    savedb(list, rq);
                }
            } catch (Exception e) {
                logger.error(e.getMessage(), e);
            }
        }
        return result;
    }

    private boolean dataChange(BiJlqcBztgTzbjDyhData data1, BiJlqcBztgTzbjDyhData data2) {
        if (data1 == null && data2 == null) {
            return false;
        } else if (data1 == null || data2 == null) {
            return true;
        } else return data1.getHf() != data2.getHf() || data1.getCjje() != data2.getCjje();
    }

    private void savedb(List<BiJlqcBztgTzbjDyh> list, String rq) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        String db_path = FileUtil.createDBFilePath();
        BiSqliteDb biSqliteDb = null;
        SqliteStatement<BiJlqcBztgTzbjDyh> sqliteStatement = null;
        try {
            biSqliteDb = new BiSqliteDb(db_path, init_sql, null);
            sqliteStatement = biSqliteDb.createSqliteStatement(insert_sql, BiJlqcBztgTzbjDyh.class);
            biSqliteDb.startTransaction();
            sqliteStatement.insert(list);
            biSqliteDb.commit();

            File fileD = biSqliteDb.getFileD();
            biDataOssUtil.upload(fileD, "bi_data/day/" + rq.replace("-", "") + "/" + getDataprobiBaseMsg().getYhid() + "/DY/" + getDataprobiBaseMsg().getUserid() + "/dy_jlqc_bztg_tzbj_dyh.db");
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return;
        } finally {
            SqliteDbUtil.closeSqliteStatements(sqliteStatement);
            SqliteDbUtil.close(biSqliteDb);
        }
    }

    private List<BiJlqcBztgTzbjDyh> getBztgTzbjDyhList(String rq) {
        List<BiJlqcBztgTzbjDyh> list = new ArrayList<>();
        try {
            for (BiJlqcAvvid biJlqcAvvid : BiThreadLocals.getDyJlqcList()) {
                String jsonKey = "bi_dy_jlqc/" + biJlqcAvvid.getAvvid() + "/" + rq + "/bztg_tzbj_dyh.json";
                String jsonStr = biDataOssUtil.readJson(jsonKey);
                if (!StringUtil.isJsonArray2(jsonStr)) {
                    return list;
                }
                JSONArray jsonArray = JSONArray.parseArray(jsonStr);
                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONObject jsonObject = jsonArray.getJSONObject(i);
                    JSONObject dimensions = jsonObject.getJSONObject("Dimensions");
                    String anchor_id = FastJSONObjAttrToNumber.toString(dimensions, "anchor_id", "ValueStr");
                    JSONArray rows = jsonObject.getJSONArray("Rows");
                    for (int j = 0; j < rows.size(); j++) {
                        JSONObject row = rows.getJSONObject(j);
                        String rq2 = FastJSONObjAttrToNumber.toString(row, "Dimensions", "stat_time_day", "ValueStr");
                        if (rq2 != null && rq2.equals(rq)) {
                            double hf = FastJSONObjAttrToNumber.toDouble(row, "Metrics", "stat_cost", "Value");
                            double cjje = FastJSONObjAttrToNumber.toDouble(row, "Metrics", "pay_order_amount", "Value");
                            BiJlqcBztgTzbjDyhData data = new BiJlqcBztgTzbjDyhData();
                            data.setCjje(cjje);
                            data.setHf(hf);

                            BiJlqcBztgTzbjDyh dyh = new BiJlqcBztgTzbjDyh();
                            dyh.setData(DataprobiConst.GSON.toJson(data));
                            dyh.setDataObj(data);
                            dyh.setDyh(anchor_id);
                            dyh.setQcid(biJlqcAvvid.getAvvid());
                            list.add(dyh);
                        }
                    }
                }
            }
            return list;
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return null;
        }
    }
}
