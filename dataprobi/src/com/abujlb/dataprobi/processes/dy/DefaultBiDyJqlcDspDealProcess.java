package com.abujlb.dataprobi.processes.dy;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.dy.DataprobiDyMsgBean;
import com.abujlb.dataprobi.bean.dy.SqliteDyJlqcDspDp;
import com.abujlb.dataprobi.bean.dy.SqliteDyJlqcDspSp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/5/11 15:49
 */
@Component
@BiPro(order = 6, qd = Qd.DY)
public class DefaultBiDyJqlcDspDealProcess extends AbstractBiDyProcess {

    private final String OSSKEY_PATTERN = "bi_dy_jlqc/%s/%s/tsp.json";
    private final String[] COLUMNS = {BiSycmDpDataTsDao.DY_COLUMN_DSP};

    @Override
    public void dealGoods() {
        super.dealJlqcGoods(
                SqliteDyJlqcDspSp.class,
                OSSKEY_PATTERN,
                ((DataprobiDyMsgBean) getDataprobiBaseMsg()).getDsp()
        );
    }

    @Override
    public void dealShop() {
        super.dealShop(
                SqliteDyJlqcDspDp.class,
                ((DataprobiDyMsgBean) getDataprobiBaseMsg()).getDspdp(),
                COLUMNS
        );
    }

    @Override
    public boolean compareGoods() {
        return super.compareJlqcGoods(
                SqliteDyJlqcDspSp.class,
                OSSKEY_PATTERN,
                ((DataprobiDyMsgBean) getDataprobiBaseMsg()).getDsp()
        );
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqliteDyJlqcDspDp.class,
                ((DataprobiDyMsgBean) getDataprobiBaseMsg()).getDspdp(),
                COLUMNS
        );
    }
}
