package com.abujlb.dataprobi.processes.dy;

import com.abujlb.dataprobi.bean.AbstractSqliteDp;
import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.dy.jlqc.BiJlqcAvvid;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.threads.BiThreadLocals;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/1/11
 */
@Component
public class AbstractBiDyProcess extends AbstractBiprocess {

    protected <T extends AbstractSqliteSp> void dealJlqcGoods(Class<T> tClass, String ossKeyFormat, List<String> rqList) {
        List<BiJlqcAvvid> dyJlqcList = BiThreadLocals.getDyJlqcList();
        if (CollectionUtils.isEmpty(dyJlqcList)) {
            return;
        }

        for (String rq : rqList) {
            List<T> list = new ArrayList<>();
            for (BiJlqcAvvid biJlqcAvvid : dyJlqcList) {
                String ossKey = String.format(ossKeyFormat, biJlqcAvvid.getAvvid(), rq);
                List<T> temp = spList(tClass, ossKey, rq);
                if (CollectionUtils.isEmpty(temp)) {
                    continue;
                }
                list.addAll(temp);
            }
            if (CollectionUtils.isEmpty(list)) {
                continue;
            }
            //按照bbid分组
            Map<String, List<T>> map = list.stream().collect(Collectors.groupingBy(T::getBbid));
            List<T> newList = new ArrayList<>(map.size());
            for (Map.Entry<String, List<T>> entry : map.entrySet()) {
                try {
                    T temp = tClass.newInstance();
                    for (T t : entry.getValue()) {
                        if (temp.getBbid() == null) {
                            BeanUtils.copyProperties(t, temp);
                            continue;
                        }
                        temp.plus(t);
                    }
                    newList.add(temp);
                } catch (Exception e) {
                    logger.error(e.getMessage(), e);
                }
            }
            processSycmSpOTS(rq, newList);

            dealShopByGoods(rq, newList);
        }
    }

    protected <T extends AbstractSqliteSp> boolean compareJlqcGoods(Class<T> tClass, String ossKeyFormat, List<String> rqList) {
        if (CollectionUtils.isEmpty(rqList)) {
            return true;
        }
        dealJlqcGoods(tClass, ossKeyFormat, rqList);
        return false;
    }

    protected <T extends AbstractSqliteDp> T dpObject(Class<T> tClass, String rq, String... columns) {
        T t = null;
        try {
            List<BiJlqcAvvid> dyJlqcList = BiThreadLocals.getDyJlqcList();
            if (CollectionUtils.isEmpty(dyJlqcList)) {
                return null;
            }
            t = tClass.newInstance();
            t.setQd(BiThreadLocals.getMsgBean().getQd());
            t.setQd_userid(BiThreadLocals.getMsgBean().getQd() + "_" + BiThreadLocals.getMsgBean().getUserid());
            t.setYhid(BiThreadLocals.getMsgBean().getYhid());
            t.setUserid(BiThreadLocals.getMsgBean().getUserid());
            t.setRq(rq);

            for (BiJlqcAvvid biJlqcAvvid : dyJlqcList) {
                String[] return_values = biSycmDpDataTsDao.getRow("DY_JLQC_" + biJlqcAvvid.getAvvid(), rq, columns);
                T tTemp = tClass.newInstance();
                tTemp.setValues(return_values);
                t.plus(tTemp);
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return t;
    }
}
