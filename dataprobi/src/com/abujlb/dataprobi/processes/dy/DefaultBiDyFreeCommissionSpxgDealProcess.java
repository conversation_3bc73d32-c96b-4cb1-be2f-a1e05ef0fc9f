package com.abujlb.dataprobi.processes.dy;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.dy.DataprobiDyMsgBean;
import com.abujlb.dataprobi.bean.dy.SqliteDyCommissionSpxgSp;
import com.abujlb.dataprobi.bean.dy.SqliteDyCommssionSpxgDp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.util.DySpxgXlsReader;
import com.abujlb.dataprobi.util.FileUtil;
import com.abujlb.dataprobi.util.MathUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 预估-免佣
 *
 * <AUTHOR>
 * @date 2023/12/13
 */
@Component
@BiPro(order = 15, qd = Qd.DY)
public class DefaultBiDyFreeCommissionSpxgDealProcess extends AbstractBiprocess {

    //售卖类型：全部  载体渠道：全部
    private final String SPXG_ALL = "bi_dy/%s/%s/spxg_all.xlsx";
    //售卖类型：全部  载体渠道：直播
    private final String SPXG_ZB = "bi_dy/%s/%s/spxg_zb.xlsx";
    //售卖类型：全部  载体渠道：短视频
    private final String SPXG_DSP = "bi_dy/%s/%s/spxg_dsp.xlsx";
    //售卖类型：全部  载体渠道：商品卡
    private final String SPXG_SPK = "bi_dy/%s/%s/spxg_spk.xlsx";
    //售卖类型：全部  载体渠道：图文
    private final String SPXG_TW = "bi_dy/%s/%s/spxg_tw.xlsx";

    private final List<String> OSS_LIST = Arrays.asList(SPXG_ALL, SPXG_ZB, SPXG_DSP, SPXG_SPK, SPXG_TW);

    @Override
    public void dealGoods() {
        super.dealGoods(
                SqliteDyCommissionSpxgSp.class,
                SPXG_ALL,
                ((DataprobiDyMsgBean) getDataprobiBaseMsg()).getFreeCommissionSpxg()
        );
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoods(
                SqliteDyCommissionSpxgSp.class,
                SPXG_ALL,
                ((DataprobiDyMsgBean) getDataprobiBaseMsg()).getFreeCommissionSpxg()
        );
    }

    @Override
    protected <T extends AbstractSqliteSp> List<T> spList(Class<T> tClass, String ossKey, String rq) {
        List<SqliteDyCommissionSpxgSp> spList = spList(rq);
        if (CollectionUtils.isEmpty(spList)) {
            return null;
        }

        List<T> list2 = new ArrayList<>(spList.size());
        for (SqliteDyCommissionSpxgSp sqliteDySpxgSp : spList) {
            try {
                T t = tClass.newInstance();
                BeanUtils.copyProperties(sqliteDySpxgSp, t);
                list2.add(t);
            } catch (Exception e) {
                logger.error(e.getMessage(), e);
            }
        }
        return list2;
    }

    @Override
    protected <T extends AbstractSqliteSp> void dealShopByGoods(String rq, List<T> list) {
        SqliteDyCommssionSpxgDp dpData = new SqliteDyCommssionSpxgDp();

        //商品成交金额
        double spxgAllZfje = 0;
        //商品退款金额
        double spxgAllTkje = 0;
        //商品成交订单数
        int spxgAllCjdds = 0;
        //商品成交人数
        int spxgAllCjrs = 0;
        //商品成交件数
        int spxgAllCjjs = 0;
        //商品退款订单数
        int spxgAllTkdds = 0;
        //商品退款人数
        int spxgAllTkrs = 0;
        //商品退款件数
        int spxgAllTkjs = 0;

        //商品直播成交金额
        double spxgZbCjje = 0;
        //商品直播退款金额
        double spxgZbTkje = 0;
        //商品直播成交订单数
        int spxgZbCjdds = 0;
        //商品直播成交人数
        int spxgZbCjrs = 0;
        //商品直播成交件数
        int spxgZbCjjs = 0;
        //商品直播退款订单数
        int spxgZbTkdds = 0;
        //商品直播退款人数
        int spxgZbTkrs = 0;
        //商品直播退款件数
        int spxgZbTkjs = 0;

        //商品短视频成交金额
        double spxgDspCjje = 0;
        //商品短视频退款金额
        double spxgDspTkje = 0;
        //商品短视频成交订单数
        int spxgDspCjdds = 0;
        //商品短视频成交人数
        int spxgDspCjrs = 0;
        //商品短视频成交件数
        int spxgDspCjjs = 0;
        //商品短视频退款订单数
        int spxgDspTkdds = 0;
        //商品短视频退款人数
        int spxgDspTkrs = 0;
        //商品短视频退款件数
        int spxgDspTkjs = 0;

        //商品商品卡成交金额
        double spxgSpkCjje = 0;
        //商品商品卡退款金额
        double spxgSpkTkje = 0;
        //商品商品卡成交订单数
        int spxgSpkCjdds = 0;
        //商品商品卡成交人数
        int spxgSpkCjrs = 0;
        //商品商品卡成交件数
        int spxgSpkCjjs = 0;
        //商品商品卡退款订单数
        int spxgSpkTkdds = 0;
        //商品商品卡退款人数
        int spxgSpkTkrs = 0;
        //商品商品卡退款件数
        int spxgSpkTkjs = 0;

        //商品图文成交金额
        double spxgTwCjje = 0;
        //商品图文退款金额
        double spxgTwTkje = 0;
        //商品图文成交订单数
        int spxgTwCjdds = 0;
        //商品图文成交人数
        int spxgTwCjrs = 0;
        //商品图文成交件数
        int spxgTwCjjs = 0;
        //商品图文退款订单数
        int spxgTwTkdds = 0;
        //商品图文退款人数
        int spxgTwTkrs = 0;
        //商品图文退款件数
        int spxgTwTkjs = 0;
        for (T t : list) {
            if (t instanceof SqliteDyCommissionSpxgSp) {
                SqliteDyCommissionSpxgSp obj = (SqliteDyCommissionSpxgSp) t;

                //商品成交金额
                spxgAllZfje = MathUtil.add(spxgAllZfje, obj.getSpxgAllZfje());
                //商品退款金额
                spxgAllTkje = MathUtil.add(spxgAllTkje, obj.getSpxgAllTkje());
                //商品成交订单数
                spxgAllCjdds += obj.getSpxgAllCjdds();
                //商品成交人数
                spxgAllCjrs += obj.getSpxgAllCjrs();
                //商品成交件数
                spxgAllCjjs += obj.getSpxgAllCjjs();
                //商品退款订单数
                spxgAllTkdds += obj.getSpxgAllTkdds();
                //商品退款人数
                spxgAllTkrs += obj.getSpxgAllTkrs();
                //商品退款件数
                spxgAllTkjs += obj.getSpxgAllTkjs();

                //商品直播成交金额
                spxgZbCjje = MathUtil.add(spxgZbCjje, obj.getSpxgZbCjje());
                //商品直播退款金额
                spxgZbTkje = MathUtil.add(spxgZbTkje, obj.getSpxgZbTkje());
                //商品直播成交订单数
                spxgZbCjdds += obj.getSpxgZbCjdds();
                //商品直播成交人数
                spxgZbCjrs += obj.getSpxgZbCjrs();
                //商品直播成交件数
                spxgZbCjjs += obj.getSpxgZbCjjs();
                //商品直播退款订单数
                spxgZbTkdds += obj.getSpxgZbTkdds();
                //商品直播退款人数
                spxgZbTkrs += obj.getSpxgZbTkrs();
                //商品直播退款件数
                spxgZbTkjs += obj.getSpxgZbTkjs();

                //商品短视频成交金额
                spxgDspCjje = MathUtil.add(spxgDspCjje, obj.getSpxgDspCjje());
                //商品短视频退款金额
                spxgDspTkje = MathUtil.add(spxgDspTkje, obj.getSpxgDspTkje());
                //商品短视频成交订单数
                spxgDspCjdds += obj.getSpxgDspCjdds();
                //商品短视频成交人数
                spxgDspCjrs += obj.getSpxgDspCjrs();
                //商品短视频成交件数
                spxgDspCjjs += obj.getSpxgDspCjjs();
                //商品短视频退款订单数
                spxgDspTkdds += obj.getSpxgDspTkdds();
                //商品短视频退款人数
                spxgDspTkrs += obj.getSpxgDspTkrs();
                //商品短视频退款件数
                spxgDspTkjs += obj.getSpxgDspTkjs();

                //商品商品卡成交金额
                spxgSpkCjje = MathUtil.add(spxgSpkCjje, obj.getSpxgSpkCjje());
                //商品商品卡退款金额
                spxgSpkTkje = MathUtil.add(spxgSpkTkje, obj.getSpxgSpkTkje());
                //商品商品卡成交订单数
                spxgSpkCjdds += obj.getSpxgSpkCjdds();
                //商品商品卡成交人数
                spxgSpkCjrs += obj.getSpxgSpkCjrs();
                //商品商品卡成交件数
                spxgSpkCjjs += obj.getSpxgSpkCjjs();
                //商品商品卡退款订单数
                spxgSpkTkdds += obj.getSpxgSpkTkdds();
                //商品商品卡退款人数
                spxgSpkTkrs += obj.getSpxgSpkTkrs();
                //商品商品卡退款件数
                spxgSpkTkjs += obj.getSpxgSpkTkjs();

                //商品图文成交金额
                spxgTwCjje = MathUtil.add(spxgTwCjje, obj.getSpxgTwCjje());
                //商品图文退款金额
                spxgTwTkje = MathUtil.add(spxgTwTkje, obj.getSpxgTwTkje());
                //商品图文成交订单数
                spxgTwCjdds += obj.getSpxgTwCjdds();
                //商品图文成交人数
                spxgTwCjrs += obj.getSpxgTwCjrs();
                //商品图文成交件数
                spxgTwCjjs += obj.getSpxgTwCjjs();
                //商品图文退款订单数
                spxgTwTkdds += obj.getSpxgTwTkdds();
                //商品图文退款人数
                spxgTwTkrs += obj.getSpxgTwTkrs();
                //商品图文退款件数
                spxgTwTkjs += obj.getSpxgTwTkjs();
            }
        }

        dpData.setSpxgAllZfje(spxgAllZfje);
        dpData.setSpxgAllCjdds(spxgAllCjdds);
        dpData.setSpxgAllCjrs(spxgAllCjrs);
        dpData.setSpxgAllCjjs(spxgAllCjjs);
        dpData.setSpxgAllTkje(spxgAllTkje);
        dpData.setSpxgAllTkdds(spxgAllTkdds);
        dpData.setSpxgAllTkrs(spxgAllTkrs);
        dpData.setSpxgAllTkjs(spxgAllTkjs);

        dpData.setSpxgZbCjje(spxgZbCjje);
        dpData.setSpxgZbCjdds(spxgZbCjdds);
        dpData.setSpxgZbCjrs(spxgZbCjrs);
        dpData.setSpxgZbCjjs(spxgZbCjjs);
        dpData.setSpxgZbTkje(spxgZbTkje);
        dpData.setSpxgZbTkdds(spxgZbTkdds);
        dpData.setSpxgZbTkrs(spxgZbTkrs);
        dpData.setSpxgZbTkjs(spxgZbTkjs);

        dpData.setSpxgDspCjje(spxgDspCjje);
        dpData.setSpxgDspCjdds(spxgDspCjdds);
        dpData.setSpxgDspCjrs(spxgDspCjrs);
        dpData.setSpxgDspCjjs(spxgDspCjjs);
        dpData.setSpxgDspTkje(spxgDspTkje);
        dpData.setSpxgDspTkdds(spxgDspTkdds);
        dpData.setSpxgDspTkrs(spxgDspTkrs);
        dpData.setSpxgDspTkjs(spxgDspTkjs);

        dpData.setSpxgSpkCjje(spxgSpkCjje);
        dpData.setSpxgSpkCjdds(spxgSpkCjdds);
        dpData.setSpxgSpkCjrs(spxgSpkCjrs);
        dpData.setSpxgSpkCjjs(spxgSpkCjjs);
        dpData.setSpxgSpkTkje(spxgSpkTkje);
        dpData.setSpxgSpkTkdds(spxgSpkTkdds);
        dpData.setSpxgSpkTkrs(spxgSpkTkrs);
        dpData.setSpxgSpkTkjs(spxgSpkTkjs);

        dpData.setSpxgTwCjje(spxgTwCjje);
        dpData.setSpxgTwCjdds(spxgTwCjdds);
        dpData.setSpxgTwCjrs(spxgTwCjrs);
        dpData.setSpxgTwCjjs(spxgTwCjjs);
        dpData.setSpxgTwTkje(spxgTwTkje);
        dpData.setSpxgTwTkdds(spxgTwTkdds);
        dpData.setSpxgTwTkrs(spxgTwTkrs);
        dpData.setSpxgTwTkjs(spxgTwTkjs);

        dpData.setQd_userid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid());
        dpData.setDpmc(getDataprobiBaseMsg().getDpmc());
        dpData.setUserid(getDataprobiBaseMsg().getUserid());
        dpData.setYhid(getDataprobiBaseMsg().getYhid());
        dpData.setQd(getDataprobiBaseMsg().getQd());
        dpData.setRq(rq);

        processSycmDpOTS(rq, dpData);
    }

    private List<SqliteDyCommissionSpxgSp> spList(String rq) {
        Map<String, SqliteDyCommissionSpxgSp> map = new HashMap<>();

        for (String osspattern : OSS_LIST) {
            String ossKey = String.format(osspattern, getDataprobiBaseMsg().getUserid(), rq);
            if (biDataOssUtil.exist(ossKey)) {
                String filePath = FileUtil.createXlsFilePath();
                biDataOssUtil.download(ossKey, filePath);

                String[] split = ossKey.split("/");
                String type = split[split.length - 1].replaceAll(".xlsx", "").split("_")[1];
                DySpxgXlsReader.parseCommissionXLS(map, type, filePath);
            }
        }

        if (map.isEmpty()) {
            return null;
        }

        for (SqliteDyCommissionSpxgSp sqliteDySpxgSp : map.values()) {
            sqliteDySpxgSp.setQd_userid_bbid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid() + "_" + sqliteDySpxgSp.getBbid());
            sqliteDySpxgSp.setRq(rq);
            sqliteDySpxgSp.setYhid(getDataprobiBaseMsg().getYhid());
            sqliteDySpxgSp.setQd(getDataprobiBaseMsg().getQd());
            sqliteDySpxgSp.setUserid(getDataprobiBaseMsg().getUserid());
        }

        return new ArrayList<>(map.values());
    }
}
