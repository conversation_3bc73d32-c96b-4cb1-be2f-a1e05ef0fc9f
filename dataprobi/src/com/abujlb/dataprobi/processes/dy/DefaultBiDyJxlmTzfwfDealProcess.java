package com.abujlb.dataprobi.processes.dy;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.dy.DataprobiDyMsgBean;
import com.abujlb.dataprobi.bean.dy.SqliteDyJxlmTzfwfDp;
import com.abujlb.dataprobi.bean.dy.SqliteDyJxlmTzfwfSp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.util.MathUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/31
 */
@Component
@BiPro(order = 3, qd = Qd.DY)
public class DefaultBiDyJxlmTzfwfDealProcess extends AbstractBiprocess {

    private final String OSSKEY_PATTERN = "bi_dy/%s/%s/jxlm_tzfwf.json";

    @Override
    public void dealGoods() {
        super.dealGoods(SqliteDyJxlmTzfwfSp.class, OSSKEY_PATTERN, ((DataprobiDyMsgBean) getDataprobiBaseMsg()).getJxlm_tzfwf());
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoods(SqliteDyJxlmTzfwfSp.class, OSSKEY_PATTERN, ((DataprobiDyMsgBean) getDataprobiBaseMsg()).getJxlm_tzfwf());
    }

    @Override
    protected <T extends AbstractSqliteSp> void dealShopByGoods(String rq, List<T> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        double hf = 0;
        double cjje = 0;
        for (T t : list) {
            if (t instanceof SqliteDyJxlmTzfwfSp) {
                hf = MathUtil.add(hf, ((SqliteDyJxlmTzfwfSp) t).getTzfwfhf());
                cjje = MathUtil.add(cjje, ((SqliteDyJxlmTzfwfSp) t).getTzfwfcjje());
            }
        }

        SqliteDyJxlmTzfwfDp dpData = new SqliteDyJxlmTzfwfDp();
        dpData.setQd(getDataprobiBaseMsg().getQd());
        dpData.setQd_userid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid());
        dpData.setRq(rq);
        dpData.setYhid(getDataprobiBaseMsg().getYhid());
        dpData.setUserid(getDataprobiBaseMsg().getUserid());
        dpData.setTzfwfcjje(cjje);
        dpData.setTzfwfhf(hf);
        processSycmDpOTS(rq, dpData);
    }
}
