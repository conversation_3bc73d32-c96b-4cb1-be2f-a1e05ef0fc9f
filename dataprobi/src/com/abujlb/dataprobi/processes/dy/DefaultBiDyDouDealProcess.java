package com.abujlb.dataprobi.processes.dy;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.BiInfoOther;
import com.abujlb.dataprobi.bean.DataprobiBaseMsgBean;
import com.abujlb.dataprobi.bean.dy.DataprobiDyMsgBean;
import com.abujlb.dataprobi.bean.dy.SqliteDyDouDp;
import com.abujlb.dataprobi.bean.dy.SqliteDyDouSp;
import com.abujlb.dataprobi.bean.hfshare.HfShareBase;
import com.abujlb.dataprobi.bean.hfshare.HfShareGoodsConfig;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.server.DataUploader;
import com.abujlb.dataprobi.tsdao.BiTghfConfigTsDao;
import com.abujlb.dataprobi.util.MathUtil;
import com.abujlb.util.StringUtil;
import com.alibaba.acm.shaded.com.google.common.reflect.TypeToken;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Component;

import java.lang.reflect.Type;
import java.util.*;
import java.util.stream.Collectors;

/**
 * dou+数据处理
 *
 * <AUTHOR>
 * @date 2023/9/7
 */
@Component
@BiPro(order = 8, qd = Qd.DY)
@EnableAsync
public class DefaultBiDyDouDealProcess extends AbstractBiprocess {

    @Autowired
    private BiTghfConfigTsDao biTghfConfigTsDao;

    private static final Type TYPE = new TypeToken<List<HfShareGoodsConfig>>() {
    }.getType();

    @Override
    public void dealGoods() {
        DataprobiBaseMsgBean msgBean = getDataprobiBaseMsg();
        //获取所有的dou+
        List<BiInfoOther> biInfoOthers = DataUploader.getAllUserid(msgBean.getBiinfoId(), "OTHER_DOU+");
        if (CollectionUtils.isEmpty(biInfoOthers)) {
            return;
        }

        //查询当前店铺下所有的dou+下的所有抖音号商品的分摊比例
        List<HfShareBase> hfShareBaseList = biTghfConfigTsDao.getRows(BiTghfConfigTsDao.LX_DY_DJIA, getDataprobiBaseMsg().getYhid(), "DY", getDataprobiBaseMsg().getUserid());
        for (String rq : ((DataprobiDyMsgBean) msgBean).getDou()) {
            //店铺纬度花费
            double dphf = 0;
            //商品纬度花费  key：bbid  value：bbid对应的dou+花费
            Map<String, SqliteDyDouSp> map = new HashMap<>();

            for (BiInfoOther biInfoOther : biInfoOthers) {
                //当前dou+下有哪些抖音号需要分摊
                String rule = biInfoOther.getRule();
                List<String> rules = Arrays.asList(rule.split(","));
                if (CollectionUtils.isEmpty(rules)) {
                    continue;
                }

                String key = "bi_dy/" + msgBean.getUserid() + "/" + rq + "/dou/" + biInfoOther.getUserid() + "_dou.json";
                String jsonStr = biDataOssUtil.readJson(key);
                if (!StringUtil.isJsonArray2(jsonStr)) {
                    continue;
                }

                List<HfShareBase> currDouConfigList = hfShareBaseList.stream().filter(hfShareBase -> hfShareBase.getDjid().equals(String.valueOf(biInfoOther.getUserid()))).collect(Collectors.toList());

                //当前dou+ 当前rq 所有抖音号的数据
                JSONArray jsonArray = JSONArray.parseArray(jsonStr);
                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONObject jsonObject = jsonArray.getJSONObject(i);
                    String dyh = jsonObject.getJSONObject("userInfo").getString("uniqueId");
                    double currhf = jsonObject.getJSONObject("stats").getDoubleValue("stat_cost");
                    if (!rules.contains(dyh)) {
                        continue;
                    }

                    //店铺纬度dou+花费
                    dphf = MathUtil.add(dphf, currhf);

                    //分摊商品
                    List<HfShareBase> tempList = hfShareBaseList.stream().filter(temp ->
                            temp.getDyh().equals(dyh)
                                    && (((StringUtils.isBlank(temp.getKsrq()) && StringUtils.isBlank(temp.getJsrq()))
                                    || ((StringUtils.isBlank(temp.getKsrq()) && StringUtils.isNotBlank(temp.getJsrq()) && rq.compareTo(temp.getJsrq()) <= 0))
                                    || ((StringUtils.isBlank(temp.getJsrq()) && StringUtils.isNotBlank(temp.getKsrq()) && rq.compareTo(temp.getKsrq()) >= 0))
                                    || (StringUtils.isNotBlank(temp.getKsrq()) && StringUtils.isNotBlank(temp.getJsrq()) && rq.compareTo(temp.getJsrq()) <= 0 && rq.compareTo(temp.getKsrq()) >= 0))
                            )
                    ).collect(Collectors.toList());

                    if (CollectionUtils.isEmpty(tempList) || tempList.size() > 1) {
                        continue;
                    }

                    HfShareBase tempHfShare = tempList.get(0);

                    List<HfShareGoodsConfig> configList = tempHfShare.getConfigList();
                    if (CollectionUtils.isEmpty(configList)) {
                        continue;
                    }

                    for (HfShareGoodsConfig hfShareGoodsConfig : configList) {
                        double ratio = hfShareGoodsConfig.getRatio();

                        SqliteDyDouSp sqliteDyDouSp = map.getOrDefault(hfShareGoodsConfig.getGoodsId(), new SqliteDyDouSp());
                        sqliteDyDouSp.setQd(getDataprobiBaseMsg().getQd());
                        sqliteDyDouSp.setQd_userid_bbid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid() + "_" + hfShareGoodsConfig.getGoodsId());
                        sqliteDyDouSp.setBbid(hfShareGoodsConfig.getGoodsId());
                        sqliteDyDouSp.setYhid(getDataprobiBaseMsg().getYhid());
                        sqliteDyDouSp.setUserid(getDataprobiBaseMsg().getUserid());
                        sqliteDyDouSp.setRq(rq);

                        sqliteDyDouSp.setDoujiahf(MathUtil.add(sqliteDyDouSp.getDoujiahf(), MathUtil.multipy(currhf, ratio)));

                        map.put(hfShareGoodsConfig.getGoodsId(), sqliteDyDouSp);
                    }
                }
            }

            if (dphf != 0) {
                //处理到店铺db
                SqliteDyDouDp sqliteDyDouDp = new SqliteDyDouDp();
                sqliteDyDouDp.setQd(msgBean.getQd());
                sqliteDyDouDp.setQd_userid(msgBean.getQd() + "_" + msgBean.getUserid());
                sqliteDyDouDp.setRq(rq);
                sqliteDyDouDp.setYhid(msgBean.getYhid());
                sqliteDyDouDp.setUserid(msgBean.getUserid());
                sqliteDyDouDp.setDoujiahf(dphf);
                processSycmDpOTS(rq, sqliteDyDouDp);
            }

            //处理到商品db
            List<SqliteDyDouSp> list = new ArrayList<>(map.values());
            processSycmSpOTS(rq, list);
        }
    }
}
