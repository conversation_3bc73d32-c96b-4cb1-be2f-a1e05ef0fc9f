package com.abujlb.dataprobi.processes.dy;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.dy.DataprobiDyMsgBean;
import com.abujlb.dataprobi.bean.dy.SqliteDyDrZyTzbjDp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import org.springframework.stereotype.Component;

/**
 * 抖音自营-达人-巨量千川-推直播间处理
 *
 * <AUTHOR>
 * @date 2023/12/5
 */
@Component
@BiPro(order = 9, qd = Qd.DY)
public class DefaultBiDrZyJlqcTzbjProcess extends AbstractBiDyProcess {

    private final String[] COLUMNS = {BiSycmDpDataTsDao.DY_COLUMN_DR_ZY_TZBJDP_DR, BiSycmDpDataTsDao.DY_COLUMN_DR_ZY_TZBJDP_ZY};

    @Override
    public void dealShop() {
        super.dealShop(
                SqliteDyDrZyTzbjDp.class,
                ((DataprobiDyMsgBean) getDataprobiBaseMsg()).getDr_zy_tzbjdp(),
                COLUMNS
        );
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqliteDyDrZyTzbjDp.class,
                ((DataprobiDyMsgBean) getDataprobiBaseMsg()).getDr_zy_tzbjdp(),
                COLUMNS
        );
    }
}
