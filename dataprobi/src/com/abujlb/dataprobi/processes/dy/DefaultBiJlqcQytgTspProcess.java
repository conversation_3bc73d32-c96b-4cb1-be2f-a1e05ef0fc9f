package com.abujlb.dataprobi.processes.dy;


import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.dy.DataprobiDyMsgBean;
import com.abujlb.dataprobi.bean.dy.SqliteDyJlqcQytgTspDp;
import com.abujlb.dataprobi.bean.dy.SqliteDyJlqcQytgTspSp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.util.MathUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 全域推广-推商品
 */
@Component
@BiPro(order = 13, qd = Qd.DY)
public class DefaultBiJlqcQytgTspProcess extends AbstractBiDyProcess {


    private final String OSSKEY_PATTERN = "bi_dy_jlqc/%s/%s/qytg_tsp.json";

    @Override
    public void dealGoods() {
        super.dealJlqcGoods(
                SqliteDyJlqcQytgTspSp.class,
                OSSKEY_PATTERN,
                ((DataprobiDyMsgBean) getDataprobiBaseMsg()).getQytgtsp()
        );
    }


    @Override
    public boolean compareGoods() {
        return super.compareJlqcGoods(
                SqliteDyJlqcQytgTspSp.class,
                OSSKEY_PATTERN,
                ((DataprobiDyMsgBean) getDataprobiBaseMsg()).getQytgtsp()
        );
    }


    @Override
    protected <T extends AbstractSqliteSp> void dealShopByGoods(String rq, List<T> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        //汇总店铺纬度
        SqliteDyJlqcQytgTspDp sqliteDyJlqcQytgTspDp = new SqliteDyJlqcQytgTspDp();
        sqliteDyJlqcQytgTspDp.setQd(getDataprobiBaseMsg().getQd());
        sqliteDyJlqcQytgTspDp.setUserid(getDataprobiBaseMsg().getUserid());
        sqliteDyJlqcQytgTspDp.setQd_userid(sqliteDyJlqcQytgTspDp.getQd() + "_" + sqliteDyJlqcQytgTspDp.getUserid());
        sqliteDyJlqcQytgTspDp.setYhid(getDataprobiBaseMsg().getYhid());
        sqliteDyJlqcQytgTspDp.setRq(rq);

        double qytgtsphf = 0;
        double qytgtspcjje = 0;
        double qytgtsp_znyhqcjje = 0;
        for (T t : list) {
            qytgtspcjje = MathUtil.add(qytgtspcjje, ((SqliteDyJlqcQytgTspSp) t).getQytgtspcjje());
            qytgtsphf = MathUtil.add(qytgtsphf, ((SqliteDyJlqcQytgTspSp) t).getQytgtsphf());
            qytgtsp_znyhqcjje = MathUtil.add(qytgtsp_znyhqcjje, ((SqliteDyJlqcQytgTspSp) t).getQytgtsp_znyhqcjje());
        }
        sqliteDyJlqcQytgTspDp.setQytgtspcjje(qytgtspcjje);
        sqliteDyJlqcQytgTspDp.setQytgtsphf(qytgtsphf);
        sqliteDyJlqcQytgTspDp.setQytgtsp_znyhqcjje(qytgtsp_znyhqcjje);
        if (qytgtsphf == 0 && qytgtspcjje == 0 && qytgtsp_znyhqcjje == 0) {
            return;
        }
        processSycmDpOTS(rq, sqliteDyJlqcQytgTspDp);
    }
}
