package com.abujlb.dataprobi.processes.dy;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.dy.DataprobiDyMsgBean;
import com.abujlb.dataprobi.bean.dy.SqliteDySpxgDp;
import com.abujlb.dataprobi.bean.dy.SqliteDySpxgSp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.util.DySpxgXlsReader;
import com.abujlb.dataprobi.util.FileUtil;
import com.abujlb.dataprobi.util.MathUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/11 15:49
 */
@Component
@BiPro(order = 1, qd = Qd.DY)
public class DefaultBiDySpxgDealProcess extends AbstractBiprocess {

    private final String OSSKEY_PATTERN = "bi_dy/%s/%s/spxg.xlsx";
    private final String OSSKEY_PATTERN2 = "bi_dy/%s/%s/spxg2.xlsx";

    @Override
    public void dealGoods() {
        super.dealGoods(
                SqliteDySpxgSp.class,
                OSSKEY_PATTERN,
                ((DataprobiDyMsgBean) getDataprobiBaseMsg()).getSpxg()
        );
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoods(
                SqliteDySpxgSp.class,
                OSSKEY_PATTERN,
                ((DataprobiDyMsgBean) getDataprobiBaseMsg()).getSpxg()
        );
    }

    private void dealShop(String rq, List<SqliteDySpxgSp> list) {
        SqliteDySpxgDp sqliteDySpxgDp = new SqliteDySpxgDp();
        for (SqliteDySpxgSp sqliteDySpxgSp : list) {
            sqliteDySpxgDp.setSpxgzfje(MathUtil.add(sqliteDySpxgDp.getSpxgzfje(), sqliteDySpxgSp.getSpxgzfje()));
            sqliteDySpxgDp.setSpxgtkje(MathUtil.add(sqliteDySpxgDp.getSpxgtkje(), sqliteDySpxgSp.getSpxgtkje()));
            sqliteDySpxgDp.setSpxgdds(sqliteDySpxgDp.getSpxgdds() + sqliteDySpxgSp.getSpxgdds());
            sqliteDySpxgDp.setSpxgzfmjs(sqliteDySpxgDp.getSpxgzfmjs() + sqliteDySpxgSp.getSpxgzfmjs());
            sqliteDySpxgDp.setSpxglll(sqliteDySpxgDp.getSpxglll() + sqliteDySpxgSp.getSpxglll());
            sqliteDySpxgDp.setSpxgsplll(sqliteDySpxgDp.getSpxglll());
            sqliteDySpxgDp.setSpxgfks(sqliteDySpxgDp.getSpxgfks() + sqliteDySpxgSp.getSpxgfks());
            sqliteDySpxgDp.setSpxgspfks(sqliteDySpxgDp.getSpxgfks());
            //抖音 商品点击-支付转化率(次数) 公式为：成交订单数/点击次数(相当于这里的访客数)
            //但是实际计算 还是按照淘系的计算  公式为：支付买家数/访客数
            sqliteDySpxgDp.setSpxgzfzhl(MathUtil.calZhlv(sqliteDySpxgDp.getSpxgzfmjs(), sqliteDySpxgDp.getSpxgfks()));
            sqliteDySpxgDp.setSpxgzfjs(sqliteDySpxgDp.getSpxgzfjs() + sqliteDySpxgSp.getSpxgzfjs());
        }
        sqliteDySpxgDp.setQd(getDataprobiBaseMsg().getQd());
        sqliteDySpxgDp.setQd_userid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid());
        sqliteDySpxgDp.setRq(rq);
        sqliteDySpxgDp.setYhid(getDataprobiBaseMsg().getYhid());
        sqliteDySpxgDp.setUserid(getDataprobiBaseMsg().getUserid());

        processSycmDpOTS(rq, sqliteDySpxgDp);
    }

    @Override
    protected <T extends AbstractSqliteSp> List<T> spList(Class<T> tClass, String ossKey, String rq) {
        List<SqliteDySpxgSp> sqliteDySpxgSps = spList(rq);
        if (CollectionUtils.isEmpty(sqliteDySpxgSps)) {
            return null;
        }

        List<T> list2 = new ArrayList<>(sqliteDySpxgSps.size());
        for (SqliteDySpxgSp sqliteDySpxgSp : sqliteDySpxgSps) {
            try {
                T t = tClass.newInstance();
                BeanUtils.copyProperties(sqliteDySpxgSp, t);
                list2.add(t);
            } catch (Exception e) {
                logger.error(e.getMessage(), e);
            }
        }
        return list2;
    }

    private List<SqliteDySpxgSp> spList(String rq) {
        List<SqliteDySpxgSp> list = null;
        String ossKey = String.format(OSSKEY_PATTERN, getDataprobiBaseMsg().getUserid(), rq);
        if (biDataOssUtil.exist(ossKey)) {
            String filePath = FileUtil.createXlsFilePath();
            biDataOssUtil.download(ossKey, filePath);
            list = DySpxgXlsReader.parseXLS(filePath);
        }

        String ossKey2 = String.format(OSSKEY_PATTERN2, getDataprobiBaseMsg().getUserid(), rq);
        if (biDataOssUtil.exist(ossKey2)) {
            String filePath = FileUtil.createXlsFilePath();
            biDataOssUtil.download(ossKey2, filePath);
            list = DySpxgXlsReader.parseXLSV2(filePath);
        }

        if (CollectionUtils.isEmpty(list)) {
            return list;
        }

        for (SqliteDySpxgSp sqliteDySpxgSp : list) {
            sqliteDySpxgSp.setQd_userid_bbid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid() + "_" + sqliteDySpxgSp.getBbid());
            sqliteDySpxgSp.setRq(rq);
            sqliteDySpxgSp.setYhid(getDataprobiBaseMsg().getYhid());
            sqliteDySpxgSp.setQd(getDataprobiBaseMsg().getQd());
            sqliteDySpxgSp.setUserid(getDataprobiBaseMsg().getUserid());
        }

        //汇总店铺纬度
        dealShop(rq, list);

        return list;
    }
}
