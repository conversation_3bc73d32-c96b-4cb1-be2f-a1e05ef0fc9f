package com.abujlb.dataprobi.processes.dy;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteDp;
import com.abujlb.dataprobi.bean.dy.DataprobiDyMsgBean;
import com.abujlb.dataprobi.bean.dy.SqliteDyJlqcCwjlDp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/8/1
 */
@Component
@BiPro(order = 7, qd = Qd.DY)
public class DefaultBiDyJqlcCwjlDealProcess extends AbstractBiDyProcess {

    private final String[] COLUMNS = {BiSycmDpDataTsDao.DY_COLUMN_JLQC_CWJL};

    @Override
    public void dealShop() {
        super.dealShop(SqliteDyJlqcCwjlDp.class, ((DataprobiDyMsgBean) getDataprobiBaseMsg()).getCwjldp(), COLUMNS);
    }


    @Override
    public boolean compareShop() {
        return super.compareShop(SqliteDyJlqcCwjlDp.class, ((DataprobiDyMsgBean) getDataprobiBaseMsg()).getCwjldp(), COLUMNS);
    }

    @Override
    protected <T extends AbstractSqliteDp> T dpObject(Class<T> tClass, String rq, String... columns) {
        T t = super.dpObject(tClass, rq, columns);
        if (t instanceof SqliteDyJlqcCwjlDp) {
            SqliteDyJlqcCwjlDp sqliteDyJlqcCwjlDp = (SqliteDyJlqcCwjlDp) t;
            sqliteDyJlqcCwjlDp.setLjhbxh(-sqliteDyJlqcCwjlDp.getLjhbxh());
        }
        return t;
    }
}
