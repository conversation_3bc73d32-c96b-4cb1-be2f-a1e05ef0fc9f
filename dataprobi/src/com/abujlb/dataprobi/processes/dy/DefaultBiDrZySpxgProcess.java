package com.abujlb.dataprobi.processes.dy;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.dy.DataprobiDyMsgBean;
import com.abujlb.dataprobi.bean.dy.SqliteDyDrZySpxgDp;
import com.abujlb.dataprobi.bean.dy.SqliteDyDrZySpxgSp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.util.DySpxgXlsReader;
import com.abujlb.dataprobi.util.FileUtil;
import com.abujlb.dataprobi.util.MathUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 抖音自营-达人商品效果处理
 *
 * <AUTHOR>
 * @date 2023/12/5
 */
@Component
@BiPro(order = 8, qd = Qd.DY)
public class DefaultBiDrZySpxgProcess extends AbstractBiprocess {


    //没有实际意义
    private final String OSSKEY_PATTERN = "bi_dy/%s/%s/spxg.xlsx";

    //售卖类型：合作  载体渠道：全部
    private final String DR_OSSKEY_PATTERN = "bi_dy/%s/%s/spxg_dr.xlsx";
    //售卖类型：合作  载体渠道：直播
    private final String DR_ZB_OSSKEY_PATTERN = "bi_dy/%s/%s/spxg_drzb.xlsx";
    //售卖类型：合作  载体渠道：短视频
    private final String DR_DSP_OSSKEY_PATTERN = "bi_dy/%s/%s/spxg_drdsp.xlsx";
    //售卖类型：合作  载体渠道：商品卡
    private final String DR_SPK_OSSKEY_PATTERN = "bi_dy/%s/%s/spxg_drspk.xlsx";
    //售卖类型：合作  载体渠道：图文
    private final String DR_TW_OSSKEY_PATTERN = "bi_dy/%s/%s/spxg_drtw.xlsx";

    //售卖类型：自营  载体渠道：全部
    private final String ZY_OSSKEY_PATTERN = "bi_dy/%s/%s/spxg_zy.xlsx";
    //售卖类型：自营  载体渠道：直播
    private final String ZY_ZB_OSSKEY_PATTERN = "bi_dy/%s/%s/spxg_zyzb.xlsx";
    //售卖类型：自营  载体渠道：短视频
    private final String ZY_DSP_OSSKEY_PATTERN = "bi_dy/%s/%s/spxg_zydsp.xlsx";
    //售卖类型：自营  载体渠道：商品卡
    private final String ZY_SPK_OSSKEY_PATTERN = "bi_dy/%s/%s/spxg_zyspk.xlsx";
    //售卖类型：自营  载体渠道：图文
    private final String ZY_TW_OSSKEY_PATTERN = "bi_dy/%s/%s/spxg_zytw.xlsx";

    private final List<String> OSS_LIST = Arrays.asList(DR_OSSKEY_PATTERN, DR_ZB_OSSKEY_PATTERN, DR_DSP_OSSKEY_PATTERN, DR_SPK_OSSKEY_PATTERN, DR_TW_OSSKEY_PATTERN
            , ZY_OSSKEY_PATTERN, ZY_ZB_OSSKEY_PATTERN, ZY_DSP_OSSKEY_PATTERN, ZY_SPK_OSSKEY_PATTERN, ZY_TW_OSSKEY_PATTERN);

    @Override
    public void dealGoods() {
        super.dealGoods(
                SqliteDyDrZySpxgSp.class,
                OSSKEY_PATTERN,
                ((DataprobiDyMsgBean) getDataprobiBaseMsg()).getDr_zy_spxg()
        );
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoods(
                SqliteDyDrZySpxgSp.class,
                OSSKEY_PATTERN,
                ((DataprobiDyMsgBean) getDataprobiBaseMsg()).getDr_zy_spxg()
        );
    }

    @Override
    protected <T extends AbstractSqliteSp> List<T> spList(Class<T> tClass, String ossKey, String rq) {
        List<SqliteDyDrZySpxgSp> spList = spList(rq);
        if (CollectionUtils.isEmpty(spList)) {
            return null;
        }

        List<T> list2 = new ArrayList<>(spList.size());
        for (SqliteDyDrZySpxgSp sqliteDySpxgSp : spList) {
            try {
                T t = tClass.newInstance();
                BeanUtils.copyProperties(sqliteDySpxgSp, t);
                list2.add(t);
            } catch (Exception e) {
                logger.error(e.getMessage(), e);
            }
        }
        return list2;
    }

    private List<SqliteDyDrZySpxgSp> spList(String rq) {
        Map<String, SqliteDyDrZySpxgSp> map = new HashMap<>();

        for (String osspattern : OSS_LIST) {
            String ossKey = String.format(osspattern, getDataprobiBaseMsg().getUserid(), rq);
            if (biDataOssUtil.exist(ossKey)) {
                String filePath = FileUtil.createXlsFilePath();
                biDataOssUtil.download(ossKey, filePath);

                String[] split = ossKey.split("/");
                String type = split[split.length - 1].replaceAll(".xlsx", "").split("_")[1];
                DySpxgXlsReader.parseXLSZydr(map, type, filePath);
            }
        }

        if (map.isEmpty()) {
            return null;
        }

        for (SqliteDyDrZySpxgSp sqliteDySpxgSp : map.values()) {
            sqliteDySpxgSp.setQd_userid_bbid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid() + "_" + sqliteDySpxgSp.getBbid());
            sqliteDySpxgSp.setRq(rq);
            sqliteDySpxgSp.setYhid(getDataprobiBaseMsg().getYhid());
            sqliteDySpxgSp.setQd(getDataprobiBaseMsg().getQd());
            sqliteDySpxgSp.setUserid(getDataprobiBaseMsg().getUserid());
        }

        ArrayList<SqliteDyDrZySpxgSp> sqliteDyDrZySpxgSps = new ArrayList<>(map.values());
        //汇总店铺纬度
        SqliteDyDrZySpxgDp sqliteDyDrZySpxgDp = new SqliteDyDrZySpxgDp();
        sqliteDyDrZySpxgDp.setQd(getDataprobiBaseMsg().getQd());
        sqliteDyDrZySpxgDp.setUserid(getDataprobiBaseMsg().getUserid());
        sqliteDyDrZySpxgDp.setQd_userid(sqliteDyDrZySpxgDp.getQd() + "_" + sqliteDyDrZySpxgDp.getUserid());
        sqliteDyDrZySpxgDp.setYhid(getDataprobiBaseMsg().getYhid());
        sqliteDyDrZySpxgDp.setRq(rq);
        for (SqliteDyDrZySpxgSp sqliteDyDrZySpxgSp : sqliteDyDrZySpxgSps) {
            //字段相加

            ///////////以下是达人部分------------------->>>
            //达人支付金额
            sqliteDyDrZySpxgDp.setSpxgDrcjje(MathUtil.add(sqliteDyDrZySpxgDp.getSpxgDrcjje(), sqliteDyDrZySpxgSp.getSpxgDrcjje()));
            //达人成交人数
            sqliteDyDrZySpxgDp.setSpxgDrcjrs(sqliteDyDrZySpxgDp.getSpxgDrcjrs() + sqliteDyDrZySpxgSp.getSpxgDrcjrs());
            //达人成交订单数
            sqliteDyDrZySpxgDp.setSpxgDrcjdds(sqliteDyDrZySpxgDp.getSpxgDrcjdds() + sqliteDyDrZySpxgSp.getSpxgDrcjdds());
            //达人成交件数
            sqliteDyDrZySpxgDp.setSpxgDrcjjs(sqliteDyDrZySpxgDp.getSpxgDrcjjs() + sqliteDyDrZySpxgSp.getSpxgDrcjjs());
            //达人退款金额
            sqliteDyDrZySpxgDp.setSpxgDrtkje(MathUtil.add(sqliteDyDrZySpxgDp.getSpxgDrtkje(), sqliteDyDrZySpxgSp.getSpxgDrtkje()));

            //达人直播成交金额
            sqliteDyDrZySpxgDp.setSpxgDrzbcjje(MathUtil.add(sqliteDyDrZySpxgDp.getSpxgDrzbcjje(), sqliteDyDrZySpxgSp.getSpxgDrzbcjje()));
            //达人直播成交人数
            sqliteDyDrZySpxgDp.setSpxgDrzbcjrs(sqliteDyDrZySpxgDp.getSpxgDrzbcjrs() + sqliteDyDrZySpxgSp.getSpxgDrzbcjrs());
            //达人直播成交订单数
            sqliteDyDrZySpxgDp.setSpxgDrzbcjdds(sqliteDyDrZySpxgDp.getSpxgDrzbcjdds() + sqliteDyDrZySpxgSp.getSpxgDrzbcjdds());
            //达人直播成交件数
            sqliteDyDrZySpxgDp.setSpxgDrzbcjjs(sqliteDyDrZySpxgDp.getSpxgDrzbcjjs() + sqliteDyDrZySpxgSp.getSpxgDrzbcjjs());
            //达人直播退款金额
            sqliteDyDrZySpxgDp.setSpxgDrzbtkje(MathUtil.add(sqliteDyDrZySpxgDp.getSpxgDrzbtkje(), sqliteDyDrZySpxgSp.getSpxgDrzbtkje()));


            //达人短视频支付金额
            sqliteDyDrZySpxgDp.setSpxgDrdspcjje(MathUtil.add(sqliteDyDrZySpxgDp.getSpxgDrdspcjje(), sqliteDyDrZySpxgSp.getSpxgDrdspcjje()));
            //达人短视频成交人数
            sqliteDyDrZySpxgDp.setSpxgDrdspcjrs(sqliteDyDrZySpxgDp.getSpxgDrdspcjrs() + sqliteDyDrZySpxgSp.getSpxgDrdspcjrs());
            //达人短视频成交订单数
            sqliteDyDrZySpxgDp.setSpxgDrdspcjdds(sqliteDyDrZySpxgDp.getSpxgDrdspcjdds() + sqliteDyDrZySpxgSp.getSpxgDrdspcjdds());
            //达人短视频成交件数
            sqliteDyDrZySpxgDp.setSpxgDrdspcjjs(sqliteDyDrZySpxgDp.getSpxgDrdspcjjs() + sqliteDyDrZySpxgSp.getSpxgDrdspcjjs());
            //达人短视频退款金额
            sqliteDyDrZySpxgDp.setSpxgDrdsptkje(MathUtil.add(sqliteDyDrZySpxgDp.getSpxgDrdsptkje(), sqliteDyDrZySpxgSp.getSpxgDrdsptkje()));


            //达人图文支付金额
            sqliteDyDrZySpxgDp.setSpxgDrtwcjje(MathUtil.add(sqliteDyDrZySpxgDp.getSpxgDrtwcjje(), sqliteDyDrZySpxgSp.getSpxgDrtwcjje()));
            //达人图文成交人数
            sqliteDyDrZySpxgDp.setSpxgDrtwcjrs(sqliteDyDrZySpxgDp.getSpxgDrtwcjrs() + sqliteDyDrZySpxgSp.getSpxgDrtwcjrs());
            //达人图文成交订单数
            sqliteDyDrZySpxgDp.setSpxgDrtwcjdds(sqliteDyDrZySpxgDp.getSpxgDrtwcjdds() + sqliteDyDrZySpxgSp.getSpxgDrtwcjdds());
            //达人图文成交件数
            sqliteDyDrZySpxgDp.setSpxgDrtwcjjs(sqliteDyDrZySpxgDp.getSpxgDrtwcjjs() + sqliteDyDrZySpxgSp.getSpxgDrtwcjjs());
            //达人图文退款金额
            sqliteDyDrZySpxgDp.setSpxgDrtwtkje(MathUtil.add(sqliteDyDrZySpxgDp.getSpxgDrtwtkje(), sqliteDyDrZySpxgSp.getSpxgDrtwtkje()));


            //达人商品卡支付金额
            sqliteDyDrZySpxgDp.setSpxgDrspkcjje(MathUtil.add(sqliteDyDrZySpxgDp.getSpxgDrspkcjje(), sqliteDyDrZySpxgSp.getSpxgDrspkcjje()));
            //达人商品卡成交人数
            sqliteDyDrZySpxgDp.setSpxgDrspkcjrs(sqliteDyDrZySpxgDp.getSpxgDrspkcjrs() + sqliteDyDrZySpxgSp.getSpxgDrspkcjrs());
            //达人商品卡成交订单数
            sqliteDyDrZySpxgDp.setSpxgDrspkcjdds(sqliteDyDrZySpxgDp.getSpxgDrspkcjdds() + sqliteDyDrZySpxgSp.getSpxgDrspkcjdds());
            //达人商品卡成交件数
            sqliteDyDrZySpxgDp.setSpxgDrspkcjjs(sqliteDyDrZySpxgDp.getSpxgDrspkcjjs() + sqliteDyDrZySpxgSp.getSpxgDrspkcjjs());
            //达人商品卡退款金额
            sqliteDyDrZySpxgDp.setSpxgDrspktkje(MathUtil.add(sqliteDyDrZySpxgDp.getSpxgDrspktkje(), sqliteDyDrZySpxgSp.getSpxgDrspktkje()));

            ///////////以下是自营部分------------------>>>>

            //自营支付金额
            sqliteDyDrZySpxgDp.setSpxgZycjje(MathUtil.add(sqliteDyDrZySpxgDp.getSpxgZycjje(), sqliteDyDrZySpxgSp.getSpxgZycjje()));
            //自营成交人数
            sqliteDyDrZySpxgDp.setSpxgZycjrs(sqliteDyDrZySpxgDp.getSpxgZycjrs() + sqliteDyDrZySpxgSp.getSpxgZycjrs());
            //自营成交订单数
            sqliteDyDrZySpxgDp.setSpxgZycjdds(sqliteDyDrZySpxgDp.getSpxgZycjdds() + sqliteDyDrZySpxgSp.getSpxgZycjdds());
            //自营成交件数
            sqliteDyDrZySpxgDp.setSpxgZycjjs(sqliteDyDrZySpxgDp.getSpxgZycjjs() + sqliteDyDrZySpxgSp.getSpxgZycjjs());
            //自营退款金额
            sqliteDyDrZySpxgDp.setSpxgZytkje(MathUtil.add(sqliteDyDrZySpxgDp.getSpxgZytkje(), sqliteDyDrZySpxgSp.getSpxgZytkje()));

            //自营直播成交金额
            sqliteDyDrZySpxgDp.setSpxgZyzbcjje(MathUtil.add(sqliteDyDrZySpxgDp.getSpxgZyzbcjje(), sqliteDyDrZySpxgSp.getSpxgZyzbcjje()));
            //自营直播成交人数
            sqliteDyDrZySpxgDp.setSpxgZyzbcjrs(sqliteDyDrZySpxgDp.getSpxgZyzbcjrs() + sqliteDyDrZySpxgSp.getSpxgZyzbcjrs());
            //自营直播成交订单数
            sqliteDyDrZySpxgDp.setSpxgZyzbcjdds(sqliteDyDrZySpxgDp.getSpxgZyzbcjdds() + sqliteDyDrZySpxgSp.getSpxgZyzbcjdds());
            //自营直播成交件数
            sqliteDyDrZySpxgDp.setSpxgZyzbcjjs(sqliteDyDrZySpxgDp.getSpxgZyzbcjjs() + sqliteDyDrZySpxgSp.getSpxgZyzbcjjs());
            //自营直播退款金额
            sqliteDyDrZySpxgDp.setSpxgZyzbtkje(MathUtil.add(sqliteDyDrZySpxgDp.getSpxgZyzbtkje(), sqliteDyDrZySpxgSp.getSpxgZyzbtkje()));

            //自营短视频支付金额
            sqliteDyDrZySpxgDp.setSpxgZydspcjje(MathUtil.add(sqliteDyDrZySpxgDp.getSpxgZydspcjje(), sqliteDyDrZySpxgSp.getSpxgZydspcjje()));
            //自营短视频成交人数
            sqliteDyDrZySpxgDp.setSpxgZydspcjrs(sqliteDyDrZySpxgDp.getSpxgZydspcjrs() + sqliteDyDrZySpxgSp.getSpxgZydspcjrs());
            //自营短视频成交订单数
            sqliteDyDrZySpxgDp.setSpxgZydspcjdds(sqliteDyDrZySpxgDp.getSpxgZydspcjdds() + sqliteDyDrZySpxgSp.getSpxgZydspcjdds());
            //自营短视频成交件数
            sqliteDyDrZySpxgDp.setSpxgZydspcjjs(sqliteDyDrZySpxgDp.getSpxgZydspcjjs() + sqliteDyDrZySpxgSp.getSpxgZydspcjjs());
            //自营短视频退款金额
            sqliteDyDrZySpxgDp.setSpxgZydsptkje(MathUtil.add(sqliteDyDrZySpxgDp.getSpxgZydsptkje(), sqliteDyDrZySpxgSp.getSpxgZydsptkje()));

            //自营图文支付金额
            sqliteDyDrZySpxgDp.setSpxgZytwcjje(MathUtil.add(sqliteDyDrZySpxgDp.getSpxgZytwcjje(), sqliteDyDrZySpxgSp.getSpxgZytwcjje()));
            //自营图文成交人数
            sqliteDyDrZySpxgDp.setSpxgZytwcjrs(sqliteDyDrZySpxgDp.getSpxgZytwcjrs() + sqliteDyDrZySpxgSp.getSpxgZytwcjrs());
            //自营图文成交订单数
            sqliteDyDrZySpxgDp.setSpxgZytwcjdds(sqliteDyDrZySpxgDp.getSpxgZytwcjdds() + sqliteDyDrZySpxgSp.getSpxgZytwcjdds());
            //自营图文成交件数
            sqliteDyDrZySpxgDp.setSpxgZytwcjjs(sqliteDyDrZySpxgDp.getSpxgZytwcjjs() + sqliteDyDrZySpxgSp.getSpxgZytwcjjs());
            //自营图文退款金额
            sqliteDyDrZySpxgDp.setSpxgZytwtkje(MathUtil.add(sqliteDyDrZySpxgDp.getSpxgZytwtkje(), sqliteDyDrZySpxgSp.getSpxgZytwtkje()));

            //自营商品卡支付金额
            sqliteDyDrZySpxgDp.setSpxgZyspkcjje(MathUtil.add(sqliteDyDrZySpxgDp.getSpxgZyspkcjje(), sqliteDyDrZySpxgSp.getSpxgZyspkcjje()));
            //自营商品卡成交人数
            sqliteDyDrZySpxgDp.setSpxgZyspkcjrs(sqliteDyDrZySpxgDp.getSpxgZyspkcjrs() + sqliteDyDrZySpxgSp.getSpxgZyspkcjrs());
            //自营商品卡成交订单数
            sqliteDyDrZySpxgDp.setSpxgZyspkcjdds(sqliteDyDrZySpxgDp.getSpxgZyspkcjdds() + sqliteDyDrZySpxgSp.getSpxgZyspkcjdds());
            //自营商品卡成交件数
            sqliteDyDrZySpxgDp.setSpxgZyspkcjjs(sqliteDyDrZySpxgDp.getSpxgZyspkcjjs() + sqliteDyDrZySpxgSp.getSpxgZyspkcjjs());
            //自营商品卡退款金额
            sqliteDyDrZySpxgDp.setSpxgZyspktkje(MathUtil.add(sqliteDyDrZySpxgDp.getSpxgZyspktkje(), sqliteDyDrZySpxgSp.getSpxgZyspktkje()));
        }
        processSycmDpOTS(rq, sqliteDyDrZySpxgDp);

        return sqliteDyDrZySpxgSps;
    }
}
