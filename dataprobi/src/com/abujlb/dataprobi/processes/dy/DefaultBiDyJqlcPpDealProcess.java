package com.abujlb.dataprobi.processes.dy;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteDp;
import com.abujlb.dataprobi.bean.dy.DataprobiDyMsgBean;
import com.abujlb.dataprobi.bean.dy.SqliteDyJlqcPpDp;
import com.abujlb.dataprobi.bean.dy.SqliteDyJlqcPpSp;
import com.abujlb.dataprobi.bean.dy.SqliteDySpxgSp;
import com.abujlb.dataprobi.bean.dy.jlqc.BiJlqcAvvid;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.threads.BiThreadLocals;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import com.abujlb.dataprobi.util.DySpxgXlsReader;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.abujlb.dataprobi.util.FileUtil;
import com.abujlb.dataprobi.util.MathUtil;
import com.abujlb.util.StringUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/5/11 15:49
 */
@Component
@BiPro(order = 5, qd = Qd.DY)
public class DefaultBiDyJqlcPpDealProcess extends AbstractBiDyProcess {

    private final String[] COLUMNS = {BiSycmDpDataTsDao.DY_COLUMN_PP};

    @Override
    public void dealShop() {
        super.dealShop(
                SqliteDyJlqcPpDp.class,
                ((DataprobiDyMsgBean) getDataprobiBaseMsg()).getPpdp(),
                COLUMNS
        );


        DataprobiDyMsgBean dataprobiBaseMsg = (DataprobiDyMsgBean) getDataprobiBaseMsg();
        List<String> rqList = new ArrayList<>();
        rqList.addAll(dataprobiBaseMsg.getPpdp());
        rqList.addAll(dataprobiBaseMsg.getSpxg());
        rqList = rqList.stream().distinct().collect(Collectors.toList());
        dealGoodsByShare(rqList);
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqliteDyJlqcPpDp.class,
                ((DataprobiDyMsgBean) getDataprobiBaseMsg()).getPpdp(),
                COLUMNS
        );
    }


    @Override
    protected <T extends AbstractSqliteDp> T dpObject(Class<T> tClass, String rq, String... columns) {
        SqliteDyJlqcPpDp t = null;
        try {
            List<BiJlqcAvvid> dyJlqcList = BiThreadLocals.getDyJlqcList();
            if (CollectionUtils.isEmpty(dyJlqcList)) {
                return null;
            }
            t = new SqliteDyJlqcPpDp();
            t.setQd(BiThreadLocals.getMsgBean().getQd());
            t.setQd_userid(BiThreadLocals.getMsgBean().getQd() + "_" + BiThreadLocals.getMsgBean().getUserid());
            t.setYhid(BiThreadLocals.getMsgBean().getYhid());
            t.setUserid(BiThreadLocals.getMsgBean().getUserid());
            t.setRq(rq);

            for (BiJlqcAvvid biJlqcAvvid : dyJlqcList) {
                String[] return_values = biSycmDpDataTsDao.getRow("DY_JLQC_" + biJlqcAvvid.getAvvid(), rq, columns);
                SqliteDyJlqcPpDp tTemp = new SqliteDyJlqcPpDp();
                tTemp.setValues(return_values);

                //搜索数据-词包
                String osskey1 = "bi_dy_jlqc/" + biJlqcAvvid.getAvvid() + "/" + rq + "/pp_wordpackage.json";
                String s1 = biDataOssUtil.readJson(osskey1);
                if (StringUtil.isJsonArray2(s1)) {
                    double hf1 = 0;
                    double cjje1 = 0;
                    JSONArray jsonArray = JSONArray.parseArray(s1);
                    for (int i = 0; i < jsonArray.size(); i++) {
                        JSONObject jsonObject = jsonArray.getJSONObject(i);
                        double hf = MathUtil.divide(FastJSONObjAttrToNumber.toDouble(jsonObject, "metrics", "stat_cost", "value"), 100000);
                        double cjje = MathUtil.divide(FastJSONObjAttrToNumber.toDouble(jsonObject, "metrics", "all_order_pay_gmv_1days", "value"), 100000);
                        hf1 = MathUtil.add(hf1, hf);
                        cjje1 = MathUtil.add(cjje1, cjje);
                    }

                    tTemp.setPphf(MathUtil.add(tTemp.getPphf(), hf1));
                    tTemp.setPpzjcjje(MathUtil.add(tTemp.getPpzjcjje(), cjje1));
                }

                //搜索数据-预订单
                String osskey2 = "bi_dy_jlqc/" + biJlqcAvvid.getAvvid() + "/" + rq + "/pp_advancedorder.json";
                String s2 = biDataOssUtil.readJson(osskey2);
                if (StringUtil.isJsonArray2(s2)) {
                    double hf2 = 0;
                    double cjje2 = 0;
                    JSONArray jsonArray = JSONArray.parseArray(s2);
                    for (int i = 0; i < jsonArray.size(); i++) {
                        JSONObject jsonObject = jsonArray.getJSONObject(i);
                        double hf = MathUtil.divide(FastJSONObjAttrToNumber.toDouble(jsonObject, "metrics", "stat_cost", "value"), 100000);
                        double cjje = MathUtil.divide(FastJSONObjAttrToNumber.toDouble(jsonObject, "metrics", "all_order_pay_gmv_1days", "value"), 100000);
                        hf2 = MathUtil.add(hf2, hf);
                        cjje2 = MathUtil.add(cjje2, cjje);
                    }

                    tTemp.setPphf(MathUtil.add(tTemp.getPphf(), hf2));
                    tTemp.setPpzjcjje(MathUtil.add(tTemp.getPpzjcjje(), cjje2));
                }
                t.plus(tTemp);
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return (T) t;
    }

    public void dealGoodsByShare(List<String> rqList) {
        if (CollectionUtils.isEmpty(rqList)) {
            return;
        }
        for (String rq : rqList) {
            dealGoodsByShareDay(rq);
        }
    }

    private void dealGoodsByShareDay(String rq) {
        try {
            final String spxgOssKeyFormat = "bi_dy/%s/%s/spxg2.xlsx";

            //获取店铺维度的品牌数据
            SqliteDyJlqcPpDp sqliteDyJlqcPpDp = dpObject(SqliteDyJlqcPpDp.class, rq, COLUMNS);
            if (sqliteDyJlqcPpDp == null || sqliteDyJlqcPpDp.getPphf() <= 0) {
                return;
            }

            String spxgOssKey = String.format(spxgOssKeyFormat, getDataprobiBaseMsg().getUserid(), rq);
            if (!biDataOssUtil.exist(spxgOssKey)) {
                return;
            }

            String filePath = FileUtil.createXlsFilePath();
            biDataOssUtil.download(spxgOssKey, filePath);
            List<SqliteDySpxgSp> spxgList = DySpxgXlsReader.parseXLSV2(filePath);
            if (CollectionUtils.isEmpty(spxgList)) {
                return;
            }

            List<SqliteDyJlqcPpSp> ppSpList = new ArrayList<>();
            boolean allCjjeZero = true;
            double totalZfje = 0;
            for (SqliteDySpxgSp sqliteDySpxgSp : spxgList) {
                if (sqliteDySpxgSp.getSpxgzfje() != 0 && allCjjeZero) {
                    allCjjeZero = false;
                }
                totalZfje = MathUtil.add(totalZfje, sqliteDySpxgSp.getSpxgzfje());
            }

            if (allCjjeZero || totalZfje == 0) {
                return;
            }

            for (SqliteDySpxgSp sqliteDySpxgSp : spxgList) {
                if (sqliteDySpxgSp.getSpxgzfje() <= 0) {
                    continue;
                }
                double sharePphf = MathUtil.multipy(MathUtil.divide(sqliteDySpxgSp.getSpxgzfje(), totalZfje, 8), sqliteDyJlqcPpDp.getPphf());

                SqliteDyJlqcPpSp sqliteDyJlqcPpSp = new SqliteDyJlqcPpSp();
                sqliteDyJlqcPpSp.setUserid(getDataprobiBaseMsg().getUserid());
                sqliteDyJlqcPpSp.setBbid(sqliteDySpxgSp.getBbid());
                sqliteDyJlqcPpSp.setRq(rq);
                sqliteDyJlqcPpSp.setYhid(getDataprobiBaseMsg().getYhid());
                sqliteDyJlqcPpSp.setQd(getDataprobiBaseMsg().getQd());
                sqliteDyJlqcPpSp.setQd_userid_bbid(sqliteDyJlqcPpSp.getQd() + "_" + sqliteDyJlqcPpSp.getUserid() + "_" + sqliteDyJlqcPpSp.getBbid());
                sqliteDyJlqcPpSp.setPphf(sharePphf);
                ppSpList.add(sqliteDyJlqcPpSp);
            }

            processSycmSpOTS(rq, ppSpList);
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
    }
}
