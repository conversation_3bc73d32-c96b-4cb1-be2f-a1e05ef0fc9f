package com.abujlb.dataprobi.processes.dy;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.dy.DataprobiDyMsgBean;
import com.abujlb.dataprobi.bean.dy.SqliteDyJlqcQytgDp;
import com.abujlb.dataprobi.bean.dy.SqliteDyJlqcQytgSp;
import com.abujlb.dataprobi.bean.dy.SqliteDySpxgSp;
import com.abujlb.dataprobi.bean.dy.jlqc.BiJlqcAvvid;
import com.abujlb.dataprobi.bean.hfshare.HfShareBase;
import com.abujlb.dataprobi.bean.hfshare.HfShareGoodsConfig;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.threads.BiThreadLocals;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import com.abujlb.dataprobi.tsdao.BiTghfConfigTsDao;
import com.abujlb.dataprobi.util.DySpxgXlsReader;
import com.abujlb.dataprobi.util.FileUtil;
import com.abujlb.dataprobi.util.MathUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/5/11 15:49
 */
@Component
@BiPro(order = 7, qd = Qd.DY)
public class DefaultBiDyJqlcQytgDealProcess extends AbstractBiDyProcess {

    private final String[] COLUMNS = {BiSycmDpDataTsDao.DY_COLUMN_QYTG};

    @Autowired
    private BiTghfConfigTsDao biTghfConfigTsDao;

    @Override
    public boolean compareGoods() {
        List<String> rqList = new ArrayList<>();
        rqList.addAll(((DataprobiDyMsgBean) getDataprobiBaseMsg()).getSpxg());
        rqList.addAll(((DataprobiDyMsgBean) getDataprobiBaseMsg()).getQytgdp());
        rqList = rqList.stream().distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(rqList)) {
            return true;
        }
        dealGoods();
        return false;
    }

    @Override
    public void dealGoods() {
        List<String> rqList = new ArrayList<>();
        rqList.addAll(((DataprobiDyMsgBean) getDataprobiBaseMsg()).getSpxg());
        rqList.addAll(((DataprobiDyMsgBean) getDataprobiBaseMsg()).getQytgdp());
        rqList = rqList.stream().distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(rqList)) {
            return;
        }

        List<BiJlqcAvvid> dyJlqcList = BiThreadLocals.getDyJlqcList();
        if (CollectionUtils.isEmpty(dyJlqcList)) {
            return;
        }

        for (String rq : rqList) {
            sqliteDbUtil.clearSpData(null, rq, SqliteDyJlqcQytgSp.class);
        }

        List<HfShareBase> hfShareBaseList = biTghfConfigTsDao.getRows(BiTghfConfigTsDao.LX_DY_QC_QYTG, getDataprobiBaseMsg().getYhid(), "DY", getDataprobiBaseMsg().getUserid());

        for (String rq : rqList) {
            final String spxgOssKeyFormat = "bi_dy/%s/%s/spxg2.xlsx";
            String spxgOssKey = String.format(spxgOssKeyFormat, getDataprobiBaseMsg().getUserid(), rq);
            List<SqliteDySpxgSp> spxgList = null;
            if (biDataOssUtil.exist(spxgOssKey)) {
                String filePath = FileUtil.createXlsFilePath();
                biDataOssUtil.download(spxgOssKey, filePath);
                spxgList = DySpxgXlsReader.parseXLSV2(filePath);
            }

            SqliteDyJlqcQytgDp sqliteDyJlqcQytgDp = new SqliteDyJlqcQytgDp();
            Map<String, SqliteDyJlqcQytgSp> map = new HashMap<>();

            try {
                sqliteDyJlqcQytgDp.setQd(BiThreadLocals.getMsgBean().getQd());
                sqliteDyJlqcQytgDp.setQd_userid(BiThreadLocals.getMsgBean().getQd() + "_" + BiThreadLocals.getMsgBean().getUserid());
                sqliteDyJlqcQytgDp.setYhid(BiThreadLocals.getMsgBean().getYhid());
                sqliteDyJlqcQytgDp.setUserid(BiThreadLocals.getMsgBean().getUserid());
                sqliteDyJlqcQytgDp.setRq(rq);

                for (BiJlqcAvvid biJlqcAvvid : dyJlqcList) {
                    String[] return_values = biSycmDpDataTsDao.getRow("DY_JLQC_" + biJlqcAvvid.getAvvid(), rq, COLUMNS);
                    SqliteDyJlqcQytgDp tTemp = SqliteDyJlqcQytgDp.class.newInstance();
                    tTemp.setValues(return_values);
                    sqliteDyJlqcQytgDp.plus(tTemp);

                    //获取bbid对应的cjje
                    if (tTemp.getQytghf() == 0) {
                        continue;
                    }

                    List<HfShareBase> tempList = hfShareBaseList.stream().filter(temp ->
                            temp.getQch().equals(biJlqcAvvid.getAvvid())
                                    && (((StringUtils.isBlank(temp.getKsrq()) && StringUtils.isBlank(temp.getJsrq()))
                                    || ((StringUtils.isBlank(temp.getKsrq()) && StringUtils.isNotBlank(temp.getJsrq()) && rq.compareTo(temp.getJsrq()) <= 0))
                                    || ((StringUtils.isBlank(temp.getJsrq()) && StringUtils.isNotBlank(temp.getKsrq()) && rq.compareTo(temp.getKsrq()) >= 0))
                                    || (StringUtils.isNotBlank(temp.getKsrq()) && StringUtils.isNotBlank(temp.getJsrq()) && rq.compareTo(temp.getJsrq()) <= 0 && rq.compareTo(temp.getKsrq()) >= 0))
                            )
                    ).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(tempList) || tempList.size() > 1) {
                        continue;
                    }

                    HfShareBase hfShareBase = tempList.get(0);

                    List<HfShareGoodsConfig> configList = hfShareBase.getConfigList();
                    if (CollectionUtils.isEmpty(configList)) {
                        continue;
                    }

                    int type = hfShareBase.getType();
                    if (type == 0 && CollectionUtils.isNotEmpty(spxgList)) {
                        xsePercentShare(map, tTemp, configList, spxgList, rq);
                    } else if (type == 1) {
                        ratioShare(map, tTemp, configList);
                    }
                }
                processSycmDpOTS(rq, sqliteDyJlqcQytgDp);

                ArrayList<SqliteDyJlqcQytgSp> sqliteDyJlqcQytgSps = new ArrayList<>(map.values());
                for (SqliteDyJlqcQytgSp sqliteDyJlqcQytgSp : sqliteDyJlqcQytgSps) {
                    sqliteDyJlqcQytgSp.setRq(rq);
                }
                processSycmSpOTS(rq, sqliteDyJlqcQytgSps);
            } catch (Exception e) {
                logger.error(e.getMessage(), e);
            }
        }
    }

    private void ratioShare(Map<String, SqliteDyJlqcQytgSp> map, SqliteDyJlqcQytgDp tTemp, List<HfShareGoodsConfig> configList) {
        for (HfShareGoodsConfig hfShareGoodsConfig : configList) {
            if (hfShareGoodsConfig.getRatio() == null || hfShareGoodsConfig.getRatio() == 0) {
                continue;
            }

            double ratio = hfShareGoodsConfig.getRatio();

            SqliteDyJlqcQytgSp sqliteDyJlqcQytgSp = map.getOrDefault(hfShareGoodsConfig.getGoodsId(), new SqliteDyJlqcQytgSp());
            sqliteDyJlqcQytgSp.setQd(getDataprobiBaseMsg().getQd());
            sqliteDyJlqcQytgSp.setQd_userid_bbid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid() + "_" + hfShareGoodsConfig.getGoodsId());
            sqliteDyJlqcQytgSp.setBbid(hfShareGoodsConfig.getGoodsId());

            sqliteDyJlqcQytgSp.setYhid(getDataprobiBaseMsg().getYhid());
            sqliteDyJlqcQytgSp.setUserid(getDataprobiBaseMsg().getUserid());

            sqliteDyJlqcQytgSp.setQytghf(MathUtil.add(sqliteDyJlqcQytgSp.getQytghf(), MathUtil.multipy(tTemp.getQytghf(), ratio)));

            map.put(hfShareGoodsConfig.getGoodsId(), sqliteDyJlqcQytgSp);
        }
    }

    private void xsePercentShare(Map<String, SqliteDyJlqcQytgSp> map, SqliteDyJlqcQytgDp tTemp, List<HfShareGoodsConfig> configList, List<SqliteDySpxgSp> spxgList, String rq) {
        double total = 0;
        for (SqliteDySpxgSp sqliteDySpxgSp : spxgList) {
            if (configList.stream().anyMatch(hfShareGoodsConfig -> hfShareGoodsConfig.getGoodsId().equals(sqliteDySpxgSp.getBbid()))) {
                total = MathUtil.add(sqliteDySpxgSp.getSpxgzfje(), total);
            }
        }

        Map<String, Double> itemCjjeZb = new HashMap<>();
        for (SqliteDySpxgSp sqliteDySpxgSp : spxgList) {
            Optional<HfShareGoodsConfig> any = configList.stream().filter(hfShareGoodsConfig -> hfShareGoodsConfig.getGoodsId().equals(sqliteDySpxgSp.getBbid())).findAny();
            if (any.isPresent()) {
                itemCjjeZb.put(sqliteDySpxgSp.getBbid(), MathUtil.divide(sqliteDySpxgSp.getSpxgzfje(), total, 8));
            }
        }

        for (HfShareGoodsConfig hfShareGoodsConfig : configList) {
            double ratio = itemCjjeZb.getOrDefault(hfShareGoodsConfig.getGoodsId(), 0D);

            SqliteDyJlqcQytgSp sqliteDyJlqcQytgSp = map.getOrDefault(hfShareGoodsConfig.getGoodsId(), new SqliteDyJlqcQytgSp());
            sqliteDyJlqcQytgSp.setQd(getDataprobiBaseMsg().getQd());
            sqliteDyJlqcQytgSp.setQd_userid_bbid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid() + "_" + hfShareGoodsConfig.getGoodsId());
            sqliteDyJlqcQytgSp.setBbid(hfShareGoodsConfig.getGoodsId());
            sqliteDyJlqcQytgSp.setYhid(getDataprobiBaseMsg().getYhid());
            sqliteDyJlqcQytgSp.setUserid(getDataprobiBaseMsg().getUserid());

            sqliteDyJlqcQytgSp.setQytghf(MathUtil.add(sqliteDyJlqcQytgSp.getQytghf(), MathUtil.multipy(tTemp.getQytghf(), ratio)));

            map.put(hfShareGoodsConfig.getGoodsId(), sqliteDyJlqcQytgSp);
        }
    }

}
