package com.abujlb.dataprobi.processes.dy;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.BiInfo;
import com.abujlb.dataprobi.bean.SqliteSp;
import com.abujlb.dataprobi.bean.dy.DataprobiDyMsgBean;
import com.abujlb.dataprobi.bean.dy.SqliteDyJlqcZbDp;
import com.abujlb.dataprobi.bean.dy.SqliteDyJlqcZbSp;
import com.abujlb.dataprobi.bean.dy.SqliteDySpxgSp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.threads.BiThreadLocals;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import com.abujlb.dataprobi.util.DySpxgXlsReader;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.abujlb.dataprobi.util.FileUtil;
import com.abujlb.dataprobi.util.MathUtil;
import com.abujlb.util.StringUtil;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/5/11 15:49
 */
@Component
@BiPro(order = 4, qd = Qd.DY)
public class DefaultBiDyJqlcZbDealProcess extends AbstractBiDyProcess {

    private final String[] COLUMNS = {BiSycmDpDataTsDao.DY_COLUMN_JLQC_ZB};

    //没有实际意义
    private final String OSSKEY_PATTERN = "bi_dy/%s/%s/spxg.xlsx";

    @Override
    public void dealGoods() {
        DataprobiDyMsgBean dataprobiBaseMsg = (DataprobiDyMsgBean) getDataprobiBaseMsg();
        List<String> rqList = new ArrayList<>();
        rqList.addAll(dataprobiBaseMsg.getZbdp());
        rqList.addAll(dataprobiBaseMsg.getDr_zy_spxg());
        rqList = rqList.stream().distinct().collect(Collectors.toList());
        super.dealGoods(
                SqliteDyJlqcZbSp.class,
                OSSKEY_PATTERN,
                rqList
        );
    }

    @Override
    public boolean compareGoods() {
        DataprobiDyMsgBean dataprobiBaseMsg = (DataprobiDyMsgBean) getDataprobiBaseMsg();
        List<String> rqList = new ArrayList<>();
        rqList.addAll(dataprobiBaseMsg.getZbdp());
        rqList.addAll(dataprobiBaseMsg.getDr_zy_spxg());
        rqList = rqList.stream().distinct().collect(Collectors.toList());
        return super.compareGoods(
                SqliteDyJlqcZbSp.class,
                OSSKEY_PATTERN,
                rqList
        );
    }

    @Override
    public void dealShop() {
        super.dealShop(SqliteDyJlqcZbDp.class, ((DataprobiDyMsgBean) getDataprobiBaseMsg()).getZbdp(), COLUMNS);
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(SqliteDyJlqcZbDp.class, ((DataprobiDyMsgBean) getDataprobiBaseMsg()).getZbdp(), COLUMNS);
    }

    @Override
    protected <T extends AbstractSqliteSp> List<T> spList(Class<T> tClass, String ossKey, String rq) {
        try {
            /**
             * 【11204-加兹尼-标准推广-直播花费分摊宝贝维度逻辑更改】
             * https://www.tapd.cn/tapd_fe/47708728/story/detail/1147708728001015099
             *
             */
            if (getDataprobiBaseMsg().getType() == 1) {
                sqliteDbUtil.clearSpData(null, rq, SqliteDyJlqcZbSp.class);
            }

            final String spxgOssKeyFormat_type1 = "bi_dy/%s/%s/spxg_zyzb.xlsx";
            final String spxgOssKeyFormat_type2 = "bi_dy/%s/%s/spxg_drzb.xlsx";

            //获取店铺维度的zb数据
            SqliteDyJlqcZbDp sqliteDyJlqcZbDp = dpObject(SqliteDyJlqcZbDp.class, rq, COLUMNS);
            if (sqliteDyJlqcZbDp == null || sqliteDyJlqcZbDp.getZbhf() <= 0) {
                return null;
            }

            // 获取店铺配置
            //0 、 默认  商品效果-自营直播
            //1 、 商品效果 合作直播
            int type = 0;
            BiInfo biInfo = BiThreadLocals.getBiInfo();
            if (biInfo != null) {
                String dpconfigjson = biInfo.getDpconfigjson();
                if (StringUtil.isJson2(dpconfigjson)) {
                    JSONObject jsonObject = JSONObject.parseObject(dpconfigjson);
                    type = FastJSONObjAttrToNumber.toInt(jsonObject, "bztg_zb_share_type");
                }
            }

            String spxgOssKey = null;
            if (type == 0) {
                spxgOssKey = String.format(spxgOssKeyFormat_type1, getDataprobiBaseMsg().getUserid(), rq);
            } else if (type == 1) {
                spxgOssKey = String.format(spxgOssKeyFormat_type2, getDataprobiBaseMsg().getUserid(), rq);
            } else {
                return null;
            }

            if (!biDataOssUtil.exist(spxgOssKey)) {
                return null;
            }

            String filePath = FileUtil.createXlsFilePath();
            biDataOssUtil.download(spxgOssKey, filePath);
            List<SqliteDySpxgSp> spxgList = DySpxgXlsReader.parseXLSV2(filePath);
            if (CollectionUtils.isEmpty(spxgList)) {
                return null;
            }

            List<SqliteDyJlqcZbSp> zbSpList = new ArrayList<>();
            boolean allCjjeZero = true;
            double totalZfje = 0;
            for (SqliteDySpxgSp sqliteDySpxgSp : spxgList) {
                if (sqliteDySpxgSp.getSpxgzfje() != 0 && allCjjeZero) {
                    allCjjeZero = false;
                }
                totalZfje = MathUtil.add(totalZfje, sqliteDySpxgSp.getSpxgzfje());
            }

            if (allCjjeZero || totalZfje == 0) {
                return null;
            }

            for (SqliteDySpxgSp sqliteDySpxgSp : spxgList) {
                if (sqliteDySpxgSp.getSpxgzfje() <= 0) {
                    continue;
                }
                double shareZbhf = MathUtil.multipy(MathUtil.divide(sqliteDySpxgSp.getSpxgzfje(), totalZfje, 8), sqliteDyJlqcZbDp.getZbhf());

                SqliteDyJlqcZbSp sqliteDyJlqcZbSp = new SqliteDyJlqcZbSp();
                sqliteDyJlqcZbSp.setUserid(getDataprobiBaseMsg().getUserid());
                sqliteDyJlqcZbSp.setBbid(sqliteDySpxgSp.getBbid());
                sqliteDyJlqcZbSp.setRq(rq);
                sqliteDyJlqcZbSp.setYhid(getDataprobiBaseMsg().getYhid());
                sqliteDyJlqcZbSp.setQd(getDataprobiBaseMsg().getQd());
                sqliteDyJlqcZbSp.setQd_userid_bbid(sqliteDyJlqcZbSp.getQd() + "_" + sqliteDyJlqcZbSp.getUserid() + "_" + sqliteDyJlqcZbSp.getBbid());
                sqliteDyJlqcZbSp.setZbhf(shareZbhf);
                zbSpList.add(sqliteDyJlqcZbSp);
            }
            return (List<T>) zbSpList;
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
        return null;
    }


    protected <T extends AbstractSqliteSp> boolean compareGoods(Class<T> tClass, String ossKeyFormat, List<String> rqList) {
        boolean result = true;
        for (String rq : rqList) {
            String ossKey = String.format(ossKeyFormat, getDataprobiBaseMsg().getUserid(), rq);
            List<T> list = spList(tClass, ossKey, rq);
            if (CollectionUtils.isEmpty(list)) {
                continue;
            }
            if (!result) {
                sqliteDbUtil.clearSpData(null, rq, SqliteDyJlqcZbSp.class);

                processSycmSpOTS(rq, list);
                dealShopByGoods(rq, list);
                list.clear();
                continue;
            }

            Map<String, SqliteSp> map = getSycmSpMapByBbid(rq);
            boolean flag = true;
            for (T t : list) {
                flag = t.compare(map.get(t.getBbid()));
                if (!flag) {
                    result = false;
                    break;
                }
            }
            map.clear();
            if (!flag) {
                sqliteDbUtil.clearSpData(null, rq, SqliteDyJlqcZbSp.class);

                processSycmSpOTS(rq, list);
                dealShopByGoods(rq, list);
            }
            list.clear();
        }
        return result;
    }
}
