package com.abujlb.dataprobi.processes.dy;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.dy.DataprobiDyMsgBean;
import com.abujlb.dataprobi.bean.dy.SqliteDyJxlmDp;
import com.abujlb.dataprobi.bean.dy.SqliteDyJxlmSp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/5/11 15:49
 */
@Component
@BiPro(order = 3, qd = Qd.DY)
public class DefaultBiDyJxlmDealProcess extends AbstractBiprocess {

    private final String OSSKEY_PATTERN = "bi_dy/%s/%s/jxlm.json";
    private final String[] COLUMNS = {BiSycmDpDataTsDao.DY_COLUMN_JXLM};

    @Override
    public void dealGoods() {
        super.dealGoods(
                SqliteDyJxlmSp.class,
                OSSKEY_PATTERN,
                ((DataprobiDyMsgBean) getDataprobiBaseMsg()).getJxlm()
        );
    }

    @Override
    public void dealShop() {
        super.dealShop(
                SqliteDyJxlmDp.class,
                ((DataprobiDyMsgBean) getDataprobiBaseMsg()).getJxlmdp(),
                COLUMNS
        );
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoods(
                SqliteDyJxlmSp.class,
                OSSKEY_PATTERN,
                ((DataprobiDyMsgBean) getDataprobiBaseMsg()).getJxlm()
        );
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqliteDyJxlmDp.class,
                ((DataprobiDyMsgBean) getDataprobiBaseMsg()).getJxlmdp(),
                COLUMNS
        );
    }

}
