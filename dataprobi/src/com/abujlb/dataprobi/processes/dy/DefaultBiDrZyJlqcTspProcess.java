package com.abujlb.dataprobi.processes.dy;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.dy.DataprobiDyMsgBean;
import com.abujlb.dataprobi.bean.dy.SqliteDyDrZyTspDp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import org.springframework.stereotype.Component;

/**
 * 抖音自营-达人-巨量千川-推商品处理
 *
 * <AUTHOR>
 * @date 2023/12/5
 */
@Component
@BiPro(order = 10, qd = Qd.DY)
public class DefaultBiDrZyJlqcTspProcess extends AbstractBiDyProcess {

    private final String[] COLUMNS = {BiSycmDpDataTsDao.DY_COLUMN_DR_ZY_TSPDP_DR, BiSycmDpDataTsDao.DY_COLUMN_DR_ZY_TSPDP_ZY};

    @Override
    public void dealShop() {
        super.dealShop(
                SqliteDyDrZyTspDp.class,
                ((DataprobiDyMsgBean) getDataprobiBaseMsg()).getDr_zy_tspdp(),
                COLUMNS
        );
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqliteDyDrZyTspDp.class,
                ((DataprobiDyMsgBean) getDataprobiBaseMsg()).getDr_zy_tspdp(),
                COLUMNS
        );
    }
}
