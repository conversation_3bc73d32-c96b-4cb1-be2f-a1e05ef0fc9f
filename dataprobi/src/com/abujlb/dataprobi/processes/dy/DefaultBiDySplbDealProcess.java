package com.abujlb.dataprobi.processes.dy;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.tsdao.SpnewTsDao;
import com.abujlb.dataprobi.util.FileToJson;
import com.abujlb.dataprobi.util.FileUtil;
import com.alibaba.fastjson.JSONArray;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;

/**
 * <AUTHOR>
 * @date 2022/9/14 11:02
 */
@Component
@BiPro(order = 0, qd = Qd.DY)
public class DefaultBiDySplbDealProcess extends AbstractBiprocess {

    private final String OSSKEY_PATTERN = "bi_dy/%s/splb.json";

    @Autowired
    private SpnewTsDao spnewTsDao ;

    @Override
    public void dealGoods() {
        if (getDataprobiBaseMsg().getSplb() == 1) {
            String osskey = String.format(OSSKEY_PATTERN, getDataprobiBaseMsg().getUserid());
            if (biDataOssUtil.exist(osskey)) {
                String filePath = FileUtil.createJSONFilePath();
                biDataOssUtil.download(osskey, filePath);
                File file = new File(filePath);
                String json = FileToJson.toJson(file);
                JSONArray jsonArray = JSONArray.parseArray(json);
                spnewTsDao.initSp(getDataprobiBaseMsg().getYhid(), getDataprobiBaseMsg().getQd(), getDataprobiBaseMsg().getUserid(), jsonArray);
            }
        }
    }

    @Override
    public boolean compareGoods() {
        if (getDataprobiBaseMsg().getSplb() != 1) {
            return true;
        }
        dealGoods();
        return false;
    }
}
