package com.abujlb.dataprobi.processes.wxsph;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.wxsph.DataprobiWxsphMsg;
import com.abujlb.dataprobi.bean.wxsph.SqliteCxztZbjDp;
import com.abujlb.dataprobi.bean.wxsph.SqliteWxsphXsqgDp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import org.springframework.stereotype.Component;


/**
 * 超雄智投直播间店铺
 */
@Component
@BiPro(order = 6, qd = Qd.WXSPH)
public class DefaultbiWxsphCxztZbjProcess extends AbstractBiprocess {

    private final String[] COLUMNS = {BiSycmDpDataTsDao.WXSPH_COLUMN_CXZTZBJDP};


    @Override
    public void dealShop() {
        super.dealShop(
                SqliteCxztZbjDp.class,
                ((DataprobiWxsphMsg) getDataprobiBaseMsg()).getCxztzbjdp(),
                COLUMNS
        );
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqliteCxztZbjDp.class,
                ((DataprobiWxsphMsg) getDataprobiBaseMsg()).getCxztzbjdp(),
                COLUMNS
        );
    }

}
