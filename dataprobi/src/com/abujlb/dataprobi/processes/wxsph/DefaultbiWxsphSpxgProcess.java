package com.abujlb.dataprobi.processes.wxsph;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.wxsph.DataprobiWxsphMsg;
import com.abujlb.dataprobi.bean.wxsph.SqliteWxsphSpxgDp;
import com.abujlb.dataprobi.bean.wxsph.SqliteWxsphSpxgSp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import org.springframework.stereotype.Component;


/**
 * 微信视频号商品效果
 */
@Component
@BiPro(order = 1, qd = Qd.WXSPH)
public class DefaultbiWxsphSpxgProcess extends AbstractBiprocess {

    public static final String OSSKEY_PATTERN = "biwxsph/%s/%s/spxg.json";
    private final String[] COLUMNS = {BiSycmDpDataTsDao.WXSPH_COLUMN_SPXGDP};

    @Override
    public void dealGoods() {
        super.dealGoods(
                SqliteWxsphSpxgSp.class,
                OSSKEY_PATTERN,
                ((DataprobiWxsphMsg) getDataprobiBaseMsg()).getSpxg()
        );
    }

    @Override
    public void dealShop() {
        super.dealShop(
                SqliteWxsphSpxgDp.class,
                ((DataprobiWxsphMsg) getDataprobiBaseMsg()).getSpxgdp(),
                COLUMNS
        );
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoods(
                SqliteWxsphSpxgSp.class,
                OSSKEY_PATTERN,
                ((DataprobiWxsphMsg) getDataprobiBaseMsg()).getSpxg()
        );
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqliteWxsphSpxgDp.class,
                ((DataprobiWxsphMsg) getDataprobiBaseMsg()).getSpxgdp(),
                COLUMNS
        );
    }

}
