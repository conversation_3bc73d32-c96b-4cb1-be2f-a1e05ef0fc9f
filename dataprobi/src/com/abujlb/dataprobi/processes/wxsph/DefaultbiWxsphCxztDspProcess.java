package com.abujlb.dataprobi.processes.wxsph;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.wxsph.*;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import org.springframework.stereotype.Component;


/**
 * 超雄智投直播间店铺
 */
@Component
@BiPro(order = 7, qd = Qd.WXSPH)
public class DefaultbiWxsphCxztDspProcess extends AbstractBiprocess {

    private final String[] COLUMNS = {BiSycmDpDataTsDao.WXSPH_COLUMN_CXZTDSPDP};
    public static final String OSSKEY_PATTERN = "biwxsph/%s/%s/cxztdsp.json";


    @Override
    public void dealGoods() {
        super.dealGoods(
                SqliteCxztDspSp.class,
                OSSKEY_PATTERN,
                ((DataprobiWxsphMsg) getDataprobiBaseMsg()).getCxztdsp()
        );
    }



    @Override
    public void dealShop() {
        super.dealShop(
                SqliteCxztDspDp.class,
                ((DataprobiWxsphMsg) getDataprobiBaseMsg()).getCxztdspdp(),
                COLUMNS
        );
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoods(
                SqliteCxztDspSp.class,
                OSSKEY_PATTERN,
                ((DataprobiWxsphMsg) getDataprobiBaseMsg()).getCxztdsp()
        );
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqliteCxztDspDp.class,
                ((DataprobiWxsphMsg) getDataprobiBaseMsg()).getCxztdspdp(),
                COLUMNS
        );
    }

}
