package com.abujlb.dataprobi.processes.wxsph;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.wxsph.DataprobiWxsphMsg;
import com.abujlb.dataprobi.bean.wxsph.SqliteWxsphXsqgDp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import org.springframework.stereotype.Component;


/**
 * 微信视频号限时抢购
 */
@Component
@BiPro(order = 2, qd = Qd.WXSPH)
public class DefaultbiWxsphXsqgProcess extends AbstractBiprocess {

    private final String[] COLUMNS = {BiSycmDpDataTsDao.WXSPH_COLUMN_XSQGDP};


    //暂无数据
    @Override
    public void dealShop() {
        super.dealShop(
                SqliteWxsphXsqgDp.class,
                ((DataprobiWxsphMsg) getDataprobiBaseMsg()).getXsqgdp(),
                COLUMNS
        );
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqliteWxsphXsqgDp.class,
                ((DataprobiWxsphMsg) getDataprobiBaseMsg()).getXsqgdp(),
                COLUMNS
        );
    }

}
