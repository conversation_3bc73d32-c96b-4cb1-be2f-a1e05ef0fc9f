package com.abujlb.dataprobi.processes.wxsph;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.wxsph.DataprobiWxsphMsg;
import com.abujlb.dataprobi.bean.wxsph.SqliteWxsphYhqDp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import org.springframework.stereotype.Component;


/**
 * 微信视频号优惠券
 */
@Component
@BiPro(order = 3, qd = Qd.WXSPH)
public class DefaultbiWxsphYhqProcess extends AbstractBiprocess {

    private final String[] COLUMNS = {BiSycmDpDataTsDao.WXSPH_COLUMN_YHQDP};


    //暂无数据
    @Override
    public void dealShop() {
        super.dealShop(
                SqliteWxsphYhqDp.class,
                ((DataprobiWxsphMsg) getDataprobiBaseMsg()).getYhqdp(),
                COLUMNS
        );
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqliteWxsphYhqDp.class,
                ((DataprobiWxsphMsg) getDataprobiBaseMsg()).getYhqdp(),
                COLUMNS
        );
    }

}
