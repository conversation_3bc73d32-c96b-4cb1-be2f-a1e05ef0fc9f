package com.abujlb.dataprobi.processes.ks;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.ks.*;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import org.springframework.stereotype.Component;

/**
 * 商品效果
 *
 * <AUTHOR>
 * @date 2024/11/18
 */
@Component
@BiPro(order = 2, qd = Qd.KS)
public class DefaultbiKsSpxgDealProcess extends AbstractBiprocess {

    public static final String OSSKEY_PATTERN = "biks/%s/%s/spxg.json";
    private final String[] COLUMNS = {BiSycmDpDataTsDao.KS_COLUMN_SPXG};

    @Override
    public void dealGoods() {
        super.dealGoods(
                SqliteKsSpxgSp.class,
                OSSKEY_PATTERN,
                ((DataprobiKsMsg) getDataprobiBaseMsg()).getSpxg()
        );
    }

    @Override
    public void dealShop() {
        super.dealShop(
                SqliteKsSpxgDp.class,
                ((DataprobiKsMsg) getDataprobiBaseMsg()).getSpxgdp(),
                COLUMNS
        );
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoods(
                SqliteKsSpxgSp.class,
                OSSKEY_PATTERN,
                ((DataprobiKsMsg) getDataprobiBaseMsg()).getSpxg()
        );
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqliteKsSpxgDp.class,
                ((DataprobiKsMsg) getDataprobiBaseMsg()).getSpxgdp(),
                COLUMNS
        );
    }
}
