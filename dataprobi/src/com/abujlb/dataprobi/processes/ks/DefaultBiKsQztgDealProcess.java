package com.abujlb.dataprobi.processes.ks;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteDp;
import com.abujlb.dataprobi.bean.ks.*;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.util.MathUtil;
import com.abujlb.util.StringUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.springframework.stereotype.Component;

/**
 * 全站推广花费处理
 *
 * <AUTHOR>
 * @date 2025/03/31
 */
@Component
@BiPro(order = 5, qd = Qd.KS)
public class DefaultBiKsQztgDealProcess extends AbstractBiprocess {

    private final String OSSKEY_SPTG_PATTERN = "biks/%s/%s/qztg_sptg.json";
    private final String OSSKEY_ZBTG_PATTERN = "biks/%s/%s/qztg_zbtg.json";
    private final String OSSKEY_SPTG_SP_PATTERN = "biks/%s/%s/qztg_sptg_sp.json";

    @Override
    public void dealGoods() {
        super.dealGoods(
                SqliteKsQztgSp.class,
                OSSKEY_SPTG_SP_PATTERN,
                ((DataprobiKsMsg) getDataprobiBaseMsg()).getQztg()
        );
    }

    @Override
    public void dealShop() {
        super.dealShop(
                SqliteKsQztgDp.class,
                ((DataprobiKsMsg) getDataprobiBaseMsg()).getQztg(),
                (String) null
        );
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqliteKsQztgDp.class,
                ((DataprobiKsMsg) getDataprobiBaseMsg()).getQztg(),
                (String) null
        );
    }

    @Override
    protected <T extends AbstractSqliteDp> T dpObject(Class<T> tClass, String rq, String... colummns) {
        String sptgosskey = String.format(OSSKEY_SPTG_PATTERN, getDataprobiBaseMsg().getUserid(), rq);
        String zbtgosskey = String.format(OSSKEY_ZBTG_PATTERN, getDataprobiBaseMsg().getUserid(), rq);
        String sptgjson = biDataOssUtil.readJson(sptgosskey);
        String zbtgjson = biDataOssUtil.readJson(zbtgosskey);
        if (!StringUtil.isJsonArray2(sptgjson) || !StringUtil.isJsonArray2(zbtgjson)) {
            return null;
        }
        SqliteKsQztgDp sqliteKsQztgDp = new SqliteKsQztgDp();
        sqliteKsQztgDp.setQd(getDataprobiBaseMsg().getQd());
        sqliteKsQztgDp.setQd_userid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid());
        sqliteKsQztgDp.setRq(rq);
        sqliteKsQztgDp.setYhid(getDataprobiBaseMsg().getYhid());
        sqliteKsQztgDp.setUserid(getDataprobiBaseMsg().getUserid());
        JSONArray sptgjsonArray = JSONArray.parseArray(sptgjson);
        JSONArray zbtgjsonArray = JSONArray.parseArray(zbtgjson);
        for (int i = 0; i < sptgjsonArray.size(); i++) {
            JSONObject jsonObject = sptgjsonArray.getJSONObject(i);
            if ("costTotal".equals(jsonObject.getString("dataIndex"))) {
                sqliteKsQztgDp.setQzsptghf(MathUtil.add(sqliteKsQztgDp.getQzsptghf(), jsonObject.getDoubleValue("value") / 1000.0));
            }
            if ("t0GMV".equals(jsonObject.getString("dataIndex"))) {
                sqliteKsQztgDp.setQzsptgcjje(MathUtil.add(sqliteKsQztgDp.getQzsptgcjje(), jsonObject.getDoubleValue("value") / 1000.0));
            }
        }
        for (int i = 0; i < zbtgjsonArray.size(); i++) {
            JSONObject jsonObject = zbtgjsonArray.getJSONObject(i);
            if ("costTotal".equals(jsonObject.getString("dataIndex"))) {
                sqliteKsQztgDp.setQzzbtghf(MathUtil.add(sqliteKsQztgDp.getQzzbtghf(), jsonObject.getDoubleValue("value") / 1000.0));
            }
            if ("t0GMV".equals(jsonObject.getString("dataIndex"))) {
                sqliteKsQztgDp.setQzzbtgcjje(MathUtil.add(sqliteKsQztgDp.getQzzbtgcjje(), jsonObject.getDoubleValue("value") / 1000.0));
            }
        }
        if (sqliteKsQztgDp.getQzsptghf() == 0 && sqliteKsQztgDp.getQzzbtghf() == 0 && sqliteKsQztgDp.getQzsptgcjje() == 0 && sqliteKsQztgDp.getQzzbtgcjje() == 0) {
            return null;
        }
        return (T) sqliteKsQztgDp;
    }
}
