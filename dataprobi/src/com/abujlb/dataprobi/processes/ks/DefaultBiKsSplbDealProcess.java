package com.abujlb.dataprobi.processes.ks;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.tsdao.SpnewTsDao;
import com.abujlb.dataprobi.util.FileToJson;
import com.abujlb.dataprobi.util.FileUtil;
import com.alibaba.fastjson.JSONArray;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;

/**
 * 商品列表
 *
 * <AUTHOR>
 * @date 2024/11/18
 */
@Component
@BiPro(order = 1, qd = Qd.KS)
public class DefaultBiKsSplbDealProcess extends AbstractBiprocess {

    private final String OSSKEY_PATTERN = "biks/%s/splb.json";


    @Autowired
    private SpnewTsDao spnewTsDao;

    @Override
    public void dealGoods() {
        if (getDataprobiBaseMsg().getSplb() == 1) {
            String key = String.format(OSSKEY_PATTERN, getDataprobiBaseMsg().getUserid());
            if (biDataOssUtil.exist(key)) {
                String filePath = FileUtil.createJSONFilePath();
                biDataOssUtil.download(key, filePath);
                File file = new File(filePath);
                String json = FileToJson.toJson(file);
                JSONArray jsonArray = JSONArray.parseArray(json);
                spnewTsDao.initSp(getDataprobiBaseMsg().getYhid(), getDataprobiBaseMsg().getQd(), getDataprobiBaseMsg().getUserid(), jsonArray);
                jsonArray.clear();
            }
        }
    }

    @Override
    public boolean compareGoods() {
        if (getDataprobiBaseMsg().getSplb() != 1) {
            return true;
        }
        dealGoods();
        return false;
    }
}
