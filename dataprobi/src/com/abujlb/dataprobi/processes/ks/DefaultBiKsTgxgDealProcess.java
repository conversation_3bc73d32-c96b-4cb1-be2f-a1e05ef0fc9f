package com.abujlb.dataprobi.processes.ks;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.ks.DataprobiKsMsg;
import com.abujlb.dataprobi.bean.ks.SqliteKsTgxgDp;
import com.abujlb.dataprobi.bean.ks.SqliteKsTgxgSp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.util.MathUtil;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 推广效果
 *
 * <AUTHOR>
 * @date 2024/11/18
 */
@Component
@BiPro(order = 3, qd = Qd.KS)
public class DefaultBiKsTgxgDealProcess extends AbstractBiprocess {

    private final String OSSKEY_PATTERN = "biks/%s/%s/tgxg.json";

    @Override
    public void dealGoods() {
        super.dealGoods(
                SqliteKsTgxgSp.class,
                OSSKEY_PATTERN,
                ((DataprobiKsMsg) getDataprobiBaseMsg()).getTgxg()
        );
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoods(
                SqliteKsTgxgSp.class,
                OSSKEY_PATTERN,
                ((DataprobiKsMsg) getDataprobiBaseMsg()).getTgxg()
        );
    }
    @Override
    protected <T extends AbstractSqliteSp> void dealShopByGoods(String rq, List<T> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        SqliteKsTgxgDp sqliteKsTgxgDp = new SqliteKsTgxgDp();
        sqliteKsTgxgDp.setQd(getDataprobiBaseMsg().getQd());
        sqliteKsTgxgDp.setQd_userid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid());
        sqliteKsTgxgDp.setRq(rq);
        sqliteKsTgxgDp.setYhid(getDataprobiBaseMsg().getYhid());
        sqliteKsTgxgDp.setUserid(getDataprobiBaseMsg().getUserid());
        //推广效果花费
        double ygyj = 0;
        //PV
        int pv = 0;
        //UV
        int uv = 0;
        //支付笔数
        int zfbs = 0;
        //支付GMV
        double zfgmv = 0;
        //技术服务费
        double jsfwf = 0;
        for (T t : list) {
            if (t instanceof SqliteKsTgxgSp) {
                ygyj = MathUtil.add(ygyj, ((SqliteKsTgxgSp) t).getYgyj());
                pv = pv + ((SqliteKsTgxgSp) t).getPv();
                uv = uv + ((SqliteKsTgxgSp) t).getUv();
                zfbs = zfbs + ((SqliteKsTgxgSp) t).getZfbs();
                zfgmv = MathUtil.add(zfgmv, ((SqliteKsTgxgSp) t).getZfgmv());
                jsfwf = MathUtil.add(jsfwf, ((SqliteKsTgxgSp) t).getJsfwf());
            }
            sqliteKsTgxgDp.setYgyj(ygyj);
            sqliteKsTgxgDp.setPv(pv);
            sqliteKsTgxgDp.setUv(uv);
            sqliteKsTgxgDp.setZfbs(zfbs);
            sqliteKsTgxgDp.setZfgmv(zfgmv);
            sqliteKsTgxgDp.setJsfwf(jsfwf);
        }
        processSycmDpOTS(rq, sqliteKsTgxgDp);
    }
}
