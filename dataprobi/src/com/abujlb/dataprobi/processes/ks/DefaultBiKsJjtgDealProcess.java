package com.abujlb.dataprobi.processes.ks;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteDp;
import com.abujlb.dataprobi.bean.ks.*;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.util.MathUtil;
import com.abujlb.util.StringUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.springframework.stereotype.Component;

/**
 * 极简推广花费处理
 *
 * <AUTHOR>
 * @date 2025/03/31
 */
@Component
@BiPro(order = 6, qd = Qd.KS)
public class DefaultBiKsJjtgDealProcess extends AbstractBiprocess {

    private final String OSSKEY_SPTG_PATTERN = "biks/%s/%s/jjtg_sptg.json";
    private final String OSSKEY_SPTG_SP_PATTERN = "biks/%s/%s/jjtg_sptg_sp.json";

    @Override
    public void dealGoods() {
        super.dealGoods(
                SqliteKsJjtgSp.class,
                OSSKEY_SPTG_SP_PATTERN,
                ((DataprobiKsMsg) getDataprobiBaseMsg()).getJjtg()
        );
    }

    @Override
    public void dealShop() {
        super.dealShop(
                SqliteKsJjtgDp.class,
                ((DataprobiKsMsg) getDataprobiBaseMsg()).getJjtg(),
                (String) null
        );
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqliteKsJjtgDp.class,
                ((DataprobiKsMsg) getDataprobiBaseMsg()).getJjtg(),
                (String) null
        );
    }

    @Override
    protected <T extends AbstractSqliteDp> T dpObject(Class<T> tClass, String rq, String... colummns) {
        String sptgosskey = String.format(OSSKEY_SPTG_PATTERN, getDataprobiBaseMsg().getUserid(), rq);
        String sptgjson = biDataOssUtil.readJson(sptgosskey);
        if (!StringUtil.isJsonArray2(sptgjson)) {
            return null;
        }
        SqliteKsJjtgDp sqliteKsJjtgDp = new SqliteKsJjtgDp();
        sqliteKsJjtgDp.setQd(getDataprobiBaseMsg().getQd());
        sqliteKsJjtgDp.setQd_userid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid());
        sqliteKsJjtgDp.setRq(rq);
        sqliteKsJjtgDp.setYhid(getDataprobiBaseMsg().getYhid());
        sqliteKsJjtgDp.setUserid(getDataprobiBaseMsg().getUserid());
        JSONArray sptgjsonArray = JSONArray.parseArray(sptgjson);
        for (int i = 0; i < sptgjsonArray.size(); i++) {
            JSONObject jsonObject = sptgjsonArray.getJSONObject(i);
            sqliteKsJjtgDp.setJjsptghf(MathUtil.add(sqliteKsJjtgDp.getJjsptghf(), jsonObject.getDoubleValue("costTotal") / 1000.0));
            sqliteKsJjtgDp.setJjsptgcjje(MathUtil.add(sqliteKsJjtgDp.getJjsptgcjje(), jsonObject.getDoubleValue("t0GMV") / 1000.0));
        }
        if (sqliteKsJjtgDp.getJjsptghf() == 0 && sqliteKsJjtgDp.getJjsptgcjje() == 0) {
            return null;
        }
        return (T) sqliteKsJjtgDp;
    }
}
