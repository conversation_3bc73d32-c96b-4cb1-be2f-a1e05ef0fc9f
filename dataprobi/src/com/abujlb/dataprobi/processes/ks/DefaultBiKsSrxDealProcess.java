package com.abujlb.dataprobi.processes.ks;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteDp;
import com.abujlb.dataprobi.bean.ks.DataprobiKsMsg;
import com.abujlb.dataprobi.bean.ks.SqliteKsSrxDp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.util.MathUtil;
import com.abujlb.util.StringUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.springframework.stereotype.Component;

/**
 * 收入项处理
 *
 * <AUTHOR>
 * @date 2025/03/14
 */
@Component
@BiPro(order = 5, qd = Qd.KS)
public class DefaultBiKsSrxDealProcess extends AbstractBiprocess {

    private final String OSSKEY_SRX_PATTERN = "biks/%s/%s/srx.json";

    @Override
    public void dealShop() {
        super.dealShop(
                SqliteKsSrxDp.class,
                ((DataprobiKsMsg) getDataprobiBaseMsg()).getSrx(),
                (String) null
        );
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqliteKsSrxDp.class,
                ((DataprobiKsMsg) getDataprobiBaseMsg()).getSrx(),
                (String) null
        );
    }

    @Override
    protected <T extends AbstractSqliteDp> T dpObject(Class<T> tClass, String rq, String... colummns) {
        String ossKey = String.format(OSSKEY_SRX_PATTERN, getDataprobiBaseMsg().getUserid(), rq);
        String json = biDataOssUtil.readJson(ossKey);
        if (!StringUtil.isJsonArray2(json)) {
            return null;
        }
        SqliteKsSrxDp sqliteKsSrxDp = new SqliteKsSrxDp();
        sqliteKsSrxDp.setQd(getDataprobiBaseMsg().getQd());
        sqliteKsSrxDp.setQd_userid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid());
        sqliteKsSrxDp.setRq(rq);
        sqliteKsSrxDp.setYhid(getDataprobiBaseMsg().getYhid());
        sqliteKsSrxDp.setUserid(getDataprobiBaseMsg().getUserid());

        JSONArray sptgjsonArray = JSONArray.parseArray(json);
        for (int i = 0; i < sptgjsonArray.size(); i++) {
            JSONObject jsonObject = sptgjsonArray.getJSONObject(i);
            if (!rq.equals(jsonObject.getString("rq"))) {
                continue;
            }
            //收入项为负
            sqliteKsSrxDp.setJlj(MathUtil.add(sqliteKsSrxDp.getJlj(), -jsonObject.getDoubleValue("jlj")));
            sqliteKsSrxDp.setKfj(MathUtil.add(sqliteKsSrxDp.getKfj(), -jsonObject.getDoubleValue("kfj")));
            sqliteKsSrxDp.setPtjlj(MathUtil.add(sqliteKsSrxDp.getPtjlj(), -jsonObject.getDoubleValue("ptjlj")));
        }
        if (sqliteKsSrxDp.getJlj() == 0 && sqliteKsSrxDp.getKfj() == 0 && sqliteKsSrxDp.getPtjlj() == 0) {
            return null;
        }
        return (T) sqliteKsSrxDp;
    }
}
