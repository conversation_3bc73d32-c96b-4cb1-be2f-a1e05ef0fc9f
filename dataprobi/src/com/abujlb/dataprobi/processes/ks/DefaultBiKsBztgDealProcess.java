package com.abujlb.dataprobi.processes.ks;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteDp;
import com.abujlb.dataprobi.bean.ks.DataprobiKsMsg;
import com.abujlb.dataprobi.bean.ks.SqliteKsBztgDp;
import com.abujlb.dataprobi.bean.ks.SqliteKsBztgSp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.util.MathUtil;
import com.abujlb.util.StringUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.springframework.stereotype.Component;

/**
 * 标准推广花费处理
 *
 * <AUTHOR>
 * @date 2025/03/14
 */
@Component
@BiPro(order = 4, qd = Qd.KS)
public class DefaultBiKsBztgDealProcess extends AbstractBiprocess {

    private final String OSSKEY_SPTG_PATTERN = "biks/%s/%s/bztg_sptg.json";
    private final String OSSKEY_ZBTG_PATTERN = "biks/%s/%s/bztg_zbtg.json";
    private final String OSSKEY_SPTG_SP_PATTERN = "biks/%s/%s/bztg_sptg_sp.json";

    @Override
    public void dealGoods() {
        super.dealGoods(
                SqliteKsBztgSp.class,
                OSSKEY_SPTG_SP_PATTERN,
                ((DataprobiKsMsg) getDataprobiBaseMsg()).getBztg()
        );
    }


    @Override
    public void dealShop() {
        super.dealShop(
                SqliteKsBztgDp.class,
                ((DataprobiKsMsg) getDataprobiBaseMsg()).getBztg(),
                (String) null
        );
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqliteKsBztgDp.class,
                ((DataprobiKsMsg) getDataprobiBaseMsg()).getBztg(),
                (String) null
        );
    }

    @Override
    protected <T extends AbstractSqliteDp> T dpObject(Class<T> tClass, String rq, String... colummns) {
        String sptgosskey = String.format(OSSKEY_SPTG_PATTERN, getDataprobiBaseMsg().getUserid(), rq);
        String zbtgosskey = String.format(OSSKEY_ZBTG_PATTERN, getDataprobiBaseMsg().getUserid(), rq);
        String sptgjson = biDataOssUtil.readJson(sptgosskey);
        String zbtgjson = biDataOssUtil.readJson(zbtgosskey);
        if (!StringUtil.isJsonArray2(sptgjson) || !StringUtil.isJsonArray2(zbtgjson)) {
            return null;
        }
        SqliteKsBztgDp sqliteKsBztgDp = new SqliteKsBztgDp();
        sqliteKsBztgDp.setQd(getDataprobiBaseMsg().getQd());
        sqliteKsBztgDp.setQd_userid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid());
        sqliteKsBztgDp.setRq(rq);
        sqliteKsBztgDp.setYhid(getDataprobiBaseMsg().getYhid());
        sqliteKsBztgDp.setUserid(getDataprobiBaseMsg().getUserid());

        JSONArray sptgjsonArray = JSONArray.parseArray(sptgjson);
        JSONArray zbtgjsonArray = JSONArray.parseArray(zbtgjson);
        for (int i = 0; i < sptgjsonArray.size(); i++) {
            JSONObject jsonObject = sptgjsonArray.getJSONObject(i);
            if ("costTotal".equals(jsonObject.getString("dataIndex"))) {
                sqliteKsBztgDp.setSptghf(MathUtil.add(sqliteKsBztgDp.getSptghf(), jsonObject.getDoubleValue("value") / 1000.0));
            }
            if ("t0GMV".equals(jsonObject.getString("dataIndex"))) {
                sqliteKsBztgDp.setBzsptgcjje(MathUtil.add(sqliteKsBztgDp.getBzsptgcjje(), jsonObject.getDoubleValue("value") / 1000.0));
            }
        }
        for (int i = 0; i < zbtgjsonArray.size(); i++) {
            JSONObject jsonObject = zbtgjsonArray.getJSONObject(i);
            if ("costTotal".equals(jsonObject.getString("dataIndex"))) {
                sqliteKsBztgDp.setZbtghf(MathUtil.add(sqliteKsBztgDp.getZbtghf(), jsonObject.getDoubleValue("value") / 1000.0));
            }
            if ("eventOrderPaymentAmount".equals(jsonObject.getString("dataIndex"))) {
                sqliteKsBztgDp.setBzsptgcjje(MathUtil.add(sqliteKsBztgDp.getBzsptgcjje(), jsonObject.getDoubleValue("value") / 1000.0));
            }
        }
        if (sqliteKsBztgDp.getSptghf() == 0 && sqliteKsBztgDp.getZbtghf() == 0 && sqliteKsBztgDp.getBzsptgcjje() == 0 && sqliteKsBztgDp.getBzzbtgcjje() == 0) {
            return null;
        }
        return (T) sqliteKsBztgDp;
    }
}
