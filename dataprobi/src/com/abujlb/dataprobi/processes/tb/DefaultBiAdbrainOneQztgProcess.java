package com.abujlb.dataprobi.processes.tb;

import com.abujlb.CommonConfig;
import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteDp;
import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.tb.DataprobiMsg;
import com.abujlb.dataprobi.bean.tb.SqliteQztgDp;
import com.abujlb.dataprobi.bean.tb.SqliteQztgSp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.util.AiztOneCSVReader;
import com.abujlb.dataprobi.util.MathUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2023/11/10
 */
@Component
@BiPro(order = 14, qd = Qd.TB)
public class DefaultBiAdbrainOneQztgProcess extends AbstractBiTXprocess {

    public static final String OSSKEY_PATTERN = "bi_jysj/%s/%s/bi_aiztone_qztg.csv";

    @Override
    public void dealGoods() {
        super.dealGoods(
                SqliteQztgSp.class,
                OSSKEY_PATTERN,
                ((DataprobiMsg) getDataprobiBaseMsg()).getQztg(),
                true
        );
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoods(
                SqliteQztgSp.class,
                OSSKEY_PATTERN,
                ((DataprobiMsg) getDataprobiBaseMsg()).getQztg(),
                true
        );
    }

    @Override
    protected <T extends AbstractSqliteSp> List<T> spList(Class<T> tClass, String ossKey, String rq) {
        if (((DataprobiMsg) getDataprobiBaseMsg()).getAiztone() == 0) {
            return Collections.emptyList();
        }

        if (!biDataOssUtil.exist(ossKey)) {
            return Collections.emptyList();
        }

        String temppath = CommonConfig.getString("tempdir") + UUID.randomUUID() + ".csv";
        biDataOssUtil.download(ossKey, temppath);

        List<SqliteQztgSp> list = AiztOneCSVReader.parseCSV(SqliteQztgSp.class, temppath, rq);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        return (List<T>) list;
    }


    @Override
    protected <T extends AbstractSqliteSp> void dealShopByGoods(String rq, List<T> list) {
        SqliteQztgDp sqliteQztgDp = dpObject(SqliteQztgDp.class, rq, StringUtils.EMPTY);
        if (sqliteQztgDp != null) {
            processSycmDpOTS(rq, sqliteQztgDp);
        }
    }

    @Override
    protected <T extends AbstractSqliteDp> T dpObject(Class<T> tClass, String rq, String... colummns) {
        String ossKey = String.format(OSSKEY_PATTERN, getDataprobiBaseMsg().getUserid(), rq);
        List<SqliteQztgSp> sqliteDmbztSps = this.spList(SqliteQztgSp.class, ossKey, rq);
        double qztghf = 0;
        double qztgcjje = 0;
        for (SqliteQztgSp t : sqliteDmbztSps) {
            qztghf = MathUtil.add(qztghf, t.getQztghf());
            qztgcjje = MathUtil.add(qztgcjje, t.getQztgcjje());
        }

        SqliteQztgDp dpData = new SqliteQztgDp();
        dpData.setQd(getDataprobiBaseMsg().getQd());
        dpData.setQd_userid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid());
        dpData.setRq(rq);
        dpData.setYhid(getDataprobiBaseMsg().getYhid());
        dpData.setUserid(getDataprobiBaseMsg().getUserid());
        dpData.setQztghf(qztghf);
        dpData.setQztgcjje(qztgcjje);
        return (T) dpData;
    }
}
