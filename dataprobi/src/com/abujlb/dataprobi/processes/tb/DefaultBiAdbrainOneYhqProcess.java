package com.abujlb.dataprobi.processes.tb;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.SqliteSp;
import com.abujlb.dataprobi.bean.tb.*;
import com.abujlb.dataprobi.constant.DataprobiConst;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.util.DataprobiDateUtil;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.abujlb.dataprobi.util.MathUtil;
import com.abujlb.util.StringUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/12/18
 */
@Component
@BiPro(order = 99, qd = Qd.TB)
public class DefaultBiAdbrainOneYhqProcess extends AbstractBiprocess {

    //无界店铺-优惠券
    private final String OSSKEY_PATTERN_AIZTONE = "bi_jysj/%s/%s/aizt_one_yhq.json";

    private final JSONArray temp = new JSONArray(0);

    @Autowired
    private DefaultBiAdbrainOneZtcProcess defaultBiZtcDealProcess;
    @Autowired
    private DefaultBiAdbrainOneYlmfProcess defaultBiYlmfDealProcess;
    @Autowired
    private DefaultBiAdbrainOneAiztProcess defaultBiAiztDealProcess;
    @Autowired
    private DefaultBiAdbrainOneQztgProcess defaultBiQztgDealProcess;
    @Autowired
    private DefaultBiAdbrainOneAiztItemProcess defaultBiAdbrainOneAiztItemProcess;
    @Autowired
    private DefaultBiAdbrainOneAiztShopProcess defaultBiAdbrainOneAiztShopProcess;

    @Override
    public void dealShop() {
        for (String rq : ((DataprobiMsg) getDataprobiBaseMsg()).getYhq()) {
            SqliteYhqDp sqliteYhqDp = aiztone2(rq);

            if (sqliteYhqDp != null) {
                sqliteYhqDp.setYlmfyhq(MathUtil.divide100(sqliteYhqDp.getYlmfyhq()));
                sqliteYhqDp.setZtcyhq(MathUtil.divide100(sqliteYhqDp.getZtcyhq()));
                sqliteYhqDp.setAiztyhq(MathUtil.divide100(sqliteYhqDp.getAiztyhq()));
                sqliteYhqDp.setNryxcjzbyhq(MathUtil.divide100(sqliteYhqDp.getNryxcjzbyhq()));
                sqliteYhqDp.setQztgyhq(MathUtil.divide100(sqliteYhqDp.getQztgyhq()));
                sqliteYhqDp.setTyyhq(-Math.abs(MathUtil.divide100(sqliteYhqDp.getTyyhq())));
                sqliteYhqDp.setAizt_item_yhq(-Math.abs(MathUtil.divide100(sqliteYhqDp.getAizt_item_yhq())));
                sqliteYhqDp.setAizt_shop_yhq(-Math.abs(MathUtil.divide100(sqliteYhqDp.getAizt_shop_yhq())));
            }

            processSycmDpOTS(rq, sqliteYhqDp);
        }
    }


    @Override
    public void dealGoods() {
        for (String rq : ((DataprobiMsg) getDataprobiBaseMsg()).getYhq()) {
            SqliteYhqDp sqliteYhqDp = aiztone2(rq);

            if (sqliteYhqDp != null) {
                sqliteYhqDp.setYlmfyhq(MathUtil.divide100(sqliteYhqDp.getYlmfyhq()));
                sqliteYhqDp.setZtcyhq(MathUtil.divide100(sqliteYhqDp.getZtcyhq()));
                sqliteYhqDp.setAiztyhq(MathUtil.divide100(sqliteYhqDp.getAiztyhq()));
                sqliteYhqDp.setNryxcjzbyhq(MathUtil.divide100(sqliteYhqDp.getNryxcjzbyhq()));
                sqliteYhqDp.setQztgyhq(MathUtil.divide100(sqliteYhqDp.getQztgyhq()));
                sqliteYhqDp.setTyyhq(-Math.abs(MathUtil.divide100(sqliteYhqDp.getTyyhq())));
                sqliteYhqDp.setAizt_item_yhq(-Math.abs(MathUtil.divide100(sqliteYhqDp.getAizt_item_yhq())));
                sqliteYhqDp.setAizt_shop_yhq(-Math.abs(MathUtil.divide100(sqliteYhqDp.getAizt_shop_yhq())));
            }

            dealGoods(sqliteYhqDp);
        }
    }

    @Override
    public boolean compareShop() {
        boolean result = true;
        for (String rq : ((DataprobiMsg) getDataprobiBaseMsg()).getYhq()) {
            SqliteYhqDp sqliteYhqDp = aiztone2(rq);
            if (sqliteYhqDp == null) {
                continue;
            }

            sqliteYhqDp.setYlmfyhq(MathUtil.divide100(sqliteYhqDp.getYlmfyhq()));
            sqliteYhqDp.setZtcyhq(MathUtil.divide100(sqliteYhqDp.getZtcyhq()));
            sqliteYhqDp.setAiztyhq(MathUtil.divide100(sqliteYhqDp.getAiztyhq()));
            sqliteYhqDp.setNryxcjzbyhq(MathUtil.divide100(sqliteYhqDp.getNryxcjzbyhq()));
            sqliteYhqDp.setQztgyhq(MathUtil.divide100(sqliteYhqDp.getQztgyhq()));
            sqliteYhqDp.setTyyhq(-Math.abs(MathUtil.divide100(sqliteYhqDp.getTyyhq())));
            sqliteYhqDp.setAizt_item_yhq(-Math.abs(MathUtil.divide100(sqliteYhqDp.getAizt_item_yhq())));
            sqliteYhqDp.setAizt_shop_yhq(-Math.abs(MathUtil.divide100(sqliteYhqDp.getAizt_shop_yhq())));

            if (!result) {
                processSycmDpOTS(rq, sqliteYhqDp);
                continue;
            }

            boolean flag = sqliteYhqDp.compare(getSycmDp(rq));
            if (!flag) {
                result = false;
                processSycmDpOTS(rq, sqliteYhqDp);
            }
        }
        return result;
    }


    @Override
    public boolean compareGoods() {
        boolean result = true;
        for (String rq : ((DataprobiMsg) getDataprobiBaseMsg()).getYhq()) {
            SqliteYhqDp sqliteYhqDp = aiztone2(rq);
            if (sqliteYhqDp == null) {
                continue;
            }

            sqliteYhqDp.setYlmfyhq(MathUtil.divide100(sqliteYhqDp.getYlmfyhq()));
            sqliteYhqDp.setZtcyhq(MathUtil.divide100(sqliteYhqDp.getZtcyhq()));
            sqliteYhqDp.setAiztyhq(MathUtil.divide100(sqliteYhqDp.getAiztyhq()));
            sqliteYhqDp.setNryxcjzbyhq(MathUtil.divide100(sqliteYhqDp.getNryxcjzbyhq()));
            sqliteYhqDp.setQztgyhq(MathUtil.divide100(sqliteYhqDp.getQztgyhq()));
            sqliteYhqDp.setTyyhq(-Math.abs(MathUtil.divide100(sqliteYhqDp.getTyyhq())));
            sqliteYhqDp.setAizt_item_yhq(-Math.abs(MathUtil.divide100(sqliteYhqDp.getAizt_item_yhq())));
            sqliteYhqDp.setAizt_shop_yhq(-Math.abs(MathUtil.divide100(sqliteYhqDp.getAizt_shop_yhq())));

            if (!result) {
                dealGoods(sqliteYhqDp);

                continue;
            }

            List<SqliteYhqSp> spList = getSpList(sqliteYhqDp);
            if (CollectionUtils.isEmpty(spList)) {
                continue;
            }

            Map<String, SqliteSp> map = getSycmSpMapByBbid(rq);
            boolean flag = true;
            for (SqliteYhqSp sqliteYhqSp : spList) {
                flag = sqliteYhqSp.compare(map.get(sqliteYhqSp.getBbid()));
                if (!flag) {
                    result = false;
                    break;
                }
            }

            if (!flag) {
                dealGoods(sqliteYhqDp, spList);
            }
        }
        return result;
    }

    private SqliteYhqDp aiztone2(String rq) {
        String osskey = String.format(OSSKEY_PATTERN_AIZTONE, getDataprobiBaseMsg().getUserid(), rq);

        String yhqjson = biDataOssUtil.readJson(osskey);
        JSONArray yhqList = StringUtil.isJsonArray(yhqjson) ? JSONArray.parseArray(yhqjson) : temp;
        if (CollectionUtils.isEmpty(yhqList)) {
            return null;
        }

        SqliteYhqDp sqliteYhqDp = new SqliteYhqDp();
        sqliteYhqDp.setQd(getDataprobiBaseMsg().getQd());
        sqliteYhqDp.setUserid(getDataprobiBaseMsg().getUserid());
        sqliteYhqDp.setQd_userid(sqliteYhqDp.getQd() + "_" + sqliteYhqDp.getUserid());
        sqliteYhqDp.setYhid(getDataprobiBaseMsg().getYhid());
        sqliteYhqDp.setRq(rq);
        sqliteYhqDp.setDpmc(getDataprobiBaseMsg().getDpmc());

        for (int i = 0; i < yhqList.size(); i++) {
            JSONObject jsonObject = yhqList.getJSONObject(i);
            String settleTime = jsonObject.getString("settleTime");
            if (settleTime == null || !settleTime.equals(DataprobiDateUtil.getDaysAfterAsString(rq, 1))) {
                continue;
            }
            JSONArray sceneArray = jsonObject.getJSONArray("scene");
            if (CollectionUtils.isEmpty(sceneArray)) {
                continue;
            }
            List<String> sceneList = sceneArray.stream().map(obj -> (String) obj).collect(Collectors.toList());
            String couponName = jsonObject.getString("name");
            double changeAmount = jsonObject.getDoubleValue("changeAmount");
            if (couponName.contains("万相台") || sceneList.contains("多场景推广") || sceneList.contains("多场景可用") || sceneList.contains("多场景") || sceneList.size() > 1) {
                sqliteYhqDp.setAiztyhq(MathUtil.add(sqliteYhqDp.getAiztyhq(), changeAmount));
            } else if (couponName.contains("直通车") || sceneList.contains("关键词推广")) {
                sqliteYhqDp.setZtcyhq(MathUtil.add(sqliteYhqDp.getZtcyhq(), changeAmount));
            } else if (couponName.contains("引力魔方") || sceneList.contains("人群推广")) {
                sqliteYhqDp.setYlmfyhq(MathUtil.add(sqliteYhqDp.getYlmfyhq(), changeAmount));
            } else if (sceneList.contains("内容营销-超级直播") || sceneList.contains("内容营销")) {
                //2024-12-18日  原来只取了 《内容营销-超级直播》  ，现改为取含《内容营销》的  算作内容营销优惠券
                sqliteYhqDp.setNryxcjzbyhq(MathUtil.add(sqliteYhqDp.getNryxcjzbyhq(), changeAmount));
            } else if (sceneList.contains("全站推广")) {
                sqliteYhqDp.setQztgyhq(MathUtil.add(sqliteYhqDp.getQztgyhq(), changeAmount));
            } else if (sceneList.contains("通用场景")) {
                //https://www.tapd.cn/47708728/prong/stories/view/1147708728001009702
                //2024-10-30日 修改
                sqliteYhqDp.setTyyhq(MathUtil.add(sqliteYhqDp.getTyyhq(), changeAmount));
            } else if (CollectionUtils.isNotEmpty(sceneList) && sceneList.get(0).equals("货品运营")) {
                sqliteYhqDp.setAizt_item_yhq(MathUtil.add(sqliteYhqDp.getAizt_item_yhq(), changeAmount));
            } else if (CollectionUtils.isNotEmpty(sceneList) && sceneList.get(0).equals("店铺运营")) {
                sqliteYhqDp.setAizt_shop_yhq(MathUtil.add(sqliteYhqDp.getAizt_shop_yhq(), changeAmount));
            }
        }
        return sqliteYhqDp;
    }

    private void dealGoods(SqliteYhqDp sqliteYhqDp) {
        List<SqliteYhqSp> sqliteYhqSps = getSpList(sqliteYhqDp);
        if (sqliteYhqDp != null) {
            processSycmSpOTS(sqliteYhqDp.getRq(), sqliteYhqSps);
        }
    }

    private void dealGoods(SqliteYhqDp sqliteYhqDp, List<SqliteYhqSp> sqliteYhqSps) {
        processSycmSpOTS(sqliteYhqDp.getRq(), sqliteYhqSps);
    }

    private List<SqliteYhqSp> getSpList(SqliteYhqDp sqliteYhqDp) {
        if (sqliteYhqDp == null) {
            return null;
        }

        Map<String, SqliteYhqSp> map = new HashMap<>();

        if (sqliteYhqDp.getZtcyhq() > 0) {
            dealGoodsZtc(sqliteYhqDp, map);
        }

        if (sqliteYhqDp.getAiztyhq() > 0) {
            dealGoodsAizt(sqliteYhqDp, map);
        }

        if (sqliteYhqDp.getYlmfyhq() > 0) {
            dealGoodsYlmf(sqliteYhqDp, map);
        }

        if (sqliteYhqDp.getQztgyhq() > 0) {
            dealGoodsQztg(sqliteYhqDp, map);
        }

        if (sqliteYhqDp.getTyyhq() != 0) {
            dealGoodsTyyhq(sqliteYhqDp, map);
        }

        if (sqliteYhqDp.getAizt_item_yhq() != 0) {
            dealGoodsAiztItem(sqliteYhqDp, map);
        }

        if (sqliteYhqDp.getAizt_shop_yhq() != 0) {
            dealGoodsAiztShop(sqliteYhqDp, map);
        }

        ArrayList<SqliteYhqSp> sqliteYhqSps = new ArrayList<>(map.values());
        for (SqliteYhqSp sqliteYhqSp : sqliteYhqSps) {
            sqliteYhqSp.setQd(getDataprobiBaseMsg().getQd());
            sqliteYhqSp.setUserid(getDataprobiBaseMsg().getUserid());
            sqliteYhqSp.setQd_userid_bbid(sqliteYhqSp.getQd() + "_" + sqliteYhqSp.getUserid() + "_" + sqliteYhqSp.getBbid());
            sqliteYhqSp.setYhid(getDataprobiBaseMsg().getYhid());
            sqliteYhqSp.setRq(sqliteYhqDp.getRq());
            if (sqliteYhqSp.getTyyhq() != 0) {
                sqliteYhqSp.setTyyhq(-Math.abs(sqliteYhqSp.getTyyhq()));
            }
            if (sqliteYhqSp.getAizt_item_yhq() != 0) {
                sqliteYhqSp.setAizt_item_yhq(-Math.abs(sqliteYhqSp.getAizt_item_yhq()));
            }
            if (sqliteYhqSp.getAizt_shop_yhq() != 0) {
                sqliteYhqSp.setAizt_shop_yhq(-Math.abs(sqliteYhqSp.getAizt_shop_yhq()));
            }
        }

        return sqliteYhqSps;
    }

    private void dealGoodsTyyhq(SqliteYhqDp sqliteYhqDp, Map<String, SqliteYhqSp> map) {
        //获取店铺维度的无界总花费
        String aiztone_dpdata = biSycmDpDataTsDao.getRow(sqliteYhqDp.getQd_userid(), sqliteYhqDp.getRq(), "tx_aiztone");
        if (!StringUtil.isJson2(aiztone_dpdata)) {
            return;
        }

        JSONObject dpdata = JSONObject.parseObject(aiztone_dpdata);
        double hf_total = FastJSONObjAttrToNumber.toDouble(dpdata, "totalCharge");
        if (hf_total <= 0) {
            return;
        }

        //获取每个宝贝的无界总花费
        String spDbKey = String.format(DataprobiConst.SYCM_SP_DAY, sqliteYhqDp.getRq().replace("-", ""), sqliteYhqDp.getYhid(), sqliteYhqDp.getQd(), sqliteYhqDp.getUserid());
        List<SqliteSp> sqliteSps = sqliteDbUtil.readSpList(spDbKey);
        if (CollectionUtils.isEmpty(sqliteSps)) {
            return;
        }

        for (SqliteSp sqliteSp : sqliteSps) {
            String otherOrDefault = sqliteSp.getOtherOrDefault();
            JSONObject otherObj = JSONObject.parseObject(otherOrDefault);
            double ztchf = FastJSONObjAttrToNumber.toDouble(otherObj, "ztchf");
            double aizt_shop_qdzd_hf = FastJSONObjAttrToNumber.toDouble(otherObj, "aizt_shop_qdzd_hf");
            double aizt_shop_dpzd_hf = FastJSONObjAttrToNumber.toDouble(otherObj, "aizt_shop_dpzd_hf");
            double ylmfhf = FastJSONObjAttrToNumber.toDouble(otherObj, "ylmfhf");
            double xstghf = FastJSONObjAttrToNumber.toDouble(otherObj, "xstghf");
            double dmbzthf = FastJSONObjAttrToNumber.toDouble(otherObj, "dmbzthf");
            double qztghf = FastJSONObjAttrToNumber.toDouble(otherObj, "qztghf");
            double nryxdsphf = FastJSONObjAttrToNumber.toDouble(otherObj, "nryxdsphf");


            //每个商品的无界总花费
            //公式：全站推广+ 多目标直投 + 直通车 + 店铺运营-全店智投 + 店铺运营-店铺直达   + 引力魔方 +  线索推广  + 内容营销-短视频
            double sp_hf_total = MathUtil.add(qztghf, dmbzthf, ztchf, aizt_shop_qdzd_hf, aizt_shop_dpzd_hf, ylmfhf, xstghf, nryxdsphf);
            //所占比例
            double ratio = MathUtil.divide(sp_hf_total, hf_total, 8);
            //通过比例计算商品所占的通用优惠券
            double sp_tyyhq = MathUtil.multipy(sqliteYhqDp.getTyyhq(), ratio, 8);

            SqliteYhqSp sqliteYhqSp = map.getOrDefault(sqliteSp.getBbid(), new SqliteYhqSp(sqliteSp.getBbid()));
            sqliteYhqSp.setTyyhq(MathUtil.addAbsScale(sqliteYhqSp.getTyyhq(), sp_tyyhq, 8));
            map.put(sqliteYhqSp.getBbid(), sqliteYhqSp);
        }
    }

    private void dealGoodsAiztItem(SqliteYhqDp sqliteYhqDp, Map<String, SqliteYhqSp> map) {
        List<SqliteAiztItemSp> list = defaultBiAdbrainOneAiztItemProcess.spList(SqliteAiztItemSp.class, DefaultBiAdbrainOneAiztItemProcess.OSSKEY_PATTERN, sqliteYhqDp.getRq());
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        double sum = 0;
        for (SqliteAiztItemSp sp : list) {
            sum = MathUtil.add(sp.getAizt_item_hf(), sum);
        }

        for (SqliteAiztItemSp sp : list) {
            double ratio = MathUtil.divide(sp.getAizt_item_hf(), sum, 8);
            double yhq = MathUtil.multipy(sqliteYhqDp.getAizt_item_yhq(), ratio, 4);

            SqliteYhqSp sqliteYhqSp = map.getOrDefault(sp.getBbid(), new SqliteYhqSp(sp.getBbid()));
            sqliteYhqSp.setAizt_item_yhq(MathUtil.add(sqliteYhqSp.getAizt_item_yhq(), Math.abs(yhq)));
            map.put(sqliteYhqSp.getBbid(), sqliteYhqSp);
        }
    }

    private void dealGoodsAiztShop(SqliteYhqDp sqliteYhqDp, Map<String, SqliteYhqSp> map) {
        List<SqliteAiztShopSp> list = defaultBiAdbrainOneAiztShopProcess.spList(SqliteAiztShopSp.class, DefaultBiAdbrainOneAiztShopProcess.OSSKEY_PATTERN, sqliteYhqDp.getRq());
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        double sum = 0;
        for (SqliteAiztShopSp sp : list) {
            sum = MathUtil.add(sp.getAizt_shop_hf(), sum);
        }

        for (SqliteAiztShopSp sp : list) {
            double ratio = MathUtil.divide(sp.getAizt_shop_hf(), sum, 8);
            double yhq = MathUtil.multipy(sqliteYhqDp.getAizt_shop_yhq(), ratio, 4);

            SqliteYhqSp sqliteYhqSp = map.getOrDefault(sp.getBbid(), new SqliteYhqSp(sp.getBbid()));
            sqliteYhqSp.setAizt_shop_yhq(MathUtil.add(sqliteYhqSp.getAizt_shop_yhq(), Math.abs(yhq)));
            map.put(sqliteYhqSp.getBbid(), sqliteYhqSp);
        }
    }

    private void dealGoodsQztg(SqliteYhqDp sqliteYhqDp, Map<String, SqliteYhqSp> map) {
        List<SqliteQztgSp> sqliteQztgSps = defaultBiQztgDealProcess.spList(SqliteQztgSp.class, DefaultBiAdbrainOneQztgProcess.OSSKEY_PATTERN, sqliteYhqDp.getRq());
        if (CollectionUtils.isEmpty(sqliteQztgSps)) {
            return;
        }
        double sum = 0;
        for (SqliteQztgSp sqliteQztgSp : sqliteQztgSps) {
            sum = MathUtil.add(sqliteQztgSp.getQztghf(), sum);
        }

        for (SqliteQztgSp sqliteQztgSp : sqliteQztgSps) {
            double ratio = MathUtil.divide(sqliteQztgSp.getQztghf(), sum, 8);
            double spQztgYhq = MathUtil.multipy(sqliteYhqDp.getQztgyhq(), ratio, 4);

            SqliteYhqSp sqliteYhqSp = map.getOrDefault(sqliteQztgSp.getBbid(), new SqliteYhqSp(sqliteQztgSp.getBbid()));
            sqliteYhqSp.setQztgyhq(MathUtil.add(sqliteYhqSp.getQztgyhq(), spQztgYhq));
            map.put(sqliteYhqSp.getBbid(), sqliteYhqSp);
        }
    }

    private void dealGoodsYlmf(SqliteYhqDp sqliteYhqDp, Map<String, SqliteYhqSp> map) {
        List<SqliteYlmfSp> sqliteYlmfSps = defaultBiYlmfDealProcess.spList(SqliteYlmfSp.class, DefaultBiAdbrainOneYlmfProcess.OSSKEY_PATTERN, sqliteYhqDp.getRq());
        if (CollectionUtils.isEmpty(sqliteYlmfSps)) {
            return;
        }
        double sum = 0;
        for (SqliteYlmfSp sqliteAiztSp : sqliteYlmfSps) {
            sum = MathUtil.add(sqliteAiztSp.getYlmfhf(), sum);
        }

        for (SqliteYlmfSp sqliteYlmfSp : sqliteYlmfSps) {
            double ratio = MathUtil.divide(sqliteYlmfSp.getYlmfhf(), sum, 8);
            double spYlmfyhq = MathUtil.multipy(sqliteYhqDp.getYlmfyhq(), ratio, 4);

            SqliteYhqSp sqliteYhqSp = map.getOrDefault(sqliteYlmfSp.getBbid(), new SqliteYhqSp(sqliteYlmfSp.getBbid()));
            sqliteYhqSp.setYlmfyhq(MathUtil.add(sqliteYhqSp.getYlmfyhq(), spYlmfyhq));
            map.put(sqliteYhqSp.getBbid(), sqliteYhqSp);
        }
    }

    private void dealGoodsAizt(SqliteYhqDp sqliteYhqDp, Map<String, SqliteYhqSp> map) {
        List<SqliteAiztSp> sqliteAiztSps = defaultBiAiztDealProcess.spList(SqliteAiztSp.class, String.format(DefaultBiAdbrainOneAiztProcess.OSSKEY_PATTERN, getDataprobiBaseMsg().getUserid(), sqliteYhqDp.getRq()), sqliteYhqDp.getRq());
        if (CollectionUtils.isEmpty(sqliteAiztSps)) {
            return;
        }
        double sum = 0;
        for (SqliteAiztSp sqliteAiztSp : sqliteAiztSps) {
            sum = MathUtil.add(sqliteAiztSp.getAizthf(), sum);
        }

        for (SqliteAiztSp sqliteAiztSp : sqliteAiztSps) {
            double ratio = MathUtil.divide(sqliteAiztSp.getAizthf(), sum, 8);
            double spAiztyhq = MathUtil.multipy(sqliteYhqDp.getAiztyhq(), ratio, 4);

            SqliteYhqSp sqliteYhqSp = map.getOrDefault(sqliteAiztSp.getBbid(), new SqliteYhqSp(sqliteAiztSp.getBbid()));
            sqliteYhqSp.setAiztyhq(MathUtil.add(sqliteYhqSp.getAiztyhq(), spAiztyhq));
            map.put(sqliteYhqSp.getBbid(), sqliteYhqSp);
        }
    }

    private void dealGoodsZtc(SqliteYhqDp sqliteYhqDp, Map<String, SqliteYhqSp> map) {
        List<SqliteZtcSp> sqliteZtcSps = defaultBiZtcDealProcess.spList(SqliteZtcSp.class, String.format(DefaultBiAdbrainOneZtcProcess.OSSKEY_PATTERN.replace("$rq$", sqliteYhqDp.getRq()), getDataprobiBaseMsg().getUserid(), sqliteYhqDp.getRq()), sqliteYhqDp.getRq());
        if (CollectionUtils.isEmpty(sqliteZtcSps)) {
            return;
        }
        double sum = 0;
        for (SqliteZtcSp sqliteZtcSp : sqliteZtcSps) {
            sum = MathUtil.add(sqliteZtcSp.getZtchf(), sum);
        }

        for (SqliteZtcSp sqliteZtcSp : sqliteZtcSps) {
            double ratio = MathUtil.divide(sqliteZtcSp.getZtchf(), sum, 8);
            double spZtcyhq = MathUtil.multipy(sqliteYhqDp.getZtcyhq(), ratio, 4);

            SqliteYhqSp sqliteYhqSp = map.getOrDefault(sqliteZtcSp.getBbid(), new SqliteYhqSp(sqliteZtcSp.getBbid()));
            sqliteYhqSp.setZtcyhq(MathUtil.add(sqliteYhqSp.getZtcyhq(), spZtcyhq));
            map.put(sqliteYhqSp.getBbid(), sqliteYhqSp);
        }
    }

}
