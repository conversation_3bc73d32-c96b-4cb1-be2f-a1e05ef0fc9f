package com.abujlb.dataprobi.processes.tb;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteDp;
import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.SqliteDp;
import com.abujlb.dataprobi.bean.SqliteSp;
import com.abujlb.dataprobi.bean.tb.DataprobiMsg;
import com.abujlb.dataprobi.bean.tb.SqliteAiztoneZhlvDp;
import com.abujlb.dataprobi.bean.tb.SqliteAiztoneZhlvSp;
import com.abujlb.dataprobi.constant.DataprobiConst;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.threads.BiThreadLocals;
import com.abujlb.dataprobi.util.MathUtil;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/2/26
 */
@Component
@BiPro(order = 15, qd = Qd.TB)
public class DefaultBiAiztoneZhlvDealProcess extends AbstractBiprocess {

    public static final String OSSKEY_PATTERN_NOT_REAL_USED = "bi_jysj/%s/%s/";
    private final String[] COLUMNS = {};

    @Override
    public void dealGoods() {
        DataprobiMsg dataprobiMsg = (DataprobiMsg) BiThreadLocals.getMsgBean();
        if (dataprobiMsg.getAiztone() == 0) {
            return;
        }

        List<String> rqList = new ArrayList<>();
        rqList.addAll(dataprobiMsg.getZtc());
        rqList.addAll(dataprobiMsg.getYlmf());
        rqList.addAll(dataprobiMsg.getAizt());
        rqList = rqList.stream().distinct().collect(Collectors.toList());
        if (rqList.isEmpty()) {
            return;
        }
        super.dealGoods(SqliteAiztoneZhlvSp.class, OSSKEY_PATTERN_NOT_REAL_USED, rqList);
    }

    @Override
    public void dealShop() {
        DataprobiMsg dataprobiMsg = (DataprobiMsg) BiThreadLocals.getMsgBean();
        if (dataprobiMsg.getAiztone() == 0) {
            return;
        }

        List<String> rqList = new ArrayList<>();
        rqList.addAll(dataprobiMsg.getZtc());
        rqList.addAll(dataprobiMsg.getYlmf());
        rqList.addAll(dataprobiMsg.getAizt());
        rqList = rqList.stream().distinct().collect(Collectors.toList());
        if (rqList.isEmpty()) {
            return;
        }

        super.dealShop(SqliteAiztoneZhlvDp.class, rqList, COLUMNS);
    }

    @Override
    public boolean compareGoods() {
        DataprobiMsg dataprobiMsg = (DataprobiMsg) BiThreadLocals.getMsgBean();
        if (dataprobiMsg.getAiztone() == 0) {
            return true;
        }

        List<String> rqList = dataprobiMsg.getZtc();
        rqList.addAll(dataprobiMsg.getYlmf());
        rqList.addAll(dataprobiMsg.getAizt());
        rqList = rqList.stream().distinct().collect(Collectors.toList());
        if (rqList.isEmpty()) {
            return true;
        }

        return super.compareGoods(SqliteAiztoneZhlvSp.class, OSSKEY_PATTERN_NOT_REAL_USED, rqList);
    }

    @Override
    public boolean compareShop() {
        DataprobiMsg dataprobiMsg = (DataprobiMsg) BiThreadLocals.getMsgBean();
        if (dataprobiMsg.getAiztone() == 0) {
            return true;
        }

        List<String> rqList = dataprobiMsg.getZtc();
        rqList.addAll(dataprobiMsg.getYlmf());
        rqList.addAll(dataprobiMsg.getAizt());
        rqList = rqList.stream().distinct().collect(Collectors.toList());
        if (rqList.isEmpty()) {
            return true;
        }

        return super.compareShop(SqliteAiztoneZhlvDp.class, rqList, COLUMNS);
    }


    @Override
    protected <T extends AbstractSqliteDp> T dpObject(Class<T> tClass, String rq, String... colummns) {
        String key = String.format(DataprobiConst.SYCM_DP_DAY, rq.replaceAll("-", ""), getDataprobiBaseMsg().getYhid(), getDataprobiBaseMsg().getQd(), getDataprobiBaseMsg().getUserid());
        SqliteDp sqliteDp = sqliteDbUtil.readDp(key);
        if (sqliteDp == null) {
            return null;
        }

        String other = sqliteDp.getOtherOrDefault();
        JSONObject otherObj = JSONObject.parseObject(other);

        int ztccjbs = otherObj.getIntValue("ztccjbs");
        int ztcdjl = otherObj.getIntValue("ztcdjl");
        int ylmfcjbs = otherObj.getIntValue("ylmfcjbs");
        int ylmfdjl = otherObj.getIntValue("ylmfdjl");
        int aiztcjbs = otherObj.getIntValue("aiztcjbs");
        int aiztdjl = otherObj.getIntValue("aiztdjl");

        int aiztonecjbs = ztccjbs + ylmfcjbs + aiztcjbs;
        int aiztonedjl = ztcdjl + ylmfdjl + aiztdjl;
        double aiztonezhlv = MathUtil.divide(aiztonecjbs, aiztonedjl);

        SqliteAiztoneZhlvDp sqliteAiztoneZhlvDp = new SqliteAiztoneZhlvDp();
        sqliteAiztoneZhlvDp.setQd(getDataprobiBaseMsg().getQd());
        sqliteAiztoneZhlvDp.setQd_userid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid());
        sqliteAiztoneZhlvDp.setRq(rq);
        sqliteAiztoneZhlvDp.setYhid(getDataprobiBaseMsg().getYhid());
        sqliteAiztoneZhlvDp.setUserid(getDataprobiBaseMsg().getUserid());
        sqliteAiztoneZhlvDp.setAiztonecjbs(aiztonecjbs);
        sqliteAiztoneZhlvDp.setAiztonedjl(aiztonedjl);
        sqliteAiztoneZhlvDp.setAiztonezhlv(aiztonezhlv);

        return (T) sqliteAiztoneZhlvDp;
    }

    @Override
    protected <T extends AbstractSqliteSp> List<T> spList(Class<T> tClass, String ossKey, String rq) {
        String key = String.format(DataprobiConst.SYCM_SP_DAY, rq.replaceAll("-", ""), getDataprobiBaseMsg().getYhid(), getDataprobiBaseMsg().getQd(), getDataprobiBaseMsg().getUserid());
        List<SqliteSp> sqliteSps = sqliteDbUtil.readSpList(key);
        if (CollectionUtils.isEmpty(sqliteSps)) {
            return null;
        }

        List<SqliteAiztoneZhlvSp> list = new ArrayList<>(sqliteSps.size());
        for (SqliteSp sqliteSp : sqliteSps) {
            String other = sqliteSp.getOtherOrDefault();
            JSONObject otherObj = JSONObject.parseObject(other);

            int ztccjbs = otherObj.getIntValue("ztccjbs");
            int ztcdjl = otherObj.getIntValue("ztcdjl");
            int ylmfcjbs = otherObj.getIntValue("ylmfcjbs");
            int ylmfdjl = otherObj.getIntValue("ylmfdjl");
            int aiztcjbs = otherObj.getIntValue("aiztcjbs");
            int aiztdjl = otherObj.getIntValue("aiztdjl");

            int aiztonecjbs = ztccjbs + ylmfcjbs + aiztcjbs;
            int aiztonedjl = ztcdjl + ylmfdjl + aiztdjl;
            if (aiztonecjbs == 0 && aiztonedjl == 0) {
                continue;
            }

            double aiztonezhlv = MathUtil.divide(aiztonecjbs, aiztonedjl);

            SqliteAiztoneZhlvSp sqliteAiztoneZhlvSp = new SqliteAiztoneZhlvSp();
            sqliteAiztoneZhlvSp.setQd(getDataprobiBaseMsg().getQd());
            sqliteAiztoneZhlvSp.setQd_userid_bbid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid() + "_" + sqliteSp.getBbid());
            sqliteAiztoneZhlvSp.setRq(rq);
            sqliteAiztoneZhlvSp.setYhid(getDataprobiBaseMsg().getYhid());
            sqliteAiztoneZhlvSp.setUserid(getDataprobiBaseMsg().getUserid());
            sqliteAiztoneZhlvSp.setAiztonecjbs(aiztonecjbs);
            sqliteAiztoneZhlvSp.setAiztonedjl(aiztonedjl);
            sqliteAiztoneZhlvSp.setAiztonezhlv(aiztonezhlv);
            sqliteAiztoneZhlvSp.setBbid(sqliteSp.getBbid());
            list.add(sqliteAiztoneZhlvSp);
        }
        return (List<T>) list;
    }
}
