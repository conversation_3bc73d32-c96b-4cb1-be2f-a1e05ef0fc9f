package com.abujlb.dataprobi.processes.tb;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.tb.DataprobiMsg;
import com.abujlb.dataprobi.bean.tb.SqlitePpxxYxtgXkSp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/9/4
 */
@Component
@BiPro(order = 14, qd = Qd.TB)
public class DefaultBiPpxxXkyxtgProcess extends AbstractBiprocess {

    public static final String OSSKEY_PATTERN = "bi_jysj/%s/%s/ppxx_yxtg_xk.json";

    @Override
    public void dealGoods() {
        super.dealGoods(
                SqlitePpxxYxtgXkSp.class,
                OSSKEY_PATTERN,
                ((DataprobiMsg) getDataprobiBaseMsg()).getPpxx_yxtg_xk()
        );
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoods(
                SqlitePpxxYxtgXkSp.class,
                OSSKEY_PATTERN,
                ((DataprobiMsg) getDataprobiBaseMsg()).getPpxx_yxtg_xk()
        );
    }
}
