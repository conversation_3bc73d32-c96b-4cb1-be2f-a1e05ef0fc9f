package com.abujlb.dataprobi.processes.tb;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.tb.DataprobiMsg;
import com.abujlb.dataprobi.bean.tb.SqliteSpxgDp;
import com.abujlb.dataprobi.bean.tb.SqliteSpxgSp;
import com.abujlb.dataprobi.constant.DataprobiConst;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/5/11 15:49
 */
@Component
@BiPro(order = 2, qd = Qd.TB)
public class DefaultBiSpxgDealProcess extends AbstractBiprocess {

    private final String OSSKEY_PATTERN = "bi_jysj/%s/%s/spxg_0_" + DataprobiConst.RQLX_DAY + ".json";
    private final String[] COLUMNS = {BiSycmDpDataTsDao.TX_COLUMN_SPXG, BiSycmDpDataTsDao.TX_COLUMN_SPXG_JYSJ};

    @Override
    public void dealGoods() {
        super.dealGoods(
                SqliteSpxgSp.class,
                OSSKEY_PATTERN,
                ((DataprobiMsg) getDataprobiBaseMsg()).getSpxg()
        );
    }

    @Override
    public void dealShop() {
        super.dealShop(
                SqliteSpxgDp.class,
                ((DataprobiMsg) getDataprobiBaseMsg()).getSpxgdp(),
                COLUMNS
        );
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoods(
                SqliteSpxgSp.class,
                OSSKEY_PATTERN,
                ((DataprobiMsg) getDataprobiBaseMsg()).getSpxg()
        );
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqliteSpxgDp.class,
                ((DataprobiMsg) getDataprobiBaseMsg()).getSpxgdp(),
                COLUMNS
        );
    }
}
