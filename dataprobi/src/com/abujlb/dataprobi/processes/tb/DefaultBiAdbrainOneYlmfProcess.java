package com.abujlb.dataprobi.processes.tb;

import com.abujlb.CommonConfig;
import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteDp;
import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.tb.DataprobiMsg;
import com.abujlb.dataprobi.bean.tb.SqliteYlmfDp;
import com.abujlb.dataprobi.bean.tb.SqliteYlmfSp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.util.AiztOneCSVReader;
import com.abujlb.dataprobi.util.MathUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2022/5/11 15:49
 */
@Component
@BiPro(order = 5, qd = Qd.TB)
public class DefaultBiAdbrainOneYlmfProcess extends AbstractBiTXprocess {

    public static final String OSSKEY_PATTERN = "bi_jysj/%s/%s/bi_aiztone_ylmf.csv";

    @Override
    public void dealGoods() {
        super.dealGoods(
                SqliteYlmfSp.class,
                OSSKEY_PATTERN,
                ((DataprobiMsg) getDataprobiBaseMsg()).getYlmf(),
                true
        );
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoods(
                SqliteYlmfSp.class,
                OSSKEY_PATTERN,
                ((DataprobiMsg) getDataprobiBaseMsg()).getYlmf(),
                true
        );
    }

    @Override
    protected <T extends AbstractSqliteSp> List<T> spList(Class<T> tClass, String ossKey, String rq) {
        if (((DataprobiMsg) getDataprobiBaseMsg()).getAiztone() == 0) {
            return Collections.emptyList();
        }

        //万相台无界
        if (!biDataOssUtil.exist(ossKey)) {
            return Collections.emptyList();
        }

        String temppath = CommonConfig.getString("tempdir") + UUID.randomUUID() + ".csv";
        biDataOssUtil.download(ossKey, temppath);

        List<SqliteYlmfSp> list = AiztOneCSVReader.parseCSV(SqliteYlmfSp.class, temppath, rq);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        return (List<T>) list;
    }

    @Override
    protected <T extends AbstractSqliteSp> void dealShopByGoods(String rq, List<T> list) {
        SqliteYlmfDp sqliteYlmfDp = dpObject(SqliteYlmfDp.class, rq, StringUtils.EMPTY);
        if (sqliteYlmfDp != null) {
            processSycmDpOTS(rq, sqliteYlmfDp);
        }
    }

    @Override
    protected <T extends AbstractSqliteDp> T dpObject(Class<T> tClass, String rq, String... colummns) {
        String ossKey = String.format(OSSKEY_PATTERN, getDataprobiBaseMsg().getUserid(), rq);
        List<SqliteYlmfSp> list = spList(SqliteYlmfSp.class, ossKey, rq);
        double ylmfhf = 0;
        double ylmfcjje = 0;
        int ylmfzrllbgl = 0;
        double ylmfzrllzhje = 0;
        int ylmfzxl = 0;
        int ylmfdjl = 0;
        int ylmfcjbs = 0;
        for (SqliteYlmfSp t : list) {
            ylmfhf = MathUtil.add(ylmfhf, t.getYlmfhf());
            ylmfcjje = MathUtil.add(ylmfcjje, t.getYlmfcjje());
            ylmfzrllbgl += t.getYlmfzrllbgl();
            ylmfzrllzhje += MathUtil.add(ylmfzrllzhje, t.getYlmfzrllzhje());
            ylmfzxl += t.getYlmfzxl();
            ylmfdjl += t.getYlmfdjl();
            ylmfcjbs += t.getYlmfcjbs();
        }
        SqliteYlmfDp sqliteylmfDp = new SqliteYlmfDp();
        sqliteylmfDp.setQd(getDataprobiBaseMsg().getQd());
        sqliteylmfDp.setQd_userid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid());
        sqliteylmfDp.setRq(rq);
        sqliteylmfDp.setYhid(getDataprobiBaseMsg().getYhid());
        sqliteylmfDp.setUserid(getDataprobiBaseMsg().getUserid());
        sqliteylmfDp.setYlmfhf(ylmfhf);
        sqliteylmfDp.setYlmfcjje(ylmfcjje);
        sqliteylmfDp.setYlmfzxl(ylmfzxl);
        sqliteylmfDp.setYlmfdjl(ylmfdjl);
        sqliteylmfDp.setYlmfcjbs(ylmfcjbs);
        sqliteylmfDp.setYlmfzrllbgl(ylmfzrllbgl);
        sqliteylmfDp.setYlmfzrllzhje(ylmfzrllzhje);
        return (T) sqliteylmfDp;
    }
}
