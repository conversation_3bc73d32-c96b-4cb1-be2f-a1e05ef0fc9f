package com.abujlb.dataprobi.processes.tb;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.tb.DataprobiMsg;
import com.abujlb.dataprobi.bean.tb.SqliteFwCustomerDp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/12/18
 */
@Component
@BiPro(order = 10, qd = Qd.TB)
public class DefaultBiSycmFwCustomerSummaryDealProcess extends AbstractBiprocess {

    private final String[] COLUMNS = {BiSycmDpDataTsDao.TX_COLUMN_FW_CUSTOMER};

    @Override
    public void dealShop() {
        super.dealShop(
                SqliteFwCustomerDp.class,
                ((DataprobiMsg) getDataprobiBaseMsg()).getFwCustomer(),
                COLUMNS
        );
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqliteFwCustomerDp.class,
                ((DataprobiMsg) getDataprobiBaseMsg()).getFwCustomer(),
                COLUMNS
        );
    }
}
