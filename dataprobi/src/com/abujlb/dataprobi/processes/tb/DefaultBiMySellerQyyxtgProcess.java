package com.abujlb.dataprobi.processes.tb;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.tb.DataprobiMsg;
import com.abujlb.dataprobi.bean.tb.SqliteQyyxtgDp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/4/1
 */
@Component
@BiPro(order = 6, qd = Qd.TB)
public class DefaultBiMySellerQyyxtgProcess extends AbstractBiTXprocess {

    private final String[] COLUMNS = {BiSycmDpDataTsDao.TX_COLUMN_QYYXTG};


    @Override
    public void dealShop() {
        super.dealShop(
                SqliteQyyxtgDp.class,
                ((DataprobiMsg) getDataprobiBaseMsg()).getQyyxtg(),
                COLUMNS
        );
    }


    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqliteQyyxtgDp.class,
                ((DataprobiMsg) getDataprobiBaseMsg()).getQyyxtg(),
                COLUMNS
        );
    }
}
