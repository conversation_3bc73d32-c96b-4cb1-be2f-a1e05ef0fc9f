package com.abujlb.dataprobi.processes.tb;

import com.abujlb.CommonConfig;
import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.tb.DataprobiMsg;
import com.abujlb.dataprobi.bean.tb.SqliteUdzhtDp;
import com.abujlb.dataprobi.bean.tb.SqliteUdzhtSp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.util.MathUtil;
import com.abujlb.dataprobi.util.UDCSVReader;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2024/11/18
 */
@Component
@BiPro(order = 5, qd = Qd.TB)
public class DefaultBiUDDealProcess extends AbstractBiTXprocess {

    public static final String OSSKEY_PATTERN = "bi_jysj/%s/%s/bi_udzht.csv";

    @Override
    public void dealGoods() {
        super.dealGoods(
                SqliteUdzhtSp.class,
                OSSKEY_PATTERN,
                ((DataprobiMsg) getDataprobiBaseMsg()).getUdzht(),
                true
        );
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoods(
                SqliteUdzhtSp.class,
                OSSKEY_PATTERN,
                ((DataprobiMsg) getDataprobiBaseMsg()).getUdzht(),
                true
        );
    }

    @Override
    protected <T extends AbstractSqliteSp> List<T> spList(Class<T> tClass, String ossKey, String rq) {
        String key = String.format(OSSKEY_PATTERN, getDataprobiBaseMsg().getUserid(), rq);
        if (!biDataOssUtil.exist(key)) {
            return Collections.emptyList();
        }

        String temppath = CommonConfig.getString("tempdir") + UUID.randomUUID() + ".csv";
        biDataOssUtil.download(key, temppath);

        List<SqliteUdzhtSp> list = UDCSVReader.parseCSV(temppath, rq);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        return (List<T>) list;
    }

    @Override
    protected <T extends AbstractSqliteSp> void dealShopByGoods(String rq, List<T> list) {
        double udzhthf = 0;
        double udzhtcjje = 0;
        for (T t : list) {
            if (t instanceof SqliteUdzhtSp) {
                udzhthf = MathUtil.add(udzhthf, ((SqliteUdzhtSp) t).getUdzhthf());
                udzhtcjje = MathUtil.add(udzhtcjje, ((SqliteUdzhtSp) t).getUdzhtcjje());
            }
        }
        SqliteUdzhtDp sqliteUdzhtDp = new SqliteUdzhtDp();
        sqliteUdzhtDp.setQd(getDataprobiBaseMsg().getQd());
        sqliteUdzhtDp.setQd_userid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid());
        sqliteUdzhtDp.setRq(rq);
        sqliteUdzhtDp.setYhid(getDataprobiBaseMsg().getYhid());
        sqliteUdzhtDp.setUserid(getDataprobiBaseMsg().getUserid());
        sqliteUdzhtDp.setUdzhthf(udzhthf);
        sqliteUdzhtDp.setUdzhtcjje(udzhtcjje);

        processSycmDpOTS(rq, sqliteUdzhtDp);
    }
}
