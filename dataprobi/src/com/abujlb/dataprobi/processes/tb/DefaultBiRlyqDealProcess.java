package com.abujlb.dataprobi.processes.tb;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.SqliteSp;
import com.abujlb.dataprobi.bean.tb.DataprobiMsg;
import com.abujlb.dataprobi.bean.tb.SqliteRlyqDp;
import com.abujlb.dataprobi.bean.tb.SqliteRlyqSp;
import com.abujlb.dataprobi.bean.tb.SqliteRlyqVTaskSp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/5/11 15:49
 */
@Component
@BiPro(order = 8, qd = Qd.TB)
public class DefaultBiRlyqDealProcess extends AbstractBiprocess {

    private final String OSSKEY_PATTERN1 = "bi_jysj/%s/%s/rlyq.json";

    private final String OSSKEY_PATTERN2 = "bi_jysj/%s/%s/rlyq_vtasks.json";

    private final String[] COLUMNS = {BiSycmDpDataTsDao.TX_COLUMN_RLYQ2, BiSycmDpDataTsDao.TX_COLUMN_RLYQ_VTASK};

    @Override
    public void dealGoods() {
        dealCommission();
        dealVtask();
    }


    @Override
    public void dealShop() {
        super.dealShop(
                SqliteRlyqDp.class,
                ((DataprobiMsg) getDataprobiBaseMsg()).getRlyq(),
                COLUMNS
        );

    }

    @Override
    public boolean compareGoods() {
        boolean result = true;

        boolean flag = compareCommission();

        if (!flag) {
            result = false;
        }

        compareVtask();

        return result;
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqliteRlyqDp.class,
                ((DataprobiMsg) getDataprobiBaseMsg()).getRlyqdp(),
                COLUMNS
        );
    }


    private void dealCommission() {
        super.dealGoods(
                SqliteRlyqSp.class,
                OSSKEY_PATTERN1,
                ((DataprobiMsg) getDataprobiBaseMsg()).getRlyq()
        );
    }

    private void dealVtask() {
        for (String rq : ((DataprobiMsg) getDataprobiBaseMsg()).getRlyq()) {
            //坑位费部分
            String ossKey = String.format(OSSKEY_PATTERN2, getDataprobiBaseMsg().getUserid(), rq);
            List<SqliteRlyqVTaskSp> vTaskSps = spList(SqliteRlyqVTaskSp.class, ossKey, rq);
            processSycmSpOTS(rq, vTaskSps);

            //处理店铺
            if (CollectionUtils.isNotEmpty(vTaskSps)) {
                double vtaskhf = vTaskSps.stream().collect(Collectors.summarizingDouble(SqliteRlyqVTaskSp::getRlyqvtaskhf)).getSum();
                if (vtaskhf != 0) {
                    String json = "{\"vtaskhf\":" + vtaskhf + "}";
                    biSycmDpDataTsDao.updateRow(getDataprobiBaseMsg().getQd(), getDataprobiBaseMsg().getUserid(), rq, BiSycmDpDataTsDao.TX_COLUMN_RLYQ_VTASK, json);
                }
            }
        }
    }

    private boolean compareCommission() {
        return super.compareGoods(
                SqliteRlyqSp.class,
                OSSKEY_PATTERN1,
                ((DataprobiMsg) getDataprobiBaseMsg()).getRlyq()
        );
    }

    private void compareVtask() {
        boolean result = true;
        for (String rq : ((DataprobiMsg) getDataprobiBaseMsg()).getRlyq()) {
            //坑位费部分
            String ossKey = String.format(OSSKEY_PATTERN2, getDataprobiBaseMsg().getUserid(), rq);
            List<SqliteRlyqVTaskSp> list = spList(SqliteRlyqVTaskSp.class, ossKey, rq);
            if (CollectionUtils.isEmpty(list)) {
                continue;
            }

            if (!result) {
                processSycmSpOTS(rq, list);
                continue;
            }

            Map<String, SqliteSp> map = getSycmSpMapByBbid(rq);
            boolean flag = true;
            for (SqliteRlyqVTaskSp sqliteRlyqVTaskSp : list) {
                flag = sqliteRlyqVTaskSp.compare(map.get(sqliteRlyqVTaskSp.getBbid()));
                if (result && !flag) {
                    result = false;
                }
            }
            map.clear();
            if (!flag) {
                processSycmSpOTS(rq, list);

                double vtaskhf = list.stream().collect(Collectors.summarizingDouble(SqliteRlyqVTaskSp::getRlyqvtaskhf)).getSum();
                if (vtaskhf != 0) {
                    String json = "{\"vtaskhf\":" + vtaskhf + "}";
                    biSycmDpDataTsDao.updateRow(getDataprobiBaseMsg().getQd(), getDataprobiBaseMsg().getUserid(), rq, BiSycmDpDataTsDao.TX_COLUMN_RLYQ_VTASK, json);
                }
            }
        }
    }

}
