package com.abujlb.dataprobi.processes.tb;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.BiInfoOther;
import com.abujlb.dataprobi.bean.SqliteSp;
import com.abujlb.dataprobi.bean.tb.DataprobiMsg;
import com.abujlb.dataprobi.bean.tb.SqliteMsyqDp;
import com.abujlb.dataprobi.bean.tb.SqliteMsyqSp;
import com.abujlb.dataprobi.bean.tb.SqliteSpxgSp;
import com.abujlb.dataprobi.bean.tb.msyq.ItemMsyq;
import com.abujlb.dataprobi.constant.DataprobiConst;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.server.DataUploader;
import com.abujlb.dataprobi.util.MathUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @packageName com.abujlb.dataprobi.processes.tb
 * @ClassName DefaultBiMsqDealProcess
 * @Description 麦斯引擎数据处理
 * <AUTHOR>
 * @Date 2024/10/21
 */
@Component
@BiPro(order = 21, qd = Qd.TB)
public class DefaultBiMsqDealProcess extends AbstractBiprocess {

    private final String OSSKEY_PATTERN = "bi_jysj/%s/%s/spxg_0_" + DataprobiConst.RQLX_DAY + ".json";

    @Override
    public void dealGoods() {
        DataprobiMsg msgBean = (DataprobiMsg)getDataprobiBaseMsg();
        //获取所有的引擎账号
        List<BiInfoOther> biInfoOthers = DataUploader.getAllUserid(msgBean.getBiinfoId(),"OTHER_MSYQ");
        if (biInfoOthers == null) {
            throw new RuntimeException("麦斯引擎获取bi_info_others为null");
        }
        if (biInfoOthers.isEmpty()) {
            return;
        }
        for (String rq : msgBean.getMsyqJlgg()) {
            //查询商品销售额
            String ossKey = String.format(OSSKEY_PATTERN, getDataprobiBaseMsg().getUserid(), rq);
            Map<String, Double> bbidMap = spList(SqliteSpxgSp.class, ossKey, rq).stream().collect(Collectors.toMap(SqliteSp::getBbid, SqliteSpxgSp::getSpxgzfje));
            //店铺纬度花费
            double dphf = 0;
            //商品纬度花费  key：bbid  value：bbid对应的麦斯花费
            Map<String, ItemMsyq> map = new HashMap<>();
            for (BiInfoOther biInfoOther : biInfoOthers) {
                String key = "bi_jysj/" + msgBean.getUserid() + StrUtil.SLASH + rq + "/msyq/" + biInfoOther.getUserid() + StrUtil.SLASH + "jlgg.json";
                String s = biDataOssUtil.readJson(key);
                if (StringUtils.isBlank(s)) {
                    continue;
                }
                //当前麦斯引擎 当前rq 所有账号的数据
                List<JSONObject> list = JSONUtil.toList(s, JSONObject.class);
                Map<String, Double> filteredMap = null;
                Double reduce = null;
                for (int i = 0; i < list.size(); i++) {
                    JSONObject jsonObject = list.get(i);
                    List<String> itemIds = jsonObject.getBeanList("item_ids", String.class);
                    double currhf = jsonObject.getDouble("costs");
                    //店铺纬度麦斯引擎花费
                    dphf = MathUtil.add(dphf, currhf);
                    if (CollUtil.isEmpty(itemIds)) {
                        continue;
                    }
                    //根据销售额占比计算 商品消耗
                    filteredMap = bbidMap.entrySet()
                            .stream()
                            .filter(entry -> itemIds.contains(entry.getKey()))
                            .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
                    reduce = filteredMap.values().stream().mapToDouble(Double::doubleValue).sum();
                    ItemMsyq temp = null;
                    for (Map.Entry<String, Double> entry : filteredMap.entrySet()) {
                        temp = map.get(entry.getKey());
                        if (Objects.isNull(temp)) {
                            temp = new ItemMsyq();
                            temp.setBbid(entry.getKey());
                        }
                        temp.setMsyqhf(MathUtil.add(temp.getMsyqhf(), MathUtil.multipy(currhf, NumberUtil.div(entry.getValue(), reduce))));
                        map.put(entry.getKey(), temp);
                    }
                }
            }
            if (dphf != 0) {
                //处理到店铺db
                SqliteMsyqDp sqliteMsyqDp = new SqliteMsyqDp();
                sqliteMsyqDp.setMsyqhf(dphf);
                sqliteMsyqDp.setQd(msgBean.getQd());
                sqliteMsyqDp.setQd_userid(msgBean.getQd() + "_" + msgBean.getUserid());
                sqliteMsyqDp.setRq(rq);
                sqliteMsyqDp.setYhid(msgBean.getYhid());
                sqliteMsyqDp.setUserid(msgBean.getUserid());
                processSycmDpOTS(rq, sqliteMsyqDp);
            }

            if (map.size() > 0) {
                //处理到商品db
                List<SqliteMsyqSp> list = new ArrayList<>(map.size());
                for (Map.Entry<String, ItemMsyq> entry : map.entrySet()) {
                    SqliteMsyqSp sqliteMsyqSp = new SqliteMsyqSp();
                    sqliteMsyqSp.setMsyqhf(entry.getValue().getMsyqhf());
                    sqliteMsyqSp.setBbid(entry.getKey());
                    sqliteMsyqSp.setQd_userid_bbid(msgBean.getQd() + "_" + msgBean.getUserid() + "_" + entry.getKey());
                    sqliteMsyqSp.setRq(rq);
                    sqliteMsyqSp.setYhid(msgBean.getYhid());
                    sqliteMsyqSp.setQd(msgBean.getQd());
                    sqliteMsyqSp.setUserid(msgBean.getUserid());
                    list.add(sqliteMsyqSp);
                }
                processSycmSpOTS(rq, list);
            }
        }
    }
}
