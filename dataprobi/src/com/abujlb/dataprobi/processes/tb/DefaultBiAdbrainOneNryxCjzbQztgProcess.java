package com.abujlb.dataprobi.processes.tb;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteDp;
import com.abujlb.dataprobi.bean.tb.DataprobiMsg;
import com.abujlb.dataprobi.bean.tb.SqliteNryxCjzbQztgDp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.abujlb.util.StringUtil;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 内容营销-超级直播-全站推广
 *
 * <AUTHOR>
 * @date 2025/5/8
 */
@Component
@BiPro(order = 20, qd = Qd.TB)
public class DefaultBiAdbrainOneNryxCjzbQztgProcess extends AbstractBiTXprocess {

    public static final String OSSKEY_PATTERN = "bi_jysj/%s/%s/nryx_cjzb_qztg_dpdata.json";

    @Override
    public void dealShop() {
        super.dealShop(
                SqliteNryxCjzbQztgDp.class,
                ((DataprobiMsg) getDataprobiBaseMsg()).getNryx_cjzb_qztg(),
                StringUtils.EMPTY
        );
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqliteNryxCjzbQztgDp.class,
                ((DataprobiMsg) getDataprobiBaseMsg()).getNryx_cjzb_qztg(),
                StringUtils.EMPTY
        );
    }

    @Override
    protected <T extends AbstractSqliteDp> T dpObject(Class<T> tClass, String rq, String... colummns) {
        try {
            String key = String.format(OSSKEY_PATTERN, getDataprobiBaseMsg().getUserid(), rq);
            String json = biDataOssUtil.readJson(key);
            if (!StringUtil.isJson2(json)) {
                return null;
            }

            JSONObject jsonObject = JSONObject.parseObject(json);
            SqliteNryxCjzbQztgDp dpdata = new SqliteNryxCjzbQztgDp();
            dpdata.setQd(getDataprobiBaseMsg().getQd());
            dpdata.setQd_userid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid());
            dpdata.setRq(rq);
            dpdata.setYhid(getDataprobiBaseMsg().getYhid());
            dpdata.setUserid(getDataprobiBaseMsg().getUserid());
            dpdata.setNryx_cjzb_qztg_hf(FastJSONObjAttrToNumber.toDouble(jsonObject, "wholeSiteCharge"));
            dpdata.setNryx_cjzb_qztg_cjje(FastJSONObjAttrToNumber.toDouble(jsonObject, "wholeSiteAlipayAmt"));
            return (T) dpdata;
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
            return null;
        }
    }
}
