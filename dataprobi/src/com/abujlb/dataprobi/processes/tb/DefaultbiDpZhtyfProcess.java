package com.abujlb.dataprobi.processes.tb;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.tb.DataprobiMsg;
import com.abujlb.dataprobi.bean.tb.SqliteDpZhtyfDp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import org.springframework.stereotype.Component;

@Component
@BiPro(order = 2, qd = Qd.TB)
public class DefaultbiDpZhtyfProcess extends AbstractBiprocess {

    private final String[] COLUMNS = {BiSycmDpDataTsDao.TX_COLUMN_DPZHTYF};


    @Override
    public void dealShop() {
        super.dealShop(
                SqliteDpZhtyfDp.class,
                ((DataprobiMsg) getDataprobiBaseMsg()).getQndpzhtyf(),
                COLUMNS
        );
    }


    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqliteDpZhtyfDp.class,
                ((DataprobiMsg) getDataprobiBaseMsg()).getQndpzhtyf(),
                COLUMNS
        );
    }
}
