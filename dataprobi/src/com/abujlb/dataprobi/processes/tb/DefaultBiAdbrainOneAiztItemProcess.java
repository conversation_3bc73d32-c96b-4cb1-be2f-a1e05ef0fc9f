package com.abujlb.dataprobi.processes.tb;

import com.abujlb.CommonConfig;
import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteDp;
import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.tb.DataprobiMsg;
import com.abujlb.dataprobi.bean.tb.SqliteAiztItemDp;
import com.abujlb.dataprobi.bean.tb.SqliteAiztItemSp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.util.AiztOneCSVReader;
import com.abujlb.dataprobi.util.MathUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.UUID;

/**
 * 货品运营
 *
 * <AUTHOR>
 * @date 2024/12/16
 */
@Component
@BiPro(order = 6, qd = Qd.TB)
public class DefaultBiAdbrainOneAiztItemProcess extends AbstractBiTXprocess {

    public static final String OSSKEY_PATTERN = "bi_jysj/%s/%s/bi_aiztone_aizt_item.csv";

    @Override
    public void dealGoods() {
        super.dealGoods(
                SqliteAiztItemSp.class,
                OSSKEY_PATTERN,
                ((DataprobiMsg) getDataprobiBaseMsg()).getAizt_item(),
                true
        );
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoods(
                SqliteAiztItemSp.class,
                OSSKEY_PATTERN,
                ((DataprobiMsg) getDataprobiBaseMsg()).getAizt_item(),
                true
        );
    }

    @Override
    protected <T extends AbstractSqliteSp> List<T> spList(Class<T> tClass, String ossKey, String rq) {
        if (((DataprobiMsg) getDataprobiBaseMsg()).getAiztone() == 0) {
            return Collections.emptyList();
        }

        if (!biDataOssUtil.exist(ossKey)) {
            return Collections.emptyList();
        }

        String temppath = CommonConfig.getString("tempdir") + UUID.randomUUID() + ".csv";
        biDataOssUtil.download(ossKey, temppath);

        List<SqliteAiztItemSp> list = AiztOneCSVReader.parseCSV(SqliteAiztItemSp.class, temppath, rq);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        return (List<T>) list;
    }

    @Override
    protected <T extends AbstractSqliteSp> void dealShopByGoods(String rq, List<T> list) {
        SqliteAiztItemDp dpData = dpObject(SqliteAiztItemDp.class, rq, StringUtils.EMPTY);
        if (dpData != null) {
            processSycmDpOTS(rq, dpData);
        }
    }

    @Override
    protected <T extends AbstractSqliteDp> T dpObject(Class<T> tClass, String rq, String... colummns) {
        String ossKey = String.format(OSSKEY_PATTERN, getDataprobiBaseMsg().getUserid(), rq);
        List<SqliteAiztItemSp> list = spList(SqliteAiztItemSp.class, ossKey, rq);
        double aizt_item_hf = 0;
        double aizt_item_cjje = 0;
        for (SqliteAiztItemSp t : list) {
            aizt_item_hf = MathUtil.add(aizt_item_hf, t.getAizt_item_hf());
            aizt_item_cjje = MathUtil.add(aizt_item_cjje, t.getAizt_item_cjje());
        }
        SqliteAiztItemDp dpData = new SqliteAiztItemDp();
        dpData.setQd(getDataprobiBaseMsg().getQd());
        dpData.setQd_userid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid());
        dpData.setRq(rq);
        dpData.setYhid(getDataprobiBaseMsg().getYhid());
        dpData.setUserid(getDataprobiBaseMsg().getUserid());
        dpData.setAizt_item_hf(aizt_item_hf);
        dpData.setAizt_item_cjje(aizt_item_cjje);
        return (T) dpData;
    }
}
