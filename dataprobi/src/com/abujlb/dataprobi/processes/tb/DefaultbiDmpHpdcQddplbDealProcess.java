package com.abujlb.dataprobi.processes.tb;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.tb.DataprobiMsg;
import com.abujlb.dataprobi.bean.tb.SqliteDmpHpdcQddplbSp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import org.springframework.stereotype.Component;

@Component
@BiPro(order = 10, qd = Qd.TB)
public class DefaultbiDmpHpdcQddplbDealProcess extends AbstractBiprocess {

    public static final String OSSKEY_PATTERN = "bi_jysj/%s/%s/dpmhpdcqddplb.json";

    @Override
    public void dealGoods() {
        super.dealGoods(
                SqliteDmpHpdcQddplbSp.class,
                OSSKEY_PATTERN,
                ((DataprobiMsg) getDataprobiBaseMsg()).getDpmhpdcqddplb()
        );
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoods(
                SqliteDmpHpdcQddplbSp.class,
                OSSKEY_PATTERN,
                ((DataprobiMsg) getDataprobiBaseMsg()).getDpmhpdcqddplb()
        );
    }
}
