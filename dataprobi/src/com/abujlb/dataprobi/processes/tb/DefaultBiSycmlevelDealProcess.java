package com.abujlb.dataprobi.processes.tb;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.tb.DataprobiMsg;
import com.abujlb.dataprobi.bean.tb.SqliteSycmlevelDp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/5/11 15:49
 */
@Component
@BiPro(order = 6, qd = Qd.TB)
public class DefaultBiSycmlevelDealProcess extends AbstractBiprocess {

    public static final String[] COLUMNS = {BiSycmDpDataTsDao.TX_COLUMN_SYCMLEVEL};

    @Override
    public void dealShop() {
        super.dealShop(
                SqliteSycmlevelDp.class,
                ((DataprobiMsg) getDataprobiBaseMsg()).getSycmlevel(),
                COLUMNS
        );
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqliteSycmlevelDp.class,
                ((DataprobiMsg) getDataprobiBaseMsg()).getSycmlevel(),
                COLUMNS
        );
    }

}
