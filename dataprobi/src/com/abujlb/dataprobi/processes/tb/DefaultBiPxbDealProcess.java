package com.abujlb.dataprobi.processes.tb;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.SqliteSp;
import com.abujlb.dataprobi.bean.hfshare.HfShareBase;
import com.abujlb.dataprobi.bean.hfshare.HfShareGoodsConfig;
import com.abujlb.dataprobi.bean.tb.DataprobiMsg;
import com.abujlb.dataprobi.bean.tb.SqlitePxbDp;
import com.abujlb.dataprobi.bean.tb.SqlitePxbSp;
import com.abujlb.dataprobi.bean.tb.sp.PxbCreativeReportBean;
import com.abujlb.dataprobi.constant.DataprobiConst;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.tsdao.BiTghfConfigTsDao;
import com.abujlb.dataprobi.util.DataprobiDateUtil;
import com.abujlb.dataprobi.util.FileToJson;
import com.abujlb.dataprobi.util.FileUtil;
import com.abujlb.dataprobi.util.MathUtil;
import com.alibaba.fastjson.JSONArray;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/5/11 15:49
 */
@Component
@BiPro(order = 7, qd = Qd.TB)
public class DefaultBiPxbDealProcess extends AbstractBiprocess {

    private final String OSSKEY_PATTERN = "bi_jysj/%s/%s/pxb.json";

    @Autowired
    private BiTghfConfigTsDao biTghfConfigTsDao;

    @Override
    public void dealGoods() {
        List<String> rqList = new ArrayList<>();
        rqList.addAll(((DataprobiMsg) getDataprobiBaseMsg()).getSpxg());
        rqList.addAll(((DataprobiMsg) getDataprobiBaseMsg()).getPxb());
        if (CollectionUtils.isEmpty(rqList)) {
            return;
        }
        rqList = rqList.stream().distinct().collect(Collectors.toList());

        super.dealGoods(SqlitePxbSp.class, OSSKEY_PATTERN, rqList);
    }

    @Override
    public boolean compareGoods() {
        List<String> rqList = new ArrayList<>();
        rqList.addAll(((DataprobiMsg) getDataprobiBaseMsg()).getSpxg());
        rqList.addAll(((DataprobiMsg) getDataprobiBaseMsg()).getPxb());
        if (CollectionUtils.isEmpty(rqList)) {
            return true;
        }
        rqList = rqList.stream().distinct().collect(Collectors.toList());

        return super.compareGoods(SqlitePxbSp.class, OSSKEY_PATTERN, rqList);
    }

    @Override
    protected <T extends AbstractSqliteSp> List<T> spList(Class<T> tClass, String ossKey, String rq) {
        if (!biDataOssUtil.exist(ossKey)) {
            return Collections.emptyList();
        }

        String filePath = FileUtil.createJSONFilePath();
        biDataOssUtil.download(ossKey, filePath);

        File file = new File(filePath);
        String json = FileToJson.toJson(file);
        List<PxbCreativeReportBean> pxbBeanList = JSONArray.parseArray(json, PxbCreativeReportBean.class);
        if (CollectionUtils.isEmpty(pxbBeanList)) {
            return Collections.emptyList();
        }

        //处理店铺维度
        dealShopByGoods(pxbBeanList, rq);

        //商品分摊
        return (List<T>) goodsShare(pxbBeanList, rq);
    }

    private List<SqlitePxbSp> goodsShare(List<PxbCreativeReportBean> pxbBeanList, String rq) {
        updateRecords(pxbBeanList);

        List<HfShareBase> list = biTghfConfigTsDao.getRows(BiTghfConfigTsDao.LX_TX_PXB, getDataprobiBaseMsg().getYhid(), "TX", getDataprobiBaseMsg().getUserid());
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        Map<String, SqlitePxbSp> newMap = new HashMap<>();
        for (PxbCreativeReportBean pxbCreativeReportBean : pxbBeanList) {
            //查询是否有在有效期内的记录
            List<HfShareBase> tempList = list.stream().filter(temp ->
                    temp.getJhmc().equals(pxbCreativeReportBean.getJhmc())
                            && temp.getCymc().equals(pxbCreativeReportBean.getCymc())
                            && (((StringUtils.isBlank(temp.getKsrq()) && StringUtils.isBlank(temp.getJsrq()))
                            || ((StringUtils.isBlank(temp.getKsrq()) && StringUtils.isNotBlank(temp.getJsrq()) && rq.compareTo(temp.getJsrq()) <= 0))
                            || ((StringUtils.isBlank(temp.getJsrq()) && StringUtils.isNotBlank(temp.getKsrq()) && rq.compareTo(temp.getKsrq()) >= 0))
                            || (StringUtils.isNotBlank(temp.getKsrq()) && StringUtils.isNotBlank(temp.getJsrq()) && rq.compareTo(temp.getJsrq()) <= 0 && rq.compareTo(temp.getKsrq()) >= 0))
                    )
            ).collect(Collectors.toList());
            //同一rq内，有且仅有一个有效的记录，如果出现多个，说明配置错误
            if (CollectionUtils.isEmpty(tempList) || tempList.size() > 1) {
                continue;
            }

            HfShareBase hfShareBase = tempList.get(0);
            //0 销售额占比  1 固定比例
            int type = hfShareBase.getType();
            if (type == 0) {
                xsePercentShare(newMap, hfShareBase, pxbCreativeReportBean, rq);
            } else if (type == 1) {
                ratioShare(newMap, hfShareBase, pxbCreativeReportBean);
            }
        }

        ArrayList<SqlitePxbSp> sqlitePxbSps = new ArrayList<>(newMap.values());
        for (SqlitePxbSp sqlitePxbSp : sqlitePxbSps) {
            sqlitePxbSp.setRq(rq);
        }
        return sqlitePxbSps;
    }

    private void updateRecords(List<PxbCreativeReportBean> pxbBeanList) {
        List<HfShareBase> list = biTghfConfigTsDao.getRows(BiTghfConfigTsDao.LX_TX_PXB, getDataprobiBaseMsg().getYhid(), "TX", getDataprobiBaseMsg().getUserid());
        for (PxbCreativeReportBean pxbCreativeReportBean : pxbBeanList) {
            HfShareBase hfShareBase = new HfShareBase();
            hfShareBase.setLx(BiTghfConfigTsDao.LX_TX_PXB);
            hfShareBase.setYhid(getDataprobiBaseMsg().getYhid());
            hfShareBase.setQd("TX");
            hfShareBase.setUserid(getDataprobiBaseMsg().getUserid());
            hfShareBase.setJhmc(pxbCreativeReportBean.getJhmc());
            hfShareBase.setCymc(pxbCreativeReportBean.getCymc());
            hfShareBase.setUuid(UUID.randomUUID().toString());

            List<HfShareBase> tempList = list.stream().filter(hfShareBase1 -> hfShareBase1.getJhmc().equals(pxbCreativeReportBean.getJhmc()) && hfShareBase1.getCymc().equals(pxbCreativeReportBean.getCymc())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(tempList)) {
                //仅更新  gxsj sxsj  type
                hfShareBase.setSxsj(DataprobiDateUtil.getCurrentTime());
                hfShareBase.setType(BiTghfConfigTsDao.TYPE_1_DEFAULT);
                biTghfConfigTsDao.updateRow(hfShareBase);
            } else {
                //仅更新  gxsj
                biTghfConfigTsDao.updateRows(tempList);
            }
        }
    }

    private void xsePercentShare(Map<String, SqlitePxbSp> newMap, HfShareBase hfShareBase, PxbCreativeReportBean pxbCreativeReportBean, String rq) {
        Map<String, Double> itemCjje = new HashMap<>();

        List<HfShareGoodsConfig> configList = hfShareBase.getConfigList();
        if (CollectionUtils.isEmpty(configList)) {
            return;
        }

        List<SqliteSp> sqliteSps = sqliteDbUtil.readSpList(String.format(DataprobiConst.SYCM_SP_DAY, rq.replace("-", ""), getDataprobiBaseMsg().getYhid(), getDataprobiBaseMsg().getQd(), getDataprobiBaseMsg().getUserid()));
        for (SqliteSp sqliteSp : sqliteSps) {
            String goodsId = sqliteSp.getBbid();
            if (configList.stream().anyMatch(hfShareGoodsConfig -> hfShareGoodsConfig.getGoodsId().equals(goodsId))) {
                itemCjje.put(goodsId, sqliteSp.getSpxgzfje());
            }
        }

        double totalCjje = 0;
        for (Map.Entry<String, Double> entry : itemCjje.entrySet()) {
            totalCjje = MathUtil.add(totalCjje, entry.getValue());
        }

        Map<String, Double> itemCjjeZb = new HashMap<>();
        for (Map.Entry<String, Double> entry : itemCjje.entrySet()) {
            itemCjjeZb.put(entry.getKey(), MathUtil.divide(entry.getValue(), totalCjje, 8));
        }

        for (HfShareGoodsConfig hfShareGoodsConfig : configList) {
            double ratio = itemCjjeZb.getOrDefault(hfShareGoodsConfig.getGoodsId(), 0D);

            SqlitePxbSp sqlitePxbSp = newMap.getOrDefault(hfShareGoodsConfig.getGoodsId(), new SqlitePxbSp());
            sqlitePxbSp.setQd(getDataprobiBaseMsg().getQd());
            sqlitePxbSp.setQd_userid_bbid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid() + "_" + hfShareGoodsConfig.getGoodsId());
            sqlitePxbSp.setBbid(hfShareGoodsConfig.getGoodsId());
            sqlitePxbSp.setYhid(getDataprobiBaseMsg().getYhid());
            sqlitePxbSp.setRq(rq);
            sqlitePxbSp.setUserid(getDataprobiBaseMsg().getUserid());

            sqlitePxbSp.setPxbhf(MathUtil.add(sqlitePxbSp.getPxbhf(), MathUtil.multipy(pxbCreativeReportBean.getHf(), ratio)));
            sqlitePxbSp.setPxbzxl(sqlitePxbSp.getPxbzxl() + (int) MathUtil.multipy(pxbCreativeReportBean.getZxl(), ratio));
            sqlitePxbSp.setPxbdjl(sqlitePxbSp.getPxbdjl() + (int) MathUtil.multipy(pxbCreativeReportBean.getDjl(), ratio));
            sqlitePxbSp.setPxbcjje(MathUtil.add(sqlitePxbSp.getPxbcjje(), MathUtil.multipy(pxbCreativeReportBean.getHf(), ratio)));
            sqlitePxbSp.setPxbcjbs(sqlitePxbSp.getPxbcjbs() + (int) MathUtil.multipy(pxbCreativeReportBean.getCjbs(), ratio));

            newMap.put(hfShareGoodsConfig.getGoodsId(), sqlitePxbSp);
        }
    }

    private void ratioShare(Map<String, SqlitePxbSp> newMap, HfShareBase hfShareBase, PxbCreativeReportBean pxbCreativeReportBean) {
        List<HfShareGoodsConfig> configList = hfShareBase.getConfigList();
        if (CollectionUtils.isEmpty(configList)) {
            return;
        }

        for (HfShareGoodsConfig hfShareGoodsConfig : configList) {
            if (hfShareGoodsConfig.getRatio() == null || hfShareGoodsConfig.getRatio() == 0) {
                continue;
            }

            double ratio = hfShareGoodsConfig.getRatio();

            SqlitePxbSp sqlitePxbSp = newMap.getOrDefault(hfShareGoodsConfig.getGoodsId(), new SqlitePxbSp());
            sqlitePxbSp.setQd(getDataprobiBaseMsg().getQd());
            sqlitePxbSp.setQd_userid_bbid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid() + "_" + hfShareGoodsConfig.getGoodsId());
            sqlitePxbSp.setBbid(hfShareGoodsConfig.getGoodsId());

            sqlitePxbSp.setYhid(getDataprobiBaseMsg().getYhid());
            sqlitePxbSp.setUserid(getDataprobiBaseMsg().getUserid());

            sqlitePxbSp.setPxbhf(MathUtil.add(sqlitePxbSp.getPxbhf(), MathUtil.multipy(pxbCreativeReportBean.getHf(), ratio)));
            sqlitePxbSp.setPxbzxl(sqlitePxbSp.getPxbzxl() + (int) MathUtil.multipy(pxbCreativeReportBean.getZxl(), ratio));
            sqlitePxbSp.setPxbdjl(sqlitePxbSp.getPxbdjl() + (int) MathUtil.multipy(pxbCreativeReportBean.getDjl(), ratio));
            sqlitePxbSp.setPxbcjje(MathUtil.add(sqlitePxbSp.getPxbcjje(), MathUtil.multipy(pxbCreativeReportBean.getHf(), ratio)));
            sqlitePxbSp.setPxbcjbs(sqlitePxbSp.getPxbcjbs() + (int) MathUtil.multipy(pxbCreativeReportBean.getCjbs(), ratio));

            newMap.put(hfShareGoodsConfig.getGoodsId(), sqlitePxbSp);
        }
    }

    private void dealShopByGoods(List<PxbCreativeReportBean> list, String rq) {
        SqlitePxbDp sqlitePxbDp = new SqlitePxbDp();
        sqlitePxbDp.setQd(getDataprobiBaseMsg().getQd());
        sqlitePxbDp.setQd_userid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid());
        sqlitePxbDp.setRq(rq);
        sqlitePxbDp.setYhid(getDataprobiBaseMsg().getYhid());
        sqlitePxbDp.setUserid(getDataprobiBaseMsg().getUserid());


        double hf = 0;
        double cjje = 0;
        int pxbzxl = 0;
        int pxbdjl = 0;
        int pxbcjbs = 0;
        for (PxbCreativeReportBean data : list) {
            hf = MathUtil.add(hf, data.getHf());
            cjje = MathUtil.add(cjje, data.getCjje());
            pxbzxl += (int) data.getZxl();
            pxbdjl += (int) data.getDjl();
            pxbcjbs += data.getCjbs();
        }

        sqlitePxbDp.setPxbcjbs(pxbcjbs);
        sqlitePxbDp.setPxbcjje(cjje);
        sqlitePxbDp.setPxbdjl(pxbdjl);
        sqlitePxbDp.setPxbzxl(pxbzxl);
        sqlitePxbDp.setPxbhf(hf);
        processSycmDpOTS(rq, sqlitePxbDp);
    }
}
