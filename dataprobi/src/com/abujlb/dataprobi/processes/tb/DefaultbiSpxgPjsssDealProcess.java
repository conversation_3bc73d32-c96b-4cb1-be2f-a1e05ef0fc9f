package com.abujlb.dataprobi.processes.tb;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.BiInfo;
import com.abujlb.dataprobi.bean.SqliteSp;
import com.abujlb.dataprobi.bean.tb.DataprobiMsg;
import com.abujlb.dataprobi.bean.tb.SqliteSpxgPjsssSp;
import com.abujlb.dataprobi.bean.tb.spxgpjsss.SpPjsssTrend;
import com.abujlb.dataprobi.constant.DataprobiConst;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.threads.BiThreadLocals;
import com.abujlb.dataprobi.util.DataprobiDateUtil;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.abujlb.dataprobi.util.MathUtil;
import com.abujlb.util.StringUtil;
import com.alibaba.fastjson.JSONObject;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@BiPro(order = 9, qd = Qd.TB)
public class DefaultbiSpxgPjsssDealProcess extends AbstractBiprocess {

    private final String OSSKEY_PATTERN = "bi_jysj/%s/%s/spxg_0_" + DataprobiConst.RQLX_DAY + ".json";

    @Override
    public void dealGoods() {
        super.dealGoods(SqliteSpxgPjsssSp.class, OSSKEY_PATTERN, ((DataprobiMsg) getDataprobiBaseMsg()).getSpxg());
    }


    @Override
    public boolean compareGoods() {
        return super.compareGoods(SqliteSpxgPjsssSp.class, OSSKEY_PATTERN, ((DataprobiMsg) getDataprobiBaseMsg()).getSpxg());
    }


    @Override
    protected <T extends AbstractSqliteSp> List<T> spList(Class<T> tClass, String ossKey, String rq) {
        //是否配置了处理平均搜索数
        BiInfo biInfo = BiThreadLocals.getBiInfo();
        if (biInfo == null) {
            return null;
        }

        String dpconfigjson = biInfo.getDpconfigjson();
        if (!StringUtil.isJson2(dpconfigjson)) {
            return null;
        }

        JSONObject dpConfig = JSONObject.parseObject(dpconfigjson);
        int clpjsss = FastJSONObjAttrToNumber.toInt(dpConfig, "clpjsss");
        if (clpjsss != 1) {
            return null;
        }

        if (!biDataOssUtil.exist(ossKey)) {
            return null;
        }

        Map<String, SpPjsssTrend> spPjsssTrendsMap = new HashMap<>();

        String spDayDbPath = String.format(DataprobiConst.SYCM_SP_DAY, rq.replace("-", ""), getDataprobiBaseMsg().getYhid(), getDataprobiBaseMsg().getQd(), getDataprobiBaseMsg().getUserid());
        List<SqliteSp> sqliteSps = sqliteDbUtil.readSpList(spDayDbPath);
        for (SqliteSp sqliteSp : sqliteSps) {
            SpPjsssTrend trend = new SpPjsssTrend();
            trend.setBbid(sqliteSp.getBbid());
            trend.setPjsss(new int[60]);
            trend.getPjsss()[59] = FastJSONObjAttrToNumber.toInt(JSONObject.parseObject(sqliteSp.getOtherOrDefault()), "spxgssydfks");
            spPjsssTrendsMap.put(sqliteSp.getBbid(), trend);
        }

        if (spPjsssTrendsMap.isEmpty()) {
            return null;
        }

        List<String> rqList = DataprobiDateUtil.getDaysBefore(DataprobiDateUtil.getDaysBeforeAsString(rq, 1), 58);
        for (int i = 0; i < rqList.size(); i++) {
            String rqTemp = rqList.get(i);
            spDayDbPath = String.format(DataprobiConst.SYCM_SP_DAY, rqTemp.replace("-", ""), getDataprobiBaseMsg().getYhid(), getDataprobiBaseMsg().getQd(), getDataprobiBaseMsg().getUserid());
            sqliteSps = sqliteDbUtil.readSpList(spDayDbPath);

            for (SqliteSp sqliteSp : sqliteSps) {
                if (!spPjsssTrendsMap.containsKey(sqliteSp.getBbid())) {
                    continue;
                }
                SpPjsssTrend trend = spPjsssTrendsMap.get(sqliteSp.getBbid());
                trend.getPjsss()[i] = FastJSONObjAttrToNumber.toInt(JSONObject.parseObject(sqliteSp.getOtherOrDefault()), "spxgssydfks");
                spPjsssTrendsMap.put(sqliteSp.getBbid(), trend);
            }
        }

        ArrayList<SpPjsssTrend> spPjsssTrends = new ArrayList<>(spPjsssTrendsMap.values());

        for (SpPjsssTrend spPjsssTrend : spPjsssTrends) {
            String[] maxPjsssRqList = new String[3];
            int[] maxPjsssList = new int[3];

            for (int i = 0; i < spPjsssTrend.getPjsss().length; i++) {
                if (i < 3) {
                    maxPjsssRqList[i] = rqList.get(i);
                    maxPjsssList[i] = spPjsssTrend.getPjsss()[i];
                } else if (i == spPjsssTrend.getPjsss().length - 1) {
                    break;
                } else {
                    //数据比对
                    int curr = sum(maxPjsssList);

                    int[] currPjsssList = {spPjsssTrend.getPjsss()[i - 2], spPjsssTrend.getPjsss()[i - 1], spPjsssTrend.getPjsss()[i]};
                    int curr2 = sum(currPjsssList);

                    if (curr >= curr2) {
                        continue;
                    }

                    maxPjsssList = currPjsssList;

                    maxPjsssRqList = new String[]{rqList.get(i - 2), rqList.get(i - 1), rqList.get(i)};
                }
            }
            spPjsssTrend.setRqPjsss(maxPjsssList);
            spPjsssTrend.setRqList(maxPjsssRqList);
            spPjsssTrend.setPjsssPj(MathUtil.divide(sum(maxPjsssList), maxPjsssList.length));
        }

        List<SqliteSpxgPjsssSp> list = new ArrayList<>();
        for (SpPjsssTrend spPjsssTrend : spPjsssTrends) {
            if (sum(spPjsssTrend.getRqPjsss()) <= 0) {
                continue;
            }
            SqliteSpxgPjsssSp sqliteSpxgPjsssSp = new SqliteSpxgPjsssSp();
            sqliteSpxgPjsssSp.setQd(getDataprobiBaseMsg().getQd());
            sqliteSpxgPjsssSp.setQd_userid_bbid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid() + "_" + spPjsssTrend.getBbid());
            sqliteSpxgPjsssSp.setBbid(spPjsssTrend.getBbid());
            sqliteSpxgPjsssSp.setRq(rq);
            sqliteSpxgPjsssSp.setYhid(getDataprobiBaseMsg().getYhid());
            sqliteSpxgPjsssSp.setUserid(getDataprobiBaseMsg().getUserid());
            sqliteSpxgPjsssSp.setSpxgpjsss(spPjsssTrend.getPjsssPj());
            sqliteSpxgPjsssSp.setSpxgpjsssRqArr(spPjsssTrend.getRqList());
            sqliteSpxgPjsssSp.setSpxgpjsssArr(spPjsssTrend.getPjsss());
            list.add(sqliteSpxgPjsssSp);
        }

        return (List<T>) list;
    }


    private int sum(int[] pjsssList) {
        int sum = 0;
        for (int i : pjsssList) {
            sum = sum + i;
        }
        return sum;
    }
}
