package com.abujlb.dataprobi.processes.tb;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.tb.DataprobiMsg;
import com.abujlb.dataprobi.bean.tb.SqlitePpxxYxtgXpSp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/9/4
 */
@Component
@BiPro(order = 14, qd = Qd.TB)
public class DefaultBiPpxxXpyxtgProcess extends AbstractBiprocess {

    public static final String OSSKEY_PATTERN = "bi_jysj/%s/%s/ppxx_yxtg_xp.json";

    @Override
    public void dealGoods() {
        super.dealGoods(
                SqlitePpxxYxtgXpSp.class,
                OSSKEY_PATTERN,
                ((DataprobiMsg) getDataprobiBaseMsg()).getPpxx_yxtg_xp()
        );
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoods(
                SqlitePpxxYxtgXpSp.class,
                OSSKEY_PATTERN,
                ((DataprobiMsg) getDataprobiBaseMsg()).getPpxx_yxtg_xp()
        );
    }
}
