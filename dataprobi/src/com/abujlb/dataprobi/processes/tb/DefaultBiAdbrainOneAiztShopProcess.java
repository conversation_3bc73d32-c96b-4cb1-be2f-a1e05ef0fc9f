package com.abujlb.dataprobi.processes.tb;

import com.abujlb.CommonConfig;
import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.tb.DataprobiMsg;
import com.abujlb.dataprobi.bean.tb.SqliteAiztShopDp;
import com.abujlb.dataprobi.bean.tb.SqliteAiztShopSp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.util.AiztOneCSVReader;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.UUID;

/**
 * 货品运营
 *
 * <AUTHOR>
 * @date 2024/12/16
 */
@Component
@BiPro(order = 6, qd = Qd.TB)
public class DefaultBiAdbrainOneAiztShopProcess extends AbstractBiTXprocess {

    public static final String OSSKEY_PATTERN = "bi_jysj/%s/%s/bi_aiztone_aizt_shop.csv";
    public static final String[] COLUMNS = {"tx_aiztone_dpyy","tx_aiztone"};

    @Override
    public void dealGoods() {
        super.dealGoods(
                SqliteAiztShopSp.class,
                OSSKEY_PATTERN,
                ((DataprobiMsg) getDataprobiBaseMsg()).getAizt_shop(),
                true
        );
    }

    @Override
    public void dealShop() {
        super.dealShop(
                SqliteAiztShopDp.class,
                ((DataprobiMsg) getDataprobiBaseMsg()).getAiztone_dpsummary(),
                COLUMNS
        );
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoods(
                SqliteAiztShopSp.class,
                OSSKEY_PATTERN,
                ((DataprobiMsg) getDataprobiBaseMsg()).getAizt_shop(),
                true
        );
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqliteAiztShopDp.class,
                ((DataprobiMsg) getDataprobiBaseMsg()).getAiztone_dpsummary(),
                COLUMNS
        );
    }

    @Override
    protected <T extends AbstractSqliteSp> List<T> spList(Class<T> tClass, String ossKey, String rq) {
        if (((DataprobiMsg) getDataprobiBaseMsg()).getAiztone() == 0) {
            return Collections.emptyList();
        }

        if (!biDataOssUtil.exist(ossKey)) {
            return Collections.emptyList();
        }

        String temppath = CommonConfig.getString("tempdir") + UUID.randomUUID() + ".csv";
        biDataOssUtil.download(ossKey, temppath);

        List<SqliteAiztShopSp> list = AiztOneCSVReader.parseCSV(SqliteAiztShopSp.class, temppath, rq);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        return (List<T>) list;
    }

}
