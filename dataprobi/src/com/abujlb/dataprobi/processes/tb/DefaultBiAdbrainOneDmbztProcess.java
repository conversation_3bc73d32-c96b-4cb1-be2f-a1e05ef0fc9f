package com.abujlb.dataprobi.processes.tb;

import com.abujlb.CommonConfig;
import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteDp;
import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.tb.DataprobiMsg;
import com.abujlb.dataprobi.bean.tb.SqliteDmbztDp;
import com.abujlb.dataprobi.bean.tb.SqliteDmbztSp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.util.AiztOneCSVReader;
import com.abujlb.dataprobi.util.MathUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2023/11/10
 */
@Component
@BiPro(order = 14, qd = Qd.TB)
public class DefaultBiAdbrainOneDmbztProcess extends AbstractBiTXprocess {

    public static final String OSSKEY_PATTERN = "bi_jysj/%s/%s/bi_aiztone_dmbzt.csv";

    @Override
    public void dealGoods() {
        super.dealGoods(
                SqliteDmbztSp.class,
                OSSKEY_PATTERN,
                ((DataprobiMsg) getDataprobiBaseMsg()).getDmbzt(),
                true
        );
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoods(
                SqliteDmbztSp.class,
                OSSKEY_PATTERN,
                ((DataprobiMsg) getDataprobiBaseMsg()).getDmbzt(),
                true
        );
    }

    @Override
    protected <T extends AbstractSqliteSp> List<T> spList(Class<T> tClass, String ossKey, String rq) {
        if (((DataprobiMsg) getDataprobiBaseMsg()).getAiztone() == 0) {
            return Collections.emptyList();
        }

        //万相台无界
        if (!biDataOssUtil.exist(ossKey)) {
            return Collections.emptyList();
        }

        String temppath = CommonConfig.getString("tempdir") + UUID.randomUUID() + ".csv";
        biDataOssUtil.download(ossKey, temppath);

        List<SqliteDmbztSp> list = AiztOneCSVReader.parseCSV(SqliteDmbztSp.class, temppath, rq);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        return (List<T>) list;
    }

    @Override
    protected <T extends AbstractSqliteSp> void dealShopByGoods(String rq, List<T> list) {
        SqliteDmbztDp sqliteDmbztDp = dpObject(SqliteDmbztDp.class, rq, StringUtils.EMPTY);
        if (sqliteDmbztDp != null) {
            processSycmDpOTS(rq, sqliteDmbztDp);
        }
    }

    @Override
    protected <T extends AbstractSqliteDp> T dpObject(Class<T> tClass, String rq, String... colummns) {
        String ossKey = String.format(OSSKEY_PATTERN, getDataprobiBaseMsg().getUserid(), rq);
        List<SqliteDmbztSp> sqliteDmbztSps = this.spList(SqliteDmbztSp.class, ossKey, rq);
        //汇总店铺
        double dmbzthf = 0;
        //多目标直投成交金额
        double dmbztcjje = 0;
        for (SqliteDmbztSp t : sqliteDmbztSps) {
            dmbzthf = MathUtil.add(dmbzthf, t.getDmbzthf());
            dmbztcjje = MathUtil.add(dmbztcjje, t.getDmbztcjje());
        }

        SqliteDmbztDp sqliteDmbztDp = new SqliteDmbztDp();
        sqliteDmbztDp.setQd(getDataprobiBaseMsg().getQd());
        sqliteDmbztDp.setQd_userid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid());
        sqliteDmbztDp.setRq(rq);
        sqliteDmbztDp.setYhid(getDataprobiBaseMsg().getYhid());
        sqliteDmbztDp.setUserid(getDataprobiBaseMsg().getUserid());
        sqliteDmbztDp.setDmbztcjje(dmbztcjje);
        sqliteDmbztDp.setDmbzthf(dmbzthf);
        return (T) sqliteDmbztDp;
    }
}
