package com.abujlb.dataprobi.processes.tb;

import com.abujlb.CommonConfig;
import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteDp;
import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.tb.DataprobiMsg;
import com.abujlb.dataprobi.bean.tb.SqliteZtcDp;
import com.abujlb.dataprobi.bean.tb.SqliteZtcSp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.util.AiztOneCSVReader;
import com.abujlb.dataprobi.util.MathUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2022/5/11 15:49
 */
@Component
@BiPro(order = 3, qd = Qd.TB)
public class DefaultBiAdbrainOneZtcProcess extends AbstractBiTXprocess {

    public static final String OSSKEY_PATTERN = "bi_jysj/%s/%s/bi_aiztone_ztc.csv";

    @Override
    public void dealGoods() {
        super.dealGoods(
                SqliteZtcSp.class,
                OSSKEY_PATTERN,
                ((DataprobiMsg) getDataprobiBaseMsg()).getZtc(),
                true
        );
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoods(
                SqliteZtcSp.class,
                OSSKEY_PATTERN,
                ((DataprobiMsg) getDataprobiBaseMsg()).getZtc(),
                true
        );
    }

    @Override
    protected <T extends AbstractSqliteSp> List<T> spList(Class<T> tClass, String ossKey, String rq) {
        if (((DataprobiMsg) getDataprobiBaseMsg()).getAiztone() == 0) {
            return Collections.emptyList();
        }

        //万相台无界
        if (!biDataOssUtil.exist(ossKey)) {
            return Collections.emptyList();
        }

        String temppath = CommonConfig.getString("tempdir") + UUID.randomUUID() + ".csv";
        biDataOssUtil.download(ossKey, temppath);


        List<SqliteZtcSp> list = AiztOneCSVReader.parseCSV(SqliteZtcSp.class, temppath, rq);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return (List<T>) list;
    }

    @Override
    protected <T extends AbstractSqliteSp> void dealShopByGoods(String rq, List<T> list) {
        SqliteZtcDp dpData = dpObject(SqliteZtcDp.class, rq, StringUtils.EMPTY);
        if (dpData != null) {
            processSycmDpOTS(rq, dpData);
        }
    }

    @Override
    protected <T extends AbstractSqliteDp> T dpObject(Class<T> tClass, String rq, String... colummns) {
        String ossKey = String.format(OSSKEY_PATTERN, getDataprobiBaseMsg().getUserid(), rq);
        List<SqliteZtcSp> list = spList(SqliteZtcSp.class, ossKey, rq);
        double ztchf = 0;
        double ztccjje = 0;
        int ztczxl = 0;
        int ztcdjl = 0;
        int ztccjbs = 0;
        for (SqliteZtcSp t : list) {
            ztchf = MathUtil.add(ztchf, t.getZtchf());
            ztccjje = MathUtil.add(ztccjje, t.getZtccjje());
            ztczxl += t.getZtczxl();
            ztcdjl += t.getZtcdjl();
            ztccjbs += t.getZtccjbs();
        }
        SqliteZtcDp dpData = new SqliteZtcDp();
        dpData.setQd(getDataprobiBaseMsg().getQd());
        dpData.setQd_userid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid());
        dpData.setRq(rq);
        dpData.setYhid(getDataprobiBaseMsg().getYhid());
        dpData.setUserid(getDataprobiBaseMsg().getUserid());
        dpData.setZtchf(ztchf);
        dpData.setZtccjje(ztccjje);
        dpData.setZtczxl(ztczxl);
        dpData.setZtcdjl(ztcdjl);
        dpData.setZtccjbs(ztccjbs);
        return (T) dpData;
    }

}
