package com.abujlb.dataprobi.processes.tb;

import com.abujlb.CommonConfig;
import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.tb.DataprobiMsg;
import com.abujlb.dataprobi.bean.tb.SqliteYlmfCxjhDp;
import com.abujlb.dataprobi.bean.tb.SqliteYlmfCxjhSp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.util.AiztOneCSVReader;
import com.abujlb.dataprobi.util.MathUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2025/4/14
 */
@Component
@BiPro(order = 5, qd = Qd.TB)
public class DefaultBiAdbrainOneYlmfCxjhProcess extends AbstractBiTXprocess {

    public static final String OSSKEY_PATTERN = "bi_jysj/%s/%s/bi_aiztone_ylmf_cxjh.csv";

    @Override
    public void dealGoods() {
        super.dealGoods(
                SqliteYlmfCxjhSp.class,
                OSSKEY_PATTERN,
                ((DataprobiMsg) getDataprobiBaseMsg()).getYlmf_cxjh(),
                true
        );
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoods(
                SqliteYlmfCxjhSp.class,
                OSSKEY_PATTERN,
                ((DataprobiMsg) getDataprobiBaseMsg()).getYlmf_cxjh(),
                true
        );
    }

    @Override
    protected <T extends AbstractSqliteSp> List<T> spList(Class<T> tClass, String ossKey, String rq) {
        //万相台无界
        if (!biDataOssUtil.exist(ossKey)) {
            return Collections.emptyList();
        }

        String temppath = CommonConfig.getString("tempdir") + UUID.randomUUID() + ".csv";
        biDataOssUtil.download(ossKey, temppath);

        List<SqliteYlmfCxjhSp> list = AiztOneCSVReader.parseCSV(SqliteYlmfCxjhSp.class, temppath, rq);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        return (List<T>) list;
    }

    @Override
    protected <T extends AbstractSqliteSp> void dealShopByGoods(String rq, List<T> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        double ylmfhf = 0;
        double ylmfcjje = 0;
        for (T t : list) {
            if (t instanceof SqliteYlmfCxjhSp) {
                ylmfhf = MathUtil.add(ylmfhf, ((SqliteYlmfCxjhSp) t).getYlmfcxjhhf());
                ylmfcjje = MathUtil.add(ylmfcjje, ((SqliteYlmfCxjhSp) t).getYlmfcxjhcjje());
            }
        }
        SqliteYlmfCxjhDp sqliteylmfDp = new SqliteYlmfCxjhDp();
        sqliteylmfDp.setQd(getDataprobiBaseMsg().getQd());
        sqliteylmfDp.setQd_userid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid());
        sqliteylmfDp.setRq(rq);
        sqliteylmfDp.setYhid(getDataprobiBaseMsg().getYhid());
        sqliteylmfDp.setUserid(getDataprobiBaseMsg().getUserid());
        sqliteylmfDp.setYlmfcxjhhf(ylmfhf);
        sqliteylmfDp.setYlmfcxjhcjje(ylmfcjje);
        processSycmDpOTS(rq, sqliteylmfDp);
    }
}
