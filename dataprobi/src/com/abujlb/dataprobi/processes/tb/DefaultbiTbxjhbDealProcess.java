package com.abujlb.dataprobi.processes.tb;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.tb.DataprobiMsg;
import com.abujlb.dataprobi.bean.tb.SqliteTbxjhbDp;
import com.abujlb.dataprobi.bean.tb.SqliteTbxjhbSp;
import com.abujlb.dataprobi.bean.tb.sp.TbxjhbBean;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.tsdao.ZiddTsDao;
import com.abujlb.dataprobi.util.MathUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/5/17 11:34
 */
@Component
@BiPro(order = 10, qd = Qd.TB)
public class DefaultbiTbxjhbDealProcess extends AbstractBiprocess {

    private final String OSSKEY_PATTERN = "bi_jysj/%s/%s/tbxjhb.json";

    @Autowired
    private ZiddTsDao ziddTsDao;

    @Override
    public void dealGoods() {
        super.dealGoods(
                SqliteTbxjhbSp.class,
                OSSKEY_PATTERN,
                ((DataprobiMsg) getDataprobiBaseMsg()).getTbxjhb()
        );
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoods(
                SqliteTbxjhbSp.class,
                OSSKEY_PATTERN,
                ((DataprobiMsg) getDataprobiBaseMsg()).getTbxjhb()
        );
    }

    @Override
    protected <T extends AbstractSqliteSp> List<T> spList(Class<T> tClass, String ossKey, String rq) {
        DataprobiMsg msgBean = (DataprobiMsg) getDataprobiBaseMsg();

        String json = biDataOssUtil.readJson(ossKey);
        if (StringUtils.isBlank(json)) {
            return null;
        }
        JSONArray jsonArray = JSONArray.parseArray(json);
        if (CollectionUtils.isEmpty(jsonArray)) {
            return null;
        }
        List<TbxjhbBean> list = jsonArray.stream()
                .map(obj -> new TbxjhbBean((JSONObject) obj))
                .filter(tbxjhbBean -> StringUtils.isNotBlank(tbxjhbBean.getLqrq()))
                .filter(tbxjhbBean -> tbxjhbBean.getLqrq().equals(rq))
                .collect(Collectors.toList());
        double dphf = list.stream().mapToDouble(TbxjhbBean::getJe).sum();
        dealShop(dphf, msgBean, rq);

        list = list.stream()
                .filter(tbxjhbBean -> StringUtils.isNotBlank(tbxjhbBean.getDdh()))
                .collect(Collectors.toList());

        List<SqliteTbxjhbSp> spList = new ArrayList<>();
        for (TbxjhbBean tbxjhbBean : list) {
            String ddh = tbxjhbBean.getDdh();

            Map<String, Double> itemZfjeZb = ziddTsDao.getItemZfjeZb(ddh);
            if (itemZfjeZb == null || itemZfjeZb.isEmpty()) {
                continue;
            }
            boolean size1 = itemZfjeZb.size() == 1;
            double sum = size1 ? itemZfjeZb.values().stream().findFirst().get() : itemZfjeZb.values().stream().mapToDouble(Double::doubleValue).sum();
            if (sum == 0) {
                continue;
            }

            for (Map.Entry<String, Double> entry : itemZfjeZb.entrySet()) {

                SqliteTbxjhbSp sqliteTbxjhbSp = new SqliteTbxjhbSp();
                sqliteTbxjhbSp.setQd_userid_bbid(msgBean.getQd() + "_" + msgBean.getUserid() + "_" + entry.getKey());
                sqliteTbxjhbSp.setRq(rq);
                sqliteTbxjhbSp.setYhid(msgBean.getYhid());
                sqliteTbxjhbSp.setQd(msgBean.getQd());
                sqliteTbxjhbSp.setUserid(msgBean.getUserid());
                sqliteTbxjhbSp.setBbid(entry.getKey());
                sqliteTbxjhbSp.setTbxjhbhf(
                        size1 ? tbxjhbBean.getJe() : MathUtil.multipy(tbxjhbBean.getJe(), MathUtil.divide(entry.getValue(), sum), 2)
                );

                spList.add(sqliteTbxjhbSp);
            }
        }

        Map<String, List<SqliteTbxjhbSp>> map = spList.stream().collect(Collectors.groupingBy(SqliteTbxjhbSp::getBbid));
        List<T> list2 = new ArrayList<>(spList.size());

        for (Map.Entry<String, List<SqliteTbxjhbSp>> entry : map.entrySet()) {
            SqliteTbxjhbSp temp = null;
            List<SqliteTbxjhbSp> value = entry.getValue();
            for (SqliteTbxjhbSp sqliteTbxjhbSp : value) {
                if (temp == null) {
                    temp = new SqliteTbxjhbSp();
                    BeanUtils.copyProperties(sqliteTbxjhbSp, temp);
                    continue;
                }
                temp.plus(sqliteTbxjhbSp);
            }

            try {
                if (temp == null) {
                    continue;
                }
                T t = tClass.newInstance();
                BeanUtils.copyProperties(temp, t);
                list2.add(t);
            } catch (Exception e) {
                logger.error(e.getMessage(), e);
            }
        }
        return list2;
    }

    private void dealShop(double dphf, DataprobiMsg msgBean, String rq) {
        try {
            if (dphf == 0) {
                return;
            }

            SqliteTbxjhbDp sqliteTbxjhbDp = new SqliteTbxjhbDp();
            sqliteTbxjhbDp.setQd(msgBean.getQd());
            sqliteTbxjhbDp.setQd_userid(msgBean.getQd() + "_" + msgBean.getUserid());
            sqliteTbxjhbDp.setRq(rq);
            sqliteTbxjhbDp.setYhid(msgBean.getYhid());
            sqliteTbxjhbDp.setUserid(msgBean.getUserid());
            sqliteTbxjhbDp.setTbxjhbhf(dphf);
            processSycmDpOTS(rq, sqliteTbxjhbDp);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
    }
}
