package com.abujlb.dataprobi.processes.tb;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.BiInfo;
import com.abujlb.dataprobi.bean.SqliteSp;
import com.abujlb.dataprobi.bean.hfshare.HfShareBase;
import com.abujlb.dataprobi.bean.hfshare.HfShareGoodsConfig;
import com.abujlb.dataprobi.bean.tb.DataprobiMsg;
import com.abujlb.dataprobi.bean.tb.SqliteAiztNryxDspSp;
import com.abujlb.dataprobi.bean.tb.aiztone.AiztNryxDspSp;
import com.abujlb.dataprobi.constant.DataprobiConst;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.threads.BiThreadLocals;
import com.abujlb.dataprobi.tsdao.BiTghfConfigTsDao;
import com.abujlb.dataprobi.tsdao.BiVideoItemRelaTsDao;
import com.abujlb.dataprobi.util.AiztNryxXlsParse;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.abujlb.dataprobi.util.FileUtil;
import com.abujlb.dataprobi.util.MathUtil;
import com.abujlb.util.StringUtil;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

@Component
@BiPro(order = 9, qd = Qd.TB)
public class DefaultbiAdbrainOneNryxDspProcess extends AbstractBiprocess {

    public static final String OSSKEY_PATTERN = "bi_jysj/%s/%s/aizt_nryx_dsp.xls";

    @Autowired
    private BiTghfConfigTsDao biTghfConfigTsDao;

    @Autowired
    private BiVideoItemRelaTsDao biVideoItemRelaTsDao;

    @Override
    public void dealGoods() {
        List<String> rqList = new ArrayList<>();
        rqList.addAll(((DataprobiMsg) getDataprobiBaseMsg()).getSpxg());
        rqList.addAll(((DataprobiMsg) getDataprobiBaseMsg()).getNryxdsp());
        rqList.addAll(((DataprobiMsg) getDataprobiBaseMsg()).getVideoItemRela());
        if (CollectionUtils.isEmpty(rqList)) {
            return;
        }
        rqList = rqList.stream().distinct().collect(Collectors.toList());
        super.dealGoods(SqliteAiztNryxDspSp.class, OSSKEY_PATTERN, rqList);
    }


    @Override
    public boolean compareGoods() {
        List<String> rqList = new ArrayList<>();
        rqList.addAll(((DataprobiMsg) getDataprobiBaseMsg()).getSpxg());
        rqList.addAll(((DataprobiMsg) getDataprobiBaseMsg()).getNryxdsp());
        rqList.addAll(((DataprobiMsg) getDataprobiBaseMsg()).getVideoItemRela());
        if (CollectionUtils.isEmpty(rqList)) {
            return true;
        }
        rqList = rqList.stream().distinct().collect(Collectors.toList());
        return super.compareGoods(SqliteAiztNryxDspSp.class, OSSKEY_PATTERN, rqList);
    }


    @Override
    protected <T extends AbstractSqliteSp> List<T> spList(Class<T> tClass, String ossKey, String rq) {
        sqliteDbUtil.clearSpData(BiThreadLocals.getBiYhdp(), rq, SqliteAiztNryxDspSp.class);

        //解析xls  获取视频id和花费
        if (!biDataOssUtil.exist(ossKey)) {
            return Collections.emptyList();
        }

        String temppath = FileUtil.createXlsFilePath();
        biDataOssUtil.download(ossKey, temppath);

        File file = new File(temppath);
        List<AiztNryxDspSp> list = AiztNryxXlsParse.parse(file);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        //查看配置
        try {
            BiInfo biInfo = BiThreadLocals.getBiInfo();
            if (biInfo == null) {
                //商品分摊
                return (List<T>) goodsShare(list, rq);
            }

            String dpconfigjson = biInfo.getDpconfigjson();
            if (!StringUtil.isJson2(dpconfigjson) || FastJSONObjAttrToNumber.toInt(JSONObject.parseObject(dpconfigjson), "videoItemRela") != 1) {
                //商品分摊
                return (List<T>) goodsShare(list, rq);
            } else {
                //光合平台分摊
                return (List<T>) goodsShareGuanghe(list, rq);
            }
        } catch (Exception e) {
            return null;
        }
    }

    private List<SqliteAiztNryxDspSp> goodsShareGuanghe(List<AiztNryxDspSp> dspSpList, String rq) {
        Map<String, List<String>> map = biVideoItemRelaTsDao.initVideoList(getDataprobiBaseMsg().getYhid(), getDataprobiBaseMsg().getQd(), getDataprobiBaseMsg().getUserid());
        if (map == null || map.isEmpty()) {
            return null;
        }

        try {
            Map<String, Double> itemCjje = new HashMap<>();
            List<SqliteSp> sqliteSps = sqliteDbUtil.readSpList(String.format(DataprobiConst.SYCM_SP_DAY, rq.replace("-", ""), getDataprobiBaseMsg().getYhid(), getDataprobiBaseMsg().getQd(), getDataprobiBaseMsg().getUserid()));
            for (SqliteSp sqliteSp : sqliteSps) {
                String goodsId = sqliteSp.getBbid();
                if (sqliteSp.getSpxgzfje() > 0) {
                    itemCjje.put(goodsId, sqliteSp.getSpxgzfje());
                }
            }

            Map<String, Double> itemDspHf = new HashMap<>();

            for (AiztNryxDspSp nryxDspSp : dspSpList) {
                String videoId = nryxDspSp.getVideoId();
                if (StringUtils.isBlank(videoId)) {
                    continue;
                }

                if (!map.containsKey(videoId)) {
                    continue;
                }

                List<String> itemIdList = map.getOrDefault(videoId, Collections.emptyList());
                if (itemIdList.size() == 1) {
                    String itemid = itemIdList.get(0);
                    if (itemid.equals("-") || itemid.equals("--")) {
                        continue;
                    }
                    double itemidHf = itemDspHf.getOrDefault(itemid, 0D);
                    itemidHf = MathUtil.add(itemidHf, nryxDspSp.getHf());
                    itemDspHf.put(itemid, itemidHf);

                    continue;
                }

                //根据成交金额分摊
                double totalCjje = 0;
                for (String itemid : itemIdList) {
                    if (itemid.equals("-") || itemid.equals("--")) {
                        continue;
                    }
                    totalCjje = MathUtil.add(totalCjje, itemCjje.getOrDefault(itemid, 0D));
                }

                for (String itemid : itemIdList) {
                    if (itemid.equals("-") || itemid.equals("--")) {
                        continue;
                    }
                    double itemidHf = itemDspHf.getOrDefault(itemid, 0D);
                    double ratio = MathUtil.divide(itemCjje.getOrDefault(itemid, 0D), totalCjje, 8);
                    itemidHf = MathUtil.add(itemidHf, MathUtil.multipy(nryxDspSp.getHf(), ratio));
                    itemDspHf.put(itemid, itemidHf);
                }
            }

            List<SqliteAiztNryxDspSp> list = new ArrayList<>();
            for (Map.Entry<String, Double> entry : itemDspHf.entrySet()) {
                if (entry.getValue() > 0) {
                    SqliteAiztNryxDspSp sqliteAiztNryxDspSp = new SqliteAiztNryxDspSp();
                    sqliteAiztNryxDspSp.setQd(getDataprobiBaseMsg().getQd());
                    sqliteAiztNryxDspSp.setQd_userid_bbid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid() + "_" + entry.getKey());
                    sqliteAiztNryxDspSp.setBbid(entry.getKey());
                    sqliteAiztNryxDspSp.setYhid(getDataprobiBaseMsg().getYhid());
                    sqliteAiztNryxDspSp.setUserid(getDataprobiBaseMsg().getUserid());
                    sqliteAiztNryxDspSp.setNryxdsphf(entry.getValue());
                    list.add(sqliteAiztNryxDspSp);
                }
            }

            itemCjje.clear();
            itemDspHf.clear();
            map.clear();
            return list;
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
        return null;
    }

    private List<SqliteAiztNryxDspSp> goodsShare(List<AiztNryxDspSp> dspSpList, String rq) {
        List<HfShareBase> list = biTghfConfigTsDao.getRows2(BiTghfConfigTsDao.LX_TX_AIZTONE_NRYXDSP, getDataprobiBaseMsg().getYhid(), getDataprobiBaseMsg().getQd(), getDataprobiBaseMsg().getUserid());
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        Map<String, SqliteAiztNryxDspSp> newMap = new HashMap<>();
        for (AiztNryxDspSp nryxDspSp : dspSpList) {
            //查询是否有在有效期内的记录
            List<HfShareBase> tempList = list.stream().filter(temp -> temp.getJhid().equals(nryxDspSp.getJhid()) && temp.getVideoId().equals(nryxDspSp.getVideoId()) && (((StringUtils.isBlank(temp.getKsrq()) && StringUtils.isBlank(temp.getJsrq())) || ((StringUtils.isBlank(temp.getKsrq()) && StringUtils.isNotBlank(temp.getJsrq()) && rq.compareTo(temp.getJsrq()) <= 0)) || ((StringUtils.isBlank(temp.getJsrq()) && StringUtils.isNotBlank(temp.getKsrq()) && rq.compareTo(temp.getKsrq()) >= 0)) || (StringUtils.isNotBlank(temp.getKsrq()) && StringUtils.isNotBlank(temp.getJsrq()) && rq.compareTo(temp.getJsrq()) <= 0 && rq.compareTo(temp.getKsrq()) >= 0)))).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(tempList) || tempList.size() > 1) {
                continue;
            }

            HfShareBase hfShareBase = tempList.get(0);

            List<HfShareGoodsConfig> configList = hfShareBase.getConfigList();
            if (CollectionUtils.isEmpty(configList)) {
                continue;
            }

            //1 固定比例  0 销售额占比
            int type = hfShareBase.getType();
            if (type == 0) {
                xsePercentShare(newMap, nryxDspSp, configList, rq);
            } else if (type == 1) {
                ratioShare(newMap, nryxDspSp, configList);
            }
        }

        ArrayList<SqliteAiztNryxDspSp> sqliteAiztNryxDspSps = new ArrayList<>(newMap.values());
        for (SqliteAiztNryxDspSp sqliteAiztNryxDspSp : sqliteAiztNryxDspSps) {
            sqliteAiztNryxDspSp.setRq(rq);
        }
        return sqliteAiztNryxDspSps;
    }

    private void ratioShare(Map<String, SqliteAiztNryxDspSp> newMap, AiztNryxDspSp nryxDspSp, List<HfShareGoodsConfig> configList) {
        for (HfShareGoodsConfig hfShareGoodsConfig : configList) {
            if (hfShareGoodsConfig.getRatio() == null || hfShareGoodsConfig.getRatio() == 0) {
                continue;
            }

            double ratio = hfShareGoodsConfig.getRatio();

            SqliteAiztNryxDspSp sqliteAiztNryxDspSp = newMap.getOrDefault(hfShareGoodsConfig.getGoodsId(), new SqliteAiztNryxDspSp());
            sqliteAiztNryxDspSp.setQd(getDataprobiBaseMsg().getQd());
            sqliteAiztNryxDspSp.setQd_userid_bbid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid() + "_" + hfShareGoodsConfig.getGoodsId());
            sqliteAiztNryxDspSp.setBbid(hfShareGoodsConfig.getGoodsId());
            sqliteAiztNryxDspSp.setYhid(getDataprobiBaseMsg().getYhid());
            sqliteAiztNryxDspSp.setUserid(getDataprobiBaseMsg().getUserid());

            sqliteAiztNryxDspSp.setNryxdsphf(MathUtil.add(sqliteAiztNryxDspSp.getNryxdsphf(), MathUtil.multipy(nryxDspSp.getHf(), ratio)));
            newMap.put(hfShareGoodsConfig.getGoodsId(), sqliteAiztNryxDspSp);
        }
    }

    private void xsePercentShare(Map<String, SqliteAiztNryxDspSp> newMap, AiztNryxDspSp nryxDspSp, List<HfShareGoodsConfig> configList, String rq) {
        Map<String, Double> itemCjje = new HashMap<>();

        List<SqliteSp> sqliteSps = sqliteDbUtil.readSpList(String.format(DataprobiConst.SYCM_SP_DAY, rq.replace("-", ""), getDataprobiBaseMsg().getYhid(), getDataprobiBaseMsg().getQd(), getDataprobiBaseMsg().getUserid()));
        for (SqliteSp sqliteSp : sqliteSps) {
            String goodsId = sqliteSp.getBbid();
            if (configList.stream().anyMatch(hfShareGoodsConfig -> hfShareGoodsConfig.getGoodsId().equals(goodsId))) {
                itemCjje.put(goodsId, sqliteSp.getSpxgzfje());
            }
        }

        double totalCjje = 0;
        for (Map.Entry<String, Double> entry : itemCjje.entrySet()) {
            totalCjje = MathUtil.add(totalCjje, entry.getValue());
        }

        Map<String, Double> itemCjjeZb = new HashMap<>();
        for (Map.Entry<String, Double> entry : itemCjje.entrySet()) {
            itemCjjeZb.put(entry.getKey(), MathUtil.divide(entry.getValue(), totalCjje, 8));
        }

        for (HfShareGoodsConfig hfShareGoodsConfig : configList) {
            double ratio = itemCjjeZb.getOrDefault(hfShareGoodsConfig.getGoodsId(), 0D);

            SqliteAiztNryxDspSp sqliteAiztNryxDspSp = newMap.getOrDefault(hfShareGoodsConfig.getGoodsId(), new SqliteAiztNryxDspSp());
            sqliteAiztNryxDspSp.setQd(getDataprobiBaseMsg().getQd());
            sqliteAiztNryxDspSp.setQd_userid_bbid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid() + "_" + hfShareGoodsConfig.getGoodsId());
            sqliteAiztNryxDspSp.setBbid(hfShareGoodsConfig.getGoodsId());
            sqliteAiztNryxDspSp.setYhid(getDataprobiBaseMsg().getYhid());
            sqliteAiztNryxDspSp.setRq(rq);
            sqliteAiztNryxDspSp.setUserid(getDataprobiBaseMsg().getUserid());

            sqliteAiztNryxDspSp.setNryxdsphf(MathUtil.add(sqliteAiztNryxDspSp.getNryxdsphf(), MathUtil.multipy(nryxDspSp.getHf(), ratio)));
            newMap.put(hfShareGoodsConfig.getGoodsId(), sqliteAiztNryxDspSp);
        }
    }
}
