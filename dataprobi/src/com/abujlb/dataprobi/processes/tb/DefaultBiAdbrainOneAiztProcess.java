package com.abujlb.dataprobi.processes.tb;

import com.abujlb.CommonConfig;
import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteDp;
import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.tb.DataprobiMsg;
import com.abujlb.dataprobi.bean.tb.SqliteAiztDp;
import com.abujlb.dataprobi.bean.tb.SqliteAiztSp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.util.AiztOneCSVReader;
import com.abujlb.dataprobi.util.MathUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2022/5/11 15:49
 */
@Component
@BiPro(order = 6, qd = Qd.TB)
public class DefaultBiAdbrainOneAiztProcess extends AbstractBiTXprocess {

    public static final String OSSKEY_PATTERN = "bi_jysj/%s/%s/bi_aiztone_aizt.csv";

    @Override
    public void dealGoods() {
        super.dealGoods(
                SqliteAiztSp.class,
                OSSKEY_PATTERN,
                ((DataprobiMsg) getDataprobiBaseMsg()).getAizt(),
                true
        );
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoods(
                SqliteAiztSp.class,
                OSSKEY_PATTERN,
                ((DataprobiMsg) getDataprobiBaseMsg()).getAizt(),
                true
        );
    }

    @Override
    protected <T extends AbstractSqliteSp> List<T> spList(Class<T> tClass, String ossKey, String rq) {
        if (((DataprobiMsg) getDataprobiBaseMsg()).getAiztone() == 0) {
            return Collections.emptyList();
        }

        //万相台无界
        if (!biDataOssUtil.exist(ossKey)) {
            return Collections.emptyList();
        }

        String temppath = CommonConfig.getString("tempdir") + UUID.randomUUID() + ".csv";
        biDataOssUtil.download(ossKey, temppath);

        List<SqliteAiztSp> list = AiztOneCSVReader.parseCSV(SqliteAiztSp.class, temppath, rq);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        return (List<T>) list;
    }


    @Override
    protected <T extends AbstractSqliteSp> void dealShopByGoods(String rq, List<T> list) {
        SqliteAiztDp dpData = dpObject(SqliteAiztDp.class, rq, StringUtils.EMPTY);
        if (dpData != null) {
            processSycmDpOTS(rq, dpData);
        }
    }

    @Override
    protected <T extends AbstractSqliteDp> T dpObject(Class<T> tClass, String rq, String... colummns) {
        String ossKey = String.format(OSSKEY_PATTERN, getDataprobiBaseMsg().getUserid(), rq);
        List<SqliteAiztSp> list = spList(SqliteAiztSp.class, ossKey, rq);
        double aizthf = 0;
        int aiztzxl = 0;
        int aiztdjl = 0;
        int aiztcjbs = 0;
        double aiztcjje = 0;
        for (SqliteAiztSp t : list) {
            aizthf = MathUtil.add(aizthf, t.getAizthf());
            aiztcjje = MathUtil.add(aiztcjje, t.getAiztcjje());
            aiztzxl += t.getAiztzxl();
            aiztdjl += t.getAiztdjl();
            aiztcjbs += t.getAiztcjbs();
        }
        SqliteAiztDp dpData = new SqliteAiztDp();
        dpData.setQd(getDataprobiBaseMsg().getQd());
        dpData.setQd_userid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid());
        dpData.setRq(rq);
        dpData.setYhid(getDataprobiBaseMsg().getYhid());
        dpData.setUserid(getDataprobiBaseMsg().getUserid());
        dpData.setAizthf(aizthf);
        dpData.setAiztcjje(aiztcjje);
        dpData.setAiztzxl(aiztzxl);
        dpData.setAiztdjl(aiztdjl);
        dpData.setAiztcjbs(aiztcjbs);
        return (T) dpData;
    }
}
