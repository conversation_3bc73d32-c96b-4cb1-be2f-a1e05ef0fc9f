package com.abujlb.dataprobi.processes.tb;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.BiInfoOther;
import com.abujlb.dataprobi.bean.Rule;
import com.abujlb.dataprobi.bean.jlgg.JlggAccountHf;
import com.abujlb.dataprobi.bean.jlgg.SqliteJlggTxDp;
import com.abujlb.dataprobi.bean.tb.DataprobiMsg;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.server.DataUploader;
import com.abujlb.dataprobi.util.MathUtil;
import com.abujlb.util.StringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2023/9/22
 */
@Component
@BiPro(order = 21, qd = Qd.TB)
public class DefaultBiJlggDealProcess extends AbstractBiprocess {

    @Override
    public void dealShop() {
        //获取bi_info_other
        List<BiInfoOther> biInfoOthers = DataUploader.getBiInfoOtherListByBiInfoIdAndOtherQd(getDataprobiBaseMsg().getBiinfoId(), "OTHER_JLYQ");
        if (CollectionUtils.isEmpty(biInfoOthers)) {
            return;
        }

        for (String rq : ((DataprobiMsg) getDataprobiBaseMsg()).getJlgg()) {
            Map<String, JlggAccountHf> map1 = new HashMap<>();
            Map<String, JlggAccountHf> map2 = new HashMap<>();
            Map<String, JlggAccountHf> map3 = new HashMap<>();
            Map<String, JlggAccountHf> map4 = new HashMap<>();
            Map<String, JlggAccountHf> map5 = new HashMap<>();
            Map<String, JlggAccountHf> map6 = new HashMap<>();
            Map<String, JlggAccountHf> map7 = new HashMap<>();

            for (BiInfoOther biInfoOther : biInfoOthers) {
                String ruleJson = biInfoOther.getRule();
                Rule rule = new Rule();
                if (StringUtils.isNotBlank(ruleJson)) {
                    rule = JSON.parseObject(ruleJson, Rule.class);
                }
                String key1 = "bi_dy/jlgg/" + biInfoOther.getUserid() + "/" + rq + "/JJGG.json";
                String key2 = "bi_dy/jlgg/" + biInfoOther.getUserid() + "/" + rq + "/PPGG.json";
                String key3 = "bi_dy/jlgg/" + biInfoOther.getUserid() + "/" + rq + "/JLQC_JJGG_QYTG_TZBJ.json";
                String key4 = "bi_dy/jlgg/" + biInfoOther.getUserid() + "/" + rq + "/JLQC_JJGG_QYTG_TSP.json";
                String key5 = "bi_dy/jlgg/" + biInfoOther.getUserid() + "/" + rq + "/JLQC_JJGG_BZTG_TZBJ.json";
                String key6 = "bi_dy/jlgg/" + biInfoOther.getUserid() + "/" + rq + "/JLQC_JJGG_BZTG_TSP.json";
                String key7 = "bi_dy/jlgg/" + biInfoOther.getUserid() + "/" + rq + "/JLQC_PPGG.json";

                String s1 = biDataOssUtil.readJson(key1);
                String s2 = biDataOssUtil.readJson(key2);
                String s3 = biDataOssUtil.readJson(key3);
                String s4 = biDataOssUtil.readJson(key4);
                String s5 = biDataOssUtil.readJson(key5);
                String s6 = biDataOssUtil.readJson(key6);
                String s7 = biDataOssUtil.readJson(key7);
                readToMap(s1, map1, rule.getJlgg());
                readToMap(s2, map2, rule.getJlgg());
                readToMap2(s3, map3, rule.getJlqc());
                readToMap2(s4, map4, rule.getJlqc());
                readToMap(s5, map5, rule.getJlqc());
                readToMap(s6, map6, rule.getJlqc());
                readToMap(s7, map7, rule.getJlqc());

                double hf1 = map1.values().stream().map(JlggAccountHf::getHf).map(BigDecimal::valueOf).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP).doubleValue();
                double hf2 = map2.values().stream().map(JlggAccountHf::getHf).map(BigDecimal::valueOf).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP).doubleValue();
                double hf3 = map3.values().stream().map(JlggAccountHf::getHf).map(BigDecimal::valueOf).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP).doubleValue();
                double hf4 = map4.values().stream().map(JlggAccountHf::getHf).map(BigDecimal::valueOf).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP).doubleValue();
                double hf5 = map5.values().stream().map(JlggAccountHf::getHf).map(BigDecimal::valueOf).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP).doubleValue();
                double hf6 = map6.values().stream().map(JlggAccountHf::getHf).map(BigDecimal::valueOf).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP).doubleValue();
                double hf7 = map7.values().stream().map(JlggAccountHf::getHf).map(BigDecimal::valueOf).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP).doubleValue();
                SqliteJlggTxDp sqliteJlggDp = new SqliteJlggTxDp();
                sqliteJlggDp.setQd_userid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid());
                sqliteJlggDp.setRq(rq);
                sqliteJlggDp.setDpmc(getDataprobiBaseMsg().getDpmc());
                sqliteJlggDp.setUserid(getDataprobiBaseMsg().getUserid());
                sqliteJlggDp.setYhid(getDataprobiBaseMsg().getYhid());
                sqliteJlggDp.setQd(getDataprobiBaseMsg().getQd());
                sqliteJlggDp.setTxjlggjjgghf(hf1);
                sqliteJlggDp.setTxjlggppgghf(hf2);
                sqliteJlggDp.setTxjlqcjjggqytgtzbjhf(hf3);
                sqliteJlggDp.setTxjlqcjjggqytgtsphf(hf4);
                sqliteJlggDp.setTxjlqcjjggbztgtzbjhf(hf5);
                sqliteJlggDp.setTxjlqcjjggbztgtsphf(hf6);
                sqliteJlggDp.setTxjlqcppgghf(hf7);
                processSycmDpOTS(rq, sqliteJlggDp);

            }
        }
    }

    private void readToMap(String json, Map<String, JlggAccountHf> map, String rule) {
        List<String> accountIdList = new ArrayList<>();
        if (StringUtils.isNotBlank(rule)) {
            accountIdList = Arrays.asList(rule.split(","));
        }
        if (StringUtil.isJsonArray(json)) {
            JSONArray jsonArray = JSONArray.parseArray(json);
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                String advertiserId = jsonObject.getString("advertiser_id");
                double cost = jsonObject.getDoubleValue("stat_cost");
                if (CollectionUtils.isEmpty(accountIdList)) {
                    if (cost != 0) {
                        JlggAccountHf jlggAccountHf = map.getOrDefault(advertiserId, new JlggAccountHf());
                        jlggAccountHf.setAccountId(advertiserId);
                        jlggAccountHf.setHf(MathUtil.add(jlggAccountHf.getHf(), cost));
                        map.put(advertiserId, jlggAccountHf);
                    }
                    continue;
                }
                if (accountIdList.contains(advertiserId)) {
                    if (cost != 0) {
                        JlggAccountHf jlggAccountHf = map.getOrDefault(advertiserId, new JlggAccountHf());
                        jlggAccountHf.setAccountId(advertiserId);
                        jlggAccountHf.setHf(MathUtil.add(jlggAccountHf.getHf(), cost));
                        map.put(advertiserId, jlggAccountHf);
                    }
                }
            }
        }
    }

    private void readToMap2(String json, Map<String, JlggAccountHf> map, String rule) {
        List<String> accountIdList = new ArrayList<>();
        if (StringUtils.isNotBlank(rule)) {
            accountIdList = Arrays.asList(rule.split(","));
        }
        if (StringUtil.isJsonArray(json)) {
            JSONArray jsonArray = JSONArray.parseArray(json);
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                String advertiserId = jsonObject.getString("advertiser_id");
                String statCostForRoi2 = jsonObject.getString("stat_cost_for_roi2").replace(",", "");
                double cost = Double.parseDouble(statCostForRoi2);
                if (CollectionUtils.isEmpty(accountIdList)) {
                    if (cost != 0) {
                        JlggAccountHf jlggAccountHf = map.getOrDefault(advertiserId, new JlggAccountHf());
                        jlggAccountHf.setAccountId(advertiserId);
                        jlggAccountHf.setHf(MathUtil.add(jlggAccountHf.getHf(), cost));
                        map.put(advertiserId, jlggAccountHf);
                    }
                    continue;
                }
                if (accountIdList.contains(advertiserId)) {
                    if (cost != 0) {
                        JlggAccountHf jlggAccountHf = map.getOrDefault(advertiserId, new JlggAccountHf());
                        jlggAccountHf.setAccountId(advertiserId);
                        jlggAccountHf.setHf(MathUtil.add(jlggAccountHf.getHf(), cost));
                        map.put(advertiserId, jlggAccountHf);
                    }
                }
            }
        }
    }
}
