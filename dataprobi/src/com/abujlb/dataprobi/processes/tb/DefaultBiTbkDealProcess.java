package com.abujlb.dataprobi.processes.tb;

import com.abujlb.CommonConfig;
import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.tb.DataprobiMsg;
import com.abujlb.dataprobi.bean.tb.SqliteTbkDp;
import com.abujlb.dataprobi.bean.tb.SqliteTbkSp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import com.abujlb.dataprobi.util.TbkCSVReader;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2023/12/29
 */
@Component
@BiPro(order = 10, qd = Qd.TB)
public class DefaultBiTbkDealProcess extends AbstractBiprocess {

    public static final String OSSKEY_PATTERN = "bi_jysj/%s/%s/tbk.csv";
    public static final String[] COLUMNS = {BiSycmDpDataTsDao.TX_COLUMN_TBK};

    @Override
    public void dealGoods() {
        super.dealGoods(
                SqliteTbkSp.class,
                OSSKEY_PATTERN,
                ((DataprobiMsg) getDataprobiBaseMsg()).getTbk()
        );
    }

    @Override
    public void dealShop() {
        super.dealShop(
                SqliteTbkDp.class,
                ((DataprobiMsg) getDataprobiBaseMsg()).getTbkdp(),
                COLUMNS
        );
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoods(
                SqliteTbkSp.class,
                OSSKEY_PATTERN,
                ((DataprobiMsg) getDataprobiBaseMsg()).getTbk()
        );
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqliteTbkDp.class,
                ((DataprobiMsg) getDataprobiBaseMsg()).getTbkdp(),
                COLUMNS
        );
    }

    @Override
    protected <T extends AbstractSqliteSp> List<T> spList(Class<T> tClass, String ossKey, String rq) {
        if (!biDataOssUtil.exist(ossKey)) {
            return null;
        }

        String temppath = CommonConfig.getString("tempdir") + UUID.randomUUID() + ".csv";
        biDataOssUtil.download(ossKey, temppath);

        List<SqliteTbkSp> list = TbkCSVReader.parseTbk(temppath, rq);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }

        return (List<T>) list;
    }
}
