package com.abujlb.dataprobi.processes.tb;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.tb.DataprobiMsg;
import com.abujlb.dataprobi.bean.tb.SqliteJhsDp;
import com.abujlb.dataprobi.bean.tb.SqliteJhsSp;
import com.abujlb.dataprobi.bean.tb.sp.JhsBean;
import com.abujlb.dataprobi.constant.DataprobiConst;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.google.gson.reflect.TypeToken;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/4/19 16:59
 */
@Component
@BiPro(order = 9, qd = Qd.TB)
public class DefaultBiJhsDealProcess extends AbstractBiprocess {

    private final String OSSKEY_PATTERN = "bi_jysj/%s/%s/jhs.json";
    private final Type type = new TypeToken<List<JhsBean>>() {
    }.getType();

    @Override
    public void dealGoods() {
        super.dealGoods(
                SqliteJhsSp.class,
                OSSKEY_PATTERN,
                ((DataprobiMsg) getDataprobiBaseMsg()).getJhs()
        );
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoods(
                SqliteJhsSp.class,
                OSSKEY_PATTERN,
                ((DataprobiMsg) getDataprobiBaseMsg()).getJhs()
        );
    }

    @Override
    protected <T extends AbstractSqliteSp> List<T> spList(Class<T> tClass, String ossKey, String rq) {
        if (!biDataOssUtil.exist((ossKey))) {
            return null;
        }
        String json = biDataOssUtil.readJson(ossKey);
        List<JhsBean> list = DataprobiConst.GSON.fromJson(json, type);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }

        final DataprobiMsg dataprobiMsg = (DataprobiMsg) getDataprobiBaseMsg();

        list.forEach(JhsBean::calDayHf);

        //店铺维度花费
        double sum = list.stream().mapToDouble(JhsBean::getDayHf).sum();
        if (sum <= 0) {
            return null;
        }

        dealShop(rq, sum);

        List<SqliteJhsSp> spList = new ArrayList<>();
        for (JhsBean jhsBean : list) {
            for (String goodsId : jhsBean.getGoodsIdList()) {
                SqliteJhsSp sqliteJhsSp = new SqliteJhsSp();

                sqliteJhsSp.setBbid(goodsId);
                sqliteJhsSp.setQd_userid_bbid(dataprobiMsg.getQd() + "_" + dataprobiMsg.getUserid() + "_" + goodsId);
                sqliteJhsSp.setRq(rq);
                sqliteJhsSp.setYhid(dataprobiMsg.getYhid());
                sqliteJhsSp.setQd(dataprobiMsg.getQd());
                sqliteJhsSp.setUserid(dataprobiMsg.getUserid());
                sqliteJhsSp.setJhskwhf(jhsBean.getSpDayHf());
                spList.add(sqliteJhsSp);
            }
        }

        Map<String, SqliteJhsSp> map = new HashMap<>();
        for (SqliteJhsSp t : spList) {
            SqliteJhsSp temp = null;
            if (map.containsKey(t.getBbid())) {
                temp = map.get(t.getBbid());
            } else {
                try {
                    temp = SqliteJhsSp.class.newInstance();
                } catch (Exception e) {
                    logger.error(e.getMessage(), e);
                    continue;
                }
                temp.setUserid(t.getUserid());
                temp.setYhid(t.getYhid());
                temp.setQd(t.getQd());
                temp.setQd_userid_bbid(t.getQd_userid_bbid());
                temp.setBbid(t.getBbid());
                temp.setRq(rq);
            }
            temp.plus(t);
            map.put(t.getBbid(), temp);
        }
        ArrayList<SqliteJhsSp> sqliteJhsSps = new ArrayList<>(map.values());

        List<T> list2 = new ArrayList<>(spList.size());
        for (SqliteJhsSp sqliteJhsSp : sqliteJhsSps) {
            try {
                T t = tClass.newInstance();
                BeanUtils.copyProperties(sqliteJhsSp, t);
                list2.add(t);
            } catch (Exception e) {
                logger.error(e.getMessage(), e);
            }
        }
        return list2;
    }

    public void dealShop(String rq, double hf) {
        if (hf > 0) {
            final DataprobiMsg dataprobiMsg = (DataprobiMsg) getDataprobiBaseMsg();
            SqliteJhsDp sqliteJhsDp = new SqliteJhsDp();

            sqliteJhsDp.setQd(dataprobiMsg.getQd());
            sqliteJhsDp.setQd_userid(dataprobiMsg.getQd() + "_" + dataprobiMsg.getUserid());
            sqliteJhsDp.setRq(rq);
            sqliteJhsDp.setYhid(dataprobiMsg.getYhid());
            sqliteJhsDp.setUserid(dataprobiMsg.getUserid());

            sqliteJhsDp.setJhskwhf(hf);
            processSycmDpOTS(rq, sqliteJhsDp);
        }
    }
}
