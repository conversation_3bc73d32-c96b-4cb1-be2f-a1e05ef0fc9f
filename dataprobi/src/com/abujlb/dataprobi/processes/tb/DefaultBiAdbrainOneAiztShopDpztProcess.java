package com.abujlb.dataprobi.processes.tb;

import com.abujlb.CommonConfig;
import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.SqliteSp;
import com.abujlb.dataprobi.bean.hfshare.HfShareBase;
import com.abujlb.dataprobi.bean.hfshare.HfShareGoodsConfig;
import com.abujlb.dataprobi.bean.tb.AiztoneAiztshopDpztData;
import com.abujlb.dataprobi.bean.tb.DataprobiMsg;
import com.abujlb.dataprobi.bean.tb.SqliteAiztShopDpzdDp;
import com.abujlb.dataprobi.bean.tb.SqliteAiztShopDpzdSp;
import com.abujlb.dataprobi.constant.DataprobiConst;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.tsdao.BiTghfConfigTsDao;
import com.abujlb.dataprobi.util.AiztOneCSVReader;
import com.abujlb.dataprobi.util.MathUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 无界-店铺运营 - 店铺直达
 * <p>
 * 【淘系 无界 店铺运营 - 店铺直达 花费分摊】
 * https://www.tapd.cn/tapd_fe/47708728/story/detail/1147708728001015537
 *
 * <AUTHOR>
 * @date 2025/6/13
 */
@Component
@BiPro(order = 6, qd = Qd.TB)
public class DefaultBiAdbrainOneAiztShopDpztProcess extends AbstractBiTXprocess {

    public static final String OSSKEY_PATTERN = "bi_jysj/%s/%s/bi_aiztone_dpyy_dpzd.csv";

    @Autowired
    private BiTghfConfigTsDao biTghfConfigTsDao;

    @Override
    public void dealGoods() {
        super.dealGoods(
                SqliteAiztShopDpzdSp.class,
                OSSKEY_PATTERN,
                ((DataprobiMsg) getDataprobiBaseMsg()).getAizt_shop_dpzd(),
                true
        );
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoods(
                SqliteAiztShopDpzdSp.class,
                OSSKEY_PATTERN,
                ((DataprobiMsg) getDataprobiBaseMsg()).getAizt_shop_dpzd(),
                true
        );
    }

    @Override
    protected <T extends AbstractSqliteSp> List<T> spList(Class<T> tClass, String ossKey, String rq) {
        if (((DataprobiMsg) getDataprobiBaseMsg()).getAiztone() == 0) {
            return Collections.emptyList();
        }

        if (!biDataOssUtil.exist(ossKey)) {
            return Collections.emptyList();
        }

        String temppath = CommonConfig.getString("tempdir") + UUID.randomUUID() + ".csv";
        biDataOssUtil.download(ossKey, temppath);

        //创意数据
        List<AiztoneAiztshopDpztData> list = AiztOneCSVReader.parseDpyyDpzdCSV(temppath, rq);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        dealShop(rq, list);

        //分摊
        return (List<T>) goodsShare(list, rq);
    }

    private void dealShop(String rq, List<AiztoneAiztshopDpztData> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        SqliteAiztShopDpzdDp sqliteAiztShopDpzdDp = new SqliteAiztShopDpzdDp();
        double aizt_shop_dpzd_hf = 0;
        double aizt_shop_dpzd_cjje = 0;
        for (AiztoneAiztshopDpztData t : list) {
            aizt_shop_dpzd_hf = MathUtil.add(aizt_shop_dpzd_hf, t.getHf());
            aizt_shop_dpzd_cjje = MathUtil.add(aizt_shop_dpzd_cjje, t.getCjje());
        }
        sqliteAiztShopDpzdDp.setQd(getDataprobiBaseMsg().getQd());
        sqliteAiztShopDpzdDp.setQd_userid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid());
        sqliteAiztShopDpzdDp.setRq(rq);
        sqliteAiztShopDpzdDp.setYhid(getDataprobiBaseMsg().getYhid());
        sqliteAiztShopDpzdDp.setUserid(getDataprobiBaseMsg().getUserid());
        sqliteAiztShopDpzdDp.setAizt_shop_dpzd_hf(aizt_shop_dpzd_hf);
        sqliteAiztShopDpzdDp.setAizt_shop_dpzd_cjje(aizt_shop_dpzd_cjje);
        processSycmDpOTS(rq, sqliteAiztShopDpzdDp);
    }


    private List<SqliteAiztShopDpzdSp> goodsShare(List<AiztoneAiztshopDpztData> cydataList, String rq) {
        List<HfShareBase> list = biTghfConfigTsDao.getRows2(BiTghfConfigTsDao.LX_TX_AIZTONE_DPYY_DPZD, getDataprobiBaseMsg().getYhid(), getDataprobiBaseMsg().getQd(), getDataprobiBaseMsg().getUserid());
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        Map<String, SqliteAiztShopDpzdSp> newMap = new HashMap<>();
        for (AiztoneAiztshopDpztData cydata : cydataList) {
            //查询是否有在有效期内的记录
            List<HfShareBase> tempList = list.stream().filter(temp -> temp.getJhid().equals(cydata.getJhid()) && temp.getCyId().equals(cydata.getCyid()) && (((StringUtils.isBlank(temp.getKsrq()) && StringUtils.isBlank(temp.getJsrq())) || ((StringUtils.isBlank(temp.getKsrq()) && StringUtils.isNotBlank(temp.getJsrq()) && rq.compareTo(temp.getJsrq()) <= 0)) || ((StringUtils.isBlank(temp.getJsrq()) && StringUtils.isNotBlank(temp.getKsrq()) && rq.compareTo(temp.getKsrq()) >= 0)) || (StringUtils.isNotBlank(temp.getKsrq()) && StringUtils.isNotBlank(temp.getJsrq()) && rq.compareTo(temp.getJsrq()) <= 0 && rq.compareTo(temp.getKsrq()) >= 0)))).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(tempList) || tempList.size() > 1) {
                continue;
            }

            HfShareBase hfShareBase = tempList.get(0);

            List<HfShareGoodsConfig> configList = hfShareBase.getConfigList();
            if (CollectionUtils.isEmpty(configList)) {
                continue;
            }

            //1 固定比例  0 销售额占比
            int type = hfShareBase.getType();
            if (type == 0) {
                xsePercentShare(newMap, cydata, configList, rq);
            } else if (type == 1) {
                ratioShare(newMap, cydata, configList);
            }
        }

        ArrayList<SqliteAiztShopDpzdSp> sqliteAiztShopDpzdSps = new ArrayList<>(newMap.values());
        for (SqliteAiztShopDpzdSp sqliteAiztShopDpzdSp : sqliteAiztShopDpzdSps) {
            sqliteAiztShopDpzdSp.setRq(rq);
        }
        return sqliteAiztShopDpzdSps;
    }

    private void ratioShare(Map<String, SqliteAiztShopDpzdSp> newMap, AiztoneAiztshopDpztData cydata, List<HfShareGoodsConfig> configList) {
        for (HfShareGoodsConfig hfShareGoodsConfig : configList) {
            if (hfShareGoodsConfig.getRatio() == null || hfShareGoodsConfig.getRatio() == 0) {
                continue;
            }

            double ratio = hfShareGoodsConfig.getRatio();

            SqliteAiztShopDpzdSp sqliteAiztShopDpzdSp = newMap.getOrDefault(hfShareGoodsConfig.getGoodsId(), new SqliteAiztShopDpzdSp());
            sqliteAiztShopDpzdSp.setQd(getDataprobiBaseMsg().getQd());
            sqliteAiztShopDpzdSp.setQd_userid_bbid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid() + "_" + hfShareGoodsConfig.getGoodsId());
            sqliteAiztShopDpzdSp.setBbid(hfShareGoodsConfig.getGoodsId());
            sqliteAiztShopDpzdSp.setYhid(getDataprobiBaseMsg().getYhid());
            sqliteAiztShopDpzdSp.setUserid(getDataprobiBaseMsg().getUserid());

            sqliteAiztShopDpzdSp.setAizt_shop_dpzd_hf(MathUtil.add(sqliteAiztShopDpzdSp.getAizt_shop_dpzd_hf(), MathUtil.multipy(cydata.getHf(), ratio)));
            newMap.put(hfShareGoodsConfig.getGoodsId(), sqliteAiztShopDpzdSp);
        }
    }

    private void xsePercentShare(Map<String, SqliteAiztShopDpzdSp> newMap, AiztoneAiztshopDpztData cydata, List<HfShareGoodsConfig> configList, String rq) {
        Map<String, Double> itemCjje = new HashMap<>();

        List<SqliteSp> sqliteSps = sqliteDbUtil.readSpList(String.format(DataprobiConst.SYCM_SP_DAY, rq.replace("-", ""), getDataprobiBaseMsg().getYhid(), getDataprobiBaseMsg().getQd(), getDataprobiBaseMsg().getUserid()));
        for (SqliteSp sqliteSp : sqliteSps) {
            String goodsId = sqliteSp.getBbid();
            if (configList.stream().anyMatch(hfShareGoodsConfig -> hfShareGoodsConfig.getGoodsId().equals(goodsId))) {
                itemCjje.put(goodsId, sqliteSp.getSpxgzfje());
            }
        }

        double totalCjje = 0;
        for (Map.Entry<String, Double> entry : itemCjje.entrySet()) {
            totalCjje = MathUtil.add(totalCjje, entry.getValue());
        }

        Map<String, Double> itemCjjeZb = new HashMap<>();
        for (Map.Entry<String, Double> entry : itemCjje.entrySet()) {
            itemCjjeZb.put(entry.getKey(), MathUtil.divide(entry.getValue(), totalCjje, 8));
        }

        for (HfShareGoodsConfig hfShareGoodsConfig : configList) {
            double ratio = itemCjjeZb.getOrDefault(hfShareGoodsConfig.getGoodsId(), 0D);

            SqliteAiztShopDpzdSp sqliteAiztShopDpzdSp = newMap.getOrDefault(hfShareGoodsConfig.getGoodsId(), new SqliteAiztShopDpzdSp());
            sqliteAiztShopDpzdSp.setQd(getDataprobiBaseMsg().getQd());
            sqliteAiztShopDpzdSp.setQd_userid_bbid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid() + "_" + hfShareGoodsConfig.getGoodsId());
            sqliteAiztShopDpzdSp.setBbid(hfShareGoodsConfig.getGoodsId());
            sqliteAiztShopDpzdSp.setYhid(getDataprobiBaseMsg().getYhid());
            sqliteAiztShopDpzdSp.setRq(rq);
            sqliteAiztShopDpzdSp.setUserid(getDataprobiBaseMsg().getUserid());

            sqliteAiztShopDpzdSp.setAizt_shop_dpzd_hf(MathUtil.add(sqliteAiztShopDpzdSp.getAizt_shop_dpzd_hf(), MathUtil.multipy(cydata.getHf(), ratio)));
            newMap.put(hfShareGoodsConfig.getGoodsId(), sqliteAiztShopDpzdSp);
        }
    }
}
