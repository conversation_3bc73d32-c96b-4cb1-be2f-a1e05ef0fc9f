package com.abujlb.dataprobi.processes.tb;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.tb.DataprobiMsg;
import com.abujlb.dataprobi.bean.tb.SqliteLlzhYmfxDp;
import com.abujlb.dataprobi.bean.tb.SqliteLlzhYmfxSp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.util.MathUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/9
 */
@Component
@BiPro(order = 13, qd = Qd.TB)
public class DefaultBiSycmLlzhYmfxDealProcess extends AbstractBiprocess {

    private final String OSSKEY_PATTERN = "bi_jysj/%s/%s/llzh_ymfx.json";

    @Override
    public void dealGoods() {
        super.dealGoods(
                SqliteLlzhYmfxSp.class,
                OSSKEY_PATTERN,
                ((DataprobiMsg) getDataprobiBaseMsg()).getLlzhymfx()
        );
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoods(
                SqliteLlzhYmfxSp.class,
                OSSKEY_PATTERN,
                ((DataprobiMsg) getDataprobiBaseMsg()).getLlzhymfx()
        );
    }

    @Override
    protected <T extends AbstractSqliteSp> void dealShopByGoods(String rq, List<T> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        SqliteLlzhYmfxDp sqliteLlzhYmfxDp = new SqliteLlzhYmfxDp();
        sqliteLlzhYmfxDp.setQd_userid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid());
        sqliteLlzhYmfxDp.setDpmc(getDataprobiBaseMsg().getDpmc());
        sqliteLlzhYmfxDp.setUserid(getDataprobiBaseMsg().getUserid());
        sqliteLlzhYmfxDp.setYhid(getDataprobiBaseMsg().getYhid());
        sqliteLlzhYmfxDp.setQd(getDataprobiBaseMsg().getQd());
        sqliteLlzhYmfxDp.setRq(rq);
        for (T t : list) {
            if (t instanceof SqliteLlzhYmfxSp) {
                sqliteLlzhYmfxDp.setSpxgwirelesslll(sqliteLlzhYmfxDp.getSpxgwirelesslll() + ((SqliteLlzhYmfxSp) t).getSpxgwirelesslll());
                sqliteLlzhYmfxDp.setSpxgwirelessdjcs(sqliteLlzhYmfxDp.getSpxgwirelessdjcs() + ((SqliteLlzhYmfxSp) t).getSpxgwirelessdjcs());
                sqliteLlzhYmfxDp.setSpxgwirelessdjrs(sqliteLlzhYmfxDp.getSpxgwirelessdjrs() + ((SqliteLlzhYmfxSp) t).getSpxgwirelessdjrs());
                sqliteLlzhYmfxDp.setSpxgwirelessfks(sqliteLlzhYmfxDp.getSpxgwirelessfks() + ((SqliteLlzhYmfxSp) t).getSpxgwirelessfks());
            }
        }
        sqliteLlzhYmfxDp.setSpxgwirelessdjlv(MathUtil.divide(sqliteLlzhYmfxDp.getSpxgwirelessdjrs(), sqliteLlzhYmfxDp.getSpxgwirelessfks()));
        processSycmDpOTS(rq, sqliteLlzhYmfxDp);
    }
}
