package com.abujlb.dataprobi.processes.tb;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.tb.DataprobiMsg;
import com.abujlb.dataprobi.bean.tb.SqlitePpxxDp;
import com.abujlb.dataprobi.bean.tb.SqlitePpxxSp;
import com.abujlb.dataprobi.bean.tb.sp.PpxxXlsBean;
import com.abujlb.dataprobi.constant.DataprobiConst;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import com.abujlb.dataprobi.util.FileUtil;
import com.abujlb.dataprobi.util.MathUtil;
import com.abujlb.dataprobi.util.PpxxXlsReader;
import com.google.common.reflect.TypeToken;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.io.File;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/6/19
 */
@Component
@BiPro(order = 11, qd = Qd.TB)
public class DefaultBiPpxxDealProcess extends AbstractBiprocess {

    /*未使用到，仅传入而已*/
    private final String OSSKEY_PATTERN_NOT_USED_REAL = "bi_jysj/%s/%s/xkjs_zhjs.json";

    private final String[] COLUMNS = {BiSycmDpDataTsDao.TX_COLUMN_PPXX_xkjs_zhjs,
            BiSycmDpDataTsDao.TX_COLUMN_PPXX_xkjs_jzjs,
            BiSycmDpDataTsDao.TX_COLUMN_PPXX_xpfh_zhjs,
            BiSycmDpDataTsDao.TX_COLUMN_PPXX_cjhy_zhjs,
            BiSycmDpDataTsDao.TX_COLUMN_PPXX_cjfg_fgjs,
            BiSycmDpDataTsDao.TX_COLUMN_PPXX_cjlk_lkjs,
            BiSycmDpDataTsDao.TX_COLUMN_PPXX_lklj_lklj,
            BiSycmDpDataTsDao.TX_COLUMN_PPXX_yxtg_xktg,//这两个字段TB是反的
            BiSycmDpDataTsDao.TX_COLUMN_PPXX_yxtg_xptg//这两个字段TB是反的  TB的新客是新品  新品实际上是新客
    };
    private final Type type = new TypeToken<List<PpxxXlsBean>>() {
    }.getType();

    @Override
    public void dealGoods() {
        super.dealGoods(SqlitePpxxSp.class, OSSKEY_PATTERN_NOT_USED_REAL, ((DataprobiMsg) getDataprobiBaseMsg()).getPpxx());
    }

    @Override
    public void dealShop() {
        super.dealShop(SqlitePpxxDp.class, ((DataprobiMsg) getDataprobiBaseMsg()).getPpxxdp(), COLUMNS);
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoods(SqlitePpxxSp.class, OSSKEY_PATTERN_NOT_USED_REAL, ((DataprobiMsg) getDataprobiBaseMsg()).getPpxx());
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(SqlitePpxxDp.class, ((DataprobiMsg) getDataprobiBaseMsg()).getPpxxdp(), COLUMNS);
    }


    @Override
    protected <T extends AbstractSqliteSp> List<T> spList(Class<T> tClass, String ossKey, String rq) {
        if (tClass != SqlitePpxxSp.class) {
            return null;
        }
        String oss1 = "bi_jysj/" + getDataprobiBaseMsg().getUserid() + "/" + rq + "/xkjs_zhjs.json";
        List<PpxxXlsBean> list1 = readFromOssAndParse(oss1);

        String oss2 = "bi_jysj/" + getDataprobiBaseMsg().getUserid() + "/" + rq + "/xkjs_jzjs.json";
        List<PpxxXlsBean> list2 = readFromOssAndParse(oss2);

        String oss3 = "bi_jysj/" + getDataprobiBaseMsg().getUserid() + "/" + rq + "/xpfh_zhjs.json";
        List<PpxxXlsBean> list3 = readFromOssAndParse(oss3);

        String oss4 = "bi_jysj/" + getDataprobiBaseMsg().getUserid() + "/" + rq + "/cjhy_zhjs.json";
        List<PpxxXlsBean> list4 = readFromOssAndParse(oss4);

        String oss5 = "bi_jysj/" + getDataprobiBaseMsg().getUserid() + "/" + rq + "/cjfg_fgjs.json";
        List<PpxxXlsBean> list5 = readFromOssAndParse(oss5);

        String oss6 = "bi_jysj/" + getDataprobiBaseMsg().getUserid() + "/" + rq + "/cjlk_lkjs.json";
        List<PpxxXlsBean> list6 = readFromOssAndParse(oss6);

        String oss7 = "bi_jysj/" + getDataprobiBaseMsg().getUserid() + "/" + rq + "/lklj_lklj.json";
        List<PpxxXlsBean> list7 = readFromOssAndParse(oss7);

        Map<String, T> map = new HashMap<>();
        try {
            cal(map, tClass, rq, list1, 1);
            cal(map, tClass, rq, list2, 2);
            cal(map, tClass, rq, list3, 3);
            cal(map, tClass, rq, list4, 4);
            cal(map, tClass, rq, list5, 5);
            cal(map, tClass, rq, list6, 6);
            cal(map, tClass, rq, list7, 7);

            //2025-4-3 成交金额
            cjjeInit(map, rq);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return new ArrayList<>(map.values());
    }

    private List<PpxxXlsBean> readFromOssAndParse(String osskey) {
        try {
            if (!biDataOssUtil.exist(osskey)) {
                return null;
            }

            String json = biDataOssUtil.readJson(osskey);
            return DataprobiConst.GSON.fromJson(json, type);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return null;
        }
    }

    /**
     * 1：新客加速-转化加速
     * 2：新客加速-极致加速
     * 3：新品孵化-转化加速
     * 4：会员加速-转化加速
     *
     * @param map
     * @param tClass
     * @param rq
     * @param list
     * @param type
     */
    private <T extends AbstractSqliteSp> void cal(Map<String, T> map, Class<T> tClass, String rq, List<PpxxXlsBean> list, int type) throws InstantiationException, IllegalAccessException {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        for (PpxxXlsBean ppxxXlsBean : list) {
            T t = null;
            if (map.containsKey(ppxxXlsBean.getBbid())) {
                t = map.get(ppxxXlsBean.getBbid());
                if (type == 1) {
                    ((SqlitePpxxSp) t).setXkjszhjshf(MathUtil.add(((SqlitePpxxSp) t).getXkjszhjshf(), ppxxXlsBean.getFy()));
                } else if (type == 2) {
                    ((SqlitePpxxSp) t).setXkjsjzjshf(MathUtil.add(((SqlitePpxxSp) t).getXkjsjzjshf(), ppxxXlsBean.getFy()));
                } else if (type == 3) {
                    ((SqlitePpxxSp) t).setXpfhzhjshf(MathUtil.add(((SqlitePpxxSp) t).getXpfhzhjshf(), ppxxXlsBean.getFy()));
                } else if (type == 4) {
                    ((SqlitePpxxSp) t).setHyjszhjshf(MathUtil.add(((SqlitePpxxSp) t).getHyjszhjshf(), ppxxXlsBean.getFy()));
                } else if (type == 5) {
                    ((SqlitePpxxSp) t).setCjfgfgjshf(MathUtil.add(((SqlitePpxxSp) t).getCjfgfgjshf(), ppxxXlsBean.getFy()));
                } else if (type == 6) {
                    ((SqlitePpxxSp) t).setCjlklkjshf(MathUtil.add(((SqlitePpxxSp) t).getCjlklkjshf(), ppxxXlsBean.getFy()));
                } else if (type == 7) {
                    ((SqlitePpxxSp) t).setLkljlkljhf(MathUtil.add(((SqlitePpxxSp) t).getLkljlkljhf(), ppxxXlsBean.getFy()));
                }
            } else {
                t = tClass.newInstance();
                t.setQd_userid_bbid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid() + "_" + ppxxXlsBean.getBbid());
                t.setRq(rq);
                t.setYhid(getDataprobiBaseMsg().getYhid());
                t.setQd(getDataprobiBaseMsg().getQd());
                t.setUserid(getDataprobiBaseMsg().getUserid());
                t.setBbid(ppxxXlsBean.getBbid());

                if (type == 1) {
                    ((SqlitePpxxSp) t).setXkjszhjshf(ppxxXlsBean.getFy());
                } else if (type == 2) {
                    ((SqlitePpxxSp) t).setXkjsjzjshf(ppxxXlsBean.getFy());
                } else if (type == 3) {
                    ((SqlitePpxxSp) t).setXpfhzhjshf(ppxxXlsBean.getFy());
                } else if (type == 4) {
                    ((SqlitePpxxSp) t).setHyjszhjshf(ppxxXlsBean.getFy());
                } else if (type == 5) {
                    ((SqlitePpxxSp) t).setCjfgfgjshf(ppxxXlsBean.getFy());
                } else if (type == 6) {
                    ((SqlitePpxxSp) t).setCjlklkjshf(ppxxXlsBean.getFy());
                } else if (type == 7) {
                    ((SqlitePpxxSp) t).setLkljlkljhf(ppxxXlsBean.getFy());
                }
            }
            map.put(ppxxXlsBean.getBbid(), t);
        }
    }


    private <T extends AbstractSqliteSp> void cjjeInit(Map<String, T> map, String rq) {
        String oss1 = "bi_jysj/" + getDataprobiBaseMsg().getUserid() + "/" + rq + "/xkjs_zhjs.xlsx";
        Map<String, Double> map1 = readCjjeFromOssAndParse(oss1);

        String oss2 = "bi_jysj/" + getDataprobiBaseMsg().getUserid() + "/" + rq + "/xkjs_jzjs.xlsx";
        Map<String, Double> map2 = readCjjeFromOssAndParse(oss2);

        String oss3 = "bi_jysj/" + getDataprobiBaseMsg().getUserid() + "/" + rq + "/xpfh_zhjs.xlsx";
        Map<String, Double> map3 = readCjjeFromOssAndParse(oss3);

        String oss4 = "bi_jysj/" + getDataprobiBaseMsg().getUserid() + "/" + rq + "/cjhy_zhjs.xlsx";
        Map<String, Double> map4 = readCjjeFromOssAndParse(oss4);

        String oss5 = "bi_jysj/" + getDataprobiBaseMsg().getUserid() + "/" + rq + "/cjfg_fgjs.xlsx";
        Map<String, Double> map5 = readCjjeFromOssAndParse(oss5);

        String oss6 = "bi_jysj/" + getDataprobiBaseMsg().getUserid() + "/" + rq + "/cjlk_lkjs.xlsx";
        Map<String, Double> map6 = readCjjeFromOssAndParse(oss6);

        String oss7 = "bi_jysj/" + getDataprobiBaseMsg().getUserid() + "/" + rq + "/lklj_lklj.xlsx";
        Map<String, Double> map7 = readCjjeFromOssAndParse(oss7);

        try {
            calCjje(map, map1, 1);
            calCjje(map, map2, 2);
            calCjje(map, map3, 3);
            calCjje(map, map4, 4);
            calCjje(map, map5, 5);
            calCjje(map, map6, 6);
            calCjje(map, map7, 7);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
    }

    private <T extends AbstractSqliteSp> void calCjje(Map<String, T> map, Map<String, Double> cjjeMap, int type) {
        if (cjjeMap == null) {
            return;
        }
        for (Map.Entry<String, T> entry : map.entrySet()) {
            T value = entry.getValue();
            double val = cjjeMap.getOrDefault(entry.getKey(), 0D);
            if (value instanceof SqlitePpxxSp) {
                if (type == 1) {
                    ((SqlitePpxxSp) value).setXkjszhjscjje(val);
                } else if (type == 2) {
                    ((SqlitePpxxSp) value).setXkjsjzjscjje(val);
                } else if (type == 3) {
                    ((SqlitePpxxSp) value).setXpfhzhjscjje(val);
                } else if (type == 4) {
                    ((SqlitePpxxSp) value).setHyjszhjscjje(val);
                } else if (type == 5) {
                    ((SqlitePpxxSp) value).setCjfgfgjscjje(val);
                } else if (type == 6) {
                    ((SqlitePpxxSp) value).setCjlklkjscjje(val);
                } else if (type == 7) {
                    ((SqlitePpxxSp) value).setLkljlkljcjje(val);
                }
            }
        }
    }

    private Map<String, Double> readCjjeFromOssAndParse(String osskey) {
        File file = null;
        try {
            if (!biDataOssUtil.exist(osskey)) {
                return null;
            }

            String temppath = FileUtil.createXlsxFilePath();
            biDataOssUtil.download(osskey, temppath);
            file = new File(temppath);
            return PpxxXlsReader.readXls(file);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return null;
        } finally {
            if (file != null && file.exists()) {
                file.delete();
            }
        }
    }
}
