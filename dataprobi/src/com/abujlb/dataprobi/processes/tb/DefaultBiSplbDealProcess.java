package com.abujlb.dataprobi.processes.tb;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.sql.BiSqliteDb;
import com.abujlb.dataprobi.tsdao.SpnewTsDao;
import com.abujlb.dataprobi.util.FileUtil;
import com.abujlb.dataprobi.util.SqliteDbUtil;
import com.abujlb.util.StringUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.ResultSet;
import java.sql.Statement;

/**
 * <AUTHOR>
 * @date 2022/9/14 11:02
 */
@Component
@BiPro(order = 1, qd = Qd.TB)
public class DefaultBiSplbDealProcess extends AbstractBiprocess {

    private final String OSSKEY_PATTERN = "bi_jysj/%s/splb.db";
    @Autowired
    private SpnewTsDao spnewTsDao;

    @Override
    public void dealGoods() {
        if (getDataprobiBaseMsg().getSplb() == 1) {
            String key = String.format(OSSKEY_PATTERN, getDataprobiBaseMsg().getUserid());
            if (biDataOssUtil.exist(key)) {
                String filePath = FileUtil.createDBFilePath();
                biDataOssUtil.download(key, filePath);

                int pageNo = 1;
                final int pageSize = 100;
                BiSqliteDb biSqliteDb = null;
                Statement statement = null;
                JSONArray jsonArray = new JSONArray();
                try {
                    biSqliteDb = new BiSqliteDb(filePath);
                    //分页读取，一次100条
                    statement = biSqliteDb.createStatement();
                    while (true) {
                        int count = 0;
                        ResultSet resultSet = statement.executeQuery("SELECT `data` FROM tb_splb LIMIT " + pageSize + " OFFSET " + (pageNo - 1) * pageSize);
                        while (resultSet.next()) {
                            count++;
                            String data = resultSet.getString("data");
                            if (!StringUtil.isJson2(data)) {
                                continue;
                            }
                            jsonArray.add(JSONObject.parseObject(data));
                        }

                        spnewTsDao.initSp(getDataprobiBaseMsg().getYhid(), getDataprobiBaseMsg().getQd(), getDataprobiBaseMsg().getUserid(), jsonArray);

                        if (count < pageSize) {
                            break;
                        } else {
                            pageNo++;
                            jsonArray.clear();
                        }
                    }
                } catch (Exception e) {
                    logger.error(e.getMessage(), e);
                } finally {
                    SqliteDbUtil.closeStatement(statement);
                    SqliteDbUtil.close(biSqliteDb);
                }
            }
        }
    }

    @Override
    public boolean compareGoods() {
        if (getDataprobiBaseMsg().getSplb() != 1) {
            return true;
        }
        dealGoods();
        return false;
    }

}
