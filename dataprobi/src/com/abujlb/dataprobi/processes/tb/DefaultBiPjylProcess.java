package com.abujlb.dataprobi.processes.tb;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.tb.DataprobiMsg;
import com.abujlb.dataprobi.bean.tb.SqlitePjylDp;
import com.abujlb.dataprobi.bean.tb.SqlitePjylSp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.util.MathUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 评价有礼
 *
 * <AUTHOR>
 * @date 2024/4/28
 */
@Component
@BiPro(order = 13, qd = Qd.TB)
public class DefaultBiPjylProcess extends AbstractBiprocess {

    public static final String OSSKEY_PATTERN = "bi_jysj/%s/%s/pjyl.json";


    @Override
    public void dealGoods() {
        super.dealGoods(
                SqlitePjylSp.class,
                OSSKEY_PATTERN,
                ((DataprobiMsg) getDataprobiBaseMsg()).getPjyl()
        );
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoods(
                SqlitePjylSp.class,
                OSSKEY_PATTERN,
                ((DataprobiMsg) getDataprobiBaseMsg()).getPjyl()
        );
    }

    @Override
    protected <T extends AbstractSqliteSp> void dealShopByGoods(String rq, List<T> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        //汇总店铺
        double pjylhb = 0;
        for (T t : list) {
            if (t instanceof SqlitePjylSp) {
                pjylhb = MathUtil.add(pjylhb, ((SqlitePjylSp) t).getPjylhb());
            }
        }
        if (pjylhb == 0) {
            return;
        }

        SqlitePjylDp sqlitePjylDp = new SqlitePjylDp();
        sqlitePjylDp.setQd(getDataprobiBaseMsg().getQd());
        sqlitePjylDp.setQd_userid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid());
        sqlitePjylDp.setRq(rq);
        sqlitePjylDp.setYhid(getDataprobiBaseMsg().getYhid());
        sqlitePjylDp.setUserid(getDataprobiBaseMsg().getUserid());
        sqlitePjylDp.setPjylhb(pjylhb);
        processSycmDpOTS(rq, sqlitePjylDp);
    }
}
