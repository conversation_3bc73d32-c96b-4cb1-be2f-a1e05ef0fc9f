package com.abujlb.dataprobi.processes.tb;

import com.abujlb.CommonConfig;
import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteDp;
import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.tb.DataprobiMsg;
import com.abujlb.dataprobi.bean.tb.SqliteXstgDp;
import com.abujlb.dataprobi.bean.tb.SqliteXstgSp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.util.AiztOneCSVReader;
import com.abujlb.dataprobi.util.MathUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2024/7/10
 */
@Component
@BiPro(order = 6, qd = Qd.TB)
public class DefaultBiAdbrainOneXstgProcess extends AbstractBiTXprocess {

    public static final String OSSKEY_PATTERN = "bi_jysj/%s/%s/bi_aiztone_xstg.xlsx";

    @Override
    public void dealGoods() {
        super.dealGoods(
                SqliteXstgSp.class,
                OSSKEY_PATTERN,
                ((DataprobiMsg) getDataprobiBaseMsg()).getXstg(),
                true
        );
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoods(
                SqliteXstgSp.class,
                OSSKEY_PATTERN,
                ((DataprobiMsg) getDataprobiBaseMsg()).getXstg(),
                true
        );
    }

    @Override
    protected <T extends AbstractSqliteSp> List<T> spList(Class<T> tClass, String ossKey, String rq) {
        if (!biDataOssUtil.exist(ossKey)) {
            return Collections.emptyList();
        }

        String temppath = CommonConfig.getString("tempdir") + UUID.randomUUID() + ".xlsx";
        if(!biDataOssUtil.exist(ossKey)){
            return Collections.emptyList();
        }
        biDataOssUtil.download(ossKey, temppath);

        List<SqliteXstgSp> list = AiztOneCSVReader.parseXstg(temppath, rq);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        return (List<T>) list;
    }

    @Override
    protected <T extends AbstractSqliteSp> void dealShopByGoods(String rq, List<T> list) {
        SqliteXstgDp dpData = dpObject(SqliteXstgDp.class, rq, StringUtils.EMPTY);
        if (dpData != null) {
            processSycmDpOTS(rq, dpData);
        }
    }

    @Override
    protected <T extends AbstractSqliteDp> T dpObject(Class<T> tClass, String rq, String... colummns) {
        String ossKey = String.format(OSSKEY_PATTERN, getDataprobiBaseMsg().getUserid(), rq);
        List<SqliteXstgSp> sqliteXstgSps = this.spList(SqliteXstgSp.class, ossKey, rq);
        SqliteXstgDp sqliteXstgDp = new SqliteXstgDp();
        sqliteXstgDp.setQd(getDataprobiBaseMsg().getQd());
        sqliteXstgDp.setQd_userid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid());
        sqliteXstgDp.setRq(rq);
        sqliteXstgDp.setYhid(getDataprobiBaseMsg().getYhid());
        sqliteXstgDp.setUserid(getDataprobiBaseMsg().getUserid());
        //汇总店铺维度花费
        double hf = 0;
        //汇总店铺维度成交金额
        double cjje = 0;
        for (SqliteXstgSp t : sqliteXstgSps) {
            hf = MathUtil.add(hf, t.getAiztoneXstghf());
            cjje = MathUtil.add(cjje, t.getAiztoneXstgCjje());
        }
        sqliteXstgDp.setAiztoneXstghf(hf);
        sqliteXstgDp.setAiztoneXstgCjje(cjje);
        return (T) sqliteXstgDp;
    }
}
