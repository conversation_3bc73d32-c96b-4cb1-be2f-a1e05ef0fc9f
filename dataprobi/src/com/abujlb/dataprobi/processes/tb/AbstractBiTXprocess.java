package com.abujlb.dataprobi.processes.tb;

import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.DataprobiBaseMsgBean;
import com.abujlb.dataprobi.bean.SqliteSp;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.threads.BiThreadLocals;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/5/13 14:46
 */
@Component
public abstract class AbstractBiTXprocess extends AbstractBiprocess {

    public static final Logger logger = Logger.getLogger(AbstractBiTXprocess.class);


    protected <T extends AbstractSqliteSp> void dealGoods(Class<T> tClass, String ossKeyFormat, List<String> rqList, boolean cover) {
        for (String rq : rqList) {
            sqliteDbUtil.clearSpData(null, rq, tClass);

            String ossKey = String.format(ossKeyFormat, getDataprobiBaseMsg().getUserid(), rq);
            List<T> list = spList(tClass, ossKey, rq);
            if (list == null) {
                list = new ArrayList<>();
            }
            processSycmSpOTS(rq, list);
            dealShopByGoods(rq, list);
            list.clear();
        }
    }


    protected <T extends AbstractSqliteSp> boolean compareGoods(Class<T> tClass, String ossKeyFormat, List<String> rqList, boolean cover) {
        boolean result = true;
        DataprobiBaseMsgBean msgBean = BiThreadLocals.getMsgBean();
        for (String rq : rqList) {
            String ossKey = String.format(ossKeyFormat, getDataprobiBaseMsg().getUserid(), rq);
            List<T> list = spList(tClass, ossKey, rq);
            if (list == null) {
                list = new ArrayList<>();
            }

            if (!result) {
                sqliteDbUtil.clearSpData(null, rq, tClass);

                processSycmSpOTS(rq, list);
                dealShopByGoods(rq, list);
                list.clear();
                continue;
            }

            Map<String, T> currListMap = new HashMap<>();
            for (T t : list) {
                currListMap.put(t.getBbid(), t);
            }

            boolean flag = true;
            Map<String, SqliteSp> map = getSycmSpMapByBbid(rq);
            if (map.isEmpty() && !list.isEmpty()) {
                flag = false;
            }

            for (Map.Entry<String, SqliteSp> entry : map.entrySet()) {
                String bbid = entry.getKey();
                T t = null;
                if (currListMap.containsKey(bbid)) {
                    t = currListMap.get(bbid);
                } else {
                    try {
                        t = tClass.newInstance();
                        t.setBbid(bbid);
                        t.setQd_userid_bbid(msgBean.getQd() + "_" + msgBean.getUserid() + "_" + t.getBbid());
                        t.setRq(rq);
                        t.setYhid(msgBean.getYhid());
                        t.setQd(msgBean.getQd());
                        t.setUserid(msgBean.getUserid());
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                }
                flag = t.compare(map.get(t.getBbid()));
                if (!flag) {
                    result = false;
                    break;
                }
            }
            if (!flag) {
                sqliteDbUtil.clearSpData(null, rq, tClass);

                processSycmSpOTS(rq, list);
                dealShopByGoods(rq, list);
            }
            map.clear();
            currListMap.clear();
            list.clear();
        }
        return result;
    }
}
