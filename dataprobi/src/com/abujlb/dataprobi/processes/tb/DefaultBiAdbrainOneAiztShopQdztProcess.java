package com.abujlb.dataprobi.processes.tb;

import com.abujlb.CommonConfig;
import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.tb.DataprobiMsg;
import com.abujlb.dataprobi.bean.tb.SqliteAiztShopQdztDp;
import com.abujlb.dataprobi.bean.tb.SqliteAiztShopQdztSp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.util.AiztOneCSVReader;
import com.abujlb.dataprobi.util.MathUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.UUID;

/**
 * 无界-店铺运营 - 全店智投
 *
 * <AUTHOR>
 * @date 2025/6/13
 */
@Component
@BiPro(order = 6, qd = Qd.TB)
public class DefaultBiAdbrainOneAiztShopQdztProcess extends AbstractBiTXprocess {

    public static final String OSSKEY_PATTERN = "bi_jysj/%s/%s/bi_aiztone_aizt_shop.csv";

    @Override
    public void dealGoods() {
        super.dealGoods(
                SqliteAiztShopQdztSp.class,
                OSSKEY_PATTERN,
                ((DataprobiMsg) getDataprobiBaseMsg()).getAizt_shop_qdzt(),
                true
        );
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoods(
                SqliteAiztShopQdztSp.class,
                OSSKEY_PATTERN,
                ((DataprobiMsg) getDataprobiBaseMsg()).getAizt_shop_qdzt(),
                true
        );
    }

    @Override
    protected <T extends AbstractSqliteSp> List<T> spList(Class<T> tClass, String ossKey, String rq) {
        if (((DataprobiMsg) getDataprobiBaseMsg()).getAiztone() == 0) {
            return Collections.emptyList();
        }

        if (!biDataOssUtil.exist(ossKey)) {
            return Collections.emptyList();
        }

        String temppath = CommonConfig.getString("tempdir") + UUID.randomUUID() + ".csv";
        biDataOssUtil.download(ossKey, temppath);

        List<SqliteAiztShopQdztSp> list = AiztOneCSVReader.parseCSV(SqliteAiztShopQdztSp.class, temppath, rq);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        return (List<T>) list;
    }

    @Override
    protected <T extends AbstractSqliteSp> void dealShopByGoods(String rq, List<T> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        SqliteAiztShopQdztDp sqliteAiztShopQdztDp = new SqliteAiztShopQdztDp();
        double aizt_shop_qdzd_hf = 0;
        double aizt_shop_qdzd_cjje = 0;
        for (T t : list) {
            if (t instanceof SqliteAiztShopQdztSp) {
                aizt_shop_qdzd_hf = MathUtil.add(aizt_shop_qdzd_hf, ((SqliteAiztShopQdztSp) t).getAizt_shop_qdzd_hf());
                aizt_shop_qdzd_cjje = MathUtil.add(aizt_shop_qdzd_cjje, ((SqliteAiztShopQdztSp) t).getAizt_shop_qdzd_cjje());
            }
        }
        sqliteAiztShopQdztDp.setQd(getDataprobiBaseMsg().getQd());
        sqliteAiztShopQdztDp.setQd_userid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid());
        sqliteAiztShopQdztDp.setRq(rq);
        sqliteAiztShopQdztDp.setYhid(getDataprobiBaseMsg().getYhid());
        sqliteAiztShopQdztDp.setUserid(getDataprobiBaseMsg().getUserid());
        sqliteAiztShopQdztDp.setAizt_shop_qdzd_hf(aizt_shop_qdzd_hf);
        sqliteAiztShopQdztDp.setAizt_shop_qdzd_cjje(aizt_shop_qdzd_cjje);
        processSycmDpOTS(rq, sqliteAiztShopQdztDp);
    }
}
