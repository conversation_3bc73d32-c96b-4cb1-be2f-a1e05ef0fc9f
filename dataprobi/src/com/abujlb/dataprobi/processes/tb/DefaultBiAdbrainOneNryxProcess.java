package com.abujlb.dataprobi.processes.tb;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.tb.DataprobiMsg;
import com.abujlb.dataprobi.bean.tb.SqliteNryxDp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import org.springframework.stereotype.Component;

@Component
@BiPro(order = 6, qd = Qd.TB)
public class DefaultBiAdbrainOneNryxProcess extends AbstractBiprocess {

    public static final String[] COLUMNS = {"tx_aiztone_cjzb", "tx_aiztone_dsp", "tx_aiztone_dzld", "tx_nryx_dsp"};

    @Override
    public void dealShop() {
        super.dealShop(
                SqliteNryxDp.class,
                ((DataprobiMsg) getDataprobiBaseMsg()).getNryxdp(),
                COLUMNS
        );
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqliteNryxDp.class,
                ((DataprobiMsg) getDataprobiBaseMsg()).getNryxdp(),
                COLUMNS
        );
    }
}
