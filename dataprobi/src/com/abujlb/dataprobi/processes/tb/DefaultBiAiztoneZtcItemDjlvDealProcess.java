package com.abujlb.dataprobi.processes.tb;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.DataprobiBaseMsgBean;
import com.abujlb.dataprobi.bean.tb.DataprobiMsg;
import com.abujlb.dataprobi.bean.tb.SqliteAiztoneZtcItemDjlvSp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.threads.BiThreadLocals;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2024/5/18
 */
@Component
@BiPro(order = 20, qd = Qd.TB)
public class DefaultBiAiztoneZtcItemDjlvDealProcess extends AbstractBiprocess {

    public static final String OSSKEY_PATTERN = "bi_jysj/%s/%s/bi_aiztone_ztcitemdjlv.json";

    @Override
    public void dealGoods() {
        super.dealGoods(
                SqliteAiztoneZtcItemDjlvSp.class,
                OSSKEY_PATTERN,
                ((DataprobiMsg) getDataprobiBaseMsg()).getAiztoneztcitemdjlv()
        );
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoods(
                SqliteAiztoneZtcItemDjlvSp.class,
                OSSKEY_PATTERN,
                ((DataprobiMsg) getDataprobiBaseMsg()).getAiztoneztcitemdjlv()
        );
    }

    @Override
    protected <T extends AbstractSqliteSp> List<T> spList(Class<T> tClass, String ossKey, String rq) {
        if (biDataOssUtil.exist((ossKey))) {
            String json = biDataOssUtil.readJson(ossKey);
            if (StringUtils.isBlank(json)) {
                return Collections.emptyList();
            }
            JSONArray jsonArray = JSONArray.parseArray(json);
            if (CollectionUtils.isEmpty(jsonArray)) {
                return Collections.emptyList();
            }

            DataprobiBaseMsgBean msgBean = BiThreadLocals.getMsgBean();
            Map<String, SqliteAiztoneZtcItemDjlvSp> map = new HashMap<>();
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                String bbid = FastJSONObjAttrToNumber.toString(jsonObject, "itemId");
                if (map.containsKey(bbid)) {
                    continue;
                }

                SqliteAiztoneZtcItemDjlvSp sqliteAiztoneZtcItemDjlvSp = new SqliteAiztoneZtcItemDjlvSp();
                sqliteAiztoneZtcItemDjlvSp.setValues(jsonObject);
                sqliteAiztoneZtcItemDjlvSp.setQd_userid_bbid(msgBean.getQd() + "_" + msgBean.getUserid() + "_" + bbid);
                sqliteAiztoneZtcItemDjlvSp.setRq(rq);
                sqliteAiztoneZtcItemDjlvSp.setYhid(msgBean.getYhid());
                sqliteAiztoneZtcItemDjlvSp.setQd(msgBean.getQd());
                sqliteAiztoneZtcItemDjlvSp.setUserid(msgBean.getUserid());
                map.put(bbid, sqliteAiztoneZtcItemDjlvSp);
            }
            return (List<T>) new ArrayList<>(map.values());
        }
        return Collections.emptyList();
    }
}
