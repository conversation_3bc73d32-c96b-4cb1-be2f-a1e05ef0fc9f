package com.abujlb.dataprobi.processes.wph;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.wph.DataprobiWphMsg;
import com.abujlb.dataprobi.bean.wph.SqliteJlyqHfDp;
import com.abujlb.dataprobi.bean.wph.SqliteYxptHfDp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import org.springframework.stereotype.Component;


/**
 * 营销平台花费
 */
@Component
@BiPro(order = 5, qd = Qd.WPH)
public class DefaultbiYxpthfProcess extends AbstractBiprocess {

    private final String[] COLUMNS = {BiSycmDpDataTsDao.WPH_COLUMN_YXPTHF};

    @Override
    public void dealShop() {
        super.dealShop(
                SqliteYxptHfDp.class,
                ((DataprobiWphMsg) getDataprobiBaseMsg()).getYxpthfdp(),
                COLUMNS
        );
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqliteYxptHfDp.class,
                ((DataprobiWphMsg) getDataprobiBaseMsg()).getYxpthfdp(),
                COLUMNS
        );
    }

}
