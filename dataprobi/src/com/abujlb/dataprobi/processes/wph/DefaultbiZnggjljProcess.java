package com.abujlb.dataprobi.processes.wph;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.wph.DataprobiWphMsg;
import com.abujlb.dataprobi.bean.wph.SqliteJlyqJljDp;
import com.abujlb.dataprobi.bean.wph.SqliteZnggJljDp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import org.springframework.stereotype.Component;


/**
 * 站内广告-奖励金
 */
@Component
@BiPro(order = 8, qd = Qd.WPH)
public class DefaultbiZnggjljProcess extends AbstractBiprocess {

    private final String[] COLUMNS = {BiSycmDpDataTsDao.WPH_COLUMN_ZNGGJLJ};

    @Override
    public void dealShop() {
        super.dealShop(
                SqliteZnggJljDp.class,
                ((DataprobiWphMsg) getDataprobiBaseMsg()).getZnggjljdp(),
                COLUMNS
        );
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqliteZnggJljDp.class,
                ((DataprobiWphMsg) getDataprobiBaseMsg()).getZnggjljdp(),
                COLUMNS
        );
    }

}
