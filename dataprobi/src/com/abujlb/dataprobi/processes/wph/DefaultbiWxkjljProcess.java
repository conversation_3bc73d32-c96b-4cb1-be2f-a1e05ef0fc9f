package com.abujlb.dataprobi.processes.wph;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.wph.DataprobiWphMsg;
import com.abujlb.dataprobi.bean.wph.SqliteWxkJljDp;
import com.abujlb.dataprobi.bean.wxsph.DataprobiWxsphMsg;
import com.abujlb.dataprobi.bean.wxsph.SqliteWxsphYhqDp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import org.springframework.stereotype.Component;


/**
 * 唯享客奖励金
 */
@Component
@BiPro(order = 3, qd = Qd.WPH)
public class DefaultbiWxkjljProcess extends AbstractBiprocess {

    private final String[] COLUMNS = {BiSycmDpDataTsDao.WPH_COLUMN_WXKJLJ};

    @Override
    public void dealShop() {
        super.dealShop(
                SqliteWxkJljDp.class,
                ((DataprobiWphMsg) getDataprobiBaseMsg()).getWxkjljdp(),
                COLUMNS
        );
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqliteWxkJljDp.class,
                ((DataprobiWphMsg) getDataprobiBaseMsg()).getWxkjljdp(),
                COLUMNS
        );
    }

}
