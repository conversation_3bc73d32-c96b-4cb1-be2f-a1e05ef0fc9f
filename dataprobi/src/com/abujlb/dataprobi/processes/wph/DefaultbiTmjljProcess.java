package com.abujlb.dataprobi.processes.wph;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.wph.DataprobiWphMsg;
import com.abujlb.dataprobi.bean.wph.SqliteJlyqJljDp;
import com.abujlb.dataprobi.bean.wph.SqliteTmJljDp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import org.springframework.stereotype.Component;


/**
 * target-max奖励金
 */
@Component
@BiPro(order = 7, qd = Qd.WPH)
public class DefaultbiTmjljProcess extends AbstractBiprocess {

    private final String[] COLUMNS = {BiSycmDpDataTsDao.WPH_COLUMN_TMJLJ};

    @Override
    public void dealShop() {
        super.dealShop(
                SqliteTmJljDp.class,
                ((DataprobiWphMsg) getDataprobiBaseMsg()).getTmjljdp(),
                COLUMNS
        );
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqliteTmJljDp.class,
                ((DataprobiWphMsg) getDataprobiBaseMsg()).getTmjljdp(),
                COLUMNS
        );
    }

}
