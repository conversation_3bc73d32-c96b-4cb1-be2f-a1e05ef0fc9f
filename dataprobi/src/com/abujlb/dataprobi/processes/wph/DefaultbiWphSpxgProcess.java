package com.abujlb.dataprobi.processes.wph;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.wph.DataprobiWphMsg;
import com.abujlb.dataprobi.bean.wph.SqliteWphSpxgDp;
import com.abujlb.dataprobi.bean.wph.SqliteWphSpxgSp;
import com.abujlb.dataprobi.bean.wxsph.DataprobiWxsphMsg;
import com.abujlb.dataprobi.bean.wxsph.SqliteWxsphSpxgDp;
import com.abujlb.dataprobi.bean.wxsph.SqliteWxsphSpxgSp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import org.springframework.stereotype.Component;


/**
 * 唯品会商品效果
 */
@Component
@BiPro(order = 1, qd = Qd.WPH)
public class DefaultbiWphSpxgProcess extends AbstractBiprocess {

    public static final String OSSKEY_PATTERN = "biwph/%s/%s/spxg.json";
    private final String[] COLUMNS = {BiSycmDpDataTsDao.WPH_COLUMN_SPXG};

    @Override
    public void dealGoods() {
        super.dealGoods(
                SqliteWphSpxgSp.class,
                OSSKEY_PATTERN,
                ((DataprobiWphMsg) getDataprobiBaseMsg()).getSpxg()
        );
    }

    @Override
    public void dealShop() {
        super.dealShop(
                SqliteWphSpxgDp.class,
                ((DataprobiWphMsg) getDataprobiBaseMsg()).getSpxgdp(),
                COLUMNS
        );
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoods(
                SqliteWphSpxgSp.class,
                OSSKEY_PATTERN,
                ((DataprobiWphMsg) getDataprobiBaseMsg()).getSpxg()
        );
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqliteWphSpxgDp.class,
                ((DataprobiWphMsg) getDataprobiBaseMsg()).getSpxgdp(),
                COLUMNS
        );
    }

}
