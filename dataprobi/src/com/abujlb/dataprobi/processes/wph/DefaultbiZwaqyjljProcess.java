package com.abujlb.dataprobi.processes.wph;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.wph.DataprobiWphMsg;
import com.abujlb.dataprobi.bean.wph.SqliteAqyJljDp;
import com.abujlb.dataprobi.bean.wph.SqliteJlyqJljDp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import org.springframework.stereotype.Component;


/**
 * 站外广告-爱奇艺奖励金
 */
@Component
@BiPro(order = 4, qd = Qd.WPH)
public class DefaultbiZwaqyjljProcess extends AbstractBiprocess {

    private final String[] COLUMNS = {BiSycmDpDataTsDao.WPH_COLUMN_ZWAQYJLJ};

    @Override
    public void dealShop() {
        super.dealShop(
                SqliteAqyJljDp.class,
                ((DataprobiWphMsg) getDataprobiBaseMsg()).getZwaqyjljdp(),
                COLUMNS
        );
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqliteAqyJljDp.class,
                ((DataprobiWphMsg) getDataprobiBaseMsg()).getZwaqyjljdp(),
                COLUMNS
        );
    }

}
