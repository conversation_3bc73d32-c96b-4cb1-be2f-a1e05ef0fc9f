package com.abujlb.dataprobi.processes.wph;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.wph.DataprobiWphMsg;
import com.abujlb.dataprobi.bean.wph.SqliteJlyqHfDp;
import com.abujlb.dataprobi.bean.wph.SqliteJlyqJljDp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import org.springframework.stereotype.Component;


/**
 * 站外广告-巨量引擎奖励金
 */
@Component
@BiPro(order = 6, qd = Qd.WPH)
public class DefaultbiZwjlyqjljProcess extends AbstractBiprocess {

    private final String[] COLUMNS = {BiSycmDpDataTsDao.WPH_COLUMN_ZWJLYQJLJ};

    @Override
    public void dealShop() {
        super.dealShop(
                SqliteJlyqJljDp.class,
                ((DataprobiWphMsg) getDataprobiBaseMsg()).getZwjlyqjljdp(),
                COLUMNS
        );
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqliteJlyqJljDp.class,
                ((DataprobiWphMsg) getDataprobiBaseMsg()).getZwjlyqjljdp(),
                COLUMNS
        );
    }

}
