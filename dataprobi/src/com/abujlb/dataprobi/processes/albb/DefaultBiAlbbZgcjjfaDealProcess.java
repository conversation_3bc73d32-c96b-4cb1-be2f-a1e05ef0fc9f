package com.abujlb.dataprobi.processes.albb;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.albb.Dataprobi1688Msg;
import com.abujlb.dataprobi.bean.albb.SqliteAlbbZgcjjfaDp;
import com.abujlb.dataprobi.bean.albb.SqliteAlbbZgcjjfaSp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import org.springframework.stereotype.Component;

/**
 * 找工厂解决方案
 *
 * <AUTHOR>
 * @date 2024/7/30
 */
@Component
@BiPro(order = 5, qd = Qd.ALBB)
public class DefaultBiAlbbZgcjjfaDealProcess extends AbstractBiprocess {

    private final String OSSKEY_PATTERN = "bi1688/%s/%s/zgcjjfa.json";
    private final String[] COLUMNS = {BiSycmDpDataTsDao.ALBB_COLUMN_ZGCJJFA};

    @Override
    public void dealGoods() {
        super.dealGoods(
                SqliteAlbbZgcjjfaSp.class,
                OSSKEY_PATTERN,
                ((Dataprobi1688Msg) getDataprobiBaseMsg()).getZgcjjfa()
        );
    }

    @Override
    public void dealShop() {
        super.dealShop(
                SqliteAlbbZgcjjfaDp.class,
                ((Dataprobi1688Msg) getDataprobiBaseMsg()).getZgcjjfadp(),
                COLUMNS
        );
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoods(
                SqliteAlbbZgcjjfaSp.class,
                OSSKEY_PATTERN,
                ((Dataprobi1688Msg) getDataprobiBaseMsg()).getZgcjjfa()
        );
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqliteAlbbZgcjjfaDp.class,
                ((Dataprobi1688Msg) getDataprobiBaseMsg()).getZgcjjfadp(),
                COLUMNS
        );
    }
}
