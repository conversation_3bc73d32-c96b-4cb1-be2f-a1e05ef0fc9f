package com.abujlb.dataprobi.processes.albb;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.albb.*;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import org.springframework.stereotype.Component;

/**
 * 全站推店
 *
 * <AUTHOR>
 * @date 2024/6/12
 */
@Component
@BiPro(order = 9, qd = Qd.ALBB)
public class DefaultBiAlbbQztdDealProcess extends AbstractBiprocess {

    private final String OSSKEY_PATTERN = "bi1688/%s/%s/qztd.json";
    private final String[] COLUMNS = {BiSycmDpDataTsDao.ALBB_COLUMN_QZTD};

    @Override
    public void dealGoods() {
        super.dealGoods(
                SqliteAlbbQztdSp.class,
                OSSKEY_PATTERN,
                ((Dataprobi1688Msg) getDataprobiBaseMsg()).getQztd()
        );
    }

    @Override
    public void dealShop() {
        super.dealShop(
                SqliteAlbbQztdDp.class,
                ((Dataprobi1688Msg) getDataprobiBaseMsg()).getQztddp(),
                COLUMNS
        );
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoods(
                SqliteAlbbQztdSp.class,
                OSSKEY_PATTERN,
                ((Dataprobi1688Msg) getDataprobiBaseMsg()).getQztd()
        );
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqliteAlbbQztdDp.class,
                ((Dataprobi1688Msg) getDataprobiBaseMsg()).getQztddp(),
                COLUMNS
        );
    }
}
