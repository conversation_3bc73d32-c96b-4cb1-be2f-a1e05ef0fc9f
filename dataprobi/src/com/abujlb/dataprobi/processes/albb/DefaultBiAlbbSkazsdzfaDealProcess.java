package com.abujlb.dataprobi.processes.albb;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.albb.*;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import org.springframework.stereotype.Component;

/**
 * SKA专属定制方案
 *
 * <AUTHOR>
 * @date 2024/7/9
 */
@Component
@BiPro(order = 9, qd = Qd.ALBB)
public class DefaultBiAlbbSkazsdzfaDealProcess extends AbstractBiprocess {

    private final String OSSKEY_PATTERN = "bi1688/%s/%s/skazsdzfa.json";
    private final String[] COLUMNS = {BiSycmDpDataTsDao.ALBB_COLUMN_SKAZSDZFA};

    @Override
    public void dealGoods() {
        super.dealGoods(
                SqliteAlbbSkazsdzfaSp.class,
                OSSKEY_PATTERN,
                ((Dataprobi1688Msg) getDataprobiBaseMsg()).getSkazsdzfa()
        );
    }

    @Override
    public void dealShop() {
        super.dealShop(
                SqliteAlbbSkazsdzfaDp.class,
                ((Dataprobi1688Msg) getDataprobiBaseMsg()).getSkazsdzfadp(),
                COLUMNS
        );
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoods(
                SqliteAlbbSkazsdzfaSp.class,
                OSSKEY_PATTERN,
                ((Dataprobi1688Msg) getDataprobiBaseMsg()).getSkazsdzfa()
        );
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqliteAlbbSkazsdzfaDp.class,
                ((Dataprobi1688Msg) getDataprobiBaseMsg()).getSkazsdzfadp(),
                COLUMNS
        );
    }
}
