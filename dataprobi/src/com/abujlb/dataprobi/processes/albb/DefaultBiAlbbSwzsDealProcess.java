package com.abujlb.dataprobi.processes.albb;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.albb.*;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import org.springframework.stereotype.Component;

/**
 * 首位展示
 *
 * <AUTHOR>
 * @date 2024/6/12
 */
@Component
@BiPro(order = 11, qd = Qd.ALBB)
public class DefaultBiAlbbSwzsDealProcess extends AbstractBiprocess {

    private final String[] COLUMNS = {BiSycmDpDataTsDao.ALBB_COLUMN_SWZS};

    @Override
    public void dealShop() {
        super.dealShop(
                SqliteAlbbSwzsDp.class,
                ((Dataprobi1688Msg) getDataprobiBaseMsg()).getSwzsdp(),
                COLUMNS
        );
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqliteAlbbSwzsDp.class,
                ((Dataprobi1688Msg) getDataprobiBaseMsg()).getSwzsdp(),
                COLUMNS
        );
    }
}
