package com.abujlb.dataprobi.processes.albb;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.albb.Dataprobi1688Msg;
import com.abujlb.dataprobi.bean.albb.SqliteAlbbDwtgDp;
import com.abujlb.dataprobi.bean.albb.SqliteAlbbSwzsDp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import org.springframework.stereotype.Component;

/**
 * 定位推广
 *
 * <AUTHOR>
 * @date 2024/6/12
 */
@Component
@BiPro(order = 4, qd = Qd.ALBB)
public class DefaultBiAlbbDwtgDealProcess extends AbstractBiprocess {

    private final String[] COLUMNS = {BiSycmDpDataTsDao.ALBB_COLUMN_DWTG};

    @Override
    public void dealShop() {
        super.dealShop(
                SqliteAlbbDwtgDp.class,
                ((Dataprobi1688Msg) getDataprobiBaseMsg()).getDwtgdp(),
                COLUMNS
        );
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqliteAlbbDwtgDp.class,
                ((Dataprobi1688Msg) getDataprobiBaseMsg()).getDwtgdp(),
                COLUMNS
        );
    }
}
