package com.abujlb.dataprobi.processes.albb;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.albb.*;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import org.springframework.stereotype.Component;

/**
 * 核心商家成长计划
 *
 * <AUTHOR>
 * @date 2024/7/30
 */
@Component
@BiPro(order = 5, qd = Qd.ALBB)
public class DefaultBiAlbbHxsjczjhDealProcess extends AbstractBiprocess {

    private final String OSSKEY_PATTERN = "bi1688/%s/%s/hxsjczjh.json";
    private final String[] COLUMNS = {BiSycmDpDataTsDao.ALBB_COLUMN_HXSJCZJH};

    @Override
    public void dealGoods() {
        super.dealGoods(
                SqliteAlbbHxsjczjhSp.class,
                OSSKEY_PATTERN,
                ((Dataprobi1688Msg) getDataprobiBaseMsg()).getHxsjczjh()
        );
    }

    @Override
    public void dealShop() {
        super.dealShop(
                SqliteAlbbHxsjczjhDp.class,
                ((Dataprobi1688Msg) getDataprobiBaseMsg()).getHxsjczjhdp(),
                COLUMNS
        );
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoods(
                SqliteAlbbHxsjczjhSp.class,
                OSSKEY_PATTERN,
                ((Dataprobi1688Msg) getDataprobiBaseMsg()).getHxsjczjh()
        );
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqliteAlbbHxsjczjhDp.class,
                ((Dataprobi1688Msg) getDataprobiBaseMsg()).getHxsjczjhdp(),
                COLUMNS
        );
    }
}
