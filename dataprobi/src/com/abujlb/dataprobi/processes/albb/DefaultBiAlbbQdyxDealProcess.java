package com.abujlb.dataprobi.processes.albb;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.albb.*;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import org.springframework.stereotype.Component;

/**
 * 全店引新
 *
 * <AUTHOR>
 * @date 2024/6/12
 */
@Component
@BiPro(order = 8, qd = Qd.ALBB)
public class DefaultBiAlbbQdyxDealProcess extends AbstractBiprocess {

    private final String OSSKEY_PATTERN = "bi1688/%s/%s/qdyx.json";
    private final String[] COLUMNS = {BiSycmDpDataTsDao.ALBB_COLUMN_QDYX};

    @Override
    public void dealGoods() {
        super.dealGoods(
                SqliteAlbbQdyxSp.class,
                OSSKEY_PATTERN,
                ((Dataprobi1688Msg) getDataprobiBaseMsg()).getQdyx()
        );
    }

    @Override
    public void dealShop() {
        super.dealShop(
                SqliteAlbbQdyxDp.class,
                ((Dataprobi1688Msg) getDataprobiBaseMsg()).getQdyxdp(),
                COLUMNS
        );
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoods(
                SqliteAlbbQdyxSp.class,
                OSSKEY_PATTERN,
                ((Dataprobi1688Msg) getDataprobiBaseMsg()).getQdyx()
        );
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqliteAlbbQdyxDp.class,
                ((Dataprobi1688Msg) getDataprobiBaseMsg()).getQdyxdp(),
                COLUMNS
        );
    }
}
