package com.abujlb.dataprobi.processes.albb;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.albb.*;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import org.springframework.stereotype.Component;

/**
 * 工业品解决方案
 *
 * <AUTHOR>
 * @date 2024/6/12
 */
@Component
@BiPro(order = 5, qd = Qd.ALBB)
public class DefaultBiAlbbGypjjfaDealProcess extends AbstractBiprocess {

    private final String OSSKEY_PATTERN = "bi1688/%s/%s/gypjjfa.json";
    private final String[] COLUMNS = {BiSycmDpDataTsDao.ALBB_COLUMN_GYPJJFA};

    @Override
    public void dealGoods() {
        super.dealGoods(
                SqliteAlbbGypjjfaSp.class,
                OSSKEY_PATTERN,
                ((Dataprobi1688Msg) getDataprobiBaseMsg()).getGypjjfa()
        );
    }

    @Override
    public void dealShop() {
        super.dealShop(
                SqliteAlbbGypjjfaDp.class,
                ((Dataprobi1688Msg) getDataprobiBaseMsg()).getGypjjfadp(),
                COLUMNS
        );
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoods(
                SqliteAlbbGypjjfaSp.class,
                OSSKEY_PATTERN,
                ((Dataprobi1688Msg) getDataprobiBaseMsg()).getGypjjfa()
        );
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqliteAlbbGypjjfaDp.class,
                ((Dataprobi1688Msg) getDataprobiBaseMsg()).getGypjjfadp(),
                COLUMNS
        );
    }
}
