package com.abujlb.dataprobi.processes.albb;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.albb.*;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import org.springframework.stereotype.Component;

/**
 * 消费品解决方案
 *
 * <AUTHOR>
 * @date 2024/7/30
 */
@Component
@BiPro(order = 5, qd = Qd.ALBB)
public class DefaultBiAlbbXfpjjfaDealProcess extends AbstractBiprocess {

    private final String OSSKEY_PATTERN = "bi1688/%s/%s/xfpjjfa.json";
    private final String[] COLUMNS = {BiSycmDpDataTsDao.ALBB_COLUMN_XFPJJFA};

    @Override
    public void dealGoods() {
        super.dealGoods(
                SqliteAlbbXfpjjfaSp.class,
                OSSKEY_PATTERN,
                ((Dataprobi1688Msg) getDataprobiBaseMsg()).getXfpjjfa()
        );
    }

    @Override
    public void dealShop() {
        super.dealShop(
                SqliteAlbbXfpjjfaDp.class,
                ((Dataprobi1688Msg) getDataprobiBaseMsg()).getXfpjjfadp(),
                COLUMNS
        );
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoods(
                SqliteAlbbXfpjjfaSp.class,
                OSSKEY_PATTERN,
                ((Dataprobi1688Msg) getDataprobiBaseMsg()).getXfpjjfa()
        );
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqliteAlbbXfpjjfaDp.class,
                ((Dataprobi1688Msg) getDataprobiBaseMsg()).getXfpjjfadp(),
                COLUMNS
        );
    }
}
