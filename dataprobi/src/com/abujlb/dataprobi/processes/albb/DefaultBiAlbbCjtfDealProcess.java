package com.abujlb.dataprobi.processes.albb;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.albb.*;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import org.springframework.stereotype.Component;

/**
 * 场景投放
 *
 * <AUTHOR>
 * @date 2024/6/12
 */
@Component
@BiPro(order = 3, qd = Qd.ALBB)
public class DefaultBiAlbbCjtfDealProcess extends AbstractBiprocess {

    private final String OSSKEY_PATTERN = "bi1688/%s/%s/cjtf.json";
    private final String[] COLUMNS = {BiSycmDpDataTsDao.ALBB_COLUMN_CJTF};

    @Override
    public void dealGoods() {
        super.dealGoods(
                SqliteAlbbCjtfSp.class,
                OSSKEY_PATTERN,
                ((Dataprobi1688Msg) getDataprobiBaseMsg()).getCjtf()
        );
    }

    @Override
    public void dealShop() {
        super.dealShop(
                SqliteAlbbCjtfDp.class,
                ((Dataprobi1688Msg) getDataprobiBaseMsg()).getCjtfdp(),
                COLUMNS
        );
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoods(
                SqliteAlbbCjtfSp.class,
                OSSKEY_PATTERN,
                ((Dataprobi1688Msg) getDataprobiBaseMsg()).getCjtf()
        );
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqliteAlbbCjtfDp.class,
                ((Dataprobi1688Msg) getDataprobiBaseMsg()).getCjtfdp(),
                COLUMNS
        );
    }
}
