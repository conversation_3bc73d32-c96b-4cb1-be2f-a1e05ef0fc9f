package com.abujlb.dataprobi.processes.albb;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.albb.*;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import org.springframework.stereotype.Component;

/**
 * 超星方案
 *
 * <AUTHOR>
 * @date 2025/5/13
 */
@Component
@BiPro(order = 12, qd = Qd.ALBB)
public class DefaultBiAlbbCxfaDealProcess extends AbstractBiprocess {

    private final String OSSKEY_PATTERN = "bi1688/%s/%s/cxfa.json";
    private final String[] COLUMNS = {BiSycmDpDataTsDao.ALBB_COLUMN_CXFA};

    @Override
    public void dealGoods() {
        super.dealGoods(
                SqliteAlbbCxfaSp.class,
                OSSKEY_PATTERN,
                ((Dataprobi1688Msg) getDataprobiBaseMsg()).getCxfa()
        );
    }

    @Override
    public void dealShop() {
        super.dealShop(
                SqliteAlbbCxfaDp.class,
                ((Dataprobi1688Msg) getDataprobiBaseMsg()).getCxfadp(),
                COLUMNS
        );
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoods(
                SqliteAlbbCxfaSp.class,
                OSSKEY_PATTERN,
                ((Dataprobi1688Msg) getDataprobiBaseMsg()).getCxfa()
        );
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqliteAlbbCxfaDp.class,
                ((Dataprobi1688Msg) getDataprobiBaseMsg()).getCxfadp(),
                COLUMNS
        );
    }
}
