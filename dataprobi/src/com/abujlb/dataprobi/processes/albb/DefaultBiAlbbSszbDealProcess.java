package com.abujlb.dataprobi.processes.albb;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.albb.Dataprobi1688Msg;
import com.abujlb.dataprobi.bean.albb.SqliteAlbbDwtgDp;
import com.abujlb.dataprobi.bean.albb.SqliteAlbbSszbDp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import org.springframework.stereotype.Component;

/**
 * 搜索展播
 *
 * <AUTHOR>
 * @date 2024/6/12
 */
@Component
@BiPro(order = 10, qd = Qd.ALBB)
public class DefaultBiAlbbSszbDealProcess extends AbstractBiprocess {

    private final String[] COLUMNS = {BiSycmDpDataTsDao.ALBB_COLUMN_SSZB};

    @Override
    public void dealShop() {
        super.dealShop(
                SqliteAlbbSszbDp.class,
                ((Dataprobi1688Msg) getDataprobiBaseMsg()).getSszbdp(),
                COLUMNS
        );
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqliteAlbbSszbDp.class,
                ((Dataprobi1688Msg) getDataprobiBaseMsg()).getSszbdp(),
                COLUMNS
        );
    }
}
