package com.abujlb.dataprobi.processes.albb;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.albb.*;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import org.springframework.stereotype.Component;

/**
 * 工厂生意加速计划
 *
 * <AUTHOR>
 * @date 2024/7/24
 */
@Component
@BiPro(order = 5, qd = Qd.ALBB)
public class DefaultBiAlbbGcsyjsjhDealProcess extends AbstractBiprocess {

    private final String OSSKEY_PATTERN = "bi1688/%s/%s/gcsyjsjh.json";
    private final String[] COLUMNS = {BiSycmDpDataTsDao.ALBB_COLUMN_GCSYJSJH};

    @Override
    public void dealGoods() {
        super.dealGoods(
                SqliteAlbbGcsyjsjhSp.class,
                OSSKEY_PATTERN,
                ((Dataprobi1688Msg) getDataprobiBaseMsg()).getGcsyjsjh()
        );
    }

    @Override
    public void dealShop() {
        super.dealShop(
                SqliteAlbbGcsyjsjhDp.class,
                ((Dataprobi1688Msg) getDataprobiBaseMsg()).getGcsyjsjhdp(),
                COLUMNS
        );
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoods(
                SqliteAlbbGcsyjsjhSp.class,
                OSSKEY_PATTERN,
                ((Dataprobi1688Msg) getDataprobiBaseMsg()).getGcsyjsjh()
        );
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqliteAlbbGcsyjsjhDp.class,
                ((Dataprobi1688Msg) getDataprobiBaseMsg()).getGcsyjsjhdp(),
                COLUMNS
        );
    }
}
