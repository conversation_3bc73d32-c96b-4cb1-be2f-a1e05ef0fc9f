package com.abujlb.dataprobi.processes.albb;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.albb.Dataprobi1688Msg;
import com.abujlb.dataprobi.bean.albb.SqliteAlbbMxgcDp;
import com.abujlb.dataprobi.bean.albb.SqliteAlbbPpzqDp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import org.springframework.stereotype.Component;

/**
 * 明星工厂
 *
 * <AUTHOR>
 * @date 2024/6/12
 */
@Component
@BiPro(order = 6, qd = Qd.ALBB)
public class DefaultBiAlbbMxgcDealProcess extends AbstractBiprocess {

    private final String[] COLUMNS = {BiSycmDpDataTsDao.ALBB_COLUMN_MXGC};

    @Override
    public void dealShop() {
        super.dealShop(
                SqliteAlbbMxgcDp.class,
                ((Dataprobi1688Msg) getDataprobiBaseMsg()).getMxgcdp(),
                COLUMNS
        );
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqliteAlbbMxgcDp.class,
                ((Dataprobi1688Msg) getDataprobiBaseMsg()).getMxgcdp(),
                COLUMNS
        );
    }
}
