package com.abujlb.dataprobi.processes.albb;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteDp;
import com.abujlb.dataprobi.bean.albb.Dataprobi1688Msg;
import com.abujlb.dataprobi.bean.albb.SqliteAlbbYxhbDp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.util.MathUtil;
import com.abujlb.util.StringUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * 营销红包
 *
 * <AUTHOR>
 * @date 2024/4/24
 */
@Component
@BiPro(order = 10, qd = Qd.ALBB)
public class DefaultBiAlbbYxhbDealProcess extends AbstractBiprocess {

    private final String OSSKEY_PATTERN = "bi1688/%s/%s/yxhb.json";
    private final String MEMO = "红包";
    private final String MEMO2 = "现金";
    private final List<String> YXB_HB = Collections.singletonList("营效宝消耗");
    private final List<String> YXJJFA_HB = Collections.singletonList("定制营销解决方案");
    private final List<String> QZTF_HB = Collections.singletonList("全站投放");
    private final List<String> PPFA_HB = Collections.singletonList("品牌套餐-长期霸屏");

    @Override
    public void dealShop() {
        super.dealShop(
                SqliteAlbbYxhbDp.class,
                ((Dataprobi1688Msg) getDataprobiBaseMsg()).getYxhb(),
                (String) null
        );
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqliteAlbbYxhbDp.class,
                ((Dataprobi1688Msg) getDataprobiBaseMsg()).getYxhb(),
                (String) null
        );
    }

    @Override
    protected <T extends AbstractSqliteDp> T dpObject(Class<T> tClass, String rq, String... colummns) {
        String osskey = String.format(OSSKEY_PATTERN, getDataprobiBaseMsg().getUserid(), rq);
        String json = biDataOssUtil.readJson(osskey);
        if (!StringUtil.isJsonArray2(json)) {
            return null;
        }


        SqliteAlbbYxhbDp sqliteAlbbYxhbDp = new SqliteAlbbYxhbDp();
        sqliteAlbbYxhbDp.setQd(getDataprobiBaseMsg().getQd());
        sqliteAlbbYxhbDp.setQd_userid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid());
        sqliteAlbbYxhbDp.setRq(rq);
        sqliteAlbbYxhbDp.setYhid(getDataprobiBaseMsg().getYhid());
        sqliteAlbbYxhbDp.setUserid(getDataprobiBaseMsg().getUserid());

        JSONArray jsonArray = JSONArray.parseArray(json);
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            String memo = jsonObject.getString("memo");
            String tradeTypeName = jsonObject.getString("tradeTypeName");
            if (MEMO.equals(memo)) {
                if (YXB_HB.contains(tradeTypeName)) {
                    sqliteAlbbYxhbDp.setYxbhb(MathUtil.add(sqliteAlbbYxhbDp.getYxbhb(), jsonObject.getDoubleValue("tradeAMT")));
                }
                if (YXJJFA_HB.contains(tradeTypeName)) {
                    sqliteAlbbYxhbDp.setYxjjfahb(MathUtil.add(sqliteAlbbYxhbDp.getYxjjfahb(), jsonObject.getDoubleValue("tradeAMT")));
                }
                if (QZTF_HB.contains(tradeTypeName)) {
                    sqliteAlbbYxhbDp.setQztfhb(MathUtil.add(sqliteAlbbYxhbDp.getQztfhb(), jsonObject.getDoubleValue("tradeAMT")));
                }
                //2024-10-25 按照陈达的约定，收入项就提供负数，支出项就提供正数
                if (PPFA_HB.contains(tradeTypeName)) {
                    sqliteAlbbYxhbDp.setPpfahb(MathUtil.add(sqliteAlbbYxhbDp.getPpfahb(), -jsonObject.getDoubleValue("tradeAMT")));
                }
                //合并所有红包
                sqliteAlbbYxhbDp.setSzyxdkhb(MathUtil.add(sqliteAlbbYxhbDp.getSzyxdkhb(), -jsonObject.getDoubleValue("tradeAMT")));
            }
//            if (getDataprobiBaseMsg().getDzyxjjfa() == 0) {
//                //红包+现金
//                if (YXJJFA_HB.contains(tradeTypeName)) {
//                    sqliteAlbbYxhbDp.setYxjjfahf(MathUtil.add(sqliteAlbbYxhbDp.getYxjjfahf(), jsonObject.getDoubleValue("tradeAMT")));
//                }
//            }
        }

        try {
            if (sqliteAlbbYxhbDp.getYxjjfahb() == 0 && sqliteAlbbYxhbDp.getYxbhb() == 0 && sqliteAlbbYxhbDp.getQztfhb() == 0
                    && sqliteAlbbYxhbDp.getPpfahb() == 0 && sqliteAlbbYxhbDp.getSzyxdkhb() == 0) {
                return null;
            }

            return (T) sqliteAlbbYxhbDp;
        } finally {
            jsonArray.clear();
        }
    }
}
