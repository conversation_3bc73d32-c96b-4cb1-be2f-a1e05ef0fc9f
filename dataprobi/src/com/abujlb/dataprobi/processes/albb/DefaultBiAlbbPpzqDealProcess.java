package com.abujlb.dataprobi.processes.albb;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.albb.Dataprobi1688Msg;
import com.abujlb.dataprobi.bean.albb.SqliteAlbbPpzqDp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import org.springframework.stereotype.Component;

/**
 * 品牌专区
 *
 * <AUTHOR>
 * @date 2024/6/12
 */
@Component
@BiPro(order = 7, qd = Qd.ALBB)
public class DefaultBiAlbbPpzqDealProcess extends AbstractBiprocess {

    private final String[] COLUMNS = {BiSycmDpDataTsDao.ALBB_COLUMN_PPZQ};

    @Override
    public void dealShop() {
        super.dealShop(
                SqliteAlbbPpzqDp.class,
                ((Dataprobi1688Msg) getDataprobiBaseMsg()).getPpzqdp(),
                COLUMNS
        );
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqliteAlbbPpzqDp.class,
                ((Dataprobi1688Msg) getDataprobiBaseMsg()).getPpzqdp(),
                COLUMNS
        );
    }
}
