package com.abujlb.dataprobi.processes.albb;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.albb.Dataprobi1688Msg;
import com.abujlb.dataprobi.bean.albb.SqliteAlbbSpxgDp;
import com.abujlb.dataprobi.bean.albb.SqliteAlbbSpxgSp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import org.springframework.stereotype.Component;

/**
 * 解决方案
 *
 * <AUTHOR>
 * @date 2023/8/10
 */
@Component
@BiPro(order = 2, qd = Qd.ALBB)
public class DefaultBiAlbbSpxgDealProcess extends AbstractBiprocess {

    private final String OSSKEY_PATTERN = "bi1688/%s/%s/spxg.json";
    private final String[] COLUMNS = {BiSycmDpDataTsDao.ALBB_COLUMN_SPXG1, BiSycmDpDataTsDao.ALBB_COLUMN_SPXG2};

    @Override
    public void dealGoods() {
        super.dealGoods(
                SqliteAlbbSpxgSp.class,
                OSSKEY_PATTERN,
                ((Dataprobi1688Msg) getDataprobiBaseMsg()).getSpxg()
        );
    }

    @Override
    public void dealShop() {
        super.dealShop(
                SqliteAlbbSpxgDp.class,
                ((Dataprobi1688Msg) getDataprobiBaseMsg()).getSpxgdp(),
                COLUMNS
        );
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoods(
                SqliteAlbbSpxgSp.class,
                OSSKEY_PATTERN,
                ((Dataprobi1688Msg) getDataprobiBaseMsg()).getSpxg()
        );
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqliteAlbbSpxgDp.class,
                ((Dataprobi1688Msg) getDataprobiBaseMsg()).getSpxgdp(),
                COLUMNS
        );
    }
}
