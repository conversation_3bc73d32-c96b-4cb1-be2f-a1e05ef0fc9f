package com.abujlb.dataprobi.processes.albb;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.albb.*;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import org.springframework.stereotype.Component;

/**
 * 全站销品
 *
 * <AUTHOR>
 * @date 2024/6/12
 */
@Component
@BiPro(order = 9, qd = Qd.ALBB)
public class DefaultBiAlbbQzxpDealProcess extends AbstractBiprocess {

    private final String OSSKEY_PATTERN = "bi1688/%s/%s/qzxp.json";
    private final String[] COLUMNS = {BiSycmDpDataTsDao.ALBB_COLUMN_QZXP};

    @Override
    public void dealGoods() {
        super.dealGoods(
                SqliteAlbbQzxpSp.class,
                OSSKEY_PATTERN,
                ((Dataprobi1688Msg) getDataprobiBaseMsg()).getQzxp()
        );
    }

    @Override
    public void dealShop() {
        super.dealShop(
                SqliteAlbbQzxpDp.class,
                ((Dataprobi1688Msg) getDataprobiBaseMsg()).getQzxpdp(),
                COLUMNS
        );
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoods(
                SqliteAlbbQzxpSp.class,
                OSSKEY_PATTERN,
                ((Dataprobi1688Msg) getDataprobiBaseMsg()).getQzxp()
        );
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqliteAlbbQzxpDp.class,
                ((Dataprobi1688Msg) getDataprobiBaseMsg()).getQzxpdp(),
                COLUMNS
        );
    }
}
