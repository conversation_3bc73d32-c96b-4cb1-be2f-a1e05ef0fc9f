package com.abujlb.dataprobi.processes.albb;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.albb.*;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import org.springframework.stereotype.Component;

/**
 * SKA驾驶舱
 *
 * <AUTHOR>
 * @date 2024/7/15
 */
@Component
@BiPro(order = 9, qd = Qd.ALBB)
public class DefaultBiAlbbSkajscDealProcess extends AbstractBiprocess {

    private final String OSSKEY_PATTERN = "bi1688/%s/%s/skajsc.json";
    private final String[] COLUMNS = {BiSycmDpDataTsDao.ALBB_COLUMN_SKAJSC};

    @Override
    public void dealGoods() {
        super.dealGoods(
                SqliteAlbbSkajscSp.class,
                OSSKEY_PATTERN,
                ((Dataprobi1688Msg) getDataprobiBaseMsg()).getSkajsc()
        );
    }

    @Override
    public void dealShop() {
        super.dealShop(
                SqliteAlbbSkajscDp.class,
                ((Dataprobi1688Msg) getDataprobiBaseMsg()).getSkajscdp(),
                COLUMNS
        );
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoods(
                SqliteAlbbSkajscSp.class,
                OSSKEY_PATTERN,
                ((Dataprobi1688Msg) getDataprobiBaseMsg()).getSkajsc()
        );
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqliteAlbbSkajscDp.class,
                ((Dataprobi1688Msg) getDataprobiBaseMsg()).getSkajscdp(),
                COLUMNS
        );
    }
}
