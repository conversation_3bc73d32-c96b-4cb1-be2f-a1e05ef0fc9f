package com.abujlb.dataprobi.processes.ymx;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.DataprobiBaseMsgBean;
import com.abujlb.dataprobi.bean.ymx.DataprobiYmxMsg;
import com.abujlb.dataprobi.bean.ymx.SqliteYmxSpTgsjSp;
import com.abujlb.dataprobi.bean.ymx.SqliteYmxSptgsjDp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.interfaces.Plusable;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.threads.BiThreadLocals;
import com.abujlb.dataprobi.util.MathUtil;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @packageName com.abujlb.dataprobi.processes.ymx
 * @ClassName DefaultBiYmxSptgProcess
 * @Description 亚马逊 广告推广花费
 * <AUTHOR>
 * @Date 2025/7/1
 */
@Component
@BiPro(order = 1, qd = Qd.YMX)
public class DefaultBiYmxSptgProcess extends AbstractBiprocess {
    //json 数据路径
    public static final String OSSKEY_PATTERN = "bi_ymx/%s/%s/sptgsj.json";

    @Override
    public void dealGoods() {
        super.dealGoods(
                SqliteYmxSpTgsjSp.class,
                OSSKEY_PATTERN,
                ((DataprobiYmxMsg) getDataprobiBaseMsg()).getSptg()
        );
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoods(
                SqliteYmxSpTgsjSp.class,
                OSSKEY_PATTERN,
                ((DataprobiYmxMsg) getDataprobiBaseMsg()).getSptg()
        );
    }

    public <T extends AbstractSqliteSp> List<T> spList(Class<T> tClass, String ossKey, String rq) {
        try {
            if (biDataOssUtil.exist((ossKey))) {
                String json = biDataOssUtil.readJson(ossKey);
                if (StringUtils.isBlank(json)) {
                    return Collections.emptyList();
                }
                List<JSONObject> list1 = JSONUtil.toList(json, JSONObject.class);
                if (CollUtil.isEmpty(list1)) {
                    return Collections.emptyList();
                }
                //过滤出 日期对应的数据
                List<JSONObject> data = list1.stream().filter(e -> StrUtil.equals(rq, e.getString("date"))).collect(Collectors.toList());
                DataprobiBaseMsgBean msgBean = BiThreadLocals.getMsgBean();
                List<T> list = new ArrayList<>(data.size());
                for (int i = 0; i < data.size(); i++) {
                    JSONObject temp = data.get(i);
                    T t = tClass.newInstance();
                    t.setValues(temp);
                    t.setQd_userid_bbid(msgBean.getQd() + "_" + msgBean.getUserid() + "_" + t.getBbid());
                    t.setRq(rq);
                    t.setYhid(msgBean.getYhid());
                    t.setQd(msgBean.getQd());
                    t.setUserid(msgBean.getUserid());
                    list.add(t);
                }
                Class<?>[] interfaces = tClass.getInterfaces();
                boolean plus = false;
                for (Class<?> anInterface : interfaces) {
                    if (anInterface == Plusable.class) {
                        plus = true;
                        break;
                    }
                }
                if (!plus) {
                    return list;
                }
                Map<String, T> map = new HashMap<>();
                for (T t : list) {
                    T temp = null;
                    if (map.containsKey(t.getBbid())) {
                        temp = map.get(t.getBbid());
                    } else {
                        temp = tClass.newInstance();
                        temp.setUserid(t.getUserid());
                        temp.setYhid(t.getYhid());
                        temp.setQd(t.getQd());
                        temp.setQd_userid_bbid(t.getQd_userid_bbid());
                        temp.setBbid(t.getBbid());
                    }
                    tClass.getMethod("plus", tClass).invoke(temp, t);
                    map.put(t.getBbid(), temp);
                }
                //计算 roas、costPerClick、clickThroughRate、acos、zhl等
                for (Map.Entry<String, T> entry : map.entrySet()) {
                    SqliteYmxSpTgsjSp value = (SqliteYmxSpTgsjSp) entry.getValue();
                    //TODO  待确定 销售额  用哪个字段  sales1d 和页面对不上
                    value.setRoas(MathUtil.divide(value.getSales7d(), value.getGgtghf()));
                    value.setAcos(MathUtil.divide(value.getGgtghf(), value.getSales7d()));
                    //TODO  待确定 订单数数  用哪个字段  purchases1d 和页面对不上
                    value.setZhl(MathUtil.divide(value.getPurchases7d(), value.getClicks()));
                    value.setCostPerClick(MathUtil.divide(value.getGgtghf(), value.getClicks()));
                    value.setClickThroughRate(MathUtil.divide(value.getClicks(), value.getImpressions()));
                }
                return new ArrayList<>(map.values());
            } else {
                return Collections.emptyList();
            }
        } catch (Exception e) {
            logger.error("亚马逊商品推广数据处理失败！", e);
            return Collections.emptyList();
        }
    }

    @Override
    protected <T extends AbstractSqliteSp> void dealShopByGoods(String rq, List<T> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        double attributedSalesSameSku1d = 0;
        double attributedSalesSameSku14d = 0;
        double sales7d = 0;
        double sales1d = 0;
        double attributedSalesSameSku30d = 0;
        double attributedSalesSameSku7d = 0;
        double spend = 0;
        double ggtghf = 0;
        double sales14d = 0;
        double sales30d = 0;
        int unitsSoldClicks1d = 0;
        int kindleEditionNormalizedPagesRoyalties14d = 0;
        int unitsSoldSameSku1d = 0;
        int salesOtherSku7d = 0;
        int purchasesSameSku7d = 0;
        int campaignBudgetAmount = 0;
        int purchases7d = 0;
        int unitsSoldSameSku30d = 0;
        int unitsSoldClicks14d = 0;
        int kindleEditionNormalizedPagesRead14d = 0;
        int unitsSoldClicks30d = 0;
        int qualifiedBorrows = 0;
        int unitsSoldSameSku14d = 0;
        int unitsSoldClicks7d = 0;
        int royaltyQualifiedBorrows = 0;
        int addToList = 0;
        int purchasesSameSku14d = 0;
        int unitsSoldOtherSku7d = 0;
        int purchasesSameSku1d = 0;
        int purchases1d = 0;
        int unitsSoldSameSku7d = 0;
        int impressions = 0;
        int purchasesSameSku30d = 0;
        int purchases14d = 0;
        int purchases30d = 0;
        int clicks = 0;
        for (T t : list) {
            if (t instanceof SqliteYmxSpTgsjSp) {
                SqliteYmxSpTgsjSp obj = (SqliteYmxSpTgsjSp) t;
                attributedSalesSameSku1d = MathUtil.add(attributedSalesSameSku1d, obj.getAttributedSalesSameSku1d());
                attributedSalesSameSku14d = MathUtil.add(attributedSalesSameSku14d, obj.getAttributedSalesSameSku14d());
                sales1d = MathUtil.add(sales1d, obj.getSales1d());
                sales7d = MathUtil.add(sales7d, obj.getSales7d());
                attributedSalesSameSku30d = MathUtil.add(attributedSalesSameSku30d, obj.getAttributedSalesSameSku30d());
                attributedSalesSameSku7d = MathUtil.add(attributedSalesSameSku7d, obj.getAttributedSalesSameSku7d());
                spend = MathUtil.add(spend, obj.getSpend());
                ggtghf = MathUtil.add(ggtghf, obj.getGgtghf());
                sales14d = MathUtil.add(sales14d, obj.getSales14d());
                sales30d = MathUtil.add(sales30d, obj.getSales30d());
                unitsSoldClicks1d = unitsSoldClicks1d + obj.getUnitsSoldClicks1d();
                kindleEditionNormalizedPagesRoyalties14d = kindleEditionNormalizedPagesRoyalties14d + obj.getKindleEditionNormalizedPagesRoyalties14d();
                unitsSoldSameSku1d = unitsSoldSameSku1d + obj.getUnitsSoldSameSku1d();
                salesOtherSku7d = salesOtherSku7d + obj.getSalesOtherSku7d();
                purchasesSameSku7d = purchasesSameSku7d + obj.getPurchasesSameSku7d();
                campaignBudgetAmount = campaignBudgetAmount + obj.getCampaignBudgetAmount();
                purchases7d = purchases7d + obj.getPurchases7d();
                unitsSoldSameSku30d = unitsSoldSameSku30d + obj.getUnitsSoldSameSku30d();
                unitsSoldClicks14d = unitsSoldClicks14d + obj.getUnitsSoldClicks14d();
                kindleEditionNormalizedPagesRead14d = kindleEditionNormalizedPagesRead14d + obj.getKindleEditionNormalizedPagesRead14d();
                unitsSoldClicks30d = unitsSoldClicks30d + obj.getUnitsSoldClicks30d();
                qualifiedBorrows = qualifiedBorrows + obj.getQualifiedBorrows();
                unitsSoldSameSku14d = unitsSoldSameSku14d + obj.getUnitsSoldSameSku14d();
                unitsSoldClicks7d = unitsSoldClicks7d + obj.getUnitsSoldClicks7d();
                royaltyQualifiedBorrows = royaltyQualifiedBorrows + obj.getRoyaltyQualifiedBorrows();
                addToList = addToList + obj.getAddToList();
                purchasesSameSku14d = purchasesSameSku14d + obj.getPurchasesSameSku14d();
                unitsSoldOtherSku7d = unitsSoldOtherSku7d + obj.getUnitsSoldOtherSku7d();
                purchasesSameSku1d = purchasesSameSku1d + obj.getPurchasesSameSku1d();
                purchases1d = purchases1d + obj.getPurchases1d();
                unitsSoldSameSku7d = unitsSoldSameSku7d + obj.getUnitsSoldSameSku7d();
                impressions = impressions + obj.getImpressions();
                purchasesSameSku30d = purchasesSameSku30d + obj.getPurchasesSameSku30d();
                purchases14d = purchases14d + obj.getPurchases14d();
                purchases30d = purchases30d + obj.getPurchases30d();
                clicks = clicks + obj.getClicks();

            }
        }
        SqliteYmxSptgsjDp sqliteYmxSptgsjDp = new SqliteYmxSptgsjDp();
        DataprobiBaseMsgBean dataprobiBaseMsg = getDataprobiBaseMsg();
        sqliteYmxSptgsjDp.setQd_userid(dataprobiBaseMsg.getQd() + "_" + dataprobiBaseMsg.getUserid());
        sqliteYmxSptgsjDp.setRq(rq);
        sqliteYmxSptgsjDp.setDpmc(dataprobiBaseMsg.getDpmc());
        sqliteYmxSptgsjDp.setUserid(dataprobiBaseMsg.getUserid());
        sqliteYmxSptgsjDp.setYhid(dataprobiBaseMsg.getYhid());
        sqliteYmxSptgsjDp.setQd(dataprobiBaseMsg.getQd());
        sqliteYmxSptgsjDp.setUnitsSoldClicks1d(unitsSoldClicks1d);
        sqliteYmxSptgsjDp.setKindleEditionNormalizedPagesRoyalties14d(kindleEditionNormalizedPagesRoyalties14d);
        sqliteYmxSptgsjDp.setUnitsSoldSameSku1d(unitsSoldSameSku1d);
        sqliteYmxSptgsjDp.setSalesOtherSku7d(salesOtherSku7d);
        sqliteYmxSptgsjDp.setPurchasesSameSku7d(purchasesSameSku7d);
        sqliteYmxSptgsjDp.setCampaignBudgetAmount(campaignBudgetAmount);
        sqliteYmxSptgsjDp.setPurchases7d(purchases7d);
        sqliteYmxSptgsjDp.setUnitsSoldSameSku30d(unitsSoldSameSku30d);
        sqliteYmxSptgsjDp.setUnitsSoldClicks14d(unitsSoldClicks14d);
        sqliteYmxSptgsjDp.setKindleEditionNormalizedPagesRead14d(kindleEditionNormalizedPagesRead14d);
        sqliteYmxSptgsjDp.setUnitsSoldClicks30d(unitsSoldClicks30d);
        sqliteYmxSptgsjDp.setQualifiedBorrows(qualifiedBorrows);
        sqliteYmxSptgsjDp.setUnitsSoldSameSku14d(unitsSoldSameSku14d);
        sqliteYmxSptgsjDp.setUnitsSoldClicks7d(unitsSoldClicks7d);
        sqliteYmxSptgsjDp.setRoyaltyQualifiedBorrows(royaltyQualifiedBorrows);
        sqliteYmxSptgsjDp.setAddToList(addToList);
        sqliteYmxSptgsjDp.setPurchasesSameSku14d(purchasesSameSku14d);
        sqliteYmxSptgsjDp.setUnitsSoldOtherSku7d(unitsSoldOtherSku7d);
        sqliteYmxSptgsjDp.setPurchasesSameSku1d(purchasesSameSku1d);
        sqliteYmxSptgsjDp.setPurchases1d(purchases1d);
        sqliteYmxSptgsjDp.setUnitsSoldSameSku7d(unitsSoldSameSku7d);
        sqliteYmxSptgsjDp.setImpressions(impressions);
        sqliteYmxSptgsjDp.setPurchasesSameSku30d(purchasesSameSku30d);
        sqliteYmxSptgsjDp.setPurchases14d(purchases14d);
        sqliteYmxSptgsjDp.setPurchases30d(purchases30d);
        sqliteYmxSptgsjDp.setClicks(clicks);
        sqliteYmxSptgsjDp.setAttributedSalesSameSku1d(attributedSalesSameSku1d);
        sqliteYmxSptgsjDp.setAttributedSalesSameSku14d(attributedSalesSameSku14d);
        sqliteYmxSptgsjDp.setSales1d(sales1d);
        sqliteYmxSptgsjDp.setSales7d(sales7d);
        sqliteYmxSptgsjDp.setAttributedSalesSameSku30d(attributedSalesSameSku30d);
        sqliteYmxSptgsjDp.setAttributedSalesSameSku7d(attributedSalesSameSku7d);
        sqliteYmxSptgsjDp.setSpend(spend);
        sqliteYmxSptgsjDp.setGgtghf(ggtghf);
        sqliteYmxSptgsjDp.setSales14d(sales14d);
        sqliteYmxSptgsjDp.setSales30d(sales30d);
        sqliteYmxSptgsjDp.setRoas(MathUtil.divide(sqliteYmxSptgsjDp.getSales7d(), sqliteYmxSptgsjDp.getGgtghf()));
        sqliteYmxSptgsjDp.setAcos(MathUtil.divide(sqliteYmxSptgsjDp.getGgtghf(), sqliteYmxSptgsjDp.getSales7d()));
        sqliteYmxSptgsjDp.setZhl(MathUtil.divide(sqliteYmxSptgsjDp.getPurchases7d(), sqliteYmxSptgsjDp.getClicks()));
        sqliteYmxSptgsjDp.setCostPerClick(MathUtil.divide(sqliteYmxSptgsjDp.getGgtghf(), sqliteYmxSptgsjDp.getClicks()));
        sqliteYmxSptgsjDp.setClickThroughRate(MathUtil.divide(sqliteYmxSptgsjDp.getClicks(), sqliteYmxSptgsjDp.getImpressions()));
        processSycmDpOTS(rq, sqliteYmxSptgsjDp);
    }


}

