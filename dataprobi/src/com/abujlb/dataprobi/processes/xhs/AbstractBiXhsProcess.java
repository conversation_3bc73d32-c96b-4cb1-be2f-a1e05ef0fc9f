package com.abujlb.dataprobi.processes.xhs;

import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.DataprobiBaseMsgBean;
import com.abujlb.dataprobi.interfaces.Plusable;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2024/8/08
 */
@Component
public class AbstractBiXhsProcess extends AbstractBiprocess {

    private static final Logger logger = Logger.getLogger(AbstractBiXhsProcess.class);

    protected <T extends AbstractSqliteSp> List<T> spList(Class<T> tClass, String ossKey, String rq) {
        try {
            // 获取指定目录下的所有JSON文件
            List<String> jsonFiles = biDataOssUtil.listFiles(ossKey);
            if (CollectionUtils.isEmpty(jsonFiles)) {
                return Collections.emptyList();
            }

            // 合并所有JSON文件的内容
            JSONArray mergedJsonArray = new JSONArray();
            for (String jsonFile : jsonFiles) {
                if (!jsonFile.endsWith(".json")) {
                    continue;
                }
                String json = biDataOssUtil.readJson(jsonFile);
                if (StringUtils.isBlank(json)) {
                    continue;
                }
                JSONArray fileJsonArray = JSONArray.parseArray(json);
                if (CollectionUtils.isNotEmpty(fileJsonArray)) {
                    mergedJsonArray.addAll(fileJsonArray);
                }
            }

            if (CollectionUtils.isEmpty(mergedJsonArray)) {
                return Collections.emptyList();
            }

            // 转换数据
            DataprobiBaseMsgBean msgBean = getDataprobiBaseMsg();
            List<T> list = new ArrayList<>(mergedJsonArray.size());
            for (int i = 0; i < mergedJsonArray.size(); i++) {
                JSONObject temp = mergedJsonArray.getJSONObject(i);
                T t = tClass.newInstance();
                t.setValues(temp);
                t.setQd_userid_bbid(msgBean.getQd() + "_" + msgBean.getUserid() + "_" + t.getBbid());
                t.setRq(rq);
                t.setYhid(msgBean.getYhid());
                t.setQd(msgBean.getQd());
                t.setUserid(msgBean.getUserid());
                list.add(t);
            }

            // 检查是否需要合并数据（Plusable接口）
            Class<?>[] interfaces = tClass.getInterfaces();
            boolean plus = false;
            for (Class<?> anInterface : interfaces) {
                if (anInterface == Plusable.class) {
                    plus = true;
                    break;
                }
            }

            if (!plus) {
                return list;
            }

            // 合并数据
            Map<String, T> map = new HashMap<>();
            for (T t : list) {
                T temp = null;
                if (map.containsKey(t.getBbid())) {
                    temp = map.get(t.getBbid());
                } else {
                    temp = tClass.newInstance();
                    temp.setUserid(t.getUserid());
                    temp.setYhid(t.getYhid());
                    temp.setQd(t.getQd());
                    temp.setQd_userid_bbid(t.getQd_userid_bbid());
                    temp.setBbid(t.getBbid());
                }
                tClass.getMethod("plus", tClass).invoke(temp, t);
                map.put(t.getBbid(), temp);
            }

            return new ArrayList<>(map.values());

        } catch (Exception e) {
            logger.error("处理JSON文件失败: " + ossKey, e);
            return Collections.emptyList();
        }
    }

    /**
     * 处理SPU数据，合并文件夹下所有JSON文件
     *
     * @param tClass 数据类型
     * @param ossDirectoryPrefix 文件夹路径前缀，如 "bixhs/userid/date/cfzbtg/"
     * @param rqList 日期列表
     */
    protected <T extends AbstractSqliteSp> void dealSpu(Class<T> tClass, String ossDirectoryPrefix, List<String> rqList) {
        for (String rq : rqList) {
            // 构建目录路径
            String ossDirectory = String.format(ossDirectoryPrefix, getDataprobiBaseMsg().getUserid(), rq);
            //System.out.println("处理目录: " + ossDirectory);

            try {
                // 直接将整个目录传递给 spList 方法处理
                // spList 方法会获取目录下所有JSON文件并合并处理
                List<T> mergedList = spList(tClass, ossDirectory, rq);

                if (mergedList == null || mergedList.isEmpty()) {
                    continue;
                }

                // 处理合并后的数据
                processSycmSpuOTS(rq, mergedList);

                // 处理店铺商品数据
                dealShopByGoods(rq, mergedList);

            } catch (Exception e) {
                //System.err.println("处理目录 " + ossDirectory + " 时出错: " + e.getMessage());
                e.printStackTrace();
            }
        }
    }

    protected <T extends AbstractSqliteSp> boolean compareSpu(Class<T> tClass, String ossKeyFormat, List<String> rqList) {
        if (CollectionUtils.isEmpty(rqList)) {
            return true;
        }
        dealSpu(tClass, ossKeyFormat, rqList);
        return false;
    }
}
