package com.abujlb.dataprobi.processes.xhs;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.xhs.*;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import com.abujlb.dataprobi.util.MathUtil;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * 千帆直播营销
 */
@Component
@BiPro(order = 2, qd = Qd.XHS)
public class DefaultbiXhsQfzbyxProcess extends AbstractBiprocess {

    private final String[] COLUMNS = {BiSycmDpDataTsDao.XHS_COLUMN_QFZBDP};


    //暂无数据
    @Override
    public void dealShop() {
        super.dealShop(
                SqliteXhsQfzbDp.class,
                ((DataprobiXhsMsg) getDataprobiBaseMsg()).getQfzbyxdp(),
                COLUMNS
        );
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqliteXhsQfzbDp.class,
                ((DataprobiXhsMsg) getDataprobiBaseMsg()).getQfzbyxdp(),
                COLUMNS
        );
    }

}
