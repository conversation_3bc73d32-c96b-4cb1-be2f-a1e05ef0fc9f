package com.abujlb.dataprobi.processes.xhs;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.xhs.DataprobiXhsMsg;
import com.abujlb.dataprobi.bean.xhs.SqliteXhsCfsptgDp;
import com.abujlb.dataprobi.bean.xhs.SqliteXhsCfsptgSp;
import com.abujlb.dataprobi.enums.Qd;
import org.springframework.stereotype.Component;


/**
 * 乘风平台推广花费（商品）
 */
@Component
@BiPro(order = 7, qd = Qd.XHS)
public class DefaultbiXhsCfspTgProcess extends AbstractBiXhsProcess {

    public static final String OSSKEY_PATTERN_SP = "bixhs/%s/%s/cfsptg/";
    public static final String OSSKEY_PATTERN_DP = "bixhs/%s/%s/cfsptgdp/";

    @Override
    public void dealGoods() {
        super.dealSpu(
                SqliteXhsCfsptgSp.class,
                OSSKEY_PATTERN_SP,
                ((DataprobiXhsMsg) getDataprobiBaseMsg()).getCfsptg()
        );
    }


    @Override
    public void dealShop() {
        super.dealShop(
                SqliteXhsCfsptgDp.class,
                OSSKEY_PATTERN_DP,
                ((DataprobiXhsMsg) getDataprobiBaseMsg()).getCfsptgdp()
        );
    }


    @Override
    public boolean compareGoods() {
        return super.compareSpu(
                SqliteXhsCfsptgSp.class,
                OSSKEY_PATTERN_SP,
                ((DataprobiXhsMsg) getDataprobiBaseMsg()).getCfsptg()
        );
    }


    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqliteXhsCfsptgDp.class,
                OSSKEY_PATTERN_DP,
                ((DataprobiXhsMsg) getDataprobiBaseMsg()).getCfsptgdp()
        );
    }


}
