package com.abujlb.dataprobi.processes.xhs;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.xhs.*;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * 聚光平台笔记推广
 */
@Component
@BiPro(order = 6, qd = Qd.XHS)
public class DefaultbiXhsJgptBitgProcess extends AbstractBiXhsProcess {

    public static final String OSSKEY_PATTERN_SP = "bixhs/%s/%s/jgptbj/";
    public static final String OSSKEY_PATTERN_DP = "bixhs/%s/%s/jgptbjdp/";

    @Override
    public void dealGoods() {
        super.dealSpu(
                SqliteXhsJgptbjSp.class,
                OSSKEY_PATTERN_SP,
                ((DataprobiXhsMsg) getDataprobiBaseMsg()).getJgptbjtg()
        );
    }

    @Override
    public void dealShop() {
        super.dealShop(
                SqliteXhsJgptbjDp.class,
                OSSKEY_PATTERN_DP,
                ((DataprobiXhsMsg) getDataprobiBaseMsg()).getJgptbjtgdp()
        );
    }
//    @Override
//    public void dealShop() {
//        List<String> rqList = ((DataprobiXhsMsg) getDataprobiBaseMsg()).getJgptbjtgdp();
//
//        for (String rq : rqList) {
//            try {
//                // 构建OSS文件夹路径
//                String ossFolderPath = String.format(OSSKEY_PATTERN_DP, getDataprobiBaseMsg().getUserid(), rq);
//
//                // 获取文件夹下所有JSON文件
//                List<String> jsonFiles = biDataOssUtil.listFiles(ossFolderPath);
//                if (CollectionUtils.isEmpty(jsonFiles)) {
//                    continue;
//                }
//
//                // 计算所有文件的jgbjhf总和
//                double totalJgbjhf = 0.0;
//                for (String jsonFile : jsonFiles) {
//                    if (!jsonFile.endsWith(".json")) {
//                        continue;
//                    }
//
//                    String json = biDataOssUtil.readJson(jsonFile);
//                    if (StringUtils.hasText(json)) {
//                        JSONObject jsonObject = JSONObject.parseObject(json);
//                        if (jsonObject != null) {
//                            // 使用FastJSONObjAttrToNumber工具类转换数值
//                            double fileJgbjhf = FastJSONObjAttrToNumber.toDouble(jsonObject, "jgbjzxf");
//                            totalJgbjhf += fileJgbjhf;
//                        }
//                    }
//                }
//
//                // 创建并设置SqliteXhsJgptbjDp对象
//                SqliteXhsJgptbjDp dp = new SqliteXhsJgptbjDp();
//                dp.setQd(getDataprobiBaseMsg().getQd());
//                dp.setQd_userid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid());
//                dp.setRq(rq);
//                dp.setYhid(getDataprobiBaseMsg().getYhid());
//                dp.setUserid(getDataprobiBaseMsg().getUserid());
//                dp.setJgbjhf(totalJgbjhf);
//
//                // 处理数据
//                processSycmDpOTS(rq, dp);
//
//            } catch (Exception e) {
//                logger.error("处理店铺数据失败: " + rq, e);
//            }
//        }
//    }


    @Override
    public boolean compareGoods() {
        return super.compareSpu(
                SqliteXhsJgptbjSp.class,
                OSSKEY_PATTERN_SP,
                ((DataprobiXhsMsg) getDataprobiBaseMsg()).getJgptbjtgdp()
        );
    }


//    @Override
//    protected <T extends AbstractSqliteSp> void dealShopByGoods(String rq, List<T> list) {
//         //店铺汇总
//        double jgbjhf = 0 ;
//        for (T t : list) {
//            if (t instanceof SqliteXhsJgptbjSp) {
//                jgbjhf = MathUtil.add(jgbjhf, ((SqliteXhsJgptbjSp) t).getJgbjhf());
//            }
//        }
//        if (jgbjhf == 0) {
//            return;
//        }
//
//        SqliteXhsJgptbjDp sqliteXhsJgptbjDp = new SqliteXhsJgptbjDp();
//        sqliteXhsJgptbjDp.setQd(getDataprobiBaseMsg().getQd());
//        sqliteXhsJgptbjDp.setQd_userid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid());
//        sqliteXhsJgptbjDp.setRq(rq);
//        sqliteXhsJgptbjDp.setYhid(getDataprobiBaseMsg().getYhid());
//        sqliteXhsJgptbjDp.setUserid(getDataprobiBaseMsg().getUserid());
//        sqliteXhsJgptbjDp.setJgbjhf(jgbjhf);
//        processSycmDpOTS(rq, sqliteXhsJgptbjDp);
//    }
        @Override
        public boolean compareShop() {
            return super.compareShop(
                    SqliteXhsJgptbjDp.class,
                    OSSKEY_PATTERN_DP,
                    ((DataprobiXhsMsg) getDataprobiBaseMsg()).getJgptbjtgdp()
            );
        }
}
