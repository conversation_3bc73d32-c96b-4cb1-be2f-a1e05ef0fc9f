package com.abujlb.dataprobi.processes.xhs;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.tgc.DataprobiTgcMsg;
import com.abujlb.dataprobi.bean.tgc.SqliteTgcSpxgDp;
import com.abujlb.dataprobi.bean.tgc.SqliteTgcSpxgSp;
import com.abujlb.dataprobi.bean.xhs.*;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import com.abujlb.dataprobi.util.MathUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;




/**
 * 千帆直播营销
 */
@Component
@BiPro(order = 3, qd = Qd.XHS)
public class DefaultbiXhsQfbjyxProcess extends AbstractBiprocess {

    public static final String OSSKEY_PATTERN = "bixhs/%s/%s/tgbj.json";

    @Override
    public void dealGoods() {
        super.dealGoods(
                SqliteXhsQfbjSp.class,
                OSSKEY_PATTERN,
                ((DataprobiXhsMsg) getDataprobiBaseMsg()).getQfbjyx()
        );
    }


    @Override
    public boolean compareGoods() {
        return super.compareGoods(
                SqliteXhsQfbjSp.class,
                OSSKEY_PATTERN,
                ((DataprobiXhsMsg) getDataprobiBaseMsg()).getQfbjyx()
        );
    }

    @Override
    protected <T extends AbstractSqliteSp> void dealShopByGoods(String rq, List<T> list) {
        //店铺汇总

        //千帆笔记花费
        double qfbjhf = 0 ;
        for (T t : list) {
            if (t instanceof SqliteXhsQfbjSp) {
                qfbjhf = MathUtil.add(qfbjhf, ((SqliteXhsQfbjSp) t).getQfbjhf());
            }
        }
        if (qfbjhf == 0) {
            return;
        }

        SqliteXhsQfbjDp sqliteXhsQfbjDp = new SqliteXhsQfbjDp();
        sqliteXhsQfbjDp.setQd(getDataprobiBaseMsg().getQd());
        sqliteXhsQfbjDp.setQd_userid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid());
        sqliteXhsQfbjDp.setRq(rq);
        sqliteXhsQfbjDp.setYhid(getDataprobiBaseMsg().getYhid());
        sqliteXhsQfbjDp.setUserid(getDataprobiBaseMsg().getUserid());
        sqliteXhsQfbjDp.setQfbjhf(qfbjhf);
        processSycmDpOTS(rq, sqliteXhsQfbjDp);
    }

}
