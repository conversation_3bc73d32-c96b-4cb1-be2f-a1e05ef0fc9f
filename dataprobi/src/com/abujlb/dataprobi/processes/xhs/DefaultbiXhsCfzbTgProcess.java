package com.abujlb.dataprobi.processes.xhs;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.xhs.*;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;

import java.util.List;

/**
 * 乘风平台推广花费（直播）
 */
@Component
@BiPro(order = 8, qd = Qd.XHS)
public class DefaultbiXhsCfzbTgProcess extends AbstractBiXhsProcess {

    public static final String OSSKEY_PATTERN_SP = "bixhs/%s/%s/cfzbtg/";
    public static final String OSSKEY_PATTERN_DP = "bixhs/%s/%s/cfzbtgdp/";
    private static final Logger logger = LoggerFactory.getLogger(DefaultbiXhsCfzbTgProcess.class);

    @Override
    public void dealGoods() {
        super.dealSpu(
                SqliteXhsCfzbtgSp.class,
                OSSKEY_PATTERN_SP,
                ((DataprobiXhsMsg) getDataprobiBaseMsg()).getCfzbtg()
        );
    }


//    @Override
//    public void dealShop() {
//        List<String> rqList = ((DataprobiXhsMsg) getDataprobiBaseMsg()).getCfzbtgdp();
//
//        for (String rq : rqList) {
//            try {
//                // 构建OSS文件夹路径
//                String ossFolderPath = String.format(OSSKEY_PATTERN_DP, getDataprobiBaseMsg().getUserid(), rq);
//
//                // 获取文件夹下所有JSON文件
//                List<String> jsonFiles = biDataOssUtil.listFiles(ossFolderPath);
//                if (CollectionUtils.isEmpty(jsonFiles)) {
//                    continue;
//                }
//
//                // 初始化总费用
//                double totalZbzbyghf = 0.0;  // 直播直播预告花费
//                double totalZbrcxshf = 0.0;  // 直播日常销售花费
//
//                // 遍历所有JSON文件并累加费用
//                for (String jsonFile : jsonFiles) {
//                    if (!jsonFile.endsWith(".json")) {
//                        continue;
//                    }
//
//                    String json = biDataOssUtil.readJson(jsonFile);
//                    if (StringUtils.isEmpty(json)) {
//                        continue;
//                    }
//
//
//                    JSONObject jsonObject = JSONObject.parseObject(json);
//                    if (jsonObject != null) {
//                        // 累加各项费用
//                        totalZbzbyghf += FastJSONObjAttrToNumber.toDouble(jsonObject, "zbzbyg_fee");
//                        totalZbrcxshf += FastJSONObjAttrToNumber.toDouble(jsonObject, "zbrcxs_fee");
//                    }
//                }
//
//                // 创建并设置SqliteXhsCfzbtgDp对象
//                SqliteXhsCfzbtgDp dp = new SqliteXhsCfzbtgDp();
//                dp.setQd(getDataprobiBaseMsg().getQd());
//                dp.setQd_userid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid());
//                dp.setRq(rq);
//                dp.setYhid(getDataprobiBaseMsg().getYhid());
//                dp.setUserid(getDataprobiBaseMsg().getUserid());
//
//                // 设置累计的费用值
//                dp.setZbzbyghf(totalZbzbyghf);
//                dp.setZbrcxshf(totalZbrcxshf);
//
//                // 处理数据
//                processSycmDpOTS(rq, dp);
//
//            } catch (Exception e) {
//                logger.error("处理店铺数据失败: " + rq, e);
//            }
//        }
//    }


    @Override
    public void dealShop() {
        super.dealShop(
                SqliteXhsCfzbtgDp.class,
                OSSKEY_PATTERN_DP,
                ((DataprobiXhsMsg) getDataprobiBaseMsg()).getCfzbtgdp()
        );
    }

    @Override
    public boolean compareGoods() {
        return super.compareSpu(
                SqliteXhsCfzbtgSp.class,
                OSSKEY_PATTERN_SP,
                ((DataprobiXhsMsg) getDataprobiBaseMsg()).getCfzbtg()
        );
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqliteXhsCfzbtgDp.class,
                OSSKEY_PATTERN_DP,
                ((DataprobiXhsMsg) getDataprobiBaseMsg()).getCfzbtgdp()
        );
    }

}
