package com.abujlb.dataprobi.processes.xhs;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.xhs.DataprobiXhsMsg;
import com.abujlb.dataprobi.bean.xhs.SqliteXhsLlsjDp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import org.springframework.stereotype.Component;


/**
 * 千帆流量数据
 */
@Component
@BiPro(order = 4, qd = Qd.XHS)
public class DefaultbiXhsLlsjProcess extends AbstractBiprocess {

    private final String[] COLUMNS = {BiSycmDpDataTsDao.XHS_COLUMN_LLSJDP};


    @Override
    public void dealShop() {
        super.dealShop(
                SqliteXhsLlsjDp.class,
                ((DataprobiXhsMsg) getDataprobiBaseMsg()).getLlsjdp(),
                COLUMNS
        );
    }


    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqliteXhsLlsjDp.class,
                ((DataprobiXhsMsg) getDataprobiBaseMsg()).getLlsjdp(),
                COLUMNS
        );
    }

}
