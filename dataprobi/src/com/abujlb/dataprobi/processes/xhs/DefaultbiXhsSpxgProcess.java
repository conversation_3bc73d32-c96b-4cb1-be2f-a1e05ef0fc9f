package com.abujlb.dataprobi.processes.xhs;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.tgc.DataprobiTgcMsg;
import com.abujlb.dataprobi.bean.tgc.SqliteTgcSpxgDp;
import com.abujlb.dataprobi.bean.tgc.SqliteTgcSpxgSp;
import com.abujlb.dataprobi.bean.xhs.DataprobiXhsMsg;
import com.abujlb.dataprobi.bean.xhs.SqliteXhsSpxgDp;
import com.abujlb.dataprobi.bean.xhs.SqliteXhsSpxgSp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@BiPro(order = 1, qd = Qd.XHS)
public class DefaultbiXhsSpxgProcess extends AbstractBiprocess {

    public static final String OSSKEY_PATTERN = "bixhs/%s/%s/spxg.json";
    private final String[] COLUMNS = {BiSycmDpDataTsDao.XHS_COLUMN_SPXGDP};

    @Override
    public void dealGoods() {
        super.dealGoods(
                SqliteXhsSpxgSp.class,
                OSSKEY_PATTERN,
                ((DataprobiXhsMsg) getDataprobiBaseMsg()).getSpxg()
        );
    }

    @Override
    public void dealShop() {
        super.dealShop(
                SqliteXhsSpxgDp.class,
                ((DataprobiXhsMsg) getDataprobiBaseMsg()).getSpxgdp(),
                COLUMNS
        );
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoods(
                SqliteXhsSpxgSp.class,
                OSSKEY_PATTERN,
                ((DataprobiXhsMsg) getDataprobiBaseMsg()).getSpxg()
        );
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqliteXhsSpxgDp.class,
                ((DataprobiXhsMsg) getDataprobiBaseMsg()).getSpxgdp(),
                COLUMNS
        );
    }

}
