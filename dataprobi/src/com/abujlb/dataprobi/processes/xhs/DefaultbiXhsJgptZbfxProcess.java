package com.abujlb.dataprobi.processes.xhs;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.tgc.DataprobiTgcMsg;
import com.abujlb.dataprobi.bean.tgc.SqliteTgcSpxgDp;
import com.abujlb.dataprobi.bean.tgc.SqliteTgcSpxgSp;
import com.abujlb.dataprobi.bean.xhs.*;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.abujlb.dataprobi.util.MathUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import com.alibaba.fastjson.JSONObject;

import java.util.List;


/**
 * 聚光平台直播分析
 */
@Component
@BiPro(order = 5, qd = Qd.XHS)
public class DefaultbiXhsJgptZbfxProcess extends AbstractBiprocess {

    public static final String OSSKEY_PATTERN_DP = "bixhs/%s/%s/jgptzbdp/";


    //暂无数据
    @Override
    public void dealShop() {
        List<String> rqList = ((DataprobiXhsMsg) getDataprobiBaseMsg()).getJgptzbfxdp();
        
        for (String rq : rqList) {
            try {
                // 构建OSS文件夹路径
                String ossFolderPath = String.format(OSSKEY_PATTERN_DP, getDataprobiBaseMsg().getUserid(), rq);
                
                // 获取文件夹下所有JSON文件
                List<String> jsonFiles = biDataOssUtil.listFiles(ossFolderPath);
                if (CollectionUtils.isEmpty(jsonFiles)) {
                    continue;
                }

                // 初始化累计值
                double totalJgzbhf = 0.0;      // 直播花费（累加）
                double totalJgzbjzfje = 0.0;   // 直播间支付金额（累加）
                int totalJgzbjzfdsl = 0;       // 直播间支付订单量（累加）
                int totalJgzbjgkcs = 0;        // 直播间观看次数（累加）
                int totalJgzbjyxgkcs = 0;      // 直播间有效观看次数（累加）
                int totalJgzbjxzfs = 0;        // 直播间新增粉丝数（累加）
                int totalJgzbjplcs = 0;        // 直播间评论次数（累加）

                // 用于计算平均值的变量
                double sumJgzbjzfroi = 0.0;    // ROI总和
                double sumJgzbjzfdcb = 0.0;    // 订单成本总和
                double sumJgzbjrjtlsc = 0.0;   // 人均停留时长总和
                int validFileCount = 0;         // 有效文件计数

                // 遍历所有JSON文件并处理数据
                for (String jsonFile : jsonFiles) {
                    if (!jsonFile.endsWith(".json")) {
                        continue;
                    }
                    
                    String json = biDataOssUtil.readJson(jsonFile);
                    if (StringUtils.isBlank(json)) {
                        continue;
                    }
                    
                    JSONObject jsonObject = JSONObject.parseObject(json);
                    if (jsonObject != null) {
                        validFileCount++;

                        // 累加值
                        totalJgzbhf += FastJSONObjAttrToNumber.toDouble(jsonObject, "fee");
                        totalJgzbjzfje += FastJSONObjAttrToNumber.toDouble(jsonObject, "clkLiveRoomRgmv");
                        totalJgzbjzfdsl += FastJSONObjAttrToNumber.toInt(jsonObject, "clkLiveRoomOrderNum");
                        totalJgzbjgkcs += FastJSONObjAttrToNumber.toInt(jsonObject, "clkLiveEntryPv");
                        totalJgzbjyxgkcs += FastJSONObjAttrToNumber.toInt(jsonObject, "clkLive5sEntryPv");
                        totalJgzbjxzfs += FastJSONObjAttrToNumber.toInt(jsonObject, "clkLiveAllFollow");
                        totalJgzbjplcs += FastJSONObjAttrToNumber.toInt(jsonObject, "clkLiveComment");

                        // 累加需要计算平均值的字段
                        sumJgzbjzfroi += FastJSONObjAttrToNumber.toDouble(jsonObject, "clkLiveRoomRoi");
                        sumJgzbjzfdcb += FastJSONObjAttrToNumber.toDouble(jsonObject, "liveAverageOrderCost");
                        sumJgzbjrjtlsc += FastJSONObjAttrToNumber.toDouble(jsonObject, "clkLiveAvgViewTime");
                    }
                }

                if (validFileCount == 0) {
                    continue;
                }

                // 创建并设置SqliteXhsJgzbDp对象
                SqliteXhsJgzbDp dp = new SqliteXhsJgzbDp();
                dp.setQd(getDataprobiBaseMsg().getQd());
                dp.setQd_userid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid());
                dp.setRq(rq);
                dp.setYhid(getDataprobiBaseMsg().getYhid());
                dp.setUserid(getDataprobiBaseMsg().getUserid());
                
                // 设置累加值
                dp.setJgzbhf(totalJgzbhf);
                dp.setJgzbjzfje(totalJgzbjzfje);
                dp.setJgzbjzfdsl(totalJgzbjzfdsl);
                dp.setJgzbjgkcs(totalJgzbjgkcs);
                dp.setJgzbjyxgkcs(totalJgzbjyxgkcs);
                dp.setJgzbjxzfs(totalJgzbjxzfs);
                dp.setJgzbjplcs(totalJgzbjplcs);

                // 设置平均值
                dp.setJgzbjzfroi(sumJgzbjzfroi / validFileCount);
                dp.setJgzbjzfdcb(sumJgzbjzfdcb / validFileCount);
                dp.setJgzbjrjtlsc(sumJgzbjrjtlsc / validFileCount);

                // 处理数据
                processSycmDpOTS(rq, dp);

            } catch (Exception e) {
                logger.error("处理店铺数据失败: " + rq, e);
            }
        }
    }
}
