package com.abujlb.dataprobi.processes.xhs;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.xhs.*;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import com.abujlb.dataprobi.util.MathUtil;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * 千帆直播营销
 */
@Component
@BiPro(order = 9, qd = Qd.XHS)
public class DefaultbiXhsPgyhfProcess extends AbstractBiprocess {

    public static final String OSSKEY_PATTERN = "bixhs/%s/%s/pgybjbg.json";
    private final String[] COLUMNS = {BiSycmDpDataTsDao.XHS_COLUMN_PGYBJBGDP};

    @Override
    public void dealGoods() {
        super.dealGoods(
                SqliteXhsPgyhfSp.class,
                OSSKEY_PATTERN,
                ((DataprobiXhsMsg) getDataprobiBaseMsg()).getPgybjbg()
        );
    }

    @Override
    public void dealShop() {
        super.dealShop(
                SqliteXhsPgyhfDp.class,
                ((DataprobiXhsMsg) getDataprobiBaseMsg()).getPgybjbgdp(),
                COLUMNS
        );
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoods(
                SqliteXhsPgyhfSp.class,
                OSSKEY_PATTERN,
                ((DataprobiXhsMsg) getDataprobiBaseMsg()).getPgybjbg()
        );
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqliteXhsPgyhfDp.class,
                ((DataprobiXhsMsg) getDataprobiBaseMsg()).getPgybjbgdp(),
                COLUMNS
        );
    }

}
