package com.abujlb.dataprobi.processes.tgc;

import com.abujlb.CommonConfig;
import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.tgc.DataprobiTgcMsg;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.sqlite.SqliteDb;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.io.File;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Component
@BiPro(order = 2, qd = Qd.TGC)
public class DefaultbiTgcDaySplbProcess extends AbstractBiprocess {

    public static final String OSSKEY_PATTERN = "bitgc/%s/%s/cplb.json";

    @Override
    public void dealGoods() {
        List<String> rqList = ((DataprobiTgcMsg) getDataprobiBaseMsg()).getCpgl();
        if (CollectionUtils.isEmpty(rqList)) {
            return;
        }
        String rq = rqList.get(rqList.size() - 1);
        String osskey = String.format(OSSKEY_PATTERN, getDataprobiBaseMsg().getUserid(), rq);
        if (!biDataOssUtil.exist(osskey)) {
            return;
        }

        String json = biDataOssUtil.readJson(osskey);
        JSONArray jsonArray = JSONArray.parseArray(json);
        //处理type
        dealTGCGoodsType(jsonArray);
    }

    private void dealTGCGoodsType(JSONArray jsonArray) {
        List<String> rqList = ((DataprobiTgcMsg) getDataprobiBaseMsg()).getCpgl();
        SqliteDb sqliteDb = null;
        String dbpath = null;
        Statement statement = null;
        File file = null;
        if (CollectionUtils.isEmpty(jsonArray)) {
            return;
        }
        Map<String, String> map = new HashMap<>();
        try {
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                String type = jsonObject.getString("type");
                String itemId = jsonObject.getString("bbid");
                map.put(itemId, type);
            }

            dbpath = CommonConfig.getString("tempdir") + UUID.randomUUID() + ".db";
            sqliteDb = new SqliteDb(dbpath, "CREATE TABLE tgc_cplb (bbid text,type text) ;", null);
            statement = sqliteDb.createStatement();
            for (Map.Entry<String, String> entry : map.entrySet()) {
                String itemId = entry.getKey();
                String type = entry.getValue();
                statement.addBatch(" INSERT INTO tgc_cplb (`bbid`,`type`) values ('" + itemId + "','" + type + "')");
            }
            statement.executeBatch();

            file = new File(dbpath);
            for (String rq : rqList) {
                biDataOssUtil.upload(file, "bi_data/day/" + rq.replace("-", "") + "/" + getDataprobiBaseMsg().getYhid() + "/TGC/" + getDataprobiBaseMsg().getUserid() + "/tgc_cplb.db");
            }
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        } finally {
            if (statement != null) {
                try {
                    statement.close();
                } catch (SQLException e) {
                    throw new RuntimeException(e);
                }
            }
            if (sqliteDb != null) {
                try {
                    sqliteDb.close();
                } catch (SQLException e) {
                    throw new RuntimeException(e);
                }
            }
            if (file != null && file.exists()) {
                file.delete();
            }
        }
    }
}
