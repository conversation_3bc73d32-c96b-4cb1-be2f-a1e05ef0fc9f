package com.abujlb.dataprobi.processes.tgc;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.tgc.BiTgcAiztone;
import com.abujlb.dataprobi.bean.tgc.DataprobiTgcMsg;
import com.abujlb.dataprobi.bean.tgc.SqliteTgcAiztDp;
import com.abujlb.dataprobi.bean.tgc.SqliteTgcAiztSp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.threads.BiThreadLocals;
import com.abujlb.dataprobi.util.FileUtil;
import com.abujlb.dataprobi.util.MathUtil;
import com.abujlb.dataprobi.util.StringToNumber;
import com.csvreader.CsvReader;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileInputStream;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@BiPro(order = 6, qd = Qd.TGC)
public class DefaultbiTgcAiztProcess extends AbstractBiprocess {

    public static final String OSSKEY_PATTERN1 = "bitgc/%s/%s/bi_aiztone_aizt.csv";
    public static final String OSSKEY_PATTERN2 = "bitgc/%s/%s/%s/bi_aiztone_aizt.csv";

    @Override
    public void dealGoods() {
        super.dealGoods(
                SqliteTgcAiztSp.class,
                OSSKEY_PATTERN1,
                ((DataprobiTgcMsg) getDataprobiBaseMsg()).getAizt()
        );
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoods(
                SqliteTgcAiztSp.class,
                OSSKEY_PATTERN1,
                ((DataprobiTgcMsg) getDataprobiBaseMsg()).getAizt()
        );
    }

    @Override
    protected <T extends AbstractSqliteSp> void dealShopByGoods(String rq, List<T> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        SqliteTgcAiztDp sqliteTgcAiztDp = new SqliteTgcAiztDp();
        sqliteTgcAiztDp.setQd(getDataprobiBaseMsg().getQd());
        sqliteTgcAiztDp.setUserid(getDataprobiBaseMsg().getUserid());
        sqliteTgcAiztDp.setQd_userid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid());
        sqliteTgcAiztDp.setYhid(getDataprobiBaseMsg().getYhid());
        sqliteTgcAiztDp.setRq(rq);

        for (T t : list) {
            if (t instanceof SqliteTgcAiztSp) {
                sqliteTgcAiztDp.setAiztzxl(sqliteTgcAiztDp.getAiztzxl() + ((SqliteTgcAiztSp) t).getAiztzxl());
                sqliteTgcAiztDp.setAiztdjl(sqliteTgcAiztDp.getAiztdjl() + ((SqliteTgcAiztSp) t).getAiztdjl());
                sqliteTgcAiztDp.setAiztcjbs(sqliteTgcAiztDp.getAiztcjbs() + ((SqliteTgcAiztSp) t).getAiztcjbs());
                sqliteTgcAiztDp.setAiztcjje(MathUtil.add(sqliteTgcAiztDp.getAiztcjje(), ((SqliteTgcAiztSp) t).getAiztcjje()));
                sqliteTgcAiztDp.setAizthf(MathUtil.add(sqliteTgcAiztDp.getAizthf(), ((SqliteTgcAiztSp) t).getAizthf()));
            }
        }
        processSycmDpOTS(rq, sqliteTgcAiztDp);
    }


    @Override
    protected <T extends AbstractSqliteSp> List<T> spList(Class<T> tClass, String ossKey, String rq) {
        List<BiTgcAiztone> tgcAiztoneList = BiThreadLocals.getTgcAiztoneList();
        if (CollectionUtils.isEmpty(tgcAiztoneList)) {
            return null;
        }
        Map<String, SqliteTgcAiztSp> map = new HashMap<>();
        for (BiTgcAiztone biTgcAiztone : tgcAiztoneList) {
            String osskey = String.format(OSSKEY_PATTERN2, getDataprobiBaseMsg().getUserid(), rq, biTgcAiztone.getAiztoneid());
            if (!biDataOssUtil.exist(osskey)) {
                continue;
            }
            String temppath = FileUtil.createCSVFilePath();
            biDataOssUtil.download(osskey, temppath);

            analyseCSV(map, temppath, rq);
        }
        return (List<T>) new ArrayList<>(map.values());
    }

    private void analyseCSV(Map<String, SqliteTgcAiztSp> map, String temppath, String rq) {
        File file = null;
        FileInputStream fileInputStream = null;
        CsvReader cr = null;
        try {
            file = new File(temppath);
            if (!file.exists()) {
                return;
            }
            fileInputStream = new FileInputStream(file);
            cr = new CsvReader(fileInputStream, Charset.forName("GBK"));
            cr.readHeaders();

            while (cr.readRecord()) {
                String ztlx = cr.get("主体类型");
                if (StringUtils.isBlank(ztlx) || !ztlx.equals("商品")) {
                    continue;
                }

                String rq2 = cr.get("日期");
                if (!rq.equals(rq2)) {
                    continue;
                }

                String bbid = cr.get("主体ID");
                if (StringUtils.isBlank(bbid)) {
                    continue;
                }
                SqliteTgcAiztSp sqliteTgcAiztSp = null;
                if (map.containsKey(bbid)) {
                    sqliteTgcAiztSp = map.get(bbid);
                } else {
                    sqliteTgcAiztSp = new SqliteTgcAiztSp();
                    sqliteTgcAiztSp.setQd_userid_bbid(BiThreadLocals.getMsgBean().getQd() + "_" + BiThreadLocals.getMsgBean().getUserid() + "_" + bbid);
                    sqliteTgcAiztSp.setYhid(BiThreadLocals.getMsgBean().getYhid());
                    sqliteTgcAiztSp.setQd(BiThreadLocals.getMsgBean().getQd());
                    sqliteTgcAiztSp.setUserid(BiThreadLocals.getMsgBean().getUserid());
                    sqliteTgcAiztSp.setRq(rq);
                    sqliteTgcAiztSp.setBbid(bbid);
                }

                SqliteTgcAiztSp temp = new SqliteTgcAiztSp();
                temp.setAizthf(StringToNumber.getDouble(cr.get("花费")));
                temp.setAiztzxl(StringToNumber.getInt(cr.get("展现量")));
                temp.setAiztdjl(StringToNumber.getInt(cr.get("点击量")));
                temp.setAiztcjje(StringToNumber.getDouble(cr.get("总成交金额")));
                temp.setAiztcjbs(StringToNumber.getInt(cr.get("总成交笔数")));

                plus(sqliteTgcAiztSp, temp);
                map.put(bbid, sqliteTgcAiztSp);
            }
            cr.close();
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        } finally {
            if (cr != null) {
                cr.close();
            }
            if (file != null && file.exists()) {
                file.delete();
            }
        }
    }

    private void plus(SqliteTgcAiztSp target, SqliteTgcAiztSp source) {
        target.setAiztcjbs(target.getAiztcjbs() + source.getAiztcjbs());
        target.setAiztzxl(target.getAiztzxl() + source.getAiztzxl());
        target.setAiztdjl(target.getAiztdjl() + source.getAiztdjl());
        target.setAiztcjje(MathUtil.add(target.getAiztcjje(), source.getAiztcjje()));
        target.setAizthf(MathUtil.add(target.getAizthf(), source.getAizthf()));
    }
}
