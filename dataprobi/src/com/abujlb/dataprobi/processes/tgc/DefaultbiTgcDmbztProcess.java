package com.abujlb.dataprobi.processes.tgc;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.tgc.BiTgcAiztone;
import com.abujlb.dataprobi.bean.tgc.DataprobiTgcMsg;
import com.abujlb.dataprobi.bean.tgc.SqliteTgcDmbztDp;
import com.abujlb.dataprobi.bean.tgc.SqliteTgcDmbztSp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.threads.BiThreadLocals;
import com.abujlb.dataprobi.util.FileUtil;
import com.abujlb.dataprobi.util.MathUtil;
import com.abujlb.dataprobi.util.StringToNumber;
import com.csvreader.CsvReader;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileInputStream;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@BiPro(order = 8, qd = Qd.TGC)
public class DefaultbiTgcDmbztProcess extends AbstractBiprocess {

    public static final String OSSKEY_PATTERN1 = "bitgc/%s/%s/bi_aiztone_dmbzt.csv";
    public static final String OSSKEY_PATTERN2 = "bitgc/%s/%s/%s/bi_aiztone_dmbzt.csv";

    @Override
    public void dealGoods() {
        super.dealGoods(
                SqliteTgcDmbztSp.class,
                OSSKEY_PATTERN1,
                ((DataprobiTgcMsg) getDataprobiBaseMsg()).getDmbzt()
        );
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoods(
                SqliteTgcDmbztSp.class,
                OSSKEY_PATTERN1,
                ((DataprobiTgcMsg) getDataprobiBaseMsg()).getDmbzt()
        );
    }

    @Override
    protected <T extends AbstractSqliteSp> void dealShopByGoods(String rq, List<T> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        SqliteTgcDmbztDp sqliteTgcDmbztDp = new SqliteTgcDmbztDp();
        sqliteTgcDmbztDp.setQd(getDataprobiBaseMsg().getQd());
        sqliteTgcDmbztDp.setUserid(getDataprobiBaseMsg().getUserid());
        sqliteTgcDmbztDp.setQd_userid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid());
        sqliteTgcDmbztDp.setYhid(getDataprobiBaseMsg().getYhid());
        sqliteTgcDmbztDp.setRq(rq);

        for (T t : list) {
            if (t instanceof SqliteTgcDmbztSp) {
                sqliteTgcDmbztDp.setDmbztzxl(sqliteTgcDmbztDp.getDmbztzxl() + ((SqliteTgcDmbztSp) t).getDmbztzxl());
                sqliteTgcDmbztDp.setDmbztdjl(sqliteTgcDmbztDp.getDmbztdjl() + ((SqliteTgcDmbztSp) t).getDmbztdjl());
                sqliteTgcDmbztDp.setDmbztcjbs(sqliteTgcDmbztDp.getDmbztcjbs() + ((SqliteTgcDmbztSp) t).getDmbztcjbs());
                sqliteTgcDmbztDp.setDmbztcjje(MathUtil.add(sqliteTgcDmbztDp.getDmbztcjje(), ((SqliteTgcDmbztSp) t).getDmbztcjje()));
                sqliteTgcDmbztDp.setDmbzthf(MathUtil.add(sqliteTgcDmbztDp.getDmbzthf(), ((SqliteTgcDmbztSp) t).getDmbzthf()));
            }
        }
        processSycmDpOTS(rq, sqliteTgcDmbztDp);
    }


    @Override
    protected <T extends AbstractSqliteSp> List<T> spList(Class<T> tClass, String ossKey, String rq) {
        List<BiTgcAiztone> tgcAiztoneList = BiThreadLocals.getTgcAiztoneList();
        if (CollectionUtils.isEmpty(tgcAiztoneList)) {
            return null;
        }
        Map<String, SqliteTgcDmbztSp> map = new HashMap<>();
        for (BiTgcAiztone biTgcAiztone : tgcAiztoneList) {
            String osskey = String.format(OSSKEY_PATTERN2, getDataprobiBaseMsg().getUserid(), rq, biTgcAiztone.getAiztoneid());
            if (!biDataOssUtil.exist(osskey)) {
                continue;
            }
            String temppath = FileUtil.createCSVFilePath();
            biDataOssUtil.download(osskey, temppath);

            analyseCSV(map, temppath, rq);
        }
        return (List<T>) new ArrayList<>(map.values());
    }

    private void analyseCSV(Map<String, SqliteTgcDmbztSp> map, String temppath, String rq) {
        File file = null;
        FileInputStream fileInputStream = null;
        CsvReader cr = null;
        try {
            file = new File(temppath);
            if (!file.exists()) {
                return;
            }
            fileInputStream = new FileInputStream(file);
            cr = new CsvReader(fileInputStream, Charset.forName("GBK"));
            cr.readHeaders();

            while (cr.readRecord()) {
                String ztlx = cr.get("主体类型");
                if (StringUtils.isBlank(ztlx) || !ztlx.equals("商品")) {
                    continue;
                }

                String rq2 = cr.get("日期");
                if (!rq.equals(rq2)) {
                    continue;
                }

                String bbid = cr.get("主体ID");
                if (StringUtils.isBlank(bbid)) {
                    continue;
                }
                SqliteTgcDmbztSp sqliteTgcDmbztSp = null;
                if (map.containsKey(bbid)) {
                    sqliteTgcDmbztSp = map.get(bbid);
                } else {
                    sqliteTgcDmbztSp = new SqliteTgcDmbztSp();
                    sqliteTgcDmbztSp.setQd_userid_bbid(BiThreadLocals.getMsgBean().getQd() + "_" + BiThreadLocals.getMsgBean().getUserid() + "_" + bbid);
                    sqliteTgcDmbztSp.setYhid(BiThreadLocals.getMsgBean().getYhid());
                    sqliteTgcDmbztSp.setQd(BiThreadLocals.getMsgBean().getQd());
                    sqliteTgcDmbztSp.setUserid(BiThreadLocals.getMsgBean().getUserid());
                    sqliteTgcDmbztSp.setRq(rq);
                    sqliteTgcDmbztSp.setBbid(bbid);
                }

                SqliteTgcDmbztSp temp = new SqliteTgcDmbztSp();
                temp.setDmbzthf(StringToNumber.getDouble(cr.get("花费")));
                temp.setDmbztzxl(StringToNumber.getInt(cr.get("展现量")));
                temp.setDmbztdjl(StringToNumber.getInt(cr.get("点击量")));
                temp.setDmbztcjje(StringToNumber.getDouble(cr.get("总成交金额")));
                temp.setDmbztcjbs(StringToNumber.getInt(cr.get("总成交笔数")));

                plus(sqliteTgcDmbztSp, temp);
                map.put(bbid, sqliteTgcDmbztSp);
            }
            cr.close();
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        } finally {
            if (cr != null) {
                cr.close();
            }
            if (file != null && file.exists()) {
                file.delete();
            }
        }
    }

    private void plus(SqliteTgcDmbztSp target, SqliteTgcDmbztSp source) {
        target.setDmbztcjbs(target.getDmbztcjbs() + source.getDmbztcjbs());
        target.setDmbztzxl(target.getDmbztzxl() + source.getDmbztzxl());
        target.setDmbztdjl(target.getDmbztdjl() + source.getDmbztdjl());
        target.setDmbztcjje(MathUtil.add(target.getDmbztcjje(), source.getDmbztcjje()));
        target.setDmbzthf(MathUtil.add(target.getDmbzthf(), source.getDmbzthf()));
    }
}
