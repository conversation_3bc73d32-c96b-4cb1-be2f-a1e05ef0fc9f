package com.abujlb.dataprobi.processes.tgc;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.tgc.DataprobiTgcMsg;
import com.abujlb.dataprobi.bean.tgc.SqliteTgcSpyytgDp;
import com.abujlb.dataprobi.bean.tgc.SqliteTgcSpyytgSp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.util.MathUtil;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 淘工厂 - 商品运营托管
 */
@Component
@BiPro(order = 5, qd = Qd.TGC)
public class DefaultbiTgcSpyytgProcess extends AbstractBiprocess {

    public static final String OSSKEY_PATTERN = "bitgc/%s/%s/spyytg.json";

    @Override
    public void dealGoods() {
        super.dealGoods(
                SqliteTgcSpyytgSp.class,
                OSSKEY_PATTERN,
                ((DataprobiTgcMsg) getDataprobiBaseMsg()).getSpyytg()
        );
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoods(
                SqliteTgcSpyytgSp.class,
                OSSKEY_PATTERN,
                ((DataprobiTgcMsg) getDataprobiBaseMsg()).getSpyytg()
        );
    }

    @Override
    protected <T extends AbstractSqliteSp> void dealShopByGoods(String rq, List<T> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        SqliteTgcSpyytgDp sqliteTgcSpyytgDp = new SqliteTgcSpyytgDp();
        sqliteTgcSpyytgDp.setQd(getDataprobiBaseMsg().getQd());
        sqliteTgcSpyytgDp.setQd_userid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid());
        sqliteTgcSpyytgDp.setRq(rq);
        sqliteTgcSpyytgDp.setYhid(getDataprobiBaseMsg().getYhid());
        sqliteTgcSpyytgDp.setUserid(getDataprobiBaseMsg().getUserid());

        //淘工厂-商品运营托管-预估花费
        double hf = 0;
        double cjje = 0;
        for (T t : list) {
            if (t instanceof SqliteTgcSpyytgSp) {
                hf = MathUtil.add(hf, ((SqliteTgcSpyytgSp) t).getSpyytgyghf());
                cjje = MathUtil.add(cjje, ((SqliteTgcSpyytgSp) t).getSpyytgygcjje());
            }
        }

        sqliteTgcSpyytgDp.setSpyytgyghf(hf);
        sqliteTgcSpyytgDp.setSpyytgygcjje(cjje);
        if (hf != 0 && cjje != 0) {
            processSycmDpOTS(rq, sqliteTgcSpyytgDp);
        }
    }
}
