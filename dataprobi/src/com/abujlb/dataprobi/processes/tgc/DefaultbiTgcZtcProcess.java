package com.abujlb.dataprobi.processes.tgc;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.tgc.BiTgcAiztone;
import com.abujlb.dataprobi.bean.tgc.DataprobiTgcMsg;
import com.abujlb.dataprobi.bean.tgc.SqliteTgcZtcDp;
import com.abujlb.dataprobi.bean.tgc.SqliteTgcZtcSp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.threads.BiThreadLocals;
import com.abujlb.dataprobi.util.FileUtil;
import com.abujlb.dataprobi.util.MathUtil;
import com.abujlb.dataprobi.util.StringToNumber;
import com.csvreader.CsvReader;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileInputStream;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@BiPro(order = 5, qd = Qd.TGC)
public class DefaultbiTgcZtcProcess extends AbstractBiprocess {

    public static final String OSSKEY_PATTERN1 = "bitgc/%s/%s/bi_aiztone_ztc.csv";
    public static final String OSSKEY_PATTERN2 = "bitgc/%s/%s/%s/bi_aiztone_ztc.csv";

    @Override
    public void dealGoods() {
        super.dealGoods(
                SqliteTgcZtcSp.class,
                OSSKEY_PATTERN1,
                ((DataprobiTgcMsg) getDataprobiBaseMsg()).getZtc()
        );
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoods(
                SqliteTgcZtcSp.class,
                OSSKEY_PATTERN1,
                ((DataprobiTgcMsg) getDataprobiBaseMsg()).getZtc()
        );
    }

    @Override
    protected <T extends AbstractSqliteSp> void dealShopByGoods(String rq, List<T> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        SqliteTgcZtcDp sqliteTgcZtcDp = new SqliteTgcZtcDp();
        sqliteTgcZtcDp.setQd(getDataprobiBaseMsg().getQd());
        sqliteTgcZtcDp.setUserid(getDataprobiBaseMsg().getUserid());
        sqliteTgcZtcDp.setQd_userid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid());
        sqliteTgcZtcDp.setYhid(getDataprobiBaseMsg().getYhid());
        sqliteTgcZtcDp.setRq(rq);

        for (T t : list) {
            if (t instanceof SqliteTgcZtcSp) {
                sqliteTgcZtcDp.setZtczxl(sqliteTgcZtcDp.getZtczxl() + ((SqliteTgcZtcSp) t).getZtczxl());
                sqliteTgcZtcDp.setZtcdjl(sqliteTgcZtcDp.getZtcdjl() + ((SqliteTgcZtcSp) t).getZtcdjl());
                sqliteTgcZtcDp.setZtccjbs(sqliteTgcZtcDp.getZtccjbs() + ((SqliteTgcZtcSp) t).getZtccjbs());
                sqliteTgcZtcDp.setZtccjje(MathUtil.add(sqliteTgcZtcDp.getZtccjje(), ((SqliteTgcZtcSp) t).getZtccjje()));
                sqliteTgcZtcDp.setZtchf(MathUtil.add(sqliteTgcZtcDp.getZtchf(), ((SqliteTgcZtcSp) t).getZtchf()));
            }
        }
        processSycmDpOTS(rq, sqliteTgcZtcDp);
    }


    @Override
    protected <T extends AbstractSqliteSp> List<T> spList(Class<T> tClass, String ossKey, String rq) {
        List<BiTgcAiztone> tgcAiztoneList = BiThreadLocals.getTgcAiztoneList();
        if (CollectionUtils.isEmpty(tgcAiztoneList)) {
            return null;
        }
        Map<String, SqliteTgcZtcSp> map = new HashMap<>();
        for (BiTgcAiztone biTgcAiztone : tgcAiztoneList) {
            String osskey = String.format(OSSKEY_PATTERN2, getDataprobiBaseMsg().getUserid(), rq, biTgcAiztone.getAiztoneid());
            if (!biDataOssUtil.exist(osskey)) {
                continue;
            }
            String temppath = FileUtil.createCSVFilePath();
            biDataOssUtil.download(osskey, temppath);

            analyseCSV(map, temppath, rq);
        }
        return (List<T>) new ArrayList<>(map.values());
    }

    private void analyseCSV(Map<String, SqliteTgcZtcSp> map, String temppath, String rq) {
        File file = null;
        FileInputStream fileInputStream = null;
        CsvReader cr = null;
        try {
            file = new File(temppath);
            if (!file.exists()) {
                return;
            }
            fileInputStream = new FileInputStream(file);
            cr = new CsvReader(fileInputStream, Charset.forName("GBK"));
            cr.readHeaders();

            while (cr.readRecord()) {
                String ztlx = cr.get("主体类型");
                if (StringUtils.isBlank(ztlx) || !ztlx.equals("商品")) {
                    continue;
                }

                String rq2 = cr.get("日期");
                if (!rq.equals(rq2)) {
                    continue;
                }

                String bbid = cr.get("主体ID");
                if (StringUtils.isBlank(bbid)) {
                    continue;
                }
                SqliteTgcZtcSp sqliteTgcZtcSp = null;
                if (map.containsKey(bbid)) {
                    sqliteTgcZtcSp = map.get(bbid);
                } else {
                    sqliteTgcZtcSp = new SqliteTgcZtcSp();
                    sqliteTgcZtcSp.setQd_userid_bbid(BiThreadLocals.getMsgBean().getQd() + "_" + BiThreadLocals.getMsgBean().getUserid() + "_" + bbid);
                    sqliteTgcZtcSp.setYhid(BiThreadLocals.getMsgBean().getYhid());
                    sqliteTgcZtcSp.setQd(BiThreadLocals.getMsgBean().getQd());
                    sqliteTgcZtcSp.setUserid(BiThreadLocals.getMsgBean().getUserid());
                    sqliteTgcZtcSp.setRq(rq);
                    sqliteTgcZtcSp.setBbid(bbid);
                }

                SqliteTgcZtcSp temp = new SqliteTgcZtcSp();
                temp.setZtchf(StringToNumber.getDouble(cr.get("花费")));
                temp.setZtczxl(StringToNumber.getInt(cr.get("展现量")));
                temp.setZtcdjl(StringToNumber.getInt(cr.get("点击量")));
                temp.setZtccjje(StringToNumber.getDouble(cr.get("总成交金额")));
                temp.setZtccjbs(StringToNumber.getInt(cr.get("总成交笔数")));

                plus(sqliteTgcZtcSp, temp);
                map.put(bbid, sqliteTgcZtcSp);
            }
            cr.close();
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        } finally {
            if (cr != null) {
                cr.close();
            }
            if (file != null && file.exists()) {
                file.delete();
            }
        }
    }

    private void plus(SqliteTgcZtcSp target, SqliteTgcZtcSp source) {
        target.setZtccjbs(target.getZtccjbs() + source.getZtccjbs());
        target.setZtczxl(target.getZtczxl() + source.getZtczxl());
        target.setZtcdjl(target.getZtcdjl() + source.getZtcdjl());
        target.setZtccjje(MathUtil.add(target.getZtccjje(), source.getZtccjje()));
        target.setZtchf(MathUtil.add(target.getZtchf(), source.getZtchf()));
    }
}
