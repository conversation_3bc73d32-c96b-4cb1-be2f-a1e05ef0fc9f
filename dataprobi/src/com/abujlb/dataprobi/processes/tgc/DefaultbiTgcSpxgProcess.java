package com.abujlb.dataprobi.processes.tgc;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.tgc.DataprobiTgcMsg;
import com.abujlb.dataprobi.bean.tgc.SqliteTgcSpxgDp;
import com.abujlb.dataprobi.bean.tgc.SqliteTgcSpxgSp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@BiPro(order = 2, qd = Qd.TGC)
public class DefaultbiTgcSpxgProcess extends AbstractBiprocess {

    public static final String OSSKEY_PATTERN = "bitgc/%s/%s/spxg.json";
    private final String[] COLUMNS = {BiSycmDpDataTsDao.TGC_COLUMN_SPXGDP, BiSycmDpDataTsDao.TGC_COLUMN_SPXGDP_ZFMJS, BiSycmDpDataTsDao.TGC_COLUMN_SPXGDP2};

    @Override
    public void dealGoods() {
        super.dealGoods(
                SqliteTgcSpxgSp.class,
                OSSKEY_PATTERN,
                ((DataprobiTgcMsg) getDataprobiBaseMsg()).getSpxg()
        );
    }

    @Override
    public void dealShop() {
        super.dealShop(
                SqliteTgcSpxgDp.class,
                ((DataprobiTgcMsg) getDataprobiBaseMsg()).getSpxgdp(),
                COLUMNS
        );
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoods(
                SqliteTgcSpxgSp.class,
                OSSKEY_PATTERN,
                ((DataprobiTgcMsg) getDataprobiBaseMsg()).getSpxg()
        );
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqliteTgcSpxgDp.class,
                ((DataprobiTgcMsg) getDataprobiBaseMsg()).getSpxgdp(),
                COLUMNS
        );
    }

    @Override
    protected <T extends AbstractSqliteSp> void dealShopByGoods(String rq, List<T> list) {
        //汇总
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        int zfmjs = list.stream().mapToInt(obj -> obj.getSpxgzfmjs()).sum();
        int fks = list.stream().mapToInt(obj -> obj.getSpxgfks()).sum();
        int lll = list.stream().mapToInt(obj -> obj.getSpxglll()).sum();

        String json = "{\"zfmjs\":" + zfmjs + ",\"fks\":" + fks + ",\"lll\":" + lll + "}";
        biSycmDpDataTsDao.updateRow(getDataprobiBaseMsg().getQd(), getDataprobiBaseMsg().getUserid(), rq, BiSycmDpDataTsDao.TGC_COLUMN_SPXGDP_ZFMJS, json);
    }
}
