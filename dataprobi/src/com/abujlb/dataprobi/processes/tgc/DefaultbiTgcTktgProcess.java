package com.abujlb.dataprobi.processes.tgc;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.tgc.DataprobiTgcMsg;
import com.abujlb.dataprobi.bean.tgc.SqliteTgcTktgDp;
import com.abujlb.dataprobi.bean.tgc.SqliteTgcTktgSp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.util.MathUtil;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 淘工厂 - 淘客推广
 *
 * <AUTHOR>
 * @date 2025-5-21
 */
@Component
@BiPro(order = 5, qd = Qd.TGC)
public class DefaultbiTgcTktgProcess extends AbstractBiprocess {

    public static final String OSSKEY_PATTERN = "bitgc/%s/%s/tgc_tk.json";

    @Override
    public void dealGoods() {
        super.dealGoods(SqliteTgcTktgSp.class, OSSKEY_PATTERN, ((DataprobiTgcMsg) getDataprobiBaseMsg()).getTktg());
    }

    @Override
    public boolean compareGoods() {
        super.dealGoods(SqliteTgcTktgSp.class, OSSKEY_PATTERN, ((DataprobiTgcMsg) getDataprobiBaseMsg()).getTktg());
        return false;
    }

    @Override
    protected <T extends AbstractSqliteSp> void dealShopByGoods(String rq, List<T> list) {
        //淘客推广-单品平推-花费
        double tktg_dppt_hf = 0;
        //淘客推广-单品平推-成交金额
        double tktg_dppt_cjje = 0;
        //淘客推广-单品加速-花费
        double tktg_dpjs_hf = 0;
        //淘客推广-单品加速-花费
        double tktg_dpjs_cjje = 0;
        for (T t : list) {
            if (t instanceof SqliteTgcTktgSp) {
                tktg_dppt_hf = MathUtil.add(tktg_dppt_hf, ((SqliteTgcTktgSp) t).getTktg_dppt_hf());
                tktg_dppt_cjje = MathUtil.add(tktg_dppt_cjje, ((SqliteTgcTktgSp) t).getTktg_dppt_cjje());
                tktg_dpjs_hf = MathUtil.add(tktg_dpjs_hf, ((SqliteTgcTktgSp) t).getTktg_dpjs_hf());
                tktg_dpjs_cjje = MathUtil.add(tktg_dpjs_cjje, ((SqliteTgcTktgSp) t).getTktg_dpjs_cjje());
            }
        }

        SqliteTgcTktgDp sqliteTgcTktgDp = new SqliteTgcTktgDp();
        sqliteTgcTktgDp.setQd(getDataprobiBaseMsg().getQd());
        sqliteTgcTktgDp.setQd_userid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid());
        sqliteTgcTktgDp.setRq(rq);
        sqliteTgcTktgDp.setYhid(getDataprobiBaseMsg().getYhid());
        sqliteTgcTktgDp.setUserid(getDataprobiBaseMsg().getUserid());
        sqliteTgcTktgDp.setTktg_dppt_hf(tktg_dppt_hf);
        sqliteTgcTktgDp.setTktg_dppt_cjje(tktg_dppt_cjje);
        sqliteTgcTktgDp.setTktg_dpjs_hf(tktg_dpjs_hf);
        sqliteTgcTktgDp.setTktg_dpjs_cjje(tktg_dpjs_cjje);
        processSycmDpOTS(rq, sqliteTgcTktgDp);
    }
}
