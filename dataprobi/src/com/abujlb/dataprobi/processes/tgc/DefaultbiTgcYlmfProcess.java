package com.abujlb.dataprobi.processes.tgc;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.tgc.BiTgcAiztone;
import com.abujlb.dataprobi.bean.tgc.DataprobiTgcMsg;
import com.abujlb.dataprobi.bean.tgc.SqliteTgcYlmfDp;
import com.abujlb.dataprobi.bean.tgc.SqliteTgcYlmfSp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.threads.BiThreadLocals;
import com.abujlb.dataprobi.util.FileUtil;
import com.abujlb.dataprobi.util.MathUtil;
import com.abujlb.dataprobi.util.StringToNumber;
import com.csvreader.CsvReader;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileInputStream;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@BiPro(order = 7, qd = Qd.TGC)
public class DefaultbiTgcYlmfProcess extends AbstractBiprocess {

    public static final String OSSKEY_PATTERN1 = "bitgc/%s/%s/bi_aiztone_ylmf.csv";
    public static final String OSSKEY_PATTERN2 = "bitgc/%s/%s/%s/bi_aiztone_ylmf.csv";


    @Override
    public void dealGoods() {
        super.dealGoods(
                SqliteTgcYlmfSp.class,
                OSSKEY_PATTERN1,
                ((DataprobiTgcMsg) getDataprobiBaseMsg()).getYlmf()
        );
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoods(
                SqliteTgcYlmfSp.class,
                OSSKEY_PATTERN1,
                ((DataprobiTgcMsg) getDataprobiBaseMsg()).getYlmf()
        );
    }

    @Override
    protected <T extends AbstractSqliteSp> void dealShopByGoods(String rq, List<T> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        SqliteTgcYlmfDp sqliteTgcYlmfDp = new SqliteTgcYlmfDp();
        sqliteTgcYlmfDp.setQd(getDataprobiBaseMsg().getQd());
        sqliteTgcYlmfDp.setUserid(getDataprobiBaseMsg().getUserid());
        sqliteTgcYlmfDp.setQd_userid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid());
        sqliteTgcYlmfDp.setYhid(getDataprobiBaseMsg().getYhid());
        sqliteTgcYlmfDp.setRq(rq);

        for (T t : list) {
            if (t instanceof SqliteTgcYlmfSp) {
                sqliteTgcYlmfDp.setYlmfzxl(sqliteTgcYlmfDp.getYlmfzxl() + ((SqliteTgcYlmfSp) t).getYlmfzxl());
                sqliteTgcYlmfDp.setYlmfdjl(sqliteTgcYlmfDp.getYlmfdjl() + ((SqliteTgcYlmfSp) t).getYlmfdjl());
                sqliteTgcYlmfDp.setYlmfcjbs(sqliteTgcYlmfDp.getYlmfcjbs() + ((SqliteTgcYlmfSp) t).getYlmfcjbs());
                sqliteTgcYlmfDp.setYlmfcjje(MathUtil.add(sqliteTgcYlmfDp.getYlmfcjje(), ((SqliteTgcYlmfSp) t).getYlmfcjje()));
                sqliteTgcYlmfDp.setYlmfhf(MathUtil.add(sqliteTgcYlmfDp.getYlmfhf(), ((SqliteTgcYlmfSp) t).getYlmfhf()));
            }
        }
        processSycmDpOTS(rq, sqliteTgcYlmfDp);
    }


    @Override
    protected <T extends AbstractSqliteSp> List<T> spList(Class<T> tClass, String ossKey, String rq) {
        List<BiTgcAiztone> tgcAiztoneList = BiThreadLocals.getTgcAiztoneList();
        if (CollectionUtils.isEmpty(tgcAiztoneList)) {
            return null;
        }
        Map<String, SqliteTgcYlmfSp> map = new HashMap<>();
        for (BiTgcAiztone biTgcAiztone : tgcAiztoneList) {
            String osskey = String.format(OSSKEY_PATTERN2, getDataprobiBaseMsg().getUserid(), rq, biTgcAiztone.getAiztoneid());
            if (!biDataOssUtil.exist(osskey)) {
                continue;
            }
            String temppath = FileUtil.createCSVFilePath();
            biDataOssUtil.download(osskey, temppath);

            analyseCSV(map, temppath, rq);
        }
        return (List<T>) new ArrayList<>(map.values());
    }

    private void analyseCSV(Map<String, SqliteTgcYlmfSp> map, String temppath, String rq) {
        File file = null;
        FileInputStream fileInputStream = null;
        CsvReader cr = null;
        try {
            file = new File(temppath);
            if (!file.exists()) {
                return;
            }
            fileInputStream = new FileInputStream(file);
            cr = new CsvReader(fileInputStream, Charset.forName("GBK"));
            cr.readHeaders();

            while (cr.readRecord()) {
                String ztlx = cr.get("主体类型");
                if (StringUtils.isBlank(ztlx) || !ztlx.equals("商品")) {
                    continue;
                }

                String rq2 = cr.get("日期");
                if (!rq.equals(rq2)) {
                    continue;
                }

                String bbid = cr.get("主体ID");
                if (StringUtils.isBlank(bbid)) {
                    continue;
                }
                SqliteTgcYlmfSp sqliteTgcYlmfSp = null;
                if (map.containsKey(bbid)) {
                    sqliteTgcYlmfSp = map.get(bbid);
                } else {
                    sqliteTgcYlmfSp = new SqliteTgcYlmfSp();
                    sqliteTgcYlmfSp.setQd_userid_bbid(BiThreadLocals.getMsgBean().getQd() + "_" + BiThreadLocals.getMsgBean().getUserid() + "_" + bbid);
                    sqliteTgcYlmfSp.setYhid(BiThreadLocals.getMsgBean().getYhid());
                    sqliteTgcYlmfSp.setQd(BiThreadLocals.getMsgBean().getQd());
                    sqliteTgcYlmfSp.setUserid(BiThreadLocals.getMsgBean().getUserid());
                    sqliteTgcYlmfSp.setRq(rq);
                    sqliteTgcYlmfSp.setBbid(bbid);
                }

                SqliteTgcYlmfSp temp = new SqliteTgcYlmfSp();
                temp.setYlmfhf(StringToNumber.getDouble(cr.get("花费")));
                temp.setYlmfzxl(StringToNumber.getInt(cr.get("展现量")));
                temp.setYlmfdjl(StringToNumber.getInt(cr.get("点击量")));
                temp.setYlmfcjje(StringToNumber.getDouble(cr.get("总成交金额")));
                temp.setYlmfcjbs(StringToNumber.getInt(cr.get("总成交笔数")));

                plus(sqliteTgcYlmfSp, temp);
                map.put(bbid, sqliteTgcYlmfSp);
            }
            cr.close();
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        } finally {
            if (cr != null) {
                cr.close();
            }
            if (file != null && file.exists()) {
                file.delete();
            }
        }
    }

    private void plus(SqliteTgcYlmfSp target, SqliteTgcYlmfSp source) {
        target.setYlmfcjbs(target.getYlmfcjbs() + source.getYlmfcjbs());
        target.setYlmfzxl(target.getYlmfzxl() + source.getYlmfzxl());
        target.setYlmfdjl(target.getYlmfdjl() + source.getYlmfdjl());
        target.setYlmfcjje(MathUtil.add(target.getYlmfcjje(), source.getYlmfcjje()));
        target.setYlmfhf(MathUtil.add(target.getYlmfhf(), source.getYlmfhf()));
    }
}
