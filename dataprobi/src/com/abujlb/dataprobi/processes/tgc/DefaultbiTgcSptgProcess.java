package com.abujlb.dataprobi.processes.tgc;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.tgc.DataprobiTgcMsg;
import com.abujlb.dataprobi.bean.tgc.SqliteTgcSptgDp;
import com.abujlb.dataprobi.bean.tgc.SqliteTgcSptgSp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.abujlb.dataprobi.util.MathUtil;
import com.abujlb.util.StringUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 淘工厂 - 商品推广
 * <p>
 * https://www.tapd.cn/47708728/prong/stories/view/1147708728001010995
 *
 * <AUTHOR>
 * @date 2024-12-25
 */
@Component
@BiPro(order = 5, qd = Qd.TGC)
public class DefaultbiTgcSptgProcess extends AbstractBiprocess {

    public static final String OSSKEY_PATTERN = "bitgc/%s/%s/sptg.json";
    public static final String SPTG_RYY_OSS_PATTERN = "bitgc/%s/%s/sptg_rys.json";
    public static final String SPTG_ZQYY_OSS_PATTERN = "bitgc/%s/%s/sptg_zqys.json";
    public static final String DP_OSSKEY_PATTERN = "bitgc/%s/%s/sptgdp.json";
    public static final String YSJM_DP_OSSKEY_PATTERN = "bitgc/%s/%s/sptg_ysjm_dp.json";

    @Override
    public void dealGoods() {
        super.dealGoods(SqliteTgcSptgSp.class, OSSKEY_PATTERN, ((DataprobiTgcMsg) getDataprobiBaseMsg()).getSptg());
    }

    @Override
    public boolean compareGoods() {
        super.dealGoods(SqliteTgcSptgSp.class, OSSKEY_PATTERN, ((DataprobiTgcMsg) getDataprobiBaseMsg()).getSptg());
        return false;
    }

    @Override
    protected <T extends AbstractSqliteSp> List<T> spList(Class<T> tClass, String ossKey, String rq) {
        Map<String, SqliteTgcSptgSp> map = new HashMap<>();
        //处理  预算加码 、 推广佣金 预估   商品维度
        dealStep1(map, ossKey);
        //处理 日预算 商品维度
        dealStep2(map, rq);
        //处理  周期预算  商品维度
        dealStep3(map, rq);

        for (Map.Entry<String, SqliteTgcSptgSp> entry : map.entrySet()) {
            SqliteTgcSptgSp sqliteTgcSptgSp = entry.getValue();
            sqliteTgcSptgSp.setBbid(entry.getKey());
            sqliteTgcSptgSp.setQd_userid_bbid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid() + "_" + sqliteTgcSptgSp.getBbid());
            sqliteTgcSptgSp.setRq(rq);
            sqliteTgcSptgSp.setYhid(getDataprobiBaseMsg().getYhid());
            sqliteTgcSptgSp.setQd(getDataprobiBaseMsg().getQd());
            sqliteTgcSptgSp.setUserid(getDataprobiBaseMsg().getUserid());
        }
        return (List<T>) new ArrayList<>(map.values());
    }

    private void dealStep1(Map<String, SqliteTgcSptgSp> map, String ossKey) {
        String json = biDataOssUtil.readJson(ossKey);
        if (!StringUtil.isJsonArray2(json)) {
            return;
        }

        JSONArray jsonArray = JSONArray.parseArray(json);
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            SqliteTgcSptgSp sqliteTgcSptgSp = new SqliteTgcSptgSp();
            String bbid = FastJSONObjAttrToNumber.toString(jsonObject, "itemId");
            sqliteTgcSptgSp.setSptg_tgyj_hf(FastJSONObjAttrToNumber.toDouble(jsonObject, "trCost"));
            sqliteTgcSptgSp.setSptg_ysjm_hf(FastJSONObjAttrToNumber.toDouble(jsonObject, "trChargeCost"));
            map.put(bbid, sqliteTgcSptgSp);
        }
    }

    private void dealStep2(Map<String, SqliteTgcSptgSp> map, String rq) {
        String ossKey = String.format(SPTG_RYY_OSS_PATTERN, getDataprobiBaseMsg().getUserid(), rq);
        String json = biDataOssUtil.readJson(ossKey);
        if (!StringUtil.isJsonArray2(json)) {
            return;
        }

        JSONArray jsonArray = JSONArray.parseArray(json);
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            String bbid = FastJSONObjAttrToNumber.toString(jsonObject, "itemId");
            SqliteTgcSptgSp sqliteTgcSptgSp = null;
            if (map.containsKey(bbid)) {
                sqliteTgcSptgSp = map.get(bbid);
            } else {
                sqliteTgcSptgSp = new SqliteTgcSptgSp();
            }
            sqliteTgcSptgSp.setSptg_ryy_hf(FastJSONObjAttrToNumber.toDouble(jsonObject, "cumulativeIndicatorData", "totalCost"));
            map.put(bbid, sqliteTgcSptgSp);
        }
    }


    private void dealStep3(Map<String, SqliteTgcSptgSp> map, String rq) {
        String ossKey = String.format(SPTG_ZQYY_OSS_PATTERN, getDataprobiBaseMsg().getUserid(), rq);
        String json = biDataOssUtil.readJson(ossKey);
        if (!StringUtil.isJsonArray2(json)) {
            return;
        }

        JSONArray jsonArray = JSONArray.parseArray(json);
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            String bbid = FastJSONObjAttrToNumber.toString(jsonObject, "itemId");

            SqliteTgcSptgSp sqliteTgcSptgSp = null;
            if (map.containsKey(bbid)) {
                sqliteTgcSptgSp = map.get(bbid);
            } else {
                sqliteTgcSptgSp = new SqliteTgcSptgSp();
            }

            sqliteTgcSptgSp.setSptg_zqyy_hf(FastJSONObjAttrToNumber.toDouble(jsonObject, "cumulativeIndicatorData", "totalCost"));
            map.put(bbid, sqliteTgcSptgSp);
        }
    }

    @Override
    protected <T extends AbstractSqliteSp> void dealShopByGoods(String rq, List<T> list) {
        double ryy_hf_sum = 0;
        double zqyy_hf_sum = 0;
        for (T t : list) {
            if (t instanceof SqliteTgcSptgSp) {
                ryy_hf_sum = MathUtil.add(ryy_hf_sum, ((SqliteTgcSptgSp) t).getSptg_ryy_hf());
                zqyy_hf_sum = MathUtil.add(zqyy_hf_sum, ((SqliteTgcSptgSp) t).getSptg_zqyy_hf());
            }
        }

        double ysjm_hf_sum = ysjm_dp_data(rq);
        double cost = dpdata(rq);

        SqliteTgcSptgDp sqliteTgcSptgDp = new SqliteTgcSptgDp();
        sqliteTgcSptgDp.setQd(getDataprobiBaseMsg().getQd());
        sqliteTgcSptgDp.setQd_userid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid());
        sqliteTgcSptgDp.setRq(rq);
        sqliteTgcSptgDp.setYhid(getDataprobiBaseMsg().getYhid());
        sqliteTgcSptgDp.setUserid(getDataprobiBaseMsg().getUserid());
        sqliteTgcSptgDp.setSptg_ryy_hf(ryy_hf_sum);
        sqliteTgcSptgDp.setSptg_ysjm_hf(ysjm_hf_sum);
        sqliteTgcSptgDp.setSptg_zqyy_hf(zqyy_hf_sum);
        sqliteTgcSptgDp.setSptg_tgyj_hf(MathUtil.minus(cost, MathUtil.add(ryy_hf_sum, zqyy_hf_sum, ysjm_hf_sum)));
        processSycmDpOTS(rq, sqliteTgcSptgDp);
    }

    private double ysjm_dp_data(String rq) {
        String dpKey = String.format(YSJM_DP_OSSKEY_PATTERN, getDataprobiBaseMsg().getUserid(), rq);
        String dpjson = biDataOssUtil.readJson(dpKey);
        if (!StringUtil.isJson2(dpjson)) {
            return 0;
        }
        JSONObject dpdata = JSONObject.parseObject(dpjson);
        return FastJSONObjAttrToNumber.toDouble(dpdata, "settleAmount");
    }

    private double dpdata(String rq) {
        String dpKey = String.format(DP_OSSKEY_PATTERN, getDataprobiBaseMsg().getUserid(), rq);
        String dpjson = biDataOssUtil.readJson(dpKey);
        if (!StringUtil.isJson2(dpjson)) {
            return 0;
        }
        JSONObject dpdata = JSONObject.parseObject(dpjson);
        return FastJSONObjAttrToNumber.toDouble(dpdata, "cost");
    }

}
