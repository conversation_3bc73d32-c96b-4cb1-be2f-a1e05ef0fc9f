package com.abujlb.dataprobi.processes.tgc;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.SpInit;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.tsdao.SpnewTsDao;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
@BiPro(order = 2, qd = Qd.TGC)
public class DefaultbiTgcSplbProcess extends AbstractBiprocess {

    public static final String OSSKEY_PATTERN = "bitgc/%s/cplb.json";

    @Autowired
    private SpnewTsDao spnewTsDao ;
    @Override
    public void dealGoods() {
        if(getDataprobiBaseMsg().getSplb()==0){
            return;
        }

        String osskey = String.format(OSSKEY_PATTERN, getDataprobiBaseMsg().getUserid());
        if (!biDataOssUtil.exist(osskey)) {
            return;
        }

        String json = biDataOssUtil.readJson(osskey);
        JSONArray jsonArray = JSONArray.parseArray(json);
        //处理sp表
        dealTGCSpData(jsonArray);
    }

    private void dealTGCSpData(JSONArray jsonArray) {
        List<SpInit> list = new ArrayList<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            SpInit item = new SpInit();
            item.setBbid(FastJSONObjAttrToNumber.toString(jsonObject, "bbid"));
            item.setBbmc(FastJSONObjAttrToNumber.toString(jsonObject, "bbmc"));
            item.setSjzt(FastJSONObjAttrToNumber.toInt(jsonObject, "sfsj"));
            item.setCid(FastJSONObjAttrToNumber.toString(jsonObject, "cid"));
            item.setCjsj(FastJSONObjAttrToNumber.toString(jsonObject, "cjsj"));
            item.setScsjsj(FastJSONObjAttrToNumber.toString(jsonObject, "scsjsj"));
            item.setYhid(getDataprobiBaseMsg().getYhid());
            item.setQd(getDataprobiBaseMsg().getQd());
            item.setUserid(getDataprobiBaseMsg().getUserid());

            list.add(item);
        }
        spnewTsDao.initSpV2(list);
    }
}
