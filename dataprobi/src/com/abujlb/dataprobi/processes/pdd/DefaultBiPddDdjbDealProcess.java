package com.abujlb.dataprobi.processes.pdd;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.pdd.DataprobiPddMsgBean;
import com.abujlb.dataprobi.bean.pdd.SqlitePddDdjbDp;
import com.abujlb.dataprobi.bean.pdd.SqlitePddDdjbSp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/6/9 11:26
 */
@Component
@BiPro(order = 3, qd = Qd.PDD)
public class DefaultBiPddDdjbDealProcess extends AbstractBiprocess {

    private final String OSSKEY_PATTERN = "bipdd/%s/%s/ddjb.json";
    private final String[] COLUMNS = {BiSycmDpDataTsDao.PDD_COLUMN_DDJB};

    @Override
    public void dealGoods() {
        super.dealGoods(
                SqlitePddDdjbSp.class,
                OSSKEY_PATTERN,
                ((DataprobiPddMsgBean) getDataprobiBaseMsg()).getDdjb()
        );
    }

    @Override
    public void dealShop() {
        super.dealShop(
                SqlitePddDdjbDp.class,
                ((DataprobiPddMsgBean) getDataprobiBaseMsg()).getDdjbdp(),
                COLUMNS
        );
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoods(
                SqlitePddDdjbSp.class,
                OSSKEY_PATTERN,
                ((DataprobiPddMsgBean) getDataprobiBaseMsg()).getDdjb()
        );
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqlitePddDdjbDp.class,
                ((DataprobiPddMsgBean) getDataprobiBaseMsg()).getDdjbdp(),
                COLUMNS
        );
    }
}
