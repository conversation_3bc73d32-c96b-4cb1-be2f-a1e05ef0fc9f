package com.abujlb.dataprobi.processes.pdd;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.pdd.DataprobiPddMsgBean;
import com.abujlb.dataprobi.bean.pdd.SqlitePddPjylDp;
import com.abujlb.dataprobi.bean.pdd.SqlitePddPjylSp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.util.MathUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/14
 */
@Component
@BiPro(order = 14, qd = Qd.PDD)
public class DefaultBiPddPjylProcess extends AbstractBiprocess {

    private final String OSSKEY_PATTERN = "bipdd/%s/%s/pjyl.json";

    @Override
    public void dealGoods() {
        super.dealGoods(
                SqlitePddPjylSp.class,
                OSSKEY_PATTERN,
                ((DataprobiPddMsgBean) getDataprobiBaseMsg()).getPjyl()
        );
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoods(
                SqlitePddPjylSp.class,
                OSSKEY_PATTERN,
                ((DataprobiPddMsgBean) getDataprobiBaseMsg()).getPjyl()
        );
    }

    @Override
    protected <T extends AbstractSqliteSp> void dealShopByGoods(String rq, List<T> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        SqlitePddPjylDp sqlitePddPjylDp = new SqlitePddPjylDp();
        sqlitePddPjylDp.setQd(getDataprobiBaseMsg().getQd());
        sqlitePddPjylDp.setQd_userid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid());
        sqlitePddPjylDp.setRq(rq);
        sqlitePddPjylDp.setYhid(getDataprobiBaseMsg().getYhid());
        sqlitePddPjylDp.setUserid(getDataprobiBaseMsg().getUserid());

        double pjylhf = 0;
        for (T t : list) {
            if (t instanceof SqlitePddPjylSp) {
                pjylhf = MathUtil.add(pjylhf, ((SqlitePddPjylSp) t).getPddpjylhf());
            }
        }
        sqlitePddPjylDp.setPddpjylhf(pjylhf);
        processSycmDpOTS(rq, sqlitePddPjylDp);

    }
}
