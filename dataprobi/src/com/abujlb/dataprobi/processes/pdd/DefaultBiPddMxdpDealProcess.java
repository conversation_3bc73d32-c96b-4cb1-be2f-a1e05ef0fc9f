package com.abujlb.dataprobi.processes.pdd;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.pdd.DataprobiPddMsgBean;
import com.abujlb.dataprobi.bean.pdd.SqlitePddMxdpDp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/6/9 11:26
 */
@Component
@BiPro(order = 7, qd = Qd.PDD)
public class DefaultBiPddMxdpDealProcess extends AbstractBiprocess {

    private final String[] COLUMNS = {BiSycmDpDataTsDao.PDD_COLUMN_MXDP};

    @Override
    public void dealShop() {
        super.dealShop(
                SqlitePddMxdpDp.class,
                ((DataprobiPddMsgBean) getDataprobiBaseMsg()).getMxdp(),
                COLUMNS
        );
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqlitePddMxdpDp.class,
                ((DataprobiPddMsgBean) getDataprobiBaseMsg()).getMxdp(),
                COLUMNS
        );
    }
}
