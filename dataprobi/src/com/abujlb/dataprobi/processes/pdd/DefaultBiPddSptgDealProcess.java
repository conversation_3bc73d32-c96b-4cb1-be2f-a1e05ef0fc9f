package com.abujlb.dataprobi.processes.pdd;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.pdd.DataprobiPddMsgBean;
import com.abujlb.dataprobi.bean.pdd.SqlitePddSptgDp;
import com.abujlb.dataprobi.bean.pdd.SqlitePddSptgSp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/3/1
 */
@Component
@BiPro(order = 12, qd = Qd.PDD)
public class DefaultBiPddSptgDealProcess extends AbstractBiprocess {

    private final String OSSKEY_PATTERN = "bipdd/%s/%s/sptg.json";
    private final String[] COLUMNS = {BiSycmDpDataTsDao.PDD_COLUMN_SPTG};

    @Override
    public void dealGoods() {
        super.dealGoods(
                SqlitePddSptgSp.class,
                OSSKEY_PATTERN,
                ((DataprobiPddMsgBean) getDataprobiBaseMsg()).getSptg()
        );
    }

    @Override
    public void dealShop() {
        super.dealShop(
                SqlitePddSptgDp.class,
                ((DataprobiPddMsgBean) getDataprobiBaseMsg()).getSptgdp(),
                COLUMNS
        );
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoods(
                SqlitePddSptgSp.class,
                OSSKEY_PATTERN,
                ((DataprobiPddMsgBean) getDataprobiBaseMsg()).getSptg()
        );
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqlitePddSptgDp.class,
                ((DataprobiPddMsgBean) getDataprobiBaseMsg()).getSptgdp(),
                COLUMNS
        );
    }
}
