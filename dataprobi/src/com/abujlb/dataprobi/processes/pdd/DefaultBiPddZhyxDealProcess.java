package com.abujlb.dataprobi.processes.pdd;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.pdd.DataprobiPddMsgBean;
import com.abujlb.dataprobi.bean.pdd.SqlitePddZhyxDp;
import com.abujlb.dataprobi.bean.pdd.SqlitePddZhyxSp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import com.abujlb.dataprobi.util.MathUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/24
 */
@Component
@BiPro(order = 7, qd = Qd.PDD)
public class DefaultBiPddZhyxDealProcess extends AbstractBiprocess {

    private final String[] COLUMNS = {BiSycmDpDataTsDao.PDD_COLUMN_ZHYX};

    @Override
    public void dealShop() {
        super.dealShop(
                SqlitePddZhyxDp.class,
                ((DataprobiPddMsgBean) getDataprobiBaseMsg()).getZhyxdp(),
                COLUMNS
        );

        //处理商品
        for (String rq : ((DataprobiPddMsgBean) getDataprobiBaseMsg()).getZhyxdp()) {
            dealGoods(rq);
        }
    }

    @Override
    public boolean compareShop() {
        boolean result = super.compareShop(
                SqlitePddZhyxDp.class,
                ((DataprobiPddMsgBean) getDataprobiBaseMsg()).getZhyxdp(),
                COLUMNS
        );

        if (!result) {
            for (String rq : ((DataprobiPddMsgBean) getDataprobiBaseMsg()).getZhyxdp()) {
                dealGoods(rq);
            }
        }
        return result;
    }

    private void dealGoods(String rq) {
        try {
            SqlitePddZhyxDp t = biUtil.dpObject(SqlitePddZhyxDp.class, rq, COLUMNS);
            if (t == null || t.getZhyxhf() == 0) {
                return;
            }

            String key = "bipdd/" + getDataprobiBaseMsg().getUserid() + "/" + rq + "/zhyx.json";
            if (!biDataOssUtil.exist(key)) {
                return;
            }

            List<String> goodsIdList = getGoodsIdList(key);
            if (CollectionUtils.isEmpty(goodsIdList)) {
                return;
            }

            List<SqlitePddZhyxSp> sqlitePddZhyxSps = new ArrayList<>(goodsIdList.size());
            double sphf = MathUtil.divide(t.getZhyxhf(), goodsIdList.size(), 8);
            for (String goodsId : goodsIdList) {
                SqlitePddZhyxSp sqlitePddZhyxSp = new SqlitePddZhyxSp();
                sqlitePddZhyxSp.setQd_userid_bbid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid() + "_" + goodsId);
                sqlitePddZhyxSp.setRq(rq);
                sqlitePddZhyxSp.setYhid(getDataprobiBaseMsg().getYhid());
                sqlitePddZhyxSp.setQd(getDataprobiBaseMsg().getQd());
                sqlitePddZhyxSp.setUserid(getDataprobiBaseMsg().getUserid());
                sqlitePddZhyxSp.setBbid(goodsId);
                sqlitePddZhyxSp.setZhyxhf(sphf);
                sqlitePddZhyxSps.add(sqlitePddZhyxSp);
            }

            goodsIdList.clear();
            processSycmSpOTS(rq, sqlitePddZhyxSps);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
    }

    private List<String> getGoodsIdList(String key) {
        String json = biDataOssUtil.readJson(key);
        List<String> goodsIdList = new ArrayList<>();
        JSONArray jsonArray = JSONArray.parseArray(json);
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            JSONArray goods = jsonObject.getJSONArray("goods");
            for (int j = 0; j < goods.size(); j++) {
                JSONObject good = goods.getJSONObject(j);
                String goodsId = good.getString("goodsId");
                if (!goodsIdList.contains(goodsId)) {
                    goodsIdList.add(goodsId);
                }
            }
        }
        return goodsIdList;
    }

}
