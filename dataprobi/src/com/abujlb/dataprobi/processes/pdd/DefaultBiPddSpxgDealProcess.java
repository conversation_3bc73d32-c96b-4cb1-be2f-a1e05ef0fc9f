package com.abujlb.dataprobi.processes.pdd;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.pdd.DataprobiPddMsgBean;
import com.abujlb.dataprobi.bean.pdd.SqlitePddSpxgDp;
import com.abujlb.dataprobi.bean.pdd.SqlitePddSpxgSp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/6/9 11:26
 */
@Component
@BiPro(order = 2, qd = Qd.PDD)
public class DefaultBiPddSpxgDealProcess extends AbstractBiprocess {

    private final String OSSKEY_PATTERN = "bipdd/%s/%s/spmx.json";
    private final String[] COLUMNS = {BiSycmDpDataTsDao.PDD_COLUMN_SPXG, BiSycmDpDataTsDao.PDD_COLUMN_SPXG_JYSJ, BiSycmDpDataTsDao.PDD_COLUMN_SPXG_SHSJ, BiSycmDpDataTsDao.PDD_COLUMN_SPXG_JYGK};

    @Override
    public void dealGoods() {
        super.dealGoods(SqlitePddSpxgSp.class,
                OSSKEY_PATTERN,
                ((DataprobiPddMsgBean) getDataprobiBaseMsg()).getSpxg()
        );
    }

    @Override
    public void dealShop() {
        super.dealShop(
                SqlitePddSpxgDp.class,
                ((DataprobiPddMsgBean) getDataprobiBaseMsg()).getSpxgdp(),
                COLUMNS
        );
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoods(
                SqlitePddSpxgSp.class,
                OSSKEY_PATTERN,
                ((DataprobiPddMsgBean) getDataprobiBaseMsg()).getSpxg()
        );
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqlitePddSpxgDp.class,
                ((DataprobiPddMsgBean) getDataprobiBaseMsg()).getSpxgdp(),
                COLUMNS
        );
    }

    @Override
    protected <T extends AbstractSqliteSp> void dealShopByGoods(String rq, List<T> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        int zfjs = list.stream().mapToInt(obj -> obj.getSpxgzfjs()).sum();
        int spscrs = (int) list.stream().mapToDouble(obj -> ((SqlitePddSpxgSp) obj).getSpxgscrs()).sum();
        if (zfjs != 0 || spscrs != 0) {
            String json = "{\"zfjs\":" + zfjs + ",\"scrs\":" + spscrs + "}";
            biSycmDpDataTsDao.updateRow(getDataprobiBaseMsg().getQd(), getDataprobiBaseMsg().getUserid(), rq, BiSycmDpDataTsDao.PDD_COLUMN_SPXG_JYGK, json);
        }
    }
}
