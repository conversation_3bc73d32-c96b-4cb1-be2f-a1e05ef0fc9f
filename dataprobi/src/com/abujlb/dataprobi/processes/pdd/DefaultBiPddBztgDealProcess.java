package com.abujlb.dataprobi.processes.pdd;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.pdd.DataprobiPddMsgBean;
import com.abujlb.dataprobi.bean.pdd.SqlitePddBztgDp;
import com.abujlb.dataprobi.bean.pdd.SqlitePddBztgSp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/11/3
 */
@Component
@BiPro(order = 11, qd = Qd.PDD)
public class DefaultBiPddBztgDealProcess extends AbstractBiprocess {

    private final String OSSKEY_PATTERN = "bipdd/%s/%s/bztg.json";
    private final String[] COLUMNS = {BiSycmDpDataTsDao.PDD_COLUMN_BZTG};

    @Override
    public void dealGoods() {
        super.dealGoods(
                SqlitePddBztgSp.class,
                OSSKEY_PATTERN,
                ((DataprobiPddMsgBean) getDataprobiBaseMsg()).getBztg()
        );
    }

    @Override
    public void dealShop() {
        super.dealShop(
                SqlitePddBztgDp.class,
                ((DataprobiPddMsgBean) getDataprobiBaseMsg()).getBztgdp(),
                COLUMNS
        );
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoods(
                SqlitePddBztgSp.class,
                OSSKEY_PATTERN,
                ((DataprobiPddMsgBean) getDataprobiBaseMsg()).getBztg()
        );
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqlitePddBztgDp.class,
                ((DataprobiPddMsgBean) getDataprobiBaseMsg()).getBztgdp(),
                COLUMNS
        );
    }
}
