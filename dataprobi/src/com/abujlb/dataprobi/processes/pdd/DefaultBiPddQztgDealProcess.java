package com.abujlb.dataprobi.processes.pdd;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.pdd.DataprobiPddMsgBean;
import com.abujlb.dataprobi.bean.pdd.SqlitePddQztgDp;
import com.abujlb.dataprobi.bean.pdd.SqlitePddQztgSp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/6/9 11:26
 */
@Component
@BiPro(order = 6, qd = Qd.PDD)
public class DefaultBiPddQztgDealProcess extends AbstractBiprocess {

    private final String OSSKEY_PATTERN = "bipdd/%s/%s/qztg.json";
    private final String[] COLUMNS = {BiSycmDpDataTsDao.PDD_COLUMN_QZTG};

    @Override
    public void dealGoods() {
        super.dealGoods(
                SqlitePddQztgSp.class,
                OSSKEY_PATTERN,
                ((DataprobiPddMsgBean) getDataprobiBaseMsg()).getQztg()
        );
    }

    @Override
    public void dealShop() {
        super.dealShop(
                SqlitePddQztgDp.class,
                ((DataprobiPddMsgBean) getDataprobiBaseMsg()).getQztgdp(),
                COLUMNS
        );
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoods(
                SqlitePddQztgSp.class,
                OSSKEY_PATTERN,
                ((DataprobiPddMsgBean) getDataprobiBaseMsg()).getQztg()
        );
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqlitePddQztgDp.class,
                ((DataprobiPddMsgBean) getDataprobiBaseMsg()).getQztgdp(),
                COLUMNS
        );
    }
}
