package com.abujlb.dataprobi.processes.pdd;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.SpInit;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.sql.BiSqliteDb;
import com.abujlb.dataprobi.tsdao.SpnewTsDao;
import com.abujlb.dataprobi.util.FileUtil;
import com.abujlb.dataprobi.util.SqliteDbUtil;
import com.abujlb.util.StringUtil;
import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/9/14 11:02
 */
@Component
@BiPro(order = 1, qd = Qd.PDD)
public class DefaultBiPddSplbDealProcess extends AbstractBiprocess {

    private final String OSSKEY_PATTERN = "bipdd/%s/splb.db";

    @Autowired
    private SpnewTsDao spnewTsDao ;

    @Override
    public void dealGoods() {
        if (getDataprobiBaseMsg().getSplb() != 1) {
            return;
        }

        String key = String.format(OSSKEY_PATTERN, getDataprobiBaseMsg().getUserid());
        if (!biDataOssUtil.exist(key)) {
            return;
        }

        BiSqliteDb biSqliteDb = null;
        Statement statement = null;
        try {
            String filePath = FileUtil.createDBFilePath();
            biDataOssUtil.download(key, filePath);

            biSqliteDb = new BiSqliteDb(filePath);
            statement = biSqliteDb.createStatement();

            List<SpInit> list = new ArrayList<>();
            int pageNo = 1;
            int pageSize = 100;
            while (true) {
                String sql = String.format("SELECT `data` from pdd_splb limit %d OFFSET %d;", pageSize, (pageNo - 1) * pageSize);
                ResultSet resultSet = statement.executeQuery(sql);

                while (resultSet.next()) {
                    String data = resultSet.getString("data");
                    if (!StringUtil.isJson2(data)) {
                        continue;
                    }
                    JSONObject jsonObject = JSONObject.parseObject(data);
                    SpInit spInit = new SpInit(getDataprobiBaseMsg().getQd(), jsonObject);
                    spInit.setYhid(getDataprobiBaseMsg().getYhid());
                    spInit.setUserid(getDataprobiBaseMsg().getUserid());
                    list.add(spInit);
                }

                spnewTsDao.initSp(list);
                if (list.size() < pageSize) {
                    list.clear();
                    break;
                }
                list.clear();
                pageNo++;
            }
        } catch (Throwable e) {
            LOG.error(e.getMessage(), e);
        } finally {
            SqliteDbUtil.closeStatement(statement);
            SqliteDbUtil.close(biSqliteDb);
        }
    }

    @Override
    public boolean compareGoods() {
        if (getDataprobiBaseMsg().getSplb() != 1) {
            return true;
        }
        dealGoods();
        return false;
    }
}
