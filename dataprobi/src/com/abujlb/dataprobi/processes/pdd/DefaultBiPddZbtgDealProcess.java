package com.abujlb.dataprobi.processes.pdd;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.pdd.DataprobiPddMsgBean;
import com.abujlb.dataprobi.bean.pdd.SqlitePddZbtgDp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/6/9 11:26
 */
@Component
@BiPro(order = 8, qd = Qd.PDD)
public class DefaultBiPddZbtgDealProcess extends AbstractBiprocess {

    private final String[] COLUMNS = {BiSycmDpDataTsDao.PDD_COLUMN_ZBTG};

    @Override
    public void dealShop() {
        super.dealShop(
                SqlitePddZbtgDp.class,
                ((DataprobiPddMsgBean) getDataprobiBaseMsg()).getZbtg(),
                COLUMNS
        );
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqlitePddZbtgDp.class,
                ((DataprobiPddMsgBean) getDataprobiBaseMsg()).getZbtg(),
                COLUMNS
        );
    }
}
