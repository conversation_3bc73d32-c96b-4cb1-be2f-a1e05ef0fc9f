package com.abujlb.dataprobi.processes.pdd;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.pdd.DataprobiPddMsgBean;
import com.abujlb.dataprobi.bean.pdd.SqlitePddHbdkDp;
import com.abujlb.dataprobi.bean.pdd.SqlitePddZbtgDp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/8/29 11:26
 * @desc 红包抵扣金额
 */
@Component
@BiPro(order = 15, qd = Qd.PDD)
public class DefaultBiPddHbdkDealProcess extends AbstractBiprocess {

    private final String[] COLUMNS = {BiSycmDpDataTsDao.PDD_COLUMN_HBDK};

    @Override
    public void dealShop() {
        super.dealShop(
                SqlitePddHbdkDp.class,
                ((DataprobiPddMsgBean) getDataprobiBaseMsg()).getHbdk(),
                COLUMNS
        );
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqlitePddHbdkDp.class,
                ((DataprobiPddMsgBean) getDataprobiBaseMsg()).getHbdk(),
                COLUMNS
        );
    }
}
