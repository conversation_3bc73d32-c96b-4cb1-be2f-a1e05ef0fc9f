package com.abujlb.dataprobi.processes.dw;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteDp;
import com.abujlb.dataprobi.bean.dw.DataprobiDwMsg;
import com.abujlb.dataprobi.bean.dw.SqliteDwYltfCzjlDp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.util.ExcelReaderUtil;
import com.abujlb.dataprobi.util.FileUtil;
import com.abujlb.dataprobi.util.MathUtil;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @packageName com.abujlb.dataprobi.processes.dw
 * @ClassName DefaultBiDwYltfCzjlProcess
 * @Description 得物引力 -> 钱包 -> 我的钱包->充值记录
 * <AUTHOR>
 * @Date 2024/11/26
 */
@Component
@BiPro(order = 8, qd = Qd.DW)
public class DefaultBiDwYltfCzjlProcess extends AbstractBiprocess {
    private final String OSSKEY_PATTERN = "bi_dw/%s/%s/yltfCzjl.xlsx";
    private final List<String> CZFS = Lists.newArrayList("精选补贴返现激励", "品牌直发-奖励金额", "其他");


    @Override
    public void dealShop() {
        super.dealShop(
                SqliteDwYltfCzjlDp.class,
                ((DataprobiDwMsg) getDataprobiBaseMsg()).getYltfCzjl(),
                StringUtils.EMPTY
        );
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqliteDwYltfCzjlDp.class,
                ((DataprobiDwMsg) getDataprobiBaseMsg()).getYltfCzjl(),
                StringUtils.EMPTY
        );
    }

    @Override
    protected <T extends AbstractSqliteDp> T dpObject(Class<T> tClass, String rq, String... colummns) {
        try {
            String osskey = String.format(OSSKEY_PATTERN, getDataprobiBaseMsg().getUserid(), rq);
            if (!biDataOssUtil.exist(osskey)) {
                return null;
            }
            String filePath = FileUtil.createXlsxFilePath();
            biDataOssUtil.download(osskey, filePath);
            //获取 日期下的数据 处理
            List<List<String>> lists = ExcelReaderUtil.readExcel(filePath, 0, 0);
            List<List<String>> data = lists.stream().filter(e -> StrUtil.equals(rq, LocalDateTimeUtil.parse(e.get(0), DatePattern.NORM_DATETIME_PATTERN).toLocalDate().format(DatePattern.NORM_DATE_FORMATTER))).filter(y -> CZFS.contains(y.get(3))).collect(Collectors.toList());
            if (CollUtil.isEmpty(data)) {
                return null;
            }
            double yltfJlj = 0;
            for (int i = 0; i < data.size(); i++) {
                String je = data.get(i).get(2);
                if (StrUtil.isBlank(je)) {
                    continue;
                }
                String je1 = StrUtil.replace(je, "元", "");
                if (StrUtil.isBlank(je1)) {
                    continue;
                }
                yltfJlj=MathUtil.add(yltfJlj, NumberUtil.parseDouble(je1));
            }
            if (yltfJlj != 0) {
                SqliteDwYltfCzjlDp czjl = new SqliteDwYltfCzjlDp();
                czjl.setQd(getDataprobiBaseMsg().getQd());
                czjl.setQd_userid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid());
                czjl.setRq(rq);
                czjl.setYhid(getDataprobiBaseMsg().getYhid());
                czjl.setUserid(getDataprobiBaseMsg().getUserid());
                //2024-10-25 按照陈达的约定，收入项就提供负数，支出项就提供正数
                czjl.setYltfJlj(-yltfJlj);
                return (T) czjl;
            }
        } catch (Exception e) {
            logger.error("引力投放充值记录处理失败！" + e);
        }
        return null;
    }
}
