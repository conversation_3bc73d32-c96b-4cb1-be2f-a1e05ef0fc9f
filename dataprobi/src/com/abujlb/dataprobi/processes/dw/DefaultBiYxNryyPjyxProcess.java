package com.abujlb.dataprobi.processes.dw;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteDp;
import com.abujlb.dataprobi.bean.dw.DataprobiDwMsg;
import com.abujlb.dataprobi.bean.dw.SqliteDwYxnryxPjyxDp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @packageName com.abujlb.zdcjbidw.processes
 * @ClassName DefaultBiYxNryyPjyxProcess
 * @Description 营销 -> 内容运营 -> 评价营销 采集
 * <AUTHOR>
 * @Date 2024/11/25
 */
@Component
@BiPro(order = 5, qd = Qd.DW)
public class DefaultBiYxNryyPjyxProcess extends AbstractBiprocess {
    public static final String OSSKEY_PATTERN = "bi_dw/%s/%s/yxnryypjyxdp.json";

    @Override
    public void dealShop() {
        super.dealShop(
                SqliteDwYxnryxPjyxDp.class,
                ((DataprobiDwMsg) getDataprobiBaseMsg()).getYxnryypjyx(),
                StringUtils.EMPTY
        );
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqliteDwYxnryxPjyxDp.class,
                ((DataprobiDwMsg) getDataprobiBaseMsg()).getYxnryypjyx(),
                StringUtils.EMPTY
        );
    }

    @Override
    protected <T extends AbstractSqliteDp> T dpObject(Class<T> tClass, String rq, String... colummns) {
        try {
            T t = tClass.newInstance();
            t.setQd(getDataprobiBaseMsg().getQd());
            t.setQd_userid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid());
            t.setRq(rq);
            t.setYhid(getDataprobiBaseMsg().getYhid());
            t.setUserid(getDataprobiBaseMsg().getUserid());
            String osskey = String.format(OSSKEY_PATTERN, getDataprobiBaseMsg().getUserid(), rq);
            if (!biDataOssUtil.exist(osskey)) {
                return t;
            }
            String json = biDataOssUtil.readJson(osskey);
            if (StringUtils.isBlank(json)) {
                return t;
            }
            List<JSONObject> list = JSONUtil.toList(json, JSONObject.class);
            if (CollUtil.isEmpty(list)) {
                return t;
            }
            com.alibaba.fastjson.JSONObject jsonObject = new com.alibaba.fastjson.JSONObject();
            jsonObject.put("data", list);
            t.setValues(jsonObject);
            return t;
        } catch (Exception e) {
            logger.error("评价营销推广花费处理失败！" + e);
        }
        return null;
    }
}
