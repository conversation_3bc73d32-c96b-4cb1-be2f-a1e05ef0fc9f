package com.abujlb.dataprobi.processes.dw;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteDp;
import com.abujlb.dataprobi.bean.dw.DataprobiDwMsg;
import com.abujlb.dataprobi.bean.dw.SqliteDwYltfXhjlDp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.util.ExcelReaderUtil;
import com.abujlb.dataprobi.util.FileUtil;
import com.abujlb.dataprobi.util.MathUtil;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @packageName com.abujlb.dataprobi.processes.dw
 * @ClassName DefaultBiDwYltfXhjlProcess
 * @Description 得物引力 -> 钱包 -> 我的钱包-> 消费记录
 * <AUTHOR>
 * @Date 2024/11/26
 */
@Component
@BiPro(order = 7, qd = Qd.DW)
public class DefaultBiDwYltfXhjlProcess extends AbstractBiprocess {
    private final String OSSKEY_PATTERN = "bi_dw/%s/%s/yltfxhjl.xlsx";
    private final List<String> JYZT = Lists.newArrayList("支付(扣款)", "财务调减(扣款)");


    @Override
    public void dealShop() {
        super.dealShop(
                SqliteDwYltfXhjlDp.class,
                ((DataprobiDwMsg) getDataprobiBaseMsg()).getYltfXhjl(),
                StringUtils.EMPTY
        );
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqliteDwYltfXhjlDp.class,
                ((DataprobiDwMsg) getDataprobiBaseMsg()).getYltfXhjl(),
                StringUtils.EMPTY
        );
    }

    @Override
    protected <T extends AbstractSqliteDp> T dpObject(Class<T> tClass, String rq, String... colummns) {
        try {
            String osskey = String.format(OSSKEY_PATTERN, getDataprobiBaseMsg().getUserid(), rq);
            if (!biDataOssUtil.exist(osskey)) {
                return null;
            }
            String filePath = FileUtil.createXlsxFilePath();
            biDataOssUtil.download(osskey, filePath);
            //获取 日期下的数据 处理
            List<List<String>> lists = ExcelReaderUtil.readExcel(filePath, 0, 0);
            List<List<String>> data = lists.stream().filter(e -> StrUtil.equals(rq, LocalDateTimeUtil.parse(e.get(4), DatePattern.NORM_DATETIME_PATTERN).toLocalDate().format(DatePattern.NORM_DATE_FORMATTER))).filter(y -> JYZT.contains(y.get(5))).collect(Collectors.toList());
            if (CollUtil.isEmpty(data)) {
                return null;
            }
            double yltfXhhf = 0;
            for (int i = 0; i < data.size(); i++) {
                String jyje = data.get(i).get(6);
                if (StrUtil.isBlank(jyje)) {
                    continue;
                }
                String jyje1 = StrUtil.replace(jyje, "元", "");
                if (StrUtil.isBlank(jyje1)) {
                    continue;
                }
                yltfXhhf=MathUtil.add(yltfXhhf, NumberUtil.parseDouble(jyje1));
            }
            if (yltfXhhf != 0) {
                SqliteDwYltfXhjlDp xhjl = new SqliteDwYltfXhjlDp();
                xhjl.setQd(getDataprobiBaseMsg().getQd());
                xhjl.setQd_userid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid());
                xhjl.setRq(rq);
                xhjl.setYhid(getDataprobiBaseMsg().getYhid());
                xhjl.setUserid(getDataprobiBaseMsg().getUserid());
                xhjl.setYltfXhhf(yltfXhhf);
                return (T) xhjl;
            }
        } catch (Exception e) {
            logger.error("引力投放花费处理失败！" + e);
        }
        return null;
    }
}
