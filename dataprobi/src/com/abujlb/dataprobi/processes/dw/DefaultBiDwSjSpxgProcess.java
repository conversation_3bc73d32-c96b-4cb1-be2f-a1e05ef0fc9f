package com.abujlb.dataprobi.processes.dw;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.dw.DataprobiDwMsg;
import com.abujlb.dataprobi.bean.dw.SqliteDwSpfxDp;
import com.abujlb.dataprobi.bean.dw.SqliteDwSpfxSp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import org.springframework.stereotype.Component;

/**
 * @packageName com.abujlb.dataprobi.processes.dw
 * @ClassName DefaultBiSjSpfxProcess
 * @Description 数据 -> 商品分析
 * <AUTHOR>
 * @Date 2024/10/16
 */
@Component
@BiPro(order = 1, qd = Qd.DW)
public class DefaultBiDwSjSpxgProcess extends AbstractBiprocess {
    public static final String OSSKEY_PATTERN = "bi_dw/%s/%s/spfx.json";
    private final String[] COLUMNS = {BiSycmDpDataTsDao.DW_COLUMN_SJSPFXDP};

    @Override
    public void dealGoods() {
        super.dealGoods(
                SqliteDwSpfxSp.class,
                OSSKEY_PATTERN,
                ((DataprobiDwMsg) getDataprobiBaseMsg()).getSjspfx()
        );
    }

    @Override
    public void dealShop() {
        super.dealShop(
                SqliteDwSpfxDp.class,
                ((DataprobiDwMsg) getDataprobiBaseMsg()).getSjspfxdp(),
                COLUMNS
        );
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoods(
                SqliteDwSpfxSp.class,
                OSSKEY_PATTERN,
                ((DataprobiDwMsg) getDataprobiBaseMsg()).getSjspfx()
        );
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqliteDwSpfxDp.class,
                ((DataprobiDwMsg) getDataprobiBaseMsg()).getSjspfxdp(),
                COLUMNS
        );
    }

}
