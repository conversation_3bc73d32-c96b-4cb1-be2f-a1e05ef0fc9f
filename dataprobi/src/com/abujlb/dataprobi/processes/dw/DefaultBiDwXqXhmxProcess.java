package com.abujlb.dataprobi.processes.dw;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteDp;
import com.abujlb.dataprobi.bean.dw.DataprobiDwMsg;
import com.abujlb.dataprobi.bean.dw.SqliteDwXqXhmxDp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.util.MathUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @packageName com.abujlb.dataprobi.processes.dw
 * @ClassName DefaultBiDwXqFwddProcess
 * @Description 星桥-消耗明细
 * <AUTHOR>
 * @Date 2024/11/26
 */
@Component
@BiPro(order = 2, qd = Qd.DW)
public class DefaultBiDwXqXhmxProcess extends AbstractBiprocess {
    public static final String OSSKEY_PATTERN = "bi_dw/%s/%s/xqXhmx.json";

    @Override
    public void dealShop() {
        super.dealShop(
                SqliteDwXqXhmxDp.class,
                ((DataprobiDwMsg) getDataprobiBaseMsg()).getXqxhmx(),
                StringUtils.EMPTY);
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqliteDwXqXhmxDp.class,
                ((DataprobiDwMsg) getDataprobiBaseMsg()).getXqxhmx(),
                StringUtils.EMPTY);
    }

    @Override
    protected <T extends AbstractSqliteDp> T dpObject(Class<T> tClass, String rq, String... colummns) {
        try {
            String osskey = String.format(OSSKEY_PATTERN, getDataprobiBaseMsg().getUserid(), rq);
            if (!biDataOssUtil.exist(osskey)) {
                return null;
            }
            String json = biDataOssUtil.readJson(osskey);
            if (StringUtils.isBlank(json)) {
                return null;
            }
            List<JSONObject> list = JSONUtil.toList(json, JSONObject.class);
            if (CollUtil.isEmpty(list)) {
                return null;
            }
            double dwXqTghf = 0;
            double dwXqQyj = 0;
            double dwXqFlj = 0;
            for (int i = 0; i < list.size(); i++) {
                JSONObject jsonObject = list.get(i);
                double tghf = NumberUtil.div(jsonObject.getDouble("amount").doubleValue(), 100);
                double qyj = NumberUtil.div(jsonObject.getDouble("equityDetail").doubleValue(), 100);
                double flj = NumberUtil.div(jsonObject.getDouble("rebateDetail").doubleValue(), 100);
                dwXqTghf=MathUtil.add(dwXqTghf, tghf);
                dwXqQyj=MathUtil.add(dwXqQyj, qyj);
                dwXqFlj=MathUtil.add(dwXqFlj, flj);
            }
            if (dwXqTghf != 0 || dwXqQyj != 0 || dwXqFlj != 0) {
                SqliteDwXqXhmxDp xqXhjl = new SqliteDwXqXhmxDp();
                xqXhjl.setQd(getDataprobiBaseMsg().getQd());
                xqXhjl.setQd_userid(getDataprobiBaseMsg().getQd() + "_" + getDataprobiBaseMsg().getUserid());
                xqXhjl.setRq(rq);
                xqXhjl.setYhid(getDataprobiBaseMsg().getYhid());
                xqXhjl.setUserid(getDataprobiBaseMsg().getUserid());
                xqXhjl.setDwXqTghf(dwXqTghf);
                //2024-10-25 按照陈达的约定，收入项就提供负数，支出项就提供正数
                xqXhjl.setDwXqQyj(-dwXqQyj);
                xqXhjl.setDwXqFlj(-dwXqFlj);
                return (T) xqXhjl;
            }
        } catch (Exception e) {
            logger.error("星桥推广花费处理失败！" + e);
        }
        return null;
    }
}
