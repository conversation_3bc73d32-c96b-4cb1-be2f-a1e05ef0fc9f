package com.abujlb.dataprobi.processes.dw;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.dw.DataprobiDwMsg;
import com.abujlb.dataprobi.bean.dw.SqliteDwYxDwtCwlsDp;
import com.abujlb.dataprobi.bean.dw.SqliteDwYxDwtTgsjDp;
import com.abujlb.dataprobi.bean.dw.SqliteDwYxDwtTgsjSp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import org.springframework.stereotype.Component;

/**
 * @packageName com.abujlb.dataprobi.processes.dw
 * @ClassName DefaultBiDwYxDwtCwlsProcess
 * @Description 营销 -》得物推 -> 财务流水
 * <AUTHOR>
 * @Date 2024/11/25
 */
@Component
@BiPro(order = 6, qd = Qd.DW)
public class DefaultBiDwYxDwtCwlsProcess extends AbstractBiprocess {
    private final String[] COLUMNS = {BiSycmDpDataTsDao.DW_COLUMN_YXDWTCWLSDP};



    @Override
    public void dealShop() {
        super.dealShop(
                SqliteDwYxDwtCwlsDp.class,
                ((DataprobiDwMsg) getDataprobiBaseMsg()).getYxdwtcwls(),
                COLUMNS
        );
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqliteDwYxDwtCwlsDp.class,
                ((DataprobiDwMsg) getDataprobiBaseMsg()).getYxdwtcwls(),
                COLUMNS
        );
    }
}
