package com.abujlb.dataprobi.processes.dw;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.DataprobiBaseMsgBean;
import com.abujlb.dataprobi.bean.dw.DataprobiDwMsg;
import com.abujlb.dataprobi.bean.dw.JsonRootBean;
import com.abujlb.dataprobi.bean.dw.SqliteDwYxDwtTgsjDp;
import com.abujlb.dataprobi.bean.dw.SqliteDwYxDwtTgsjSp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.interfaces.Plusable;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import com.abujlb.dataprobi.threads.BiThreadLocals;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import com.abujlb.dataprobi.util.ExcelReaderUtil;
import com.abujlb.dataprobi.util.FileUtil;
import com.alibaba.fastjson.JSONObject;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @packageName com.abujlb.dataprobi.processes.dw
 * @ClassName DefaultBiYxDwtTgsjProcess
 * @Description 营销 -》得物推 -> 推广数据
 * <AUTHOR>
 * @Date 2024/10/16
 */
@Component
@BiPro(order = 4, qd = Qd.DW)
public class DefaultBiDwYxDwtTgsjProcess extends AbstractBiprocess {
    //    分页采集的数据
    //        public static final String OSSKEY_PATTERN = "bi_dw/%s/%s/yxdwttgsj.json";
//    导出采集的数据
    public static final String OSSKEY_PATTERN = "bi_dw/%s/%s/yxdwttgsj.xlsx";
    private final String[] COLUMNS = {BiSycmDpDataTsDao.DW_COLUMN_YXDWTTGSJDP};

    @Override
    public void dealGoods() {
        super.dealGoods(
                SqliteDwYxDwtTgsjSp.class,
                OSSKEY_PATTERN,
                ((DataprobiDwMsg) getDataprobiBaseMsg()).getYxdwttgsj()
        );
    }

    @Override
    public void dealShop() {
        super.dealShop(
                SqliteDwYxDwtTgsjDp.class,
                ((DataprobiDwMsg) getDataprobiBaseMsg()).getYxdwttgsjdp(),
                COLUMNS
        );
    }

    @Override
    public boolean compareGoods() {
        return super.compareGoods(
                SqliteDwYxDwtTgsjSp.class,
                OSSKEY_PATTERN,
                ((DataprobiDwMsg) getDataprobiBaseMsg()).getYxdwttgsj()
        );
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqliteDwYxDwtTgsjDp.class,
                ((DataprobiDwMsg) getDataprobiBaseMsg()).getYxdwttgsjdp(),
                COLUMNS
        );
    }

    //使用导出的方式采集   当使用分页的时候这里不需要重写此方法
    @Override
    protected <T extends AbstractSqliteSp> List<T> spList(Class<T> tClass, String ossKey, String rq) {
        try {
            //解析xlsx  获取商品id和花费
            if (!biDataOssUtil.exist((ossKey))) {
                return Collections.emptyList();
            }
            String temppath = FileUtil.createXlsxFilePath();
            biDataOssUtil.download(ossKey, temppath);
            List<List<String>> lists = ExcelReaderUtil.readExcel(temppath, 0, 1);
            List<List<String>> data = lists.stream().filter(e -> StrUtil.equals(DateUtil.parse(rq, DatePattern.NORM_DATE_PATTERN).toString("MM/dd"), e.get(0))).collect(Collectors.toList());
            if (CollUtil.isEmpty(data)) {
                Collections.emptyList();
            }
            DataprobiBaseMsgBean msgBean = BiThreadLocals.getMsgBean();

            JSONObject json = null;
            Map<String, T> bb = new HashMap<>();
            for (int i = 0; i < data.size(); i++) {
                List<String> strings = data.get(i);
                JsonRootBean jsonRootBean = new JsonRootBean();
                jsonRootBean.init(strings, rq);
                json = JSONObject.parseObject(JSONUtil.parseObj(jsonRootBean).toString());
                T t = tClass.newInstance();
                t.setValues(json);
                t.setQd_userid_bbid(msgBean.getQd() + "_" + msgBean.getUserid() + "_" + t.getBbid());
                t.setRq(rq);
                t.setYhid(msgBean.getYhid());
                t.setQd(msgBean.getQd());
                t.setUserid(msgBean.getUserid());
                T t1 = bb.get(t.getBbid());
                if (Objects.nonNull(t1)) {
                    t.plus(t1);
                }
                bb.put(t.getBbid(), t);
            }
            List<T> list = bb.values().stream().collect(Collectors.toList());
            Class<?>[] interfaces = tClass.getInterfaces();
            boolean plus = false;
            for (Class<?> anInterface : interfaces) {
                if (anInterface == Plusable.class) {
                    plus = true;
                    break;
                }
            }

            if (!plus) {
                return list;
            }

            Map<String, T> map = new HashMap<>();
            for (T t : list) {
                T temp = null;
                if (map.containsKey(t.getBbid())) {
                    temp = map.get(t.getBbid());
                } else {
                    temp = tClass.newInstance();
                    temp.setUserid(t.getUserid());
                    temp.setYhid(t.getYhid());
                    temp.setQd(t.getQd());
                    temp.setQd_userid_bbid(t.getQd_userid_bbid());
                    temp.setBbid(t.getBbid());
                }
                tClass.getMethod("plus", tClass).invoke(temp, t);
                map.put(t.getBbid(), temp);
            }

            return new ArrayList<>(map.values());
        } catch (Exception e) {
            logger.error("得物推商品推广数据处理失败！" + e);
            return Collections.emptyList();
        }
    }
}
