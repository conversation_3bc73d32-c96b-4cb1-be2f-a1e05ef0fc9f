package com.abujlb.dataprobi.processes.dw;

import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.bean.dw.DataprobiDwMsg;
import com.abujlb.dataprobi.bean.dw.SqliteDwYxZbtgDp;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.processes.AbstractBiprocess;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * @packageName com.abujlb.dataprobi.processes.dw
 * @ClassName DefaultBiYxZbtgProcess
 * @Description 营销 -> 直播推广采集
 * <AUTHOR>
 * @Date 2024/10/16
 */
@Component
@BiPro(order = 3, qd = Qd.DW)
public class DefaultBiDwYxZbtgProcess extends AbstractBiprocess {
    public static final String OSSKEY_PATTERN = "bi_dw/%s/%s/yxzbtg.json";

    @Override
    public void dealShop() {
        super.dealShop(
                SqliteDwYxZbtgDp.class,
                ((DataprobiDwMsg) getDataprobiBaseMsg()).getYxzbtg(),
                StringUtils.EMPTY
        );
    }

    @Override
    public boolean compareShop() {
        return super.compareShop(
                SqliteDwYxZbtgDp.class,
                ((DataprobiDwMsg) getDataprobiBaseMsg()).getYxzbtg(),StringUtils.EMPTY
        );
    }
}
