package com.abujlb.dataprobi.threads;

import com.abujlb.dataprobi.bean.*;
import com.abujlb.dataprobi.bean.dy.jlqc.BiJlqcAvvid;
import com.abujlb.dataprobi.bean.mn.BiSupplier;
import com.abujlb.dataprobi.bean.tgc.BiTgcAiztone;
import com.alibaba.fastjson.JSONObject;
import org.apache.log4j.Logger;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.TreeSet;
import java.util.concurrent.locks.ReentrantLock;

/**
 * <AUTHOR>
 * @date 2022/11/10 16:45
 */
public class BiThreadLocals {

    public static ThreadLocal<ThreadLocalObjects> THREADLOCALS = new ThreadLocal<>();

    static final ReentrantLock lock = new ReentrantLock();

    private static final Logger log = Logger.getLogger(BiThreadLocals.class);

    public static <T extends DataprobiBaseMsgBean> void init(T t) {
        try {
            lock.lock();
            ThreadLocalObjects objects = new ThreadLocalObjects();
            objects.setDataprobiBaseMsgBean(t);
            THREADLOCALS.set(objects);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            lock.unlock();
        }
    }

    public static DataprobiBaseMsgBean getMsgBean() {
        return THREADLOCALS.get().getDataprobiBaseMsgBean();
    }

    public static void addBiInfo(BiInfo biInfo) {
        THREADLOCALS.get().setBiInfo(biInfo);
    }

    public static BiInfo getBiInfo() {
        return THREADLOCALS.get().getBiInfo();
    }

    public static void addBiYhdp(BiYhdp biYhdp) {
        THREADLOCALS.get().setBiYhdp(biYhdp);
    }

    public static BiYhdp getBiYhdp() {
        return THREADLOCALS.get().getBiYhdp();
    }

    public static void remove() {
        THREADLOCALS.remove();
    }

    public static void addAllDates(TreeSet<String> allDates) {
        if (Objects.nonNull(allDates)) {
            THREADLOCALS.get().setAllDates(allDates);
        }
    }

    public static TreeSet<String> getAllDates() {
        return THREADLOCALS.get().getAllDates();
    }


    public static void addCsAllDates(TreeSet<String> allDates) {
        if (Objects.nonNull(allDates)) {
            THREADLOCALS.get().setCs_dates(allDates);
        }
    }

    public static TreeSet<String> getCsAllDates() {
        return THREADLOCALS.get().getCs_dates();
    }

    public static void addMnMetaRule(JSONObject jsonObject) {
        THREADLOCALS.get().setMnMetaRules(jsonObject);
    }

    public static JSONObject getMnMetaRule() {
        return THREADLOCALS.get().getMnMetaRules();
    }

    public static void addSupplier(BiSupplier biSupplier) {
        THREADLOCALS.get().setBiSupplier(biSupplier);
    }

    public static BiSupplier getSupplier() {
        return THREADLOCALS.get().getBiSupplier();
    }

    public static boolean getTestYh() {
        if (THREADLOCALS.get() == null) {
            return false;
        }
        return THREADLOCALS.get().isTestYh();
    }

    public static void setTestYh() {
        THREADLOCALS.get().setTestYh(true);
    }

    public static void addReRunTask(BiRuleReRunTask biRuleReRunTask) {
        if (THREADLOCALS.get() != null) {
            THREADLOCALS.get().setBiRuleReRunTask(biRuleReRunTask);
        }
    }

    public static BiRuleReRunTask getRerunTask() {
        return THREADLOCALS.get() == null ? null : THREADLOCALS.get().getBiRuleReRunTask();
    }

    public static void addDyJlqcAvvidList(List<BiJlqcAvvid> list) {
        if (THREADLOCALS.get() != null) {
            THREADLOCALS.get().setJlqcAvvidList(list);
        }
    }

    public static List<BiJlqcAvvid> getDyJlqcList() {
        if (THREADLOCALS.get() != null) {
            return THREADLOCALS.get().getJlqcAvvidList() == null ? Collections.emptyList() : THREADLOCALS.get().getJlqcAvvidList();
        }
        return Collections.emptyList();
    }

    public static void addTgcAiztoneList(List<BiTgcAiztone> list) {
        if (THREADLOCALS.get() != null) {
            THREADLOCALS.get().setTgcAiztoneList(list);
        }
    }

    public static List<BiTgcAiztone> getTgcAiztoneList() {
        if (THREADLOCALS.get() != null) {
            return THREADLOCALS.get().getTgcAiztoneList() == null ? Collections.emptyList() : THREADLOCALS.get().getTgcAiztoneList();
        }
        return Collections.emptyList();
    }

    public static void setMsgLx(int msgLx) {
        if (THREADLOCALS.get() != null) {
            THREADLOCALS.get().setMsgLx(msgLx);
        }
    }

    public static int getMsgLx() {
        if (THREADLOCALS.get() != null) {
            return THREADLOCALS.get().getMsgLx();
        }
        return 0;
    }
}



