package com.abujlb.dataprobi.enums.dw;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @packageName com.abujlb.dataprobi.enums.dw
 * @ClassName AdTypeEnum
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/10/30
 */
public enum AdTypeEnum {
    ADTYPE1(1, "站外信息服务"),
    ADTYPE2(2, "站外达人内容投放"),
    ADTYPE3(3, "GPS(原站内互动玩法)"),
    ADTYPE4(4, "得物App消息推送(push)"),
    ADTYPE5(5, "得物App优惠券推送(智能发券)"),
    ;

    AdTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private Integer code;
    private String desc;

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static AdTypeEnum getByCode(Integer code) {
        Map<Integer, AdTypeEnum> map = Arrays.stream(AdTypeEnum.values()).collect(Collectors.toMap(AdTypeEnum::getCode, Function.identity()));
        return map.get(code);
    }
}
