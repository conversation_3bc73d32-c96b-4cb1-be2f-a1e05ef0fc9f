package com.abujlb.dataprobi.util;

import com.abujlb.dataprobi.bean.jd.SqliteJdJtkZdSp;
import org.apache.log4j.Logger;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.File;
import java.io.InputStream;
import java.nio.file.Files;
import java.util.*;

public class JtkXlsReader {
    static final Logger LOG = Logger.getLogger(JtkXlsReader.class);

    public static int sheetNum(String temppath) {
        InputStream is = null;
        File file = null;
        try {
            file = new File(temppath);
            is = Files.newInputStream(file.toPath());
            Workbook wb = new XSSFWorkbook(is);
            return wb.getNumberOfSheets();
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        } finally {
            try {
                is.close();
            } catch (Exception e) {
            }
        }
        return -1;
    }

    public static List<SqliteJdJtkZdSp> read(String temppath) {
        Map<Long, SqliteJdJtkZdSp> map = new HashMap<>();
        InputStream is = null;
        File file = null;
        try {
            file = new File(temppath);
            is = Files.newInputStream(file.toPath());
            Workbook wb = new XSSFWorkbook(is);

            int numberOfSheets = wb.getNumberOfSheets();
            if (numberOfSheets > 0) {
                Sheet sheet0 = wb.getSheetAt(0);
                if (sheet0.getSheetName().contains("扣费")) {
                    readCommission(wb, 0, map);
                } else if (sheet0.getSheetName().contains("返款")) {
                    readRefund(wb, 0, map);
                }
            }
            if (numberOfSheets > 1) {
                Sheet sheet1 = wb.getSheetAt(1);
                if (sheet1.getSheetName().contains("扣费")) {
                    readCommission(wb, 1, map);
                } else if (sheet1.getSheetName().contains("返款")) {
                    readRefund(wb, 1, map);
                }
            }
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
            return null;
        } finally {
            try {
                is.close();
            } catch (Exception e) {
            }
            if (file != null && file.exists()) {
                file.delete();
            }
        }
        return new ArrayList<>(map.values());
    }

    private static void readRefund(Workbook wb, int sheetNum, Map<Long, SqliteJdJtkZdSp> map) {
        Sheet sheet = wb.getSheetAt(sheetNum);
        Iterator<Row> rows = sheet.rowIterator();
        Map<String, Integer> titles = null;// 存储列标题名称和下标
        boolean start = false;
        while (rows.hasNext()) {
            Row row = rows.next();
            if (!start) {
                titles = new HashMap<String, Integer>(row.getLastCellNum());
                for (int i = 0; i < row.getLastCellNum(); i++) {
                    titles.put(row.getCell(i).getStringCellValue(), i);
                }
                start = true;
                continue;
            }
            int cols = row.getPhysicalNumberOfCells();
            long skuid = getRowLong(row, cols, titles, "商品编号");
            if (skuid <= 0) {
                continue;
            }
            double total = getRowDouble(row, cols, titles, "返款金额");
            if (total == 0) {
                continue;
            }
            SqliteJdJtkZdSp temp = null;
            if (map.containsKey(skuid)) {
                temp = map.get(skuid);
                temp.setJtkhfZd(MathUtil.add(temp.getJtkhfZd(), total));
            } else {
                temp = new SqliteJdJtkZdSp();
                temp.setBbid(skuid + "");
                temp.setJtkhfZd(total);
            }
            map.put(skuid, temp);
        }
    }

    private static void readCommission(Workbook wb, int sheetNum, Map<Long, SqliteJdJtkZdSp> map) {
        Sheet sheet = wb.getSheetAt(sheetNum);
        Iterator<Row> rows = sheet.rowIterator();
        Map<String, Integer> titles = null;// 存储列标题名称和下标
        boolean start = false;
        while (rows.hasNext()) {
            Row row = rows.next();
            if (!start) {
                titles = new HashMap<String, Integer>(row.getLastCellNum());
                for (int i = 0; i < row.getLastCellNum(); i++) {
                    titles.put(row.getCell(i).getStringCellValue(), i);
                }
                start = true;
                continue;
            }
            int cols = row.getPhysicalNumberOfCells();
            long skuid = getRowLong(row, cols, titles, "商品编号");
            if (skuid <= 0) {
                continue;
            }
            double yj = getRowDouble(row, cols, titles, "佣金");
            double tzfwf = getRowDouble(row, cols, titles, "团长服务费");
            double ptfwf = getRowDouble(row, cols, titles, "京东平台技术服务费");
            double total = MathUtil.add(yj, tzfwf, ptfwf);
            if (total == 0) {
                continue;
            }
            SqliteJdJtkZdSp temp = null;
            if (map.containsKey(skuid)) {
                temp = map.get(skuid);
                temp.setJtkhfZd(MathUtil.add(temp.getJtkhfZd(), total));
            } else {
                temp = new SqliteJdJtkZdSp();
                temp.setBbid(skuid + "");
                temp.setJtkhfZd(total);
            }
            map.put(skuid, temp);
        }
    }

    public synchronized static String getRowStr(Row row, int cols, Map<String, Integer> titles, String title) {
        Object index = titles.get(title);
        if (index == null) {
            return null;
        }
        int col = Integer.parseInt(index.toString());
        if (col < 0 || col >= cols) {
            return null;
        }
        return row.getCell(col).getStringCellValue();
    }

    public synchronized static double getRowDouble(Row row, int cols, Map<String, Integer> titles, String title) {
        Object index = titles.get(title);
        if (index == null) {
            return 0D;
        }
        int col = Integer.parseInt(index.toString());
        if (col < 0 || col >= cols) {
            return 0D;
        }
        Cell cell = row.getCell(col);
//        if (cell.getCellType() == Cell.CELL_TYPE_NUMERIC) {
//            return cell.getNumericCellValue();
//        }
        String str = row.getCell(col).getStringCellValue();
        return StringToNumber.transform(str);
    }

    public synchronized static long getRowLong(Row row, int cols, Map<String, Integer> titles, String title) {
        Object index = titles.get(title);
        if (index == null) {
            return 0L;
        }
        int col = Integer.parseInt(index.toString());
        if (col < 0 || col >= cols) {
            return 0L;
        }
        Cell cell = row.getCell(col);
//        if (cell.getCellType() == Cell.CELL_TYPE_NUMERIC) {
//            return (long) cell.getNumericCellValue();
//        }
        String str = row.getCell(col).getStringCellValue();
        return StringToNumber.transformLong(str);
    }
}
