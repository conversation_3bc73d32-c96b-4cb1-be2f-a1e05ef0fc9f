package com.abujlb.dataprobi.util;

import com.abujlb.dataprobi.threads.BiThreadLocals;
import com.abujlb.util.StringUtil;
import com.alibaba.fastjson.JSONObject;
import org.apache.log4j.Logger;

/**
 * json对象属性转数字
 *
 * <AUTHOR>
 * @date 2021-08-08 09:57:23
 */
public class FastJSONObjAttrToNumber {

    private static final Logger log = Logger.getLogger(FastJSONObjAttrToNumber.class);

    /**
     * 对象属性转int
     *
     * @param obj json对象
     * @param key 对象的某个属性
     */
    public static int toInt(JSONObject obj, String key) {
        int result = 0;
        try {
            if (obj == null || obj.isEmpty()) {
                return result;
            }
            if (obj.containsKey(key) && !StringUtil.isNull2(obj.getString(key))) {
                // result = obj.getInt(key);
                result = Double.valueOf(obj.getString(key).replaceAll(",", "").replaceAll("%", "")).intValue();
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            log.error(BiThreadLocals.getMsgBean().getDpmc() + "-->FastJSONObjAttrToNumber.toInt(obj,key)-->error-->" + DataprobiDateUtil.getCurrentTime());
        }
        return result;
    }


    /**
     * 对象属性转int
     *
     * @param obj  json对象
     * @param keys 对象的某个属性
     */
    public static int toInt(JSONObject obj, String... keys) {
        try {
            if (obj == null || obj.isEmpty()) {
                return 0;
            }
            JSONObject temp = null;
            for (int i = 0; i < keys.length; i++) {
                if (temp == null || i != keys.length - 1) {
                    temp = (temp == null ? obj : temp).getJSONObject(keys[i]);
                    continue;
                }
                //最后一个 直接return
                return temp.getIntValue(keys[i]);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return 0;
    }

    public static String toString(JSONObject obj, String... keys) {
        try {
            if (obj == null || obj.isEmpty()) {
                return null;
            }
            JSONObject temp = null;
            for (int i = 0; i < keys.length; i++) {
                if (temp == null || i != keys.length - 1) {
                    temp = (temp == null ? obj : temp).getJSONObject(keys[i]);
                    continue;
                }
                //最后一个 直接return
                return temp.getString(keys[i]);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }


    /**
     * 对象属性转int
     *
     * @param obj  json对象
     * @param keys 对象的某个属性
     */
    public static double toDouble(JSONObject obj, String... keys) {
        try {
            if (obj == null || obj.isEmpty()) {
                return 0;
            }
            JSONObject temp = null;
            for (int i = 0; i < keys.length; i++) {
                if (temp == null || i != keys.length - 1) {
                    temp = ((temp == null ? obj : temp).getJSONObject(keys[i]));
                    continue;
                }
                //最后一个 直接return
                return temp.getDoubleValue(keys[i]);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return 0;
    }

    /**
     * 对象属性转double
     *
     * @param obj json对象
     * @param key 对象的某个属性
     */
    public static double toDouble(JSONObject obj, String key) {
        double result = 0D;
        try {
            if (obj == null || obj.isEmpty()) {
                return result;
            }
            if (obj.containsKey(key) && !StringUtil.isNull2(obj.getString(key))) {
                String str = obj.getString(key).replaceAll(",", "").replaceAll("%", "");
                return Double.valueOf(str);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            log.error(BiThreadLocals.getMsgBean().getDpmc() + "-->FastJSONObjAttrToNumber.toDouble(obj,key)-->error-->" + DataprobiDateUtil.getCurrentTime());
        }
        return result;
    }


    /**
     * 对象属性转String
     *
     * @param obj json对象
     * @param key 对象的某个属性
     */
    public static String toString(JSONObject obj, String key) {
        String result = "";
        try {
            if (obj == null || obj.isEmpty()) {
                return result;
            }
            if (obj.containsKey(key) && !StringUtil.isNull2(obj.getString(key))) {
                result = obj.getString(key);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            log.error(BiThreadLocals.getMsgBean().getDpmc() + "-->FastJSONObjAttrToNumber.toDouble(obj,key)-->error-->" + DataprobiDateUtil.getCurrentTime());
        }
        return result;
    }

    /**
     * 对象属性的属性数组第n个转String
     *
     * @param obj json对象
     * @param key1 对象的某个属性
     * @param key2 数组的某个属性
     * @param index 位置
     */
    public static String toString2(JSONObject obj, String key1,String key2,int index) {
        String result = "";
        try {
            if (obj == null || obj.isEmpty()) {
                return result;
            }
            result = obj.getJSONObject(key1).getJSONArray(key2).getString(index);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            log.error(BiThreadLocals.getMsgBean().getDpmc() + "-->FastJSONObjAttrToNumber.toDouble(obj,key)-->error-->" + DataprobiDateUtil.getCurrentTime());
        }
        return result;
    }


    public static String toString(JSONObject obj, String childJsonObjectKey, String key) {
        String result = "";
        try {
            if (obj == null || obj.isEmpty()) {
                return result;
            }
            if (obj.containsKey(childJsonObjectKey)) {
                JSONObject jsonObject = obj.getJSONObject(childJsonObjectKey);
                return toString(jsonObject, key);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            log.error(BiThreadLocals.getMsgBean().getDpmc() + "-->FastJSONObjAttrToNumber.toString(obj,,childJsonObjectKey,key)-->error-->" + DataprobiDateUtil.getCurrentTime());
        }
        return result;
    }

    public static int toInt(JSONObject obj, String childJsonObjectKey, String key) {
        int result = 0;
        try {
            if (obj == null || obj.isEmpty()) {
                return result;
            }
            if (obj.containsKey(childJsonObjectKey)) {
                JSONObject jsonObject = obj.getJSONObject(childJsonObjectKey);
                return toInt(jsonObject, key);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            log.error(BiThreadLocals.getMsgBean().getDpmc() + "-->FastJSONObjAttrToNumber.toInt(obj,,childJsonObjectKey,key)-->error-->" + DataprobiDateUtil.getCurrentTime());
        }
        return result;
    }


    public static double toDouble(JSONObject obj, String childJsonObjectKey, String key) {
        double result = 0;
        try {
            if (obj == null || obj.isEmpty()) {
                return result;
            }
            if (obj.containsKey(childJsonObjectKey)) {
                JSONObject jsonObject = obj.getJSONObject(childJsonObjectKey);
                return toDouble(jsonObject, key);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            log.error(BiThreadLocals.getMsgBean().getDpmc() + "-->FastJSONObjAttrToNumber.toDouble(obj,key)-->error-->" + DataprobiDateUtil.getCurrentTime());
        }
        return result;
    }

    public static JSONObject toJSONObject(JSONObject jsonObject, String key) {
        try {
            if (jsonObject == null || jsonObject.isEmpty()) {
                return null;
            }
            if (jsonObject.containsKey(key)) {
                return jsonObject.getJSONObject(key);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            log.error(BiThreadLocals.getMsgBean().getDpmc() + "-->FastJSONObjAttrToNumber.toJSONObject(obj,key)-->error-->" + DataprobiDateUtil.getCurrentTime());
        }
        return null;
    }

    public static boolean toBoolean(JSONObject obj, String key) {
        boolean result = false;
        try {
            if (obj == null || obj.isEmpty()) {
                return result;
            }
            if (obj.containsKey(key)) {
                result = obj.getBooleanValue(key);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            log.error(BiThreadLocals.getMsgBean().getDpmc() + "-->FastJSONObjAttrToNumber.toBoolean(obj,key)-->error-->" + DataprobiDateUtil.getCurrentTime());
        }
        return result;
    }
}
