package com.abujlb.dataprobi.util;

import com.google.gson.Gson;

import java.lang.reflect.Type;

public class Json2Object {

	public static <T> T parseObj(String value, Type typeOfT) {
		try {
			Gson gson = new Gson();
			return gson.fromJson(value, typeOfT);
		} catch (Exception e) {
			return null;
		}
	}

	public static <T> T parseObj(String value, Class<T> classOfT) {
		try {
			Gson gson = new Gson();
			return gson.fromJson(value, classOfT);
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}
}
