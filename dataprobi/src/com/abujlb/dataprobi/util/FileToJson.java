package com.abujlb.dataprobi.util;

import com.abujlb.util.RegexPattern;
import com.abujlb.util.ResourceLoader;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.nio.charset.StandardCharsets;

/**
 * json文件转json
 * 
 * <AUTHOR>
 * @date 2018-12-27 09:45:35
 */
public class FileToJson {

	/**
	 * 文件转json
	 * 
	 * @param file
	 * @return json
	 */
	public static String toJson(File file) {
		if (!file.exists()) {
			return "";
		}
		try {
			String s = readFile(file);
			String json = RegexPattern.patternFind("(\\[.*\\])", s);
			return json;
		} catch (Exception e) {
			return "";
		} finally {
			file.delete();
		}
	}

	public static String toJson2(File file) {
		if (!file.exists()) {
			return "";
		}
		try {
			return readFile(file);
		} catch (Exception e) {
			return "";
		} finally {
			file.delete();
		}
	}

	/**
	 * 读取文件转string
	 *
	 * @return str
	 */
	public static String readFile(File f) {
		BufferedReader br = null;
		try {
			br = new BufferedReader(new InputStreamReader(new FileInputStream(f), StandardCharsets.UTF_8));
			StringBuilder sb = new StringBuilder();
			String line = null;
			while ((line = br.readLine()) != null) {
				sb.append(line);
			}
			return sb.toString();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			try {
				br.close();
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		return null;
	}

	/**
	 * 根据文件路径转json
	 * 
	 * @param path
	 * @return json
	 */
	public static String toJson(String path) {
		return toJson(new File(path));
	}

	/**
	 * 获取远程的json文件数据
	 * 
	 * @param url
	 * @return json
	 */
	public static String loadJson(String url) {
		String json = "";
		try {
			URL urls = new URL(url);
			HttpURLConnection conn = (HttpURLConnection) urls.openConnection();
			conn.setRequestProperty("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
			conn.setDoOutput(true);
			conn.setDoInput(true);
			conn.setRequestMethod("GET");
			conn.setRequestProperty("contentType", "UTF-8");

			int state = conn.getResponseCode();
			if (state != 200) { // 文件不存在
				return null;
			}

			InputStream inputStream = conn.getInputStream();
			BufferedReader bf = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));
			String line = null;
			while ((line = bf.readLine()) != null) {// 一行一行的读
				json = json + line;
			}
			if (inputStream != null) {
				inputStream.close();
			}
			String[] strs = json.split("\\\\");
			String str = "";
			StringBuffer jsons = new StringBuffer();
			for (int i = 0; i < strs.length; i++) {
				str = strs[i];
				jsons = jsons.append(str);
			}
			jsons.replace(0, 9, "");
			jsons.replace(jsons.length() - 2, jsons.length(), "");
			return jsons.toString();
		} catch (MalformedURLException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		}
		return json;
	}
	
	/**
	 * 读取文件转string
	 * 
	 * @param path 包路径，例如：/com/abujlb/zdcjsjs/http/md5_sjs.js
	 * @return str
	 */
	public static String readFile(String path) {
		Reader r = null;
		try {
			StringBuilder sb = new StringBuilder();

			InputStream in = ResourceLoader.getResourceAsStream(path);
			r = new InputStreamReader(in, StandardCharsets.UTF_8);
			int length = 0;
			for (char[] c = new char[1024]; (length = r.read(c)) != -1; ) {
				sb.append(c, 0, length);
			}
			return sb.toString();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			try {
				if (r != null) {
					r.close();
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		return null;
	}
}
