package com.abujlb.dataprobi.util;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * @date 2021-7-28
 */
public class MathUtil {

    static final BigDecimal BIG_100 = BigDecimal.valueOf(100);
    static final BigDecimal BIG_1000 = BigDecimal.valueOf(1000);

    /**
     * 将分为单位的转换为元 （除100）
     *
     * @param amount
     * @return
     * @throws Exception
     */
    public static double changeF2Y(String amount) {
        return BigDecimal.valueOf(Long.valueOf(amount)).divide(new BigDecimal(100)).doubleValue();
    }

    /**
     * 将分为单位的转换为元 （除100）
     *
     * @param amount
     * @return
     * @throws Exception
     */
    public static double changeF2Y(long amount) {
        return changeF2Y(amount + "");
    }

    /**
     * 将厘为单位的转换为元 （除100）
     *
     * @param amount 原始数据
     * @param length 小数点保留位数
     * @return
     */
    public static double changeF2Y(double amount, int length) {
        return new BigDecimal(amount).divide(new BigDecimal(100), length, RoundingMode.HALF_UP).doubleValue();
    }

    /**
     * 将厘为单位的转换为元 （除1000）
     *
     * @param amount
     * @return
     * @throws Exception
     */
    public static double changeL2Y(String amount) {
        return BigDecimal.valueOf(Long.valueOf(amount)).divide(new BigDecimal(1000)).doubleValue();
    }

    /**
     * 将厘为单位的转换为元 （除1000）
     *
     * @param amount
     * @return
     * @throws Exception
     */
    public static double changeL2Y(long amount) {
        return changeL2Y(amount + "");
    }

    /**
     * 将厘为单位的转换为元 （除1000）
     *
     * @param amount 原始数据
     * @param length 小数点保留位数
     * @return
     */
    public static double changeL2Y(double amount, int length) {
        return new BigDecimal(amount).divide(new BigDecimal(1000), length, RoundingMode.HALF_UP).doubleValue();
    }

    public static double add(double a, double b) {
        return BigDecimal.valueOf(a).add(BigDecimal.valueOf(b)).setScale(2, RoundingMode.HALF_UP).doubleValue();
    }

    public static double addAbs(double a, double b) {
        return BigDecimal.valueOf(Math.abs(a)).add(BigDecimal.valueOf(Math.abs(b))).setScale(2, RoundingMode.HALF_UP).doubleValue();
    }

    public static double addAbsScale(double a, double b,int scale) {
        return BigDecimal.valueOf(Math.abs(a)).add(BigDecimal.valueOf(Math.abs(b))).setScale(scale, RoundingMode.HALF_UP).doubleValue();
    }

    public static double divide100(double a) {
        return BigDecimal.valueOf(a).divide(BIG_100).doubleValue();
    }

    public static double divide1000(double a) {
        return BigDecimal.valueOf(a).divide(BIG_1000).doubleValue();
    }

    public static double divide(double a, double b, int scale) {
        if (b == 0) {
            return 0;
        }
        return BigDecimal.valueOf(a).divide(BigDecimal.valueOf(b), scale, RoundingMode.HALF_UP).doubleValue();
    }

    public static double divide(double a, double b) {
        if (b == 0) {
            return 0;
        }
        return BigDecimal.valueOf(a).divide(BigDecimal.valueOf(b), 4, RoundingMode.HALF_UP).doubleValue();
    }

    public static double calZhlv(double a, double b) {
        if (b == 0) {
            return 0;
        }
        return BigDecimal.valueOf(a).divide(BigDecimal.valueOf(b), 4, RoundingMode.HALF_UP).doubleValue();
    }

    public static double add(double... a) {
        BigDecimal TOTAL = new BigDecimal(0);
        for (double v : a) {
            TOTAL = TOTAL.add(BigDecimal.valueOf(v));
        }
        return TOTAL.setScale(2, RoundingMode.HALF_UP).doubleValue();
    }

    public static double multipy(double a, double b) {
        return multipy(a, b, 4);
    }

    public static double multipy(double a, double b, int scales) {
        return BigDecimal.valueOf(a).multiply(BigDecimal.valueOf(b)).setScale(scales, RoundingMode.HALF_UP).doubleValue();
    }

    public static void main(String[] args) throws Exception {
        System.out.println(changeF2Y("1"));
    }

    public static double minus(double a, double b) {
        return BigDecimal.valueOf(a).subtract(BigDecimal.valueOf(b)).setScale(2, RoundingMode.HALF_UP).doubleValue();
    }
}
