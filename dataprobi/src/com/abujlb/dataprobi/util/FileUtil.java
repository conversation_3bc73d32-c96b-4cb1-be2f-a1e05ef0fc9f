package com.abujlb.dataprobi.util;

import com.abujlb.CommonConfig;
import org.apache.log4j.Logger;

import java.io.File;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2022/8/3 16:39
 */
public class FileUtil {

    public static final Logger logger = Logger.getLogger(FileUtil.class);

    public static String createXlsFilePath() {
        return CommonConfig.getString("tempdir") + getUUID() + ".xls";
    }

    public static String createXlsxFilePath() {
        return CommonConfig.getString("tempdir") + getUUID() + ".xlsx";
    }

    public static String createJSONFilePath() {
        return CommonConfig.getString("tempdir") + getUUID() + ".json";
    }

    public static String createCSVFilePath() {
        return CommonConfig.getString("tempdir") + getUUID() + ".csv";
    }


    public static String createDBFilePath() {
        return CommonConfig.getString("tempdir") + getUUID() + ".db";
    }

    public static String createYhidUseridSycmSpDBFilePath(int yhid, String userid) {
        String filepath = CommonConfig.getString("tempdir") + yhid + "_" + userid;
        try {
            File folder = new File(filepath);
            if (!folder.exists()) {
                folder.mkdirs();
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return filepath + "/sycm_sp.db";
    }

    public static String createYhidUseridZipFilePath(int yhid, String userid) {
        String filepath = CommonConfig.getString("tempdir") + yhid + "_" + userid;
        try {

            File folder = new File(filepath);
            if (!folder.exists()) {
                folder.mkdir();
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return filepath + "/" + "sycm_sp.zip";
    }

    public static String createZipFilePath() {
        return CommonConfig.getString("tempdir") + getUUID() + ".zip";
    }

    private static String getUUID() {
        return UUID.randomUUID().toString().replace("-", "");
    }

}
