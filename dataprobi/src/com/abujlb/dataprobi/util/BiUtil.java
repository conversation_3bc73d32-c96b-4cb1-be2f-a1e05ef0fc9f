package com.abujlb.dataprobi.util;

import com.abujlb.dataprobi.bean.*;
import com.abujlb.dataprobi.interfaces.Plusable;
import com.abujlb.dataprobi.oss.BiDataOssUtil;
import com.abujlb.dataprobi.threads.BiThreadLocals;
import com.abujlb.dataprobi.tsdao.BiSycmDpDataTsDao;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.reflect.TypeToken;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2023/3/27 15:48
 */
@Component
public class BiUtil {

    static final Logger logger = Logger.getLogger(BiUtil.class);
    @Autowired
    private BiSycmDpDataTsDao biSycmDpDataTsDao;
    @Autowired
    private BiDataOssUtil biDataOssUtil;

    public <T extends AbstractSqliteDp> T dpObject(Class<T> tClass, String rq, String... columns) {
        DataprobiBaseMsgBean msgBean = BiThreadLocals.getMsgBean();
        if (Objects.isNull(msgBean)) {
            return null;
        }
        try {
            String[] columnValue = biSycmDpDataTsDao.getRow(msgBean.getQd() + "_" + msgBean.getUserid(), rq, columns);
            T t = tClass.newInstance();
            t.setValues(columnValue);
            t.setQd(msgBean.getQd());
            t.setQd_userid(msgBean.getQd() + "_" + msgBean.getUserid());
            t.setRq(rq);
            t.setYhid(msgBean.getYhid());
            t.setUserid(msgBean.getUserid());
            return t;
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return null;
    }

    public <T extends AbstractSqliteSp> List<T> spList(Class<T> tClass, String ossKey, String rq) {
        try {
            if (biDataOssUtil.exist((ossKey))) {
                String json = biDataOssUtil.readJson(ossKey);
                if (StringUtils.isBlank(json)) {
                    return Collections.emptyList();
                }
                JSONArray jsonArray = JSONArray.parseArray(json);
                if (CollectionUtils.isEmpty(jsonArray)) {
                    return Collections.emptyList();
                }

                DataprobiBaseMsgBean msgBean = BiThreadLocals.getMsgBean();
                List<T> list = new ArrayList<>(jsonArray.size());
                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONObject temp = jsonArray.getJSONObject(i);
                    T t = tClass.newInstance();
                    t.setValues(temp);
                    t.setQd_userid_bbid(msgBean.getQd() + "_" + msgBean.getUserid() + "_" + t.getBbid());
                    t.setRq(rq);
                    t.setYhid(msgBean.getYhid());
                    t.setQd(msgBean.getQd());
                    t.setUserid(msgBean.getUserid());
                    list.add(t);
                }

                Class<?>[] interfaces = tClass.getInterfaces();
                boolean plus = false;
                for (Class<?> anInterface : interfaces) {
                    if (anInterface == Plusable.class) {
                        plus = true;
                        break;
                    }
                }

                if (!plus) {
                    return list;
                }

                Map<String, T> map = new HashMap<>();
                for (T t : list) {
                    T temp = null;
                    if (map.containsKey(t.getBbid())) {
                        temp = map.get(t.getBbid());
                    } else {
                        temp = tClass.newInstance();
                        temp.setUserid(t.getUserid());
                        temp.setYhid(t.getYhid());
                        temp.setQd(t.getQd());
                        temp.setQd_userid_bbid(t.getQd_userid_bbid());
                        temp.setBbid(t.getBbid());
                    }
                    tClass.getMethod("plus", tClass).invoke(temp, t);
                    map.put(t.getBbid(), temp);
                }

                return new ArrayList<>(map.values());
            } else {
                return Collections.emptyList();
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    public List<SqliteSp> readDaySpListJson(String osskey) {
        try {
            if (biDataOssUtil.exist(osskey)) {
                String jsonFilePath = FileUtil.createJSONFilePath();
                biDataOssUtil.download(osskey, jsonFilePath);
                String json = FileToJson.toJson2(new File(jsonFilePath));
                List<SqliteSp> list = Json2Object.parseObj(json, new TypeToken<List<SqliteSp>>() {
                }.getType());
                return list;
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return Collections.emptyList();
    }

    public SqliteDp readDayDpJson(String osskey) {
        try {
            if (biDataOssUtil.exist(osskey)) {
                String jsonFilePath = FileUtil.createJSONFilePath();
                biDataOssUtil.download(osskey, jsonFilePath);
                String json = FileToJson.toJson2(new File(jsonFilePath));
                SqliteDp temp = Json2Object.parseObj(json, SqliteDp.class);
                return temp;
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return null;
    }
}
