package com.abujlb.dataprobi.util;

import com.abujlb.dataprobi.bean.BiInfo;
import com.abujlb.dataprobi.bean.tb.SqliteUdzhtSp;
import com.abujlb.dataprobi.threads.BiThreadLocals;
import com.csvreader.CsvReader;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;

import java.io.File;
import java.io.FileInputStream;
import java.nio.charset.Charset;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2024/11/18
 */
public class UDCSVReader extends BaseXlsReader {

    private static final Logger LOG = Logger.getLogger(UDCSVReader.class);

    public static List<SqliteUdzhtSp> parseCSV(String temppath, String rq) {
        File file = null;
        Map<String, SqliteUdzhtSp> map = new HashMap<>();
        FileInputStream fileInputStream = null;
        CsvReader cr = null;
        try {
            file = new File(temppath);
            if (!file.exists()) {
                return Collections.emptyList();
            }
            fileInputStream = new FileInputStream(file);

            cr = new CsvReader(fileInputStream, Charset.forName("GBK"));
            cr.readHeaders();

            while (cr.readRecord()) {
//                String ztlx = cr.get("主体类型");
//                if (StringUtils.isBlank(ztlx) || !ztlx.equals("商品")) {
//                    continue;
//                }

                String rq2 = cr.get("日期");
                if (!rq.equals(rq2)) {
                    continue;
                }

                String bbid = cr.get("主体ID");
                if (StringUtils.isBlank(bbid)) {
                    continue;
                }
                SqliteUdzhtSp sqliteUdzhtSp = null;
                if (map.containsKey(bbid)) {
                    sqliteUdzhtSp = map.get(bbid);
                } else {
                    sqliteUdzhtSp = new SqliteUdzhtSp();
                    sqliteUdzhtSp.setQd_userid_bbid(BiThreadLocals.getMsgBean().getQd() + "_" + BiThreadLocals.getMsgBean().getUserid() + "_" + bbid);
                    sqliteUdzhtSp.setYhid(BiThreadLocals.getMsgBean().getYhid());
                    sqliteUdzhtSp.setQd(BiThreadLocals.getMsgBean().getQd());
                    sqliteUdzhtSp.setUserid(BiThreadLocals.getMsgBean().getUserid());
                    sqliteUdzhtSp.setRq(rq);
                    sqliteUdzhtSp.setBbid(bbid);
                }

                SqliteUdzhtSp temp = new SqliteUdzhtSp();
                temp.setUdzhthf(StringToNumber.getDouble(cr.get("花费")));
                temp.setUdzhtcjje(StringToNumber.getDouble(cr.get("总成交金额")));

                sqliteUdzhtSp.plus(temp);

                map.put(bbid, sqliteUdzhtSp);
            }

            cr.close();
            return new ArrayList<>(map.values());
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        } finally {
            if (cr != null) {
                cr.close();
            }
            if (file != null && file.exists()) {
                file.delete();
            }
        }
        return null;
    }



    public static List<SqliteUdzhtSp> parseCSV2(BiInfo biInfo , String temppath, String rq) {
        File file = null;
        Map<String, SqliteUdzhtSp> map = new HashMap<>();
        FileInputStream fileInputStream = null;
        CsvReader cr = null;
        try {
            file = new File(temppath);
            if (!file.exists()) {
                return Collections.emptyList();
            }
            fileInputStream = new FileInputStream(file);

            cr = new CsvReader(fileInputStream, Charset.forName("GBK"));
            cr.readHeaders();

            while (cr.readRecord()) {
//                String ztlx = cr.get("主体类型");
//                if (StringUtils.isBlank(ztlx) || !ztlx.equals("商品")) {
//                    continue;
//                }

                String rq2 = cr.get("日期");
                if (!rq.equals(rq2)) {
                    continue;
                }

                String bbid = cr.get("主体ID");
                if (StringUtils.isBlank(bbid)) {
                    continue;
                }
                SqliteUdzhtSp sqliteUdzhtSp = null;
                if (map.containsKey(bbid)) {
                    sqliteUdzhtSp = map.get(bbid);
                } else {
                    sqliteUdzhtSp = new SqliteUdzhtSp();
                    sqliteUdzhtSp.setQd_userid_bbid(biInfo.getQd() + "_" + biInfo.getUserid() + "_" + bbid);
                    sqliteUdzhtSp.setYhid(biInfo.getYhid());
                    sqliteUdzhtSp.setQd(biInfo.getQd());
                    sqliteUdzhtSp.setUserid(biInfo.getUserid());
                    sqliteUdzhtSp.setRq(rq);
                    sqliteUdzhtSp.setBbid(bbid);
                }

                SqliteUdzhtSp temp = new SqliteUdzhtSp();
                temp.setUdzhthf(StringToNumber.getDouble(cr.get("花费")));
                temp.setUdzhtcjje(StringToNumber.getDouble(cr.get("总成交金额")));

                sqliteUdzhtSp.plus(temp);

                map.put(bbid, sqliteUdzhtSp);
            }

            cr.close();
            return new ArrayList<>(map.values());
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        } finally {
            if (cr != null) {
                cr.close();
            }
            if (file != null && file.exists()) {
                file.delete();
            }
        }
        return null;
    }
}
