package com.abujlb.dataprobi.util;

import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.File;
import java.io.InputStream;
import java.nio.file.Files;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

public class PpxxXlsReader extends BaseXlsReader {

    private static final Logger log = Logger.getLogger(PpxxXlsReader.class);

    public static Map<String, Double> readXls(File file) {
        Map<String, Double> cjjeMap = new HashMap<>();
        InputStream is = null;
        try {
            is = Files.newInputStream(file.toPath());
            Workbook wb = new XSSFWorkbook(is);
            Sheet sheet = wb.getSheetAt(0);
            Iterator<Row> rows = sheet.rowIterator();
            Map<String, Integer> titles = null;
            boolean start = false;
            while (rows.hasNext()) {
                Row row = rows.next();
                if (!start) {
                    titles = new HashMap<String, Integer>(row.getLastCellNum());
                    for (int i = 0; i < row.getLastCellNum(); i++) {
                        titles.put(row.getCell(i).getStringCellValue(), i);
                    }
                    start = true;
                } else {
                    int cols = row.getPhysicalNumberOfCells();
                    String bbid = getRowStr(row, cols, titles, "商品ID");
                    if (StringUtils.isBlank(bbid)) {
                        bbid = getRowStr(row, cols, titles, "商品id");
                    }
                    if (StringUtils.isBlank(bbid)) {
                        continue;
                    }

                    double val = cjjeMap.getOrDefault(bbid, 0D);
                    double newVal = MathUtil.add(val, getRowDouble(row, 3));
                    cjjeMap.put(bbid, newVal);
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        } finally {
            try {
                if (is != null) {
                    is.close();
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
            if (file != null && file.exists()) {
                file.delete();
            }
        }
        return cjjeMap;
    }


    public synchronized static double getRowDouble(Row row, int index) {
        Cell cell = row.getCell(index);
        if (cell.getCellType() == Cell.CELL_TYPE_NUMERIC) {
            return cell.getNumericCellValue();
        }
        String str = row.getCell(index).getStringCellValue();
        return StringToNumber.transform(str);
    }

}
