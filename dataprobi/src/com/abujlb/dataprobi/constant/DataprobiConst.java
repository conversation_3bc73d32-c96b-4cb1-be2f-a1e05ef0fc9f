package com.abujlb.dataprobi.constant;

import com.abujlb.dataprobi.bean.SqliteDp;
import com.abujlb.dataprobi.bean.SqliteSp;
import com.google.gson.ExclusionStrategy;
import com.google.gson.FieldAttributes;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
/**
 * <AUTHOR>
 * @date 2022/5/11 15:36
 */
public interface DataprobiConst {

    Gson GSON = new Gson();

    String regExp = "\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}";
    String regExp2 = "\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}";

    Gson GSON2 = new GsonBuilder().addSerializationExclusionStrategy(
            new ExclusionStrategy() {
                @Override
                public boolean shouldSkipField(FieldAttributes field) {
                    return field.getDeclaringClass() == SqliteDp.class || field.getDeclaringClass() == SqliteSp.class;
                }

                @Override
                public boolean shouldSkipClass(Class<?> aClass) {
                    return false;
                }
            }
    ).create();

    String RQLX_DAY = "0";


    //bi_data/day/20220201/用户id/渠道/店铺userid/sycm_sp.db
    String SYCM_SP_DAY = "bi_data/day/%s/%s/%s/%s/sycm_sp.db";
    String SYCM_SPU_DAY = "bi_data/day/%s/%s/%s/%s/sycm_spu.db";
    String SYCM_DP_DAY = "bi_data/day/%s/%s/%s/%s/sycm_dp.db";


    //bi_data/month/202202/用户id/渠道/店铺userid/sycm_dp.db
    String SYCM_DP_MONTH = "bi_data/month/%s/%s/%s/%s/sycm_dp.db";


    String SELECT_SP_SQL = "select * from sycm_sp ;";
    String SELECT_SPU_SQL = "select * from sycm_spu ;";
    String SELECT_DP_SQL = "select * from sycm_dp ;";

    String TYPE_DATAPROBITASK = "dataprobitask";
    String TYPE_DATAPROBICOMPOENT = "dataprobicompoent";
}
