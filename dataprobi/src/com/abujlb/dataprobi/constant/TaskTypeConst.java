package com.abujlb.dataprobi.constant;


public interface TaskTypeConst {

    String ALBB_TGFX_CJTF = "albb_tgfx_cjtf";
    String ALBB_TGFX_GCSYJSJH = "albb_tgfx_gcsyjsjh";
    String ALBB_TGFX_GYPJJFA = "albb_tgfx_gypjjfa";
    String ALBB_TGFX_HXSJCZJH = "albb_tgfx_hxsjczjh";
    String ALBB_TGFX_QZTD = "albb_tgfx_qztd";
    String ALBB_TGFX_QZXP = "albb_tgfx_qzxp";
    String ALBB_TGFX_SKAJSC = "albb_tgfx_skajsc";
    String ALBB_TGFX_SKAZSDZFA = "albb_tgfx_skazsdzfa";
    String ALBB_TGFX_XFPJJFA = "albb_tgfx_xfpjjfa";
    String ALBB_TGFX_ZGCJJFA = "albb_tgfx_zgcjjfa";
    String ALBB_TGFX_QDYX = "albb_tgfx_qdyx";
    String ALBB_TGFX_CXFA = "albb_tgfx_cxfa";

    String ALBB_JHZT = "albb_jhzt";

    String DY_TGFX_TSP = "dy_tgfx_tsp";
    String DY_TGFX_QYTG = "dy_qytg_tgfx";
    String DY_JHZT = "dy_jhzt";
    String JD_JHZT = "jd_jhzt";
    String JD_TGFX_GWCD = "jd_tgfx_gwcd";
    String JD_TGFX_NRGG = "jd_tgfx_nrgg";
    String JD_TGFX_JDKC = "jd_tgfx_jdkc";
    String JD_TGFX_QZYX = "jd_tgfx_qzyx";
    String JD_TGFX_JST = "jd_tgfx_jst";
    String JD_TGFX_JDZT = "jd_tgfx_jdzt";
    String JD_TGFX_JDZW = "jd_tgfx_jdzw";
    String PDD_TGFX_SPTG = "pdd_tgfx_sptg";
    String PDD_TGFX_BZTG = "pdd_tgfx_bztg";
    String PDD_TGFX_QZTG = "pdd_tgfx_qztg";
    String PDD_JHZT = "pdd_jhzt";
    String MN_TGFX_YLMF = "mn_tgfx_ylmf";
    String MN_TGFX_AIZT = "mn_tgfx_aizt";
    String MN_TGFX_DMBZT = "mn_tgfx_dmbzt";
    String MN_TGFX_QZTG = "mn_tgfx_qztg";
    String MN_TGFX_ZTC = "mn_tgfx_ztc";


    String TX_TGFX_YLMF = "tx_tgfx_ylmf";
    String TX_TGFX_YLMF_CXJH = "tx_tgfx_ylmf_cxjh";
    String TX_TGFX_AIZT = "tx_tgfx_aizt";
    String TX_TGFX_DMBZT = "tx_tgfx_dmbzt";
    String TX_TGFX_QZTG = "tx_tgfx_qztg";
    String TX_TGFX_ZTC = "tx_tgfx_ztc";
    String TX_TGFX_AIZT_SHOP = "tx_tgfx_aizt_shop";
    String TX_TGFX_AIZT_ITEM = "tx_tgfx_aizt_item";
    String TX_TGFX_XSTG = "tx_tgfx_xstg";
    String TX_TGFX_ZTC_CROWD = "tx_tgfx_ztc_crowd";
    String TX_TGFX_ZTC_KEYWORD = "tx_tgfx_ztc_keyword";
    String DY_YGHF_JXLM = "dy_yghf_jxlm";
    String DY_YGHF_JXLM_TZFWF = "dy_yghf_jxlm_tzfwf";
    String TX_YGHF_PPXX = "tx_yghf_ppxx";
    String TX_AIZTONEJHZT = "tx_aiztonejhzt";
    String TX_AIZT_CYBB = "tx_aizt_cybb";
    String TX_AIZT_UNIT_STATUS = "tx_aizt_unit_status";
    String TX_YGHF_BYBT = "tx_yghf_bybt";
    String TX_NEWITEM = "tx_newitem";
    String TX_ZTCVISTOR = "tx_ztcvistor";
    String PDD_SPPJ = "pdd_sppj";
    String TX_FKS = "tx_fks";
    String TX_QZTGFKS = "tx_qztgfks";
    String TX_XKFX = "tx_xkfx";
    String TX_XKFX_OLD = "tx_xkfx_old";
    String JD_XKFX = "jd_xkfx";
    String PDD_XKFX = "pdd_xkfx";

    String JD_FBP_SPCATE = "jd_fbp_spcate";
    String JD_FBP_SPCJSJ = "jd_fbp_spcjsj";

    String XHS_JGTGFX = "xhs_jgtgfx";
    String XHS_QFTGFX = "xhs_qftgfx";
    String PGY_DRYGYJ = "pgy_drygyj";


    String SYCM_PERFORMANCE_CUSTOMER_SUMMARY = "sycm_performance_customer_summary";
    String SYCM_PERFORMANCE_CORE = "sycm_performance_core";
    String SYCM_PERFORMANCE_CUSTOMER_RECEPTION_DATA = "sycm_performance_customer_reception_data";
    String SYCM_PERFORMANCE_CUSTOMER_RECEPTION_DY_DATA = "sycm_performance_customer_reception_dy_data";
    String SYCM_PERFORMANCE_CUSTOMER_RECEPTION_XHS_DATA = "sycm_performance_customer_reception_xhs_data";
    String SYCM_PERFORMANCE_CUSTOMER_RECEPTION_PDD_DATA = "sycm_performance_customer_reception_pdd_data";
    String SYCM_PERFORMANCE_CUSTOMER_RECEPTION_KS_DATA = "sycm_performance_customer_reception_ks_data";
    String SYCM_PERFORMANCE_DUTY_DATA = "sycm_performance_duty_data";
    String SYCM_PERFORMANCE_REFUND = "sycm_performance_refund";
    String SYCM_PERFORMANCE_INQUIIRY_ORDER = "sycm_performance_inquiiry_order";
    String SYCM_PERFORMANCE_REA = "sycm_performance_rea";
    String DY_FGKF = "dy_fgkf";
    String JD_KF_RECEPTION_DATA = "jd_kf_reception_data";
    String JD_KF_MARKETING_DATA = "jd_kf_marketing_data";
    String JD_KF_ATTENDANCE_DATA = "jd_kf_attendance_data";

    String WPH_YGHF_WXKYJ = "wph_yghf_wxkyj";
}
