package com.abujlb.dataprobi.service;

import com.abujlb.dataprobi.bean.BiInfo;
import com.abujlb.dataprobi.bean.jd.DataprobiJdMsgBean;
import com.abujlb.dataprobi.constant.TaskTypeConst;
import com.abujlb.dataprobi.server.DataUploader;
import com.abujlb.dataprobi.threads.BiThreadLocals;
import com.alibaba.fastjson.JSONObject;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/6/22
 */
@Component
public class BiJdDataDealService extends BiService {

    @Override
    protected void after() {
        DataprobiJdMsgBean msgBean = (DataprobiJdMsgBean) BiThreadLocals.getMsgBean();

        BiInfo biInfo = BiThreadLocals.getBiInfo();
        if (biInfo == null) {
            biInfo = DataUploader.getBiinfoById(msgBean.getBiinfoId());
        }

        boolean fbp = false;
        if (biInfo != null) {
            fbp = JSONObject.parseObject(biInfo.getDpconfigjson()).getIntValue("dplx") == 1;
        }

        List<String> rqList = new ArrayList<>();
        rqList.addAll(msgBean.getSpxg());
        rqList.addAll(msgBean.getGwcd());
        rqList.addAll(msgBean.getJdkc());
        rqList.addAll(msgBean.getJdht());
        rqList.addAll(msgBean.getJdzt());
        rqList.addAll(msgBean.getJdzw());
        rqList.addAll(msgBean.getXkfx());
        rqList = rqList.stream().distinct().collect(Collectors.toList());

        sendDataprobiTaskXkfx(msgBean, rqList, TaskTypeConst.JD_XKFX, fbp);
        sendDataprobiTaskTgfx(msgBean, msgBean.getJdkc(), TaskTypeConst.JD_TGFX_JDKC, fbp);
        sendDataprobiTaskTgfx(msgBean, msgBean.getJdzt(), TaskTypeConst.JD_TGFX_JDZT, fbp);
        sendDataprobiTaskTgfx(msgBean, msgBean.getJdzw(), TaskTypeConst.JD_TGFX_JDZW, fbp);
        sendDataprobiTaskTgfx(msgBean, msgBean.getJdht(), TaskTypeConst.JD_TGFX_JST, fbp);
        sendDataprobiTaskTgfx(msgBean, msgBean.getGwcd(), TaskTypeConst.JD_TGFX_GWCD, fbp);
        sendDataprobiTaskTgfx(msgBean, msgBean.getNrgg(), TaskTypeConst.JD_TGFX_NRGG, fbp);

        List<String> qzyxRqList = new ArrayList<>();
        qzyxRqList.addAll(msgBean.getQzyxtgfx());
        qzyxRqList.addAll(msgBean.getQzyx());
        qzyxRqList = qzyxRqList.stream().distinct().collect(Collectors.toList());
        sendDataprobiTaskTgfx(msgBean, qzyxRqList, TaskTypeConst.JD_TGFX_QZYX, fbp);

        sendDataprobiTask(msgBean, msgBean.getJdjhzt(), TaskTypeConst.JD_JHZT);
        sendDataprobiTask(msgBean, msgBean.getFbpspmx(), TaskTypeConst.JD_FBP_SPCJSJ, fbp);
        sendDataprobiTask(msgBean, msgBean.getFbpspmx(), TaskTypeConst.JD_FBP_SPCATE, fbp);
        sendDataprobiTask(msgBean, msgBean.getJm_kf_reception_data(), TaskTypeConst.JD_KF_RECEPTION_DATA, fbp);
        sendDataprobiTask(msgBean, msgBean.getJm_kf_marketing_data(), TaskTypeConst.JD_KF_MARKETING_DATA, fbp);
        sendDataprobiTask(msgBean, msgBean.getJm_kf_attendance_data(), TaskTypeConst.JD_KF_ATTENDANCE_DATA, fbp);

        sendDataprobiCompoent(msgBean);
    }
}