package com.abujlb.dataprobi.service;

import com.abujlb.SpringBeanUtil;
import com.abujlb.dataprobi.annotations.Beta;
import com.abujlb.dataprobi.annotations.BiPro;
import com.abujlb.dataprobi.annotations.SumIgnore;
import com.abujlb.dataprobi.annotations.UpdateSycmcljzrqIgnore;
import com.abujlb.dataprobi.bean.*;
import com.abujlb.dataprobi.constant.DataprobiConst;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.oss.BiDataOssUtil;
import com.abujlb.dataprobi.processes.BiDealProcess;
import com.abujlb.dataprobi.processes.sum.BiSumStrategy;
import com.abujlb.dataprobi.server.DataUploader;
import com.abujlb.dataprobi.threads.BiThreadLocals;
import com.abujlb.dataprobi.tsdao.DataprobiMsgLogTsDao;
import com.abujlb.dataprobi.util.DataprobiDateUtil;
import com.abujlb.dataprobi.util.DataprobiStringUtil;
import com.abujlb.lock.DistributedLock;
import com.abujlb.lock.LockObj;
import com.abujlb.mq.AliyunMq2;
import com.abujlb.mq.JobMessage;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/8/11 14:35
 */
@Component
public abstract class BiService {

    private static final Logger LOG = Logger.getLogger(BiService.class);

    private final Map<Qd, List<BiDealProcess>> map = new HashMap<>();

    @Autowired
    private BiSumStrategy biSumStrategy;

    @Autowired
    private DistributedLock distributedLock;

    @Autowired
    protected AliyunMq2 aliyunMq2;

    @Autowired
    private DataprobiMsgLogTsDao dataprobiMsgLogTsDao;

    @Autowired
    private BiDataOssUtil biDataOssUtil;

    public boolean process() {
        LockObj lockObj = null;
        DataprobiBaseMsgBean dataprobiBaseMsgBean = BiThreadLocals.getMsgBean();
        try {
            if (dataprobiBaseMsgBean == null || dataprobiBaseMsgBean.getBiinfoId() == 0) {
                return true;
            }

            lockObj = distributedLock.lock("dataprobi_" + dataprobiBaseMsgBean.getQd() + "_" + dataprobiBaseMsgBean.getUserid(), 30 * 60 * 2);
            if (lockObj == null) {
                return false;
            }

            dataprobiMsgLogTsDao.start(dataprobiBaseMsgBean);

            TreeSet<String> allDates = getAllDates(dataprobiBaseMsgBean.getQd());
            if (CollectionUtils.isEmpty(allDates)
                    && dataprobiBaseMsgBean.getSplb() == 0
                    && CollectionUtils.isEmpty(dataprobiBaseMsgBean.getDpMonths())) {
                return true;
            }

            List<BiDealProcess> biProcesses = getProcessesByQd(dataprobiBaseMsgBean.getQd());
            if (biProcesses == null) {
                return true;
            }

            BiInfo biInfo = DataUploader.getBiinfoById(dataprobiBaseMsgBean.getBiinfoId());
            if (biInfo != null) {
                BiThreadLocals.addBiInfo(biInfo);
                boolean b = DataUploader.testYh(dataprobiBaseMsgBean.getQd(), dataprobiBaseMsgBean.getUserid(), dataprobiBaseMsgBean.getYhid());
                if (b) {
                    BiThreadLocals.setTestYh();
                }

            }

            before();

            boolean flag = dataCompare();
            biProcesses.forEach(biProcess -> {
                biProcess.dealAll(dataprobiBaseMsgBean.getType());
            });

            BiYhdp biYhdp = null;
            if (biInfo != null) {
                DataUploader.updateBiInfoClrq(biInfo, dataprobiBaseMsgBean);
                biYhdp = getBiYhdp();
            }

            if (!flag && CollectionUtils.isNotEmpty(allDates)) {
                updateBiYhdp(biYhdp);
            }

            if (CollectionUtils.isNotEmpty(dataprobiBaseMsgBean.getDpMonths())) {
                biSumStrategy.sumFirst();
            }

            if (CollectionUtils.isNotEmpty(allDates)) {
                dbAdd();
            }
            return true;
        } catch (Throwable e) {
            LOG.error(e.getMessage(), e);
        } finally {
            if (lockObj != null) {
                try {
                    after();
                } catch (Exception e) {
                    LOG.error(e.getMessage(), e);
                }
            }

            dataprobiMsgLogTsDao.end(dataprobiBaseMsgBean);

            distributedLock.unlock(lockObj);
            BiThreadLocals.remove();
        }
        return false;
    }

    private void dbAdd() {
        try {
            TreeSet<String> allDates = BiThreadLocals.getAllDates();
            for (String rq : allDates) {
                spDbAdd(rq);
                dpDbAdd(rq);
            }
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
    }

    private void dpDbAdd(String rq) {
        String ossPath = String.format(DataprobiConst.SYCM_DP_DAY, rq.replaceAll("-", ""), BiThreadLocals.getMsgBean().getYhid(), BiThreadLocals.getMsgBean().getQd(), BiThreadLocals.getMsgBean().getUserid());
        if (biDataOssUtil.exist(ossPath)) {
            return;
        }

        biDataOssUtil.copyFile("biconst/sycm_dp.db", ossPath);
    }

    private void spDbAdd(String rq) {
        String ossPath = String.format(DataprobiConst.SYCM_SP_DAY, rq.replaceAll("-", ""), BiThreadLocals.getMsgBean().getYhid(), BiThreadLocals.getMsgBean().getQd(), BiThreadLocals.getMsgBean().getUserid());
        if (biDataOssUtil.exist(ossPath)) {
            return;
        }

        biDataOssUtil.copyFile("biconst/sycm_sp.db", ossPath);
    }

    protected void before() {

    }

    /**
     * 1.对下午补采的数据进行比较 如果下午采集后的数据和上午的数据一致 则不需要重新处理了 直接return
     *
     * @return true 不需要比对数据 或者 比对数据失败
     */
    private boolean dataCompare() {
        if (BiThreadLocals.getMsgBean().getType() != 2) {
            return false;
        }
        return dataCompare2();
    }

    private boolean dataCompare2() {
        LOG.info(BiThreadLocals.getMsgBean().getDpmc() + "dataCompare2开始：" + DataprobiDateUtil.getCurrentTime());
        boolean compareResult = true;
        List<BiDealProcess> biProcesses = getProcessesByQd(BiThreadLocals.getMsgBean().getQd());
        assert biProcesses != null;
        for (BiDealProcess biProcess : biProcesses) {
            LOG.info(BiThreadLocals.getMsgBean().getDpmc() + "," + this.getClass().getName() + "下午补采比对开始：" + DataprobiDateUtil.getCurrentTime());
            boolean flag = biProcess.compareGoods();
            if (compareResult && !flag) {
                compareResult = false;
            }

            flag = biProcess.compareShop();
            if (compareResult && !flag) {
                compareResult = false;
            }
            LOG.info(BiThreadLocals.getMsgBean().getDpmc() + "," + this.getClass().getName() + "下午补采比对结束：" + DataprobiDateUtil.getCurrentTime());
        }
        LOG.info(BiThreadLocals.getMsgBean().getDpmc() + "dataCompare2结束：" + DataprobiDateUtil.getCurrentTime() + ",结果：" + compareResult);
        return compareResult;
    }


    /**
     * 更新bi_yhdp表
     *
     * @param biYhdp
     */
    private void updateBiYhdp(BiYhdp biYhdp) {
        if (biYhdp != null && biYhdp.isUpdate()) {
            DataUploader.updateBiYhdp(biYhdp);
        }

        if (BiThreadLocals.getRerunTask() != null && BiThreadLocals.getMsgBean().getAuto() != 2) {
            DataUploader.rerunTask(BiThreadLocals.getRerunTask());
        }
    }

    /**
     * 获取处理日期JSON
     *
     * @param qdDesc
     * @return
     */
    private List<Object> getClrqJson(String qdDesc) {
        try {
            Qd qd = Qd.getQdByQdDesc(qdDesc);
            if (qd == null) {
                return null;
            }

            String clrqjson = BiThreadLocals.getBiInfo().getClrqjson();
            List<Object> list = new ArrayList<>();

            JSONObject jsonObject = JSONObject.parseObject(clrqjson);
            for (String key : jsonObject.keySet()) {
                try {
                    Field field = qd.gettClass().getDeclaredField(key);
                    if (field.getAnnotation(UpdateSycmcljzrqIgnore.class) == null) {
                        list.add(jsonObject.get(key));
                    }
                } catch (NoSuchFieldException e) {
                }
            }
            return list.stream().sorted().collect(Collectors.toList());
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
            return null;
        }
    }

    private BiYhdp getBiYhdp() {
        BiInfo biInfo = BiThreadLocals.getBiInfo();

        String clrqjson = biInfo.getClrqjson();
        if (StringUtils.isBlank(clrqjson)) {
            return null;
        }

        List<Object> list = getClrqJson(biInfo.getQd());
        if (list != null && list.size() > 0) {
            if (list.get(0) == null) {
                return null;
            }

            String minDate = (String) list.get(0);
            BiYhdp biYhdp = DataUploader.getBiYhdpByYhidAndUseridQd(biInfo.getUserid(), biInfo.getQd(), BiThreadLocals.getMsgBean().getYhid());
            if (biYhdp != null) {
                String sycmcljzrq = biYhdp.getSycmcljzrq();
                if (StringUtils.isBlank(minDate)) {
                    return null;
                }
                //后期要改成minDate.compareTo(sycmcljzrq) > 0
                //这2个差别很大的
                if (StringUtils.isBlank(sycmcljzrq)) {
                    biYhdp.setSycmcljzrq(minDate);
                    biYhdp.setUpdate(true);
                    BiThreadLocals.addBiYhdp(biYhdp);
                    return biYhdp;
                } else if (minDate.compareTo(sycmcljzrq) > 0) {
                    if (CollectionUtils.isNotEmpty(BiThreadLocals.getCsAllDates()) && BiThreadLocals.getCsAllDates().first().compareTo(sycmcljzrq) <= 0) {
                        //提交重算任务 从 Bithread.first 到 sycmcljzrq
                        submitRerunTask2(biYhdp, BiThreadLocals.getCsAllDates().first(), sycmcljzrq);
                    }

                    biYhdp.setSycmcljzrq(minDate);
                    biYhdp.setUpdate(true);
                    BiThreadLocals.addBiYhdp(biYhdp);
                    return biYhdp;
                }
                if (minDate.compareTo(sycmcljzrq) <= 0) {
                    BiThreadLocals.addBiYhdp(biYhdp);
                    submitRerunTask(biYhdp);
                    return biYhdp;
                }
                if (BiThreadLocals.getMsgBean().getAuto() == 1) {
                    BiThreadLocals.addBiYhdp(biYhdp);
                    return biYhdp;
                }
            }
        }
        return null;
    }


    /**
     * 通过渠道获取对应处理程序
     *
     * @param qdDesc
     * @return
     */
    private List<BiDealProcess> getProcessesByQd(String qdDesc) {
        List<BiDealProcess> list = null;
        Qd qd = Qd.getQdByQdDesc(qdDesc);
        if (qd == null) {
            return null;
        }
        if (qd == Qd.TM) {
            qd = Qd.TB;
        }
        if (CollectionUtils.isNotEmpty(list = map.get(qd))) {
            return list;
        }

        try {
            Map<String, Object> beans = SpringBeanUtil.getContext().getSpringContext().getBeansWithAnnotation(BiPro.class);
            Qd temp = qd;
            list = beans.values()
                    .stream()
                    .filter(bean -> bean instanceof BiDealProcess)
                    .filter(bean -> {
                        BiPro biPro = AnnotationUtils.findAnnotation(bean.getClass(), BiPro.class);
                        return biPro.qd() == temp;
                    })
                    .sorted((a, b) -> {
                        BiPro biPro1 = AnnotationUtils.findAnnotation(a.getClass(), BiPro.class);
                        BiPro biPro2 = AnnotationUtils.findAnnotation(b.getClass(), BiPro.class);
                        return biPro1.order() - biPro2.order();
                    })
                    .map(obj -> (BiDealProcess) obj)
                    .collect(Collectors.toList());
            return list;
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
        return null;
    }


    protected void after() {

    }

    /**
     * 获取所有的处理日期 并排序 （小-->大）
     *
     * @param qdDesc
     * @return
     */
    private TreeSet<String> getAllDates(String qdDesc) {
        Qd qd = Qd.getQdByQdDesc(qdDesc);
        if (qd == null) {
            return null;
        }

        TreeSet<String> allDates = new TreeSet<>();
        try {
            Field[] fields = qd.gettClass().getDeclaredFields();
            if (fields.length == 0) {
                return allDates;
            }
            for (Field field : fields) {
                Class<?> type = field.getType();
                if (type == List.class && field.getAnnotation(SumIgnore.class) == null) {
                    allDates.addAll((List) qd.gettClass().getMethod("get" + DataprobiStringUtil.strFirstCharUpper(field.getName())).invoke(BiThreadLocals.getMsgBean()));
                }
            }

            initCsAllDates(qdDesc);
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        } finally {
            BiThreadLocals.addAllDates(allDates);
        }
        return allDates;
    }

    private TreeSet<String> initCsAllDates(String qdDesc) {
        Qd qd = Qd.getQdByQdDesc(qdDesc);
        if (qd == null) {
            return null;
        }

        TreeSet<String> allDates = new TreeSet<>();
        try {
            Field[] fields = qd.gettClass().getDeclaredFields();
            if (fields.length == 0) {
                return allDates;
            }
            for (Field field : fields) {
                Class<?> type = field.getType();
                if (type == List.class && field.getAnnotation(SumIgnore.class) == null && field.getAnnotation(Beta.class) == null) {
                    allDates.addAll((List) qd.gettClass().getMethod("get" + DataprobiStringUtil.strFirstCharUpper(field.getName())).invoke(BiThreadLocals.getMsgBean()));
                }
            }
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        } finally {
            BiThreadLocals.addCsAllDates(allDates);
        }
        return allDates;
    }

    private void submitRerunTask(BiYhdp biYhdp) {
        if (CollectionUtils.isEmpty(BiThreadLocals.getCsAllDates())) {
            return;
        }
        BiRuleReRunTask task = new BiRuleReRunTask();
        task.setId("sycm_dp_" + UUID.randomUUID());
        task.setYhid(biYhdp.getYhid());
        task.setQd(biYhdp.getQd());
        task.setUserid(biYhdp.getUserid());

        try {
            String first = BiThreadLocals.getCsAllDates().first();
            if (first.compareTo(DataprobiDateUtil.getMonthBeforeCurrMonthAsFirstday()) < 0) {
                LOG.info("重算日期大于上上月1日！当前店铺：" + biYhdp.getDpmc() + ",当前渠道："
                        + biYhdp.getQd() + ",当前消息体：" + BiThreadLocals.getMsgBean()
                        + ",当前时间：" + DataprobiDateUtil.getCurrentTime());
                return;
            }
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }

        task.setKsrq(BiThreadLocals.getCsAllDates().first());
        String last = BiThreadLocals.getCsAllDates().last();
        if (biYhdp.getSycmjzrq().compareTo(last) <= 0) {
            task.setJsrq(biYhdp.getSycmjzrq());
        } else {
            task.setJsrq(last);
        }
        task.setCjsj(DataprobiDateUtil.getCurrentTime());
        task.setZt(0);
        task.setType(16);
        task.setCjrxm("BI推广数据处理系统");
        if (BiThreadLocals.getMsgLx() == 0) {
            task.setReason("补采重算");
        } else if (BiThreadLocals.getMsgLx() == 1) {
            task.setReason("推广花费设置重算");
        }
        if (task.getKsrq().compareTo(task.getJsrq()) <= 0) {
            BiThreadLocals.addReRunTask(task);
        }
    }

    private void submitRerunTask2(BiYhdp biYhdp, String ksrq, String jsrq) {
        BiRuleReRunTask task = new BiRuleReRunTask();
        task.setId("sycm_dp_" + UUID.randomUUID());
        task.setYhid(biYhdp.getYhid());
        task.setQd(biYhdp.getQd());
        task.setUserid(biYhdp.getUserid());
        task.setKsrq(BiThreadLocals.getCsAllDates().first());
        task.setJsrq(jsrq);
        task.setCjsj(DataprobiDateUtil.getCurrentTime());
        task.setZt(0);
        task.setType(16);
        task.setCjrxm("BI推广数据处理系统");
        if (BiThreadLocals.getMsgLx() == 0) {
            task.setReason("补采重算");
        } else if (BiThreadLocals.getMsgLx() == 1) {
            task.setReason("推广花费设置重算");
        }
        if (task.getKsrq().compareTo(task.getJsrq()) <= 0) {
            BiThreadLocals.addReRunTask(task);
        }
    }

    protected void sendDataprobiTask(DataprobiBaseMsgBean dataprobiBaseMsgBean, List<String> rqList, String taskType, boolean fbp) {
        sendDataprobiTask(dataprobiBaseMsgBean, rqList, taskType, DataprobiConst.TYPE_DATAPROBITASK, fbp);
    }

    protected void sendDataprobiTask(DataprobiBaseMsgBean dataprobiBaseMsgBean, List<String> rqList, String taskType) {
        sendDataprobiTask(dataprobiBaseMsgBean, rqList, taskType, DataprobiConst.TYPE_DATAPROBITASK, false);
    }

    protected void sendDataprobiTaskTgfx(DataprobiBaseMsgBean dataprobiBaseMsgBean, List<String> rqList, String taskType) {
        sendDataprobiTaskTgfx(dataprobiBaseMsgBean, rqList, taskType, false);
    }

    protected void sendDataprobiTaskXkfx(DataprobiBaseMsgBean dataprobiBaseMsgBean, List<String> rqList, String taskType) {
        sendDataprobiTaskXkfx(dataprobiBaseMsgBean, rqList, taskType, false);
    }

    protected void sendDataprobiTaskTgfx(DataprobiBaseMsgBean dataprobiBaseMsgBean, List<String> rqList, String taskType, boolean fbp) {
        try {
            if (CollectionUtils.isEmpty(rqList)) {
                return;
            }

            DataprobiTaskMsg taskMsg = new DataprobiTaskMsg();
            taskMsg.setDpmc(dataprobiBaseMsgBean.getDpmc());
            taskMsg.setTaskType(taskType);
            taskMsg.setQd(dataprobiBaseMsgBean.getQd());
            taskMsg.setYhid(dataprobiBaseMsgBean.getYhid());
            taskMsg.setRqList(rqList);
            taskMsg.setUserid(dataprobiBaseMsgBean.getUserid());
            taskMsg.setUuid(UUID.randomUUID().toString());
            if (!fbp) {
                aliyunMq2.send("dataprobi_tgfx", UUID.randomUUID().toString(), new JobMessage("dataprobi_tgfx_processor", taskMsg));
            } else {
                aliyunMq2.send("dataprobi_task_fbp", UUID.randomUUID().toString(), new JobMessage("dataprobi_task_fbp_processor", taskMsg));
            }
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
    }

    protected void sendDataprobiTaskXkfx(DataprobiBaseMsgBean dataprobiBaseMsgBean, List<String> rqList, String taskType, boolean fbp) {
        try {
            if (CollectionUtils.isEmpty(rqList)) {
                return;
            }

            DataprobiTaskMsg taskMsg = new DataprobiTaskMsg();
            taskMsg.setDpmc(dataprobiBaseMsgBean.getDpmc());
            taskMsg.setTaskType(taskType);
            taskMsg.setQd(dataprobiBaseMsgBean.getQd());
            taskMsg.setYhid(dataprobiBaseMsgBean.getYhid());
            taskMsg.setRqList(rqList);
            taskMsg.setUserid(dataprobiBaseMsgBean.getUserid());
            taskMsg.setUuid(UUID.randomUUID().toString());
            if (!fbp) {
                aliyunMq2.send("dataprobi_xkfx", UUID.randomUUID().toString(), new JobMessage("dataprobi_xkfx_processor", taskMsg));
            } else {
                aliyunMq2.send("dataprobi_task_fbp", UUID.randomUUID().toString(), new JobMessage("dataprobi_task_fbp_processor", taskMsg));
            }
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
    }

    protected void sendDataprobiTask(DataprobiBaseMsgBean dataprobiBaseMsgBean, List<String> rqList, String taskType, String type, boolean fbp) {
        try {
            if (CollectionUtils.isEmpty(rqList)) {
                return;
            }

            DataprobiTaskMsg taskMsg = new DataprobiTaskMsg();
            taskMsg.setDpmc(dataprobiBaseMsgBean.getDpmc());
            taskMsg.setTaskType(taskType);
            taskMsg.setQd(dataprobiBaseMsgBean.getQd());
            taskMsg.setYhid(dataprobiBaseMsgBean.getYhid());
            taskMsg.setRqList(rqList);
            taskMsg.setUserid(dataprobiBaseMsgBean.getUserid());
            taskMsg.setType(type);
            taskMsg.setUuid(UUID.randomUUID().toString());
            if (!fbp) {
                aliyunMq2.send("dataprobi_task", UUID.randomUUID().toString(), new JobMessage("dataprobi_task_processor", taskMsg));
            } else {
                aliyunMq2.send("dataprobi_task_fbp", UUID.randomUUID().toString(), new JobMessage("dataprobi_task_fbp_processor", taskMsg));
            }
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
    }

    protected final <T extends DataprobiBaseMsgBean> void sendDataprobiCompoent(T t) {
        aliyunMq2.send("dataprobi_compoent", UUID.randomUUID().toString(), new JobMessage("dataprobi_compoent_processor", t));
    }
}
