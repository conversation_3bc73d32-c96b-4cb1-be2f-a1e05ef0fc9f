package com.abujlb.dataprobi.service;

import com.abujlb.dataprobi.bean.wph.DataprobiWphMsg;
import com.abujlb.dataprobi.bean.xhs.DataprobiXhsMsg;
import com.abujlb.dataprobi.constant.TaskTypeConst;
import com.abujlb.dataprobi.threads.BiThreadLocals;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @date 2024/7/9
 */
@Component
public class BiWphDataDealService extends BiService {

    @Override
    protected void after() {
        DataprobiWphMsg dataprobiWphMsg = (DataprobiWphMsg) BiThreadLocals.getMsgBean();
        sendDataprobiTask(dataprobiWphMsg, dataprobiWphMsg.getWxkyjyghf(), TaskTypeConst.WPH_YGHF_WXKYJ);
        sendDataprobiCompoent(dataprobiWphMsg);

    }
}
