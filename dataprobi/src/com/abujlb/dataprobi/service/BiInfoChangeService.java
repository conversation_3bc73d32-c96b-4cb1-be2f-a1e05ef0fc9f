package com.abujlb.dataprobi.service;


import com.abujlb.dataprobi.bean.BiInfo;
import com.abujlb.dataprobi.bean.BiInfoChangeMsgBean;
import com.abujlb.dataprobi.bean.BiYhdp;
import com.abujlb.dataprobi.bean.DataprobiTaskMsg;
import com.abujlb.dataprobi.constant.DataprobiConst;
import com.abujlb.dataprobi.constant.TaskTypeConst;
import com.abujlb.dataprobi.enums.Qd;
import com.abujlb.dataprobi.oss.BiDataOssUtil;
import com.abujlb.dataprobi.server.DataUploader;
import com.abujlb.dataprobi.sql.BiSqliteDb;
import com.abujlb.dataprobi.util.DataprobiDateUtil;
import com.abujlb.dataprobi.util.FileUtil;
import com.abujlb.dataprobi.util.SqliteDbUtil;
import com.abujlb.mq.AliyunMq2;
import com.abujlb.mq.JobMessage;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.Statement;
import java.util.List;
import java.util.UUID;

@Component
public class BiInfoChangeService {

    private static final Logger LOG = Logger.getLogger(BiInfoChangeService.class);

    @Autowired
    private BiDataOssUtil biDataOssUtil;
    @Autowired
    private AliyunMq2 aliyunMq2;

    public void process(BiInfoChangeMsgBean infoChangeMsgBean) {
        //开始前  需要保证 当前没有进行中的处理任务
        try {
            BiInfo oldInfo = DataUploader.getBiinfoById(infoChangeMsgBean.getOriginInfoId());
            BiInfo newInfo = DataUploader.getBiinfoById(infoChangeMsgBean.getNewInfoId());
            if (oldInfo == null || newInfo == null) {
                return;
            }
            //1、DB文件拷贝
            LOG.info("DB文件拷贝开始，" + DataprobiDateUtil.getCurrentTime());
            dbCopy(oldInfo, newInfo);
            LOG.info("DB文件拷贝结束，" + DataprobiDateUtil.getCurrentTime());
            //2、账单文件等拷贝
            LOG.info("账单文件拷贝开始，" + DataprobiDateUtil.getCurrentTime());
            billCopy(oldInfo, newInfo);
            LOG.info("账单文件拷贝结束，" + DataprobiDateUtil.getCurrentTime());
            //3、推广分析(不用处理)、选款分析等数据处理
            otherDataCopy(oldInfo, newInfo);
            //4、更新一些日期  sycmclksrq sycmcljzrq zdksrq zdjsrq 等
            updateRq(oldInfo, newInfo);
        } catch (Throwable e) {
            LOG.error(e.getMessage(), e);
        }
    }

    private void updateRq(BiInfo oldInfo, BiInfo newInfo) {
        //更新 bi_info  new   的日期
        if (oldInfo.getSycmcljzrq() != null) {
            newInfo.setSycmcljzrq(oldInfo.getSycmcljzrq());
            DataUploader.updateBiInfoSycmcljzrq(newInfo);
        }
        //更新 bi_yhdp 表的日期
        BiYhdp oldbiYhdp = DataUploader.getBiYhdpByYhidAndUseridQd(oldInfo.getUserid(), oldInfo.getQd(), oldInfo.getYhid(), 2);

        BiYhdp newbiYhdp = DataUploader.getBiYhdpByYhidAndUseridQd(newInfo.getUserid(), newInfo.getQd(), newInfo.getYhid());
        if (oldbiYhdp != null && newbiYhdp != null) {
            newbiYhdp.setSycmclksrq(oldbiYhdp.getSycmclksrq());
            newbiYhdp.setSycmcljzrq(oldbiYhdp.getSycmcljzrq());
            if (oldbiYhdp.getQd().equals(Qd.JD.getQdDesc())
                    || oldbiYhdp.getQd().equals(Qd.PDD.getQdDesc())
                    || oldbiYhdp.getQd().equals(Qd.DY.getQdDesc())
            ) {
                newbiYhdp.setZdksrq(oldbiYhdp.getZdksrq());
                newbiYhdp.setZdjsrq(oldbiYhdp.getZdjsrq());
                if (StringUtils.isNotBlank(newbiYhdp.getZdksrq()) && StringUtils.isNotBlank(newbiYhdp.getZdjsrq()))
                    DataUploader.updateBiYhdpAlipayrq(newbiYhdp.getUserid(), newbiYhdp.getQd(), newbiYhdp.getZdksrq(), newbiYhdp.getZdjsrq());
            }
        }
        DataUploader.updateBiYhdp(newbiYhdp);
    }

    private void dbCopy(BiInfo oldInfo, BiInfo newInfo) {
        String start = oldInfo.getStart();
        String end = DataprobiDateUtil.getLastday();
        List<String> rqList = DataprobiDateUtil.getBetweenDate(start, end);
        for (String rq : rqList) {
            String spKey = String.format(DataprobiConst.SYCM_SP_DAY, rq.replace("-", ""), oldInfo.getYhid(), oldInfo.getQd(), oldInfo.getUserid());
            String spKeyNew = String.format(DataprobiConst.SYCM_SP_DAY, rq.replace("-", ""), newInfo.getYhid(), newInfo.getQd(), newInfo.getUserid());
            String dpKey = String.format(DataprobiConst.SYCM_DP_DAY, rq.replace("-", ""), oldInfo.getYhid(), oldInfo.getQd(), oldInfo.getUserid());
            String dpKeyNew = String.format(DataprobiConst.SYCM_DP_DAY, rq.replace("-", ""), newInfo.getYhid(), newInfo.getQd(), newInfo.getUserid());
            String spuKey = String.format(DataprobiConst.SYCM_SPU_DAY, rq.replace("-", ""), oldInfo.getYhid(), oldInfo.getQd(), oldInfo.getUserid());
            String spuKeyNew = String.format(DataprobiConst.SYCM_SPU_DAY, rq.replace("-", ""), newInfo.getYhid(), newInfo.getQd(), newInfo.getUserid());
            dbCopy(newInfo, spKey, spKeyNew, "sycm_sp");
            dbCopy(newInfo, dpKey, dpKeyNew, "sycm_dp");
            dbCopy(newInfo, spuKey, spuKeyNew, "sycm_spu");
        }
    }

    private void dbCopy(BiInfo newInfo, String key, String keynew, String tableName) {
        if (!biDataOssUtil.exist(key)) {
            return;
        }

        String dbFilePath = FileUtil.createDBFilePath();
        biDataOssUtil.download(key, dbFilePath);

        BiSqliteDb biSqliteDb = null;
        Statement statement = null;
        try {
            biSqliteDb = new BiSqliteDb(dbFilePath);

            statement = biSqliteDb.createStatement();
            biSqliteDb.startTransaction();
            String sql = "update " + tableName + " set `yhid` = " + newInfo.getYhid() + ",`qd`='" + newInfo.getQd() + "';";
            statement.executeUpdate(sql);

            if (tableName.equals("sycm_sp") || tableName.equals("sycm_spu")) {
                String sql2 = "update " + tableName + " set `qd_userid_bbid` = qd||'_'||userid||'_'||bbid ;";
                statement.executeUpdate(sql2);
            } else if (tableName.equals("sycm_dp")) {
                String sql2 = "update " + tableName + " set `qd_userid` = qd||'_'||userid ;";
                statement.executeUpdate(sql2);
            }

            biSqliteDb.commit();
            biSqliteDb.endTransaction();

            biDataOssUtil.upload(biSqliteDb.getFileD(), keynew);
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            SqliteDbUtil.closeStatement(statement);
            SqliteDbUtil.close(biSqliteDb);
        }
    }

    private void billCopy(BiInfo oldInfo, BiInfo newInfo) {
        String start = oldInfo.getStart();
        String end = DataprobiDateUtil.getLastday();
        List<String> rqList = DataprobiDateUtil.getBetweenDate(start, end);
        for (String rq : rqList) {
            if (oldInfo.getQd().equals(Qd.TM.getQdDesc()) || oldInfo.getQd().equals(Qd.TB.getQdDesc())) {

                String bzjKey_old = "bi_data/day/" + rq.replace("-", "") + "/" + oldInfo.getYhid() + "/" + oldInfo.getQd() + "/" + oldInfo.getUserid() + "/tmbzj.zip";
                String bzjKey_new = "bi_data/day/" + rq.replace("-", "") + "/" + newInfo.getYhid() + "/" + newInfo.getQd() + "/" + newInfo.getUserid() + "/tmbzj.zip";
                biDataOssUtil.copyFile(bzjKey_old, bzjKey_new);

                String wxzfzdKey_old = "bi_data/day/" + rq.replace("-", "") + "/" + oldInfo.getYhid() + "/" + oldInfo.getQd() + "/" + oldInfo.getUserid() + "/fundbill.xlsx";
                String wxzfzdKey_new = "bi_data/day/" + rq.replace("-", "") + "/" + newInfo.getYhid() + "/" + newInfo.getQd() + "/" + newInfo.getUserid() + "/fundbill.xlsx";
                biDataOssUtil.copyFile(wxzfzdKey_old, wxzfzdKey_new);

                String gjbKey_old = "bi_data/day/" + rq.replace("-", "") + "/" + oldInfo.getYhid() + "/" + oldInfo.getQd() + "/" + oldInfo.getUserid() + "/gjbdd2.json";
                String gjbKey_new = "bi_data/day/" + rq.replace("-", "") + "/" + newInfo.getYhid() + "/" + newInfo.getQd() + "/" + newInfo.getUserid() + "/gjbdd2.json";
                biDataOssUtil.copyFile(gjbKey_old, gjbKey_new);

                String xedkKey_old = "bi_data/day/" + rq.replace("-", "") + "/" + oldInfo.getYhid() + "/" + oldInfo.getQd() + "/" + oldInfo.getUserid() + "/xedk.xlsx";
                String xedkKey_new = "bi_data/day/" + rq.replace("-", "") + "/" + newInfo.getYhid() + "/" + newInfo.getQd() + "/" + newInfo.getUserid() + "/xedk.xlsx";
                biDataOssUtil.copyFile(xedkKey_old, xedkKey_new);
            } else if (oldInfo.getQd().equals(Qd.PDD.getQdDesc())) {
                String hkbillKey_old = "bi_data/day/" + rq.replace("-", "") + "/" + oldInfo.getYhid() + "/" + oldInfo.getQd() + "/" + oldInfo.getUserid() + "/货款账户.zip";
                String hkbillKey_new = "bi_data/day/" + rq.replace("-", "") + "/" + newInfo.getYhid() + "/" + newInfo.getQd() + "/" + newInfo.getUserid() + "/货款账户.zip";
                biDataOssUtil.copyFile(hkbillKey_old, hkbillKey_new);

                String yxbillKey_old = "bi_data/day/" + rq.replace("-", "") + "/" + oldInfo.getYhid() + "/" + oldInfo.getQd() + "/" + oldInfo.getUserid() + "/营销账户.zip";
                String yxbillKey_new = "bi_data/day/" + rq.replace("-", "") + "/" + newInfo.getYhid() + "/" + newInfo.getQd() + "/" + newInfo.getUserid() + "/营销账户.zip";
                biDataOssUtil.copyFile(yxbillKey_old, yxbillKey_new);

                String bzjbillKey_old = "bi_data/day/" + rq.replace("-", "") + "/" + oldInfo.getYhid() + "/" + oldInfo.getQd() + "/" + oldInfo.getUserid() + "/保证金账户.zip";
                String bzjbillKey_new = "bi_data/day/" + rq.replace("-", "") + "/" + newInfo.getYhid() + "/" + newInfo.getQd() + "/" + newInfo.getUserid() + "/保证金账户.zip";
                biDataOssUtil.copyFile(bzjbillKey_old, bzjbillKey_new);

                String billsumKey_old = "bi_data/day/" + rq.replace("-", "") + "/" + oldInfo.getYhid() + "/" + oldInfo.getQd() + "/" + oldInfo.getUserid() + "/daybill.json";
                String billsumKey_new = "bi_data/day/" + rq.replace("-", "") + "/" + newInfo.getYhid() + "/" + newInfo.getQd() + "/" + newInfo.getUserid() + "/daybill.json";
                biDataOssUtil.copyFile(billsumKey_old, billsumKey_new);

                String bybtKey_old = "bi_data/day/" + rq.replace("-", "") + "/" + oldInfo.getYhid() + "/" + oldInfo.getQd() + "/" + oldInfo.getUserid() + "/pdd_bybt.db";
                String bybtKey_new = "bi_data/day/" + rq.replace("-", "") + "/" + newInfo.getYhid() + "/" + newInfo.getQd() + "/" + newInfo.getUserid() + "/pdd_bybt.db";
                biDataOssUtil.copyFile(bybtKey_old, bybtKey_new);
            } else if (oldInfo.getQd().equals(Qd.DY.getQdDesc())) {
                String old1 = "bi_data/day/" + rq.replace("-", "") + "/" + oldInfo.getYhid() + "/" + oldInfo.getQd() + "/" + oldInfo.getUserid() + "/daybill.json";
                String new1 = "bi_data/day/" + rq.replace("-", "") + "/" + newInfo.getYhid() + "/" + newInfo.getQd() + "/" + newInfo.getUserid() + "/daybill.json";
                biDataOssUtil.copyFile(old1, new1);

                String old2 = "bi_data/day/" + rq.replace("-", "") + "/" + oldInfo.getYhid() + "/" + oldInfo.getQd() + "/" + oldInfo.getUserid() + "/bill.csv";
                String new2 = "bi_data/day/" + rq.replace("-", "") + "/" + newInfo.getYhid() + "/" + newInfo.getQd() + "/" + newInfo.getUserid() + "/bill.csv";
                biDataOssUtil.copyFile(old2, new2);

                String old3 = "bi_data/day/" + rq.replace("-", "") + "/" + oldInfo.getYhid() + "/" + oldInfo.getQd() + "/" + oldInfo.getUserid() + "/dybzj.csv";
                String new3 = "bi_data/day/" + rq.replace("-", "") + "/" + newInfo.getYhid() + "/" + newInfo.getQd() + "/" + newInfo.getUserid() + "/dybzj.csv";
                biDataOssUtil.copyFile(old3, new3);

                String old4 = "bi_data/day/" + rq.replace("-", "") + "/" + oldInfo.getYhid() + "/" + oldInfo.getQd() + "/" + oldInfo.getUserid() + "/fundbill.json";
                String new4 = "bi_data/day/" + rq.replace("-", "") + "/" + newInfo.getYhid() + "/" + newInfo.getQd() + "/" + newInfo.getUserid() + "/fundbill.json";
                biDataOssUtil.copyFile(old4, new4);

                String old5 = "bi_data/day/" + rq.replace("-", "") + "/" + oldInfo.getYhid() + "/" + oldInfo.getQd() + "/" + oldInfo.getUserid() + "/bill_allowance.csv";
                String new5 = "bi_data/day/" + rq.replace("-", "") + "/" + newInfo.getYhid() + "/" + newInfo.getQd() + "/" + newInfo.getUserid() + "/bill_allowance.csv";
                biDataOssUtil.copyFile(old5, new5);

                String old6 = "bi_data/day/" + rq.replace("-", "") + "/" + oldInfo.getYhid() + "/" + oldInfo.getQd() + "/" + oldInfo.getUserid() + "/bill_monthpay_allowance.csv";
                String new6 = "bi_data/day/" + rq.replace("-", "") + "/" + newInfo.getYhid() + "/" + newInfo.getQd() + "/" + newInfo.getUserid() + "/bill_monthpay_allowance.csv";
                biDataOssUtil.copyFile(old6, new6);

                String old7 = "bi_data/day/" + rq.replace("-", "") + "/" + oldInfo.getYhid() + "/" + oldInfo.getQd() + "/" + oldInfo.getUserid() + "/pydqfwf.xlsx";
                String new7 = "bi_data/day/" + rq.replace("-", "") + "/" + newInfo.getYhid() + "/" + newInfo.getQd() + "/" + newInfo.getUserid() + "/pydqfwf.xlsx";
                biDataOssUtil.copyFile(old7, new7);

                String old8 = "bi_data/day/" + rq.replace("-", "") + "/" + oldInfo.getYhid() + "/" + oldInfo.getQd() + "/" + oldInfo.getUserid() + "/dy_xedk.zip";
                String new8 = "bi_data/day/" + rq.replace("-", "") + "/" + newInfo.getYhid() + "/" + newInfo.getQd() + "/" + newInfo.getUserid() + "/dy_xedk.zip";
                biDataOssUtil.copyFile(old8, new8);

                String old9 = "bi_data/day/" + rq.replace("-", "") + "/" + oldInfo.getYhid() + "/" + oldInfo.getQd() + "/" + oldInfo.getUserid() + "/my_spk.zip";
                String new9 = "bi_data/day/" + rq.replace("-", "") + "/" + newInfo.getYhid() + "/" + newInfo.getQd() + "/" + newInfo.getUserid() + "/my_spk.zip";
                biDataOssUtil.copyFile(old9, new9);

                String old10 = "bi_data/day/" + rq.replace("-", "") + "/" + oldInfo.getYhid() + "/" + oldInfo.getQd() + "/" + oldInfo.getUserid() + "/my_tw.zip";
                String new10 = "bi_data/day/" + rq.replace("-", "") + "/" + newInfo.getYhid() + "/" + newInfo.getQd() + "/" + newInfo.getUserid() + "/my_tw.zip";
                biDataOssUtil.copyFile(old10, new10);
            } else if (oldInfo.getQd().equals(Qd.JD.getQdDesc())) {
                String old1 = "bi_data/day/" + rq.replace("-", "") + "/" + oldInfo.getYhid() + "/" + oldInfo.getQd() + "/" + oldInfo.getUserid() + "/fbp_dyfx.xlsx";
                String new1 = "bi_data/day/" + rq.replace("-", "") + "/" + newInfo.getYhid() + "/" + newInfo.getQd() + "/" + newInfo.getUserid() + "/fbp_dyfx.xlsx";
                biDataOssUtil.copyFile(old1, new1);

                String old2 = "bi_data/day/" + rq.replace("-", "") + "/" + oldInfo.getYhid() + "/" + oldInfo.getQd() + "/" + oldInfo.getUserid() + "/jd_orders.zip";
                String new2 = "bi_data/day/" + rq.replace("-", "") + "/" + newInfo.getYhid() + "/" + newInfo.getQd() + "/" + newInfo.getUserid() + "/jd_orders.zip";
                biDataOssUtil.copyFile(old2, new2);

                String old3 = "bi_data/day/" + rq.replace("-", "") + "/" + oldInfo.getYhid() + "/" + oldInfo.getQd() + "/" + oldInfo.getUserid() + "/jd_bybt.db";
                String new3 = "bi_data/day/" + rq.replace("-", "") + "/" + newInfo.getYhid() + "/" + newInfo.getQd() + "/" + newInfo.getUserid() + "/jd_bybt.db";
                biDataOssUtil.copyFile(old3, new3);

                String old4 = "bi_data/day/" + rq.replace("-", "") + "/" + oldInfo.getYhid() + "/" + oldInfo.getQd() + "/" + oldInfo.getUserid() + "/jd_pyby.db";
                String new4 = "bi_data/day/" + rq.replace("-", "") + "/" + newInfo.getYhid() + "/" + newInfo.getQd() + "/" + newInfo.getUserid() + "/jd_pyby.db";
                biDataOssUtil.copyFile(old4, new4);

                String old5 = "bi_data/day/" + rq.replace("-", "") + "/" + oldInfo.getYhid() + "/" + oldInfo.getQd() + "/" + oldInfo.getUserid() + "/jd_jdb.csv";
                String new5 = "bi_data/day/" + rq.replace("-", "") + "/" + newInfo.getYhid() + "/" + newInfo.getQd() + "/" + newInfo.getUserid() + "/jd_jdb.csv";
                biDataOssUtil.copyFile(old5, new5);

                String old6 = "bi_data/day/" + rq.replace("-", "") + "/" + oldInfo.getYhid() + "/" + oldInfo.getQd() + "/" + oldInfo.getUserid() + "/tzzp.json";
                String new6 = "bi_data/day/" + rq.replace("-", "") + "/" + newInfo.getYhid() + "/" + newInfo.getQd() + "/" + newInfo.getUserid() + "/tzzp.json";
                biDataOssUtil.copyFile(old6, new6);

                String old7 = "bi_data/day/" + rq.replace("-", "") + "/" + oldInfo.getYhid() + "/" + oldInfo.getQd() + "/" + oldInfo.getUserid() + "/xedk.json";
                String new7 = "bi_data/day/" + rq.replace("-", "") + "/" + newInfo.getYhid() + "/" + newInfo.getQd() + "/" + newInfo.getUserid() + "/xedk.json";
                biDataOssUtil.copyFile(old7, new7);

                String old8 = "bi_data/day/" + rq.replace("-", "") + "/" + oldInfo.getYhid() + "/" + oldInfo.getQd() + "/" + oldInfo.getUserid() + "/zjlszd.csv";
                String new8 = "bi_data/day/" + rq.replace("-", "") + "/" + newInfo.getYhid() + "/" + newInfo.getQd() + "/" + newInfo.getUserid() + "/zjlszd.csv";
                biDataOssUtil.copyFile(old8, new8);

                List<String> osskeys = biDataOssUtil.listFiles("bi_data/day/" + rq.replace("-", "") + "/" + oldInfo.getYhid() + "/" + oldInfo.getQd() + "/" + oldInfo.getUserid());
                for (String osskey : osskeys) {
                    if (osskey.contains("_bill")) {
                        String newKey = osskey.replace("/" + oldInfo.getYhid() + "/", "/" + newInfo.getYhid() + "/").replace("/" + oldInfo.getQd() + "/", "/" + newInfo.getQd() + "/");
                        biDataOssUtil.copyFile(osskey, newKey);
                    }
                }
            } else if (oldInfo.getQd().equals(Qd.TGC.getQdDesc())) {
                String old1 = "bi_data/day/" + rq.replace("-", "") + "/" + oldInfo.getYhid() + "/" + oldInfo.getQd() + "/" + oldInfo.getUserid() + "/bzj.json";
                String new1 = "bi_data/day/" + rq.replace("-", "") + "/" + newInfo.getYhid() + "/" + newInfo.getQd() + "/" + newInfo.getUserid() + "/bzj.json";
                biDataOssUtil.copyFile(old1, new1);
            }
        }
    }

    private void otherDataCopy(BiInfo oldInfo, BiInfo newInfo) {
        String start = oldInfo.getStart();
        String end = DataprobiDateUtil.getLastday();
        List<String> rqList = DataprobiDateUtil.getBetweenDate(start, end);

        if (newInfo.getQd().equals(Qd.TM.getQdDesc()) || newInfo.getQd().equals(Qd.TB.getQdDesc())) {
            sendDataprobiTaskXkfx(newInfo, rqList, TaskTypeConst.TX_XKFX, false);
            sendDataprobiTaskXkfx(newInfo, rqList, TaskTypeConst.TX_XKFX_OLD, false);
        } else if (newInfo.getQd().equals(Qd.PDD.getQdDesc())) {
            sendDataprobiTaskXkfx(newInfo, rqList, TaskTypeConst.PDD_XKFX, false);
        } else if (newInfo.getQd().equals(Qd.JD.getQdDesc())) {
            boolean fbp = JSONObject.parseObject(newInfo.getDpconfigjson()).getIntValue("dplx") == 1;
            sendDataprobiTaskXkfx(newInfo, rqList, TaskTypeConst.JD_XKFX, fbp);
        }
    }


    private void sendDataprobiTaskXkfx(BiInfo newInfo, List<String> rqList, String taskType, boolean fbp) {
        try {
            if (CollectionUtils.isEmpty(rqList)) {
                return;
            }

            DataprobiTaskMsg taskMsg = new DataprobiTaskMsg();
            taskMsg.setDpmc(newInfo.getDpmc());
            taskMsg.setTaskType(taskType);
            taskMsg.setQd(newInfo.getQd());
            taskMsg.setYhid(newInfo.getYhid());
            taskMsg.setRqList(rqList);
            taskMsg.setUserid(newInfo.getUserid());
            taskMsg.setUuid(UUID.randomUUID().toString());
            if (!fbp) {
                aliyunMq2.send("dataprobi_xkfx", UUID.randomUUID().toString(), new JobMessage("dataprobi_xkfx_processor", taskMsg));
            } else {
                aliyunMq2.send("dataprobi_task_fbp", UUID.randomUUID().toString(), new JobMessage("dataprobi_task_fbp_processor", taskMsg));
            }
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
    }
}
