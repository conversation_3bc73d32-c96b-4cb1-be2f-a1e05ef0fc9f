package com.abujlb.dataprobi.service;

import com.abujlb.dataprobi.bean.albb.Dataprobi1688Msg;
import com.abujlb.dataprobi.constant.TaskTypeConst;
import com.abujlb.dataprobi.threads.BiThreadLocals;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/8/10
 */
@Component
public class BiAlbbDataDealService extends BiService {
    @Override
    protected void after() {
        Dataprobi1688Msg datapro1688msg = (Dataprobi1688Msg) BiThreadLocals.getMsgBean();

        sendDataprobiTaskTgfx(datapro1688msg, datapro1688msg.getCjtf(), TaskTypeConst.ALBB_TGFX_CJTF);
        sendDataprobiTaskTgfx(datapro1688msg, datapro1688msg.getGcsyjsjh(), TaskTypeConst.ALBB_TGFX_GCSYJSJH);
        sendDataprobiTaskTgfx(datapro1688msg, datapro1688msg.getGypjjfa(), TaskTypeConst.ALBB_TGFX_GYPJJFA);
        sendDataprobiTaskTgfx(datapro1688msg, datapro1688msg.getHxsjczjh(), TaskTypeConst.ALBB_TGFX_HXSJCZJH);
        sendDataprobiTaskTgfx(datapro1688msg, datapro1688msg.getQztd(), TaskTypeConst.ALBB_TGFX_QZTD);
        sendDataprobiTaskTgfx(datapro1688msg, datapro1688msg.getQzxp(), TaskTypeConst.ALBB_TGFX_QZXP);
        sendDataprobiTaskTgfx(datapro1688msg, datapro1688msg.getSkajsc(), TaskTypeConst.ALBB_TGFX_SKAJSC);
        sendDataprobiTaskTgfx(datapro1688msg, datapro1688msg.getSkazsdzfa(), TaskTypeConst.ALBB_TGFX_SKAZSDZFA);
        sendDataprobiTaskTgfx(datapro1688msg, datapro1688msg.getXfpjjfa(), TaskTypeConst.ALBB_TGFX_XFPJJFA);
        sendDataprobiTaskTgfx(datapro1688msg, datapro1688msg.getZgcjjfa(), TaskTypeConst.ALBB_TGFX_ZGCJJFA);
        sendDataprobiTaskTgfx(datapro1688msg, datapro1688msg.getQdyx(), TaskTypeConst.ALBB_TGFX_QDYX);
        sendDataprobiTaskTgfx(datapro1688msg, datapro1688msg.getCxfa(), TaskTypeConst.ALBB_TGFX_CXFA);
        sendDataprobiTask(datapro1688msg, datapro1688msg.getAlbbjhzt(), TaskTypeConst.ALBB_JHZT);

        sendDataprobiCompoent(datapro1688msg);
    }
}
