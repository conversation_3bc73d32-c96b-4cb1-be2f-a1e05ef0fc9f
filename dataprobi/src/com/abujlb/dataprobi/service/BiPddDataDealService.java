package com.abujlb.dataprobi.service;

import com.abujlb.dataprobi.bean.pdd.DataprobiPddMsgBean;
import com.abujlb.dataprobi.constant.TaskTypeConst;
import com.abujlb.dataprobi.threads.BiThreadLocals;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/11 15:47
 */
@Component
public class BiPddDataDealService extends BiService {

    @Override
    protected void after() {
        DataprobiPddMsgBean dataprobiPddMsgBean = (DataprobiPddMsgBean) BiThreadLocals.getMsgBean();

        List<String> rqList = new ArrayList<>();
        rqList.addAll(dataprobiPddMsgBean.getSpxg());
        rqList.addAll(dataprobiPddMsgBean.getDdjb());
        rqList.addAll(dataprobiPddMsgBean.getQztg());
        rqList.addAll(dataprobiPddMsgBean.getXkfx());
        sendDataprobiTaskXkfx(dataprobiPddMsgBean, rqList, TaskTypeConst.PDD_XKFX);

        sendDataprobiTaskTgfx(dataprobiPddMsgBean, dataprobiPddMsgBean.getSptg(), TaskTypeConst.PDD_TGFX_SPTG);
        sendDataprobiTaskTgfx(dataprobiPddMsgBean, dataprobiPddMsgBean.getBztg(), TaskTypeConst.PDD_TGFX_BZTG);
        sendDataprobiTaskTgfx(dataprobiPddMsgBean, dataprobiPddMsgBean.getQztg(), TaskTypeConst.PDD_TGFX_QZTG);

        sendDataprobiTask(dataprobiPddMsgBean, dataprobiPddMsgBean.getPddjhzt(), TaskTypeConst.PDD_JHZT);
        sendDataprobiTask(dataprobiPddMsgBean, dataprobiPddMsgBean.getSppj(), TaskTypeConst.PDD_SPPJ);

        sendDataprobiTask(dataprobiPddMsgBean, dataprobiPddMsgBean.getKfjxfx(), TaskTypeConst.SYCM_PERFORMANCE_CUSTOMER_RECEPTION_PDD_DATA);
        sendDataprobiCompoent(dataprobiPddMsgBean);
    }
}
