package com.abujlb.dataprobi.service;

import com.abujlb.dataprobi.bean.xhs.DataprobiXhsMsg;
import com.abujlb.dataprobi.constant.TaskTypeConst;
import com.abujlb.dataprobi.threads.BiThreadLocals;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @date 2024/7/9
 */
@Component
public class BiXhsDataDealService extends BiService {

    @Override
    protected void after() {
        DataprobiXhsMsg dataprobiXhsMsg = (DataprobiXhsMsg) BiThreadLocals.getMsgBean();
        sendDataprobiTaskTgfx(dataprobiXhsMsg, dataprobiXhsMsg.getJgtgfx(), TaskTypeConst.XHS_JGTGFX);
        sendDataprobiTaskTgfx(dataprobiXhsMsg, dataprobiXhsMsg.getQftgfx(), TaskTypeConst.XHS_QFTGFX);
        sendDataprobiTask(dataprobiXhsMsg, dataprobiXhsMsg.getPgydryj(), TaskTypeConst.PGY_DRYGYJ);
        sendDataprobiTask(dataprobiXhsMsg, dataprobiXhsMsg.getKfperformance(), TaskTypeConst.SYCM_PERFORMANCE_CUSTOMER_RECEPTION_XHS_DATA);
        sendDataprobiCompoent(dataprobiXhsMsg);

    }
}
