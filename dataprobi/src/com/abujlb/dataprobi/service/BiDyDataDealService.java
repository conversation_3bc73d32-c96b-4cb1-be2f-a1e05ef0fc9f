package com.abujlb.dataprobi.service;

import com.abujlb.dataprobi.bean.dy.DataprobiDyMsgBean;
import com.abujlb.dataprobi.bean.dy.jlqc.BiJlqcAvvid;
import com.abujlb.dataprobi.constant.TaskTypeConst;
import com.abujlb.dataprobi.server.DataUploader;
import com.abujlb.dataprobi.threads.BiThreadLocals;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/11 15:47
 */
@Component
public class BiDyDataDealService extends BiService {

    @Override
    protected void before() {
        //获取当前店铺的当前时间下的所有千川。
        List<BiJlqcAvvid> jlqcAvvids = DataUploader.getJlqcAvvids(BiThreadLocals.getMsgBean().getUserid());
        if (CollectionUtils.isNotEmpty(jlqcAvvids)) {
            BiThreadLocals.addDyJlqcAvvidList(jlqcAvvids);
        }
    }

    @Override
    protected void after() {
        DataprobiDyMsgBean dataprobiDyMsgBean = (DataprobiDyMsgBean) BiThreadLocals.getMsgBean();
        sendDataprobiTask(dataprobiDyMsgBean, dataprobiDyMsgBean.getTspjhzt(), TaskTypeConst.DY_JHZT);
        sendDataprobiTask(dataprobiDyMsgBean, dataprobiDyMsgBean.getJxlmdd(), TaskTypeConst.DY_YGHF_JXLM);
        sendDataprobiTask(dataprobiDyMsgBean, dataprobiDyMsgBean.getJxlm_tzfwf(), TaskTypeConst.DY_YGHF_JXLM_TZFWF);
        sendDataprobiTask(dataprobiDyMsgBean, dataprobiDyMsgBean.getCompass_customer_analysis(), TaskTypeConst.SYCM_PERFORMANCE_CUSTOMER_RECEPTION_DY_DATA);
        sendDataprobiTask(dataprobiDyMsgBean, dataprobiDyMsgBean.getFgkf(), TaskTypeConst.DY_FGKF);

        sendDataprobiCompoent(dataprobiDyMsgBean);

        sendDataprobiTaskTgfx(dataprobiDyMsgBean, dataprobiDyMsgBean.getTsptgfx(), TaskTypeConst.DY_TGFX_TSP);
        sendDataprobiTaskTgfx(dataprobiDyMsgBean, dataprobiDyMsgBean.getQytg_tsp_tgfx(), TaskTypeConst.DY_TGFX_QYTG);
    }
}
