package com.abujlb.dataprobi.service;

import com.abujlb.dataprobi.bean.DataprobiBaseMsgBean;
import com.abujlb.dataprobi.bean.mn.BiSupplier;
import com.abujlb.dataprobi.bean.mn.DataprobiMnMsg;
import com.abujlb.dataprobi.constant.TaskTypeConst;
import com.abujlb.dataprobi.server.DataUploader;
import com.abujlb.dataprobi.threads.BiThreadLocals;
import com.abujlb.dataprobi.tsdao.SpnewTsDao;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/7/4
 */
@Component
public class BiMNDataDealService extends BiService {

    @Autowired
    private SpnewTsDao spTsDao;

    @Override
    protected void before() {
        final DataprobiBaseMsgBean dataprobiBaseMsgBean = BiThreadLocals.getMsgBean();
        //查询供应商的匹配规则。
        BiSupplier biSupplier = DataUploader.getSupplierRules(dataprobiBaseMsgBean.getUserid(), dataprobiBaseMsgBean.getQd());
        if (biSupplier != null && StringUtils.isNotBlank(biSupplier.getMateRule())) {
            BiThreadLocals.addSupplier(biSupplier);
            BiThreadLocals.addMnMetaRule(JSONObject.parseObject(biSupplier.getMateRule()));

            JSONObject mnMetaRule = BiThreadLocals.getMnMetaRule();
            boolean type3 = false;
            for (String key : mnMetaRule.keySet()) {
                JSONObject jsonObject = mnMetaRule.getJSONObject(key);
                int type = jsonObject.getIntValue("type");
                if (type == 3) {
                    type3 = true;
                    break;
                }
            }

            if (type3) {
                //查询当前MN店铺的所有宝贝
                List<String> bbidList = spTsDao.getMNbbidList(dataprobiBaseMsgBean.getUserid());
                biSupplier.setBbidList(bbidList);
            }
        }
    }

    @Override
    protected void after() {

        DataprobiMnMsg mnMsg = (DataprobiMnMsg) BiThreadLocals.getMsgBean();

        mnMsg.getAizttgfx().addAll(mnMsg.getAizt());
        List<String> aizt_tgfx_rqlist = mnMsg.getAizttgfx().stream().distinct().collect(Collectors.toList());
        sendDataprobiTaskTgfx(mnMsg, aizt_tgfx_rqlist, TaskTypeConst.MN_TGFX_AIZT);

        mnMsg.getDmbzttgfx().addAll(mnMsg.getDmbzt());
        List<String> dmbzt_tgfx_rqlist = mnMsg.getDmbzttgfx().stream().distinct().collect(Collectors.toList());
        sendDataprobiTaskTgfx(mnMsg, dmbzt_tgfx_rqlist, TaskTypeConst.MN_TGFX_DMBZT);

        mnMsg.getQztgtgfx().addAll(mnMsg.getQztg());
        List<String> qztg_tgfx_rqlist = mnMsg.getQztgtgfx().stream().distinct().collect(Collectors.toList());
        sendDataprobiTaskTgfx(mnMsg, qztg_tgfx_rqlist, TaskTypeConst.MN_TGFX_QZTG);

        mnMsg.getYlmftgfx().addAll(mnMsg.getYlmf());
        List<String> ylmf_tgfx_rqlist = mnMsg.getYlmftgfx().stream().distinct().collect(Collectors.toList());
        sendDataprobiTaskTgfx(mnMsg, ylmf_tgfx_rqlist, TaskTypeConst.MN_TGFX_YLMF);

        mnMsg.getZtctgfx().addAll(mnMsg.getZtc());
        List<String> ztc_tgfx_rqlist = mnMsg.getZtctgfx().stream().distinct().collect(Collectors.toList());
        sendDataprobiTaskTgfx(mnMsg, ztc_tgfx_rqlist, TaskTypeConst.MN_TGFX_ZTC);

    }
}
