package com.abujlb.dataprobi.service;

import com.abujlb.dataprobi.bean.tgc.BiTgcAiztone;
import com.abujlb.dataprobi.bean.tgc.DataprobiTgcMsg;
import com.abujlb.dataprobi.server.DataUploader;
import com.abujlb.dataprobi.threads.BiThreadLocals;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/4/26
 */
@Component
public class BiTgcDataDealService extends BiService {

    @Override
    protected void before() {
        List<BiTgcAiztone> list = DataUploader.getTgcAiztoneList(BiThreadLocals.getMsgBean().getUserid());
        if (CollectionUtils.isNotEmpty(list)) {
            Map<String, BiTgcAiztone> map = new HashMap<>();
            for (BiTgcAiztone biTgcAiztone : list) {
                if (!map.containsKey(biTgcAiztone.getAiztoneid())) {
                    map.put(biTgcAiztone.getAiztoneid(), biTgcAiztone);
                }
            }
            BiThreadLocals.addTgcAiztoneList(new ArrayList<>(map.values()));
        }
    }

    @Override
    protected void after() {
        DataprobiTgcMsg dataprobiTgcMsg = (DataprobiTgcMsg) BiThreadLocals.getMsgBean();
        sendDataprobiCompoent(dataprobiTgcMsg);
    }
}
