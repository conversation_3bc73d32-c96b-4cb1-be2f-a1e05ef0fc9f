package com.abujlb.dataprobi.service;

import com.abujlb.dataprobi.bean.dw.DataprobiDwMsg;
import com.abujlb.dataprobi.bean.ks.DataprobiKsMsg;
import com.abujlb.dataprobi.threads.BiThreadLocals;
import org.springframework.stereotype.Component;

/**
 * @packageName com.abujlb.dataprobi.service
 * @ClassName BiDwDataDealService
 * @Description 得物 处理业务
 * <AUTHOR>
 * @Date 2024/10/18
 */
@Component
public class BiDwDataDealService extends BiService {


    @Override
    protected void after() {
        DataprobiDwMsg dataprobiDwMsg = (DataprobiDwMsg) BiThreadLocals.getMsgBean();
        sendDataprobiCompoent(dataprobiDwMsg);
    }
}
