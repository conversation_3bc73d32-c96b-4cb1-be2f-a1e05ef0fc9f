package com.abujlb.dataprobi.service;

import com.abujlb.dataprobi.bean.tb.DataprobiMsg;
import com.abujlb.dataprobi.constant.TaskTypeConst;
import com.abujlb.dataprobi.threads.BiThreadLocals;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2022/5/11 15:47
 */
@Component
public class BiDataDealService extends BiService {

    @Override
    protected void before() {
        DataprobiMsg dataprobiMsg = (DataprobiMsg) BiThreadLocals.getMsgBean();
        dataprobiMsg.setAiztone(1);
        if (CollectionUtils.isEmpty(dataprobiMsg.getPpxxYghf()) && CollectionUtils.isNotEmpty(dataprobiMsg.getPpxx())) {
            dataprobiMsg.setPpxxYghf(dataprobiMsg.getPpxx());
        }

        List<String> rqList = new ArrayList<>(dataprobiMsg.getZtc());
        rqList.addAll(dataprobiMsg.getYlmf());
        rqList.addAll(dataprobiMsg.getAizt());
        rqList.addAll(dataprobiMsg.getDmbzt());
        rqList.addAll(dataprobiMsg.getQztg());
        rqList.addAll(dataprobiMsg.getXstg());
        rqList.addAll(dataprobiMsg.getNryxdsp());
        rqList.addAll(dataprobiMsg.getNryxdp());
        rqList.addAll(dataprobiMsg.getAizt_item());
        rqList.addAll(dataprobiMsg.getAizt_shop());
        rqList.addAll(dataprobiMsg.getAizt_shop_dpzd());
        rqList.addAll(dataprobiMsg.getAizt_shop_qdzt());
        rqList.addAll(dataprobiMsg.getVideoItemRela());
        rqList = rqList.stream().distinct().collect(Collectors.toList());

        List<String> yhq = dataprobiMsg.getYhq();
        List<String> yhqNewList = Stream.concat(rqList.stream(), yhq.stream()).distinct().collect(Collectors.toList());
        dataprobiMsg.setYhq(yhqNewList);
    }

    @Override
    protected void after() {
        DataprobiMsg dataprobiMsg = (DataprobiMsg) BiThreadLocals.getMsgBean();
        List<String> rqList = new ArrayList<>();
        rqList.addAll(dataprobiMsg.getSpxg());
        rqList.addAll(dataprobiMsg.getZtc());
        rqList.addAll(dataprobiMsg.getYlmf());
        rqList.addAll(dataprobiMsg.getAizt());
        rqList.addAll(dataprobiMsg.getSpsjhz());
        rqList = rqList.stream().distinct().collect(Collectors.toList());
        sendDataprobiTaskXkfx(dataprobiMsg, rqList, TaskTypeConst.TX_XKFX);
        sendDataprobiTaskXkfx(dataprobiMsg, rqList, TaskTypeConst.TX_XKFX_OLD);


        dataprobiMsg.getAizt_tgfx().addAll(dataprobiMsg.getAizt());
        List<String> aizt_tgfx_rqlist = dataprobiMsg.getAizt_tgfx().stream().distinct().collect(Collectors.toList());
        sendDataprobiTaskTgfx(dataprobiMsg, aizt_tgfx_rqlist, TaskTypeConst.TX_TGFX_AIZT);

        dataprobiMsg.getDmbzt_tgfx().addAll(dataprobiMsg.getDmbzt());
        List<String> dmbzt_tgfx_rqlist = dataprobiMsg.getDmbzt_tgfx().stream().distinct().collect(Collectors.toList());
        sendDataprobiTaskTgfx(dataprobiMsg, dmbzt_tgfx_rqlist, TaskTypeConst.TX_TGFX_DMBZT);

        dataprobiMsg.getQztg_tgfx().addAll(dataprobiMsg.getQztg());
        List<String> qztg_tgfx_rqlist = dataprobiMsg.getQztg_tgfx().stream().distinct().collect(Collectors.toList());
        sendDataprobiTaskTgfx(dataprobiMsg, qztg_tgfx_rqlist, TaskTypeConst.TX_TGFX_QZTG);

        dataprobiMsg.getYlmf_tgfx().addAll(dataprobiMsg.getYlmf());
        List<String> ylmf_tgfx_rqlist = dataprobiMsg.getYlmf_tgfx().stream().distinct().collect(Collectors.toList());
        sendDataprobiTaskTgfx(dataprobiMsg, ylmf_tgfx_rqlist, TaskTypeConst.TX_TGFX_YLMF);

        dataprobiMsg.getZtc_tgfx().addAll(dataprobiMsg.getZtc());
        List<String> ztc_tgfx_rqlist = dataprobiMsg.getZtc_tgfx().stream().distinct().collect(Collectors.toList());
        sendDataprobiTaskTgfx(dataprobiMsg, ztc_tgfx_rqlist, TaskTypeConst.TX_TGFX_ZTC);

        sendDataprobiTaskTgfx(dataprobiMsg, dataprobiMsg.getZtc_crowd_tgfx(), TaskTypeConst.TX_TGFX_ZTC_CROWD);
        sendDataprobiTaskTgfx(dataprobiMsg, dataprobiMsg.getZtc_keyword_tgfx(), TaskTypeConst.TX_TGFX_ZTC_KEYWORD);
        sendDataprobiTaskTgfx(dataprobiMsg, dataprobiMsg.getYlmf_cxjh(), TaskTypeConst.TX_TGFX_YLMF_CXJH);

        dataprobiMsg.getAizt_shop_tgfx().addAll(dataprobiMsg.getAizt_shop());
        List<String> aizt_shop_tgfx_rqlist = dataprobiMsg.getAizt_shop_tgfx().stream().distinct().collect(Collectors.toList());
        sendDataprobiTaskTgfx(dataprobiMsg, aizt_shop_tgfx_rqlist, TaskTypeConst.TX_TGFX_AIZT_SHOP);

        dataprobiMsg.getAizt_item_tgfx().addAll(dataprobiMsg.getAizt_item());
        List<String> aizt_item_tgfx_rqlist = dataprobiMsg.getAizt_item_tgfx().stream().distinct().collect(Collectors.toList());
        sendDataprobiTaskTgfx(dataprobiMsg, aizt_item_tgfx_rqlist, TaskTypeConst.TX_TGFX_AIZT_ITEM);

        dataprobiMsg.getXstg_tgfx().addAll(dataprobiMsg.getXstg());
        List<String> xstg_tgfx_rqlist = dataprobiMsg.getXstg_tgfx().stream().distinct().collect(Collectors.toList());
        sendDataprobiTaskTgfx(dataprobiMsg, xstg_tgfx_rqlist, TaskTypeConst.TX_TGFX_XSTG);

        //淘系-客服数据
        sendDataprobiTask(dataprobiMsg, dataprobiMsg.getSycm_performance_core_monitor(), TaskTypeConst.SYCM_PERFORMANCE_CORE);
        sendDataprobiTask(dataprobiMsg, dataprobiMsg.getSycm_user_sale_summary(), TaskTypeConst.SYCM_PERFORMANCE_CUSTOMER_SUMMARY);
        sendDataprobiTask(dataprobiMsg, dataprobiMsg.getSycm_spcecial_cra(), TaskTypeConst.SYCM_PERFORMANCE_CUSTOMER_RECEPTION_DATA);
        sendDataprobiTask(dataprobiMsg, dataprobiMsg.getSycm_performance_duty(), TaskTypeConst.SYCM_PERFORMANCE_DUTY_DATA);
        sendDataprobiTask(dataprobiMsg, dataprobiMsg.getSycm_performance_refund(), TaskTypeConst.SYCM_PERFORMANCE_REFUND);
        sendDataprobiTask(dataprobiMsg, dataprobiMsg.getSycm_spcecial_rea(), TaskTypeConst.SYCM_PERFORMANCE_REA);
        sendDataprobiTask(dataprobiMsg, dataprobiMsg.getSycm_performance_inquiiry_order(), TaskTypeConst.SYCM_PERFORMANCE_INQUIIRY_ORDER);

        sendDataprobiTask(dataprobiMsg, dataprobiMsg.getPpxxYghf(), TaskTypeConst.TX_YGHF_PPXX);
        sendDataprobiTask(dataprobiMsg, dataprobiMsg.getAiztonejhzt(), TaskTypeConst.TX_AIZTONEJHZT);
        sendDataprobiTask(dataprobiMsg, dataprobiMsg.getNewitem(), TaskTypeConst.TX_NEWITEM);
        sendDataprobiTask(dataprobiMsg, dataprobiMsg.getSycmflow(), TaskTypeConst.TX_FKS);
        sendDataprobiTask(dataprobiMsg, dataprobiMsg.getZtc(), TaskTypeConst.TX_ZTCVISTOR);
        sendDataprobiTask(dataprobiMsg, dataprobiMsg.getQztg(), TaskTypeConst.TX_QZTGFKS);
        sendDataprobiTask(dataprobiMsg, dataprobiMsg.getAiztcybb(), TaskTypeConst.TX_AIZT_CYBB);
        sendDataprobiTask(dataprobiMsg, dataprobiMsg.getAiztunitstatus(), TaskTypeConst.TX_AIZT_UNIT_STATUS);
        sendDataprobiTask(dataprobiMsg, dataprobiMsg.getSycmbybtybz(), TaskTypeConst.TX_YGHF_BYBT);

        sendDataprobiCompoent(dataprobiMsg);
    }
}
