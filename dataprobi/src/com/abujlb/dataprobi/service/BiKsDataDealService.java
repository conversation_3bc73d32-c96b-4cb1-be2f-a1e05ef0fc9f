package com.abujlb.dataprobi.service;

import com.abujlb.dataprobi.bean.ks.DataprobiKsMsg;
import com.abujlb.dataprobi.bean.xhs.DataprobiXhsMsg;
import com.abujlb.dataprobi.constant.TaskTypeConst;
import com.abujlb.dataprobi.threads.BiThreadLocals;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/11/18
 */
@Component
public class BiKsDataDealService extends BiService {

    @Override
    protected void after() {
        DataprobiKsMsg dataprobiKsMsg = (DataprobiKsMsg) BiThreadLocals.getMsgBean();
        sendDataprobiCompoent(dataprobiKsMsg);
        sendDataprobiTask(dataprobiKsMsg, dataprobiKsMsg.getKfzl(), TaskTypeConst.SYCM_PERFORMANCE_CUSTOMER_RECEPTION_KS_DATA);
    }

}
