package com.abujlb.dataprobi.bean.dw;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.SqliteSp;
import com.abujlb.dataprobi.threads.BiThreadLocals;
import com.alibaba.fastjson.JSONObject;

/**
 * @packageName com.abujlb.dataprobi.bean.dw
 * @ClassName SqliteDwSpfxSp
 * @Description 商品分析
 * <AUTHOR>
 * @Date 2024/10/16
 */
public class SqliteDwSpfxSp extends AbstractSqliteSp {

    /**
     * 货号
     */
    private String articleNumber;
    /**
     * 品牌id
     */
    private long brandId;
    /**
     * 品牌名称
     */
    private String brandName;
    /**
     * 一级类目id
     */
    private long categoryLv1Id;
    /**
     * 一级类目名称
     */
    private String categoryLv1Name;
    /**
     * 二级类目id
     */
    private long categoryLv2Id;
    /**
     * 二级类目名称
     */
    private String categoryLv2Name;
    /**
     * 二三级类目id
     */
    private long categoryLv3Id;
    /**
     * 三级类目名称
     */
    private String categoryLv3Name;
    /**
     * 是否一商
     */
    private int isYpys;
    /**
     * 是否spu
     */
    private int isFSpu;
    /**
     *
     */
    private int indexationStatus;
    /**
     * 出价渠道
     */
    private int defaultBiddingType;
    /**
     * sku 数量
     */
    private int skuSize;

    /**
     * 支付订单数
     */
    private int payOrderCnt;
    /**
     * 支付笔单价
     */
    private Double payAmountAvg;
    /**
     * 退货订单数
     */
    private int refundOrderCnt;
    /**
     * 好评数
     */
    private int commentPosCnt;
    /**
     * 中差评数
     */
    private int commentNegCnt;
    /**
     * commentPosRate
     */
    private Double commentPosRate;
    /**
     *
     */
    private int qualityCompCnt;
    /**
     * 品质投诉单量
     */
    private int qualityRefundCnt;
    /**
     * 商详转化率
     */
    private Double cdpConvertRate;

    /**
     * 收藏用户数
     */
    private int collectUvNum;
    /**
     * 支付品牌新客占比
     */
    private Double payNewUvRate;
    /**
     * 支付Z世代客占比
     */
    private Double payA90UvRate;
    /**
     * 商详品牌新客占比
     */
    private Double cdpNewUvRate;
    /**
     * 商详Z世代占比
     */
    private Double cdpA90UvRate;
    /**
     * 收藏品牌新客占比
     */
    private Double collectNewUvRate;
    /**
     * 收藏Z世代客占比
     */
    private Double collectA90UvRate;

    public String getArticleNumber() {
        return articleNumber;
    }

    public void setArticleNumber(String articleNumber) {
        this.articleNumber = articleNumber;
    }

    public long getBrandId() {
        return brandId;
    }

    public void setBrandId(long brandId) {
        this.brandId = brandId;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public long getCategoryLv1Id() {
        return categoryLv1Id;
    }

    public void setCategoryLv1Id(long categoryLv1Id) {
        this.categoryLv1Id = categoryLv1Id;
    }

    public String getCategoryLv1Name() {
        return categoryLv1Name;
    }

    public void setCategoryLv1Name(String categoryLv1Name) {
        this.categoryLv1Name = categoryLv1Name;
    }

    public long getCategoryLv2Id() {
        return categoryLv2Id;
    }

    public void setCategoryLv2Id(long categoryLv2Id) {
        this.categoryLv2Id = categoryLv2Id;
    }

    public String getCategoryLv2Name() {
        return categoryLv2Name;
    }

    public void setCategoryLv2Name(String categoryLv2Name) {
        this.categoryLv2Name = categoryLv2Name;
    }

    public long getCategoryLv3Id() {
        return categoryLv3Id;
    }

    public void setCategoryLv3Id(long categoryLv3Id) {
        this.categoryLv3Id = categoryLv3Id;
    }

    public String getCategoryLv3Name() {
        return categoryLv3Name;
    }

    public void setCategoryLv3Name(String categoryLv3Name) {
        this.categoryLv3Name = categoryLv3Name;
    }

    public int getIsYpys() {
        return isYpys;
    }

    public void setIsYpys(int isYpys) {
        this.isYpys = isYpys;
    }

    public int getIsFSpu() {
        return isFSpu;
    }

    public void setIsFSpu(int isFSpu) {
        this.isFSpu = isFSpu;
    }

    public int getIndexationStatus() {
        return indexationStatus;
    }

    public void setIndexationStatus(int indexationStatus) {
        this.indexationStatus = indexationStatus;
    }

    public int getDefaultBiddingType() {
        return defaultBiddingType;
    }

    public void setDefaultBiddingType(int defaultBiddingType) {
        this.defaultBiddingType = defaultBiddingType;
    }

    public int getSkuSize() {
        return skuSize;
    }

    public void setSkuSize(int skuSize) {
        this.skuSize = skuSize;
    }

    public int getPayOrderCnt() {
        return payOrderCnt;
    }

    public void setPayOrderCnt(int payOrderCnt) {
        this.payOrderCnt = payOrderCnt;
    }

    public Double getPayAmountAvg() {
        return payAmountAvg;
    }

    public void setPayAmountAvg(Double payAmountAvg) {
        this.payAmountAvg = payAmountAvg;
    }

    public int getRefundOrderCnt() {
        return refundOrderCnt;
    }

    public void setRefundOrderCnt(int refundOrderCnt) {
        this.refundOrderCnt = refundOrderCnt;
    }

    public int getCommentPosCnt() {
        return commentPosCnt;
    }

    public void setCommentPosCnt(int commentPosCnt) {
        this.commentPosCnt = commentPosCnt;
    }

    public int getCommentNegCnt() {
        return commentNegCnt;
    }

    public void setCommentNegCnt(int commentNegCnt) {
        this.commentNegCnt = commentNegCnt;
    }

    public Double getCommentPosRate() {
        return commentPosRate;
    }

    public void setCommentPosRate(Double commentPosRate) {
        this.commentPosRate = commentPosRate;
    }

    public int getQualityCompCnt() {
        return qualityCompCnt;
    }

    public void setQualityCompCnt(int qualityCompCnt) {
        this.qualityCompCnt = qualityCompCnt;
    }

    public int getQualityRefundCnt() {
        return qualityRefundCnt;
    }

    public void setQualityRefundCnt(int qualityRefundCnt) {
        this.qualityRefundCnt = qualityRefundCnt;
    }

    public Double getCdpConvertRate() {
        return cdpConvertRate;
    }

    public void setCdpConvertRate(Double cdpConvertRate) {
        this.cdpConvertRate = cdpConvertRate;
    }

    public int getCollectUvNum() {
        return collectUvNum;
    }

    public void setCollectUvNum(int collectUvNum) {
        this.collectUvNum = collectUvNum;
    }

    public Double getPayNewUvRate() {
        return payNewUvRate;
    }

    public void setPayNewUvRate(Double payNewUvRate) {
        this.payNewUvRate = payNewUvRate;
    }

    public Double getPayA90UvRate() {
        return payA90UvRate;
    }

    public void setPayA90UvRate(Double payA90UvRate) {
        this.payA90UvRate = payA90UvRate;
    }

    public Double getCdpNewUvRate() {
        return cdpNewUvRate;
    }

    public void setCdpNewUvRate(Double cdpNewUvRate) {
        this.cdpNewUvRate = cdpNewUvRate;
    }

    public Double getCdpA90UvRate() {
        return cdpA90UvRate;
    }

    public void setCdpA90UvRate(Double cdpA90UvRate) {
        this.cdpA90UvRate = cdpA90UvRate;
    }

    public Double getCollectNewUvRate() {
        return collectNewUvRate;
    }

    public void setCollectNewUvRate(Double collectNewUvRate) {
        this.collectNewUvRate = collectNewUvRate;
    }

    public Double getCollectA90UvRate() {
        return collectA90UvRate;
    }

    public void setCollectA90UvRate(Double collectA90UvRate) {
        this.collectA90UvRate = collectA90UvRate;
    }

    public SqliteDwSpfxSp() {
    }

    @Override
    public void setValues(JSONObject jsonObject) {
        cn.hutool.json.JSONObject json = JSONUtil.parseObj(jsonObject);
        // 解析商品的基础信息
        this.bbid = StrUtil.toString(json.getLong("spuId"));
        this.bbmc = json.getStr("productTitle");
        this.img = json.getStr("productImage");
        this.dpmc = BiThreadLocals.getMsgBean().getDpmc();
        this.spxgfks = json.getInt("cdpUvNum");
        this.spxglll = json.getInt("cdpPvNum");
        this.spxgzfje = NumberUtil.div(json.getDouble("payAmount").doubleValue(), 100);
        this.spxgzfmjs = json.getInt("payUvNum");

        // 设置新建字段的值
        this.articleNumber = json.getStr("articleNumber");
        this.brandId = json.getLong("brandId");
        this.brandName = json.getStr("brandName");
        this.categoryLv1Id = json.getLong("categoryLv1Id");
        this.categoryLv1Name = json.getStr("categoryLv1Name");
        this.categoryLv2Id = json.getLong("categoryLv2Id");
        this.categoryLv2Name = json.getStr("categoryLv2Name");
        this.categoryLv3Id = json.getLong("categoryLv3Id");
        this.categoryLv3Name = json.getStr("categoryLv3Name");
        this.isYpys = json.getInt("isYpys");
        this.isFSpu = json.getInt("isFSpu");
        this.indexationStatus = json.getInt("indexationStatus");
        this.defaultBiddingType = json.getInt("defaultBiddingType");
        this.skuSize = json.getInt("skuSize");
        this.payOrderCnt = json.getInt("payOrderCnt");
        this.payAmountAvg = NumberUtil.div(json.getDouble("payAmountAvg").doubleValue(), 100);
        this.refundOrderCnt = json.getInt("refundOrderCnt");
        this.commentPosCnt = json.getInt("commentPosCnt");
        this.commentNegCnt = json.getInt("commentNegCnt");
        this.commentPosRate = NumberUtil.div(json.getDouble("commentPosRate").doubleValue(), 100);
        this.qualityCompCnt = json.getInt("qualityCompCnt");
        this.qualityRefundCnt = json.getInt("qualityRefundCnt");
        this.cdpConvertRate = NumberUtil.div(json.getDouble("cdpConvertRate").doubleValue(), 100);
        this.collectUvNum = json.getInt("collectUvNum");
        this.payNewUvRate = NumberUtil.div(json.getDouble("payNewUvRate").doubleValue(), 100);
        this.payA90UvRate = NumberUtil.div(json.getDouble("payA90UvRate").doubleValue(), 100);
        this.cdpNewUvRate = NumberUtil.div(json.getDouble("cdpNewUvRate").doubleValue(), 100);
        this.cdpA90UvRate = NumberUtil.div(json.getDouble("cdpA90UvRate").doubleValue(), 100);
        this.collectNewUvRate = NumberUtil.div(json.getDouble("collectNewUvRate").doubleValue(), 100);
        this.collectA90UvRate = NumberUtil.div(json.getDouble("collectA90UvRate").doubleValue(), 100);


    }

    @Override
    public boolean compare(SqliteSp sqliteSp) {
        if (sqliteSp == null) {
            return false;
        }
        cn.hutool.json.JSONObject json = JSONUtil.parseObj(sqliteSp.getOtherOrDefault());
        return this.getArticleNumber() == json.getStr("articleNumber")
                && this.getBrandId() == json.getLong("brandId")
                && this.getBrandName() == json.getStr("brandName")
                && this.getCategoryLv1Id() == json.getLong("categoryLv1Id")
                && this.getCategoryLv1Name() == json.getStr("categoryLv1Name")
                && this.getCategoryLv2Id() == json.getLong("categoryLv2Id")
                && this.getCategoryLv2Name() == json.getStr("categoryLv2Name")
                && this.getCategoryLv3Id() == json.getLong("categoryLv3Id")
                && this.getCategoryLv3Name() == json.getStr("categoryLv3Name")
                && this.getIsYpys() == json.getInt("isYpys")
                && this.getIsFSpu() == json.getInt("isFSpu")
                && this.getIndexationStatus() == json.getInt("indexationStatus")
                && this.getDefaultBiddingType() == json.getInt("defaultBiddingType")
                && this.getSkuSize() == json.getInt("skuSize")
                && this.getPayOrderCnt() == json.getInt("payOrderCnt")
                && this.getPayAmountAvg() == NumberUtil.div(json.getDouble("payAmountAvg").doubleValue(), 100)
                && this.getRefundOrderCnt() == json.getInt("refundOrderCnt")
                && this.getCommentPosCnt() == json.getInt("commentPosCnt")
                && this.getCommentNegCnt() == json.getInt("commentNegCnt")
                && this.getCommentPosRate() == NumberUtil.div(json.getDouble("commentPosRate").doubleValue(), 100)
                && this.getQualityCompCnt() == json.getInt("qualityCompCnt")
                && this.getQualityRefundCnt() == json.getInt("qualityRefundCnt")
                && this.getCdpConvertRate() == NumberUtil.div(json.getDouble("cdpConvertRate").doubleValue(), 100)
                && this.getCollectUvNum() == json.getInt("collectUvNum")
                && this.getPayNewUvRate() == NumberUtil.div(json.getDouble("payNewUvRate").doubleValue(), 100)
                && this.getPayA90UvRate() == NumberUtil.div(json.getDouble("payA90UvRate").doubleValue(), 100)
                && this.getCdpNewUvRate() == NumberUtil.div(json.getDouble("cdpNewUvRate").doubleValue(), 100)
                && this.getCdpA90UvRate() == NumberUtil.div(json.getDouble("cdpA90UvRate").doubleValue(), 100)
                && this.getCollectNewUvRate() == NumberUtil.div(json.getDouble("collectNewUvRate").doubleValue(), 100)
                && this.getCollectA90UvRate() == NumberUtil.div(json.getDouble("collectA90UvRate").doubleValue(), 100);
    }

}
