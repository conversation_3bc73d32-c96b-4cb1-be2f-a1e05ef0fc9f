package com.abujlb.dataprobi.bean.wxsph;


import com.abujlb.dataprobi.bean.AbstractSqliteDp;
import com.abujlb.dataprobi.bean.SqliteDp;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.alibaba.fastjson.JSONObject;

/**
 * 微信视频号营销花费类限时抢购
 */
public class SqliteWxsphXsqgDp extends AbstractSqliteDp {

    //-优惠成本
    private double xsqgyhcb;//罗盘营销限时抢购优惠成本

    public double getXsqgyhcb() {
        return xsqgyhcb;
    }

    public void setXsqgyhcb(double xsqgyhcb) {
        this.xsqgyhcb = xsqgyhcb;
    }

    @Override
    public <T extends AbstractSqliteDp> void setValues(String[] tableStoreColumnValues) {
        String value0 = tableStoreColumnValues[0];
        if (value0 != null) {
            JSONObject jsonObject = JSONObject.parseObject(value0);
            this.xsqgyhcb = FastJSONObjAttrToNumber.toDouble(jsonObject, "content");
        }
    }


    @Override
    public boolean compare(SqliteDp sqliteDp) {
        if (sqliteDp == null) {
            return false;
        }

        JSONObject jsonObject = JSONObject.parseObject(sqliteDp.getOtherOrDefault());

        return FastJSONObjAttrToNumber.toDouble(jsonObject, "yhqyhcb") == this.getXsqgyhcb();
    }
}
