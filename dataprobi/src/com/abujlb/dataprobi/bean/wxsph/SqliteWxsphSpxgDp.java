package com.abujlb.dataprobi.bean.wxsph;

import com.abujlb.dataprobi.bean.AbstractSqliteDp;
import com.abujlb.dataprobi.bean.SqliteDp;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.alibaba.fastjson.JSONObject;


/**
 * 微信视频号商品效果店铺
 */
public class SqliteWxsphSpxgDp extends AbstractSqliteDp {

    //支付订单数
    private int spxgzfdds;

    // 父类不存在的字段
    private double cjjetk;
    private int cjtkdds;
    private int cjtkrs;
    private int cjtkjs;
    private double cjtkxl;
    private double fhqcjtkxl;
    private double fhhcjtkxl;
    private double sjdrjyzc;
    private double sjfwfzc;
    private double sjjsje;
    private double yjdrjyzc;
    private double yjfwfzc;
    private double yjjsje;

    public SqliteWxsphSpxgDp() {
    }

    public int getSpxgzfdds() {
        return spxgzfdds;
    }

    public void setSpxgzfdds(int spxgzfdds) {
        this.spxgzfdds = spxgzfdds;
    }


    public double getCjjetk() {
        return cjjetk;
    }

    public void setCjjetk(double cjjetk) {
        this.cjjetk = cjjetk;
    }

    public int getCjtkdds() {
        return cjtkdds;
    }

    public void setCjtkdds(int cjtkdds) {
        this.cjtkdds = cjtkdds;
    }

    public int getCjtkrs() {
        return cjtkrs;
    }

    public void setCjtkrs(int cjtkrs) {
        this.cjtkrs = cjtkrs;
    }

    public int getCjtkjs() {
        return cjtkjs;
    }

    public void setCjtkjs(int cjtkjs) {
        this.cjtkjs = cjtkjs;
    }

    public double getCjtkxl() {
        return cjtkxl;
    }

    public void setCjtkxl(double cjtkxl) {
        this.cjtkxl = cjtkxl;
    }

    public double getFhqcjtkxl() {
        return fhqcjtkxl;
    }

    public void setFhqcjtkxl(double fhqcjtkxl) {
        this.fhqcjtkxl = fhqcjtkxl;
    }

    public double getFhhcjtkxl() {
        return fhhcjtkxl;
    }

    public void setFhhcjtkxl(double fhhcjtkxl) {
        this.fhhcjtkxl = fhhcjtkxl;
    }

    public double getSjdrjyzc() {
        return sjdrjyzc;
    }

    public void setSjdrjyzc(double sjdrjyzc) {
        this.sjdrjyzc = sjdrjyzc;
    }

    public double getSjfwfzc() {
        return sjfwfzc;
    }

    public void setSjfwfzc(double sjfwfzc) {
        this.sjfwfzc = sjfwfzc;
    }

    public double getSjjsje() {
        return sjjsje;
    }

    public void setSjjsje(double sjjsje) {
        this.sjjsje = sjjsje;
    }

    public double getYjdrjyzc() {
        return yjdrjyzc;
    }

    public void setYjdrjyzc(double yjdrjyzc) {
        this.yjdrjyzc = yjdrjyzc;
    }

    public double getYjfwfzc() {
        return yjfwfzc;
    }

    public void setYjfwfzc(double yjfwfzc) {
        this.yjfwfzc = yjfwfzc;
    }

    public double getYjjsje() {
        return yjjsje;
    }

    public void setYjjsje(double yjjsje) {
        this.yjjsje = yjjsje;
    }

    @Override
    public <T extends AbstractSqliteDp> void setValues(String[] tableStoreColumnValues) {
        String value0 = tableStoreColumnValues[0];
        if (value0 != null) {
            JSONObject jsonObject = JSONObject.parseObject(value0);
            this.spxgzfdds = FastJSONObjAttrToNumber.toInt(jsonObject, "cjdds");
            this.spxgzfje = FastJSONObjAttrToNumber.toDouble(jsonObject, "cjje");
            this.spxgzfmjs = FastJSONObjAttrToNumber.toInt(jsonObject, "cjrs");
            this.spxgspfks = FastJSONObjAttrToNumber.toInt(jsonObject, "spdjrs");

            // 新建字段赋值
            this.cjjetk = FastJSONObjAttrToNumber.toDouble(jsonObject, "cjjetk");
            this.cjtkdds = FastJSONObjAttrToNumber.toInt(jsonObject, "cjtkdds");
            this.cjtkrs = FastJSONObjAttrToNumber.toInt(jsonObject, "cjtkrs");
            this.cjtkjs = FastJSONObjAttrToNumber.toInt(jsonObject, "cjtkjs");
            this.cjtkxl = FastJSONObjAttrToNumber.toDouble(jsonObject, "cjtkxl");
            this.fhqcjtkxl = FastJSONObjAttrToNumber.toDouble(jsonObject, "fhqcjtkxl");
            this.fhhcjtkxl = FastJSONObjAttrToNumber.toDouble(jsonObject, "fhhcjtkxl");
            this.sjdrjyzc = FastJSONObjAttrToNumber.toDouble(jsonObject, "sjdrjyzc");
            this.sjfwfzc = FastJSONObjAttrToNumber.toDouble(jsonObject, "sjfwfzc");
            this.sjjsje = FastJSONObjAttrToNumber.toDouble(jsonObject, "sjjsje");
            this.yjdrjyzc = FastJSONObjAttrToNumber.toDouble(jsonObject, "yjdrjyzc");
            this.yjfwfzc = FastJSONObjAttrToNumber.toDouble(jsonObject, "yjfwfzc");
            this.yjjsje = FastJSONObjAttrToNumber.toDouble(jsonObject, "yjjsje");
        }
    }

    @Override
    public boolean compare(SqliteDp sqliteDp) {
        if (sqliteDp == null) {
            return false;
        }

        JSONObject jsonObject = JSONObject.parseObject(sqliteDp.getOtherOrDefault());

        return FastJSONObjAttrToNumber.toDouble(jsonObject, "cjjetk") == this.getCjjetk()
                && FastJSONObjAttrToNumber.toInt(jsonObject, "cjtkdds") == this.getCjtkdds()
                && FastJSONObjAttrToNumber.toInt(jsonObject, "cjtkrs") == this.getCjtkrs()
                && FastJSONObjAttrToNumber.toInt(jsonObject, "cjtkjs") == this.getCjtkjs()
                && FastJSONObjAttrToNumber.toDouble(jsonObject, "cjtkxl") == this.getCjtkxl()
                && FastJSONObjAttrToNumber.toDouble(jsonObject, "fhqcjtkxl") == this.getFhqcjtkxl()
                && FastJSONObjAttrToNumber.toDouble(jsonObject, "fhhcjtkxl") == this.getFhhcjtkxl()
                && FastJSONObjAttrToNumber.toDouble(jsonObject, "sjdrjyzc") == this.getSjdrjyzc()
                && FastJSONObjAttrToNumber.toDouble(jsonObject, "sjfwfzc") == this.getSjfwfzc()
                && FastJSONObjAttrToNumber.toDouble(jsonObject, "sjjsje") == this.getSjjsje()
                && FastJSONObjAttrToNumber.toDouble(jsonObject, "yjdrjyzc") == this.getYjdrjyzc()
                && FastJSONObjAttrToNumber.toDouble(jsonObject, "yjfwfzc") == this.getYjfwfzc()
                && FastJSONObjAttrToNumber.toDouble(jsonObject, "yjjsje") == this.getYjjsje();
    }

}
