package com.abujlb.dataprobi.bean.wxsph;

import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.SqliteSp;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.abujlb.dataprobi.util.MathUtil;
import com.alibaba.fastjson.JSONObject;


/**
 * 微信视频号商品效果类
 */
public class SqliteWxsphSpxgSp extends AbstractSqliteSp {

    // 退款订单占比，对应JSON字段 "pay_refund_ratio"
    private double tkddzb;

    // 商品点击人数，对应JSON字段 "spu_click_uv"
    private int spdjrs;

    // 商品点击次数，对应JSON字段 "spu_click_cnt"
    private int spdjcs;

    // 下单金额，对应JSON字段 "create_gmv"
    private double xdje;

    // 下单订单数，对应JSON字段 "create_cnt"
    private int xddds;

    // 下单人数，对应JSON字段 "create_uv"
    private int xdrs;

    // 下单件数，对应JSON字段 "create_product_cnt"
    private int xdjs;

    // 成交金额（剔除退款），对应JSON字段 "pure_pay_gmv"
    private double cjjetk;

    // 成交订单数，对应JSON字段 "pay_cnt"
    private int cjdds;

    // 成交人数，对应JSON字段 "pay_uv"
    private int cjrs;

    // 成交件数，对应JSON字段 "pay_product_cnt"
    private int cjjs;

    // 成交客单价，对应JSON字段 "pay_gmv_per_uv"
    private double cjkdj;

    // 实际结算金额，对应JSON字段 "seller_actual_settle_amount"
    private double sjjsje;

    // 预计结算金额，对应JSON字段 "seller_predict_settle_amount"
    private double yjjsje;

    // 实际服务费支出，对应JSON字段 "platform_actual_commission"
    private double sjfwfzc;

    // 预计服务费支出，对应JSON字段 "platform_predict_commission"
    private double yjfwfzc;

    // 实际达人佣金支出，对应JSON字段 "finderuin_actual_commission"
    private double sjdrjyzc;

    // 预计达人佣金支出，对应JSON字段 "finderuin_predict_commission"
    private double yjdrjyzc;

    // 发货前成交退款率，对应JSON字段 "pay_refund_before_send_ratio"
    private double fhqcjtkxl;

    // 发货后成交退款率，对应JSON字段 "pay_refund_after_send_ratio"
    private double fhhcjtkxl;

    public double getTkddzb() {
        return tkddzb;
    }

    public void setTkddzb(double tkddzb) {
        this.tkddzb = tkddzb;
    }

    public int getSpdjrs() {
        return spdjrs;
    }

    public void setSpdjrs(int spdjrs) {
        this.spdjrs = spdjrs;
    }

    public int getSpdjcs() {
        return spdjcs;
    }

    public void setSpdjcs(int spdjcs) {
        this.spdjcs = spdjcs;
    }

    public double getXdje() {
        return xdje;
    }

    public void setXdje(double xdje) {
        this.xdje = xdje;
    }

    public int getXddds() {
        return xddds;
    }

    public void setXddds(int xddds) {
        this.xddds = xddds;
    }

    public int getXdrs() {
        return xdrs;
    }

    public void setXdrs(int xdrs) {
        this.xdrs = xdrs;
    }

    public int getXdjs() {
        return xdjs;
    }

    public void setXdjs(int xdjs) {
        this.xdjs = xdjs;
    }

    public double getCjjetk() {
        return cjjetk;
    }

    public void setCjjetk(double cjjetk) {
        this.cjjetk = cjjetk;
    }

    public int getCjdds() {
        return cjdds;
    }

    public void setCjdds(int cjdds) {
        this.cjdds = cjdds;
    }

    public int getCjrs() {
        return cjrs;
    }

    public void setCjrs(int cjrs) {
        this.cjrs = cjrs;
    }

    public int getCjjs() {
        return cjjs;
    }

    public void setCjjs(int cjjs) {
        this.cjjs = cjjs;
    }

    public double getCjkdj() {
        return cjkdj;
    }

    public void setCjkdj(double cjkdj) {
        this.cjkdj = cjkdj;
    }

    public double getSjjsje() {
        return sjjsje;
    }

    public void setSjjsje(double sjjsje) {
        this.sjjsje = sjjsje;
    }

    public double getYjjsje() {
        return yjjsje;
    }

    public void setYjjsje(double yjjsje) {
        this.yjjsje = yjjsje;
    }

    public double getSjfwfzc() {
        return sjfwfzc;
    }

    public void setSjfwfzc(double sjfwfzc) {
        this.sjfwfzc = sjfwfzc;
    }

    public double getYjfwfzc() {
        return yjfwfzc;
    }

    public void setYjfwfzc(double yjfwfzc) {
        this.yjfwfzc = yjfwfzc;
    }

    public double getSjdrjyzc() {
        return sjdrjyzc;
    }

    public void setSjdrjyzc(double sjdrjyzc) {
        this.sjdrjyzc = sjdrjyzc;
    }

    public double getYjdrjyzc() {
        return yjdrjyzc;
    }

    public void setYjdrjyzc(double yjdrjyzc) {
        this.yjdrjyzc = yjdrjyzc;
    }

    public double getFhqcjtkxl() {
        return fhqcjtkxl;
    }

    public void setFhqcjtkxl(double fhqcjtkxl) {
        this.fhqcjtkxl = fhqcjtkxl;
    }

    public double getFhhcjtkxl() {
        return fhhcjtkxl;
    }

    public void setFhhcjtkxl(double fhhcjtkxl) {
        this.fhhcjtkxl = fhhcjtkxl;
    }

    @Override
    public void setValues(JSONObject jsonObject) {
        this.img = FastJSONObjAttrToNumber.toString(jsonObject, "fields", "coverImg");
        this.bbmc = FastJSONObjAttrToNumber.toString(jsonObject, "fields", "title");
        this.spxgfks = FastJSONObjAttrToNumber.toInt(jsonObject, "fields", "spu_click_uv");
        this.spxglll = FastJSONObjAttrToNumber.toInt(jsonObject, "fields", "spu_click_cnt");
        this.spxgzfje = MathUtil.divide100(FastJSONObjAttrToNumber.toDouble(jsonObject, "fields", "pay_gmv"));
        this.spxgzfmjs = FastJSONObjAttrToNumber.toInt(jsonObject, "fields", "pay_uv");
        this.spxgtkje = MathUtil.divide100(FastJSONObjAttrToNumber.toDouble(jsonObject, "fields", "pay_refund_gmv"));
        this.spxgzfjs = FastJSONObjAttrToNumber.toInt(jsonObject, "fields", "pay_product_cnt");


        // 设置新建字段的值
//        this.spdjrs = FastJSONObjAttrToNumber.toInt(jsonObject, "fields", "spu_click_uv");
//        this.spdjcs = FastJSONObjAttrToNumber.toInt(jsonObject, "fields", "spu_click_cnt");
        this.tkddzb = FastJSONObjAttrToNumber.toDouble(jsonObject, "fields", "pay_refund_ratio");
        this.xdje = MathUtil.divide100(FastJSONObjAttrToNumber.toDouble(jsonObject, "fields", "create_gmv"));
        this.xddds = FastJSONObjAttrToNumber.toInt(jsonObject, "fields", "create_cnt");
        this.xdrs = FastJSONObjAttrToNumber.toInt(jsonObject, "fields", "create_uv");
        this.xdjs = FastJSONObjAttrToNumber.toInt(jsonObject, "fields", "create_product_cnt");
        this.cjjetk = MathUtil.divide100(FastJSONObjAttrToNumber.toDouble(jsonObject, "fields", "pure_pay_gmv"));
        this.cjdds = FastJSONObjAttrToNumber.toInt(jsonObject, "fields", "pay_cnt");
        //this.cjrs = FastJSONObjAttrToNumber.toInt(jsonObject, "fields", "pay_uv");
        //this.cjjs = FastJSONObjAttrToNumber.toInt(jsonObject, "fields", "pay_product_cnt");
        this.cjkdj = MathUtil.divide100(FastJSONObjAttrToNumber.toDouble(jsonObject, "fields", "pay_gmv_per_uv"));
        this.sjjsje = MathUtil.divide100(FastJSONObjAttrToNumber.toDouble(jsonObject, "fields", "seller_actual_settle_amount"));
        this.yjjsje = MathUtil.divide100(FastJSONObjAttrToNumber.toDouble(jsonObject, "fields", "seller_predict_settle_amount"));
        this.sjfwfzc = MathUtil.divide100(FastJSONObjAttrToNumber.toDouble(jsonObject, "fields", "platform_actual_commission"));
        this.yjfwfzc = MathUtil.divide100(FastJSONObjAttrToNumber.toDouble(jsonObject, "fields", "platform_predict_commission"));
        this.sjdrjyzc = MathUtil.divide100(FastJSONObjAttrToNumber.toDouble(jsonObject, "fields", "finderuin_actual_commission"));
        this.yjdrjyzc = MathUtil.divide100(FastJSONObjAttrToNumber.toDouble(jsonObject, "fields", "finderuin_predict_commission"));
        this.fhqcjtkxl = FastJSONObjAttrToNumber.toDouble(jsonObject, "fields", "pay_refund_before_send_ratio");
        this.fhhcjtkxl = FastJSONObjAttrToNumber.toDouble(jsonObject, "fields", "pay_refund_after_send_ratio");

        // 解析商品的基础信息
        this.bbid = FastJSONObjAttrToNumber.toString(jsonObject, "fields", "product_id");
    }

    @Override
    public boolean compare(SqliteSp sqliteSp) {
        if (sqliteSp == null) {
            return false;
        }

        JSONObject jsonObject = JSONObject.parseObject(sqliteSp.getOtherOrDefault());

        return this.getXdje() == FastJSONObjAttrToNumber.toDouble(jsonObject, "xdje")
                && this.getXddds() == FastJSONObjAttrToNumber.toInt(jsonObject, "xddds")
                && this.getXdrs() == FastJSONObjAttrToNumber.toInt(jsonObject, "xdrs")
                && this.getXdjs() == FastJSONObjAttrToNumber.toInt(jsonObject, "xdjs")
                && this.getCjjetk() == FastJSONObjAttrToNumber.toDouble(jsonObject, "cjjetk")
                && this.getCjdds() == FastJSONObjAttrToNumber.toInt(jsonObject, "cjdds")
                && this.getTkddzb() == FastJSONObjAttrToNumber.toDouble(jsonObject, "tkddzb")
                && this.getCjkdj() == FastJSONObjAttrToNumber.toDouble(jsonObject, "cjkdj")
                && this.getSjjsje() == FastJSONObjAttrToNumber.toDouble(jsonObject, "sjjsje")
                && this.getYjjsje() == FastJSONObjAttrToNumber.toDouble(jsonObject, "yjjsje")
                && this.getSjfwfzc() == FastJSONObjAttrToNumber.toDouble(jsonObject, "sjfwfzc")
                && this.getYjfwfzc() == FastJSONObjAttrToNumber.toDouble(jsonObject, "yjfwfzc")
                && this.getSjdrjyzc() == FastJSONObjAttrToNumber.toDouble(jsonObject, "sjdrjyzc")
                && this.getYjdrjyzc() == FastJSONObjAttrToNumber.toDouble(jsonObject, "yjdrjyzc")
                && this.getFhqcjtkxl() == FastJSONObjAttrToNumber.toDouble(jsonObject, "fhqcjtkxl")
                && this.getFhhcjtkxl() == FastJSONObjAttrToNumber.toDouble(jsonObject, "fhhcjtkxl");
    }

}
