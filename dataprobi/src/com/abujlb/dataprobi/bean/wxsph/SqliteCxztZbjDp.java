package com.abujlb.dataprobi.bean.wxsph;

import com.abujlb.dataprobi.bean.AbstractSqliteDp;
import com.abujlb.dataprobi.bean.SqliteDp;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.alibaba.fastjson.JSONObject;

/**
 * 超雄智投直播间店铺
 */
public class SqliteCxztZbjDp extends AbstractSqliteDp {

    private double cxztzbhf;// 超雄智直播花费,
    private double cxztzbcjje;// 超雄智直播成交金额,
    private int cxztzbcjdd;// 超雄智直播成交订单，
    private double cxztzbcjroi;//  超雄智直播成交roi

    @Override
    public <T extends AbstractSqliteDp> void setValues(String[] tableStoreColumnValues) {
        if (tableStoreColumnValues == null || tableStoreColumnValues.length == 0) {
            return;
        }

        String value0 = tableStoreColumnValues[0];
        if (value0 != null) {
            JSONObject jsonObject = JSONObject.parseObject(value0);
            // 设置各个字段的值
            this.cxztzbhf = FastJSONObjAttrToNumber.toDouble(jsonObject, "cxztzbhf");
            this.cxztzbcjje = FastJSONObjAttrToNumber.toDouble(jsonObject, "cxztzbcjje");
            this.cxztzbcjdd = FastJSONObjAttrToNumber.toInt(jsonObject, "cxztzbcjdd");
            this.cxztzbcjroi = FastJSONObjAttrToNumber.toDouble(jsonObject, "cxztzbcjroi");
        }
    }

    @Override
    public boolean compare(SqliteDp sqliteDp) {
        if (sqliteDp == null) {
            return false;
        }

        JSONObject jsonObject = JSONObject.parseObject(sqliteDp.getOtherOrDefault());
        if (jsonObject == null) {
            return false;
        }

        // 比较所有相关字段
        return this.cxztzbhf == FastJSONObjAttrToNumber.toDouble(jsonObject, "cxztzbhf") &&
                this.cxztzbcjje == FastJSONObjAttrToNumber.toDouble(jsonObject, "cxztzbcjje") &&
                this.cxztzbcjdd == FastJSONObjAttrToNumber.toInt(jsonObject, "cxztzbcjdd") &&
                this.cxztzbcjroi == FastJSONObjAttrToNumber.toDouble(jsonObject, "cxztzbcjroi");
    }

    public double getCxztzbhf() {
        return cxztzbhf;
    }

    public void setCxztzbhf(double cxztzbhf) {
        this.cxztzbhf = cxztzbhf;
    }

    public double getCxztzbcjje() {
        return cxztzbcjje;
    }

    public void setCxztzbcjje(double cxztzbcjje) {
        this.cxztzbcjje = cxztzbcjje;
    }

    public int getCxztzbcjdd() {
        return cxztzbcjdd;
    }

    public void setCxztzbcjdd(int cxztzbcjdd) {
        this.cxztzbcjdd = cxztzbcjdd;
    }

    public double getCxztzbcjroi() {
        return cxztzbcjroi;
    }

    public void setCxztzbcjroi(double cxztzbcjroi) {
        this.cxztzbcjroi = cxztzbcjroi;
    }
}