package com.abujlb.dataprobi.bean.wxsph;

import com.abujlb.dataprobi.bean.AbstractSqliteDp;
import com.abujlb.dataprobi.bean.SqliteDp;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.alibaba.fastjson.JSONObject;

/**
 * 超雄智投短视频店铺
 */
public class SqliteCxztDspDp extends AbstractSqliteDp {

    private double cxztdsphf;// 超雄智短视频投花费,
    private double cxztdspcjje;//  超雄智投短视频成交金额,
    private int cxztdspcjdd;// 超雄智投短视频成交订单，
    private double cxztdspcjroi;//  超雄智投短视频成交roi

    @Override
    public <T extends AbstractSqliteDp> void setValues(String[] tableStoreColumnValues) {
        if (tableStoreColumnValues == null || tableStoreColumnValues.length == 0) {
            return;
        }

        String value0 = tableStoreColumnValues[0];
        if (value0 != null) {
            JSONObject jsonObject = JSONObject.parseObject(value0);
            // 设置各个字段的值
            this.cxztdsphf = FastJSONObjAttrToNumber.toDouble(jsonObject, "cxztdsphf");
            this.cxztdspcjje = FastJSONObjAttrToNumber.toDouble(jsonObject, "cxztdspcjje");
            this.cxztdspcjdd = FastJSONObjAttrToNumber.toInt(jsonObject, "cxztdspcjdd");
            this.cxztdspcjroi = FastJSONObjAttrToNumber.toDouble(jsonObject, "cxztdspcjroi");
        }
    }

    @Override
    public boolean compare(SqliteDp sqliteDp) {
        if (sqliteDp == null) {
            return false;
        }

        JSONObject jsonObject = JSONObject.parseObject(sqliteDp.getOtherOrDefault());
        if (jsonObject == null) {
            return false;
        }

        // 比较所有相关字段
        return this.cxztdsphf == FastJSONObjAttrToNumber.toDouble(jsonObject, "cxztdsphf") &&
                this.cxztdspcjje == FastJSONObjAttrToNumber.toDouble(jsonObject, "cxztdspcjje") &&
                this.cxztdspcjdd == FastJSONObjAttrToNumber.toInt(jsonObject, "cxztdspcjdd") &&
                this.cxztdspcjroi == FastJSONObjAttrToNumber.toDouble(jsonObject, "cxztdspcjroi");
    }

    // Getters and Setters
    public double getCxztdsphf() {
        return cxztdsphf;
    }

    public void setCxztdsphf(double cxztdsphf) {
        this.cxztdsphf = cxztdsphf;
    }

    public double getCxztdspcjje() {
        return cxztdspcjje;
    }

    public void setCxztdspcjje(double cxztdspcjje) {
        this.cxztdspcjje = cxztdspcjje;
    }

    public int getCxztdspcjdd() {
        return cxztdspcjdd;
    }

    public void setCxztdspcjdd(int cxztdspcjdd) {
        this.cxztdspcjdd = cxztdspcjdd;
    }

    public double getCxztdspcjroi() {
        return cxztdspcjroi;
    }

    public void setCxztdspcjroi(double cxztdspcjroi) {
        this.cxztdspcjroi = cxztdspcjroi;
    }
}