package com.abujlb.dataprobi.bean.wxsph;


import com.abujlb.dataprobi.bean.AbstractSqliteDp;
import com.abujlb.dataprobi.bean.SqliteDp;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.alibaba.fastjson.JSONObject;

/**
 * 微信视频号营销花费类优惠券
 */
public class SqliteWxsphYhqDp extends AbstractSqliteDp {

    //-优惠成本
    private double yhqyhcb;//罗盘营销优惠券优惠成本

    public double getYhqyhcb() {
        return yhqyhcb;
    }

    public void setYhqyhcb(double yhqyhcb) {
        this.yhqyhcb = yhqyhcb;
    }

    @Override
    public <T extends AbstractSqliteDp> void setValues(String[] tableStoreColumnValues) {
        String value0 = tableStoreColumnValues[0];
        if (value0 != null) {
            JSONObject jsonObject = JSONObject.parseObject(value0);
            this.yhqyhcb = FastJSONObjAttrToNumber.toDouble(jsonObject, "content");
        }
    }


    @Override
    public boolean compare(SqliteDp sqliteDp) {
        if (sqliteDp == null) {
            return false;
        }

        JSONObject jsonObject = JSONObject.parseObject(sqliteDp.getOtherOrDefault());

        return FastJSONObjAttrToNumber.toDouble(jsonObject, "yhqyhcb") == this.getYhqyhcb();
    }
}
