package com.abujlb.dataprobi.bean.wxsph;

import com.abujlb.dataprobi.annotations.ClrqIgnore;
import com.abujlb.dataprobi.annotations.SumIgnore;
import com.abujlb.dataprobi.annotations.UpdateSycmcljzrqIgnore;
import com.abujlb.dataprobi.bean.DataprobiBaseMsgBean;
import com.alibaba.fastjson.JSON;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/10
 */
public class DataprobiWxsphMsg extends DataprobiBaseMsgBean {

    //商品效果
    private List<String> spxg;
    //商品效果-店铺维度
    private List<String> spxgdp;
    //优惠券店铺
    private List<String> yhqdp;
    //限时抢购店铺
    private List<String> xsqgdp;
    //超雄智投直播间店铺
    @UpdateSycmcljzrqIgnore
    private List<String> cxztzbjdp;
    //超雄智投短视频店铺
    @UpdateSycmcljzrqIgnore
    private List<String> cxztdspdp;
    //超雄智投短视频商品
    @UpdateSycmcljzrqIgnore
    private List<String> cxztdsp;


    public List<String> getSpxg() {
        return spxg;
    }

    public void setSpxg(List<String> spxg) {
        this.spxg = spxg;
    }

    public List<String> getSpxgdp() {
        return spxgdp;
    }

    public void setSpxgdp(List<String> spxgdp) {
        this.spxgdp = spxgdp;
    }

    public List<String> getYhqdp() {
        return yhqdp;
    }

    public void setYhqdp(List<String> yhqdp) {
        this.yhqdp = yhqdp;
    }

    public List<String> getXsqgdp() {
        return xsqgdp;
    }

    public void setXsqgdp(List<String> xsqgdp) {
        this.xsqgdp = xsqgdp;
    }

    public List<String> getCxztzbjdp() {
        return cxztzbjdp;
    }

    public void setCxztzbjdp(List<String> cxztzbjdp) {
        this.cxztzbjdp = cxztzbjdp;
    }

    public List<String> getCxztdspdp() {
        return cxztdspdp;
    }

    public void setCxztdspdp(List<String> cxztdspdp) {
        this.cxztdspdp = cxztdspdp;
    }

    public List<String> getCxztdsp() {
        return cxztdsp;
    }

    public void setCxztdsp(List<String> cxztdsp) {
        this.cxztdsp = cxztdsp;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }

    public void fillNullList() throws InvocationTargetException, IllegalAccessException {
        Method[] methods = this.getClass().getMethods();
        for (Method method : methods) {
            if (method.getName().startsWith("get")) {
                Object value = method.invoke(this);
                if (value == null) {
                    Method setMethod = null;
                    try {
                        setMethod = this.getClass().getMethod("s" + method.getName().substring(1), List.class);
                        setMethod.invoke(this, new ArrayList<>());
                    } catch (NoSuchMethodException ignored) {
                    }
                }
            }
        }
    }
}
