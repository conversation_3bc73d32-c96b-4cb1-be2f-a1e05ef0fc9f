package com.abujlb.dataprobi.bean.wxsph;

import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.SqliteSp;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.alibaba.fastjson.JSONObject;

/**
 * 微信视频号商品结算相关花费
 */
public class SqliteCxztDspSp extends AbstractSqliteSp {

    // 超雄智投短视频投花费，对应JSON字段 "cost"
    private double cxztdsphf;

    // 超雄智投短视频成交金额，对应JSON字段 "order_amount"
    private double cxztdspcjje;

    // 超雄智投短视频成交订单，对应JSON字段 "order_num"
    private int cxztdspcjdd;

    // 超雄智投短视频成交roi，对应JSON字段 "roi"
    private double cxztdspcjroi;

    public double getCxztdsphf() {
        return cxztdsphf;
    }

    public void setCxztdsphf(double cxztdsphf) {
        this.cxztdsphf = cxztdsphf;
    }

    public double getCxztdspcjje() {
        return cxztdspcjje;
    }

    public void setCxztdspcjje(double cxztdspcjje) {
        this.cxztdspcjje = cxztdspcjje;
    }

    public int getCxztdspcjdd() {
        return cxztdspcjdd;
    }

    public void setCxztdspcjdd(int cxztdspcjdd) {
        this.cxztdspcjdd = cxztdspcjdd;
    }

    public double getCxztdspcjroi() {
        return cxztdspcjroi;
    }

    public void setCxztdspcjroi(double cxztdspcjroi) {
        this.cxztdspcjroi = cxztdspcjroi;
    }

    @Override
    public void setValues(JSONObject jsonObject) {
        // 设置雄智短视频相关字段
        this.cxztdsphf = FastJSONObjAttrToNumber.toDouble(jsonObject, "cost");
        this.cxztdspcjje = FastJSONObjAttrToNumber.toDouble(jsonObject, "order_amount");
        this.cxztdspcjdd = FastJSONObjAttrToNumber.toInt(jsonObject, "order_num");
        this.cxztdspcjroi = FastJSONObjAttrToNumber.toDouble(jsonObject, "roi");

        // 解析商品的基础信息
        this.bbid = FastJSONObjAttrToNumber.toString(jsonObject, "product_id");
    }

    @Override
    public boolean compare(SqliteSp sqliteSp) {
        if (sqliteSp == null) {
            return false;
        }

        JSONObject jsonObject = JSONObject.parseObject(sqliteSp.getOtherOrDefault());

        return this.getCxztdsphf() == FastJSONObjAttrToNumber.toDouble(jsonObject, "cxztdsphf") &&
                this.getCxztdspcjje() == FastJSONObjAttrToNumber.toDouble(jsonObject, "cxztdspcjje") &&
                this.getCxztdspcjdd() == FastJSONObjAttrToNumber.toInt(jsonObject, "cxztdspcjdd") &&
                this.getCxztdspcjroi() == FastJSONObjAttrToNumber.toDouble(jsonObject, "cxztdspcjroi");
    }
}