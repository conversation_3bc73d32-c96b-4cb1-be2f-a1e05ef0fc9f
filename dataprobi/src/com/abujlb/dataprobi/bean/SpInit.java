package com.abujlb.dataprobi.bean;

import com.abujlb.dataprobi.constant.DataprobiConst;
import com.abujlb.dataprobi.util.DataprobiDateUtil;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.abujlb.dataprobi.util.MathUtil;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2022/9/14 11:30
 */
public class SpInit {

    private int yhid;
    private String qd;
    private String userid;

    private String bbid;
    private String img;
    private String bbmc;
    private String price;
    private String cjsj;
    private int sjzt;
    private String cid;
    private String scsjsj;
    private String mainskuid;
    private String spuid;

    public SpInit() {
    }

    public SpInit(String qd, JSONObject jsonObject) {
        try {
            this.qd = qd;
            if (qd.equals("TM")) {
                this.bbid = FastJSONObjAttrToNumber.toString(jsonObject, "itemId");
                this.cjsj = FastJSONObjAttrToNumber.toString(jsonObject, "upShelfDate_m", "value");
                JSONObject upShelfDate_m = FastJSONObjAttrToNumber.toJSONObject(jsonObject, "upShelfDate_m");
                if (upShelfDate_m != null && upShelfDate_m.containsKey("status")) {
                    String type = FastJSONObjAttrToNumber.toString(upShelfDate_m, "status", "type");
                    if (type.equals("success")) {
                        this.sjzt = 1;
                    } else {
                        this.sjzt = 0;
                    }
                }

                if (StringUtils.isNotBlank(this.cjsj)) {
                    if (this.cjsj.matches(DataprobiConst.regExp2)) {
                        this.cjsj = this.cjsj + ":00";
                    }
                }
            }
            if (qd.equals("TB")) {
                this.bbid = FastJSONObjAttrToNumber.toString(jsonObject, "itemId");
                this.price = FastJSONObjAttrToNumber.toString(jsonObject, "managerPrice", "currentPrice");
                this.cjsj = FastJSONObjAttrToNumber.toString(jsonObject, "upShelfDate_m", "value");
                JSONObject upShelfDate_m = FastJSONObjAttrToNumber.toJSONObject(jsonObject, "upShelfDate_m");
                if (upShelfDate_m != null && upShelfDate_m.containsKey("type")) {
                    String type = FastJSONObjAttrToNumber.toString(upShelfDate_m, "type");
                    if (type.equals("success")) {
                        this.sjzt = 1;
                    } else {
                        this.sjzt = 0;
                    }
                }

                if (upShelfDate_m != null && upShelfDate_m.containsKey("status")) {
                    String type = FastJSONObjAttrToNumber.toString(upShelfDate_m, "status", "type");
                    if (type.equals("success")) {
                        this.sjzt = 1;
                    } else {
                        this.sjzt = 0;
                    }
                }

                this.cid = FastJSONObjAttrToNumber.toString(jsonObject, "catId");
                if (StringUtils.isNotBlank(this.cjsj)) {
                    if (this.cjsj.matches(DataprobiConst.regExp2)) {
                        this.cjsj = this.cjsj + ":00";
                    }
                }
            }

            if (qd.equals("PDD")) {
                this.bbid = FastJSONObjAttrToNumber.toString(jsonObject, "id");
                this.img = FastJSONObjAttrToNumber.toString(jsonObject, "hd_url");
                this.bbmc = FastJSONObjAttrToNumber.toString(jsonObject, "goods_name");
                this.price = FastJSONObjAttrToNumber.toString(jsonObject, "managerPrice", "currentPrice");
                String tm = FastJSONObjAttrToNumber.toString(jsonObject, "created_at");
                this.cjsj = DataprobiDateUtil.TimeStamp2Date(tm);
                boolean onsale = FastJSONObjAttrToNumber.toBoolean(jsonObject, "is_onsale");
                this.sjzt = onsale ? 1 : 0;
                this.cid = FastJSONObjAttrToNumber.toString(jsonObject, "cat_id");
            }

            if (qd.equals("DY")) {
                this.bbid = FastJSONObjAttrToNumber.toString(jsonObject, "product_id");
                this.img = FastJSONObjAttrToNumber.toString(jsonObject, "img");
                this.bbmc = FastJSONObjAttrToNumber.toString(jsonObject, "name");
                this.price = MathUtil.divide100(FastJSONObjAttrToNumber.toDouble(jsonObject, "discount_price")) + "";
                String tm = FastJSONObjAttrToNumber.toString(jsonObject, "created_at");
                this.cjsj = FastJSONObjAttrToNumber.toString(jsonObject, "create_time");
                String tab = FastJSONObjAttrToNumber.toString(jsonObject, "tab");
                this.sjzt = StringUtils.isBlank(tab) ? 0 : (tab.equals("售卖中") ? 1 : 0);
                this.cid = FastJSONObjAttrToNumber.toString(jsonObject, "category_leaf_id");
            }

            if (qd.equals("TGC")) {
                this.bbid = FastJSONObjAttrToNumber.toString(jsonObject, "itemId");
                this.img = FastJSONObjAttrToNumber.toString(jsonObject, "url");
                this.bbmc = FastJSONObjAttrToNumber.toString(jsonObject, "itemTitle");
                this.sjzt = FastJSONObjAttrToNumber.toString(jsonObject, "shelfStatus", "auditStatusCode").equals("UPSHELF") ? 1 : 0;
                this.cid = FastJSONObjAttrToNumber.toString(jsonObject, "cateLeafId");
            }

            if (qd.equals("KS")) {
                this.bbid = FastJSONObjAttrToNumber.toString(jsonObject, "itemId");
                this.img = FastJSONObjAttrToNumber.toString2(jsonObject, "itemDesc", "mainImage", 0);
                this.bbmc = FastJSONObjAttrToNumber.toString(jsonObject, "itemDesc", "title", "text");
                String minprice = FastJSONObjAttrToNumber.toString2(jsonObject, "managerPrice", "price", 0);
                String maxprice = FastJSONObjAttrToNumber.toString2(jsonObject, "managerPrice", "price", 1);
                this.price = minprice + "-" + maxprice;
                this.cjsj = FastJSONObjAttrToNumber.toString(jsonObject, "createTime");
                int zt = FastJSONObjAttrToNumber.toInt(jsonObject, "status", "statusKey");
                if (zt == 1) {
                    this.sjzt = 1;
                } else {
                    this.sjzt = 0;
                }
                //没给分类id
                //this.cid = FastJSONObjAttrToNumber.toString(jsonObject, "catId");
            }
            if (qd.equals("WPH")) {
                this.bbid = FastJSONObjAttrToNumber.toString(jsonObject, "bbid");
                this.img = FastJSONObjAttrToNumber.toString(jsonObject, "img");
                this.bbmc = FastJSONObjAttrToNumber.toString(jsonObject, "bbmc");
                this.price = FastJSONObjAttrToNumber.toString(jsonObject, "price");
                this.scsjsj = FastJSONObjAttrToNumber.toString(jsonObject, "scsjsj");
                this.cjsj = FastJSONObjAttrToNumber.toString(jsonObject, "cjsj");
                this.sjzt = FastJSONObjAttrToNumber.toInt(jsonObject, "sfsj");
                this.cid = FastJSONObjAttrToNumber.toString(jsonObject, "cid");
                this.mainskuid = FastJSONObjAttrToNumber.toString(jsonObject, "mainskuid");
                this.spuid = FastJSONObjAttrToNumber.toString(jsonObject, "spuid");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    public int getYhid() {
        return yhid;
    }

    public void setYhid(int yhid) {
        this.yhid = yhid;
    }

    public String getBbid() {
        return bbid;
    }

    public void setBbid(String bbid) {
        this.bbid = bbid;
    }

    public String getImg() {
        return img == null ? "" : img;
    }

    public void setImg(String img) {
        this.img = img;
    }

    public String getBbmc() {
        return bbmc;
    }

    public void setBbmc(String bbmc) {
        this.bbmc = bbmc;
    }

    public String getPrice() {
        return price == null ? "" : price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public String getCjsj() {
        return cjsj == null ? "" : cjsj;
    }

    public void setCjsj(String cjsj) {
        this.cjsj = cjsj;
    }

    public int getSjzt() {
        return sjzt;
    }

    public void setSjzt(int sjzt) {
        this.sjzt = sjzt;
    }

    public String getQd() {
        return qd;
    }

    public void setQd(String qd) {
        this.qd = qd;
    }

    public String getUserid() {
        return userid;
    }

    public void setUserid(String userid) {
        this.userid = userid;
    }

    public String getCid() {
        return cid;
    }

    public void setCid(String cid) {
        this.cid = cid;
    }

    public String getScsjsj() {
        return scsjsj;
    }

    public void setScsjsj(String scsjsj) {
        this.scsjsj = scsjsj;
    }

    public String getMainskuid() {
        return mainskuid;
    }

    public void setMainskuid(String mainskuid) {
        this.mainskuid = mainskuid;
    }

    public String getSpuid() {
        return spuid;
    }

    public void setSpuid(String spuid) {
        this.spuid = spuid;
    }
}
