package com.abujlb.dataprobi.bean.albb;

import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.SqliteSp;
import com.abujlb.dataprobi.interfaces.Plusable;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.abujlb.dataprobi.util.MathUtil;
import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 * @date 2025/5/13
 */
public class SqliteAlbbCxfaSp extends AbstractSqliteSp implements Plusable<SqliteAlbbCxfaSp> {

    //超星方案花费方案花费
    private double cxfahf;

    public double getCxfahf() {
        return cxfahf;
    }

    public void setCxfahf(double cxfahf) {
        this.cxfahf = cxfahf;
    }
    @Override
    public void setValues(JSONObject jsonObject) {
        this.setBbid(FastJSONObjAttrToNumber.toString(jsonObject, "spid"));
        this.setCxfahf(FastJSONObjAttrToNumber.toDouble(jsonObject, "xhl"));
    }

    @Override
    public void plus(SqliteAlbbCxfaSp temp) {
        this.cxfahf = MathUtil.add(this.cxfahf, temp.getCxfahf());
    }

    @Override
    public boolean compare(SqliteSp sqliteSp) {
        if (sqliteSp == null) {
            return false;
        }

        JSONObject jsonObject = JSONObject.parseObject(sqliteSp.getOtherOrDefault());
        return FastJSONObjAttrToNumber.toDouble(jsonObject, "cxfahf") == this.getCxfahf();
    }
}
