package com.abujlb.dataprobi.bean.albb;

import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.SqliteSp;
import com.abujlb.dataprobi.interfaces.Plusable;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.abujlb.dataprobi.util.MathUtil;
import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 * @date 2024/7/30
 */
public class SqliteAlbbXfpjjfaSp extends AbstractSqliteSp implements Plusable<SqliteAlbbXfpjjfaSp> {

    //消费品解决方案花费
    private double xfpjjfahf;

    public double getXfpjjfahf() {
        return xfpjjfahf;
    }

    public void setXfpjjfahf(double xfpjjfahf) {
        this.xfpjjfahf = xfpjjfahf;
    }
    @Override
    public void setValues(JSONObject jsonObject) {
        this.setBbid(FastJSONObjAttrToNumber.toString(jsonObject, "spid"));
        this.setXfpjjfahf(FastJSONObjAttrToNumber.toDouble(jsonObject, "xhl"));
    }

    @Override
    public void plus(SqliteAlbbXfpjjfaSp temp) {
        this.xfpjjfahf = MathUtil.add(this.xfpjjfahf, temp.getXfpjjfahf());
    }

    @Override
    public boolean compare(SqliteSp sqliteSp) {
        if (sqliteSp == null) {
            return false;
        }

        JSONObject jsonObject = JSONObject.parseObject(sqliteSp.getOtherOrDefault());
        return FastJSONObjAttrToNumber.toDouble(jsonObject, "xfpjjfahf") == this.getXfpjjfahf();
    }
}
