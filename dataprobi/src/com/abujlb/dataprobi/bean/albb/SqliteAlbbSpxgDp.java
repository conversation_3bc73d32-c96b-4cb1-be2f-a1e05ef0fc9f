package com.abujlb.dataprobi.bean.albb;


import com.abujlb.dataprobi.bean.AbstractSqliteDp;
import com.abujlb.dataprobi.bean.SqliteDp;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.abujlb.dataprobi.util.MathUtil;
import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 * @date 2023/8/11
 */
public class SqliteAlbbSpxgDp extends AbstractSqliteDp {

    //展现次数
    private int spxgzxcs;
    //广告展现次数
    private int spxgggzxcs;
    //营销账户扣费
    private double yxzhkf;

    public int getSpxgzxcs() {
        return spxgzxcs;
    }

    public void setSpxgzxcs(int spxgzxcs) {
        this.spxgzxcs = spxgzxcs;
    }

    public int getSpxgggzxcs() {
        return spxgggzxcs;
    }

    public void setSpxgggzxcs(int spxgggzxcs) {
        this.spxgggzxcs = spxgggzxcs;
    }

    public double getYxzhkf() {
        return yxzhkf;
    }

    public void setYxzhkf(double yxzhkf) {
        this.yxzhkf = yxzhkf;
    }

    @Override
    public <T extends AbstractSqliteDp> void setValues(String[] tableStoreColumnValues) {
        String value0 = tableStoreColumnValues[0];
        if (value0 != null) {
            JSONObject jsonObject = JSONObject.parseObject(value0);
            this.spxgzxcs = FastJSONObjAttrToNumber.toInt(jsonObject, "revealCnt", "value");
            this.spxgggzxcs = FastJSONObjAttrToNumber.toInt(jsonObject, "adRevealCnt", "value");
            this.spxgfks = FastJSONObjAttrToNumber.toInt(jsonObject, "uv", "value");
            this.spxglll = FastJSONObjAttrToNumber.toInt(jsonObject, "pv", "value");
            this.spxgzfje = FastJSONObjAttrToNumber.toDouble(jsonObject, "payAmt", "value");
            this.spxgzfmjs = FastJSONObjAttrToNumber.toInt(jsonObject, "payByrCnt", "value");
            this.spxgzfzhl = MathUtil.calZhlv(this.spxgzfmjs, this.spxgfks);
            this.spxgtkje = FastJSONObjAttrToNumber.toDouble(jsonObject, "rfdSucAmt", "value");
            this.spxgzfjs = FastJSONObjAttrToNumber.toInt(jsonObject, "payItemQty", "value");
            this.yxzhkf = FastJSONObjAttrToNumber.toDouble(jsonObject, "wxbCostFamt", "value");
        }
        String value1 = tableStoreColumnValues[1];
        if (value1 != null) {
            JSONObject jsonObject = JSONObject.parseObject(value1);
            this.spxgspfks = FastJSONObjAttrToNumber.toInt(jsonObject, "uv", "value");
            this.spxgsplll = FastJSONObjAttrToNumber.toInt(jsonObject, "itemPv", "value");
        }
    }

    @Override
    public boolean compare(SqliteDp sqliteDp) {
        if (sqliteDp == null) {
            return false;
        }

        JSONObject jsonObject = JSONObject.parseObject(sqliteDp.getOtherOrDefault());
        return FastJSONObjAttrToNumber.toInt(jsonObject, "spxgzxcs") == this.getSpxgzxcs()
                && FastJSONObjAttrToNumber.toInt(jsonObject, "spxgggzxcs") == this.getSpxgggzxcs()
                && sqliteDp.getSpxgfks() == this.getSpxgfks()
                && sqliteDp.getSpxglll() == this.getSpxglll()
                && sqliteDp.getSpxgzfje() == this.getSpxgzfje()
                && sqliteDp.getSpxgzfmjs() == this.getSpxgzfmjs()
                && sqliteDp.getSpxgtkje() == this.getSpxgtkje()
                && sqliteDp.getSpxgzfjs() == this.getSpxgzfjs()
                && FastJSONObjAttrToNumber.toDouble(jsonObject, "yxzhkf") == this.getYxzhkf();
    }
}
