package com.abujlb.dataprobi.bean.albb;

import com.abujlb.dataprobi.annotations.ClrqIgnore;
import com.abujlb.dataprobi.annotations.SumIgnore;
import com.abujlb.dataprobi.annotations.UpdateSycmcljzrqIgnore;
import com.abujlb.dataprobi.bean.DataprobiBaseMsgBean;
import com.abujlb.dataprobi.enums.Qd;
import com.alibaba.fastjson.JSON;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/10
 */
public class Dataprobi1688Msg extends DataprobiBaseMsgBean {
    //商品效果
    @UpdateSycmcljzrqIgnore
    private List<String> spxg;

    //商品效果店铺
    @UpdateSycmcljzrqIgnore
    private List<String> spxgdp;

    //全站推店
    private List<String> qztd;
    //全站推店店铺
    private List<String> qztddp;
    //全站销品
    private List<String> qzxp;
    //全站销品店铺
    private List<String> qzxpdp;
    //场景投放
    private List<String> cjtf;
    private List<String> cjtfdp;
    //首位展示店铺
    private List<String> swzsdp;
    //定位推广店铺
    private List<String> dwtgdp;
    //搜索展播店铺
    private List<String> sszbdp;
    //品牌专区店铺
    private List<String> ppzqdp;
    //明星工厂店铺
    private List<String> mxgcdp;
    //工业品解决方案
    private List<String> gypjjfa;
    //工业品解决方案店铺
    private List<String> gypjjfadp;
    //全店引新
    private List<String> qdyx;
    //全店引新店铺
    private List<String> qdyxdp;
    //SKA专属定制方案
    private List<String> skazsdzfa;
    //SKA专属定制方案店铺
    private List<String> skazsdzfadp;
    //SKA驾驶舱
    private List<String> skajsc;
    //SKA驾驶舱店铺
    private List<String> skajscdp;
    private List<String> yxhb;
    //工厂生意加速计划
    private List<String> gcsyjsjh;
    //工厂生意加速计划店铺
    private List<String> gcsyjsjhdp;
    //找工厂解决方案
    private List<String> zgcjjfa;
    //找工厂解决方案店铺
    private List<String> zgcjjfadp;
    //核心商家成长计划
    private List<String> hxsjczjh;
    //核心商家成长计划店铺
    private List<String> hxsjczjhdp;
    //消费品解决方案
    private List<String> xfpjjfa;
    //消费品解决方案店铺
    private List<String> xfpjjfadp;
    //超星方案
    private List<String> cxfa;
    //超星方案店铺
    private List<String> cxfadp;


    //阿里巴巴计划状态
    @UpdateSycmcljzrqIgnore
    @ClrqIgnore
    @SumIgnore
    private List<String> albbjhzt;




//    店铺体验分店铺维度
    @UpdateSycmcljzrqIgnore
    @ClrqIgnore
    @SumIgnore
    private List<String> albbsjtyf;

    public List<String> getAlbbsjtyf() {
        return albbsjtyf;
    }

    public void setAlbbsjtyf(List<String> albbsjtyf) {
        this.albbsjtyf = albbsjtyf;
    }

    public Dataprobi1688Msg() {
    }

    public List<String> getSpxg() {
        return spxg;
    }

    public void setSpxg(List<String> spxg) {
        this.spxg = spxg;
    }

    public List<String> getSpxgdp() {
        return spxgdp;
    }

    public void setSpxgdp(List<String> spxgdp) {
        this.spxgdp = spxgdp;
    }

    public List<String> getYxhb() {
        return yxhb;
    }

    public void setYxhb(List<String> yxhb) {
        this.yxhb = yxhb;
    }

    public List<String> getQztd() {
        return qztd;
    }

    public void setQztd(List<String> qztd) {
        this.qztd = qztd;
    }

    public List<String> getQztddp() {
        return qztddp;
    }

    public void setQztddp(List<String> qztddp) {
        this.qztddp = qztddp;
    }

    public List<String> getCjtf() {
        return cjtf;
    }

    public void setCjtf(List<String> cjtf) {
        this.cjtf = cjtf;
    }

    public List<String> getCjtfdp() {
        return cjtfdp;
    }

    public void setCjtfdp(List<String> cjtfdp) {
        this.cjtfdp = cjtfdp;
    }

    public List<String> getSwzsdp() {
        return swzsdp;
    }

    public void setSwzsdp(List<String> swzsdp) {
        this.swzsdp = swzsdp;
    }

    public List<String> getDwtgdp() {
        return dwtgdp;
    }

    public void setDwtgdp(List<String> dwtgdp) {
        this.dwtgdp = dwtgdp;
    }

    public List<String> getSszbdp() {
        return sszbdp;
    }

    public void setSszbdp(List<String> sszbdp) {
        this.sszbdp = sszbdp;
    }

    public List<String> getPpzqdp() {
        return ppzqdp;
    }

    public void setPpzqdp(List<String> ppzqdp) {
        this.ppzqdp = ppzqdp;
    }

    public List<String> getMxgcdp() {
        return mxgcdp;
    }

    public void setMxgcdp(List<String> mxgcdp) {
        this.mxgcdp = mxgcdp;
    }

    public List<String> getGypjjfa() {
        return gypjjfa;
    }

    public void setGypjjfa(List<String> gypjjfa) {
        this.gypjjfa = gypjjfa;
    }

    public List<String> getGypjjfadp() {
        return gypjjfadp;
    }

    public void setGypjjfadp(List<String> gypjjfadp) {
        this.gypjjfadp = gypjjfadp;
    }

    public List<String> getQdyx() {
        return qdyx;
    }

    public void setQdyx(List<String> qdyx) {
        this.qdyx = qdyx;
    }

    public List<String> getQdyxdp() {
        return qdyxdp;
    }

    public void setQdyxdp(List<String> qdyxdp) {
        this.qdyxdp = qdyxdp;
    }

    public List<String> getQzxp() {
        return qzxp;
    }

    public void setQzxp(List<String> qzxp) {
        this.qzxp = qzxp;
    }

    public List<String> getQzxpdp() {
        return qzxpdp;
    }

    public void setQzxpdp(List<String> qzxpdp) {
        this.qzxpdp = qzxpdp;
    }

    public List<String> getSkazsdzfa() {
        return skazsdzfa;
    }

    public void setSkazsdzfa(List<String> skazsdzfa) {
        this.skazsdzfa = skazsdzfa;
    }

    public List<String> getSkazsdzfadp() {
        return skazsdzfadp;
    }

    public void setSkazsdzfadp(List<String> skazsdzfadp) {
        this.skazsdzfadp = skazsdzfadp;
    }

    public List<String> getSkajsc() {
        return skajsc;
    }

    public void setSkajsc(List<String> skajsc) {
        this.skajsc = skajsc;
    }

    public List<String> getSkajscdp() {
        return skajscdp;
    }

    public void setSkajscdp(List<String> skajscdp) {
        this.skajscdp = skajscdp;
    }

    public List<String> getGcsyjsjh() {
        return gcsyjsjh;
    }

    public void setGcsyjsjh(List<String> gcsyjsjh) {
        this.gcsyjsjh = gcsyjsjh;
    }

    public List<String> getGcsyjsjhdp() {
        return gcsyjsjhdp;
    }

    public void setGcsyjsjhdp(List<String> gcsyjsjhdp) {
        this.gcsyjsjhdp = gcsyjsjhdp;
    }

    public List<String> getZgcjjfa() {
        return zgcjjfa;
    }

    public void setZgcjjfa(List<String> zgcjjfa) {
        this.zgcjjfa = zgcjjfa;
    }

    public List<String> getZgcjjfadp() {
        return zgcjjfadp;
    }

    public void setZgcjjfadp(List<String> zgcjjfadp) {
        this.zgcjjfadp = zgcjjfadp;
    }

    public List<String> getHxsjczjh() {
        return hxsjczjh;
    }

    public void setHxsjczjh(List<String> hxsjczjh) {
        this.hxsjczjh = hxsjczjh;
    }

    public List<String> getHxsjczjhdp() {
        return hxsjczjhdp;
    }

    public void setHxsjczjhdp(List<String> hxsjczjhdp) {
        this.hxsjczjhdp = hxsjczjhdp;
    }

    public List<String> getXfpjjfa() {
        return xfpjjfa;
    }

    public void setXfpjjfa(List<String> xfpjjfa) {
        this.xfpjjfa = xfpjjfa;
    }

    public List<String> getXfpjjfadp() {
        return xfpjjfadp;
    }

    public void setXfpjjfadp(List<String> xfpjjfadp) {
        this.xfpjjfadp = xfpjjfadp;
    }

    public List<String> getAlbbjhzt() {
        return albbjhzt;
    }

    public void setAlbbjhzt(List<String> albbjhzt) {
        this.albbjhzt = albbjhzt;
    }

    public List<String> getCxfa() {
        return cxfa;
    }

    public void setCxfa(List<String> cxfa) {
        this.cxfa = cxfa;
    }

    public List<String> getCxfadp() {
        return cxfadp;
    }

    public void setCxfadp(List<String> cxfadp) {
        this.cxfadp = cxfadp;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }


    public void fillNullList() throws InvocationTargetException, IllegalAccessException {
        Method[] methods = this.getClass().getMethods();
        for (Method method : methods) {
            if (method.getName().startsWith("get")) {
                Object value = method.invoke(this);
                if (value == null) {
                    Method setMethod = null;
                    try {
                        setMethod = this.getClass().getMethod("s" + method.getName().substring(1), List.class);
                        setMethod.invoke(this, new ArrayList<>());
                    } catch (NoSuchMethodException ignored) {
                    }
                }
            }
        }
    }
}
