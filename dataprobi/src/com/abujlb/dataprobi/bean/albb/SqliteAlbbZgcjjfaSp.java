package com.abujlb.dataprobi.bean.albb;

import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.SqliteSp;
import com.abujlb.dataprobi.interfaces.Plusable;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.abujlb.dataprobi.util.MathUtil;
import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 * @date 2024/7/30
 */
public class SqliteAlbbZgcjjfaSp extends AbstractSqliteSp implements Plusable<SqliteAlbbZgcjjfaSp> {

    //找工厂解决方案花费
    private double zgcjjfahfhf;

    public double getZgcjjfahfhf() {
        return zgcjjfahfhf;
    }

    public void setZgcjjfahfhf(double zgcjjfahfhf) {
        this.zgcjjfahfhf = zgcjjfahfhf;
    }
    @Override
    public void setValues(JSONObject jsonObject) {
        this.setBbid(FastJSONObjAttrToNumber.toString(jsonObject, "spid"));
        this.setZgcjjfahfhf(FastJSONObjAttrToNumber.toDouble(jsonObject, "xhl"));
    }

    @Override
    public void plus(SqliteAlbbZgcjjfaSp temp) {
        this.zgcjjfahfhf = MathUtil.add(this.zgcjjfahfhf, temp.getZgcjjfahfhf());
    }

    @Override
    public boolean compare(SqliteSp sqliteSp) {
        if (sqliteSp == null) {
            return false;
        }

        JSONObject jsonObject = JSONObject.parseObject(sqliteSp.getOtherOrDefault());
        return FastJSONObjAttrToNumber.toDouble(jsonObject, "zgcjjfahfhf") == this.getZgcjjfahfhf();
    }
}
