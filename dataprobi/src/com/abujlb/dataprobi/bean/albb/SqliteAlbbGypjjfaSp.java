package com.abujlb.dataprobi.bean.albb;

import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.SqliteSp;
import com.abujlb.dataprobi.interfaces.Plusable;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.abujlb.dataprobi.util.MathUtil;
import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 * @date 2024/6/12
 */
public class SqliteAlbbGypjjfaSp extends AbstractSqliteSp implements Plusable<SqliteAlbbGypjjfaSp> {

    //工业品解决方案花费
    private double gypjjfahf;

    public double getGypjjfahf() {
        return gypjjfahf;
    }

    public void setGypjjfahf(double gypjjfahf) {
        this.gypjjfahf = gypjjfahf;
    }
    @Override
    public void setValues(JSONObject jsonObject) {
        this.setBbid(FastJSONObjAttrToNumber.toString(jsonObject, "spid"));
        this.setGypjjfahf(FastJSONObjAttrToNumber.toDouble(jsonObject, "xhl"));
    }

    @Override
    public void plus(SqliteAlbbGypjjfaSp temp) {
        this.gypjjfahf = MathUtil.add(this.gypjjfahf, temp.getGypjjfahf());
    }

    @Override
    public boolean compare(SqliteSp sqliteSp) {
        if (sqliteSp == null) {
            return false;
        }

        JSONObject jsonObject = JSONObject.parseObject(sqliteSp.getOtherOrDefault());
        return FastJSONObjAttrToNumber.toDouble(jsonObject, "gypjjfahf") == this.getGypjjfahf();
    }
}
