package com.abujlb.dataprobi.bean.albb;

import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.SqliteSp;
import com.abujlb.dataprobi.interfaces.Plusable;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.abujlb.dataprobi.util.MathUtil;
import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 * @date 2024/6/12
 */
public class SqliteAlbbGcsyjsjhSp extends AbstractSqliteSp implements Plusable<SqliteAlbbGcsyjsjhSp> {

    //工厂生意加速计划花费
    private double gcsyjsjhhf;

    public double getGcsyjsjhhf() {
        return gcsyjsjhhf;
    }

    public void setGcsyjsjhhf(double gcsyjsjhhf) {
        this.gcsyjsjhhf = gcsyjsjhhf;
    }
    @Override
    public void setValues(JSONObject jsonObject) {
        this.setBbid(FastJSONObjAttrToNumber.toString(jsonObject, "spid"));
        this.setGcsyjsjhhf(FastJSONObjAttrToNumber.toDouble(jsonObject, "xhl"));
    }

    @Override
    public void plus(SqliteAlbbGcsyjsjhSp temp) {
        this.gcsyjsjhhf = MathUtil.add(this.gcsyjsjhhf, temp.getGcsyjsjhhf());
    }

    @Override
    public boolean compare(SqliteSp sqliteSp) {
        if (sqliteSp == null) {
            return false;
        }

        JSONObject jsonObject = JSONObject.parseObject(sqliteSp.getOtherOrDefault());
        return FastJSONObjAttrToNumber.toDouble(jsonObject, "gcsyjsjhhf") == this.getGcsyjsjhhf();
    }
}
