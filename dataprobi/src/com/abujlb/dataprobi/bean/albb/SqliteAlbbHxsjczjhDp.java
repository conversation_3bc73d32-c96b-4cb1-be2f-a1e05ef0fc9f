package com.abujlb.dataprobi.bean.albb;


import com.abujlb.dataprobi.bean.AbstractSqliteDp;
import com.abujlb.dataprobi.bean.SqliteDp;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 * @date 2024/7/30
 */
public class SqliteAlbbHxsjczjhDp extends AbstractSqliteDp {

    //核心商家成长计划花费
    private double hxsjczjhhf;

    public double getHxsjczjhhf() {
        return hxsjczjhhf;
    }

    public void setHxsjczjhhf(double hxsjczjhhf) {
        this.hxsjczjhhf = hxsjczjhhf;
    }

    @Override
    public <T extends AbstractSqliteDp> void setValues(String[] tableStoreColumnValues) {
        String value0 = tableStoreColumnValues[0];
        if (value0 != null) {
            JSONObject jsonObject = JSONObject.parseObject(value0);
            this.hxsjczjhhf = FastJSONObjAttrToNumber.toDouble(jsonObject, "cost");
        }
    }

    @Override
    public boolean compare(SqliteDp sqliteDp) {
        if (sqliteDp == null) {
            return false;
        }

        JSONObject jsonObject = JSONObject.parseObject(sqliteDp.getOtherOrDefault());
        return FastJSONObjAttrToNumber.toDouble(jsonObject, "hxsjczjhhf") == this.getHxsjczjhhf();
    }
}
