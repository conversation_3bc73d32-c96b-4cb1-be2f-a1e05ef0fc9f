package com.abujlb.dataprobi.bean.albb;


import com.abujlb.dataprobi.bean.AbstractSqliteDp;
import com.abujlb.dataprobi.bean.SqliteDp;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 * @date 2024/6/12
 */
public class SqliteAlbbPpzqDp extends AbstractSqliteDp {

    //品牌专区花费
    private double ppzqhf;

    public double getPpzqhf() {
        return ppzqhf;
    }

    public void setPpzqhf(double ppzqhf) {
        this.ppzqhf = ppzqhf;
    }

    @Override
    public <T extends AbstractSqliteDp> void setValues(String[] tableStoreColumnValues) {
        String value0 = tableStoreColumnValues[0];
        if (value0 != null) {
            JSONObject jsonObject = JSONObject.parseObject(value0);
            this.ppzqhf = FastJSONObjAttrToNumber.toDouble(jsonObject, "cost");
        }
    }

    @Override
    public boolean compare(SqliteDp sqliteDp) {
        if (sqliteDp == null) {
            return false;
        }

        JSONObject jsonObject = JSONObject.parseObject(sqliteDp.getOtherOrDefault());
        return FastJSONObjAttrToNumber.toDouble(jsonObject, "ppzqhf") == this.getPpzqhf();
    }
}
