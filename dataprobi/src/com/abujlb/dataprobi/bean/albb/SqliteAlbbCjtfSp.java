package com.abujlb.dataprobi.bean.albb;

import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.SqliteSp;
import com.abujlb.dataprobi.interfaces.Plusable;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.abujlb.dataprobi.util.MathUtil;
import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 * @date 2024/6/12
 */
public class SqliteAlbbCjtfSp extends AbstractSqliteSp implements Plusable<SqliteAlbbCjtfSp> {

    //场景投放花费
    private double cjtfhf;

    public double getCjtfhf() {
        return cjtfhf;
    }

    public void setCjtfhf(double cjtfhf) {
        this.cjtfhf = cjtfhf;
    }

    @Override
    public void setValues(JSONObject jsonObject) {
        this.setBbid(FastJSONObjAttrToNumber.toString(jsonObject, "spid"));
        this.setCjtfhf(FastJSONObjAttrToNumber.toDouble(jsonObject, "xhl"));
    }

    @Override
    public void plus(SqliteAlbbCjtfSp temp) {
        this.cjtfhf = MathUtil.add(this.cjtfhf, temp.getCjtfhf());
    }

    @Override
    public boolean compare(SqliteSp sqliteSp) {
        if (sqliteSp == null) {
            return false;
        }

        JSONObject jsonObject = JSONObject.parseObject(sqliteSp.getOtherOrDefault());
        return FastJSONObjAttrToNumber.toDouble(jsonObject, "cjtfhf") == this.getCjtfhf();
    }
}
