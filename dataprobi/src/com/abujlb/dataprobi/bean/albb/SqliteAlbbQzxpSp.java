package com.abujlb.dataprobi.bean.albb;

import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.SqliteSp;
import com.abujlb.dataprobi.interfaces.Plusable;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.abujlb.dataprobi.util.MathUtil;
import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 * @date 2024/7/5
 */
public class SqliteAlbbQzxpSp extends AbstractSqliteSp implements Plusable<SqliteAlbbQzxpSp> {

    //全站销品花费
    private double qzxphf;

    public double getQzxphf() {
        return qzxphf;
    }

    public void setQzxphf(double qzxphf) {
        this.qzxphf = qzxphf;
    }
    @Override
    public void setValues(JSONObject jsonObject) {
        this.setBbid(FastJSONObjAttrToNumber.toString(jsonObject, "spid"));
        this.setQzxphf(FastJSONObjAttrToNumber.toDouble(jsonObject, "xhl"));
    }

    @Override
    public void plus(SqliteAlbbQzxpSp temp) {
        this.qzxphf = MathUtil.add(this.qzxphf, temp.getQzxphf());
    }

    @Override
    public boolean compare(SqliteSp sqliteSp) {
        if (sqliteSp == null) {
            return false;
        }

        JSONObject jsonObject = JSONObject.parseObject(sqliteSp.getOtherOrDefault());
        return FastJSONObjAttrToNumber.toDouble(jsonObject, "qzxphf") == this.getQzxphf();
    }
}
