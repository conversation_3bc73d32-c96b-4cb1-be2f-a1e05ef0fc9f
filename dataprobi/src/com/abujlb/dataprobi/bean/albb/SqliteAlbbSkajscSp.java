package com.abujlb.dataprobi.bean.albb;

import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.SqliteSp;
import com.abujlb.dataprobi.interfaces.Plusable;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.abujlb.dataprobi.util.MathUtil;
import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 * @date 2024/7/15
 */
public class SqliteAlbbSkajscSp extends AbstractSqliteSp implements Plusable<SqliteAlbbSkajscSp> {

    //SKA驾驶舱花费
    private double skajschf;

    public double getSkajschf() {
        return skajschf;
    }

    public void setSkajschf(double skajschf) {
        this.skajschf = skajschf;
    }
    @Override
    public void setValues(JSONObject jsonObject) {
        this.setBbid(FastJSONObjAttrToNumber.toString(jsonObject, "spid"));
        this.setSkajschf(FastJSONObjAttrToNumber.toDouble(jsonObject, "xhl"));
    }

    @Override
    public void plus(SqliteAlbbSkajscSp temp) {
        this.skajschf = MathUtil.add(this.skajschf, temp.getSkajschf());
    }

    @Override
    public boolean compare(SqliteSp sqliteSp) {
        if (sqliteSp == null) {
            return false;
        }

        JSONObject jsonObject = JSONObject.parseObject(sqliteSp.getOtherOrDefault());
        return FastJSONObjAttrToNumber.toDouble(jsonObject, "skajschf") == this.getSkajschf();
    }
}
