package com.abujlb.dataprobi.bean.albb;

import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.SqliteSp;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 * @date 2023/8/10
 */
public class SqliteAlbbSpxgSp extends AbstractSqliteSp {

    //展现次数
    private int spxgzxcs;
    private int spxgscrs;
    private int spxgjgrs;

    public int getSpxgzxcs() {
        return spxgzxcs;
    }

    public void setSpxgzxcs(int spxgzxcs) {
        this.spxgzxcs = spxgzxcs;
    }

    public int getSpxgscrs() {
        return spxgscrs;
    }

    public void setSpxgscrs(int spxgscrs) {
        this.spxgscrs = spxgscrs;
    }

    public int getSpxgjgrs() {
        return spxgjgrs;
    }

    public void setSpxgjgrs(int spxgjgrs) {
        this.spxgjgrs = spxgjgrs;
    }

    @Override
    public void setValues(JSONObject jsonObject) {
        this.bbid = FastJSONObjAttrToNumber.toString(jsonObject, "item", "offerId");
        this.spxgfks = FastJSONObjAttrToNumber.toInt(jsonObject, "uv", "value");
        this.spxglll = FastJSONObjAttrToNumber.toInt(jsonObject, "pv", "value");
        this.spxgzfje = FastJSONObjAttrToNumber.toDouble(jsonObject, "payAmt", "value");
        this.spxgzfmjs = FastJSONObjAttrToNumber.toInt(jsonObject, "payByrCnt", "value");
        this.spxgzfzhl = FastJSONObjAttrToNumber.toDouble(jsonObject, "payRate", "value");
        this.spxgzfjs = FastJSONObjAttrToNumber.toInt(jsonObject, "payItemQty", "value");
        this.spxgzxcs = FastJSONObjAttrToNumber.toInt(jsonObject, "itemRevealCnt", "value");
        this.spxgscrs = FastJSONObjAttrToNumber.toInt(jsonObject, "cltByrCnt", "value");
        this.spxgjgrs = FastJSONObjAttrToNumber.toInt(jsonObject, "additionByrCnt", "value");
    }

    @Override
    public boolean compare(SqliteSp sqliteSp) {
        if (sqliteSp == null) {
            return false;
        }

        JSONObject jsonObject = JSONObject.parseObject(sqliteSp.getOtherOrDefault());
        return FastJSONObjAttrToNumber.toInt(jsonObject, "spxgzxcs") == this.getSpxgzxcs()
                && sqliteSp.getSpxgfks() == this.getSpxgfks()
                && sqliteSp.getSpxglll() == this.getSpxglll()
                && sqliteSp.getSpxgzfje() == this.getSpxgzfje()
                && sqliteSp.getSpxgzfmjs() == this.getSpxgzfmjs()
                && sqliteSp.getSpxgzfjs() == this.getSpxgzfjs()
                && FastJSONObjAttrToNumber.toInt(jsonObject, "spxgscrs") == this.getSpxgscrs()
                && FastJSONObjAttrToNumber.toInt(jsonObject, "spxgjgrs") == this.getSpxgjgrs();
    }
}
