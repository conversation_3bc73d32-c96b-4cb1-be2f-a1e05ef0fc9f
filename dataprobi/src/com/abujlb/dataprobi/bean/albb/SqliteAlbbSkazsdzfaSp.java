package com.abujlb.dataprobi.bean.albb;

import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.SqliteSp;
import com.abujlb.dataprobi.interfaces.Plusable;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.abujlb.dataprobi.util.MathUtil;
import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 * @date 2024/7/9
 */
public class SqliteAlbbSkazsdzfaSp extends AbstractSqliteSp implements Plusable<SqliteAlbbSkazsdzfaSp> {

    //SKA专属定制方案花费
    private double skazsdzfahf;

    public double getSkazsdzfahf() {
        return skazsdzfahf;
    }

    public void setSkazsdzfahf(double skazsdzfahf) {
        this.skazsdzfahf = skazsdzfahf;
    }
    @Override
    public void setValues(JSONObject jsonObject) {
        this.setBbid(FastJSONObjAttrToNumber.toString(jsonObject, "spid"));
        this.setSkazsdzfahf(FastJSONObjAttrToNumber.toDouble(jsonObject, "xhl"));
    }

    @Override
    public void plus(SqliteAlbbSkazsdzfaSp temp) {
        this.skazsdzfahf = MathUtil.add(this.skazsdzfahf, temp.getSkazsdzfahf());
    }

    @Override
    public boolean compare(SqliteSp sqliteSp) {
        if (sqliteSp == null) {
            return false;
        }

        JSONObject jsonObject = JSONObject.parseObject(sqliteSp.getOtherOrDefault());
        return FastJSONObjAttrToNumber.toDouble(jsonObject, "skazsdzfahf") == this.getSkazsdzfahf();
    }
}
