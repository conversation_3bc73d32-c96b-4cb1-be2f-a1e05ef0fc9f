package com.abujlb.dataprobi.bean.albb;

import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.SqliteSp;
import com.abujlb.dataprobi.interfaces.Plusable;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.abujlb.dataprobi.util.MathUtil;
import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 * @date 2024/6/12
 */
public class SqliteAlbbQdyxSp extends AbstractSqliteSp implements Plusable<SqliteAlbbQdyxSp> {

    //全店引新花费
    private double qdyxhf;

    public double getQdyxhf() {
        return qdyxhf;
    }

    public void setQdyxhf(double qdyxhf) {
        this.qdyxhf = qdyxhf;
    }
    @Override
    public void setValues(JSONObject jsonObject) {
        this.setBbid(FastJSONObjAttrToNumber.toString(jsonObject, "spid"));
        this.setQdyxhf(FastJSONObjAttrToNumber.toDouble(jsonObject, "xhl"));
    }

    @Override
    public void plus(SqliteAlbbQdyxSp temp) {
        this.qdyxhf = MathUtil.add(this.qdyxhf, temp.getQdyxhf());
    }

    @Override
    public boolean compare(SqliteSp sqliteSp) {
        if (sqliteSp == null) {
            return false;
        }

        JSONObject jsonObject = JSONObject.parseObject(sqliteSp.getOtherOrDefault());
        return FastJSONObjAttrToNumber.toDouble(jsonObject, "qdyxhf") == this.getQdyxhf();
    }
}
