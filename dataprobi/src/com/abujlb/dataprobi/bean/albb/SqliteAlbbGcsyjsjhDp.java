package com.abujlb.dataprobi.bean.albb;


import com.abujlb.dataprobi.bean.AbstractSqliteDp;
import com.abujlb.dataprobi.bean.SqliteDp;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 * @date 2024/7/24
 */
public class SqliteAlbbGcsyjsjhDp extends AbstractSqliteDp {

    //工厂生意加速计划花费
    private double gcsyjsjhhf;

    public double getGcsyjsjhhf() {
        return gcsyjsjhhf;
    }

    public void setGcsyjsjhhf(double gcsyjsjhhf) {
        this.gcsyjsjhhf = gcsyjsjhhf;
    }

    @Override
    public <T extends AbstractSqliteDp> void setValues(String[] tableStoreColumnValues) {
        String value0 = tableStoreColumnValues[0];
        if (value0 != null) {
            JSONObject jsonObject = JSONObject.parseObject(value0);
            this.gcsyjsjhhf = FastJSONObjAttrToNumber.toDouble(jsonObject, "cost");
        }
    }

    @Override
    public boolean compare(SqliteDp sqliteDp) {
        if (sqliteDp == null) {
            return false;
        }

        JSONObject jsonObject = JSONObject.parseObject(sqliteDp.getOtherOrDefault());
        return FastJSONObjAttrToNumber.toDouble(jsonObject, "gcsyjsjhhf") == this.getGcsyjsjhhf();
    }
}
