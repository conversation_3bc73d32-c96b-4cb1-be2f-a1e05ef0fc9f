package com.abujlb.dataprobi.bean.albb;

import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.SqliteSp;
import com.abujlb.dataprobi.interfaces.Plusable;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.abujlb.dataprobi.util.MathUtil;
import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 * @date 2024/7/30
 */
public class SqliteAlbbHxsjczjhSp extends AbstractSqliteSp implements Plusable<SqliteAlbbHxsjczjhSp> {

    //核心商家成长计划花费
    private double hxsjczjhhf;

    public double getHxsjczjhhf() {
        return hxsjczjhhf;
    }

    public void setHxsjczjhhf(double hxsjczjhhf) {
        this.hxsjczjhhf = hxsjczjhhf;
    }
    @Override
    public void setValues(JSONObject jsonObject) {
        this.setBbid(FastJSONObjAttrToNumber.toString(jsonObject, "spid"));
        this.setHxsjczjhhf(FastJSONObjAttrToNumber.toDouble(jsonObject, "xhl"));
    }

    @Override
    public void plus(SqliteAlbbHxsjczjhSp temp) {
        this.hxsjczjhhf = MathUtil.add(this.hxsjczjhhf, temp.getHxsjczjhhf());
    }

    @Override
    public boolean compare(SqliteSp sqliteSp) {
        if (sqliteSp == null) {
            return false;
        }

        JSONObject jsonObject = JSONObject.parseObject(sqliteSp.getOtherOrDefault());
        return FastJSONObjAttrToNumber.toDouble(jsonObject, "hxsjczjhhf") == this.getHxsjczjhhf();
    }
}
