package com.abujlb.dataprobi.bean.albb;


import com.abujlb.dataprobi.bean.AbstractSqliteDp;
import com.abujlb.dataprobi.bean.SqliteDp;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 * @date 2024/44
 */
public class SqliteAlbbYxhbDp extends AbstractSqliteDp {

    //营效宝花费  红包
    private double yxbhb;
    //定制营销解决方案 红包
    private double yxjjfahb;
    //全站投放红包
    private double qztfhb;
    //品牌方案红包
    private double ppfahb;
    //数字营销抵扣红包
    private double szyxdkhb;
//    //定制营销解决方案 花费
//    private double yxjjfahf;

    public double getYxbhb() {
        return yxbhb;
    }

    public void setYxbhb(double yxbhb) {
        this.yxbhb = yxbhb;
    }

    public double getYxjjfahb() {
        return yxjjfahb;
    }

    public void setYxjjfahb(double yxjjfahb) {
        this.yxjjfahb = yxjjfahb;
    }

    public double getQztfhb() {
        return qztfhb;
    }

    public void setQztfhb(double qztfhb) {
        this.qztfhb = qztfhb;
    }

    public double getPpfahb() {
        return ppfahb;
    }

    public void setPpfahb(double ppfahb) {
        this.ppfahb = ppfahb;
    }

//    public double getYxjjfahf() {
//        return yxjjfahf;
//    }
//
//    public void setYxjjfahf(double yxjjfahf) {
//        this.yxjjfahf = yxjjfahf;
//    }

    public double getSzyxdkhb() {
        return szyxdkhb;
    }

    public void setSzyxdkhb(double szyxdkhb) {
        this.szyxdkhb = szyxdkhb;
    }

    @Override
    public boolean compare(SqliteDp sqliteDp) {
        if (sqliteDp == null) {
            return false;
        }

        JSONObject jsonObject = JSONObject.parseObject(sqliteDp.getOtherOrDefault());
        return FastJSONObjAttrToNumber.toDouble(jsonObject, "yxbhb") == this.getYxbhb()
                && FastJSONObjAttrToNumber.toDouble(jsonObject, "yxjjfahb") == this.getYxjjfahb()
                && FastJSONObjAttrToNumber.toDouble(jsonObject, "qztfhb") == this.getQztfhb()
                && FastJSONObjAttrToNumber.toDouble(jsonObject, "ppfahb") == this.getPpfahb();
    }
}
