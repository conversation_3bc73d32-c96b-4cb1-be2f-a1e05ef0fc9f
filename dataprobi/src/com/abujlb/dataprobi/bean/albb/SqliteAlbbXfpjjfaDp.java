package com.abujlb.dataprobi.bean.albb;


import com.abujlb.dataprobi.bean.AbstractSqliteDp;
import com.abujlb.dataprobi.bean.SqliteDp;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 * @date 2024/7/30
 */
public class SqliteAlbbXfpjjfaDp extends AbstractSqliteDp {

    //消费品解决方案花费
    private double xfpjjfahf;

    public double getXfpjjfahf() {
        return xfpjjfahf;
    }

    public void setXfpjjfahf(double xfpjjfahf) {
        this.xfpjjfahf = xfpjjfahf;
    }

    @Override
    public <T extends AbstractSqliteDp> void setValues(String[] tableStoreColumnValues) {
        String value0 = tableStoreColumnValues[0];
        if (value0 != null) {
            JSONObject jsonObject = JSONObject.parseObject(value0);
            this.xfpjjfahf = FastJSONObjAttrToNumber.toDouble(jsonObject, "cost");
        }
    }

    @Override
    public boolean compare(SqliteDp sqliteDp) {
        if (sqliteDp == null) {
            return false;
        }

        JSONObject jsonObject = JSONObject.parseObject(sqliteDp.getOtherOrDefault());
        return FastJSONObjAttrToNumber.toDouble(jsonObject, "xfpjjfahf") == this.getXfpjjfahf();
    }
}
