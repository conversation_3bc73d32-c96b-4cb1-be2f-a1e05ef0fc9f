package com.abujlb.dataprobi.bean.albb;


import com.abujlb.dataprobi.bean.AbstractSqliteDp;
import com.abujlb.dataprobi.bean.SqliteDp;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 * @date 2024/7/9
 */
public class SqliteAlbbSkazsdzfaDp extends AbstractSqliteDp {

    //SKA专属定制方案花费
    private double skazsdzfahf;

    public double getSkazsdzfahf() {
        return skazsdzfahf;
    }

    public void setSkazsdzfahf(double skazsdzfahf) {
        this.skazsdzfahf = skazsdzfahf;
    }

    @Override
    public <T extends AbstractSqliteDp> void setValues(String[] tableStoreColumnValues) {
        String value0 = tableStoreColumnValues[0];
        if (value0 != null) {
            JSONObject jsonObject = JSONObject.parseObject(value0);
            this.skazsdzfahf = FastJSONObjAttrToNumber.toDouble(jsonObject, "cost");
        }
    }

    @Override
    public boolean compare(SqliteDp sqliteDp) {
        if (sqliteDp == null) {
            return false;
        }

        JSONObject jsonObject = JSONObject.parseObject(sqliteDp.getOtherOrDefault());
        return FastJSONObjAttrToNumber.toDouble(jsonObject, "skazsdzfahf") == this.getSkazsdzfahf();
    }
}
