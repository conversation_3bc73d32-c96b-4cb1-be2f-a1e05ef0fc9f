package com.abujlb.dataprobi.bean;

import com.abujlb.dataprobi.bean.dy.jlqc.BiJlqcAvvid;
import com.abujlb.dataprobi.bean.mn.BiSupplier;
import com.abujlb.dataprobi.bean.tgc.BiTgcAiztone;
import com.alibaba.fastjson.JSONObject;

import java.util.List;
import java.util.TreeSet;

/**
 * <AUTHOR>
 * @date 2022/11/10 16:46
 */
public class ThreadLocalObjects {
    private DataprobiBaseMsgBean dataprobiBaseMsgBean;
    private BiInfo biInfo;
    private BiYhdp biYhdp;
    private TreeSet<String> allDates;
    //猫宁匹配规则
    //新增日期2023-7-4
    private JSONObject mnMetaRules;
    private BiSupplier biSupplier;
    private List<BiJlqcAvvid> jlqcAvvidList;
    private List<BiTgcAiztone> tgcAiztoneList;
    private boolean testYh;
    private BiRuleReRunTask biRuleReRunTask;
    // 0 :默认  1：推广花费分摊设置调整重算
    private int msgLx;


    //需要重算的日期
    private TreeSet<String> cs_dates;

    public DataprobiBaseMsgBean getDataprobiBaseMsgBean() {
        return dataprobiBaseMsgBean;
    }

    public void setDataprobiBaseMsgBean(DataprobiBaseMsgBean dataprobiBaseMsgBean) {
        this.dataprobiBaseMsgBean = dataprobiBaseMsgBean;
    }

    public BiInfo getBiInfo() {
        return biInfo;
    }

    public void setBiInfo(BiInfo biInfo) {
        this.biInfo = biInfo;
    }

    public BiYhdp getBiYhdp() {
        return biYhdp;
    }

    public void setBiYhdp(BiYhdp biYhdp) {
        this.biYhdp = biYhdp;
    }

    public TreeSet<String> getAllDates() {
        return allDates;
    }

    public void setAllDates(TreeSet<String> allDates) {
        this.allDates = allDates;
    }

    public JSONObject getMnMetaRules() {
        return mnMetaRules;
    }

    public void setMnMetaRules(JSONObject mnMetaRules) {
        this.mnMetaRules = mnMetaRules;
    }

    public BiSupplier getBiSupplier() {
        return biSupplier;
    }

    public void setBiSupplier(BiSupplier biSupplier) {
        this.biSupplier = biSupplier;
    }

    public boolean isTestYh() {
        return testYh;
    }

    public void setTestYh(boolean testYh) {
        this.testYh = testYh;
    }

    public BiRuleReRunTask getBiRuleReRunTask() {
        return biRuleReRunTask;
    }

    public void setBiRuleReRunTask(BiRuleReRunTask biRuleReRunTask) {
        this.biRuleReRunTask = biRuleReRunTask;
    }

    public List<BiJlqcAvvid> getJlqcAvvidList() {
        return jlqcAvvidList;
    }

    public void setJlqcAvvidList(List<BiJlqcAvvid> jlqcAvvidList) {
        this.jlqcAvvidList = jlqcAvvidList;
    }

    public List<BiTgcAiztone> getTgcAiztoneList() {
        return tgcAiztoneList;
    }

    public void setTgcAiztoneList(List<BiTgcAiztone> tgcAiztoneList) {
        this.tgcAiztoneList = tgcAiztoneList;
    }

    public int getMsgLx() {
        return msgLx;
    }

    public void setMsgLx(int msgLx) {
        this.msgLx = msgLx;
    }

    public TreeSet<String> getCs_dates() {
        return cs_dates;
    }

    public void setCs_dates(TreeSet<String> cs_dates) {
        this.cs_dates = cs_dates;
    }
}
