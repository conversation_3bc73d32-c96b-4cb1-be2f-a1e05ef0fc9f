package com.abujlb.dataprobi.bean;

import com.abujlb.dataprobi.constant.DataprobiConst;
import com.abujlb.dataprobi.threads.BiThreadLocals;
import com.abujlb.dataprobi.util.MathUtil;
import com.alibaba.fastjson.JSONObject;
import org.apache.log4j.Logger;

import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022/5/12 10:03
 */
public class SqliteDp {

    public static final Logger logger = Logger.getLogger(SqliteSp.class);

    //-----------主键---------------//
    protected String qd_userid;
    protected String rq;

    //---------店铺基本信息-------------//
    //店铺名称
    protected String dpmc;

    //------------重要信息--------------//
    //店铺userid
    protected String userid;
    //用户id
    protected int yhid;
    //渠道
    protected String qd;

    //------------不同渠道共有的数据--------------//
    //店铺访客数
    protected int spxgfks;
    //店铺浏览量
    protected int spxglll;
    //商品访客数
    protected int spxgspfks;
    //商品浏览量
    protected int spxgsplll;
    //支付金额
    protected double spxgzfje;
    //支付买家数
    protected int spxgzfmjs;
    //支付转化率
    protected double spxgzfzhl;
    //退款金额
    protected double spxgtkje;
    //支付件数
    protected int spxgzfjs;

    //------------不同渠道的差异性数据字段存入JSON中--------------//
    private String other;

    //--------------------汇总部分---------------------------------//
    //推广花费
    //推广花费  具体计算规则，参考每个渠道的汇总process里的calDpTghf(SqliteDp sqliteDp)方法
    private double tghf;
    //推广花费详情  参考tghf字段
    private String tghfxq;

    public <T extends SqliteDp> SqliteDp(T t) {
        this.setQd_userid(t.getQd_userid());
        this.setRq(t.getRq());
        this.setDpmc(t.getDpmc());
        this.setUserid(t.getUserid());
        this.setYhid(t.getYhid());
        this.setQd(t.getQd());
        this.setSpxgfks(t.getSpxgfks());
        this.setSpxglll(t.getSpxglll());
        this.setSpxgspfks(t.getSpxgspfks());
        this.setSpxgsplll(t.getSpxgsplll());
        this.setSpxgzfje(t.getSpxgzfje());
        this.setSpxgzfmjs(t.getSpxgzfmjs());
        this.setSpxgzfzhl(t.getSpxgzfzhl());
        this.setSpxgtkje(t.getSpxgtkje());
        this.setSpxgzfjs(t.getSpxgzfjs());
        JSONObject tJSONObject = JSONObject.parseObject(DataprobiConst.GSON2.toJson(t));
        this.setOther(tJSONObject.toJSONString());
    }

    public SqliteDp() {

    }

    public String getQd_userid() {
        return qd_userid;
    }

    public void setQd_userid(String qd_userid) {
        this.qd_userid = qd_userid;
    }

    public String getDpmc() {
        return dpmc == null ? "" : dpmc;
    }

    public void setDpmc(String dpmc) {
        this.dpmc = dpmc;
    }

    public String getUserid() {
        return userid;
    }

    public void setUserid(String userid) {
        this.userid = userid;
    }

    public int getYhid() {
        return yhid;
    }

    public void setYhid(int yhid) {
        this.yhid = yhid;
    }

    public String getQd() {
        return qd;
    }

    public void setQd(String qd) {
        this.qd = qd;
    }

    public String getRq() {
        return rq;
    }

    public void setRq(String rq) {
        this.rq = rq;
    }

    public int getSpxgfks() {
        return spxgfks;
    }

    public void setSpxgfks(int spxgfks) {
        this.spxgfks = spxgfks;
    }

    public int getSpxglll() {
        return spxglll;
    }

    public void setSpxglll(int spxglll) {
        this.spxglll = spxglll;
    }

    public int getSpxgspfks() {
        return spxgspfks;
    }

    public void setSpxgspfks(int spxgspfks) {
        this.spxgspfks = spxgspfks;
    }

    public int getSpxgsplll() {
        return spxgsplll;
    }

    public void setSpxgsplll(int spxgsplll) {
        this.spxgsplll = spxgsplll;
    }

    public double getSpxgzfje() {
        return spxgzfje;
    }

    public void setSpxgzfje(double spxgzfje) {
        this.spxgzfje = spxgzfje;
    }

    public int getSpxgzfmjs() {
        return spxgzfmjs;
    }

    public void setSpxgzfmjs(int spxgzfmjs) {
        this.spxgzfmjs = spxgzfmjs;
    }

    public String getOther() {
        return other;
    }

    public String getOtherOrDefault() {
        return other == null ? "{}" : other;
    }

    public double getSpxgzfzhl() {
        return spxgzfzhl;
    }

    public void setSpxgzfzhl(double spxgzfzhl) {
        this.spxgzfzhl = spxgzfzhl;
    }

    public double getSpxgtkje() {
        return spxgtkje;
    }

    public void setSpxgtkje(double spxgtkje) {
        this.spxgtkje = spxgtkje;
    }

    public int getSpxgzfjs() {
        return spxgzfjs;
    }

    public void setSpxgzfjs(int spxgzfjs) {
        this.spxgzfjs = spxgzfjs;
    }

    public void setOther(String other) {
        this.other = other;
    }

    public double getTghf() {
        return tghf;
    }

    public void setTghf(double tghf) {
        this.tghf = tghf;
    }

    public String getTghfxq() {
        return tghfxq == null ? "{}" : tghfxq;
    }

    public void setTghfxq(String tghfxq) {
        this.tghfxq = tghfxq;
    }

    public <T extends SqliteDp> void adapter(T t) {
        if (t == null) {
            return;
        }
        if (t.getDpmc() != null) {
            this.setDpmc(t.getDpmc());
        } else {
            this.setDpmc(BiThreadLocals.getBiYhdp().getDpmc());
        }
        if (t.getSpxgfks() != 0) {
            this.setSpxgfks(t.getSpxgfks());
        }
        if (t.getSpxglll() != 0) {
            this.setSpxglll(t.getSpxglll());
        }
        if (t.getSpxgspfks() != 0) {
            this.setSpxgspfks(t.getSpxgspfks());
        }
        if (t.getSpxgsplll() != 0) {
            this.setSpxgsplll(t.getSpxgsplll());
        }
        if (t.getSpxgzfje() != 0) {
            this.setSpxgzfje(t.getSpxgzfje());
        }
        if (t.getSpxgzfmjs() != 0) {
            this.setSpxgzfmjs(t.getSpxgzfmjs());
        }
        if (t.getSpxgzfzhl() != 0) {
            this.setSpxgzfzhl(t.getSpxgzfzhl());
        }
        if (t.getSpxgtkje() != 0) {
            this.setSpxgtkje(t.getSpxgtkje());
        }
        if (t.getSpxgzfjs() != 0) {
            this.setSpxgzfjs(t.getSpxgzfjs());
        }

        try {
            JSONObject jsonObject = JSONObject.parseObject(this.getOtherOrDefault());
            JSONObject tJSONObject = JSONObject.parseObject(DataprobiConst.GSON2.toJson(t));
            jsonObject.putAll(tJSONObject);
            this.setOther(jsonObject.toJSONString());
        } catch (Exception ignored) {
        }
    }

    public String toInsertSql() {
        return String.format("insert into sycm_dp(" +
                        "'qd_userid','rq','userid','yhid','qd','dpmc','spxgfks','spxglll','spxgspfks','spxgsplll'" +
                        ",'spxgzfje','spxgzfmjs','spxgzfzhl','spxgtkje','spxgzfjs','tghf','tghfxq','other') " +
                        " values('%s','%s','%s','%s','%s','%s','%s',%s,%s,%s,%s,%s,%s,%s,%s,%s,'%s','%s')", this.qd_userid,
                this.rq, this.userid, this.yhid, this.qd, this.dpmc, this.spxgfks, this.spxglll, this.spxgspfks, this.spxgsplll,
                this.spxgzfje, this.spxgzfmjs, this.spxgzfzhl, this.spxgtkje, this.spxgzfjs, this.tghf, this.getTghfxq(), this.getOtherOrDefault());
    }

    public void calTghf() {
        try {
            JSONObject jsonObject = JSONObject.parseObject(this.getOtherOrDefault());
            JSONObject tghfDetail = new JSONObject();
            double tghf = 0D;

            Set<String> keySet = jsonObject.keySet();
            for (String key : keySet) {
                if ((key.endsWith("hf") || key.endsWith("tkyj") || key.endsWith("hfZd")) && jsonObject.getDoubleValue(key) != 0) {
                    tghfDetail.put(key, jsonObject.getDouble(key));
                    tghf = MathUtil.add(tghf, jsonObject.getDouble(key));
                }
            }

            this.setTghf(tghf);
            this.setTghfxq(tghfDetail.toJSONString());
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
    }

    @Override
    public String toString() {
        return DataprobiConst.GSON.toJson(this);
    }
}
