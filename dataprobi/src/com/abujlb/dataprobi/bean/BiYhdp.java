package com.abujlb.dataprobi.bean;

import com.abujlb.dataprobi.constant.DataprobiConst;

/**
 * mysql bi.bi_yhdp
 *
 * <AUTHOR>
 * @date 2022/5/9 9:35
 */
public class BiYhdp {

    private int id;
    //用户id
    private int yhid;
    //渠道 TM TB ALI JD PDD DY KS YMX
    private String qd;
    //店铺唯一标识
    private String userid;
    //店铺名称
    private String dpmc;
    //进入bi系统，开始调用接口的开始日期
    private String ksrq;
    //调用接口的结束日期
    private String jsrq;
    //订单数据处理截至日期
    private String ddcljzrq;
    //生参处理开始日期
    private String sycmclksrq;
    //生意参谋处理截至日期
    private String sycmcljzrq;
    //修改陈达那边的日期
    private String sycmjzrq;
    //修改陈达那边的日期
    private String sycmjzrq2;
    //是否停用  0：正常  1：已停用
    private int sfty;

    private String zdksrq;
    private String zdjsrq;

    //冗余字段 是否需要更新bi_yhdp
    private transient boolean update;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getYhid() {
        return yhid;
    }

    public void setYhid(int yhid) {
        this.yhid = yhid;
    }

    public String getQd() {
        return qd;
    }

    public void setQd(String qd) {
        this.qd = qd;
    }

    public String getUserid() {
        return userid;
    }

    public String getSycmclksrq() {
        return sycmclksrq;
    }

    public void setSycmclksrq(String sycmclksrq) {
        this.sycmclksrq = sycmclksrq;
    }

    public void setUserid(String userid) {
        this.userid = userid;
    }

    public String getDpmc() {
        return dpmc;
    }

    public void setDpmc(String dpmc) {
        this.dpmc = dpmc;
    }

    public String getKsrq() {
        return ksrq;
    }

    public void setKsrq(String ksrq) {
        this.ksrq = ksrq;
    }

    public String getJsrq() {
        return jsrq;
    }

    public void setJsrq(String jsrq) {
        this.jsrq = jsrq;
    }

    public String getDdcljzrq() {
        return ddcljzrq;
    }

    public void setDdcljzrq(String ddcljzrq) {
        this.ddcljzrq = ddcljzrq;
    }

    public String getSycmcljzrq() {
        return sycmcljzrq == null ? "" : sycmcljzrq;
    }

    public void setSycmcljzrq(String sycmcljzrq) {
        this.sycmcljzrq = sycmcljzrq;
    }

    public int getSfty() {
        return sfty;
    }

    public void setSfty(int sfty) {
        this.sfty = sfty;
    }

    public String getSycmjzrq() {
        return sycmjzrq == null ? "" : sycmjzrq;
    }

    public void setSycmjzrq(String sycmjzrq) {
        this.sycmjzrq = sycmjzrq;
    }

    public String getSycmjzrq2() {
        return sycmjzrq2 == null ? "" : sycmjzrq2;
    }

    public void setSycmjzrq2(String sycmjzrq2) {
        this.sycmjzrq2 = sycmjzrq2;
    }

    public boolean isUpdate() {
        return update;
    }

    public void setUpdate(boolean update) {
        this.update = update;
    }

    public String getZdksrq() {
        return zdksrq;
    }

    public void setZdksrq(String zdksrq) {
        this.zdksrq = zdksrq;
    }

    public String getZdjsrq() {
        return zdjsrq;
    }

    public void setZdjsrq(String zdjsrq) {
        this.zdjsrq = zdjsrq;
    }

    @Override
    public String toString() {
        return DataprobiConst.GSON.toJson(this);
    }

}
