package com.abujlb.dataprobi.bean;

/**
 * <AUTHOR>
 * @date 2023-09-04
 */
public class BiInfoOther {

    //主键id
    private long id;
    //对应bi_info表的if
    private long infoId;
    //对应bi_info表的userid
    private long infoUserid;
    //对应bi_info表的yhid
    private long infoYhid;
    //对应bi_info表的jlbdpid
    private long infoJlbdpid;
    //对应bi_info表的qd
    private String infoQd;
    //是否停用 0正常 1停用
    private long sfty;
    //第三方userid
    private long userid;
    //第三方qd
    private String qd;
    //第三方jlbdpid
    private String jlbdpid;
    //店铺名称
    private String dpmc;
    //采集的rqjson
    private String rqjson;
    //采集开始日期
    private String cjksrq;
    //采集截止日期
    private String cjjzrq;
    //规则
    private String rule;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public long getInfoId() {
        return infoId;
    }

    public void setInfoId(long infoId) {
        this.infoId = infoId;
    }

    public long getInfoUserid() {
        return infoUserid;
    }

    public void setInfoUserid(long infoUserid) {
        this.infoUserid = infoUserid;
    }

    public long getInfoYhid() {
        return infoYhid;
    }

    public void setInfoYhid(long infoYhid) {
        this.infoYhid = infoYhid;
    }

    public long getInfoJlbdpid() {
        return infoJlbdpid;
    }

    public void setInfoJlbdpid(long infoJlbdpid) {
        this.infoJlbdpid = infoJlbdpid;
    }

    public String getInfoQd() {
        return infoQd;
    }

    public void setInfoQd(String infoQd) {
        this.infoQd = infoQd;
    }

    public long getUserid() {
        return userid;
    }

    public void setUserid(long userid) {
        this.userid = userid;
    }

    public String getQd() {
        return qd;
    }

    public void setQd(String qd) {
        this.qd = qd;
    }

    public String getJlbdpid() {
        return jlbdpid;
    }

    public void setJlbdpid(String jlbdpid) {
        this.jlbdpid = jlbdpid;
    }

    public String getDpmc() {
        return dpmc;
    }

    public void setDpmc(String dpmc) {
        this.dpmc = dpmc;
    }

    public String getRqjson() {
        return rqjson;
    }

    public void setRqjson(String rqjson) {
        this.rqjson = rqjson;
    }

    public String getCjksrq() {
        return cjksrq;
    }

    public void setCjksrq(String cjksrq) {
        this.cjksrq = cjksrq;
    }

    public String getCjjzrq() {
        return cjjzrq;
    }

    public void setCjjzrq(String cjjzrq) {
        this.cjjzrq = cjjzrq;
    }

    public long getSfty() {
        return sfty;
    }

    public void setSfty(long sfty) {
        this.sfty = sfty;
    }

    public String getRule() {
        return rule;
    }

    public void setRule(String rule) {
        this.rule = rule;
    }
}
