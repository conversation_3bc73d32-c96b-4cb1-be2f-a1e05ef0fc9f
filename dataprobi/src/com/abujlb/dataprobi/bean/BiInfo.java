package com.abujlb.dataprobi.bean;

import com.abujlb.dataprobi.util.DataprobiDateUtil;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022/6/23
 */
public class BiInfo {

    private int id;
    private String userid;
    private String qd;
    private transient String start;
    private int yhid;
    private String rqjson;
    private String dpmc;
    //处理日期JSON
    private String clrqjson;
    //店铺配置JSON
    private String dpconfigjson;
    //
    private int splb;

    private int allownull; // 是否允许为空 0表示不为空,其他表示为空

    private String sycmcljzrq;


    public BiInfo() {

    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getUserid() {
        return userid;
    }

    public void setUserid(String userid) {
        this.userid = userid;
    }

    public String getQd() {
        return qd;
    }

    public void setQd(String qd) {
        this.qd = qd;
    }

    public String getStart() {
        return start;
    }

    public void setStart(String start) {
        this.start = start;
    }

    public String getClrqjson() {
        return clrqjson;
    }

    public void setClrqjson(String clrqjson) {
        this.clrqjson = clrqjson;
    }

    public String getDpconfigjson() {
        return dpconfigjson;
    }

    public void setDpconfigjson(String dpconfigjson) {
        this.dpconfigjson = dpconfigjson;
    }

    public int getSplb() {
        return splb;
    }

    public void setSplb(int splb) {
        this.splb = splb;
    }

    public String getRqjson() {
        return rqjson;
    }

    public void setRqjson(String rqjson) {
        this.rqjson = rqjson;
    }

    public int getYhid() {
        return yhid;
    }

    public void setYhid(int yhid) {
        this.yhid = yhid;
    }

    public String getDpmc() {
        return dpmc;
    }

    public void setDpmc(String dpmc) {
        this.dpmc = dpmc;
    }

    public int getAllownull() {
        return allownull;
    }

    public void setAllownull(int allownull) {
        this.allownull = allownull;
    }

    public String getSycmcljzrq() {
        return sycmcljzrq;
    }

    public void setSycmcljzrq(String sycmcljzrq) {
        this.sycmcljzrq = sycmcljzrq;
    }

    public boolean cjrqAllYesterday() {
        final List<String> ignoreKyes = Arrays.asList("tbxjhb", "tbxjhbdp");
        if (StringUtils.isBlank(rqjson)) {
            return false;
        }

        try {
            JSONObject jsonObject = JSONObject.parseObject(rqjson);
            Set<String> keySet = jsonObject.keySet();
            String min = DataprobiDateUtil.getLastday();
            for (String key : keySet) {
                if (ignoreKyes.contains(key)) {
                    continue;
                }
                if (min.compareTo(jsonObject.getString(key)) > 0) {
                    min = jsonObject.getString(key);
                }
            }

            return min.equals(DataprobiDateUtil.getLastday());
        } catch (Exception e) {
            return false;
        }
    }
}
