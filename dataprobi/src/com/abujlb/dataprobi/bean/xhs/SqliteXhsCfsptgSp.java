package com.abujlb.dataprobi.bean.xhs;

import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.SqliteSp;
import com.abujlb.dataprobi.interfaces.Plusable;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.abujlb.dataprobi.util.MathUtil;
import com.alibaba.fastjson.JSONObject;

public class SqliteXhsCfsptgSp extends AbstractSqliteSp implements Plusable<SqliteXhsCfsptgSp>{

    // 商品日常销售花费（sprcxs_fee）
    private double sprcxshf;
    // 商品笔记加热花费（spbjjr_fee）
    private double spbjjrhf;
    // 商品新客转化花费（spxkzh_fee）
    private double spxkzhhf;

    public double getSprcxshf() {
        return sprcxshf;
    }

    public void setSprcxshf(double sprcxshf) {
        this.sprcxshf = sprcxshf;
    }

    public double getSpbjjrhf() {
        return spbjjrhf;
    }

    public void setSpbjjrhf(double spbjjrhf) {
        this.spbjjrhf = spbjjrhf;
    }

    public double getSpxkzhhf() {
        return spxkzhhf;
    }

    public void setSpxkzhhf(double spxkzhhf) {
        this.spxkzhhf = spxkzhhf;
    }

    @Override
    public void setValues(JSONObject jsonObject) {
        if (jsonObject != null) {
            // 设置宝贝ID (商品ID)
            this.bbid = FastJSONObjAttrToNumber.toString(jsonObject, "spuId");

            // 设置各类花费
            this.sprcxshf = FastJSONObjAttrToNumber.toDouble(jsonObject, "sprcxs_fee");
            this.spxkzhhf = FastJSONObjAttrToNumber.toDouble(jsonObject, "spxkzh_fee");
            this.spbjjrhf = FastJSONObjAttrToNumber.toDouble(jsonObject, "spbjjr_fee");
        }
    }

    @Override
    public boolean compare(SqliteSp sqliteSp) {
        if (sqliteSp == null) {
            return false;
        }

        // 解析sqliteSp的附加数据
        JSONObject jsonObject = JSONObject.parseObject(sqliteSp.getOtherOrDefault());

        return this.sprcxshf == FastJSONObjAttrToNumber.toDouble(jsonObject, "sprcxs_fee") &&
                this.spbjjrhf == FastJSONObjAttrToNumber.toDouble(jsonObject, "spbjjr_fee") &&
                this.spxkzhhf == FastJSONObjAttrToNumber.toDouble(jsonObject, "spxkzh_fee");
    }

    @Override
    public void plus(SqliteXhsCfsptgSp sqliteXhsCfsptgSp) {
            this.sprcxshf = MathUtil.add(this.sprcxshf, sqliteXhsCfsptgSp.getSprcxshf());
            this.spbjjrhf = MathUtil.add(this.spbjjrhf, sqliteXhsCfsptgSp.getSpbjjrhf());
            this.spxkzhhf = MathUtil.add(this.spxkzhhf, sqliteXhsCfsptgSp.getSpxkzhhf());
    }
}