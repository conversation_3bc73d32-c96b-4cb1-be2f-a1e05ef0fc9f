package com.abujlb.dataprobi.bean.xhs;

import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.SqliteSp;
import com.abujlb.dataprobi.interfaces.Plusable;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.abujlb.dataprobi.util.MathUtil;
import com.alibaba.fastjson.JSONObject;

public class SqliteXhsJgptzbSp extends AbstractSqliteSp implements Plusable<SqliteXhsJgptzbSp> {
    // 直播花费
    private double jgzbhf ;

    public double getJgzbhf() {
        return jgzbhf;
    }

    public void setJgzbhf(double jgzbhf) {
        this.jgzbhf = jgzbhf;
    }

    @Override
    public void setValues(JSONObject jsonObject) {
            this.bbid = FastJSONObjAttrToNumber.toString(jsonObject,"goodsid");
            this.jgzbhf  = FastJSONObjAttrToNumber.toDouble(jsonObject,"fee");
    }

    @Override
    public boolean compare(SqliteSp sqliteSp) {
        if (sqliteSp == null) {
            return false;
        }

        JSONObject jsonObject = JSONObject.parseObject(sqliteSp.getOtherOrDefault());
        return this.getJgzbhf() == FastJSONObjAttrToNumber.toDouble(jsonObject, "jgzbhf");

    }

    @Override
    public void plus(SqliteXhsJgptzbSp temp) {
        this.jgzbhf = MathUtil.add(this.jgzbhf, temp.getJgzbhf());
    }
}
