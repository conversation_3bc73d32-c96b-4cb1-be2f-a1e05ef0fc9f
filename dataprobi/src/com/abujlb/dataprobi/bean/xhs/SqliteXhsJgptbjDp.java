package com.abujlb.dataprobi.bean.xhs;


import com.abujlb.dataprobi.bean.AbstractSqliteDp;
import com.abujlb.dataprobi.bean.SqliteDp;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 * @date 2024/4/29
 */
public class SqliteXhsJgptbjDp extends AbstractSqliteDp {

    //聚光平台笔记花费
    private double jgbjhf;

    public SqliteXhsJgptbjDp() {
    }

    public double getJgbjhf() {
        return jgbjhf;
    }

    public void setJgbjhf(double jgbjhf) {
        this.jgbjhf = jgbjhf;
    }

    @Override
    public <T extends AbstractSqliteDp> void setValues(String[] tableStoreColumnValues) {
        String value0 = tableStoreColumnValues[0];
        if (value0 != null) {
            JSONObject jsonObject = JSONObject.parseObject(value0);
            this.jgbjhf = FastJSONObjAttrToNumber.toDouble(jsonObject, "jgbjzxf");
        }
    }

    @Override
    public boolean compare(SqliteDp sqliteDp) {
        if (sqliteDp == null) {
            return false;
        }

        JSONObject jsonObject = JSONObject.parseObject(sqliteDp.getOtherOrDefault());
        return FastJSONObjAttrToNumber.toDouble(jsonObject, "jgbjhf") == this.getJgbjhf();
    }

    @Override
    public <T extends AbstractSqliteDp> void plus(T t) {
        if (t instanceof SqliteXhsJgptbjDp) {
            SqliteXhsJgptbjDp other = (SqliteXhsJgptbjDp) t;
            // 累加各类费用值
            this.jgbjhf += other.getJgbjhf();
        }
    }
}
