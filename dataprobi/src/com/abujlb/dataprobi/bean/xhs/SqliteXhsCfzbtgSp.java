package com.abujlb.dataprobi.bean.xhs;

import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.SqliteSp;
import com.abujlb.dataprobi.interfaces.Plusable;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.abujlb.dataprobi.util.MathUtil;
import com.alibaba.fastjson.JSONObject;

public class SqliteXhsCfzbtgSp extends AbstractSqliteSp implements Plusable<SqliteXhsCfzbtgSp>{

    // 商品日常销售笔记花费（zbrcxs_fee）
    private double zbrcxshf;
    // 商品新客转化花费（zbxkzh_fee）
    private double zbxkzhhf;

    public double getZbrcxshf() {
        return zbrcxshf;
    }

    public void setZbrcxshf(double zbrcxshf) {
        this.zbrcxshf = zbrcxshf;
    }

    public double getZbxkzhhf() {
        return zbxkzhhf;
    }

    public void setZbxkzhhf(double zbxkzhhf) {
        this.zbxkzhhf = zbxkzhhf;
    }

    @Override
    public void setValues(JSONObject jsonObject) {
        if (jsonObject != null) {
            // 设置宝贝ID (商品ID)
            this.bbid = FastJSONObjAttrToNumber.toString(jsonObject, "spuId");

            // 设置各类花费
            this.zbrcxshf = FastJSONObjAttrToNumber.toDouble(jsonObject, "zbrcxs_fee");
            // 设置各类花费
            this.zbxkzhhf = FastJSONObjAttrToNumber.toDouble(jsonObject, "zbxkzh_fee");
        }
    }

    @Override
    public boolean compare(SqliteSp sqliteSp) {
        if (sqliteSp == null) {
            return false;
        }

        // 解析sqliteSp的附加数据
        JSONObject jsonObject = JSONObject.parseObject(sqliteSp.getOtherOrDefault());

        return this.zbrcxshf == FastJSONObjAttrToNumber.toDouble(jsonObject, "zbrcxs_fee")
                && this.zbxkzhhf == FastJSONObjAttrToNumber.toDouble(jsonObject, "zbxkzh_fee");
    }

    @Override
    public void plus(SqliteXhsCfzbtgSp sqliteXhsCfsptgSp) {
            this.zbrcxshf = MathUtil.add(this.zbrcxshf, sqliteXhsCfsptgSp.getZbrcxshf());
            this.zbxkzhhf = MathUtil.add(this.zbxkzhhf, sqliteXhsCfsptgSp.getZbxkzhhf());
    }
}