package com.abujlb.dataprobi.bean.xhs;

import com.abujlb.dataprobi.bean.AbstractSqliteDp;
import com.abujlb.dataprobi.bean.SqliteDp;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.alibaba.fastjson.JSONObject;

public class SqliteXhsCfzbtgDp extends AbstractSqliteDp {
    // 类字段
    private double zbzbyghf;  // 乘风平台推广花费(直播直播预告花费)
    private double zbrcxshf;  // 乘风平台推广花费(直播日常销售花费)
    private double zbxkzhhf;  // 商品新客转化花费（zbxkzh_fee）

    // getter和setter方法

    public double getZbzbyghf() {
        return zbzbyghf;
    }

    public void setZbzbyghf(double zbzbyghf) {
        this.zbzbyghf = zbzbyghf;
    }

    public double getZbrcxshf() {
        return zbrcxshf;
    }

    public void setZbrcxshf(double zbrcxshf) {
        this.zbrcxshf = zbrcxshf;
    }

    public double getZbxkzhhf() {
        return zbxkzhhf;
    }

    public void setZbxkzhhf(double zbxkzhhf) {
        this.zbxkzhhf = zbxkzhhf;
    }

    @Override
    public <T extends AbstractSqliteDp> void setValues(String[] tableStoreColumnValues) {
        String value0 = tableStoreColumnValues[0];
        if (value0 != null) {
            JSONObject jsonObject = JSONObject.parseObject(value0);

            // 从JSON对象中提取费用数据并设置到对应字段
            this.zbzbyghf = FastJSONObjAttrToNumber.toDouble(jsonObject, "zbzbyg_fee");
            this.zbrcxshf = FastJSONObjAttrToNumber.toDouble(jsonObject, "zbrcxs_fee");
            this.zbxkzhhf = FastJSONObjAttrToNumber.toDouble(jsonObject, "zbxkzh_fee");
        }
    }

    @Override
    public boolean compare(SqliteDp sqliteDp) {
        if (sqliteDp == null) {
            return false;
        }

        // 从getOtherOrDefault()获取JSON对象
        JSONObject jsonObject = JSONObject.parseObject(sqliteDp.getOtherOrDefault());

        // 比较JSON对象中的值与当前对象的字段值是否相等
        return FastJSONObjAttrToNumber.toDouble(jsonObject, "zbzbyghf") == this.zbzbyghf &&
                FastJSONObjAttrToNumber.toDouble(jsonObject, "zbrcxshf") == this.zbrcxshf &&
                FastJSONObjAttrToNumber.toDouble(jsonObject, "zbxkzhhf") == this.zbxkzhhf;
    }

    @Override
    public <T extends AbstractSqliteDp> void plus(T t) {
        if (t instanceof SqliteXhsCfzbtgDp) {
            SqliteXhsCfzbtgDp other = (SqliteXhsCfzbtgDp) t;
            // 累加各类费用值
            this.zbzbyghf += other.getZbzbyghf();
            this.zbrcxshf += other.getZbrcxshf();
            this.zbxkzhhf += other.getZbxkzhhf();
        }
    }
}