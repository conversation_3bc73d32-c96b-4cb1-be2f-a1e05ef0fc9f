package com.abujlb.dataprobi.bean.xhs;


import com.abujlb.dataprobi.bean.AbstractSqliteDp;
import com.abujlb.dataprobi.bean.SqliteDp;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 * @date 2024/4/29
 */
public class SqliteXhsSpxgDp extends AbstractSqliteDp {

    //支付订单数
    private  int spxgzfdds;

    public SqliteXhsSpxgDp() {
    }

    public int getSpxgzfdds() {
        return spxgzfdds;
    }

    public void setSpxgzfdds(int spxgzfdds) {
        this.spxgzfdds = spxgzfdds;
    }


    @Override
    public <T extends AbstractSqliteDp> void setValues(String[] tableStoreColumnValues) {
        String value0 = tableStoreColumnValues[0];
        if (value0 != null) {
            JSONObject jsonObject = JSONObject.parseObject(value0);
            this.spxgzfdds = FastJSONObjAttrToNumber.toInt(jsonObject, "zfdds");
            this.spxgzfje = FastJSONObjAttrToNumber.toDouble(jsonObject, "zfje");
            this.spxgzfmjs = FastJSONObjAttrToNumber.toInt(jsonObject, "zfddmjys");
            this.spxgzfzhl = FastJSONObjAttrToNumber.toDouble(jsonObject, "zfzhl");
            this.spxgtkje = FastJSONObjAttrToNumber.toDouble(jsonObject, "cgtkje");
            this.spxgspfks = FastJSONObjAttrToNumber.toInt(jsonObject, "spfks");
        }
    }


    @Override
    public boolean compare(SqliteDp sqliteDp) {
        if (sqliteDp == null) {
            return false;
        }

        JSONObject jsonObject = JSONObject.parseObject(sqliteDp.getOtherOrDefault());
        return FastJSONObjAttrToNumber.toInt(jsonObject, "spxgzfdds") == this.getSpxgzfdds()
                && sqliteDp.getSpxgtkje() == this.getSpxgtkje()
                && sqliteDp.getSpxgzfmjs() == this.getSpxgzfmjs()
                && sqliteDp.getSpxgzfzhl() == this.getSpxgzfzhl()
                && sqliteDp.getSpxgspfks() == this.getSpxgspfks()
                && sqliteDp.getSpxgzfje() == this.getSpxgzfje();
    }
}
