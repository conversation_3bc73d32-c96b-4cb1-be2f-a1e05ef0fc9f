package com.abujlb.dataprobi.bean.xhs;


import com.abujlb.dataprobi.bean.AbstractSqliteDp;
import com.abujlb.dataprobi.bean.SqliteDp;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 * @date 2024/4/29
 */
public class SqliteXhsQfzbDp extends AbstractSqliteDp {

    // 千帆直播-花费
    private double qfzbhf;

    // 千帆直播-展现量
    private int qfzbzxl;

    // 千帆直播-点击量
    private int qfzbdjl;

    //千帆直播-直播间有效观看次数
    private int qfzbjyxgkcs;

    //千帆直播-直播间支付金额
    private double qfzbjzfje;

    public double getQfzbhf() {
        return qfzbhf;
    }

    public void setQfzbhf(double qfzbhf) {
        this.qfzbhf = qfzbhf;
    }

    public int getQfzbzxl() {
        return qfzbzxl;
    }

    public void setQfzbzxl(int qfzbzxl) {
        this.qfzbzxl = qfzbzxl;
    }

    public int getQfzbdjl() {
        return qfzbdjl;
    }

    public void setQfzbdjl(int qfzbdjl) {
        this.qfzbdjl = qfzbdjl;
    }

    public int getQfzbjyxgkcs() {
        return qfzbjyxgkcs;
    }

    public void setQfzbjyxgkcs(int qfzbjyxgkcs) {
        this.qfzbjyxgkcs = qfzbjyxgkcs;
    }

    public double getQfzbjzfje() {
        return qfzbjzfje;
    }

    public void setQfzbjzfje(double qfzbjzfje) {
        this.qfzbjzfje = qfzbjzfje;
    }

    @Override
    public <T extends AbstractSqliteDp> void setValues(String[] tableStoreColumnValues) {
        String value0 = tableStoreColumnValues[0];
        if (value0 != null) {
            JSONObject jsonObject = JSONObject.parseObject(value0);
            this.qfzbhf = FastJSONObjAttrToNumber.toDouble(jsonObject, "fee");
            this.qfzbzxl = FastJSONObjAttrToNumber.toInt(jsonObject, "impression");
            this.qfzbdjl = FastJSONObjAttrToNumber.toInt(jsonObject, "click");
            this.qfzbjyxgkcs = FastJSONObjAttrToNumber.toInt(jsonObject, "clk_live5s_entry_pv");
            this.qfzbjzfje = FastJSONObjAttrToNumber.toDouble(jsonObject, "clk_live_room_rgmv");
        }
    }


    @Override
    public boolean compare(SqliteDp sqliteDp) {
        if (sqliteDp == null) {
            return false;
        }

        JSONObject jsonObject = JSONObject.parseObject(sqliteDp.getOtherOrDefault());

        return FastJSONObjAttrToNumber.toDouble(jsonObject, "qfzbhf") == this.getQfzbhf()
                && FastJSONObjAttrToNumber.toInt(jsonObject, "qfzbzxl") == this.getQfzbzxl()
                && FastJSONObjAttrToNumber.toInt(jsonObject, "qfzbdjl") == this.getQfzbdjl()
                && FastJSONObjAttrToNumber.toInt(jsonObject, "qfzbjyxgkcs") == this.getQfzbjyxgkcs()
                && FastJSONObjAttrToNumber.toDouble(jsonObject, "qfzbjzfje") == this.getQfzbjzfje();
    }
}
