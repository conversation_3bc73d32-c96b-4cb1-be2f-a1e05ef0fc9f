package com.abujlb.dataprobi.bean.xhs;

import com.abujlb.dataprobi.bean.AbstractSqliteDp;
import com.abujlb.dataprobi.bean.SqliteDp;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.alibaba.fastjson.JSONObject;

public class SqliteXhsCfsptgDp extends AbstractSqliteDp {
    // 类字段
    private double sprcxshf;  // 乘风平台推广花费(商品日常销售花费)
    private double spbjjrhf;  // 乘风平台推广花费(商品笔记加热花费)
    private double spxkzhhf;  // 乘风平台推广花费(商品新客转化花费)
    // getter和setter方法
    public double getSprcxshf() {
        return sprcxshf;
    }

    public void setSprcxshf(double sprcxshf) {
        this.sprcxshf = sprcxshf;
    }

    public double getSpbjjrhf() {
        return spbjjrhf;
    }

    public void setSpbjjrhf(double spbjjrhf) {
        this.spbjjrhf = spbjjrhf;
    }

    public double getSpxkzhhf() {
        return spxkzhhf;
    }

    public void setSpxkzhhf(double spxkzhhf) {
        this.spxkzhhf = spxkzhhf;
    }

    @Override
    public <T extends AbstractSqliteDp> void setValues(String[] tableStoreColumnValues) {
        String value0 = tableStoreColumnValues[0];
        if (value0 != null) {
            JSONObject jsonObject = JSONObject.parseObject(value0);

            // 从JSON对象中提取费用数据并设置到对应字段
            this.sprcxshf = FastJSONObjAttrToNumber.toDouble(jsonObject, "sprcxs_fee");
            this.spbjjrhf = FastJSONObjAttrToNumber.toDouble(jsonObject, "spbjjr_fee");
            this.spxkzhhf = FastJSONObjAttrToNumber.toDouble(jsonObject, "spxkzh_fee");
        }
    }

    @Override
    public boolean compare(SqliteDp sqliteDp) {
        if (sqliteDp == null) {
            return false;
        }

        // 从getOtherOrDefault()获取JSON对象
        JSONObject jsonObject = JSONObject.parseObject(sqliteDp.getOtherOrDefault());

        // 比较JSON对象中的值与当前对象的字段值是否相等
        return FastJSONObjAttrToNumber.toDouble(jsonObject, "sprcxshf") == this.sprcxshf &&
                FastJSONObjAttrToNumber.toDouble(jsonObject, "spbjjrhf") == this.spbjjrhf &&
                FastJSONObjAttrToNumber.toDouble(jsonObject, "spxkzhhf") == this.spxkzhhf;
    }


    // 在 SqliteXhsCfsptgDp 类中实现已有的plus方法
    @Override
    public <T extends AbstractSqliteDp> void plus(T t) {
        if (t instanceof SqliteXhsCfsptgDp) {
            SqliteXhsCfsptgDp other = (SqliteXhsCfsptgDp) t;
            // 累加各类费用值
            this.sprcxshf += other.getSprcxshf();  // 累加商品日常销售花费
            this.spbjjrhf += other.getSpbjjrhf();  // 累加商品笔记加热花费
            this.spxkzhhf += other.getSpxkzhhf();  // 累加商品新客转化花费
        }
    }
}