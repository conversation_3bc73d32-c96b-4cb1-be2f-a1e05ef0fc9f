package com.abujlb.dataprobi.bean.xhs;

import com.abujlb.dataprobi.annotations.ClrqIgnore;
import com.abujlb.dataprobi.annotations.SumIgnore;
import com.abujlb.dataprobi.annotations.UpdateSycmcljzrqIgnore;
import com.abujlb.dataprobi.bean.DataprobiBaseMsgBean;
import com.alibaba.fastjson.JSON;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/26
 */
public class DataprobiXhsMsg extends DataprobiBaseMsgBean {

    //商品效果
    private List<String> spxg;
    //商品效果-店铺维度
    private List<String> spxgdp;
    //流量数据店铺
    private List<String> llsjdp;
    //千帆笔记营销
    private List<String> qfbjyx;
    //千帆直播营销店铺
    private List<String> qfzbyxdp;
    //聚光平台笔记
    @UpdateSycmcljzrqIgnore
    private List<String> jgptbjtg;
    //聚光平台笔记推广店铺
    @UpdateSycmcljzrqIgnore
    private List<String> jgptbjtgdp;
    //聚光平台直播分析店铺
    @UpdateSycmcljzrqIgnore
    private List<String> jgptzbfxdp;

    //小红书乘风-商品推广
    @UpdateSycmcljzrqIgnore
    private List<String> cfsptg;

    //小红书乘风-商品推广店铺
    @UpdateSycmcljzrqIgnore
    private List<String> cfsptgdp;

    //小红书乘风-直播推广店铺
    @UpdateSycmcljzrqIgnore
    private List<String> cfzbtgdp;

    //小红书乘风-直播推广
    @UpdateSycmcljzrqIgnore
    private List<String> cfzbtg;

    //千帆推广分析
    @UpdateSycmcljzrqIgnore
    @SumIgnore
    @ClrqIgnore
    private List<String> qftgfx;
    //聚光推广分析
    @UpdateSycmcljzrqIgnore
    @SumIgnore
    @ClrqIgnore
    private List<String> jgtgfx;

    //店铺金额
    @UpdateSycmcljzrqIgnore
    @ClrqIgnore
    @SumIgnore
    private List<String> money;

    //商家体验分
    @UpdateSycmcljzrqIgnore
    @ClrqIgnore
    @SumIgnore
    private List<String> xhssjtyf;

    //小红书客服数据
    @UpdateSycmcljzrqIgnore
    @ClrqIgnore
    @SumIgnore
    private List<String> kfperformance;

    //小红书客服分析
    @UpdateSycmcljzrqIgnore
    @ClrqIgnore
    @SumIgnore
    private List<String> kfanalysis;

    //蒲公英-笔记报告
    @UpdateSycmcljzrqIgnore
    @SumIgnore
    @ClrqIgnore
    private List<String> pgybjbg;

    //蒲公英-笔记报告店铺
    @UpdateSycmcljzrqIgnore
    @SumIgnore
    @ClrqIgnore
    private List<String> pgybjbgdp;

    //蒲公英-达人佣金
    @UpdateSycmcljzrqIgnore
    @SumIgnore
    @ClrqIgnore
    private List<String> pgydryj;


    public List<String> getXhssjtyf() {
        return xhssjtyf;
    }

    public void setXhssjtyf(List<String> xhssjtyf) {
        this.xhssjtyf = xhssjtyf;
    }

    public List<String> getKfperformance() {
        return kfperformance;
    }

    public void setKfperformance(List<String> kfperformance) {
        this.kfperformance = kfperformance;
    }

    public List<String> getKfanalysis() {
        return kfanalysis;
    }

    public void setKfanalysis(List<String> kfanalysis) {
        this.kfanalysis = kfanalysis;
    }

    public List<String> getMoney() {
        return money;
    }

    public void setMoney(List<String> money) {
        this.money = money;
    }

    public List<String> getSpxg() {
        return spxg;
    }

    public void setSpxg(List<String> spxg) {
        this.spxg = spxg;
    }

    public List<String> getSpxgdp() {
        return spxgdp;
    }

    public void setSpxgdp(List<String> spxgdp) {
        this.spxgdp = spxgdp;
    }

    public List<String> getLlsjdp() {
        return llsjdp;
    }

    public void setLlsjdp(List<String> llsjdp) {
        this.llsjdp = llsjdp;
    }

    public List<String> getQfbjyx() {
        return qfbjyx;
    }

    public void setQfbjyx(List<String> qfbjyx) {
        this.qfbjyx = qfbjyx;
    }

    public List<String> getQfzbyxdp() {
        return qfzbyxdp;
    }

    public void setQfzbyxdp(List<String> qfzbyxdp) {
        this.qfzbyxdp = qfzbyxdp;
    }

    public List<String> getJgptbjtg() {
        return jgptbjtg;
    }

    public void setJgptbjtg(List<String> jgptbjtg) {
        this.jgptbjtg = jgptbjtg;
    }

    public List<String> getJgptzbfxdp() {
        return jgptzbfxdp;
    }

    public void setJgptzbfxdp(List<String> jgptzbfxdp) {
        this.jgptzbfxdp = jgptzbfxdp;
    }

    public List<String> getQftgfx() {
        return qftgfx;
    }

    public void setQftgfx(List<String> qftgfx) {
        this.qftgfx = qftgfx;
    }

    public List<String> getJgtgfx() {
        return jgtgfx;
    }

    public void setJgtgfx(List<String> jgtgfx) {
        this.jgtgfx = jgtgfx;
    }

    public List<String> getJgptbjtgdp() {
        return jgptbjtgdp;
    }

    public void setJgptbjtgdp(List<String> jgptbjtgdp) {
        this.jgptbjtgdp = jgptbjtgdp;
    }

    public List<String> getCfsptg() {
        return cfsptg;
    }

    public void setCfsptg(List<String> cfsptg) {
        this.cfsptg = cfsptg;
    }

    public List<String> getCfsptgdp() {
        return cfsptgdp;
    }

    public void setCfsptgdp(List<String> cfsptgdp) {
        this.cfsptgdp = cfsptgdp;
    }

    public List<String> getCfzbtgdp() {
        return cfzbtgdp;
    }

    public void setCfzbtgdp(List<String> cfzbtgdp) {
        this.cfzbtgdp = cfzbtgdp;
    }

    public List<String> getCfzbtg() {
        return cfzbtg;
    }

    public void setCfzbtg(List<String> cfzbtg) {
        this.cfzbtg = cfzbtg;
    }

    public List<String> getPgybjbg() {
        return pgybjbg;
    }

    public void setPgybjbg(List<String> pgybjbg) {
        this.pgybjbg = pgybjbg;
    }

    public List<String> getPgybjbgdp() {
        return pgybjbgdp;
    }

    public void setPgybjbgdp(List<String> pgybjbgdp) {
        this.pgybjbgdp = pgybjbgdp;
    }

    public List<String> getPgydryj() {
        return pgydryj;
    }

    public void setPgydryj(List<String> pgydryj) {
        this.pgydryj = pgydryj;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }

    public void fillNullList() throws InvocationTargetException, IllegalAccessException {
        Method[] methods = this.getClass().getMethods();
        for (Method method : methods) {
            if (method.getName().startsWith("get")) {
                Object value = method.invoke(this);
                if (value == null) {
                    Method setMethod = null;
                    try {
                        setMethod = this.getClass().getMethod("s" + method.getName().substring(1), List.class);
                        setMethod.invoke(this, new ArrayList<>());
                    } catch (NoSuchMethodException ignored) {
                    }
                }
            }
        }
    }
}
