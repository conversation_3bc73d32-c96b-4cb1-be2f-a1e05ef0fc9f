package com.abujlb.dataprobi.bean.xhs;


import com.abujlb.dataprobi.bean.AbstractSqliteDp;
import com.abujlb.dataprobi.bean.SqliteDp;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 * @date 2024/4/29
 */
public class SqliteXhsPgyhfDp extends AbstractSqliteDp {

    private double pgybzbjje;//蒲公英-博主报价金额
    private double pgyspfwfhf;//蒲公英-商品服务费花费

    public SqliteXhsPgyhfDp() {
    }

    public double getPgybzbjje() {
        return pgybzbjje;
    }

    public void setPgybzbjje(double pgybzbjje) {
        this.pgybzbjje = pgybzbjje;
    }

    public double getPgyspfwfhf() {
        return pgyspfwfhf;
    }

    public void setPgyspfwfhf(double pgyspfwfhf) {
        this.pgyspfwfhf = pgyspfwfhf;
    }

    @Override
    public <T extends AbstractSqliteDp> void setValues(String[] tableStoreColumnValues) {
        String value0 = tableStoreColumnValues[0];
        if (value0 != null) {
            JSONObject jsonObject = JSONObject.parseObject(value0);
            this.pgybzbjje = FastJSONObjAttrToNumber.toInt(jsonObject, "kolPrice");
            this.pgyspfwfhf = FastJSONObjAttrToNumber.toDouble(jsonObject, "totalPlatformPrice");
        }
    }


    @Override
    public boolean compare(SqliteDp sqliteDp) {
        if (sqliteDp == null) {
            return false;
        }

        JSONObject jsonObject = JSONObject.parseObject(sqliteDp.getOtherOrDefault());
        return FastJSONObjAttrToNumber.toInt(jsonObject, "pgybzbjje") == this.getPgybzbjje()
                && FastJSONObjAttrToNumber.toInt(jsonObject, "pgyspfwfhf") == this.getPgyspfwfhf();
    }
}
