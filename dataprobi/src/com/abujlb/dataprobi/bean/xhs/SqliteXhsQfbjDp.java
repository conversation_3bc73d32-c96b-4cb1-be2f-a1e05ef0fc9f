package com.abujlb.dataprobi.bean.xhs;


import com.abujlb.dataprobi.bean.AbstractSqliteDp;
import com.abujlb.dataprobi.bean.SqliteDp;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 * @date 2024/4/29
 */
public class SqliteXhsQfbjDp extends AbstractSqliteDp {

    //千帆笔记花费
    private double qfbjhf;

    public double getQfbjhf() {
        return qfbjhf;
    }

    public void setQfbjhf(double qfbjhf) {
        this.qfbjhf = qfbjhf;
    }

    @Override
    public boolean compare(SqliteDp sqliteDp) {
        if (sqliteDp == null) {
            return false;
        }

        JSONObject jsonObject = JSONObject.parseObject(sqliteDp.getOtherOrDefault());
        return FastJSONObjAttrToNumber.toDouble(jsonObject, "qfbjhf") == this.getQfbjhf();
    }
}
