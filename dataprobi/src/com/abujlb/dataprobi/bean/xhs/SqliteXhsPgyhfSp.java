package com.abujlb.dataprobi.bean.xhs;

import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.SqliteSp;
import com.abujlb.dataprobi.interfaces.Plusable;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.abujlb.dataprobi.util.MathUtil;
import com.alibaba.fastjson.JSONObject;

public class SqliteXhsPgyhfSp extends AbstractSqliteSp implements Plusable<SqliteXhsPgyhfSp> {


    private double pgybzbjje;//蒲公英-博主报价金额
    private double pgyspfwfhf;//蒲公英-商品服务费花费


    public double getPgybzbjje() {
        return pgybzbjje;
    }

    public void setPgybzbjje(double pgybzbjje) {
        this.pgybzbjje = pgybzbjje;
    }

    public double getPgyspfwfhf() {
        return pgyspfwfhf;
    }

    public void setPgyspfwfhf(double pgyspfwfhf) {
        this.pgyspfwfhf = pgyspfwfhf;
    }

    @Override
    public void setValues(JSONObject jsonObject) {
        // 设置宝贝ID
        this.bbid = FastJSONObjAttrToNumber.toString(jsonObject, "itemId");

        // 设置博主报价金额
        this.pgybzbjje = FastJSONObjAttrToNumber.toDouble(jsonObject, "kolPrice");

        // 设置商品服务费花费
        this.pgyspfwfhf = FastJSONObjAttrToNumber.toInt(jsonObject, "totalPlatformPrice");
    }

    @Override
    public boolean compare(SqliteSp sqliteSp) {
        if (sqliteSp == null) {
            return false;
        }

        // 解析sqliteSp的附加数据
        JSONObject jsonObject = JSONObject.parseObject(sqliteSp.getOtherOrDefault());

        return this.pgybzbjje == FastJSONObjAttrToNumber.toDouble(jsonObject, "pgybzbjje") &
                this.pgyspfwfhf == FastJSONObjAttrToNumber.toDouble(jsonObject, "pgyspfwfhf");
    }


    @Override
    public void plus(SqliteXhsPgyhfSp temp) {
        // 累加博主报价金额
        this.pgybzbjje = MathUtil.add(this.pgybzbjje, temp.getPgybzbjje());

        // 累加商品服务费花费
        this.pgyspfwfhf += temp.getPgyspfwfhf();
    }
}
