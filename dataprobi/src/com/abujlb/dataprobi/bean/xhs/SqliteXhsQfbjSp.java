package com.abujlb.dataprobi.bean.xhs;

import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.SqliteSp;
import com.abujlb.dataprobi.bean.albb.SqliteAlbbCjtfSp;
import com.abujlb.dataprobi.interfaces.Plusable;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.abujlb.dataprobi.util.MathUtil;
import com.alibaba.fastjson.JSONObject;

public class SqliteXhsQfbjSp extends AbstractSqliteSp implements Plusable<SqliteXhsQfbjSp> {
    // 笔记花费
    private double qfbjhf ;

    public double getQfbjhf() {
        return qfbjhf;
    }
    
    public void setQfbjhf(double qfbjhf) {
        this.qfbjhf = qfbjhf;
    }

    @Override
    public void setValues(JSONObject jsonObject) {
        this.bbid = FastJSONObjAttrToNumber.toString(jsonObject,"itemId");
        this.qfbjhf  = FastJSONObjAttrToNumber.toDouble(jsonObject,"fee");
    }

    @Override
    public boolean compare(SqliteSp sqliteSp) {
        if (sqliteSp == null) {
            return false;
        }

        JSONObject jsonObject = JSONObject.parseObject(sqliteSp.getOtherOrDefault());
        return this.getQfbjhf() == FastJSONObjAttrToNumber.toDouble(jsonObject, "qfbihf");

    }

    @Override
    public void plus(SqliteXhsQfbjSp temp) {
        this.qfbjhf = MathUtil.add(this.qfbjhf, temp.getQfbjhf());
    }


}
