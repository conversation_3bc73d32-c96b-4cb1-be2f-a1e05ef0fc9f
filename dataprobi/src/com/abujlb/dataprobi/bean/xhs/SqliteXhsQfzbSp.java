package com.abujlb.dataprobi.bean.xhs;

import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.SqliteSp;
import com.abujlb.dataprobi.interfaces.Plusable;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.abujlb.dataprobi.util.MathUtil;
import com.alibaba.fastjson.JSONObject;

public class SqliteXhsQfzbSp extends AbstractSqliteSp implements Plusable<SqliteXhsQfzbSp> {
    // 直播花费
    private double qfzbhf ;

    public double getQfzbhf() {
        return qfzbhf;
    }

    public void setQfzbhf(double qfzbhf) {
        this.qfzbhf = qfzbhf;
    }

    @Override
    public void setValues(JSONObject jsonObject) {
        this.bbid = FastJSONObjAttrToNumber.toString(jsonObject,"itemId");
        this.qfzbhf  = FastJSONObjAttrToNumber.toDouble(jsonObject,"fee");
    }

    @Override
    public boolean compare(SqliteSp sqliteSp) {
        if (sqliteSp == null) {
            return false;
        }

        JSONObject jsonObject = JSONObject.parseObject(sqliteSp.getOtherOrDefault());
        return this.getQfzbhf() == FastJSONObjAttrToNumber.toDouble(jsonObject, "qfzbhf");

    }

    @Override
    public void plus(SqliteXhsQfzbSp temp) {
        this.qfzbhf = MathUtil.add(this.qfzbhf, temp.getQfzbhf());
    }


}
