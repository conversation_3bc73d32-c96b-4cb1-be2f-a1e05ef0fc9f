package com.abujlb.dataprobi.bean.xhs;


import com.abujlb.dataprobi.bean.AbstractSqliteDp;
import com.abujlb.dataprobi.bean.SqliteDp;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 * @date 2024/4/29
 */
public class SqliteXhsJgzbDp extends AbstractSqliteDp {

    /**
     * 聚光平台 -直播花费
     */
    private double jgzbhf;

    /**
     * 聚光平台 -直播间支付金额
     */
    private double jgzbjzfje;

    /**
     * 聚光平台 -直播间支付ROI
     */
    private double jgzbjzfroi;

    /**
     * 聚光平台 -直播间支付订单量
     */
    private int jgzbjzfdsl;

    /**
     * 聚光平台 -直播间支付订单成本
     */
    private double jgzbjzfdcb;

    /**
     * 聚光平台 -直播间观看次数
     */
    private int jgzbjgkcs;

    /**
     * 聚光平台 -直播间有效观看次数
     */
    private int jgzbjyxgkcs;

    /**
     * 聚光平台 -直播间人均停留时长
     */
    private double jgzbjrjtlsc;

    /**
     * 聚光平台 -直播间新增粉丝数
     */
    private int jgzbjxzfs;

    /**
     * 聚光平台 -直播间评论次数
     *
     */
    private int jgzbjplcs;

    public double getJgzbhf() {
        return jgzbhf;
    }

    public void setJgzbhf(double jgzbhf) {
        this.jgzbhf = jgzbhf;
    }

    public double getJgzbjzfje() {
        return jgzbjzfje;
    }

    public void setJgzbjzfje(double jgzbjzfje) {
        this.jgzbjzfje = jgzbjzfje;
    }

    public double getJgzbjzfroi() {
        return jgzbjzfroi;
    }

    public void setJgzbjzfroi(double jgzbjzfroi) {
        this.jgzbjzfroi = jgzbjzfroi;
    }

    public int getJgzbjzfdsl() {
        return jgzbjzfdsl;
    }

    public void setJgzbjzfdsl(int jgzbjzfdsl) {
        this.jgzbjzfdsl = jgzbjzfdsl;
    }

    public double getJgzbjzfdcb() {
        return jgzbjzfdcb;
    }

    public void setJgzbjzfdcb(double jgzbjzfdcb) {
        this.jgzbjzfdcb = jgzbjzfdcb;
    }

    public int getJgzbjgkcs() {
        return jgzbjgkcs;
    }

    public void setJgzbjgkcs(int jgzbjgkcs) {
        this.jgzbjgkcs = jgzbjgkcs;
    }

    public int getJgzbjyxgkcs() {
        return jgzbjyxgkcs;
    }

    public void setJgzbjyxgkcs(int jgzbjyxgkcs) {
        this.jgzbjyxgkcs = jgzbjyxgkcs;
    }

    public double getJgzbjrjtlsc() {
        return jgzbjrjtlsc;
    }

    public void setJgzbjrjtlsc(double jgzbjrjtlsc) {
        this.jgzbjrjtlsc = jgzbjrjtlsc;
    }

    public int getJgzbjxzfs() {
        return jgzbjxzfs;
    }

    public void setJgzbjxzfs(int jgzbjxzfs) {
        this.jgzbjxzfs = jgzbjxzfs;
    }

    public int getJgzbjplcs() {
        return jgzbjplcs;
    }

    public void setJgzbjplcs(int jgzbjplcs) {
        this.jgzbjplcs = jgzbjplcs;
    }

    public SqliteXhsJgzbDp() {
    }




    @Override
    public <T extends AbstractSqliteDp> void setValues(String[] tableStoreColumnValues) {
        String value0 = tableStoreColumnValues[0];
        if (value0 != null) {
            JSONObject jsonObject = JSONObject.parseObject(value0);
            this.jgzbhf = FastJSONObjAttrToNumber.toDouble(jsonObject, "fee");
            this.jgzbjzfje = FastJSONObjAttrToNumber.toDouble(jsonObject, "clkLiveRoomRgmv");
            this.jgzbjzfroi = FastJSONObjAttrToNumber.toDouble(jsonObject, "clkLiveRoomRoi");
            this.jgzbjzfdsl = FastJSONObjAttrToNumber.toInt(jsonObject, "clkLiveRoomOrderNum");
            this.jgzbjzfdcb = FastJSONObjAttrToNumber.toDouble(jsonObject, "liveAverageOrderCost");
            this.jgzbjgkcs = FastJSONObjAttrToNumber.toInt(jsonObject, "clkLiveEntryPv");
            this.jgzbjyxgkcs = FastJSONObjAttrToNumber.toInt(jsonObject, "clkLive5sEntryPv");
            this.jgzbjrjtlsc = FastJSONObjAttrToNumber.toDouble(jsonObject, "clkLiveAvgViewTime");
            this.jgzbjxzfs = FastJSONObjAttrToNumber.toInt(jsonObject, "clkLiveAllFollow");
            this.jgzbjplcs = FastJSONObjAttrToNumber.toInt(jsonObject, "clkLiveComment");
        }
    }


    @Override
    public boolean compare(SqliteDp sqliteDp) {
        if (sqliteDp == null) {
            return false;
        }

        JSONObject jsonObject = JSONObject.parseObject(sqliteDp.getOtherOrDefault());

        return FastJSONObjAttrToNumber.toDouble(jsonObject, "jgzbzxf") == this.getJgzbhf()
                && FastJSONObjAttrToNumber.toDouble(jsonObject, "jgzbjzfje") == this.getJgzbjzfje()
                && FastJSONObjAttrToNumber.toDouble(jsonObject, "jgzbjzfroi") == this.getJgzbjzfroi()
                && FastJSONObjAttrToNumber.toInt(jsonObject, "jgzbjzfdsl") == this.getJgzbjzfdsl()
                && FastJSONObjAttrToNumber.toDouble(jsonObject, "jgzbjzfdcb") == this.getJgzbjzfdcb()
                && FastJSONObjAttrToNumber.toInt(jsonObject, "jgzbjgkcs") == this.getJgzbjgkcs()
                && FastJSONObjAttrToNumber.toInt(jsonObject, "jgzbjyxgkcs") == this.getJgzbjyxgkcs()
                && FastJSONObjAttrToNumber.toDouble(jsonObject, "jgzbjrjtlsc") == this.getJgzbjrjtlsc()
                && FastJSONObjAttrToNumber.toInt(jsonObject, "jgzbjxzfs") == this.getJgzbjxzfs()
                && FastJSONObjAttrToNumber.toInt(jsonObject, "jgzbjplcs") == this.getJgzbjplcs();
    }
}
