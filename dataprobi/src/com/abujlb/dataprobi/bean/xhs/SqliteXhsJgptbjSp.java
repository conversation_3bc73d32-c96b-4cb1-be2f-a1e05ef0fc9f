package com.abujlb.dataprobi.bean.xhs;

import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.SqliteSp;
import com.abujlb.dataprobi.interfaces.Plusable;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.abujlb.dataprobi.util.MathUtil;
import com.alibaba.fastjson.JSONObject;

public class SqliteXhsJgptbjSp extends AbstractSqliteSp implements Plusable<SqliteXhsJgptbjSp> {
    // 笔记花费
    private double jgbjhf ;

    // 笔记展现量
    private int zxl ;

    // 笔记点击量
    private int djl ;

    // 笔记点击率
    private double djlv ;

    public double getJgbjhf() {
        return jgbjhf;
    }

    public void setJgbjhf(double jgbjhf) {
        this.jgbjhf = jgbjhf;
    }

    public int getZxl() {
        return zxl;
    }

    public void setZxl(int zxl) {
        this.zxl = zxl;
    }

    public int getDjl() {
        return djl;
    }

    public void setDjl(int djl) {
        this.djl = djl;
    }

    public double getDjlv() {
        return djlv;
    }

    public void setDjlv(double djlv) {
        this.djlv = djlv;
    }

    @Override
    public void setValues(JSONObject jsonObject) {
        // 设置宝贝ID
        this.bbid = FastJSONObjAttrToNumber.toString(jsonObject, "goodsid");

        // 设置笔记花费
        this.jgbjhf = FastJSONObjAttrToNumber.toDouble(jsonObject, "fee");

        // 设置笔记展现量
        this.zxl = FastJSONObjAttrToNumber.toInt(jsonObject, "impression");

        // 设置笔记点击量
        this.djl = FastJSONObjAttrToNumber.toInt(jsonObject, "click");

        // 计算笔记点击率
        this.djlv = zxl == 0 ? 0 : MathUtil.divide(this.djl, this.zxl);
    }


    @Override
    public boolean compare(SqliteSp sqliteSp) {
        if (sqliteSp == null) {
            return false;
        }

        // 解析sqliteSp的附加数据
        JSONObject jsonObject = JSONObject.parseObject(sqliteSp.getOtherOrDefault());

        return this.jgbjhf == FastJSONObjAttrToNumber.toDouble(jsonObject, "fee") &
                this.zxl == FastJSONObjAttrToNumber.toInt(jsonObject, "zxl") &
                this.djl == FastJSONObjAttrToNumber.toInt(jsonObject, "djl") &
                this.djlv == (this.zxl == 0 ? 0 : MathUtil.divide(this.djl, this.zxl));
    }


    @Override
    public void plus(SqliteXhsJgptbjSp temp) {
        // 累加笔记花费
        this.jgbjhf = MathUtil.add(this.jgbjhf, temp.getJgbjhf());

        // 累加笔记展现量
        this.zxl += temp.getZxl();

        // 累加笔记点击量
        this.djl += temp.getDjl();

        // 重新计算笔记点击率
        this.djlv = this.zxl == 0 ? 0 : MathUtil.divide(this.djl, this.zxl);
    }

}
