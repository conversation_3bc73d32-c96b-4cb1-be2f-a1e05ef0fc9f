package com.abujlb.dataprobi.bean.xhs;


import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.SqliteSp;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

public class SqliteXhsSpxgSp extends AbstractSqliteSp {
    // 支付转化率（次数）
    private double spxgzfzhlcs;
    // 退款订单占比
    private double spxgtkddzb;

    public double getSpxgzfzhlcs() {
        return spxgzfzhlcs;
    }

    public void setSpxgzfzhlcs(double spxgzfzhlcs) {
        this.spxgzfzhlcs = spxgzfzhlcs;
    }

    public double getSpxgtkddzb() {
        return spxgtkddzb;
    }

    public void setSpxgtkddzb(double spxgtkddzb) {
        this.spxgtkddzb = spxgtkddzb;
    }

    @Override
    public void setValues(JSONObject jsonObject) {
        this.spxgfks = FastJSONObjAttrToNumber.toInt(jsonObject, "uv", "value");
        this.spxglll = FastJSONObjAttrToNumber.toInt(jsonObject, "goodsPv", "value");
        this.spxgzfje = FastJSONObjAttrToNumber.toDouble(jsonObject, "payGmv", "value");
        this.spxgzfmjs = FastJSONObjAttrToNumber.toInt(jsonObject, "payUserNum", "value");
        this.spxgzfzhl = FastJSONObjAttrToNumber.toDouble(jsonObject, "upr", "value");
        this.spxgtkje = FastJSONObjAttrToNumber.toDouble(jsonObject, "refundPayGmv", "value");
        this.spxgzfjs = FastJSONObjAttrToNumber.toInt(jsonObject, "payGoodsCnt", "value");
        this.spxgzfzhlcs = FastJSONObjAttrToNumber.toInt(jsonObject, "uprPv", "value");
        this.spxgtkddzb = FastJSONObjAttrToNumber.toDouble(jsonObject, "refundRate", "value");

        if (jsonObject.containsKey("skuBasic") && jsonObject.getJSONObject("skuBasic").containsKey("value")) {
            Object value = jsonObject.getJSONObject("skuBasic").get("value");

            if (value instanceof JSONArray) {
                JSONArray skuBasicArray = (JSONArray) value;
                if (!skuBasicArray.isEmpty()) {
                    JSONObject itemBasic = skuBasicArray.getJSONObject(0);
                    this.bbid = FastJSONObjAttrToNumber.toString(itemBasic, "skuId");
                    this.bbmc = FastJSONObjAttrToNumber.toString(itemBasic, "skuName");
                    this.img = FastJSONObjAttrToNumber.toString(itemBasic, "skuUrl");
                }
            } else if (value instanceof String) {
                String skuId = (String) value;
                this.bbid = skuId;
                this.bbmc = "";
                this.img = "";
            }
        }
    }



    @Override
    public boolean compare(SqliteSp sqliteSp) {
        if (sqliteSp == null) {
            return false;
        }

        JSONObject jsonObject = JSONObject.parseObject(sqliteSp.getOtherOrDefault());
        return this.getSpxgzfzhlcs() == FastJSONObjAttrToNumber.toInt(jsonObject, "spxgzfzhlcs") &&
                this.getSpxgtkddzb() == FastJSONObjAttrToNumber.toDouble(jsonObject, "spxgtkddzb");
    }
}
