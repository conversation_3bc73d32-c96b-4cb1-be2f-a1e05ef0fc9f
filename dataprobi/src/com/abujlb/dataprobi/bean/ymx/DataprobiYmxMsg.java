package com.abujlb.dataprobi.bean.ymx;

import com.abujlb.dataprobi.bean.DataprobiBaseMsgBean;
import com.alibaba.fastjson.JSON;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/10
 */
public class DataprobiYmxMsg extends DataprobiBaseMsgBean {

    //亚马逊 商品推广花费 商品维度
    private List<String> sptg;

    public List<String> getSptg() {
        return sptg;
    }

    public void setSptg(List<String> sptg) {
        this.sptg = sptg;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }

    public void fillNullList() throws InvocationTargetException, IllegalAccessException {
        Method[] methods = this.getClass().getMethods();
        for (Method method : methods) {
            if (method.getName().startsWith("get")) {
                Object value = method.invoke(this);
                if (value == null) {
                    Method setMethod = null;
                    try {
                        setMethod = this.getClass().getMethod("s" + method.getName().substring(1), List.class);
                        setMethod.invoke(this, new ArrayList<>());
                    } catch (NoSuchMethodException ignored) {
                    }
                }
            }
        }
    }
}
