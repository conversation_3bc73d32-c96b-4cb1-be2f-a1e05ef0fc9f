package com.abujlb.dataprobi.bean.ymx;

import cn.hutool.json.JSONUtil;
import com.abujlb.dataprobi.bean.AbstractSqliteDp;
import com.abujlb.dataprobi.bean.SqliteDp;
import com.abujlb.dataprobi.util.MathUtil;

/**
 * 预估
 *
 * <AUTHOR>
 * @date 2022/5/12 11:18
 */
public class SqliteYmxSptgsjDp extends AbstractSqliteDp {
    /**
     * 在广告点击后 1 天内发生的销售总价值
     */
    private double attributedSalesSameSku1d;
    /**
     * 在广告点击后 7 天内发生的销售总价值
     */
    private double attributedSalesSameSku7d;
    /**
     * 在广告点击后 14 天内发生的销售总价值
     */
    private double attributedSalesSameSku14d;
    /**
     * 在广告点击后 30 天内发生的销售总价值
     */
    private double attributedSalesSameSku30d;
    /**
     * 广告支出回报 (ROAS)
     * 您从广告投资中获得的收入。计算公式为：由广告产生的销售额除以您的支出。归因销售额可能涵盖广告中未展示的商品。归因因广告活动类型而异。
     */
    private double roas;
    /**
     * 广告支出回报率，基于点击广告后 7 天内的购买。
     */
    private double roasClicks7d;
    /**
     * 广告支出回报率，基于点击广告后 14 天内的购买。
     */
    private double roasClicks14d;
    /**
     * 点击广告后 1 天内订购的商品总数。
     */
    private int unitsSoldClicks1d;
    /**
     * 点击广告后 7 天内订购的商品总数。
     */
    private int unitsSoldClicks7d;
    /**
     * 点击广告后 14 天内订购的商品总数。
     */
    private int unitsSoldClicks14d;
    /**
     * 点击广告后 30 天内订购的商品总数。
     */
    private int unitsSoldClicks30d;
    /**
     * 点击广告后 1 天内发生的销售总价值。
     */
    private double sales1d;
    /**
     * 点击广告后 7 天内发生的销售总价值。
     */
    private double sales7d;
    /**
     * 点击广告后 14 天内发生的销售总价值。
     */
    private double sales14d;
    /**
     * 点击广告后 30 天内发生的销售总价值。
     */
    private double sales30d;

    /**
     * 在广告点击后 14 天内，归因的 Kindle 版标准化页面的预估版税。
     */
    private int kindleEditionNormalizedPagesRoyalties14d;
    /**
     * 在广告点击后 1 天内订购的商品总数，其中购买的 SKU 与推广的 SKU 相同。
     */
    private int unitsSoldSameSku1d;
    /**
     * 在广告点击后 7 天内订购的商品总数，其中购买的 SKU 与推广的 SKU 相同。
     */
    private int unitsSoldSameSku7d;
    /**
     * 在广告点击后 14 天内订购的商品总数，其中购买的 SKU 与推广的 SKU 相同。
     */
    private int unitsSoldSameSku14d;

    /**
     * 在广告点击后 30 天内订购的商品总数，其中购买的 SKU 与推广的 SKU 相同。
     */
    private int unitsSoldSameSku30d;
    /**
     * 在广告点击后 7 天内发生的销售总价值，其中购买的 SKU 与推广的 SKU 不同。
     */
    private int salesOtherSku7d;
    /**
     * 在广告点击后 1 天内发生的归因转化事件数，其中购买的 SKU 与推广的 SKU 相同。
     */
    private int purchasesSameSku1d;
    /**
     * 在广告点击后 7 天内发生的归因转化事件数，其中购买的 SKU 与推广的 SKU 相同。
     */
    private int purchasesSameSku7d;

    /**
     * 在广告点击后 14 天内发生的归因转化事件数，其中购买的 SKU 与推广的 SKU 相同。
     */
    private int purchasesSameSku14d;
    /**
     * 在广告点击后 30 天内发生的归因转化事件数，其中购买的 SKU 与推广的 SKU 相同。
     */
    private int purchasesSameSku30d;
    /**
     * 分配给活动的总预算。
     */
    private int campaignBudgetAmount;

    /**
     * 点击广告后 1 天内发生的归因转化事件数。
     */
    private int purchases1d;
    /**
     * 点击广告后 7 天内发生的归因转化事件数。
     */
    private int purchases7d;
    /**
     * 点击广告后 14 天内发生的归因转化事件数。
     */
    private int purchases14d;
    /**
     * 点击广告后 30 天内发生的归因转化事件数。
     */
    private int purchases30d;

    /**
     * 总成本除以总点击次数。即每次点击的花费
     */
    private double costPerClick;

    /**
     * 点击次数除以展示次数。 点击率 顾客在广告展示期间点击广告的频率所占的比率。该值由点击量除以展示量计算得出。
     */
    private double clickThroughRate;
    /**
     * 点击广告后 14 天内阅读的归因 Kindle 版标准化页面数。
     */
    private int kindleEditionNormalizedPagesRead14d;
    /**
     * 广告投入产出比 (ACOS)。 广告支出占销售额的百分比。计算公式为：支出除以由广告产生的销售额。归因销售额可能涵盖广告中未展示的商品。归因因广告活动类型而异。
     */
    private double acos;
    /**
     * 广告销售成本基于广告点击后 7 天内的购买。
     */
    private double acosClicks7d;
    /**
     * 广告销售成本基于广告点击后 14 天内的购买。
     */
    private double acosClicks14d;

    /**
     * 在归因窗口期内，由于广告曝光而下载电子书，并在窗口内阅读了一定比例的电子书，从而触发版税支付的 Kindle Unlimited 用户数量。仅与图书广告主相关。
     */
    private int qualifiedBorrows;


    /**
     * Kindle Unlimited 用户在归因窗口期内由于广告曝光而下载电子书，并在窗口内阅读了一定比例的电子书，从而触发了版税支付的预计版税。仅与图书广告主相关。
     */
    private int royaltyQualifiedBorrows;
    /**
     * 顾客将促销商品添加到愿望单、礼品清单或注册表的次数，归因于广告浏览或点击。使用总 ATL 查看品牌商品的所有转化。
     */
    private int addToList;

    /**
     * 在点击广告后 7 天内订购的商品总数，其中购买的 SKU 与推广的 SKU 不同。
     */
    private int unitsSoldOtherSku7d;
    /**
     * 广告点击的总成本。
     */
    private double spend;

    /**
     * 广告总展示次数。曝光量
     */
    private int impressions;
    /**
     * 点击次数
     */
    private int clicks;
    /**
     * 转化率
     */
    private double zhl;
    /**
     * 广告推广花费
     */
    private double ggtghf;

    public SqliteYmxSptgsjDp() {
    }


    public double getAttributedSalesSameSku1d() {
        return attributedSalesSameSku1d;
    }

    public void setAttributedSalesSameSku1d(double attributedSalesSameSku1d) {
        this.attributedSalesSameSku1d = attributedSalesSameSku1d;
    }

    public double getRoasClicks14d() {
        return roasClicks14d;
    }

    public void setRoasClicks14d(double roasClicks14d) {
        this.roasClicks14d = roasClicks14d;
    }

    public int getUnitsSoldClicks1d() {
        return unitsSoldClicks1d;
    }

    public void setUnitsSoldClicks1d(int unitsSoldClicks1d) {
        this.unitsSoldClicks1d = unitsSoldClicks1d;
    }

    public double getAttributedSalesSameSku14d() {
        return attributedSalesSameSku14d;
    }

    public void setAttributedSalesSameSku14d(double attributedSalesSameSku14d) {
        this.attributedSalesSameSku14d = attributedSalesSameSku14d;
    }

    public double getSales7d() {
        return sales7d;
    }

    public void setSales7d(double sales7d) {
        this.sales7d = sales7d;
    }

    public double getAttributedSalesSameSku30d() {
        return attributedSalesSameSku30d;
    }

    public void setAttributedSalesSameSku30d(double attributedSalesSameSku30d) {
        this.attributedSalesSameSku30d = attributedSalesSameSku30d;
    }

    public int getKindleEditionNormalizedPagesRoyalties14d() {
        return kindleEditionNormalizedPagesRoyalties14d;
    }

    public void setKindleEditionNormalizedPagesRoyalties14d(int kindleEditionNormalizedPagesRoyalties14d) {
        this.kindleEditionNormalizedPagesRoyalties14d = kindleEditionNormalizedPagesRoyalties14d;
    }

    public int getUnitsSoldSameSku1d() {
        return unitsSoldSameSku1d;
    }

    public void setUnitsSoldSameSku1d(int unitsSoldSameSku1d) {
        this.unitsSoldSameSku1d = unitsSoldSameSku1d;
    }

    public int getSalesOtherSku7d() {
        return salesOtherSku7d;
    }

    public void setSalesOtherSku7d(int salesOtherSku7d) {
        this.salesOtherSku7d = salesOtherSku7d;
    }

    public int getPurchasesSameSku7d() {
        return purchasesSameSku7d;
    }

    public void setPurchasesSameSku7d(int purchasesSameSku7d) {
        this.purchasesSameSku7d = purchasesSameSku7d;
    }

    public int getCampaignBudgetAmount() {
        return campaignBudgetAmount;
    }

    public void setCampaignBudgetAmount(int campaignBudgetAmount) {
        this.campaignBudgetAmount = campaignBudgetAmount;
    }

    public int getPurchases7d() {
        return purchases7d;
    }

    public void setPurchases7d(int purchases7d) {
        this.purchases7d = purchases7d;
    }

    public int getUnitsSoldSameSku30d() {
        return unitsSoldSameSku30d;
    }

    public void setUnitsSoldSameSku30d(int unitsSoldSameSku30d) {
        this.unitsSoldSameSku30d = unitsSoldSameSku30d;
    }

    public double getCostPerClick() {
        return costPerClick;
    }

    public void setCostPerClick(double costPerClick) {
        this.costPerClick = costPerClick;
    }

    public int getUnitsSoldClicks14d() {
        return unitsSoldClicks14d;
    }

    public void setUnitsSoldClicks14d(int unitsSoldClicks14d) {
        this.unitsSoldClicks14d = unitsSoldClicks14d;
    }

    public double getClickThroughRate() {
        return clickThroughRate;
    }

    public void setClickThroughRate(double clickThroughRate) {
        this.clickThroughRate = clickThroughRate;
    }

    public int getKindleEditionNormalizedPagesRead14d() {
        return kindleEditionNormalizedPagesRead14d;
    }

    public void setKindleEditionNormalizedPagesRead14d(int kindleEditionNormalizedPagesRead14d) {
        this.kindleEditionNormalizedPagesRead14d = kindleEditionNormalizedPagesRead14d;
    }

    public double getAcosClicks14d() {
        return acosClicks14d;
    }

    public void setAcosClicks14d(double acosClicks14d) {
        this.acosClicks14d = acosClicks14d;
    }

    public int getUnitsSoldClicks30d() {
        return unitsSoldClicks30d;
    }

    public void setUnitsSoldClicks30d(int unitsSoldClicks30d) {
        this.unitsSoldClicks30d = unitsSoldClicks30d;
    }

    public int getQualifiedBorrows() {
        return qualifiedBorrows;
    }

    public void setQualifiedBorrows(int qualifiedBorrows) {
        this.qualifiedBorrows = qualifiedBorrows;
    }

    public double getRoasClicks7d() {
        return roasClicks7d;
    }

    public void setRoasClicks7d(double roasClicks7d) {
        this.roasClicks7d = roasClicks7d;
    }

    public int getUnitsSoldSameSku14d() {
        return unitsSoldSameSku14d;
    }

    public void setUnitsSoldSameSku14d(int unitsSoldSameSku14d) {
        this.unitsSoldSameSku14d = unitsSoldSameSku14d;
    }

    public int getUnitsSoldClicks7d() {
        return unitsSoldClicks7d;
    }

    public void setUnitsSoldClicks7d(int unitsSoldClicks7d) {
        this.unitsSoldClicks7d = unitsSoldClicks7d;
    }

    public double getAttributedSalesSameSku7d() {
        return attributedSalesSameSku7d;
    }

    public void setAttributedSalesSameSku7d(double attributedSalesSameSku7d) {
        this.attributedSalesSameSku7d = attributedSalesSameSku7d;
    }

    public int getRoyaltyQualifiedBorrows() {
        return royaltyQualifiedBorrows;
    }

    public void setRoyaltyQualifiedBorrows(int royaltyQualifiedBorrows) {
        this.royaltyQualifiedBorrows = royaltyQualifiedBorrows;
    }

    public int getAddToList() {
        return addToList;
    }

    public void setAddToList(int addToList) {
        this.addToList = addToList;
    }

    public int getPurchasesSameSku14d() {
        return purchasesSameSku14d;
    }

    public void setPurchasesSameSku14d(int purchasesSameSku14d) {
        this.purchasesSameSku14d = purchasesSameSku14d;
    }

    public int getUnitsSoldOtherSku7d() {
        return unitsSoldOtherSku7d;
    }

    public void setUnitsSoldOtherSku7d(int unitsSoldOtherSku7d) {
        this.unitsSoldOtherSku7d = unitsSoldOtherSku7d;
    }

    public double getSpend() {
        return spend;
    }

    public void setSpend(double spend) {
        this.spend = spend;
    }

    public int getPurchasesSameSku1d() {
        return purchasesSameSku1d;
    }

    public void setPurchasesSameSku1d(int purchasesSameSku1d) {
        this.purchasesSameSku1d = purchasesSameSku1d;
    }

    public int getPurchases1d() {
        return purchases1d;
    }

    public void setPurchases1d(int purchases1d) {
        this.purchases1d = purchases1d;
    }

    public int getUnitsSoldSameSku7d() {
        return unitsSoldSameSku7d;
    }

    public void setUnitsSoldSameSku7d(int unitsSoldSameSku7d) {
        this.unitsSoldSameSku7d = unitsSoldSameSku7d;
    }

    public double getGgtghf() {
        return ggtghf;
    }

    public void setGgtghf(double ggtghf) {
        this.ggtghf = ggtghf;
    }

    public double getSales14d() {
        return sales14d;
    }

    public void setSales14d(double sales14d) {
        this.sales14d = sales14d;
    }

    public double getAcosClicks7d() {
        return acosClicks7d;
    }

    public void setAcosClicks7d(double acosClicks7d) {
        this.acosClicks7d = acosClicks7d;
    }

    public double getSales30d() {
        return sales30d;
    }

    public void setSales30d(double sales30d) {
        this.sales30d = sales30d;
    }

    public int getImpressions() {
        return impressions;
    }

    public void setImpressions(int impressions) {
        this.impressions = impressions;
    }

    public int getPurchasesSameSku30d() {
        return purchasesSameSku30d;
    }

    public void setPurchasesSameSku30d(int purchasesSameSku30d) {
        this.purchasesSameSku30d = purchasesSameSku30d;
    }

    public int getPurchases14d() {
        return purchases14d;
    }

    public void setPurchases14d(int purchases14d) {
        this.purchases14d = purchases14d;
    }

    public int getPurchases30d() {
        return purchases30d;
    }

    public void setPurchases30d(int purchases30d) {
        this.purchases30d = purchases30d;
    }

    public int getClicks() {
        return clicks;
    }

    public void setClicks(int clicks) {
        this.clicks = clicks;
    }

    @Override
    public <T extends AbstractSqliteDp> void plus(T t) {
        if (t instanceof SqliteYmxSptgsjDp) {
            SqliteYmxSptgsjDp temp = (SqliteYmxSptgsjDp) t;
            this.attributedSalesSameSku1d = MathUtil.add(this.attributedSalesSameSku1d, temp.getAttributedSalesSameSku1d());
            this.attributedSalesSameSku14d = MathUtil.add(this.attributedSalesSameSku14d, temp.getAttributedSalesSameSku14d());
            this.sales1d = MathUtil.add(this.sales1d, temp.getSales1d());
            this.sales7d = MathUtil.add(this.sales7d, temp.getSales7d());
            this.attributedSalesSameSku30d = MathUtil.add(this.attributedSalesSameSku30d, temp.getAttributedSalesSameSku30d());
            this.attributedSalesSameSku7d = MathUtil.add(this.attributedSalesSameSku7d, temp.getAttributedSalesSameSku7d());
            this.spend = MathUtil.add(this.spend, temp.getSpend());
            this.ggtghf = MathUtil.add(this.ggtghf, temp.getGgtghf());
            this.sales14d = MathUtil.add(this.sales14d, temp.getSales14d());
            this.acosClicks7d = MathUtil.add(this.acosClicks7d, temp.getAcosClicks7d());
            this.sales30d = MathUtil.add(this.sales30d, temp.getSales30d());
            this.unitsSoldClicks1d = this.unitsSoldClicks1d + temp.getUnitsSoldClicks1d();
            this.kindleEditionNormalizedPagesRoyalties14d = this.kindleEditionNormalizedPagesRoyalties14d + temp.getKindleEditionNormalizedPagesRoyalties14d();
            this.unitsSoldSameSku1d = this.unitsSoldSameSku1d + temp.getUnitsSoldClicks1d();
            this.salesOtherSku7d = this.salesOtherSku7d + temp.getSalesOtherSku7d();
            this.purchasesSameSku7d = this.purchasesSameSku7d + temp.getPurchasesSameSku7d();
            this.campaignBudgetAmount = this.campaignBudgetAmount + temp.getCampaignBudgetAmount();
            this.purchases7d = this.purchases7d + temp.getPurchases7d();
            this.unitsSoldSameSku30d = this.unitsSoldSameSku30d + temp.getUnitsSoldSameSku30d();
            this.unitsSoldClicks14d = this.unitsSoldClicks14d + temp.getUnitsSoldClicks14d();
            this.kindleEditionNormalizedPagesRead14d = this.kindleEditionNormalizedPagesRead14d + temp.getKindleEditionNormalizedPagesRead14d();
            this.unitsSoldClicks30d = this.unitsSoldClicks30d + temp.getUnitsSoldClicks30d();
            this.qualifiedBorrows = this.qualifiedBorrows + temp.getQualifiedBorrows();
            this.unitsSoldSameSku14d = this.unitsSoldSameSku14d + temp.getUnitsSoldSameSku14d();
            this.unitsSoldClicks7d = this.unitsSoldClicks7d + temp.getUnitsSoldClicks7d();
            this.royaltyQualifiedBorrows = this.royaltyQualifiedBorrows + temp.getRoyaltyQualifiedBorrows();
            this.addToList = this.addToList + temp.getAddToList();
            this.purchasesSameSku14d = this.purchasesSameSku14d + temp.getPurchasesSameSku14d();
            this.unitsSoldOtherSku7d = this.unitsSoldOtherSku7d + temp.getUnitsSoldOtherSku7d();
            this.purchasesSameSku1d = this.purchasesSameSku1d + temp.getPurchasesSameSku1d();
            this.purchases1d = this.purchases1d + temp.getPurchases1d();
            this.unitsSoldSameSku7d = this.unitsSoldSameSku7d + temp.getUnitsSoldSameSku7d();
            this.impressions = this.impressions + temp.getImpressions();
            this.purchasesSameSku30d = this.purchasesSameSku30d + temp.getPurchasesSameSku30d();
            this.purchases14d = this.purchases14d + temp.getPurchases14d();
            this.purchases30d = this.purchases30d + temp.getPurchases30d();
            this.clicks = this.clicks + temp.getClicks();
        }
    }


    @Override
    public boolean compare(SqliteDp sqliteDp) {
        if (sqliteDp == null) {
            return false;
        }
        cn.hutool.json.JSONObject json = JSONUtil.parseObj(sqliteDp.getOtherOrDefault());
        return json.getDouble("attributedSalesSameSku1d") == this.attributedSalesSameSku1d
                && json.getDouble("roasClicks14d") == this.roasClicks14d
                && json.getDouble("roas") == this.roas
                && json.getInt("unitsSoldClicks1d") == this.unitsSoldClicks1d
                && json.getDouble("attributedSalesSameSku14d") == this.attributedSalesSameSku14d
                && json.getDouble("sales7d") == this.sales7d
                && json.getDouble("sales1d") == this.sales1d
                && json.getDouble("attributedSalesSameSku30d") == this.attributedSalesSameSku30d
                && json.getInt("kindleEditionNormalizedPagesRoyalties14d") == this.kindleEditionNormalizedPagesRoyalties14d
                && json.getInt("unitsSoldSameSku1d") == this.unitsSoldSameSku1d
                && json.getInt("salesOtherSku7d") == this.salesOtherSku7d
                && json.getInt("purchasesSameSku7d") == this.purchasesSameSku7d
                && json.getInt("campaignBudgetAmount") == this.campaignBudgetAmount
                && json.getInt("purchases7d") == this.purchases7d
                && json.getInt("unitsSoldSameSku30d") == this.unitsSoldSameSku30d
                && json.getDouble("costPerClick") == this.costPerClick
                && json.getInt("unitsSoldClicks14d") == this.unitsSoldClicks14d
                && json.getDouble("clickThroughRate") == this.clickThroughRate
                && json.getInt("kindleEditionNormalizedPagesRead14d") == this.kindleEditionNormalizedPagesRead14d
                && json.getDouble("acosClicks14d") == this.acosClicks14d
                && json.getInt("unitsSoldClicks30d") == this.unitsSoldClicks30d
                && json.getInt("qualifiedBorrows") == this.qualifiedBorrows
                && json.getDouble("roasClicks7d") == this.roasClicks7d
                && json.getInt("unitsSoldSameSku14d") == this.unitsSoldSameSku14d
                && json.getInt("unitsSoldClicks7d") == this.unitsSoldClicks7d
                && json.getDouble("attributedSalesSameSku7d") == this.attributedSalesSameSku7d
                && json.getInt("royaltyQualifiedBorrows") == this.royaltyQualifiedBorrows
                && json.getInt("addToList") == this.addToList
                && json.getInt("purchasesSameSku14d") == this.purchasesSameSku14d
                && json.getInt("unitsSoldOtherSku7d") == this.unitsSoldOtherSku7d
                && json.getDouble("spend") == this.spend
                && json.getInt("purchasesSameSku1d") == this.purchasesSameSku1d
                && json.getInt("purchases1d") == this.purchases1d
                && json.getInt("unitsSoldSameSku7d") == this.unitsSoldSameSku7d
                && json.getDouble("ggtghf") == this.ggtghf
                && json.getDouble("sales14d") == this.sales14d
                && json.getDouble("acos") == this.acos
                && json.getDouble("acosClicks7d") == this.acosClicks7d
                && json.getDouble("sales30d") == this.sales30d
                && json.getInt("impressions") == this.impressions
                && json.getInt("purchasesSameSku30d") == this.purchasesSameSku30d
                && json.getInt("purchases14d") == this.purchases14d
                && json.getInt("purchases30d") == this.purchases30d
                && json.getInt("clicks") == this.clicks;
    }

    public double getRoas() {
        return roas;
    }

    public void setRoas(double roas) {
        this.roas = roas;
    }

    public double getSales1d() {
        return sales1d;
    }

    public void setSales1d(double sales1d) {
        this.sales1d = sales1d;
    }

    public double getAcos() {
        return acos;
    }

    public void setAcos(double acos) {
        this.acos = acos;
    }

    public double getZhl() {
        return zhl;
    }

    public void setZhl(double zhl) {
        this.zhl = zhl;
    }
}
