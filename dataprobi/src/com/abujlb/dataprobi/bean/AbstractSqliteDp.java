package com.abujlb.dataprobi.bean;

import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 * @date 2022/6/30 10:52
 */
public abstract class AbstractSqliteDp extends SqliteDp {

    public AbstractSqliteDp() {
    }

    /**
     * 如果店铺数据只需要一个字段保存，则覆盖重写此方法
     *
     * @param jsonObject 表格存储中的字段(json格式)转JSONObject
     * @param <T>
     */
    public <T extends AbstractSqliteDp> void setValues(JSONObject jsonObject) {
    }

    /**
     * 有的店铺维度数据需要采集多个地方，然后组合
     * 所以提供了此方法以供子类覆盖重写。
     *
     * @param tableStoreColumnValues
     * @param <T>
     */
    public <T extends AbstractSqliteDp> void setValues(String[] tableStoreColumnValues) {
    }

    public <T extends AbstractSqliteDp> void plus(T t) {
    }

    /**
     * 比对数据
     *
     * @param sqliteDp
     * @return true比对成功 false sqliteDp为空或者比对失败
     */
    public boolean compare(SqliteDp sqliteDp) {
        return true;
    }

}
