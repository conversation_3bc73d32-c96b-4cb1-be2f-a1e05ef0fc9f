package com.abujlb.dataprobi.bean.wph;


import com.abujlb.dataprobi.bean.AbstractSqliteDp;
import com.abujlb.dataprobi.bean.SqliteDp;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.abujlb.dataprobi.util.MathUtil;
import com.alibaba.fastjson.JSONObject;

/**
 * 站内广告奖励金
 */
public class SqliteZnggJljDp extends AbstractSqliteDp {

    private double znwzzjlj;//站内唯智展奖励金

    private double znwcdjlj;//站内唯触点奖励金

    private double znwzdjlj;//站内唯直达奖励金

    public double getZnwzzjlj() {
        return znwzzjlj;
    }

    public void setZnwzzjlj(double znwzzjlj) {
        this.znwzzjlj = znwzzjlj;
    }

    public double getZnwcdjlj() {
        return znwcdjlj;
    }

    public void setZnwcdjlj(double znwcdjlj) {
        this.znwcdjlj = znwcdjlj;
    }

    public double getZnwzdjlj() {
        return znwzdjlj;
    }

    public void setZnwzdjlj(double znwzdjlj) {
        this.znwzdjlj = znwzdjlj;
    }

    @Override
    public <T extends AbstractSqliteDp> void setValues(String[] tableStoreColumnValues) {
        String value0 = tableStoreColumnValues[0];
        if (value0 != null) {
            JSONObject jsonObject = JSONObject.parseObject(value0);
            this.znwzzjlj = -MathUtil.changeF2Y(FastJSONObjAttrToNumber.toString(jsonObject, "znwzzjlj"));
            this.znwcdjlj = -MathUtil.changeF2Y(FastJSONObjAttrToNumber.toString(jsonObject, "znwcdjlj"));
            this.znwzdjlj = -MathUtil.changeF2Y(FastJSONObjAttrToNumber.toString(jsonObject, "znwzdjlj"));


        }
    }


    @Override
    public boolean compare(SqliteDp sqliteDp) {
        if (sqliteDp == null) {
            return false;
        }

        JSONObject jsonObject = JSONObject.parseObject(sqliteDp.getOtherOrDefault());

        return FastJSONObjAttrToNumber.toDouble(jsonObject, "znwzzjlj") == this.getZnwzzjlj()
                && FastJSONObjAttrToNumber.toDouble(jsonObject, "znwcdjlj") == this.getZnwcdjlj()
                && FastJSONObjAttrToNumber.toDouble(jsonObject, "znwzdjlj") == this.getZnwzdjlj()
                ;
    }
}
