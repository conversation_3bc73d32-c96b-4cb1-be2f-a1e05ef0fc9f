package com.abujlb.dataprobi.bean.wph;

import com.abujlb.dataprobi.bean.AbstractSqliteDp;
import com.abujlb.dataprobi.bean.SqliteDp;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.alibaba.fastjson.JSONObject;


/**
 * 唯品会渠道商品效果店铺
 */
public class SqliteWphSpxgDp extends AbstractSqliteDp {



    public SqliteWphSpxgDp() {
    }


    @Override
    public <T extends AbstractSqliteDp> void setValues(String[] tableStoreColumnValues) {
        String value0 = tableStoreColumnValues[0];
        if (value0 != null) {
            JSONObject jsonObject = JSONObject.parseObject(value0);
            
            // 店铺和商品访客数
            this.spxgfks = FastJSONObjAttrToNumber.toInt(jsonObject, "uv");
            this.spxgspfks = FastJSONObjAttrToNumber.toInt(jsonObject, "visitMerchandiseNum");
            
            // 浏览量
            this.spxglll = FastJSONObjAttrToNumber.toInt(jsonObject, "impressionFlow");
            this.spxgsplll = FastJSONObjAttrToNumber.toInt(jsonObject, "pageMerDetailFlow");
            
            // 支付相关
            this.spxgzfje = FastJSONObjAttrToNumber.toDouble(jsonObject, "goodsActureAmt");
            this.spxgzfmjs = FastJSONObjAttrToNumber.toInt(jsonObject, "userNum");
            this.spxgzfzhl = FastJSONObjAttrToNumber.toDouble(jsonObject, "payConvRate");
            this.spxgzfjs = FastJSONObjAttrToNumber.toInt(jsonObject, "goodsActureNum");
            
            // 退款金额
            this.spxgtkje = FastJSONObjAttrToNumber.toDouble(jsonObject, "returnGoodsAmt");
        }
    }

    @Override
    public boolean compare(SqliteDp sqliteDp) {
        if (sqliteDp == null) {
            return false;
        }

        JSONObject jsonObject = JSONObject.parseObject(sqliteDp.getOtherOrDefault());

        return FastJSONObjAttrToNumber.toInt(jsonObject, "uv") == this.getSpxgfks()
                && FastJSONObjAttrToNumber.toInt(jsonObject, "spxgfks") == this.getSpxgspfks()
                && FastJSONObjAttrToNumber.toInt(jsonObject, "spxglll") == this.getSpxglll()
                && FastJSONObjAttrToNumber.toInt(jsonObject, "spxgsplll") == this.getSpxgsplll()
                && FastJSONObjAttrToNumber.toDouble(jsonObject, "spxgzfje") == this.getSpxgzfje()
                && FastJSONObjAttrToNumber.toInt(jsonObject, "spxgzfmjs") == this.getSpxgzfmjs()
                && FastJSONObjAttrToNumber.toDouble(jsonObject, "spxgzfzhl") == this.getSpxgzfzhl()
                && FastJSONObjAttrToNumber.toInt(jsonObject, "spxgzfjs") == this.getSpxgzfjs()
                && FastJSONObjAttrToNumber.toDouble(jsonObject, "spxgtkje") == this.getSpxgtkje();
    }

}
