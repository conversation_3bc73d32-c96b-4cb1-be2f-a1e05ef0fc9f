package com.abujlb.dataprobi.bean.wph;


import com.abujlb.dataprobi.bean.AbstractSqliteDp;
import com.abujlb.dataprobi.bean.SqliteDp;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.abujlb.dataprobi.util.MathUtil;
import com.alibaba.fastjson.JSONObject;

/**
 * 站外广告-爱奇艺花费
 */
public class SqliteAqyHfDp extends AbstractSqliteDp {

    private double zwaqyhf;//站外广告-爱奇艺花费

    public double getZwaqyhf() {
        return zwaqyhf;
    }

    public void setZwaqyhf(double zwaqyhf) {
        this.zwaqyhf = zwaqyhf;
    }

    @Override
    public <T extends AbstractSqliteDp> void setValues(String[] tableStoreColumnValues) {
        String value0 = tableStoreColumnValues[0];
        if (value0 != null) {
            JSONObject jsonObject = JSONObject.parseObject(value0);
            this.zwaqyhf = MathUtil.changeF2Y(FastJSONObjAttrToNumber.toString(jsonObject, "cost"));
        }
    }


    @Override
    public boolean compare(SqliteDp sqliteDp) {
        if (sqliteDp == null) {
            return false;
        }

        JSONObject jsonObject = JSONObject.parseObject(sqliteDp.getOtherOrDefault());

        return FastJSONObjAttrToNumber.toDouble(jsonObject, "zwaqyhf") == this.getZwaqyhf();
    }
}
