package com.abujlb.dataprobi.bean.wph;

import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.SqliteSp;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.abujlb.dataprobi.util.MathUtil;
import com.alibaba.fastjson.JSONObject;
import org.apache.log4j.Logger;

/**
 * 唯品会商品效果数据
 */
public class SqliteWphSpxgSp extends AbstractSqliteSp {
    private static final Logger logger = Logger.getLogger(SqliteWphSpxgSp.class);

    // 唯享客销售额
    private double wxkxse;
    // 唯享客ROI
    private double wxkroi;
    // 唯享客成本
    private double wxkcb;

    public SqliteWphSpxgSp() {
    }


    // Getters and Setters
    public double getWxkxse() {
        return wxkxse;
    }

    public void setWxkxse(double wxkxse) {
        this.wxkxse = wxkxse;
    }

    public double getWxkroi() {
        return wxkroi;
    }

    public void setWxkroi(double wxkroi) {
        this.wxkroi = wxkroi;
    }

    public double getWxkcb() {
        return wxkcb;
    }

    public void setWxkcb(double wxkcb) {
        this.wxkcb = wxkcb;
    }


    @Override
    public void setValues(JSONObject jsonObject) {
        if (jsonObject == null) {
            return;
        }

        try {
            // 设置父类基础字段
            this.bbid = FastJSONObjAttrToNumber.toString(jsonObject, "skuid");
            this.img = FastJSONObjAttrToNumber.toString(jsonObject, "imgUrl");
            this.bbmc = FastJSONObjAttrToNumber.toString(jsonObject, "goodsName");
            // 商品访客数 - uv
            this.setSpxgfks(FastJSONObjAttrToNumber.toInt(jsonObject, "uv"));
            
            // 商品浏览量 - impressionNum
            this.setSpxglll(FastJSONObjAttrToNumber.toInt(jsonObject, "impressionNum"));
            
            // 支付金额 - goodsActureAmt
            this.setSpxgzfje(FastJSONObjAttrToNumber.toDouble(jsonObject, "goodsActureAmt"));
            
            // 支付买家数 - userNum
            this.setSpxgzfmjs(FastJSONObjAttrToNumber.toInt(jsonObject, "userNum"));
            
            // 支付转化率 - 需要计算：支付买家数/访客数 * 100
            if (this.getSpxgfks() > 0) {
                this.setSpxgzfzhl(MathUtil.multipy(
                    MathUtil.divide(this.getSpxgzfmjs(), this.getSpxgfks()),
                    100
                ));
            }
            
            // 退款金额 - returnGoodsAmt
            this.setSpxgtkje(FastJSONObjAttrToNumber.toDouble(jsonObject, "returnGoodsAmt"));
            
            // 支付件数 - goodsActureNum
            this.setSpxgzfjs(FastJSONObjAttrToNumber.toInt(jsonObject, "goodsActureNum"));

            // 设置唯享客相关字段
            // 唯享客销售额
            this.setWxkxse(FastJSONObjAttrToNumber.toDouble(jsonObject, "wxkStrategyGoodsActureAmt"));
            // 唯享客成本
            this.setWxkcb(FastJSONObjAttrToNumber.toDouble(jsonObject, "wxkStrategyCostAmt"));
            // 唯享客ROI - 计算公式：(销售额-成本)/成本 * 100
            if (this.getWxkcb() > 0) {
                double profit = MathUtil.minus(this.getWxkxse(), this.getWxkcb());
                this.setWxkroi(MathUtil.multipy(
                    MathUtil.divide(profit, this.getWxkcb()),
                    100
                ));
            }

        } catch (Exception e) {
            logger.error("SqliteWphSpxgSp setValues error: " + e.getMessage(), e);
        }
    }

    @Override
    public boolean compare(SqliteSp sqliteSp) {
        if (!(sqliteSp instanceof SqliteWphSpxgSp)) {
            return false;
        }

        JSONObject jsonObject = JSONObject.parseObject(sqliteSp.getOtherOrDefault());
        // 只比较唯享客相关字段
        return MathUtil.minus(this.getWxkxse(), FastJSONObjAttrToNumber.toDouble(jsonObject, "wxkxse")) == 0 &&
               MathUtil.minus(this.getWxkcb(), FastJSONObjAttrToNumber.toDouble(jsonObject, "wxkcb")) == 0;
    }


}
