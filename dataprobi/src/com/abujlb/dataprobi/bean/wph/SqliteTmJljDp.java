package com.abujlb.dataprobi.bean.wph;


import com.abujlb.dataprobi.bean.AbstractSqliteDp;
import com.abujlb.dataprobi.bean.SqliteDp;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.abujlb.dataprobi.util.MathUtil;
import com.alibaba.fastjson.JSONObject;

/**
 * target-max奖励金
 */
public class SqliteTmJljDp extends AbstractSqliteDp {

    private double tmjlj;//target-max

    public double getTmjlj() {
        return tmjlj;
    }

    public void setTmjlj(double tmjlj) {
        this.tmjlj = tmjlj;
    }

    @Override
    public <T extends AbstractSqliteDp> void setValues(String[] tableStoreColumnValues) {
        String value0 = tableStoreColumnValues[0];
        if (value0 != null) {
            JSONObject jsonObject = JSONObject.parseObject(value0);
            this.tmjlj = -MathUtil.changeF2Y(FastJSONObjAttrToNumber.toString(jsonObject, "totalBalance"));
        }
    }


    @Override
    public boolean compare(SqliteDp sqliteDp) {
        if (sqliteDp == null) {
            return false;
        }

        JSONObject jsonObject = JSONObject.parseObject(sqliteDp.getOtherOrDefault());

        return FastJSONObjAttrToNumber.toDouble(jsonObject, "tmjlj") == this.getTmjlj();
    }
}
