package com.abujlb.dataprobi.bean.wph;

import com.abujlb.dataprobi.annotations.UpdateSycmcljzrqIgnore;
import com.abujlb.dataprobi.bean.DataprobiBaseMsgBean;
import com.alibaba.fastjson.JSON;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/10
 */
public class DataprobiWphMsg extends DataprobiBaseMsgBean {

    //商品效果
    private List<String> spxg;
    //商品效果-店铺维度
    private List<String> spxgdp;
    //常态商品-商品维度
    private List<String> ctsplb;

    //唯品会-唯享客奖励金
    @UpdateSycmcljzrqIgnore
    private List<String> wxkjljdp;

    //唯品会-唯享客奖佣金服务费-预估花费
    @UpdateSycmcljzrqIgnore
    private List<String> wxkyjyghf;

    //唯品会-站外广告-巨量引擎奖励金
    @UpdateSycmcljzrqIgnore
    private List<String> zwjlyqjljdp;

    //唯品会-站外广告-爱奇艺奖励金
    @UpdateSycmcljzrqIgnore
    private List<String> zwaqyjljdp;

    //唯品会-站内广告-奖励金
    @UpdateSycmcljzrqIgnore
    private List<String> znggjljdp;

    //唯品会-targetmax-奖励金
    @UpdateSycmcljzrqIgnore
    private List<String> tmjljdp;

    //唯品会-营销平台奖励金
    @UpdateSycmcljzrqIgnore
    private List<String> yxpthfdp;



    //店铺账户金额
    private List<String> money;

    public List<String> getMoney() {
        return money;
    }

    public void setMoney(List<String> money) {
        this.money = money;
    }

    public List<String> getSpxg() {
        return spxg;
    }

    public void setSpxg(List<String> spxg) {
        this.spxg = spxg;
    }

    public List<String> getSpxgdp() {
        return spxgdp;
    }

    public void setSpxgdp(List<String> spxgdp) {
        this.spxgdp = spxgdp;
    }

    public List<String> getCtsplb() {
        return ctsplb;
    }

    public void setCtsplb(List<String> ctsplb) {
        this.ctsplb = ctsplb;
    }

    public List<String> getWxkjljdp() {
        return wxkjljdp;
    }

    public void setWxkjljdp(List<String> wxkjljdp) {
        this.wxkjljdp = wxkjljdp;
    }

    public List<String> getZwjlyqjljdp() {
        return zwjlyqjljdp;
    }

    public void setZwjlyqjljdp(List<String> zwjlyqjljdp) {
        this.zwjlyqjljdp = zwjlyqjljdp;
    }

    public List<String> getZwaqyjljdp() {
        return zwaqyjljdp;
    }

    public void setZwaqyjljdp(List<String> zwaqyjljdp) {
        this.zwaqyjljdp = zwaqyjljdp;
    }

    public List<String> getWxkyjyghf() {
        return wxkyjyghf;
    }

    public void setWxkyjyghf(List<String> wxkyjyghf) {
        this.wxkyjyghf = wxkyjyghf;
    }

    public List<String> getZnggjljdp() {
        return znggjljdp;
    }

    public void setZnggjljdp(List<String> znggjljdp) {
        this.znggjljdp = znggjljdp;
    }

    public List<String> getTmjljdp() {
        return tmjljdp;
    }

    public void setTmjljdp(List<String> tmjljdp) {
        this.tmjljdp = tmjljdp;
    }

    public List<String> getYxpthfdp() {
        return yxpthfdp;
    }

    public void setYxpthfdp(List<String> yxpthfdp) {
        this.yxpthfdp = yxpthfdp;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }

    public void fillNullList() throws InvocationTargetException, IllegalAccessException {
        Method[] methods = this.getClass().getMethods();
        for (Method method : methods) {
            if (method.getName().startsWith("get")) {
                Object value = method.invoke(this);
                if (value == null) {
                    Method setMethod = null;
                    try {
                        setMethod = this.getClass().getMethod("s" + method.getName().substring(1), List.class);
                        setMethod.invoke(this, new ArrayList<>());
                    } catch (NoSuchMethodException ignored) {
                    }
                }
            }
        }
    }
}
