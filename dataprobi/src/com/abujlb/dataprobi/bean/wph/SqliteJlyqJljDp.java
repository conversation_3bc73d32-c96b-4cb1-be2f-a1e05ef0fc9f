package com.abujlb.dataprobi.bean.wph;


import com.abujlb.dataprobi.bean.AbstractSqliteDp;
import com.abujlb.dataprobi.bean.SqliteDp;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.abujlb.dataprobi.util.MathUtil;
import com.alibaba.fastjson.JSONObject;

/**
 * 站外巨量引擎奖励金
 */
public class SqliteJlyqJljDp extends AbstractSqliteDp {

    private double zwjlyqjlj;//站外巨量引擎奖励金

    public double getZwjlyqjlj() {
        return zwjlyqjlj;
    }

    public void setZwjlyqjlj(double zwjlyqjlj) {
        this.zwjlyqjlj = zwjlyqjlj;
    }

    @Override
    public <T extends AbstractSqliteDp> void setValues(String[] tableStoreColumnValues) {
        String value0 = tableStoreColumnValues[0];
        if (value0 != null) {
            JSONObject jsonObject = JSONObject.parseObject(value0);
            this.zwjlyqjlj = -MathUtil.changeF2Y(FastJSONObjAttrToNumber.toString(jsonObject, "totalBalance"));
        }
    }


    @Override
    public boolean compare(SqliteDp sqliteDp) {
        if (sqliteDp == null) {
            return false;
        }

        JSONObject jsonObject = JSONObject.parseObject(sqliteDp.getOtherOrDefault());

        return FastJSONObjAttrToNumber.toDouble(jsonObject, "zwjlyqjlj") == this.getZwjlyqjlj();
    }
}
