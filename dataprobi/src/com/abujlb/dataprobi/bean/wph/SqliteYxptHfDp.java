package com.abujlb.dataprobi.bean.wph;


import com.abujlb.dataprobi.bean.AbstractSqliteDp;
import com.abujlb.dataprobi.bean.SqliteDp;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.abujlb.dataprobi.util.MathUtil;
import com.alibaba.fastjson.JSONObject;

/**
 * 营销平台花费
 */
public class SqliteYxptHfDp extends AbstractSqliteDp {

    private double zwjlyqhf;//站外巨量引擎花费

    private double zwaqyhf;//站外广告-爱奇艺花费

    private double tmhf;//target-max-花费

    private double znwzzhf;//站内广告-唯智战花费

    private double znwcdhf;//站内广告-唯触点花费

    private double znwzdhf;//站内广告-唯直达花费

    public double getZwjlyqhf() {
        return zwjlyqhf;
    }

    public void setZwjlyqhf(double zwjlyqhf) {
        this.zwjlyqhf = zwjlyqhf;
    }

    public double getZwaqyhf() {
        return zwaqyhf;
    }

    public void setZwaqyhf(double zwaqyhf) {
        this.zwaqyhf = zwaqyhf;
    }

    public double getZnwzzhf() {
        return znwzzhf;
    }

    public void setZnwzzhf(double znwzzhf) {
        this.znwzzhf = znwzzhf;
    }

    public double getZnwcdhf() {
        return znwcdhf;
    }

    public void setZnwcdhf(double znwcdhf) {
        this.znwcdhf = znwcdhf;
    }

    public double getZnwzdhf() {
        return znwzdhf;
    }

    public void setZnwzdhf(double znwzdhf) {
        this.znwzdhf = znwzdhf;
    }

    public double getTmhf() {
        return tmhf;
    }

    public void setTmhf(double tmhf) {
        this.tmhf = tmhf;
    }

    @Override
    public <T extends AbstractSqliteDp> void setValues(String[] tableStoreColumnValues) {
        String value0 = tableStoreColumnValues[0];
        if (value0 != null) {
            JSONObject jsonObject = JSONObject.parseObject(value0);
            this.zwjlyqhf = MathUtil.changeF2Y(FastJSONObjAttrToNumber.toString(jsonObject, "zwjlyqhf"));
            this.zwaqyhf = MathUtil.changeF2Y(FastJSONObjAttrToNumber.toString(jsonObject, "zwaqyhf"));
            this.tmhf = MathUtil.changeF2Y(FastJSONObjAttrToNumber.toString(jsonObject, "tmhf"));
            this.znwzzhf = MathUtil.changeF2Y(FastJSONObjAttrToNumber.toString(jsonObject, "znwzzhf"));
            this.znwcdhf = MathUtil.changeF2Y(FastJSONObjAttrToNumber.toString(jsonObject, "znwcdhf"));
            this.znwzdhf = MathUtil.changeF2Y(FastJSONObjAttrToNumber.toString(jsonObject, "znwzdhf"));

        }
    }


    @Override
    public boolean compare(SqliteDp sqliteDp) {
        if (sqliteDp == null) {
            return false;
        }

        JSONObject jsonObject = JSONObject.parseObject(sqliteDp.getOtherOrDefault());

        return FastJSONObjAttrToNumber.toDouble(jsonObject, "zwjlyqhf") == this.getZwjlyqhf()
                && FastJSONObjAttrToNumber.toDouble(jsonObject, "zwaqyhf") == this.getZwaqyhf()
                && FastJSONObjAttrToNumber.toDouble(jsonObject, "tmhf") == this.getTmhf()
                && FastJSONObjAttrToNumber.toDouble(jsonObject, "znwzzhf") == this.getZnwzzhf()
                && FastJSONObjAttrToNumber.toDouble(jsonObject, "znwcdhf") == this.getZnwcdhf()
                && FastJSONObjAttrToNumber.toDouble(jsonObject, "znwzdhf") == this.getZnwzdhf();
    }
}
