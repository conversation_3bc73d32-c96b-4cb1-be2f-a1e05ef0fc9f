package com.abujlb.dataprobi.bean.wph;


import com.abujlb.dataprobi.bean.AbstractSqliteDp;
import com.abujlb.dataprobi.bean.SqliteDp;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.abujlb.dataprobi.util.MathUtil;
import com.alibaba.fastjson.JSONObject;

/**
 * 营销平台花费
 */
public class SqliteJlyqHfDp extends AbstractSqliteDp {

    private double zwjlyqhf;//站外巨量引擎花费

    public double getZwjlyqhf() {
        return zwjlyqhf;
    }

    public void setZwjlyqhf(double zwjlyqhf) {
        this.zwjlyqhf = zwjlyqhf;
    }

    @Override
    public <T extends AbstractSqliteDp> void setValues(String[] tableStoreColumnValues) {
        String value0 = tableStoreColumnValues[0];
        if (value0 != null) {
            JSONObject jsonObject = JSONObject.parseObject(value0);
            this.zwjlyqhf = MathUtil.changeF2Y(FastJSONObjAttrToNumber.toString(jsonObject, "cost"));
        }
    }


    @Override
    public boolean compare(SqliteDp sqliteDp) {
        if (sqliteDp == null) {
            return false;
        }

        JSONObject jsonObject = JSONObject.parseObject(sqliteDp.getOtherOrDefault());

        return FastJSONObjAttrToNumber.toDouble(jsonObject, "zwjlyqhf") == this.getZwjlyqhf();
    }
}
