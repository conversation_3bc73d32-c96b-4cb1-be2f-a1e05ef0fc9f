package com.abujlb.dataprobi.bean.wph;


import com.abujlb.dataprobi.bean.AbstractSqliteDp;
import com.abujlb.dataprobi.bean.SqliteDp;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.abujlb.dataprobi.util.MathUtil;
import com.alibaba.fastjson.JSONObject;

/**
 * 站外爱奇艺奖励金
 */
public class SqliteAqyJljDp extends AbstractSqliteDp {

    private double zwaqyjlj;//站外爱奇艺奖励金

    public double getZwaqyjlj() {
        return zwaqyjlj;
    }

    public void setZwaqyjlj(double zwaqyjlj) {
        this.zwaqyjlj = zwaqyjlj;
    }

    @Override
    public <T extends AbstractSqliteDp> void setValues(String[] tableStoreColumnValues) {
        String value0 = tableStoreColumnValues[0];
        if (value0 != null) {
            JSONObject jsonObject = JSONObject.parseObject(value0);
            this.zwaqyjlj = -MathUtil.changeF2Y(FastJSONObjAttrToNumber.toString(jsonObject, "totalBalance"));
        }
    }


    @Override
    public boolean compare(SqliteDp sqliteDp) {
        if (sqliteDp == null) {
            return false;
        }

        JSONObject jsonObject = JSONObject.parseObject(sqliteDp.getOtherOrDefault());

        return FastJSONObjAttrToNumber.toDouble(jsonObject, "zwaqyjlj") == this.getZwaqyjlj();
    }
}
