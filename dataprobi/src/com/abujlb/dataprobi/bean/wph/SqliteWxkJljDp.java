package com.abujlb.dataprobi.bean.wph;


import com.abujlb.dataprobi.bean.AbstractSqliteDp;
import com.abujlb.dataprobi.bean.SqliteDp;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.abujlb.dataprobi.util.MathUtil;
import com.alibaba.fastjson.JSONObject;

/**
 * 唯享客奖励金
 */
public class SqliteWxkJljDp extends AbstractSqliteDp {

    private double wxkjlj;//唯享客奖励金

    public double getWxkjlj() {
        return wxkjlj;
    }

    public void setWxkjlj(double wxkjlj) {
        this.wxkjlj = wxkjlj;
    }

    @Override
    public <T extends AbstractSqliteDp> void setValues(String[] tableStoreColumnValues) {
        String value0 = tableStoreColumnValues[0];
        if (value0 != null) {
            JSONObject jsonObject = JSONObject.parseObject(value0);
            this.wxkjlj = -MathUtil.changeF2Y(FastJSONObjAttrToNumber.toString(jsonObject, "totalBalance"));
        }
    }


    @Override
    public boolean compare(SqliteDp sqliteDp) {
        if (sqliteDp == null) {
            return false;
        }

        JSONObject jsonObject = JSONObject.parseObject(sqliteDp.getOtherOrDefault());

        return FastJSONObjAttrToNumber.toDouble(jsonObject, "wxkjlj") == this.getWxkjlj();
    }
}
