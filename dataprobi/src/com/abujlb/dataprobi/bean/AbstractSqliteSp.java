package com.abujlb.dataprobi.bean;

import com.alibaba.fastjson.JSONObject;
import com.csvreader.CsvReader;

import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2022/6/30 10:32
 */
public abstract class AbstractSqliteSp extends SqliteSp {

    public AbstractSqliteSp() {
    }

    /**
     * @param t
     * @param <T>
     */
    public <T> void setValues(T t) {
    }

    public void setValues(JSONObject jsonObject) {
    }

    public void setValues( CsvReader cr ) throws IOException {}

    public <T extends AbstractSqliteSp> void plus(T t) {
    }

    /**
     * 比对数据
     *
     * @param sqliteSp
     * @return true比对成功 false比对成功或者sqliteSp为空
     */
    public boolean compare(SqliteSp sqliteSp) {
        return true;
    }
}
