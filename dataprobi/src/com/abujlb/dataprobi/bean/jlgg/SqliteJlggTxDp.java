package com.abujlb.dataprobi.bean.jlgg;

import com.abujlb.dataprobi.bean.AbstractSqliteDp;

/**
 * <AUTHOR>
 * @date 2024/11/06
 */
public class SqliteJlggTxDp extends AbstractSqliteDp {

    private double txjlggjjgghf;//巨量广告-竞价广告花费
    private double txjlggppgghf;//巨量广告-品牌广告花费
    private double txjlqcjjggqytgtzbjhf;//巨量千川-竞价广告-全域推广-推直播间
    private double txjlqcjjggqytgtsphf;//巨量千川-竞价广告-全域推广-推商品
    private double txjlqcjjggbztgtzbjhf;//巨量千川-竞价广告-标准推广-推直播间
    private double txjlqcjjggbztgtsphf;//巨量千川-竞价广告-标准推广-推商品
    private double txjlqcppgghf;//巨量千川-品牌广告

    public SqliteJlggTxDp() {
    }

    public double getTxjlggjjgghf() {
        return txjlggjjgghf;
    }

    public void setTxjlggjjgghf(double txjlggjjgghf) {
        this.txjlggjjgghf = txjlggjjgghf;
    }

    public double getTxjlggppgghf() {
        return txjlggppgghf;
    }

    public void setTxjlggppgghf(double txjlggppgghf) {
        this.txjlggppgghf = txjlggppgghf;
    }

    public double getTxjlqcjjggqytgtzbjhf() {
        return txjlqcjjggqytgtzbjhf;
    }

    public void setTxjlqcjjggqytgtzbjhf(double txjlqcjjggqytgtzbjhf) {
        this.txjlqcjjggqytgtzbjhf = txjlqcjjggqytgtzbjhf;
    }

    public double getTxjlqcjjggqytgtsphf() {
        return txjlqcjjggqytgtsphf;
    }

    public void setTxjlqcjjggqytgtsphf(double txjlqcjjggqytgtsphf) {
        this.txjlqcjjggqytgtsphf = txjlqcjjggqytgtsphf;
    }

    public double getTxjlqcjjggbztgtzbjhf() {
        return txjlqcjjggbztgtzbjhf;
    }

    public void setTxjlqcjjggbztgtzbjhf(double txjlqcjjggbztgtzbjhf) {
        this.txjlqcjjggbztgtzbjhf = txjlqcjjggbztgtzbjhf;
    }

    public double getTxjlqcjjggbztgtsphf() {
        return txjlqcjjggbztgtsphf;
    }

    public void setTxjlqcjjggbztgtsphf(double txjlqcjjggbztgtsphf) {
        this.txjlqcjjggbztgtsphf = txjlqcjjggbztgtsphf;
    }

    public double getTxjlqcppgghf() {
        return txjlqcppgghf;
    }

    public void setTxjlqcppgghf(double txjlqcppgghf) {
        this.txjlqcppgghf = txjlqcppgghf;
    }
}
