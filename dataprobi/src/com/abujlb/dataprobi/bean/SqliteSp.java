package com.abujlb.dataprobi.bean;

import com.abujlb.dataprobi.constant.DataprobiConst;
import com.abujlb.dataprobi.util.MathUtil;
import com.alibaba.fastjson.JSONObject;
import org.apache.log4j.Logger;

import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022/5/12 10:03
 */
public class SqliteSp {

    public static final Logger logger = Logger.getLogger(SqliteSp.class);

    //-----------主键---------------//
    protected String qd_userid_bbid;

    protected String rq;

    //-----------商品基本信息--------------//
    //宝贝名称
    protected transient String bbmc;
    //宝贝图片
    protected transient String img;
    //店铺名称
    protected transient String dpmc;

    //------------重要信息--------------//
    //宝贝id
    protected String bbid;
    //用户id
    protected int yhid;
    //渠道
    protected String qd;
    //店铺userid
    protected String userid;


    //------------不同渠道共有的数据--------------//
    //商品访客数
    protected int spxgfks;
    //商品浏览量
    protected int spxglll;
    //支付金额
    protected double spxgzfje;
    //支付买家数
    protected int spxgzfmjs;
    //支付转化率
    protected double spxgzfzhl;
    //退款金额
    protected double spxgtkje;
    //支付件数
    protected int spxgzfjs;

    //------------不同渠道的差异性数据字段存入JSON中--------------//
    private String other;
    //-------------------汇总部分--------------------------------//
    //推广花费  具体计算规则，参考每个渠道的汇总process里的calTghf(SqliteSp sqliteSp)方法
    private double tghf;
    //推广花费详情  参考tghf字段
    private String tghfxq;

    public <T extends SqliteSp> SqliteSp(T t) {
        this.setQd_userid_bbid(t.getQd_userid_bbid());
        this.setRq(t.getRq());
        if (t.getDpmc() != null) {
            this.setDpmc(t.getDpmc());
        }
        this.setBbid(t.getBbid());
        if (t.getBbmc() != null) {
            this.setBbmc(t.getBbmc());
        }
        if (t.getImg() != null) {
            this.setImg(t.getImg());
        }
        this.setUserid(t.getUserid());
        this.setYhid(t.getYhid());
        this.setQd(t.getQd());

        if (t.getSpxgfks() != 0) {
            this.setSpxgfks(t.getSpxgfks());
        }
        if (t.getSpxglll() != 0) {
            this.setSpxglll(t.getSpxglll());
        }
        if (t.getSpxgzfje() != 0) {
            this.setSpxgzfje(t.getSpxgzfje());
        }
        if (t.getSpxgzfmjs() != 0) {
            this.setSpxgzfmjs(t.getSpxgzfmjs());
        }
        if (t.getSpxgzfzhl() != 0) {
            this.setSpxgzfzhl(t.getSpxgzfzhl());
        }
        if (t.getSpxgtkje() != 0) {
            this.setSpxgtkje(t.getSpxgtkje());
        }
        if (t.getSpxgzfjs() != 0) {
            this.setSpxgzfjs(t.getSpxgzfjs());
        }
        JSONObject tJSONObject = JSONObject.parseObject(DataprobiConst.GSON2.toJson(t));
        this.setOther(tJSONObject.toJSONString());
    }

    public SqliteSp(BiYhdp biYhdp, String bbid) {
        this.setQd_userid_bbid(biYhdp.getQd() + "_" + biYhdp.getUserid() + "_" + bbid);
        this.setDpmc(biYhdp.getDpmc());
        this.setBbid(bbid);
        this.setUserid(biYhdp.getUserid());
        this.setYhid(biYhdp.getYhid());
        this.setQd(biYhdp.getQd());

    }

    public SqliteSp() {

    }

    public String getDpmc() {
        return dpmc == null ? "" : dpmc;
    }

    public void setDpmc(String dpmc) {
        this.dpmc = dpmc;
    }

    public String getBbmc() {
        return bbmc == null ? "" : bbmc;
    }

    public void setBbmc(String bbmc) {
        this.bbmc = bbmc;
    }

    public String getImg() {
        return img == null ? "" : img;
    }

    public void setImg(String img) {
        this.img = img;
    }

    public String getQd_userid_bbid() {
        return qd_userid_bbid;
    }

    public void setQd_userid_bbid(String qd_userid_bbid) {
        this.qd_userid_bbid = qd_userid_bbid;
    }

    public String getBbid() {
        return bbid;
    }

    public void setBbid(String bbid) {
        this.bbid = bbid;
    }

    public int getYhid() {
        return yhid;
    }

    public void setYhid(int yhid) {
        this.yhid = yhid;
    }

    public String getUserid() {
        return userid;
    }

    public void setUserid(String userid) {
        this.userid = userid;
    }

    public String getRq() {
        return rq;
    }

    public void setRq(String rq) {
        this.rq = rq;
    }

    public String getQd() {
        return qd;
    }

    public void setQd(String qd) {
        this.qd = qd;
    }

    public int getSpxgfks() {
        return spxgfks;
    }

    public void setSpxgfks(int spxgfks) {
        this.spxgfks = spxgfks;
    }

    public int getSpxglll() {
        return spxglll;
    }

    public void setSpxglll(int spxglll) {
        this.spxglll = spxglll;
    }

    public double getSpxgzfje() {
        return spxgzfje;
    }

    public void setSpxgzfje(double spxgzfje) {
        this.spxgzfje = spxgzfje;
    }

    public int getSpxgzfmjs() {
        return spxgzfmjs;
    }

    public void setSpxgzfmjs(int spxgzfmjs) {
        this.spxgzfmjs = spxgzfmjs;
    }

    public double getSpxgzfzhl() {
        return spxgzfzhl;
    }

    public void setSpxgzfzhl(double spxgzfzhl) {
        this.spxgzfzhl = spxgzfzhl;
    }

    public double getSpxgtkje() {
        return spxgtkje;
    }

    public void setSpxgtkje(double spxgtkje) {
        this.spxgtkje = spxgtkje;
    }

    public int getSpxgzfjs() {
        return spxgzfjs;
    }

    public void setSpxgzfjs(int spxgzfjs) {
        this.spxgzfjs = spxgzfjs;
    }

    public String getOther() {
        return other == null ? "{}" : other;
    }

    public void setOther(String other) {
        this.other = other;
    }

    public double getTghf() {
        return tghf;
    }

    public void setTghf(double tghf) {
        this.tghf = tghf;
    }

    public String getTghfxq() {
        return tghfxq == null ? "{}" : tghfxq;
    }

    public void setTghfxq(String tghfxq) {
        this.tghfxq = tghfxq;
    }

    public <T extends SqliteSp> void adapter(T t) {
        if (t.getSpxglll() != 0) {
            this.setSpxglll(t.getSpxglll());
        }
        if (t.getSpxgfks() != 0) {
            this.setSpxgfks(t.getSpxgfks());
        }
        if (t.getSpxgzfje() != 0) {
            this.setSpxgzfje(t.getSpxgzfje());
        }
        if (t.getSpxgzfmjs() != 0) {
            this.setSpxgzfmjs(t.getSpxgzfmjs());
        }
        if (t.getSpxgzfzhl() != 0) {
            this.setSpxgzfzhl(t.getSpxgzfzhl());
        }
        if (t.getSpxgtkje() != 0) {
            this.setSpxgtkje(t.getSpxgtkje());
        }
        if (t.getSpxgzfjs() != 0) {
            this.setSpxgzfjs(t.getSpxgzfjs());
        }
//        if (t.getDpmc() != null && this.getDpmc() == null) {
//            this.setDpmc(t.getDpmc());
//        }
//        if (t.getBbmc() != null && this.getBbmc() == null) {
//            this.setBbmc(t.getBbmc());
//        }
//        if (t.getImg() != null && this.getImg() == null) {
//            this.setImg(t.getImg());
//        }
        if (t.getDpmc() != null && (this.getDpmc() == null || this.getDpmc().isEmpty())) {
            this.setDpmc(t.getDpmc());
        }
        if (t.getBbmc() != null && (this.getBbmc() == null || this.getBbmc().isEmpty())) {
            this.setBbmc(t.getBbmc());
        }
        if (t.getImg() != null && (this.getImg() == null || this.getImg().isEmpty())) {
            this.setImg(t.getImg());
        }


        try {
            JSONObject jsonObject = JSONObject.parseObject(this.getOtherOrDefault());
            JSONObject tJSONObject = JSONObject.parseObject(DataprobiConst.GSON2.toJson(t));
            jsonObject.putAll(tJSONObject);
            this.setOther(jsonObject.toJSONString());
        } catch (Exception ignored) {
        }
    }

    public String getOtherOrDefault() {
        return other == null ? "{}" : other;
    }

    public String toInsertSql() {
        return
                String.format("insert into sycm_sp('qd_userid_bbid','rq','bbid','yhid','qd','userid' ,'spxgfks'" +
                                ",'spxglll','spxgzfje','spxgzfjs','spxgzfmjs','spxgzfzhl','spxgtkje','tghf','tghfxq','other') " +
                                " values ('%s','%s','%s','%s','%s',%s,%s,%s,%s,%s,%s,%s,%s,%s,'%s','%s')",
                        this.qd_userid_bbid, this.rq, this.bbid, this.yhid, this.qd, this.userid, this.spxgfks
                        , this.spxglll, this.spxgzfje, this.spxgzfjs, this.spxgzfmjs, this.spxgzfzhl, this.spxgtkje, this.tghf, this.getTghfxq(), this.getOtherOrDefault());
    }

    public void calTghf() {
        try {
            JSONObject jsonObject = JSONObject.parseObject(this.getOtherOrDefault());
            JSONObject tghfDetail = new JSONObject();
            double tghf = 0D;

            Set<String> keySet = jsonObject.keySet();
            for (String key : keySet) {
                if (key.endsWith("hf") && jsonObject.getDoubleValue(key) != 0) {
                    tghfDetail.put(key, jsonObject.getDouble(key));
                    tghf = MathUtil.add(tghf, jsonObject.getDouble(key));
                }
            }
            this.setTghf(tghf);
            this.setTghfxq(tghfDetail.toJSONString());
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
    }

    @Override
    public String toString() {
        return DataprobiConst.GSON.toJson(this);
    }

}
