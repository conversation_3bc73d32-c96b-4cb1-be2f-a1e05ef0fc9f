package com.abujlb.dataprobi.bean.hfshare;

import java.util.List;

public class HfShareBase {

    private int lx;
    private int yhid;
    private String qd;
    private String userid;
    //1 固定比例  0 销售额占比
    private int type;
    private String uuid ;


    private String ksrq;
    private String jsrq;
    private String gxsj;
    private String sxsj;
    private String spconfig;

    //淘系-品销宝
    private String jhmc;
    private String cymc;

    //淘系-无界-内容营销-短视频
    private String jhid;
    //冗余的 实际并无此数据键
    private String videoId;

    //dou+
    private String djid;
    private String dyh;

    //千川
    private String qch;

    //无界-店铺运营
    private String cyId ;

    private List<HfShareGoodsConfig> configList ;

    public int getLx() {
        return lx;
    }

    public void setLx(int lx) {
        this.lx = lx;
    }

    public int getYhid() {
        return yhid;
    }

    public void setYhid(int yhid) {
        this.yhid = yhid;
    }

    public String getQd() {
        return qd;
    }

    public void setQd(String qd) {
        this.qd = qd;
    }

    public String getUserid() {
        return userid;
    }

    public void setUserid(String userid) {
        this.userid = userid;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getKsrq() {
        return ksrq;
    }

    public void setKsrq(String ksrq) {
        this.ksrq = ksrq;
    }

    public String getJsrq() {
        return jsrq;
    }

    public void setJsrq(String jsrq) {
        this.jsrq = jsrq;
    }

    public String getGxsj() {
        return gxsj;
    }

    public void setGxsj(String gxsj) {
        this.gxsj = gxsj;
    }

    public String getSxsj() {
        return sxsj;
    }

    public void setSxsj(String sxsj) {
        this.sxsj = sxsj;
    }

    public String getJhmc() {
        return jhmc;
    }

    public void setJhmc(String jhmc) {
        this.jhmc = jhmc;
    }

    public String getCymc() {
        return cymc;
    }

    public void setCymc(String cymc) {
        this.cymc = cymc;
    }

    public String getJhid() {
        return jhid;
    }

    public void setJhid(String jhid) {
        this.jhid = jhid;
    }

    public String getDjid() {
        return djid;
    }

    public void setDjid(String djid) {
        this.djid = djid;
    }

    public String getDyh() {
        return dyh;
    }

    public void setDyh(String dyh) {
        this.dyh = dyh;
    }

    public String getQch() {
        return qch;
    }

    public void setQch(String qch) {
        this.qch = qch;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getSpconfig() {
        return spconfig;
    }

    public void setSpconfig(String spconfig) {
        this.spconfig = spconfig;
    }

    public List<HfShareGoodsConfig> getConfigList() {
        return configList;
    }

    public void setConfigList(List<HfShareGoodsConfig> configList) {
        this.configList = configList;
    }

    public String getVideoId() {
        return videoId;
    }

    public void setVideoId(String videoId) {
        this.videoId = videoId;
    }

    public String getCyId() {
        return cyId;
    }

    public void setCyId(String cyId) {
        this.cyId = cyId;
    }
}
