package com.abujlb.dataprobi.bean.pdd;

import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.SqliteSp;
import com.abujlb.dataprobi.interfaces.Plusable;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.abujlb.dataprobi.util.MathUtil;
import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 * @date 2023/11/3
 */
public class SqlitePddBztgSp extends AbstractSqliteSp implements Plusable<SqlitePddBztgSp> {

    //标准推广曝光量
    private int bztgbgl;
    //标准推广点击量
    private int bztgdjl;
    //标准推广成交笔数
    private int bztgcjbs;
    //标准推广成交金额
    private double bztgcjje;
    //标准推广花费
    private double bztghf;
    //标准推广点击率
    private double bztgdjlv;

    public SqlitePddBztgSp() {
    }

    public int getBztgbgl() {
        return bztgbgl;
    }

    public void setBztgbgl(int bztgbgl) {
        this.bztgbgl = bztgbgl;
    }

    public int getBztgdjl() {
        return bztgdjl;
    }

    public void setBztgdjl(int bztgdjl) {
        this.bztgdjl = bztgdjl;
    }

    public int getBztgcjbs() {
        return bztgcjbs;
    }

    public void setBztgcjbs(int bztgcjbs) {
        this.bztgcjbs = bztgcjbs;
    }

    public double getBztgcjje() {
        return bztgcjje;
    }

    public void setBztgcjje(double bztgcjje) {
        this.bztgcjje = bztgcjje;
    }

    public double getBztghf() {
        return bztghf;
    }

    public void setBztghf(double bztghf) {
        this.bztghf = bztghf;
    }

    public double getBztgdjlv() {
        return bztgdjlv;
    }

    public void setBztgdjlv(double bztgdjlv) {
        this.bztgdjlv = bztgdjlv;
    }

    @Override
    public void setValues(JSONObject jsonObject) {
        this.bbid = FastJSONObjAttrToNumber.toString(jsonObject.getJSONObject("externalFieldValues"), "goodsId");
        this.bztgbgl = FastJSONObjAttrToNumber.toInt(jsonObject, "impression");
        this.bztgdjl = FastJSONObjAttrToNumber.toInt(jsonObject, "click");
        this.bztgcjbs = FastJSONObjAttrToNumber.toInt(jsonObject, "orderNum");
        this.bztgcjje = MathUtil.divide1000(FastJSONObjAttrToNumber.toDouble(jsonObject, "gmv"));
        this.bztghf = MathUtil.divide1000(FastJSONObjAttrToNumber.toDouble(jsonObject, "spend"));
        this.bztgdjlv = FastJSONObjAttrToNumber.toDouble(jsonObject, "ctr");
    }

    @Override
    public void plus(SqlitePddBztgSp temp) {
        this.bztgbgl += temp.getBztgbgl();
        this.bztgdjl += temp.getBztgdjl();
        this.bztgcjbs += temp.getBztgcjbs();
        this.bztgcjje = MathUtil.add(this.bztgcjje, temp.getBztgcjje());
        this.bztghf = MathUtil.add(this.bztghf, temp.getBztghf());
        this.bztgdjlv = MathUtil.calZhlv(this.bztgdjl, this.bztgbgl);
    }

    @Override
    public boolean compare(SqliteSp sqliteSp) {
        if (sqliteSp == null) {
            return false;
        }

        JSONObject jsonObject = JSONObject.parseObject(sqliteSp.getOtherOrDefault());
        return FastJSONObjAttrToNumber.toInt(jsonObject, "bztgbgl") == this.getBztgbgl()
                && FastJSONObjAttrToNumber.toInt(jsonObject, "bztgdjl") == this.getBztgdjl()
                && FastJSONObjAttrToNumber.toInt(jsonObject, "bztgcjbs") == this.getBztgcjbs()
                && FastJSONObjAttrToNumber.toDouble(jsonObject, "bztgcjje") == this.getBztgcjje()
                && FastJSONObjAttrToNumber.toDouble(jsonObject, "bztghf") == this.getBztghf();
    }
}
