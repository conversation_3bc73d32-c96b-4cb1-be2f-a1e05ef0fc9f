package com.abujlb.dataprobi.bean.pdd;

import com.abujlb.dataprobi.bean.AbstractSqliteDp;
import com.abujlb.dataprobi.bean.SqliteDp;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.abujlb.dataprobi.util.MathUtil;
import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 * @date 2022/6/10 10:09
 */
public class SqlitePddSpxgDp extends AbstractSqliteDp {

    //客单价
    private double spxgkdj;
    //支付子订单数
    private int spxgzfdds;
    //uv价值
    private double spxguvjz;
    //店铺关注用户数
    private int spxggzyhs;
    //老买家占比
    private double spxglmjzb;
    //商品效果收藏人数
    private int spxgscrs;

    public SqlitePddSpxgDp() {
    }

    public double getSpxgkdj() {
        return spxgkdj;
    }

    public void setSpxgkdj(double spxgkdj) {
        this.spxgkdj = spxgkdj;
    }

    public int getSpxgzfdds() {
        return spxgzfdds;
    }

    public void setSpxgzfdds(int spxgzfdds) {
        this.spxgzfdds = spxgzfdds;
    }

    public double getSpxguvjz() {
        return spxguvjz;
    }

    public void setSpxguvjz(double spxguvjz) {
        this.spxguvjz = spxguvjz;
    }

    public int getSpxggzyhs() {
        return spxggzyhs;
    }

    public void setSpxggzyhs(int spxggzyhs) {
        this.spxggzyhs = spxggzyhs;
    }

    public double getSpxglmjzb() {
        return spxglmjzb;
    }

    public void setSpxglmjzb(double spxglmjzb) {
        this.spxglmjzb = spxglmjzb;
    }

    public int getSpxgscrs() {
        return spxgscrs;
    }

    public void setSpxgscrs(int spxgscrs) {
        this.spxgscrs = spxgscrs;
    }

    @Override
    public <T extends AbstractSqliteDp> void setValues(String[] tableStoreColumnValues) {
        if (tableStoreColumnValues == null || tableStoreColumnValues.length == 0) {
            return;
        }
        if (tableStoreColumnValues[0] != null) {
            //流量看板数据
            JSONObject jsonObject = JSONObject.parseObject(tableStoreColumnValues[0]);
            this.spxgfks = FastJSONObjAttrToNumber.toInt(jsonObject, "uv");
            this.spxglll = FastJSONObjAttrToNumber.toInt(jsonObject, "pv");
            this.spxgspfks = FastJSONObjAttrToNumber.toInt(jsonObject, "guv");
            this.spxgsplll = FastJSONObjAttrToNumber.toInt(jsonObject, "gpv");
            this.spxgzfje = FastJSONObjAttrToNumber.toDouble(jsonObject, "payOrdrAmt");
            this.spxgzfmjs = FastJSONObjAttrToNumber.toInt(jsonObject, "payOrdrUsrCnt");
            this.spxgzfzhl = MathUtil.divide100(FastJSONObjAttrToNumber.toDouble(jsonObject, "payUvRto"));
            this.spxgzfdds = FastJSONObjAttrToNumber.toInt(jsonObject, "payOrdrCnt");
            this.spxgkdj = FastJSONObjAttrToNumber.toDouble(jsonObject, "payOrdrAup");
            this.spxguvjz = FastJSONObjAttrToNumber.toDouble(jsonObject, "uvCfmVal");
        }
        if (tableStoreColumnValues[1] != null) {
            //交易数据
            JSONObject jsonObject = JSONObject.parseObject(tableStoreColumnValues[1]);
            this.spxggzyhs = FastJSONObjAttrToNumber.toInt(jsonObject, "mallFavCnt");
            this.spxglmjzb = MathUtil.divide100(FastJSONObjAttrToNumber.toDouble(jsonObject, "rpayOrdrUsrRto"));

            if (this.spxgzfje == 0) {
                this.spxgzfje = FastJSONObjAttrToNumber.toDouble(jsonObject, "payOrdrAmt");
            }
            if (this.spxgzfmjs == 0) {
                this.spxgzfmjs = FastJSONObjAttrToNumber.toInt(jsonObject, "payOrdrUsrCnt");
            }
            if (this.spxgkdj == 0) {
                this.spxgkdj = FastJSONObjAttrToNumber.toDouble(jsonObject, "payOrdrAup");
            }
            if (this.spxgzfdds == 0) {
                this.spxgzfdds = FastJSONObjAttrToNumber.toInt(jsonObject, "payOrdrCnt");
            }
            if (this.spxgzfzhl == 0) {
                this.spxgzfzhl = MathUtil.divide100(FastJSONObjAttrToNumber.toDouble(jsonObject, "payUvRto"));
            }
        }
        if (tableStoreColumnValues[2] != null) {
            //退款数据
            JSONObject jsonObject = JSONObject.parseObject(tableStoreColumnValues[2]);
            this.spxgtkje = FastJSONObjAttrToNumber.toDouble(jsonObject, "sucRfOrdrAmt1d");
        }
        if (tableStoreColumnValues[3] != null) {
            JSONObject jsonObject = JSONObject.parseObject(tableStoreColumnValues[3]);
            this.spxgzfjs = FastJSONObjAttrToNumber.toInt(jsonObject, "zfjs");
            this.spxgscrs = FastJSONObjAttrToNumber.toInt(jsonObject, "scrs");
        }

    }

    @Override
    public boolean compare(SqliteDp sqliteDp) {
        if (sqliteDp == null) {
            return false;
        }
        JSONObject jsonObject = JSONObject.parseObject(sqliteDp.getOtherOrDefault());
        return sqliteDp.getSpxgfks() == this.getSpxgfks()
                && sqliteDp.getSpxglll() == this.getSpxglll()
                && sqliteDp.getSpxgspfks() == this.getSpxgspfks()
                && sqliteDp.getSpxgsplll() == this.getSpxgsplll()
                && sqliteDp.getSpxgtkje() == this.getSpxgtkje()
                && sqliteDp.getSpxgzfmjs() == this.getSpxgzfmjs()
                && FastJSONObjAttrToNumber.toInt(jsonObject, "spxgzfdds") == this.getSpxgzfdds()
                && sqliteDp.getSpxgzfje() == this.getSpxgzfje();
    }
}
