package com.abujlb.dataprobi.bean.pdd;

import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.SqliteSp;
import com.abujlb.dataprobi.interfaces.Plusable;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.abujlb.dataprobi.util.MathUtil;
import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 * @date 2024/3/1
 */
public class SqlitePddSptgSp extends AbstractSqliteSp implements Plusable<SqlitePddSptgSp> {

    //标准推广曝光量
    private int sptgbgl;
    //标准推广点击量
    private int sptgdjl;
    //标准推广成交笔数
    private int sptgcjbs;
    //标准推广成交金额
    private double sptgcjje;
    //标准推广花费
    private double sptghf;

    public SqlitePddSptgSp() {
    }

    public int getSptgbgl() {
        return sptgbgl;
    }

    public void setSptgbgl(int sptgbgl) {
        this.sptgbgl = sptgbgl;
    }

    public int getSptgdjl() {
        return sptgdjl;
    }

    public void setSptgdjl(int sptgdjl) {
        this.sptgdjl = sptgdjl;
    }

    public int getSptgcjbs() {
        return sptgcjbs;
    }

    public void setSptgcjbs(int sptgcjbs) {
        this.sptgcjbs = sptgcjbs;
    }

    public double getSptgcjje() {
        return sptgcjje;
    }

    public void setSptgcjje(double sptgcjje) {
        this.sptgcjje = sptgcjje;
    }

    public double getSptghf() {
        return sptghf;
    }

    public void setSptghf(double sptghf) {
        this.sptghf = sptghf;
    }

    @Override
    public void plus(SqlitePddSptgSp temp) {
        this.sptgbgl += temp.getSptgbgl();
        this.sptgdjl += temp.getSptgdjl();
        this.sptgcjbs += temp.getSptgcjbs();
        this.sptgcjje = MathUtil.add(this.sptgcjje, temp.getSptgcjje());
        this.sptghf = MathUtil.add(this.sptghf, temp.getSptghf());
    }

    @Override
    public void setValues(JSONObject jsonObject) {
        this.bbid = FastJSONObjAttrToNumber.toString(jsonObject.getJSONObject("externalFieldValues"), "goodsId");
        this.sptgbgl = FastJSONObjAttrToNumber.toInt(jsonObject, "impression");
        this.sptgdjl = FastJSONObjAttrToNumber.toInt(jsonObject, "click");
        this.sptgcjbs = FastJSONObjAttrToNumber.toInt(jsonObject, "orderNum");
        this.sptgcjje = MathUtil.divide1000(FastJSONObjAttrToNumber.toDouble(jsonObject, "gmv"));
        this.sptghf = MathUtil.divide1000(FastJSONObjAttrToNumber.toDouble(jsonObject, "spend"));
    }

    @Override
    public boolean compare(SqliteSp sqliteSp) {
        if (sqliteSp == null) {
            return false;
        }

        JSONObject jsonObject = JSONObject.parseObject(sqliteSp.getOtherOrDefault());
        return FastJSONObjAttrToNumber.toInt(jsonObject, "sptgbgl") == this.getSptgbgl()
                && FastJSONObjAttrToNumber.toInt(jsonObject, "sptgdjl") == this.getSptgbgl()
                && FastJSONObjAttrToNumber.toInt(jsonObject, "sptgcjbs") == this.getSptgcjbs()
                && FastJSONObjAttrToNumber.toDouble(jsonObject, "sptgcjje") == this.getSptgcjje()
                && FastJSONObjAttrToNumber.toDouble(jsonObject, "sptghf") == this.getSptghf();
    }
}
