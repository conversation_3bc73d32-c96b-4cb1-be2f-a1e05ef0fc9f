package com.abujlb.dataprobi.bean.pdd;

import com.abujlb.dataprobi.bean.AbstractSqliteDp;
import com.abujlb.dataprobi.bean.SqliteDp;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.abujlb.dataprobi.util.MathUtil;
import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 * @date 2023/11/3
 */
public class SqlitePddBztgDp extends AbstractSqliteDp {

    //标准推广曝光量
    private int bztgbgl;
    //标准推广点击量
    private int bztgdjl;
    //标准推广成交笔数
    private int bztgcjbs;
    //标准推广成交金额
    private double bztgcjje;
    //标准推广花费
    private double bztghf;
    //标准推广点击率
    private double bztgdjlv;

    public SqlitePddBztgDp() {
    }

    public int getBztgbgl() {
        return bztgbgl;
    }

    public void setBztgbgl(int bztgbgl) {
        this.bztgbgl = bztgbgl;
    }

    public int getBztgdjl() {
        return bztgdjl;
    }

    public void setBztgdjl(int bztgdjl) {
        this.bztgdjl = bztgdjl;
    }

    public int getBztgcjbs() {
        return bztgcjbs;
    }

    public void setBztgcjbs(int bztgcjbs) {
        this.bztgcjbs = bztgcjbs;
    }

    public double getBztgcjje() {
        return bztgcjje;
    }

    public void setBztgcjje(double bztgcjje) {
        this.bztgcjje = bztgcjje;
    }

    public double getBztghf() {
        return bztghf;
    }

    public void setBztghf(double bztghf) {
        this.bztghf = bztghf;
    }

    public double getBztgdjlv() {
        return bztgdjlv;
    }

    public void setBztgdjlv(double bztgdjlv) {
        this.bztgdjlv = bztgdjlv;
    }

    @Override
    public <T extends AbstractSqliteDp> void setValues(String[] tableStoreColumnValues) {
        if (tableStoreColumnValues[0] != null) {
            JSONObject jsonObject = JSONObject.parseObject(tableStoreColumnValues[0]);
            this.bztgbgl = FastJSONObjAttrToNumber.toInt(jsonObject, "impression");
            this.bztgdjl = FastJSONObjAttrToNumber.toInt(jsonObject, "click");
            this.bztgcjbs = FastJSONObjAttrToNumber.toInt(jsonObject, "orderNum");
            this.bztgcjje = MathUtil.divide1000(FastJSONObjAttrToNumber.toInt(jsonObject, "gmv"));
            this.bztghf = MathUtil.divide1000(FastJSONObjAttrToNumber.toInt(jsonObject, "spend"));
            this.bztgdjlv = FastJSONObjAttrToNumber.toDouble(jsonObject, "ctr");
        }
    }

    @Override
    public boolean compare(SqliteDp sqliteDp) {
        if (sqliteDp == null) {
            return false;
        }
        JSONObject jsonObject = JSONObject.parseObject(sqliteDp.getOtherOrDefault());
        return FastJSONObjAttrToNumber.toInt(jsonObject, "bztgbgl") == this.getBztgbgl()
                && FastJSONObjAttrToNumber.toInt(jsonObject, "bztgdjl") == this.getBztgdjl()
                && FastJSONObjAttrToNumber.toInt(jsonObject, "bztgcjbs") == this.getBztgcjbs()
                && FastJSONObjAttrToNumber.toDouble(jsonObject, "bztgcjje") == this.getBztgcjje()
                && FastJSONObjAttrToNumber.toDouble(jsonObject, "bztghf") == this.getBztghf();
    }
}
