package com.abujlb.dataprobi.bean.pdd;

import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.SqliteSp;
import com.abujlb.dataprobi.interfaces.Plusable;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.abujlb.dataprobi.util.MathUtil;
import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 * @date 2023/11/3
 */
public class SqlitePddPjylSp extends AbstractSqliteSp implements Plusable<SqlitePddPjylSp> {

    //评价有礼花费
    private double pddpjylhf;

    public SqlitePddPjylSp() {
    }

    public double getPddpjylhf() {
        return pddpjylhf;
    }

    public void setPddpjylhf(double pddpjylhf) {
        this.pddpjylhf = pddpjylhf;
    }

    @Override
    public void setValues(JSONObject jsonObject) {
        this.bbid = FastJSONObjAttrToNumber.toString(jsonObject, "goodsId");
        String amountStr = FastJSONObjAttrToNumber.toString(jsonObject, "reviewRewardInfoVO", "rebateAmountYuan");
        double amount = 0;
        try {
            amount = Double.parseDouble(amountStr);
        } catch (Exception e) {
            amount = 0;
        } finally {
            this.pddpjylhf = amount;
        }
    }

    @Override
    public void plus(SqlitePddPjylSp temp) {
        this.pddpjylhf = MathUtil.add(this.pddpjylhf, temp.getPddpjylhf());
    }

    @Override
    public boolean compare(SqliteSp sqliteSp) {
        if (sqliteSp == null) {
            return false;
        }

        JSONObject jsonObject = JSONObject.parseObject(sqliteSp.getOtherOrDefault());
        return FastJSONObjAttrToNumber.toDouble(jsonObject, "pddpjylhf") == this.getPddpjylhf();
    }
}
