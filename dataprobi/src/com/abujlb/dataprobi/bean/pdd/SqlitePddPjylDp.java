package com.abujlb.dataprobi.bean.pdd;

import com.abujlb.dataprobi.bean.AbstractSqliteDp;

/**
 * <AUTHOR>
 * @date 2024/6/14
 */
public class SqlitePddPjylDp extends AbstractSqliteDp {

    //评价有礼花费
    private double pddpjylhf;

    public SqlitePddPjylDp() {
    }

    public double getPddpjylhf() {
        return pddpjylhf;
    }

    public void setPddpjylhf(double pddpjylhf) {
        this.pddpjylhf = pddpjylhf;
    }

}
