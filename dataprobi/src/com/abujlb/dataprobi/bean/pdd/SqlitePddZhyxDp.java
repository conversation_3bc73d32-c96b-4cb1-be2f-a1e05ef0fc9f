package com.abujlb.dataprobi.bean.pdd;

import com.abujlb.dataprobi.bean.AbstractSqliteDp;
import com.abujlb.dataprobi.bean.SqliteDp;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.alibaba.fastjson.JSONObject;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2023/7/24
 */
public class SqlitePddZhyxDp extends AbstractSqliteDp {


    //整合营销花费
    private double zhyxhf;

    public SqlitePddZhyxDp() {
    }

    public double getZhyxhf() {
        return zhyxhf;
    }

    public void setZhyxhf(double zhyxhf) {
        this.zhyxhf = zhyxhf;
    }

    @Override
    public <T extends AbstractSqliteDp> void setValues(JSONObject jsonObject) {
        super.setValues(jsonObject);
    }

    @Override
    public <T extends AbstractSqliteDp> void setValues(String[] tableStoreColumnValues) {
        if (tableStoreColumnValues[0] != null) {
            JSONObject jsonObject = JSONObject.parseObject(tableStoreColumnValues[0]);
            String summary = jsonObject.getString("summary");
            String regexp = "整合营销\\d+(\\.\\d+)?元";
            Pattern pattern = Pattern.compile(regexp);
            Matcher m = pattern.matcher(summary);
            if (m.find()) {
                String zhyxhfStr = m.group();
                regexp = "\\d+(\\.\\d+)?";
                pattern = Pattern.compile(regexp);
                m = pattern.matcher(zhyxhfStr);
            }
            if (m.find()) {
                this.zhyxhf = Double.parseDouble(m.group());
            }
        }
    }

    @Override
    public boolean compare(SqliteDp sqliteDp) {
        if (sqliteDp == null) {
            return false;
        }
        JSONObject jsonObject = JSONObject.parseObject(sqliteDp.getOtherOrDefault());
        return FastJSONObjAttrToNumber.toDouble(jsonObject, "zhyxhf") == this.getZhyxhf();
    }
}
