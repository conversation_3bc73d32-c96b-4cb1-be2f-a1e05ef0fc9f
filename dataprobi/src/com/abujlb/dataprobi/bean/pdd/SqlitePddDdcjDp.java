package com.abujlb.dataprobi.bean.pdd;

import com.abujlb.dataprobi.bean.AbstractSqliteDp;
import com.abujlb.dataprobi.bean.SqliteDp;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.abujlb.dataprobi.util.MathUtil;
import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 * @date 2022/6/10 11:38
 */
public class SqlitePddDdcjDp extends AbstractSqliteDp {

    //多多场景曝光量
    private int ddcjbgl;
    //多多场景点击量
    private int ddcjdjl;
    //多多场景成交笔数
    private int ddcjcjbs;
    //多多场景成交金额
    private double ddcjcjje;
    //多多场景花费
    private double ddcjhf;
    //多多场景点击率
    private double ddcjdjlv;

    public SqlitePddDdcjDp() {
    }

    public int getDdcjdjl() {
        return ddcjdjl;
    }

    public void setDdcjdjl(int ddcjdjl) {
        this.ddcjdjl = ddcjdjl;
    }

    public int getDdcjbgl() {
        return ddcjbgl;
    }

    public void setDdcjbgl(int ddcjbgl) {
        this.ddcjbgl = ddcjbgl;
    }

    public int getDdcjcjbs() {
        return ddcjcjbs;
    }

    public void setDdcjcjbs(int ddcjcjbs) {
        this.ddcjcjbs = ddcjcjbs;
    }

    public double getDdcjcjje() {
        return ddcjcjje;
    }

    public void setDdcjcjje(double ddcjcjje) {
        this.ddcjcjje = ddcjcjje;
    }

    public double getDdcjhf() {
        return ddcjhf;
    }

    public void setDdcjhf(double ddcjhf) {
        this.ddcjhf = ddcjhf;
    }

    public double getDdcjdjlv() {
        return ddcjdjlv;
    }

    public void setDdcjdjlv(double ddcjdjlv) {
        this.ddcjdjlv = ddcjdjlv;
    }

    @Override
    public <T extends AbstractSqliteDp> void setValues(String[] tableStoreColumnValues) {
        if (tableStoreColumnValues[0] != null) {
            JSONObject jsonObject = JSONObject.parseObject(tableStoreColumnValues[0]);
            this.ddcjbgl = FastJSONObjAttrToNumber.toInt(jsonObject, "impression");
            this.ddcjdjl = FastJSONObjAttrToNumber.toInt(jsonObject, "click");
            this.ddcjcjbs = FastJSONObjAttrToNumber.toInt(jsonObject, "orderNum");
            this.ddcjcjje = MathUtil.divide1000(FastJSONObjAttrToNumber.toInt(jsonObject, "gmv"));
            this.ddcjhf = MathUtil.divide1000(FastJSONObjAttrToNumber.toInt(jsonObject, "spend"));
            this.ddcjdjlv = FastJSONObjAttrToNumber.toDouble(jsonObject, "ctr");
        }
    }

    @Override
    public boolean compare(SqliteDp sqliteDp) {
        if (sqliteDp == null) {
            return false;
        }
        JSONObject jsonObject = JSONObject.parseObject(sqliteDp.getOtherOrDefault());
        return FastJSONObjAttrToNumber.toInt(jsonObject, "ddcjbgl") == this.getDdcjbgl()
                && FastJSONObjAttrToNumber.toInt(jsonObject, "ddcjdjl") == this.getDdcjdjl()
                && FastJSONObjAttrToNumber.toInt(jsonObject, "ddcjcjbs") == this.getDdcjcjbs()
                && FastJSONObjAttrToNumber.toDouble(jsonObject, "ddcjcjje") == this.getDdcjcjje()
                && FastJSONObjAttrToNumber.toDouble(jsonObject, "ddcjhf") == this.getDdcjhf();
    }
}
