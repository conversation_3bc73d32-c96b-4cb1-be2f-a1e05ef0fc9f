package com.abujlb.dataprobi.bean.pdd;

import com.abujlb.dataprobi.bean.AbstractSqliteDp;
import com.abujlb.dataprobi.bean.SqliteDp;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.abujlb.dataprobi.util.MathUtil;
import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 * @date 2022/6/10 11:38
 */
public class SqlitePddDdssDp extends AbstractSqliteDp {


    //多多搜索曝光量
    private int ddssbgl;
    //多多搜索点击量
    private int ddssdjl;
    //多多搜索成交笔数
    private int ddsscjbs;
    //多多搜索成交金额
    private double ddsscjje;
    //多多搜索花费
    private double ddsshf;
    //多多搜索点击率
    private double ddssdjlv;

    public SqlitePddDdssDp() {
    }

    public int getDdssbgl() {
        return ddssbgl;
    }

    public void setDdssbgl(int ddssbgl) {
        this.ddssbgl = ddssbgl;
    }

    public int getDdssdjl() {
        return ddssdjl;
    }

    public void setDdssdjl(int ddssdjl) {
        this.ddssdjl = ddssdjl;
    }

    public int getDdsscjbs() {
        return ddsscjbs;
    }

    public void setDdsscjbs(int ddsscjbs) {
        this.ddsscjbs = ddsscjbs;
    }

    public double getDdsscjje() {
        return ddsscjje;
    }

    public void setDdsscjje(double ddsscjje) {
        this.ddsscjje = ddsscjje;
    }

    public double getDdsshf() {
        return ddsshf;
    }

    public void setDdsshf(double ddsshf) {
        this.ddsshf = ddsshf;
    }

    public double getDdssdjlv() {
        return ddssdjlv;
    }

    public void setDdssdjlv(double ddssdjlv) {
        this.ddssdjlv = ddssdjlv;
    }

    @Override
    public <T extends AbstractSqliteDp> void setValues(String[] tableStoreColumnValues) {
        if (tableStoreColumnValues[0] != null) {
            JSONObject jsonObject = JSONObject.parseObject(tableStoreColumnValues[0]);
            this.ddssbgl = FastJSONObjAttrToNumber.toInt(jsonObject, "impression");
            this.ddssdjl = FastJSONObjAttrToNumber.toInt(jsonObject, "click");
            this.ddsscjbs = FastJSONObjAttrToNumber.toInt(jsonObject, "orderNum");
            this.ddsscjje = MathUtil.divide1000(FastJSONObjAttrToNumber.toInt(jsonObject, "gmv"));
            this.ddsshf = MathUtil.divide1000(FastJSONObjAttrToNumber.toInt(jsonObject, "spend"));
            this.ddssdjlv = FastJSONObjAttrToNumber.toDouble(jsonObject, "ctr");
        }
    }

    @Override
    public boolean compare(SqliteDp sqliteDp) {
        if (sqliteDp == null) {
            return false;
        }
        JSONObject jsonObject = JSONObject.parseObject(sqliteDp.getOtherOrDefault());
        return FastJSONObjAttrToNumber.toInt(jsonObject, "ddssbgl") == this.getDdssbgl()
                && FastJSONObjAttrToNumber.toInt(jsonObject, "ddssdjl") == this.getDdssdjl()
                && FastJSONObjAttrToNumber.toInt(jsonObject, "ddsscjbs") == this.getDdsscjbs()
                && FastJSONObjAttrToNumber.toDouble(jsonObject, "ddsscjje") == this.getDdsscjje()
                && FastJSONObjAttrToNumber.toDouble(jsonObject, "ddsshf") == this.getDdsshf();
    }
}
