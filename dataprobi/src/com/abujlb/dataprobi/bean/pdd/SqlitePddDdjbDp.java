package com.abujlb.dataprobi.bean.pdd;

import com.abujlb.dataprobi.bean.AbstractSqliteDp;
import com.abujlb.dataprobi.bean.SqliteDp;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.abujlb.dataprobi.util.MathUtil;
import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 * @date 2022/6/10 11:38
 */
public class SqlitePddDdjbDp extends AbstractSqliteDp {

    /**
     * 多多进宝成交笔数
     */
    private int ddjbcjbs;
    /**
     * 多多进宝成交金额
     */
    private double ddjbcjje;
    /**
     * 多多进宝预估支付佣金（源数据单位为厘，这里已经处理为元了）
     */
    private double ddjbyj;
    /**
     * 多多进宝预估软件服务费（源数据单位为厘，这里已经处理为元了）
     */
    private double ddjbrjfwf;
    /**
     * 多多进宝花费
     */
    private double ddjbhf;


    public int getDdjbcjbs() {
        return ddjbcjbs;
    }

    public void setDdjbcjbs(int ddjbcjbs) {
        this.ddjbcjbs = ddjbcjbs;
    }

    public double getDdjbcjje() {
        return ddjbcjje;
    }

    public void setDdjbcjje(double ddjbcjje) {
        this.ddjbcjje = ddjbcjje;
    }

    public double getDdjbyj() {
        return ddjbyj;
    }

    public void setDdjbyj(double ddjbyj) {
        this.ddjbyj = ddjbyj;
    }

    public double getDdjbrjfwf() {
        return ddjbrjfwf;
    }

    public void setDdjbrjfwf(double ddjbrjfwf) {
        this.ddjbrjfwf = ddjbrjfwf;
    }

    public double getDdjbhf() {
        return ddjbhf;
    }

    public void setDdjbhf(double ddjbhf) {
        this.ddjbhf = ddjbhf;
    }

    @Override
    public <T extends AbstractSqliteDp> void setValues(String[] tableStoreColumnValues) {
        if (tableStoreColumnValues[0] != null) {
            JSONObject jsonObject = JSONObject.parseObject(tableStoreColumnValues[0]);
            this.ddjbcjbs = FastJSONObjAttrToNumber.toInt(jsonObject, "group_order_num");
            this.ddjbcjje = MathUtil.divide1000(FastJSONObjAttrToNumber.toInt(jsonObject, "group_order_amount"));
            this.ddjbyj = MathUtil.divide1000(FastJSONObjAttrToNumber.toInt(jsonObject, "fee_amount"));
            this.ddjbrjfwf = MathUtil.divide1000(FastJSONObjAttrToNumber.toInt(jsonObject, "service_fee_amount"));
            this.ddjbhf = MathUtil.add(this.ddjbyj, this.ddjbrjfwf);
        }
    }

    @Override
    public boolean compare(SqliteDp sqliteDp) {
        if (sqliteDp == null) {
            return false;
        }
        JSONObject jsonObject = JSONObject.parseObject(sqliteDp.getOtherOrDefault());
        return FastJSONObjAttrToNumber.toInt(jsonObject, "ddjbcjbs") == this.getDdjbcjbs()
                && FastJSONObjAttrToNumber.toDouble(jsonObject, "ddjbcjje") == this.getDdjbcjje()
                && FastJSONObjAttrToNumber.toDouble(jsonObject, "ddjbyj") == this.getDdjbyj()
                && FastJSONObjAttrToNumber.toDouble(jsonObject, "ddjbrjfwf") == this.getDdjbrjfwf()
                && FastJSONObjAttrToNumber.toDouble(jsonObject, "ddjbhf") == this.getDdjbhf();
    }
}
