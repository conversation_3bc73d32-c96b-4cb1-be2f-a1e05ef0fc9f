package com.abujlb.dataprobi.bean.pdd;

import com.abujlb.dataprobi.bean.AbstractSqliteDp;
import com.abujlb.dataprobi.bean.SqliteDp;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.abujlb.dataprobi.util.MathUtil;
import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 * @date 2022/6/10 11:38
 */
public class SqlitePddHbdkDp extends AbstractSqliteDp {

    //红包抵扣金额
    private double hbdkje;

    public SqlitePddHbdkDp() {
    }

    public double getHbdkje() {
        return hbdkje;
    }

    public void setHbdkje(double hbdkje) {
        this.hbdkje = hbdkje;
    }

    @Override
    public <T extends AbstractSqliteDp> void setValues(String[] tableStoreColumnValues) {
        if (tableStoreColumnValues[0] != null) {
            JSONObject jsonObject = JSONObject.parseObject(tableStoreColumnValues[0]);
            this.hbdkje = -MathUtil.divide1000(FastJSONObjAttrToNumber.toDouble(jsonObject, "hbdkje"));
        }
    }

    @Override
    public boolean compare(SqliteDp sqliteDp) {
        if (sqliteDp == null) {
            return false;
        }
        JSONObject jsonObject = JSONObject.parseObject(sqliteDp.getOtherOrDefault());
        return FastJSONObjAttrToNumber.toDouble(jsonObject, "hbdkje") == this.getHbdkje();
    }
}
