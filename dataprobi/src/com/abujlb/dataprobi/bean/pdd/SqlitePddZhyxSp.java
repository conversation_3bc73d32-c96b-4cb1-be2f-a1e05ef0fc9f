package com.abujlb.dataprobi.bean.pdd;

import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.interfaces.Plusable;
import com.abujlb.dataprobi.util.MathUtil;

/**
 * <AUTHOR>
 * @date 2023/7/25
 */
public class SqlitePddZhyxSp extends AbstractSqliteSp implements Plusable<SqlitePddZhyxSp> {

    //整合营销花费
    private double zhyxhf;

    public SqlitePddZhyxSp() {
    }

    public double getZhyxhf() {
        return zhyxhf;
    }

    public void setZhyxhf(double zhyxhf) {
        this.zhyxhf = zhyxhf;
    }

    @Override
    public void plus(SqlitePddZhyxSp temp) {
        this.zhyxhf = MathUtil.add(this.zhyxhf, temp.getZhyxhf());
    }

}

