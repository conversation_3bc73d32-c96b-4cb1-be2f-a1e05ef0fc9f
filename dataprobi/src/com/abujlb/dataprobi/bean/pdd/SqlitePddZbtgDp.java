package com.abujlb.dataprobi.bean.pdd;

import com.abujlb.dataprobi.bean.AbstractSqliteDp;
import com.abujlb.dataprobi.bean.SqliteDp;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.abujlb.dataprobi.util.MathUtil;
import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 * @date 2022/6/10 11:38
 */
public class SqlitePddZbtgDp extends AbstractSqliteDp {

    //直播推广曝光量
    private int zbtgbgl;
    //直播推广点击量
    private int zbtgdjl;
    //直播推广成交笔数
    private int zbtgcjbs;
    //直播推广成交金额
    private double zbtgcjje;
    //直播推广花费
    private double zbtghf;
    //直播推广点击率
    private double zbtgdjlv;

    public SqlitePddZbtgDp() {
    }

    public int getZbtgbgl() {
        return zbtgbgl;
    }

    public void setZbtgbgl(int zbtgbgl) {
        this.zbtgbgl = zbtgbgl;
    }

    public int getZbtgdjl() {
        return zbtgdjl;
    }

    public void setZbtgdjl(int zbtgdjl) {
        this.zbtgdjl = zbtgdjl;
    }

    public int getZbtgcjbs() {
        return zbtgcjbs;
    }

    public void setZbtgcjbs(int zbtgcjbs) {
        this.zbtgcjbs = zbtgcjbs;
    }

    public double getZbtgcjje() {
        return zbtgcjje;
    }

    public void setZbtgcjje(double zbtgcjje) {
        this.zbtgcjje = zbtgcjje;
    }

    public double getZbtghf() {
        return zbtghf;
    }

    public void setZbtghf(double zbtghf) {
        this.zbtghf = zbtghf;
    }

    public double getZbtgdjlv() {
        return zbtgdjlv;
    }

    public void setZbtgdjlv(double zbtgdjlv) {
        this.zbtgdjlv = zbtgdjlv;
    }

    @Override
    public <T extends AbstractSqliteDp> void setValues(String[] tableStoreColumnValues) {
        if (tableStoreColumnValues[0] != null) {
            JSONObject jsonObject = JSONObject.parseObject(tableStoreColumnValues[0]);
            this.zbtgbgl = FastJSONObjAttrToNumber.toInt(jsonObject, "impression");
            this.zbtgdjl = FastJSONObjAttrToNumber.toInt(jsonObject, "click");
            this.zbtgcjbs = FastJSONObjAttrToNumber.toInt(jsonObject, "orderNum");
            this.zbtgcjje = MathUtil.divide1000(FastJSONObjAttrToNumber.toInt(jsonObject, "gmv"));
            this.zbtghf = MathUtil.divide1000(FastJSONObjAttrToNumber.toInt(jsonObject, "spend"));
            this.zbtgdjlv = FastJSONObjAttrToNumber.toDouble(jsonObject, "ctr");
        }
    }

    @Override
    public boolean compare(SqliteDp sqliteDp) {
        if (sqliteDp == null) {
            return false;
        }
        JSONObject jsonObject = JSONObject.parseObject(sqliteDp.getOtherOrDefault());
        return FastJSONObjAttrToNumber.toInt(jsonObject, "zbtgbgl") == this.getZbtgbgl()
                && FastJSONObjAttrToNumber.toInt(jsonObject, "zbtgdjl") == this.getZbtgdjl()
                && FastJSONObjAttrToNumber.toInt(jsonObject, "zbtgcjbs") == this.getZbtgcjbs()
                && FastJSONObjAttrToNumber.toDouble(jsonObject, "zbtgcjje") == this.getZbtgcjje()
                && FastJSONObjAttrToNumber.toDouble(jsonObject, "zbtghf") == this.getZbtghf();
    }
}
