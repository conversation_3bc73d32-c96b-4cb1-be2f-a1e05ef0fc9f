package com.abujlb.dataprobi.bean.pdd;

import com.abujlb.dataprobi.bean.AbstractSqliteDp;
import com.abujlb.dataprobi.bean.SqliteDp;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.abujlb.dataprobi.util.MathUtil;
import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 * @date 2022/6/10 11:38
 */
public class SqlitePddQztgDp extends AbstractSqliteDp {

    //全站推广曝光量
    private int qztgbgl;
    //全站推广点击量
    private int qztgdjl;
    //全站推广成交笔数
    private int qztgcjbs;
    //全站推广成交金额
    private double qztgcjje;
    //全站推广花费
    private double qztghf;
    //全站推广点击率
    private double qztgdjlv;

    public SqlitePddQztgDp() {
    }

    public int getQztgbgl() {
        return qztgbgl;
    }

    public void setQztgbgl(int qztgbgl) {
        this.qztgbgl = qztgbgl;
    }

    public int getQztgdjl() {
        return qztgdjl;
    }

    public void setQztgdjl(int qztgdjl) {
        this.qztgdjl = qztgdjl;
    }

    public int getQztgcjbs() {
        return qztgcjbs;
    }

    public void setQztgcjbs(int qztgcjbs) {
        this.qztgcjbs = qztgcjbs;
    }

    public double getQztgcjje() {
        return qztgcjje;
    }

    public void setQztgcjje(double qztgcjje) {
        this.qztgcjje = qztgcjje;
    }

    public double getQztghf() {
        return qztghf;
    }

    public void setQztghf(double qztghf) {
        this.qztghf = qztghf;
    }

    public double getQztgdjlv() {
        return qztgdjlv;
    }

    public void setQztgdjlv(double qztgdjlv) {
        this.qztgdjlv = qztgdjlv;
    }

    @Override
    public <T extends AbstractSqliteDp> void setValues(String[] tableStoreColumnValues) {
        if (tableStoreColumnValues[0] != null) {
            JSONObject jsonObject = JSONObject.parseObject(tableStoreColumnValues[0]);
            this.qztgbgl = FastJSONObjAttrToNumber.toInt(jsonObject, "impression");
            this.qztgdjl = FastJSONObjAttrToNumber.toInt(jsonObject, "click");
            this.qztgcjbs = FastJSONObjAttrToNumber.toInt(jsonObject, "orderNum");
            this.qztgcjje = MathUtil.divide1000(FastJSONObjAttrToNumber.toInt(jsonObject, "gmv"));
            this.qztghf = MathUtil.divide1000(FastJSONObjAttrToNumber.toInt(jsonObject, "spend"));
            this.qztgdjlv = FastJSONObjAttrToNumber.toDouble(jsonObject, "ctr");
        }
    }

    @Override
    public boolean compare(SqliteDp sqliteDp) {
        if (sqliteDp == null) {
            return false;
        }
        JSONObject jsonObject = JSONObject.parseObject(sqliteDp.getOtherOrDefault());
        return FastJSONObjAttrToNumber.toInt(jsonObject, "qztgbgl") == this.getQztgbgl()
                && FastJSONObjAttrToNumber.toInt(jsonObject, "qztgdjl") == this.getQztgdjl()
                && FastJSONObjAttrToNumber.toInt(jsonObject, "qztgcjbs") == this.getQztgcjbs()
                && FastJSONObjAttrToNumber.toDouble(jsonObject, "qztgcjje") == this.getQztgcjje()
                && FastJSONObjAttrToNumber.toDouble(jsonObject, "qztghf") == this.getQztghf();
    }
}
