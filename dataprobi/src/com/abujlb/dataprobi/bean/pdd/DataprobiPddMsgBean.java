package com.abujlb.dataprobi.bean.pdd;

import com.abujlb.dataprobi.annotations.ClrqIgnore;
import com.abujlb.dataprobi.annotations.SumIgnore;
import com.abujlb.dataprobi.annotations.UpdateSycmcljzrqIgnore;
import com.abujlb.dataprobi.bean.DataprobiBaseMsgBean;
import com.alibaba.fastjson.JSON;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/6/9 11:58
 */
public class DataprobiPddMsgBean extends DataprobiBaseMsgBean {

    //商品明细采集日期
    @UpdateSycmcljzrqIgnore
    private List<String> spxg;

    //商品明细店铺维度采集日期
    @UpdateSycmcljzrqIgnore
    private List<String> spxgdp;

    //多多进宝
    private List<String> ddjb;

    //多多进宝
    private List<String> ddjbdp;

    //多多搜索采集日期
    private List<String> ddss;

    //多多搜索采集日期
    private List<String> ddssdp;

    //多多场景采集日期
    private List<String> ddcj;

    //多多场景采集日期
    private List<String> ddcjdp;

    //全站推广采集日期
    private List<String> qztg;

    //全站推广采集日期
    private List<String> qztgdp;

    //明星店铺
    private List<String> mxdp;

    //直播推广
    private List<String> zbtg;

    //商品评价
    @UpdateSycmcljzrqIgnore
    @ClrqIgnore
    @SumIgnore
    private List<String> sppj;

    private List<String> zhyxdp;

    @UpdateSycmcljzrqIgnore
    @ClrqIgnore
    private List<String> jlgg;

    private List<String> bztg;
    private List<String> bztgdp;

    @UpdateSycmcljzrqIgnore
    @ClrqIgnore
    @SumIgnore
    private List<String> xkfx;
    @UpdateSycmcljzrqIgnore
    @ClrqIgnore
    @SumIgnore
    private List<String> bztgtgfx;
    @UpdateSycmcljzrqIgnore
    @ClrqIgnore
    @SumIgnore
    private List<String> qztgtgfx;
    @UpdateSycmcljzrqIgnore
    @ClrqIgnore
    @SumIgnore
    private List<String> sptgtgfx;
    private List<String> sptg;
    private List<String> sptgdp;
    @UpdateSycmcljzrqIgnore
    private List<String> pjyl;
    private List<String> hbdk;
    @UpdateSycmcljzrqIgnore
    private List<String> orders;

    @UpdateSycmcljzrqIgnore
    @ClrqIgnore
    @SumIgnore
    private List<String> pddjhzt;

    //    店铺体验分店铺维度
    @UpdateSycmcljzrqIgnore
    @ClrqIgnore
    @SumIgnore
    private List<String> pddsjtyf;

    @UpdateSycmcljzrqIgnore
    @SumIgnore
    @ClrqIgnore
    //客服-绩效分析
    private List<String> kfjxfx;

    @UpdateSycmcljzrqIgnore
    @SumIgnore
    @ClrqIgnore
    //客服-服务数据-店铺
    private List<String> kffwsj;

    @UpdateSycmcljzrqIgnore
    @SumIgnore
    @ClrqIgnore
    //客服-销售数据-店铺
    private List<String> kfxssj;

    @UpdateSycmcljzrqIgnore
    @ClrqIgnore
    @SumIgnore
    private List<String> money;
    private List<String> blacktag;

    public List<String> getMoney() {
        return money;
    }

    public void setMoney(List<String> money) {
        this.money = money;
    }

    public List<String> getPddsjtyf() {
        return pddsjtyf;
    }

    public void setPddsjtyf(List<String> pddsjtyf) {
        this.pddsjtyf = pddsjtyf;
    }

    public List<String> getDdjb() {
        return ddjb;
    }

    public void setDdjb(List<String> ddjb) {
        this.ddjb = ddjb;
    }

    public List<String> getDdjbdp() {
        return ddjbdp;
    }

    public void setDdjbdp(List<String> ddjbdp) {
        this.ddjbdp = ddjbdp;
    }

    public List<String> getDdss() {
        return ddss;
    }

    public void setDdss(List<String> ddss) {
        this.ddss = ddss;
    }

    public List<String> getDdssdp() {
        return ddssdp;
    }

    public void setDdssdp(List<String> ddssdp) {
        this.ddssdp = ddssdp;
    }

    public List<String> getDdcj() {
        return ddcj;
    }

    public void setDdcj(List<String> ddcj) {
        this.ddcj = ddcj;
    }

    public List<String> getDdcjdp() {
        return ddcjdp;
    }

    public void setDdcjdp(List<String> ddcjdp) {
        this.ddcjdp = ddcjdp;
    }

    public List<String> getQztg() {
        return qztg;
    }

    public void setQztg(List<String> qztg) {
        this.qztg = qztg;
    }

    public List<String> getQztgdp() {
        return qztgdp;
    }

    public void setQztgdp(List<String> qztgdp) {
        this.qztgdp = qztgdp;
    }

    public List<String> getMxdp() {
        return mxdp;
    }

    public void setMxdp(List<String> mxdp) {
        this.mxdp = mxdp;
    }

    public List<String> getZbtg() {
        return zbtg == null ? Collections.emptyList() : zbtg;
    }

    public void setZbtg(List<String> zbtg) {
        this.zbtg = zbtg;
    }

    public List<String> getSppj() {
        return sppj;
    }

    public void setSppj(List<String> sppj) {
        this.sppj = sppj;
    }

    public List<String> getZhyxdp() {
        return zhyxdp;
    }

    public void setZhyxdp(List<String> zhyxdp) {
        this.zhyxdp = zhyxdp;
    }

    public List<String> getSpxg() {
        return spxg;
    }

    public void setSpxg(List<String> spxg) {
        this.spxg = spxg;
    }

    public List<String> getSpxgdp() {
        return spxgdp;
    }

    public void setSpxgdp(List<String> spxgdp) {
        this.spxgdp = spxgdp;
    }

    public List<String> getJlgg() {
        return jlgg == null ? Collections.emptyList() : jlgg;
    }

    public void setJlgg(List<String> jlgg) {
        this.jlgg = jlgg;
    }

    public List<String> getBztg() {
        return bztg;
    }

    public void setBztg(List<String> bztg) {
        this.bztg = bztg;
    }

    public List<String> getBztgdp() {
        return bztgdp;
    }

    public void setBztgdp(List<String> bztgdp) {
        this.bztgdp = bztgdp;
    }

    public List<String> getXkfx() {
        return xkfx;
    }

    public void setXkfx(List<String> xkfx) {
        this.xkfx = xkfx;
    }

    public List<String> getBztgtgfx() {
        return bztgtgfx;
    }

    public void setBztgtgfx(List<String> bztgtgfx) {
        this.bztgtgfx = bztgtgfx;
    }

    public List<String> getQztgtgfx() {
        return qztgtgfx;
    }

    public void setQztgtgfx(List<String> qztgtgfx) {
        this.qztgtgfx = qztgtgfx;
    }

    public List<String> getSptg() {
        return sptg;
    }

    public void setSptg(List<String> sptg) {
        this.sptg = sptg;
    }

    public List<String> getSptgdp() {
        return sptgdp;
    }

    public void setSptgdp(List<String> sptgdp) {
        this.sptgdp = sptgdp;
    }

    public List<String> getSptgtgfx() {
        return sptgtgfx;
    }

    public void setSptgtgfx(List<String> sptgtgfx) {
        this.sptgtgfx = sptgtgfx;
    }

    public List<String> getPjyl() {
        return pjyl;
    }

    public void setPjyl(List<String> pjyl) {
        this.pjyl = pjyl;
    }

    public List<String> getHbdk() {
        return hbdk;
    }

    public void setHbdk(List<String> hbdk) {
        this.hbdk = hbdk;
    }

    public List<String> getOrders() {
        return orders;
    }

    public void setOrders(List<String> orders) {
        this.orders = orders;
    }

    public List<String> getPddjhzt() {
        return pddjhzt;
    }

    public void setPddjhzt(List<String> pddjhzt) {
        this.pddjhzt = pddjhzt;
    }

    public List<String> getKfjxfx() {
        return kfjxfx;
    }

    public void setKfjxfx(List<String> kfjxfx) {
        this.kfjxfx = kfjxfx;
    }

    public List<String> getBlacktag() {
        return blacktag;
    }

    public void setBlacktag(List<String> blacktag) {
        this.blacktag = blacktag;
    }

    public List<String> getKffwsj() {
        return kffwsj;
    }

    public void setKffwsj(List<String> kffwsj) {
        this.kffwsj = kffwsj;
    }

    public List<String> getKfxssj() {
        return kfxssj;
    }

    public void setKfxssj(List<String> kfxssj) {
        this.kfxssj = kfxssj;
    }

    public void fillNullList() throws InvocationTargetException, IllegalAccessException {
        Method[] methods = this.getClass().getMethods();
        for (Method method : methods) {
            if (method.getName().startsWith("get")) {
                Object value = method.invoke(this);
                if (value == null) {
                    Method setMethod = null;
                    try {
                        setMethod = this.getClass().getMethod("s" + method.getName().substring(1), List.class);
                        setMethod.invoke(this, new ArrayList<>());
                    } catch (NoSuchMethodException ignored) {
                    }
                }
            }
        }
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
