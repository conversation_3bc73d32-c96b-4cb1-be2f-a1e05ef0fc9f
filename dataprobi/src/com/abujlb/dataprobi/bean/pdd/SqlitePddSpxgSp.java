package com.abujlb.dataprobi.bean.pdd;

import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.SqliteSp;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.abujlb.dataprobi.util.MathUtil;
import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 * @date 2022/6/10 9:23
 */
public class SqlitePddSpxgSp extends AbstractSqliteSp {

    //下单率
    private double spxgxdl;
    //收藏人数
    private double spxgscrs;
    //流量损失指数
    private double spxgllsszs;
    //1级类目
    private int cate1Id;
    //2级类目
    private int cate2Id;
    //3级类目
    private int cate3Id;
    //1级类目名称
    private String cate1Name;
    //2级类目名称
    private String cate2Name;
    //3鸡类目名称
    private String cate3Name;
    //支付订单数
    private int spxgzfdds;
    //支付率
    private double spxgzflv;

    public SqlitePddSpxgSp() {
    }

    public double getSpxgxdl() {
        return spxgxdl;
    }

    public void setSpxgxdl(double spxgxdl) {
        this.spxgxdl = spxgxdl;
    }

    public double getSpxgscrs() {
        return spxgscrs;
    }

    public void setSpxgscrs(double spxgscrs) {
        this.spxgscrs = spxgscrs;
    }

    public double getSpxgllsszs() {
        return spxgllsszs;
    }

    public void setSpxgllsszs(double spxgllsszs) {
        this.spxgllsszs = spxgllsszs;
    }

    public int getCate1Id() {
        return cate1Id;
    }

    public void setCate1Id(int cate1Id) {
        this.cate1Id = cate1Id;
    }

    public int getCate2Id() {
        return cate2Id;
    }

    public void setCate2Id(int cate2Id) {
        this.cate2Id = cate2Id;
    }

    public int getCate3Id() {
        return cate3Id;
    }

    public void setCate3Id(int cate3Id) {
        this.cate3Id = cate3Id;
    }

    public String getCate1Name() {
        return cate1Name;
    }

    public void setCate1Name(String cate1Name) {
        this.cate1Name = cate1Name;
    }

    public String getCate2Name() {
        return cate2Name;
    }

    public void setCate2Name(String cate2Name) {
        this.cate2Name = cate2Name;
    }

    public String getCate3Name() {
        return cate3Name;
    }

    public void setCate3Name(String cate3Name) {
        this.cate3Name = cate3Name;
    }

    public int getSpxgzfdds() {
        return spxgzfdds;
    }

    public void setSpxgzfdds(int spxgzfdds) {
        this.spxgzfdds = spxgzfdds;
    }

    public double getSpxgzflv() {
        return spxgzflv;
    }

    public void setSpxgzflv(double spxgzflv) {
        this.spxgzflv = spxgzflv;
    }

    @Override
    public void setValues(JSONObject jsonObject) {
        this.bbid = FastJSONObjAttrToNumber.toString(jsonObject, "goodsId");

        this.spxgfks = FastJSONObjAttrToNumber.toInt(jsonObject, "goodsUv");
        this.spxglll = FastJSONObjAttrToNumber.toInt(jsonObject, "goodsPv");
        this.spxgzfje = FastJSONObjAttrToNumber.toDouble(jsonObject, "payOrdrAmt");
        this.spxgzfmjs = FastJSONObjAttrToNumber.toInt(jsonObject, "payOrdrUsrCnt");
        this.spxgzfzhl = MathUtil.divide100(FastJSONObjAttrToNumber.toDouble(jsonObject, "goodsVcr"));
        this.spxgzfjs = FastJSONObjAttrToNumber.toInt(jsonObject, "payOrdrGoodsQty");
        this.spxgzfdds = FastJSONObjAttrToNumber.toInt(jsonObject, "payOrdrCnt");


        this.spxgxdl = MathUtil.divide100(FastJSONObjAttrToNumber.toDouble(jsonObject, "ordrVstrRto"));
        this.spxgscrs = FastJSONObjAttrToNumber.toInt(jsonObject, "goodsFavCnt");
        this.spxgllsszs = FastJSONObjAttrToNumber.toDouble(jsonObject, "imprUsrCnt");
        this.cate1Id = FastJSONObjAttrToNumber.toInt(jsonObject, "cate1Id");

        this.cate2Id = FastJSONObjAttrToNumber.toInt(jsonObject, "cate2Id");

        this.cate3Id = FastJSONObjAttrToNumber.toInt(jsonObject, "cate3Id");

        this.spxgzflv = MathUtil.divide100(FastJSONObjAttrToNumber.toDouble(jsonObject, "payOrdrRto"));
    }

    @Override
    public boolean compare(SqliteSp sqliteSp) {
        if (sqliteSp == null) {
            return false;
        }

        JSONObject jsonObject = JSONObject.parseObject(sqliteSp.getOtherOrDefault());

        return sqliteSp.getSpxgfks() == this.getSpxgfks()
                && sqliteSp.getSpxglll() == this.getSpxglll()
                && sqliteSp.getSpxgzfje() == this.getSpxgzfje()
                && sqliteSp.getSpxgzfmjs() == this.getSpxgzfmjs()
                && sqliteSp.getSpxgtkje() == this.getSpxgtkje()
                && sqliteSp.getSpxgzfjs() == this.getSpxgzfjs()
                && FastJSONObjAttrToNumber.toInt(jsonObject, "spxgzfdds") == this.getSpxgzfdds();
    }
}
