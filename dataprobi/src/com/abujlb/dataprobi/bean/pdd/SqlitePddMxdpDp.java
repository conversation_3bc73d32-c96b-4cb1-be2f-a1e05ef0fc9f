package com.abujlb.dataprobi.bean.pdd;

import com.abujlb.dataprobi.bean.AbstractSqliteDp;
import com.abujlb.dataprobi.bean.SqliteDp;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.abujlb.dataprobi.util.MathUtil;
import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 * @date 2022/6/10 11:38
 */
public class SqlitePddMxdpDp extends AbstractSqliteDp {

    //明星店铺曝光量
    private int mxdpbgl;
    //明星店铺点击量
    private int mxdpdjl;
    //明星店铺成交笔数
    private int mxdpcjbs;
    //明星店铺成交金额
    private double mxdpcjje;
    //明星店铺花费
    private double mxdphf;
    //明星店铺点击率
    private double mxdpdjlv;

    public SqlitePddMxdpDp() {
    }

    public int getMxdpbgl() {
        return mxdpbgl;
    }

    public void setMxdpbgl(int mxdpbgl) {
        this.mxdpbgl = mxdpbgl;
    }

    public int getMxdpdjl() {
        return mxdpdjl;
    }

    public void setMxdpdjl(int mxdpdjl) {
        this.mxdpdjl = mxdpdjl;
    }

    public int getMxdpcjbs() {
        return mxdpcjbs;
    }

    public void setMxdpcjbs(int mxdpcjbs) {
        this.mxdpcjbs = mxdpcjbs;
    }

    public double getMxdpcjje() {
        return mxdpcjje;
    }

    public void setMxdpcjje(double mxdpcjje) {
        this.mxdpcjje = mxdpcjje;
    }

    public double getMxdphf() {
        return mxdphf;
    }

    public void setMxdphf(double mxdphf) {
        this.mxdphf = mxdphf;
    }

    public double getMxdpdjlv() {
        return mxdpdjlv;
    }

    public void setMxdpdjlv(double mxdpdjlv) {
        this.mxdpdjlv = mxdpdjlv;
    }

    @Override
    public <T extends AbstractSqliteDp> void setValues(String[] tableStoreColumnValues) {
        if (tableStoreColumnValues[0] != null) {
            JSONObject jsonObject = JSONObject.parseObject(tableStoreColumnValues[0]);
            this.mxdpbgl = FastJSONObjAttrToNumber.toInt(jsonObject, "impression");
            this.mxdpdjl = FastJSONObjAttrToNumber.toInt(jsonObject, "click");
            this.mxdpcjbs = FastJSONObjAttrToNumber.toInt(jsonObject, "orderNum");
            this.mxdpcjje = MathUtil.divide1000(FastJSONObjAttrToNumber.toInt(jsonObject, "gmv"));
            this.mxdphf = MathUtil.divide1000(FastJSONObjAttrToNumber.toInt(jsonObject, "spend"));
            this.mxdpdjlv = FastJSONObjAttrToNumber.toDouble(jsonObject, "ctr");
        }
    }

    @Override
    public boolean compare(SqliteDp sqliteDp) {
        if (sqliteDp == null) {
            return false;
        }
        JSONObject jsonObject = JSONObject.parseObject(sqliteDp.getOtherOrDefault());
        return FastJSONObjAttrToNumber.toInt(jsonObject, "mxdpbgl") == this.getMxdpbgl()
                && FastJSONObjAttrToNumber.toInt(jsonObject, "mxdpdjl") == this.getMxdpdjl()
                && FastJSONObjAttrToNumber.toInt(jsonObject, "mxdpcjbs") == this.getMxdpcjbs()
                && FastJSONObjAttrToNumber.toDouble(jsonObject, "mxdpcjje") == this.getMxdpcjje()
                && FastJSONObjAttrToNumber.toDouble(jsonObject, "mxdphf") == this.getMxdphf();
    }
}
