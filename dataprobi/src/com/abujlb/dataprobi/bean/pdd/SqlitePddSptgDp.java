package com.abujlb.dataprobi.bean.pdd;

import com.abujlb.dataprobi.bean.AbstractSqliteDp;
import com.abujlb.dataprobi.bean.SqliteDp;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.abujlb.dataprobi.util.MathUtil;
import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 * @date 2023/11/3
 */
public class SqlitePddSptgDp extends AbstractSqliteDp {

    //标准推广曝光量
    private int sptgbgl;
    //标准推广点击量
    private int sptgdjl;
    //标准推广成交笔数
    private int sptgcjbs;
    //标准推广成交金额
    private double sptgcjje;
    //标准推广花费
    private double sptghf;

    public SqlitePddSptgDp() {
    }

    public int getSptgbgl() {
        return sptgbgl;
    }

    public void setSptgbgl(int sptgbgl) {
        this.sptgbgl = sptgbgl;
    }

    public int getSptgdjl() {
        return sptgdjl;
    }

    public void setSptgdjl(int sptgdjl) {
        this.sptgdjl = sptgdjl;
    }

    public int getSptgcjbs() {
        return sptgcjbs;
    }

    public void setSptgcjbs(int sptgcjbs) {
        this.sptgcjbs = sptgcjbs;
    }

    public double getSptgcjje() {
        return sptgcjje;
    }

    public void setSptgcjje(double sptgcjje) {
        this.sptgcjje = sptgcjje;
    }

    public double getSptghf() {
        return sptghf;
    }

    public void setSptghf(double sptghf) {
        this.sptghf = sptghf;
    }

    @Override
    public <T extends AbstractSqliteDp> void setValues(String[] tableStoreColumnValues) {
        if (tableStoreColumnValues[0] != null) {
            JSONObject jsonObject = JSONObject.parseObject(tableStoreColumnValues[0]);
            this.sptgbgl = FastJSONObjAttrToNumber.toInt(jsonObject, "impression");
            this.sptgdjl = FastJSONObjAttrToNumber.toInt(jsonObject, "click");
            this.sptgcjbs = FastJSONObjAttrToNumber.toInt(jsonObject, "orderNum");
            this.sptgcjje = MathUtil.divide1000(FastJSONObjAttrToNumber.toInt(jsonObject, "gmv"));
            this.sptghf = MathUtil.divide1000(FastJSONObjAttrToNumber.toInt(jsonObject, "spend"));
        }
    }

    @Override
    public boolean compare(SqliteDp sqliteDp) {
        if (sqliteDp == null) {
            return false;
        }
        JSONObject jsonObject = JSONObject.parseObject(sqliteDp.getOtherOrDefault());
        return FastJSONObjAttrToNumber.toInt(jsonObject, "sptgbgl") == this.getSptgbgl()
                && FastJSONObjAttrToNumber.toInt(jsonObject, "sptgdjl") == this.getSptgbgl()
                && FastJSONObjAttrToNumber.toInt(jsonObject, "sptgcjbs") == this.getSptgcjbs()
                && FastJSONObjAttrToNumber.toDouble(jsonObject, "sptgcjje") == this.getSptgcjje()
                && FastJSONObjAttrToNumber.toDouble(jsonObject, "sptghf") == this.getSptghf();
    }
}
