package com.abujlb.dataprobi.bean.pdd;

import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.SqliteSp;
import com.abujlb.dataprobi.interfaces.Plusable;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.abujlb.dataprobi.util.MathUtil;
import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 * @date 2022/6/10 11:38
 */
public class SqlitePddDdjbSp extends AbstractSqliteSp implements Plusable<SqlitePddDdjbSp> {

    // 多多进宝成交笔数
    private int ddjbcjbs;
    //多多进宝成交金额
    private double ddjbcjje;
    //多多进宝预估支付佣金（源数据单位为厘，这里已经处理为元了）
    private double ddjbyj;
    //多多进宝预估软件服务费（源数据单位为厘，这里已经处理为元了）
    private double ddjbrjfwf;
    //多多进宝花费
    private double ddjbhf;

    public SqlitePddDdjbSp() {
    }

    public int getDdjbcjbs() {
        return ddjbcjbs;
    }

    public void setDdjbcjbs(int ddjbcjbs) {
        this.ddjbcjbs = ddjbcjbs;
    }

    public double getDdjbcjje() {
        return ddjbcjje;
    }

    public void setDdjbcjje(double ddjbcjje) {
        this.ddjbcjje = ddjbcjje;
    }

    public double getDdjbyj() {
        return ddjbyj;
    }

    public void setDdjbyj(double ddjbyj) {
        this.ddjbyj = ddjbyj;
    }

    public double getDdjbrjfwf() {
        return ddjbrjfwf;
    }

    public void setDdjbrjfwf(double ddjbrjfwf) {
        this.ddjbrjfwf = ddjbrjfwf;
    }

    public double getDdjbhf() {
        return ddjbhf;
    }

    public void setDdjbhf(double ddjbhf) {
        this.ddjbhf = ddjbhf;
    }

    @Override
    public void setValues(JSONObject jsonObject) {
        this.bbid = FastJSONObjAttrToNumber.toString(jsonObject, "goods_id");
        if (jsonObject.containsKey("cpsAdGoodVO")) {
            JSONObject cpsAdGoodVO = jsonObject.getJSONObject("cpsAdGoodVO");
            this.bbmc = FastJSONObjAttrToNumber.toString(cpsAdGoodVO, "goodsName");
            this.img = FastJSONObjAttrToNumber.toString(cpsAdGoodVO, "thumbUrl");
        }

        this.ddjbcjbs = FastJSONObjAttrToNumber.toInt(jsonObject, "group_order_num");
        this.ddjbcjje = MathUtil.divide1000(FastJSONObjAttrToNumber.toInt(jsonObject, "group_order_amount"));
        this.ddjbyj = MathUtil.divide1000(FastJSONObjAttrToNumber.toInt(jsonObject, "fee_amount"));
        this.ddjbrjfwf = MathUtil.divide1000(FastJSONObjAttrToNumber.toInt(jsonObject, "service_fee_amount"));
        this.ddjbhf = MathUtil.add(this.ddjbyj, this.ddjbrjfwf);
    }

    @Override
    public void plus(SqlitePddDdjbSp temp) {
        // 多多进宝成交笔数
        this.ddjbcjbs += temp.getDdjbcjbs();
        //多多进宝成交金额
        this.ddjbcjje = MathUtil.add(this.ddjbcjje, temp.getDdjbcjje());
        //多多进宝预估支付佣金（源数据单位为厘，这里已经处理为元了）
        this.ddjbyj = MathUtil.add(this.ddjbyj, temp.getDdjbyj());
        //多多进宝预估软件服务费（源数据单位为厘，这里已经处理为元了）
        this.ddjbrjfwf = MathUtil.add(this.ddjbrjfwf, temp.getDdjbrjfwf());
        //多多进宝花费
        this.ddjbhf = MathUtil.add(this.ddjbhf, temp.getDdjbhf());
    }

    @Override
    public boolean compare(SqliteSp sqliteSp) {
        if (sqliteSp == null) {
            return false;
        }

        JSONObject jsonObject = JSONObject.parseObject(sqliteSp.getOtherOrDefault());
        return FastJSONObjAttrToNumber.toInt(jsonObject, "ddjbcjbs") == this.getDdjbcjbs()
                && FastJSONObjAttrToNumber.toDouble(jsonObject, "ddjbcjje") == this.getDdjbcjje()
                && FastJSONObjAttrToNumber.toDouble(jsonObject, "ddjbyj") == this.getDdjbyj()
                && FastJSONObjAttrToNumber.toDouble(jsonObject, "ddjbrjfwf") == this.getDdjbrjfwf()
                && FastJSONObjAttrToNumber.toDouble(jsonObject, "ddjbhf") == this.getDdjbhf();
    }
}
