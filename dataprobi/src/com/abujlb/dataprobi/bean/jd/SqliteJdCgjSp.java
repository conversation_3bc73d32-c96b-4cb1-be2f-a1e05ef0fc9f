package com.abujlb.dataprobi.bean.jd;

import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.SqliteSp;
import com.abujlb.dataprobi.interfaces.Plusable;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.abujlb.dataprobi.util.MathUtil;
import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 * @date 2023/11/23
 */
public class SqliteJdCgjSp extends AbstractSqliteSp implements Plusable<SqliteJdCgjSp> {

    //京东自营采购价
    private double jdzycgj;
    //京东自营实付金额
    private double jdzysfje;

    public SqliteJdCgjSp() {
    }

    public double getJdzycgj() {
        return jdzycgj;
    }

    public void setJdzycgj(double jdzycgj) {
        this.jdzycgj = jdzycgj;
    }

    public double getJdzysfje() {
        return jdzysfje;
    }

    public void setJdzysfje(double jdzysfje) {
        this.jdzysfje = jdzysfje;
    }

    @Override
    public <T extends AbstractSqliteSp> void plus(T t) {
        if (t instanceof SqliteJdCgjSp) {
            SqliteJdCgjSp temp = (SqliteJdCgjSp) t;
            this.jdzycgj = MathUtil.add(this.jdzycgj, temp.getJdzycgj());
            this.jdzysfje = MathUtil.add(this.jdzysfje, temp.getJdzysfje());
        }
    }

    @Override
    public void plus(SqliteJdCgjSp sqliteJdCgjSp) {
        this.jdzycgj = MathUtil.add(this.jdzycgj, sqliteJdCgjSp.getJdzycgj());
        this.jdzysfje = MathUtil.add(this.jdzysfje, sqliteJdCgjSp.getJdzysfje());
    }

    @Override
    public void setValues(JSONObject jsonObject) {
        this.bbid = FastJSONObjAttrToNumber.toString(jsonObject, "spbh");
        this.jdzycgj = FastJSONObjAttrToNumber.toDouble(jsonObject, "cb");
        this.jdzysfje = FastJSONObjAttrToNumber.toDouble(jsonObject, "sr");
    }

    @Override
    public boolean compare(SqliteSp sqliteSp) {
        if (sqliteSp == null) {
            return false;
        }
        JSONObject jsonObject = JSONObject.parseObject(sqliteSp.getOtherOrDefault());
        return FastJSONObjAttrToNumber.toDouble(jsonObject, "jdzycgj") == this.getJdzycgj()
                && FastJSONObjAttrToNumber.toDouble(jsonObject, "jdzysfje") == this.getSpxgzfje();
    }
}
