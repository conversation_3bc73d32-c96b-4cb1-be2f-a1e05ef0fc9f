package com.abujlb.dataprobi.bean;

import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2025/3/6
 */
public class DataprobiBaseMsgBean2 {

    //时间戳
    protected String t;
    //uuid 表示消息唯一
    protected String uuid;

    //店铺userid
    protected String userid;
    //用户id
    protected int yhid;
    //渠道
    protected String qd;
    //bi_info表的ID
    protected int biinfoId;
    //店铺名称
    protected String dpmc;

    //0：无变动  1、品销宝 2、万相台  3、dou+   4、千川全域
    private int lx;

    private List<String> rqList;

    public DataprobiBaseMsgBean2() {
        this.t = System.currentTimeMillis() + "";
        this.uuid = UUID.randomUUID().toString();
    }

    public String getT() {
        return t;
    }

    public void setT(String t) {
        this.t = t;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getUserid() {
        return userid;
    }

    public void setUserid(String userid) {
        this.userid = userid;
    }

    public int getYhid() {
        return yhid;
    }

    public void setYhid(int yhid) {
        this.yhid = yhid;
    }

    public String getQd() {
        return qd;
    }

    public void setQd(String qd) {
        this.qd = qd;
    }

    public int getBiinfoId() {
        return biinfoId;
    }

    public void setBiinfoId(int biinfoId) {
        this.biinfoId = biinfoId;
    }

    public String getDpmc() {
        return dpmc;
    }

    public void setDpmc(String dpmc) {
        this.dpmc = dpmc;
    }

    public int getLx() {
        return lx;
    }

    public void setLx(int lx) {
        this.lx = lx;
    }

    public List<String> getRqList() {
        return rqList;
    }

    public void setRqList(List<String> rqList) {
        this.rqList = rqList;
    }
}
