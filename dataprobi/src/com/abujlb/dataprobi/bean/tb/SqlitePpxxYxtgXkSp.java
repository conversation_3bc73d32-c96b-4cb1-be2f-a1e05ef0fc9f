package com.abujlb.dataprobi.bean.tb;

import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.SqliteSp;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 * @date 2024/9/4
 */
public class SqlitePpxxYxtgXkSp extends AbstractSqliteSp {

    //营销托管新客托管花费
    private double yxtgxktghf;
    //营销托管新客托管成交金额
    private double yxtgxktgcjje;
    public SqlitePpxxYxtgXkSp() {
    }

    @Override
    public void setValues(JSONObject jsonObject) {
        this.bbid = FastJSONObjAttrToNumber.toString(jsonObject,"item_id");
        this.yxtgxktghf = FastJSONObjAttrToNumber.toDouble(jsonObject,"pay_cy_amt");
        this.yxtgxktgcjje = FastJSONObjAttrToNumber.toDouble(jsonObject,"div_pay_amt");
    }


    public double getYxtgxktghf() {
        return yxtgxktghf;
    }

    public void setYxtgxktghf(double yxtgxktghf) {
        this.yxtgxktghf = yxtgxktghf;
    }

    public double getYxtgxktgcjje() {
        return yxtgxktgcjje;
    }

    public void setYxtgxktgcjje(double yxtgxktgcjje) {
        this.yxtgxktgcjje = yxtgxktgcjje;
    }

    @Override
    public boolean compare(SqliteSp sqliteSp) {
        if (sqliteSp == null) {
            return false;
        }

        JSONObject jsonObject = JSONObject.parseObject(sqliteSp.getOtherOrDefault());
        return FastJSONObjAttrToNumber.toDouble(jsonObject, "yxtgxktghf") == this.getYxtgxktghf()
                && FastJSONObjAttrToNumber.toDouble(jsonObject, "yxtgxktgcjje") == this.getYxtgxktgcjje() ;
    }
}
