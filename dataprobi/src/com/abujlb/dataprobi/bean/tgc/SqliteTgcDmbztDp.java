package com.abujlb.dataprobi.bean.tgc;


import com.abujlb.dataprobi.bean.AbstractSqliteDp;
import com.abujlb.dataprobi.bean.SqliteDp;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 * @date 2024/4/29
 */
public class SqliteTgcDmbztDp extends AbstractSqliteDp {

    //多目标直投花费
    private double dmbzthf;
    //多目标直投展现量
    private int dmbztzxl;
    //多目标直投点击量
    private int dmbztdjl;
    //多目标直投成交金额
    private double dmbztcjje;
    //多目标直投成交笔数
    private int dmbztcjbs;

    public double getDmbzthf() {
        return dmbzthf;
    }

    public void setDmbzthf(double dmbzthf) {
        this.dmbzthf = dmbzthf;
    }

    public int getDmbztzxl() {
        return dmbztzxl;
    }

    public void setDmbztzxl(int dmbztzxl) {
        this.dmbztzxl = dmbztzxl;
    }

    public int getDmbztdjl() {
        return dmbztdjl;
    }

    public void setDmbztdjl(int dmbztdjl) {
        this.dmbztdjl = dmbztdjl;
    }

    public double getDmbztcjje() {
        return dmbztcjje;
    }

    public void setDmbztcjje(double dmbztcjje) {
        this.dmbztcjje = dmbztcjje;
    }

    public int getDmbztcjbs() {
        return dmbztcjbs;
    }

    public void setDmbztcjbs(int dmbztcjbs) {
        this.dmbztcjbs = dmbztcjbs;
    }

    @Override
    public boolean compare(SqliteDp sqliteDp) {
        if (sqliteDp == null) {
            return false;
        }

        JSONObject jsonObject = JSONObject.parseObject(sqliteDp.getOtherOrDefault());
        return FastJSONObjAttrToNumber.toInt(jsonObject, "dmbztzxl") == this.getDmbztzxl()
                && FastJSONObjAttrToNumber.toInt(jsonObject, "dmbztdjl") == this.getDmbztdjl()
                && FastJSONObjAttrToNumber.toInt(jsonObject, "dmbztcjbs") == this.getDmbztcjbs()
                && FastJSONObjAttrToNumber.toDouble(jsonObject, "dmbztcjje") == this.getDmbztcjje()
                && FastJSONObjAttrToNumber.toDouble(jsonObject, "dmbzthf") == this.getDmbzthf();
    }
}
