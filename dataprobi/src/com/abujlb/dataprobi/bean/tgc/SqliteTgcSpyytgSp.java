package com.abujlb.dataprobi.bean.tgc;

import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.SqliteSp;
import com.abujlb.dataprobi.interfaces.Plusable;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.abujlb.dataprobi.util.MathUtil;
import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 * @date 2024/6/3
 */
public class SqliteTgcSpyytgSp extends AbstractSqliteSp implements Plusable<SqliteTgcSpyytgSp> {
    //商品运营托管 预估花费
    private double spyytgyghf;
    //商品运营托管 预估成交金额
    private double spyytgygcjje;

    public double getSpyytgyghf() {
        return spyytgyghf;
    }

    public void setSpyytgyghf(double spyytgyghf) {
        this.spyytgyghf = spyytgyghf;
    }

    public double getSpyytgygcjje() {
        return spyytgygcjje;
    }

    public void setSpyytgygcjje(double spyytgygcjje) {
        this.spyytgygcjje = spyytgygcjje;
    }

    @Override
    public void setValues(JSONObject jsonObject) {
        this.bbid = FastJSONObjAttrToNumber.toString(jsonObject, "itemId");
        this.spyytgyghf = FastJSONObjAttrToNumber.toDouble(jsonObject, "payOrdCost");
        this.spyytgygcjje = FastJSONObjAttrToNumber.toDouble(jsonObject, "payOrdAmt");
    }

    @Override
    public boolean compare(SqliteSp sqliteSp) {
        if (sqliteSp == null) {
            return false;
        }

        JSONObject jsonObject = JSONObject.parseObject(sqliteSp.getOtherOrDefault());
        return FastJSONObjAttrToNumber.toDouble(jsonObject, "spyytgyghf") == this.getSpyytgyghf() &&
                FastJSONObjAttrToNumber.toDouble(jsonObject, "spyytgygcjje") == this.getSpyytgygcjje();
    }

    @Override
    public void plus(SqliteTgcSpyytgSp sqliteTgcSpyytgSp) {
        this.spyytgygcjje = MathUtil.add(spyytgygcjje,sqliteTgcSpyytgSp.getSpyytgygcjje());
        this.spyytgyghf = MathUtil.add(spyytgyghf,sqliteTgcSpyytgSp.getSpyytgyghf());
    }
}
