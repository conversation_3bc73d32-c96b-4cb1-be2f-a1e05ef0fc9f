package com.abujlb.dataprobi.bean.tgc;

import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.SqliteSp;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 * @date 2024/5/6
 */
public class SqliteTgcXpdtSp extends AbstractSqliteSp {

    //淘工厂-新品代投-花费
    private double tgcxpdthf;
    //淘工厂-新品代投-成交金额
    private double tgcxpdtcjje;
    //淘工厂-新品代投-成交笔数
    private int tgcxpdtcjbs;
    //淘工厂-新品代投-点击量
    private int tgcxpdtdjl;
    //淘工厂-新品代投-展现量
    private int tgcxpdtzxl;

    public SqliteTgcXpdtSp() {
    }

    public double getTgcxpdthf() {
        return tgcxpdthf;
    }

    public void setTgcxpdthf(double tgcxpdthf) {
        this.tgcxpdthf = tgcxpdthf;
    }

    public double getTgcxpdtcjje() {
        return tgcxpdtcjje;
    }

    public void setTgcxpdtcjje(double tgcxpdtcjje) {
        this.tgcxpdtcjje = tgcxpdtcjje;
    }

    public int getTgcxpdtcjbs() {
        return tgcxpdtcjbs;
    }

    public void setTgcxpdtcjbs(int tgcxpdtcjbs) {
        this.tgcxpdtcjbs = tgcxpdtcjbs;
    }

    public int getTgcxpdtdjl() {
        return tgcxpdtdjl;
    }

    public void setTgcxpdtdjl(int tgcxpdtdjl) {
        this.tgcxpdtdjl = tgcxpdtdjl;
    }

    public int getTgcxpdtzxl() {
        return tgcxpdtzxl;
    }

    public void setTgcxpdtzxl(int tgcxpdtzxl) {
        this.tgcxpdtzxl = tgcxpdtzxl;
    }

    @Override
    public void setValues(JSONObject jsonObject) {
        this.bbid = FastJSONObjAttrToNumber.toString(jsonObject, "itemId");
        this.tgcxpdthf = FastJSONObjAttrToNumber.toDouble(jsonObject, "spendingAmount");
        this.tgcxpdtcjje = FastJSONObjAttrToNumber.toDouble(jsonObject, "ordGmv");
        this.tgcxpdtcjbs = FastJSONObjAttrToNumber.toInt(jsonObject, "payOrdCnt");
        this.tgcxpdtdjl = FastJSONObjAttrToNumber.toInt(jsonObject, "hitCnt");
        this.tgcxpdtzxl = FastJSONObjAttrToNumber.toInt(jsonObject, "pv");
    }

    @Override
    public boolean compare(SqliteSp sqliteSp) {
        if (sqliteSp == null) {
            return false;
        }

        JSONObject jsonObject = JSONObject.parseObject(sqliteSp.getOtherOrDefault());
        return FastJSONObjAttrToNumber.toDouble(jsonObject, "tgcxpdthf") == this.getTgcxpdthf()
                && FastJSONObjAttrToNumber.toDouble(jsonObject, "tgcxpdtcjje") == this.getTgcxpdtcjje()
                && FastJSONObjAttrToNumber.toInt(jsonObject, "tgcxpdtcjbs") == this.getTgcxpdtcjbs()
                && FastJSONObjAttrToNumber.toInt(jsonObject, "tgcxpdtdjl") == this.getTgcxpdtdjl()
                && FastJSONObjAttrToNumber.toInt(jsonObject, "tgcxpdtzxl") == this.getTgcxpdtzxl();
    }
}
