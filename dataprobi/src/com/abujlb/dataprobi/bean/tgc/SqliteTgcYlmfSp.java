package com.abujlb.dataprobi.bean.tgc;

import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.SqliteSp;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 * @date 2024/5/22
 */
public class SqliteTgcYlmfSp extends AbstractSqliteSp {
    //引力魔方花费
    private double ylmfhf;
    //引力魔方展现量
    private int ylmfzxl;
    //引力魔方点击量
    private int ylmfdjl;
    //引力魔方成交金额
    private double ylmfcjje;
    //引力魔方成交笔数
    private int ylmfcjbs;

    public double getYlmfhf() {
        return ylmfhf;
    }

    public void setYlmfhf(double ylmfhf) {
        this.ylmfhf = ylmfhf;
    }

    public int getYlmfzxl() {
        return ylmfzxl;
    }

    public void setYlmfzxl(int ylmfzxl) {
        this.ylmfzxl = ylmfzxl;
    }

    public int getYlmfdjl() {
        return ylmfdjl;
    }

    public void setYlmfdjl(int ylmfdjl) {
        this.ylmfdjl = ylmfdjl;
    }

    public double getYlmfcjje() {
        return ylmfcjje;
    }

    public void setYlmfcjje(double ylmfcjje) {
        this.ylmfcjje = ylmfcjje;
    }

    public int getYlmfcjbs() {
        return ylmfcjbs;
    }

    public void setYlmfcjbs(int ylmfcjbs) {
        this.ylmfcjbs = ylmfcjbs;
    }
    @Override
    public boolean compare(SqliteSp sqliteSp) {
        if (sqliteSp == null) {
            return false;
        }

        JSONObject jsonObject = JSONObject.parseObject(sqliteSp.getOtherOrDefault());
        return FastJSONObjAttrToNumber.toInt(jsonObject, "ylmfzxl") == this.getYlmfzxl()
                && FastJSONObjAttrToNumber.toInt(jsonObject, "ylmfdjl") == this.getYlmfdjl()
                && FastJSONObjAttrToNumber.toInt(jsonObject, "ylmfcjbs") == this.getYlmfcjbs()
                && FastJSONObjAttrToNumber.toDouble(jsonObject, "ylmfcjje") == this.getYlmfcjje()
                && FastJSONObjAttrToNumber.toDouble(jsonObject, "ylmfhf") == this.getYlmfhf();
    }
}
