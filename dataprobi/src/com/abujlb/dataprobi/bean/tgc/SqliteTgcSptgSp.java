package com.abujlb.dataprobi.bean.tgc;

import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.SqliteSp;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 * @date 2024/12/25
 */
public class SqliteTgcSptgSp extends AbstractSqliteSp {
    //商品推广 日预算消耗花费
    private double sptg_ryy_hf;
    //商品推广 周期预算消耗花费
    private double sptg_zqyy_hf;
    //商品推广  推广佣金预估花费
    private double sptg_tgyj_hf;
    //商品推广  预算加码花费
    private double sptg_ysjm_hf;

    public SqliteTgcSptgSp() {
    }

    public double getSptg_ryy_hf() {
        return sptg_ryy_hf;
    }

    public void setSptg_ryy_hf(double sptg_ryy_hf) {
        this.sptg_ryy_hf = sptg_ryy_hf;
    }

    public double getSptg_zqyy_hf() {
        return sptg_zqyy_hf;
    }

    public void setSptg_zqyy_hf(double sptg_zqyy_hf) {
        this.sptg_zqyy_hf = sptg_zqyy_hf;
    }

    public double getSptg_tgyj_hf() {
        return sptg_tgyj_hf;
    }

    public void setSptg_tgyj_hf(double sptg_tgyj_hf) {
        this.sptg_tgyj_hf = sptg_tgyj_hf;
    }

    public double getSptg_ysjm_hf() {
        return sptg_ysjm_hf;
    }

    public void setSptg_ysjm_hf(double sptg_ysjm_hf) {
        this.sptg_ysjm_hf = sptg_ysjm_hf;
    }


    @Override
    public boolean compare(SqliteSp sqliteSp) {
        if (sqliteSp == null) {
            return false;
        }

        JSONObject jsonObject = JSONObject.parseObject(sqliteSp.getOtherOrDefault());
        return FastJSONObjAttrToNumber.toDouble(jsonObject, "sptg_ryy_hf") == this.getSptg_ryy_hf() &&
                FastJSONObjAttrToNumber.toDouble(jsonObject, "sptg_zqyy_hf") == this.getSptg_zqyy_hf() &&
                FastJSONObjAttrToNumber.toDouble(jsonObject, "sptg_tgyj_hf") == this.getSptg_tgyj_hf() &&
                FastJSONObjAttrToNumber.toDouble(jsonObject, "sptg_ysjm_hf") == this.getSptg_ysjm_hf();
    }

}
