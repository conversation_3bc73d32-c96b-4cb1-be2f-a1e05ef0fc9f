package com.abujlb.dataprobi.bean.tgc;

import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.SqliteSp;
import com.abujlb.dataprobi.interfaces.Plusable;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.abujlb.dataprobi.util.MathUtil;
import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 * @date 2024/4/29
 */
public class SqliteTgcSpxgSp extends AbstractSqliteSp implements Plusable<SqliteTgcSpxgSp> {
    //支付订单数
    private int spxgzfdds;
    //客单价
    private double spxgkdj;
    //笔单价
    private double spxgbdj;

    public int getSpxgzfdds() {
        return spxgzfdds;
    }

    public void setSpxgzfdds(int spxgzfdds) {
        this.spxgzfdds = spxgzfdds;
    }

    public double getSpxgkdj() {
        return spxgkdj;
    }

    public void setSpxgkdj(double spxgkdj) {
        this.spxgkdj = spxgkdj;
    }

    public double getSpxgbdj() {
        return spxgbdj;
    }

    public void setSpxgbdj(double spxgbdj) {
        this.spxgbdj = spxgbdj;
    }

    @Override
    public void setValues(JSONObject jsonObject) {
        this.bbid = FastJSONObjAttrToNumber.toString(jsonObject, "itemId");
        this.spxgzfdds = FastJSONObjAttrToNumber.toInt(jsonObject.getJSONObject("figureShowValMap").getJSONObject("pay_ord_cnt"), "figureVal");
        this.spxgkdj = FastJSONObjAttrToNumber.toDouble(jsonObject.getJSONObject("figureShowValMap").getJSONObject("cap"), "figureVal");
        this.spxgzfmjs = FastJSONObjAttrToNumber.toInt(jsonObject.getJSONObject("figureShowValMap").getJSONObject("pay_byr_cnt"), "figureVal");
        this.spxgzfje = FastJSONObjAttrToNumber.toDouble(jsonObject.getJSONObject("figureShowValMap").getJSONObject("pay_ord_amt"), "figureVal");
        this.spxgzfzhl = FastJSONObjAttrToNumber.toDouble(jsonObject.getJSONObject("figureShowValMap").getJSONObject("ipv_cvr"), "figureVal");
        this.spxgzfjs = FastJSONObjAttrToNumber.toInt(jsonObject.getJSONObject("figureShowValMap").getJSONObject("pay_qty_cnt"), "figureVal");
        this.spxgfks = FastJSONObjAttrToNumber.toInt(jsonObject.getJSONObject("figureShowValMap").getJSONObject("iuv"), "figureVal");
        this.spxglll = FastJSONObjAttrToNumber.toInt(jsonObject.getJSONObject("figureShowValMap").getJSONObject("ipv"), "figureVal");
        this.spxgbdj = FastJSONObjAttrToNumber.toDouble(jsonObject.getJSONObject("figureShowValMap").getJSONObject("oap"), "figureVal");
    }

    @Override
    public void plus(SqliteTgcSpxgSp sqliteTgcSpxgSp) {
        this.spxgzfdds += sqliteTgcSpxgSp.getSpxgzfdds();
        this.spxgzfmjs += sqliteTgcSpxgSp.getSpxgzfmjs();
        this.spxgzfje = MathUtil.add(spxgzfje, sqliteTgcSpxgSp.getSpxgzfje());
        this.spxgzfjs += sqliteTgcSpxgSp.getSpxgzfjs();
        this.spxgfks += sqliteTgcSpxgSp.getSpxgfks();
        this.spxglll += sqliteTgcSpxgSp.getSpxglll();

        this.spxgkdj = MathUtil.calZhlv(this.spxgzfje, this.spxgzfmjs);
        this.spxgzfzhl = MathUtil.calZhlv(this.spxgzfmjs, this.spxgfks);
        this.spxgbdj = MathUtil.divide(this.spxgzfje, this.spxgzfdds);
    }

    @Override
    public boolean compare(SqliteSp sqliteSp) {
        if (sqliteSp == null) {
            return false;
        }

        JSONObject jsonObject = JSONObject.parseObject(sqliteSp.getOtherOrDefault());
        return FastJSONObjAttrToNumber.toInt(jsonObject, "spxgzfdds") == this.getSpxgzfdds()
                && FastJSONObjAttrToNumber.toDouble(jsonObject, "spxgkdj") == this.getSpxgkdj()
                && sqliteSp.getSpxgzfmjs() == this.getSpxgzfmjs()
                && sqliteSp.getSpxgzfje() == this.getSpxgzfje()
                && sqliteSp.getSpxgzfzhl() == this.getSpxgzfzhl()
                && sqliteSp.getSpxgzfjs() == this.getSpxgzfjs()
                && sqliteSp.getSpxgfks() == this.getSpxgfks()
                && sqliteSp.getSpxglll() == this.getSpxglll()
                && FastJSONObjAttrToNumber.toDouble(jsonObject, "spxgbdj") == this.getSpxgbdj();
    }
}
