package com.abujlb.dataprobi.bean.tgc;


import com.abujlb.dataprobi.bean.AbstractSqliteDp;
import com.abujlb.dataprobi.bean.SqliteDp;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 * @date 2024/4/29
 */
public class SqliteTgcAiztDp extends AbstractSqliteDp {

    //万相台花费
    private double aizthf;
    //万相台展现量
    private int aiztzxl;
    //万相台点击量
    private int aiztdjl;
    //万相台成交金额
    private double aiztcjje;
    //万相台成交笔数
    private int aiztcjbs;

    public double getAizthf() {
        return aizthf;
    }

    public void setAizthf(double aizthf) {
        this.aizthf = aizthf;
    }

    public int getAiztzxl() {
        return aiztzxl;
    }

    public void setAiztzxl(int aiztzxl) {
        this.aiztzxl = aiztzxl;
    }

    public int getAiztdjl() {
        return aiztdjl;
    }

    public void setAiztdjl(int aiztdjl) {
        this.aiztdjl = aiztdjl;
    }

    public double getAiztcjje() {
        return aiztcjje;
    }

    public void setAiztcjje(double aiztcjje) {
        this.aiztcjje = aiztcjje;
    }

    public int getAiztcjbs() {
        return aiztcjbs;
    }

    public void setAiztcjbs(int aiztcjbs) {
        this.aiztcjbs = aiztcjbs;
    }

    @Override
    public boolean compare(SqliteDp sqliteDp) {
        if (sqliteDp == null) {
            return false;
        }

        JSONObject jsonObject = JSONObject.parseObject(sqliteDp.getOtherOrDefault());
        return FastJSONObjAttrToNumber.toInt(jsonObject, "aiztzxl") == this.getAiztzxl()
                && FastJSONObjAttrToNumber.toInt(jsonObject, "aiztdjl") == this.getAiztdjl()
                && FastJSONObjAttrToNumber.toInt(jsonObject, "aiztcjbs") == this.getAiztcjbs()
                && FastJSONObjAttrToNumber.toDouble(jsonObject, "aiztcjje") == this.getAiztcjje()
                && FastJSONObjAttrToNumber.toDouble(jsonObject, "aizthf") == this.getAizthf();
    }
}
