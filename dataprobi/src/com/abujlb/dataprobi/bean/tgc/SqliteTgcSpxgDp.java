package com.abujlb.dataprobi.bean.tgc;


import com.abujlb.dataprobi.bean.AbstractSqliteDp;
import com.abujlb.dataprobi.bean.SqliteDp;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 * @date 2024/4/29
 */
public class SqliteTgcSpxgDp extends AbstractSqliteDp {

    // 支付子订单
    private int spxgzfzdd;
    // 客单价
    private double spxgkdj;

    public SqliteTgcSpxgDp() {
    }

    public int getSpxgzfzdd() {
        return spxgzfzdd;
    }

    public void setSpxgzfzdd(int spxgzfzdd) {
        this.spxgzfzdd = spxgzfzdd;
    }

    public double getSpxgkdj() {
        return spxgkdj;
    }

    public void setSpxgkdj(double spxgkdj) {
        this.spxgkdj = spxgkdj;
    }

    @Override
    public <T extends AbstractSqliteDp> void setValues(String[] tableStoreColumnValues) {
        String value0 = tableStoreColumnValues[0];
        if (value0 != null) {
            JSONObject jsonObject = JSONObject.parseObject(value0);
            this.spxgzfzdd = FastJSONObjAttrToNumber.toInt(jsonObject, "zfzdd");
            this.spxgzfje = FastJSONObjAttrToNumber.toDouble(jsonObject, "zfje");
            this.spxgzfjs = FastJSONObjAttrToNumber.toInt(jsonObject, "zfjs");
            this.spxgzfzhl = FastJSONObjAttrToNumber.toDouble(jsonObject, "zfzhlv");
            this.spxgkdj = FastJSONObjAttrToNumber.toDouble(jsonObject, "kdj");
            this.spxgtkje = FastJSONObjAttrToNumber.toDouble(jsonObject, "tkje");
        }

        String value1 = tableStoreColumnValues[1];
        if (value1 != null) {
            JSONObject jsonObject = JSONObject.parseObject(value1);
            this.spxgzfmjs = FastJSONObjAttrToNumber.toInt(jsonObject, "zfmjs");
            this.spxgfks = FastJSONObjAttrToNumber.toInt(jsonObject, "fks");
            this.spxglll = FastJSONObjAttrToNumber.toInt(jsonObject, "lll");
        }

        String value2 = tableStoreColumnValues[2];
        if (value2 != null) {
            JSONObject jsonObject = JSONObject.parseObject(value2);
            this.spxgfks = FastJSONObjAttrToNumber.toInt(jsonObject, "iuv", "figureVal");
        }
    }

    @Override
    public boolean compare(SqliteDp sqliteDp) {
        if (sqliteDp == null) {
            return false;
        }

        JSONObject jsonObject = JSONObject.parseObject(sqliteDp.getOtherOrDefault());
        return FastJSONObjAttrToNumber.toInt(jsonObject, "spxgzfzdd") == this.getSpxgzfzdd()
                && sqliteDp.getSpxgzfje() == this.getSpxgzfje()
                && sqliteDp.getSpxgfks() == this.getSpxgfks()
                && sqliteDp.getSpxgzfjs() == this.getSpxgzfjs()
                && sqliteDp.getSpxgzfzhl() == this.getSpxgzfzhl()
                && FastJSONObjAttrToNumber.toDouble(jsonObject, "spxgkdj") == this.getSpxgkdj()
                && sqliteDp.getSpxgtkje() == this.getSpxgtkje()
                && sqliteDp.getSpxgzfmjs() == this.getSpxgzfmjs();
    }
}
