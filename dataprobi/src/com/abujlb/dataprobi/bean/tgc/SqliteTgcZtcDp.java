package com.abujlb.dataprobi.bean.tgc;


import com.abujlb.dataprobi.bean.AbstractSqliteDp;
import com.abujlb.dataprobi.bean.SqliteDp;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 * @date 2024/4/29
 */
public class SqliteTgcZtcDp extends AbstractSqliteDp {

    //直通车花费
    private double ztchf;
    //直通车展现量
    private int ztczxl;
    //直通车点击量
    private int ztcdjl;
    //直通车成交金额
    private double ztccjje;
    //直通车成交笔数
    private int ztccjbs;

    public double getZtchf() {
        return ztchf;
    }

    public void setZtchf(double ztchf) {
        this.ztchf = ztchf;
    }

    public int getZtczxl() {
        return ztczxl;
    }

    public void setZtczxl(int ztczxl) {
        this.ztczxl = ztczxl;
    }

    public int getZtcdjl() {
        return ztcdjl;
    }

    public void setZtcdjl(int ztcdjl) {
        this.ztcdjl = ztcdjl;
    }

    public double getZtccjje() {
        return ztccjje;
    }

    public void setZtccjje(double ztccjje) {
        this.ztccjje = ztccjje;
    }

    public int getZtccjbs() {
        return ztccjbs;
    }

    public void setZtccjbs(int ztccjbs) {
        this.ztccjbs = ztccjbs;
    }


    @Override
    public boolean compare(SqliteDp sqliteDp) {
        if (sqliteDp == null) {
            return false;
        }

        JSONObject jsonObject = JSONObject.parseObject(sqliteDp.getOtherOrDefault());
        return FastJSONObjAttrToNumber.toInt(jsonObject, "ztczxl") == this.getZtczxl()
                && FastJSONObjAttrToNumber.toInt(jsonObject, "ztcdjl") == this.getZtcdjl()
                && FastJSONObjAttrToNumber.toInt(jsonObject, "ztccjbs") == this.getZtccjbs()
                && FastJSONObjAttrToNumber.toDouble(jsonObject, "ztccjje") == this.getZtccjje()
                && FastJSONObjAttrToNumber.toDouble(jsonObject, "ztchf") == this.getZtchf();
    }
}
