package com.abujlb.dataprobi.bean.tgc;


import com.abujlb.dataprobi.bean.AbstractSqliteDp;

/**
 * <AUTHOR>
 * @date 2024/5/6
 */
public class SqliteTgcXpdtDp extends AbstractSqliteDp {

    //淘工厂-新品代投-花费
    private double tgcxpdthf;
    //淘工厂-新品代投-成交金额
    private double tgcxpdtcjje;
    //淘工厂-新品代投-成交笔数
    private int tgcxpdtcjbs;
    //淘工厂-新品代投-点击量
    private int tgcxpdtdjl;
    //淘工厂-新品代投-展现量
    private int tgcxpdtzxl;

    public SqliteTgcXpdtDp() {
    }

    public double getTgcxpdthf() {
        return tgcxpdthf;
    }

    public void setTgcxpdthf(double tgcxpdthf) {
        this.tgcxpdthf = tgcxpdthf;
    }

    public double getTgcxpdtcjje() {
        return tgcxpdtcjje;
    }

    public void setTgcxpdtcjje(double tgcxpdtcjje) {
        this.tgcxpdtcjje = tgcxpdtcjje;
    }

    public int getTgcxpdtcjbs() {
        return tgcxpdtcjbs;
    }

    public void setTgcxpdtcjbs(int tgcxpdtcjbs) {
        this.tgcxpdtcjbs = tgcxpdtcjbs;
    }

    public int getTgcxpdtdjl() {
        return tgcxpdtdjl;
    }

    public void setTgcxpdtdjl(int tgcxpdtdjl) {
        this.tgcxpdtdjl = tgcxpdtdjl;
    }

    public int getTgcxpdtzxl() {
        return tgcxpdtzxl;
    }

    public void setTgcxpdtzxl(int tgcxpdtzxl) {
        this.tgcxpdtzxl = tgcxpdtzxl;
    }
}
