package com.abujlb.dataprobi.bean.tgc;


import com.abujlb.dataprobi.bean.AbstractSqliteDp;

/**
 * <AUTHOR>
 * @date 2024/12/25
 */
public class SqliteTgcTktgDp extends AbstractSqliteDp {
    //淘客推广-单品平推-花费
    private double tktg_dppt_hf;
    //淘客推广-单品平推-成交金额
    private double tktg_dppt_cjje;
    //淘客推广-单品加速-花费
    private double tktg_dpjs_hf;
    //淘客推广-单品加速-成交金额
    private double tktg_dpjs_cjje;

    public SqliteTgcTktgDp() {
    }

    public double getTktg_dppt_hf() {
        return tktg_dppt_hf;
    }

    public void setTktg_dppt_hf(double tktg_dppt_hf) {
        this.tktg_dppt_hf = tktg_dppt_hf;
    }

    public double getTktg_dppt_cjje() {
        return tktg_dppt_cjje;
    }

    public void setTktg_dppt_cjje(double tktg_dppt_cjje) {
        this.tktg_dppt_cjje = tktg_dppt_cjje;
    }

    public double getTktg_dpjs_hf() {
        return tktg_dpjs_hf;
    }

    public void setTktg_dpjs_hf(double tktg_dpjs_hf) {
        this.tktg_dpjs_hf = tktg_dpjs_hf;
    }

    public double getTktg_dpjs_cjje() {
        return tktg_dpjs_cjje;
    }

    public void setTktg_dpjs_cjje(double tktg_dpjs_cjje) {
        this.tktg_dpjs_cjje = tktg_dpjs_cjje;
    }
}
