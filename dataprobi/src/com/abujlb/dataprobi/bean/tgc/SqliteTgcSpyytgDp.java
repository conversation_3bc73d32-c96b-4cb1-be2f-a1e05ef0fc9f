package com.abujlb.dataprobi.bean.tgc;


import com.abujlb.dataprobi.bean.AbstractSqliteDp;

/**
 * <AUTHOR>
 * @date 2024/5/6
 */
public class SqliteTgcSpyytgDp extends AbstractSqliteDp {
    //商品运营托管 预估花费
    private double spyytgyghf;
    //商品运营托管 预估成交金额
    private double spyytgygcjje;

    public SqliteTgcSpyytgDp() {
    }

    public double getSpyytgyghf() {
        return spyytgyghf;
    }

    public void setSpyytgyghf(double spyytgyghf) {
        this.spyytgyghf = spyytgyghf;
    }

    public double getSpyytgygcjje() {
        return spyytgygcjje;
    }

    public void setSpyytgygcjje(double spyytgygcjje) {
        this.spyytgygcjje = spyytgygcjje;
    }
}
