package com.abujlb.dataprobi.bean.tgc;

import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.SqliteSp;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2024/12/25
 */
public class SqliteTgcTktgSp extends AbstractSqliteSp {
    //淘客推广-单品平推-花费
    private double tktg_dppt_hf;
    //淘客推广-单品平推-成交金额
    private double tktg_dppt_cjje;
    //淘客推广-单品加速-花费
    private double tktg_dpjs_hf;
    //淘客推广-单品加速-花费
    private double tktg_dpjs_cjje;

    public SqliteTgcTktgSp() {
    }

    public double getTktg_dppt_hf() {
        return tktg_dppt_hf;
    }

    public void setTktg_dppt_hf(double tktg_dppt_hf) {
        this.tktg_dppt_hf = tktg_dppt_hf;
    }

    public double getTktg_dppt_cjje() {
        return tktg_dppt_cjje;
    }

    public void setTktg_dppt_cjje(double tktg_dppt_cjje) {
        this.tktg_dppt_cjje = tktg_dppt_cjje;
    }

    public double getTktg_dpjs_hf() {
        return tktg_dpjs_hf;
    }

    public void setTktg_dpjs_hf(double tktg_dpjs_hf) {
        this.tktg_dpjs_hf = tktg_dpjs_hf;
    }

    public double getTktg_dpjs_cjje() {
        return tktg_dpjs_cjje;
    }

    public void setTktg_dpjs_cjje(double tktg_dpjs_cjje) {
        this.tktg_dpjs_cjje = tktg_dpjs_cjje;
    }

    @Override
    public void setValues(JSONObject jsonObject) {
        this.bbid = FastJSONObjAttrToNumber.toString(jsonObject, "itemInfo", "itemId");
        String tkType = FastJSONObjAttrToNumber.toString(jsonObject, "promotionStatus", "tkType", "key");
        if (StringUtils.isBlank(tkType)) {
            return;
        }

        if (tkType.equals("low_commission_push_supplier_wholly")) {
            this.tktg_dppt_hf = FastJSONObjAttrToNumber.toDouble(jsonObject, "predictCommission");
            this.tktg_dppt_cjje = FastJSONObjAttrToNumber.toDouble(jsonObject, "orderAmount");
        } else if (tkType.equals("tgc_pay")) {
            this.tktg_dpjs_hf = FastJSONObjAttrToNumber.toDouble(jsonObject, "predictCommission");
            this.tktg_dpjs_cjje = FastJSONObjAttrToNumber.toDouble(jsonObject, "orderAmount");
        }
    }

    @Override
    public boolean compare(SqliteSp sqliteSp) {
        if (sqliteSp == null) {
            return false;
        }

        JSONObject jsonObject = JSONObject.parseObject(sqliteSp.getOtherOrDefault());
        return FastJSONObjAttrToNumber.toDouble(jsonObject, "tktg_dppt_hf") == this.getTktg_dppt_hf()
                && FastJSONObjAttrToNumber.toDouble(jsonObject, "tktg_dppt_cjje") == this.getTktg_dppt_cjje()
                && FastJSONObjAttrToNumber.toDouble(jsonObject, "tktg_dpjs_hf") == this.getTktg_dpjs_hf()
                && FastJSONObjAttrToNumber.toDouble(jsonObject, "tktg_dpjs_cjje") == this.getTktg_dpjs_cjje();
    }

}
