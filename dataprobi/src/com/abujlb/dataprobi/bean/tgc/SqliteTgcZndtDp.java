package com.abujlb.dataprobi.bean.tgc;


import com.abujlb.dataprobi.bean.AbstractSqliteDp;

/**
 * <AUTHOR>
 * @date 2024/5/6
 */
public class SqliteTgcZndtDp extends AbstractSqliteDp {

    //淘工厂-智能代投-花费
    private double tgczndthf;
    //淘工厂-智能代投-成交金额
    private double tgczndtcjje;
    //淘工厂-智能代投-成交笔数
    private int tgczndtcjbs;
    //淘工厂-智能代投-点击量
    private int tgczndtdjl;
    //淘工厂-智能代投-展现量
    private int tgczndtzxl;

    public SqliteTgcZndtDp() {
    }

    public double getTgczndthf() {
        return tgczndthf;
    }

    public void setTgczndthf(double tgczndthf) {
        this.tgczndthf = tgczndthf;
    }

    public double getTgczndtcjje() {
        return tgczndtcjje;
    }

    public void setTgczndtcjje(double tgczndtcjje) {
        this.tgczndtcjje = tgczndtcjje;
    }

    public int getTgczndtcjbs() {
        return tgczndtcjbs;
    }

    public void setTgczndtcjbs(int tgczndtcjbs) {
        this.tgczndtcjbs = tgczndtcjbs;
    }

    public int getTgczndtdjl() {
        return tgczndtdjl;
    }

    public void setTgczndtdjl(int tgczndtdjl) {
        this.tgczndtdjl = tgczndtdjl;
    }

    public int getTgczndtzxl() {
        return tgczndtzxl;
    }

    public void setTgczndtzxl(int tgczndtzxl) {
        this.tgczndtzxl = tgczndtzxl;
    }
}
