package com.abujlb.dataprobi.bean.tgc;

import com.abujlb.dataprobi.annotations.ClrqIgnore;
import com.abujlb.dataprobi.annotations.SumIgnore;
import com.abujlb.dataprobi.annotations.UpdateSycmcljzrqIgnore;
import com.abujlb.dataprobi.bean.DataprobiBaseMsgBean;
import com.alibaba.fastjson.JSON;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/26
 */
public class DataprobiTgcMsg extends DataprobiBaseMsgBean {

    @UpdateSycmcljzrqIgnore
    private List<String> spxg;
    private List<String> spxgdp;
    private List<String> ztc;
    private List<String> ylmf;
    private List<String> aizt;
    private List<String> dmbzt;
    private List<String> spyytg;
    private List<String> sptg;
    private List<String> tktg;
    @UpdateSycmcljzrqIgnore
    private List<String> cpgl;

    @UpdateSycmcljzrqIgnore
    @ClrqIgnore
    @SumIgnore
    private List<String> money;

    public DataprobiTgcMsg() {
    }

    public List<String> getMoney() {
        return money;
    }

    public void setMoney(List<String> money) {
        this.money = money;
    }

    public List<String> getSpxg() {
        return spxg;
    }

    public void setSpxg(List<String> spxg) {
        this.spxg = spxg;
    }

    public List<String> getSpxgdp() {
        return spxgdp;
    }

    public void setSpxgdp(List<String> spxgdp) {
        this.spxgdp = spxgdp;
    }

    public List<String> getZtc() {
        return ztc;
    }

    public void setZtc(List<String> ztc) {
        this.ztc = ztc;
    }

    public List<String> getYlmf() {
        return ylmf;
    }

    public void setYlmf(List<String> ylmf) {
        this.ylmf = ylmf;
    }

    public List<String> getAizt() {
        return aizt;
    }

    public void setAizt(List<String> aizt) {
        this.aizt = aizt;
    }

    public List<String> getDmbzt() {
        return dmbzt;
    }

    public void setDmbzt(List<String> dmbzt) {
        this.dmbzt = dmbzt;
    }

    public List<String> getSpyytg() {
        return spyytg;
    }

    public void setSpyytg(List<String> spyytg) {
        this.spyytg = spyytg;
    }

    public List<String> getCpgl() {
        return cpgl;
    }

    public void setCpgl(List<String> cpgl) {
        this.cpgl = cpgl;
    }

    public List<String> getSptg() {
        return sptg;
    }

    public void setSptg(List<String> sptg) {
        this.sptg = sptg;
    }

    public List<String> getTktg() {
        return tktg;
    }

    public void setTktg(List<String> tktg) {
        this.tktg = tktg;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }

    public void fillNullList() throws InvocationTargetException, IllegalAccessException {
        Method[] methods = this.getClass().getMethods();
        for (Method method : methods) {
            if (method.getName().startsWith("get")) {
                Object value = method.invoke(this);
                if (value == null) {
                    Method setMethod = null;
                    try {
                        setMethod = this.getClass().getMethod("s" + method.getName().substring(1), List.class);
                        setMethod.invoke(this, new ArrayList<>());
                    } catch (NoSuchMethodException ignored) {
                    }
                }
            }
        }
    }
}
