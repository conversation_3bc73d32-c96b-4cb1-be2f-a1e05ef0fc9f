package com.abujlb.dataprobi.bean.tgc;

import com.abujlb.dataprobi.bean.AbstractSqliteSp;
import com.abujlb.dataprobi.bean.SqliteSp;
import com.abujlb.dataprobi.util.FastJSONObjAttrToNumber;
import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 * @date 2024/5/6
 */
public class SqliteTgcZndtSp extends AbstractSqliteSp {

    //淘工厂-智能代投-花费
    private double tgczndthf;
    //淘工厂-智能代投-成交金额
    private double tgczndtcjje;
    //淘工厂-智能代投-成交笔数
    private int tgczndtcjbs;
    //淘工厂-智能代投-点击量
    private int tgczndtdjl;
    //淘工厂-智能代投-展现量
    private int tgczndtzxl;

    public SqliteTgcZndtSp() {
    }

    public double getTgczndthf() {
        return tgczndthf;
    }

    public void setTgczndthf(double tgczndthf) {
        this.tgczndthf = tgczndthf;
    }

    public double getTgczndtcjje() {
        return tgczndtcjje;
    }

    public void setTgczndtcjje(double tgczndtcjje) {
        this.tgczndtcjje = tgczndtcjje;
    }

    public int getTgczndtcjbs() {
        return tgczndtcjbs;
    }

    public void setTgczndtcjbs(int tgczndtcjbs) {
        this.tgczndtcjbs = tgczndtcjbs;
    }

    public int getTgczndtdjl() {
        return tgczndtdjl;
    }

    public void setTgczndtdjl(int tgczndtdjl) {
        this.tgczndtdjl = tgczndtdjl;
    }

    public int getTgczndtzxl() {
        return tgczndtzxl;
    }

    public void setTgczndtzxl(int tgczndtzxl) {
        this.tgczndtzxl = tgczndtzxl;
    }

    @Override
    public void setValues(JSONObject jsonObject) {
        this.bbid = FastJSONObjAttrToNumber.toString(jsonObject, "itemId");
        this.tgczndthf = FastJSONObjAttrToNumber.toDouble(jsonObject, "spendingAmount");
        this.tgczndtcjje = FastJSONObjAttrToNumber.toDouble(jsonObject, "ordGmv");
        this.tgczndtcjbs = FastJSONObjAttrToNumber.toInt(jsonObject, "payOrdCnt");
        this.tgczndtdjl = FastJSONObjAttrToNumber.toInt(jsonObject, "hitCnt");
        this.tgczndtzxl = FastJSONObjAttrToNumber.toInt(jsonObject, "pv");
    }

    @Override
    public boolean compare(SqliteSp sqliteSp) {
        if (sqliteSp == null) {
            return false;
        }

        JSONObject jsonObject = JSONObject.parseObject(sqliteSp.getOtherOrDefault());
        return FastJSONObjAttrToNumber.toDouble(jsonObject, "tgczndthf") == this.getTgczndthf()
                && FastJSONObjAttrToNumber.toDouble(jsonObject, "tgczndtcjje") == this.getTgczndtcjje()
                && FastJSONObjAttrToNumber.toInt(jsonObject, "tgczndtcjbs") == this.getTgczndtcjbs()
                && FastJSONObjAttrToNumber.toInt(jsonObject, "tgczndtdjl") == this.getTgczndtdjl()
                && FastJSONObjAttrToNumber.toInt(jsonObject, "tgczndtzxl") == this.getTgczndtzxl();
    }
}
