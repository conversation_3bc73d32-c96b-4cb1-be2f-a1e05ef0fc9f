package com.abujlb.dataprobi.bean;

import java.util.Collections;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2022/6/7 10:00
 */
public class DataprobiBaseMsgBean {

    //时间戳
    protected String t;
    //uuid 表示消息唯一
    protected String uuid;

    //店铺userid
    protected String userid;
    //用户id
    protected int yhid;
    //渠道
    protected String qd;
    //bi_info表的ID
    protected int biinfoId;
    //店铺名称
    protected String dpmc;
    //首次店铺补采24个月数据的月日期集合
    //如： Arrays.asList("2022-04-30","2022-05-31"....);
    protected List<String> dpMonths;

    //是否处理商品列表
    private int splb;

//    //定制营销解决方案 0需要处理 1不需要处理
//    private int dzyxjjfa;

    //默认0 不验证  1登录时 2下午补发时
    private int type;

    //0程序发送 1手动补发 手动补发的都SUM  2手动补发且不SUM
    private int auto;

    public DataprobiBaseMsgBean() {
        this.t = System.currentTimeMillis() + "";
        this.uuid = UUID.randomUUID().toString();
    }

    public String getT() {
        return t;
    }

    public void setT(String t) {
        this.t = t;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getUserid() {
        return userid;
    }

    public void setUserid(String userid) {
        this.userid = userid;
    }

    public int getYhid() {
        return yhid;
    }

    public void setYhid(int yhid) {
        this.yhid = yhid;
    }

    public String getQd() {
        return qd;
    }

    public void setQd(String qd) {
        this.qd = qd;
    }

    public int getBiinfoId() {
        return biinfoId;
    }

    public void setBiinfoId(int biinfoId) {
        this.biinfoId = biinfoId;
    }

    public String getDpmc() {
        return dpmc;
    }

    public void setDpmc(String dpmc) {
        this.dpmc = dpmc;
    }

    public List<String> getDpMonths() {
        return dpMonths == null ? Collections.emptyList() : dpMonths;
    }

    public void setDpMonths(List<String> dpMonths) {
        this.dpMonths = dpMonths;
    }

    public int getSplb() {
        return splb;
    }

    public void setSplb(int splb) {
        this.splb = splb;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getAuto() {
        return auto;
    }

    public void setAuto(int auto) {
        this.auto = auto;
    }

//    public int getDzyxjjfa() {
//        return dzyxjjfa;
//    }
//
//    public void setDzyxjjfa(int dzyxjjfa) {
//        this.dzyxjjfa = dzyxjjfa;
//    }
}
