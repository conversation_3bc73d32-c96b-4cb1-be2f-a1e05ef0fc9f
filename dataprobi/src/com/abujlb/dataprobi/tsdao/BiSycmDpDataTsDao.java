package com.abujlb.dataprobi.tsdao;

import com.abujlb.dao.v2.TsDao2;
import com.alicloud.openservices.tablestore.SyncClient;
import com.alicloud.openservices.tablestore.model.*;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

@Component
public class BiSycmDpDataTsDao {

    private static final Logger log = Logger.getLogger(BiSycmDpDataTsDao.class);

    private static final String TSINSTANCE_NAME = "abujlb6";
    private static final String TABLE_NAME = "bi_sycmdpdata";

    /***************** 主键 **************************/
    private static final String COLUMN_USERID = "qd_userid";
    private static final String COLUMN_RQ = "rq";

    /***************** 数据键 **************************/
    public static final String TX_COLUMN_SPXG = "tx_spxg";
    public static final String TX_COLUMN_SPXG_JYSJ = "tx_jysj";
    public static final String TX_COLUMN_ZTC = "tx_ztc";
    public static final String TX_COLUMN_AIZT = "tx_aizt";
    public static final String TX_COLUMN_SYCMLEVEL = "tx_sycmlevel";
    public static final String TX_COLUMN_TBK = "tx_tbk";
    public static final String TX_COLUMN_AIZT2 = "tx_aizt2";
    public static final String TX_COLUMN_AIZT3 = "tx_aizt3";
    public static final String TX_COLUMN_YLMF = "tx_ylmf";
    public static final String TX_COLUMN_PXB = "tx_pxb";
    public static final String TX_COLUMN_RLYQ2 = "tx_rlyq2";
    public static final String TX_COLUMN_RLYQ_VTASK = "tx_rlyq_vtask";
    //新客加速-转化加速
    public static final String TX_COLUMN_PPXX_xkjs_zhjs = "tx_xkjs_zhjs";
    //新客加速-极致加速
    public static final String TX_COLUMN_PPXX_xkjs_jzjs = "tx_xkjs_jzjs";
    //新品孵化-转化加速
    public static final String TX_COLUMN_PPXX_xpfh_zhjs = "tx_xpfh_zhjs";
    //超级会员-转化加速
    public static final String TX_COLUMN_PPXX_cjhy_zhjs = "tx_cjhy_zhjs";
    //超级复购-会员复购加速
    public static final String TX_COLUMN_PPXX_cjfg_fgjs = "tx_cjfg_fgjs";
    //超级老客-老客加速
    public static final String TX_COLUMN_PPXX_cjlk_lkjs = "tx_cjlk_lkjs";
    //老客礼金-老客礼金
    public static final String TX_COLUMN_PPXX_lklj_lklj = "tx_lklj_lklj";
    //营销托管-新客托管
    public static final String TX_COLUMN_PPXX_yxtg_xktg = "tx_yxtg_xktg";
    //营销托管-新品托管
    public static final String TX_COLUMN_PPXX_yxtg_xptg = "tx_yxtg_xptg";
    //全店旺旺咨询量
    public static final String TX_COLUMN_FW_CUSTOMER = "tx_fwcustomer";
    //淘系全站推广
    public static final String TX_COLUMN_QZTG = "tx_qztg";
    //千牛店铺综合体验分
    public static final String TX_COLUMN_DPZHTYF = "tx_dpzhtyf";
    public static final String TX_COLUMN_QYYXTG = "tx_qyyxtg";

    public static final String PDD_COLUMN_SPXG = "pdd_spmx";
    public static final String PDD_COLUMN_SPXG_JYSJ = "pdd_jysj";
    public static final String PDD_COLUMN_SPXG_SHSJ = "pdd_shsj";
    public static final String PDD_COLUMN_DDJB = "pdd_ddjb";
    public static final String PDD_COLUMN_DDCJ = "pdd_ddcj";
    public static final String PDD_COLUMN_BZTG = "pdd_bztg";
    public static final String PDD_COLUMN_SPTG = "pdd_sptg";
    public static final String PDD_COLUMN_DDSS = "pdd_ddss";
    public static final String PDD_COLUMN_MXDP = "pdd_mxdp";
    public static final String PDD_COLUMN_QZTG = "pdd_qztg";
    public static final String PDD_COLUMN_ZBTG = "pdd_zbtg";
    public static final String PDD_COLUMN_ZHYX = "pdd_zhxy";
    public static final String PDD_COLUMN_SPXG_JYGK = "pdd_jygk";
    public static final String PDD_COLUMN_HBDK = "pdd_hbdk";

    public static final String JD_COLUMN_SPU = "jd_spu";
    public static final String JD_COLUMN_SKU = "jd_sku";
    public static final String JD_COLUMN_JYGK = "jd_jygk";
    public static final String JD_COLUMN_REFUND = "jd_refund";
    public static final String JD_COLUMN_FCS = "jd_fcs";


    public static final String DY_COLUMN_SPXG = "dy_spxg";
    public static final String DY_COLUMN_SPXG2 = "dy_spxg2";
    public static final String DY_COLUMN_JXLM = "dy_jxlm";
    public static final String DY_COLUMN_DSP = "dy_jlqc_dsp";
    public static final String DY_COLUMN_JLQC_CWJL = "dy_jlqc_cwjl";
    public static final String DY_COLUMN_JLQC_CWJL2 = "dy_jlqc_cwjl2";
    public static final String DY_COLUMN_JLQC_ZB = "dy_jlqc_zb";
    public static final String DY_COLUMN_PP = "dy_jlqc_pp";
    public static final String DY_COLUMN_QYTG = "dy_jlqc_qytg";
    public static final String DY_COLUMN_DR_ZY_SPXGDP = "dy_dr_zy_spxgdp";
    public static final String DY_COLUMN_DR_ZY_SPXGDP_ZYALL = "dy_dr_zy_spxgdp_zy";
    public static final String DY_COLUMN_DR_ZY_SPXGDP_DRALL = "dy_dr_zy_spxgdp_dr";
    public static final String DY_COLUMN_DR_ZY_TSPDP_ZY = "dy_dr_zy_tspdp_zy";
    public static final String DY_COLUMN_DR_ZY_TSPDP_DR = "dy_dr_zy_tspdp_dr";
    public static final String DY_COLUMN_DR_ZY_TZBJDP_ZY = "dy_dr_zy_tzbjdp_zy";
    public static final String DY_COLUMN_DR_ZY_TZBJDP_DR = "dy_dr_zy_tzbjdp_dr";
    public static final String DY_COLUMN_DR_ZY_PPDP = "dy_dr_zy_ppdp";
    public static final String DY_COLUMN_DR_ZY_QYTGDP_ZY = "dy_dr_zy_qytgdp_zy";
    public static final String DY_COLUMN_DR_ZY_QYTGDP_DR = "dy_dr_zy_qytgdp_dr";


    public static final String ALBB_COLUMN_SPXG1 = "albb_spxgdp1";
    public static final String ALBB_COLUMN_SPXG2 = "albb_spxgdp2";

    //全站推店
    public static final String ALBB_COLUMN_QZTD = "albb_qztd";
    //全站销品
    public static final String ALBB_COLUMN_QZXP = "albb_qzxp";
    //场景投放
    public static final String ALBB_COLUMN_CJTF = "albb_cjtf";
    //首位展示
    public static final String ALBB_COLUMN_SWZS = "albb_swzs";
    //定位推广
    public static final String ALBB_COLUMN_DWTG = "albb_dwtg";
    //搜索展播
    public static final String ALBB_COLUMN_SSZB = "albb_sszb";
    //品牌专区
    public static final String ALBB_COLUMN_PPZQ = "albb_ppzq";
    //明星工厂
    public static final String ALBB_COLUMN_MXGC = "albb_mxgc";
    //工业品解决方案
    public static final String ALBB_COLUMN_GYPJJFA = "albb_gypjjfa";
    //全店引新
    public static final String ALBB_COLUMN_QDYX = "albb_qdyx";
    //SKA专属定制方案
    public static final String ALBB_COLUMN_SKAZSDZFA = "albb_skazsdzfa";
    //SKA驾驶舱
    public static final String ALBB_COLUMN_SKAJSC = "albb_skajsc";
    //工厂生意加速计划
    public static final String ALBB_COLUMN_GCSYJSJH = "albb_gcsyjsjh";
    //找工厂解决方案
    public static final String ALBB_COLUMN_ZGCJJFA = "albb_zgcjjfa";
    //核心商家成长计划
    public static final String ALBB_COLUMN_HXSJCZJH = "albb_hxsjczjh";
    //消费品解决方案
    public static final String ALBB_COLUMN_XFPJJFA = "albb_xfpjjfa";
    //超星方案
    public static final String ALBB_COLUMN_CXFA = "albb_cxfa";
    @Deprecated /*删除日期2023-10-13*/
    public static final String ALBB_COLUMN_YXB = "albb_yxb";

    public static final String ALBB_COLUMN_SWZSPRO_SWZSBB = "albb_swzspro_swzsbb";
    public static final String ALBB_COLUMN_SWZSPRO_SSZSBB = "albb_swzspro_sszsbb";
    public static final String ALBB_COLUMN_SWZSPRO_DWTGBB = "albb_swzspro_dwtgbb";

    public static final String ALBB_COLUMN_PPYX_PPZQ = "albb_ppyx_ppzq";
    public static final String ALBB_COLUMN_PPYX_MXGC = "albb_ppyx_mxgc";
    public static final String ALBB_COLUMN_PPYX_ZSZW = "albb_ppyx_zszw";
    public static final String ALBB_COLUMN_PPYX_PPB = "albb_ppyx_ppb";


    public static final String TGC_COLUMN_SPXGDP = "tgc_spxgdp";
    public static final String TGC_COLUMN_SPXGDP2 = "tgc_spxgdp2";
    public static final String TGC_COLUMN_SPXGDP_ZFMJS = "tgc_spxgdp_zfmjs";

    public static final String XHS_COLUMN_SPXGDP = "xhs_spxgdp";
    public static final String XHS_COLUMN_LLSJDP = "xhs_llsjdp";
    public static final String XHS_COLUMN_JGZBDP = "xhs_jgzbdp";
    public static final String XHS_COLUMN_JGBJDP = "xhs_jgbjdp";
    public static final String XHS_COLUMN_QFZBDP = "xhs_qfzbdp";
    public static final String XHS_COLUMN_CFSPDP = "xhs_cfsptgdp";
    public static final String XHS_COLUMN_CFZBDP = "xhs_cfzbtgdp";
    //xhs_pgybjbgdp
    public static final String XHS_COLUMN_PGYBJBGDP = "xhs_pgybjbgdp";


    public static final String WXSPH_COLUMN_SPXGDP = "wxsph_spxgdp";
    public static final String WXSPH_COLUMN_YHQDP = "wxsph_yhqdp";
    public static final String WXSPH_COLUMN_XSQGDP = "wxsph_xsqgdp";
    public static final String WXSPH_COLUMN_CXZTDSPDP = "wxsph_cxzt_dspdp";
    public static final String WXSPH_COLUMN_CXZTZBJDP = "wxsph_cxzt_zbjdp";
    public static final String DW_COLUMN_SJSPFXDP = "dw_sjspfxdp";
    public static final String DW_COLUMN_YXDWTTGSJDP = "dw_yxdwttgsjdp";
    public static final String DW_COLUMN_YXDWTCWLSDP = "dw_yxdwtcwlsdp";
    public static final String DW_COLUMN_YXNRYYPJYXDP = "dw_yxnryypjyxdp";

    public static final String KS_COLUMN_SPXG = "ks_spxg";

    public static final String WPH_COLUMN_SPXG = "wph_spxgdp";
    public static final String WPH_COLUMN_WXKJLJ = "wph_wxkjlj";
    public static final String WPH_COLUMN_ZWJLYQHF = "wph_zwjlyqhf";
    public static final String WPH_COLUMN_ZWJLYQJLJ = "wph_zwjlyqjlj";
    public static final String WPH_COLUMN_ZWAQYHF = "wph_zwaqyhf";
    public static final String WPH_COLUMN_ZWAQYJLJ = "wph_zwaqyjlj";

    public static final String WPH_COLUMN_TMJLJ = "wph_tmjlj";
    public static final String WPH_COLUMN_ZNGGJLJ = "wph_znggjlj";
    public static final String WPH_COLUMN_YXPTHF = "wph_yxpthf";




    @Autowired
    private TsDao2 tsDao2;

    SyncClient client = null;

    @PostConstruct
    public void init() {
        client = tsDao2.getClient("abujlb6@jushita").getClient();
    }

    public String getRow(String qd_userid, String rq, String column_x) {
        try {
            PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
            primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_USERID, PrimaryKeyValue.fromString(qd_userid));
            primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_RQ, PrimaryKeyValue.fromString(rq));
            PrimaryKey primaryKey = primaryKeyBuilder.build();
            SingleRowQueryCriteria criteria = new SingleRowQueryCriteria(TABLE_NAME, primaryKey);
            criteria.setMaxVersions(1);
            criteria.addColumnsToGet(column_x);
            GetRowResponse getRowResponse = client.getRow(new GetRowRequest(criteria));
            Row row = getRowResponse.getRow();
            if (row != null && row.getLatestColumn(column_x) != null && row.getLatestColumn(column_x).getValue() != null) {
                return row.getLatestColumn(column_x).getValue().asString();
            }
        } catch (Throwable e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }

    public String[] getRow(String qd_userid, String rq, String... column_x) {
        try {
            PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
            primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_USERID, PrimaryKeyValue.fromString(qd_userid));
            primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_RQ, PrimaryKeyValue.fromString(rq));
            PrimaryKey primaryKey = primaryKeyBuilder.build();
            SingleRowQueryCriteria criteria = new SingleRowQueryCriteria(TABLE_NAME, primaryKey);
            criteria.setMaxVersions(1);
            for (String columnX : column_x) {
                criteria.addColumnsToGet(columnX);
            }

            String[] returnColumnValues = new String[column_x.length];
            GetRowResponse getRowResponse = client.getRow(new GetRowRequest(criteria));
            Row row = getRowResponse.getRow();
            if (row != null && !row.isEmpty()) {
                for (int i = 0; i < column_x.length; i++) {
                    if (row.getLatestColumn(column_x[i]) != null && row.getLatestColumn(column_x[i]).getValue() != null) {
                        String json = row.getLatestColumn(column_x[i]).getValue().asString();
                        returnColumnValues[i] = json;
                    }
                }
            }

            return returnColumnValues;
        } catch (Throwable e) {
            log.error(e.getMessage(), e);
        }
        return new String[column_x.length];
    }

    public void updateRow(String qd, String userid, String rq, String column, String val) {
        try {
            // 构造主键
            PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
            primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_USERID, PrimaryKeyValue.fromString(qd + "_" + userid));
            primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_RQ, PrimaryKeyValue.fromString(rq));
            PrimaryKey primaryKey = primaryKeyBuilder.build();
            // 设置表名
            RowUpdateChange rowUpdateChange = new RowUpdateChange(TABLE_NAME, primaryKey);
            rowUpdateChange.put(new Column(column, ColumnValue.fromString(val == null ? "" : val)));
            // 更新
            client.updateRow(new UpdateRowRequest(rowUpdateChange));
        } catch (Throwable e) {
            log.error(e.getMessage(), e);
        }
    }
}
