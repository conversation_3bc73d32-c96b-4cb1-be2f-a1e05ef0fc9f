package com.abujlb.dataprobi.tsdao;

import com.abujlb.dao.v2.TsDao2;
import com.abujlb.dataprobi.bean.hfshare.HfShareBase;
import com.abujlb.dataprobi.bean.hfshare.HfShareGoodsConfig;
import com.abujlb.dataprobi.constant.DataprobiConst;
import com.abujlb.dataprobi.util.DataprobiDateUtil;
import com.abujlb.util.StringUtil;
import com.alibaba.acm.shaded.com.google.common.reflect.TypeToken;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alicloud.openservices.tablestore.SyncClient;
import com.alicloud.openservices.tablestore.model.*;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;

@Component
public class BiTghfConfigTsDao {

    private static final Logger LOG = Logger.getLogger(BiTghfConfigTsDao.class);
    private static final Type TYPE = new TypeToken<List<HfShareGoodsConfig>>() {
    }.getType();

    public static final int TYPE_0 = 0;
    public static final int TYPE_1_DEFAULT = 1;

    public static final int LX_TX_PXB = 1;
    public static final int LX_TX_AIZTONE_NRYXDSP = 2;
    public static final int LX_DY_DJIA = 3;
    public static final int LX_DY_QC_QYTG = 4;
    public static final int LX_TX_AIZTONE_DPYY_DPZD = 5;


    private static final String TABLE_NAME = "bi_tghf_config";

    public static final String PRIMARY_COLUMN_LX = "lx";
    public static final String PRIMARY_COLUMN_YHID_QD_USERID = "yhid_qd_userid";
    public static final String PRIMARY_COLUMN_UUID = "uuid";
    public static final String COLUMN_TYPE = "type";
    public static final String COLUMN_KSRQ = "ksrq";
    public static final String COLUMN_JSRQ = "jsrq";
    public static final String COLUMN_GXSJ = "gxsj";
    public static final String COLUMN_SXSJ = "sxsj";
    public static final String COLUMN_JHID = "jhid";
    public static final String COLUMN_JHMC = "jhmc";
    public static final String COLUMN_CYMC = "cymc";
    public static final String COLUMN_DJID = "djid";
    public static final String COLUMN_DYH = "dyh";
    public static final String COLUMN_QCH = "qch";
    public static final String COLUMN_SPCONFIG = "spconfig";

    private SyncClient client = null;

    @Autowired
    private TsDao2 tsDao2;

    @PostConstruct
    public void init() {
        client = tsDao2.getClient("abujlb7@jushita").getClient();
    }

    public List<HfShareBase> getRows(int lx, int yhid, String qd, String userid) {
        List<HfShareBase> total = new ArrayList<>();
        try {
            RangeRowQueryCriteria rangeRowQueryCriteria = new RangeRowQueryCriteria(TABLE_NAME);

            //设置起始主键。
            PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
            primaryKeyBuilder.addPrimaryKeyColumn(PRIMARY_COLUMN_LX, PrimaryKeyValue.fromLong(lx));
            primaryKeyBuilder.addPrimaryKeyColumn(PRIMARY_COLUMN_YHID_QD_USERID, PrimaryKeyValue.fromString(yhid + "_" + qd + "_" + userid));
            primaryKeyBuilder.addPrimaryKeyColumn(PRIMARY_COLUMN_UUID, PrimaryKeyValue.INF_MIN);
            rangeRowQueryCriteria.setInclusiveStartPrimaryKey(primaryKeyBuilder.build());

            //设置结束主键。
            primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
            primaryKeyBuilder.addPrimaryKeyColumn(PRIMARY_COLUMN_LX, PrimaryKeyValue.fromLong(lx));
            primaryKeyBuilder.addPrimaryKeyColumn(PRIMARY_COLUMN_YHID_QD_USERID, PrimaryKeyValue.fromString(yhid + "_" + qd + "_" + userid));
            primaryKeyBuilder.addPrimaryKeyColumn(PRIMARY_COLUMN_UUID, PrimaryKeyValue.INF_MAX);
            rangeRowQueryCriteria.setExclusiveEndPrimaryKey(primaryKeyBuilder.build());

            rangeRowQueryCriteria.setMaxVersions(1);
            while (true) {
                GetRangeResponse getRangeResponse = client.getRange(new GetRangeRequest(rangeRowQueryCriteria));
                List<Row> rows = getRangeResponse.getRows();
                if (CollectionUtils.isEmpty(rows)) {
                    break;
                }
                for (Row row : rows) {
                    HfShareBase hfShareBase = new HfShareBase();
                    hfShareBase.setLx(lx);
                    hfShareBase.setYhid(yhid);
                    hfShareBase.setQd(qd);
                    hfShareBase.setUserid(userid);
                    hfShareBase.setType((int) row.getLatestColumn(COLUMN_TYPE).getValue().asLong());
                    hfShareBase.setUuid(row.getPrimaryKey().getPrimaryKeyColumn(PRIMARY_COLUMN_UUID).getValue().asString());
                    if (row.getLatestColumn(COLUMN_KSRQ) != null && row.getLatestColumn(COLUMN_KSRQ).getValue() != null) {
                        hfShareBase.setKsrq(row.getLatestColumn(COLUMN_KSRQ).getValue().asString());
                    }
                    if (row.getLatestColumn(COLUMN_JSRQ) != null && row.getLatestColumn(COLUMN_JSRQ).getValue() != null) {
                        hfShareBase.setJsrq(row.getLatestColumn(COLUMN_JSRQ).getValue().asString());
                    }
                    if (row.getLatestColumn(COLUMN_SXSJ) != null && row.getLatestColumn(COLUMN_SXSJ).getValue() != null) {
                        hfShareBase.setSxsj(row.getLatestColumn(COLUMN_SXSJ).getValue().asString());
                    }
                    if (row.getLatestColumn(COLUMN_JHID) != null && row.getLatestColumn(COLUMN_JHID).getValue() != null) {
                        hfShareBase.setJhid(row.getLatestColumn(COLUMN_JHID).getValue().asString());
                    }
                    if (row.getLatestColumn(COLUMN_JHMC) != null && row.getLatestColumn(COLUMN_JHMC).getValue() != null) {
                        hfShareBase.setJhmc(row.getLatestColumn(COLUMN_JHMC).getValue().asString());
                    }
                    if (row.getLatestColumn(COLUMN_CYMC) != null && row.getLatestColumn(COLUMN_CYMC).getValue() != null) {
                        hfShareBase.setCymc(row.getLatestColumn(COLUMN_CYMC).getValue().asString());
                    }
                    if (row.getLatestColumn(COLUMN_DJID) != null && row.getLatestColumn(COLUMN_DJID).getValue() != null) {
                        hfShareBase.setDjid(row.getLatestColumn(COLUMN_DJID).getValue().asString());
                    }
                    if (row.getLatestColumn(COLUMN_DYH) != null && row.getLatestColumn(COLUMN_DYH).getValue() != null) {
                        hfShareBase.setDyh(row.getLatestColumn(COLUMN_DYH).getValue().asString());
                    }
                    if (row.getLatestColumn(COLUMN_QCH) != null && row.getLatestColumn(COLUMN_QCH).getValue() != null) {
                        hfShareBase.setQch(row.getLatestColumn(COLUMN_QCH).getValue().asString());
                    }
                    if (row.getLatestColumn(COLUMN_SPCONFIG) != null && row.getLatestColumn(COLUMN_SPCONFIG).getValue() != null) {
                        String spconfig = row.getLatestColumn(COLUMN_SPCONFIG).getValue().asString();
                        if (StringUtil.isJsonArray2(spconfig)) {
                            hfShareBase.setSpconfig(spconfig);
                            JSONArray jsonArray = JSONArray.parseArray(spconfig);
                            List<HfShareGoodsConfig> list = new ArrayList<>();
                            for (int i = 0; i < jsonArray.size(); i++) {
                                JSONObject jsonObject = jsonArray.getJSONObject(i);

                                JSONArray jsonArray1 = jsonObject.getJSONArray("");
                            }
                            hfShareBase.setConfigList(DataprobiConst.GSON.fromJson(spconfig, TYPE));
                        }
                    }
                    total.add(hfShareBase);
                }
                //如果NextStartPrimaryKey不为null，则继续读取。
                if (getRangeResponse.getNextStartPrimaryKey() != null) {
                    rangeRowQueryCriteria.setInclusiveStartPrimaryKey(getRangeResponse.getNextStartPrimaryKey());
                } else {
                    break;
                }
            }
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
        return total;
    }


    public List<HfShareBase> getRows2(int lx, int yhid, String qd, String userid) {
        List<HfShareBase> total = new ArrayList<>();
        try {
            RangeRowQueryCriteria rangeRowQueryCriteria = new RangeRowQueryCriteria(TABLE_NAME);

            //设置起始主键。
            PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
            primaryKeyBuilder.addPrimaryKeyColumn(PRIMARY_COLUMN_LX, PrimaryKeyValue.fromLong(lx));
            primaryKeyBuilder.addPrimaryKeyColumn(PRIMARY_COLUMN_YHID_QD_USERID, PrimaryKeyValue.fromString(yhid + "_" + qd + "_" + userid));
            primaryKeyBuilder.addPrimaryKeyColumn(PRIMARY_COLUMN_UUID, PrimaryKeyValue.INF_MIN);
            rangeRowQueryCriteria.setInclusiveStartPrimaryKey(primaryKeyBuilder.build());

            //设置结束主键。
            primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
            primaryKeyBuilder.addPrimaryKeyColumn(PRIMARY_COLUMN_LX, PrimaryKeyValue.fromLong(lx));
            primaryKeyBuilder.addPrimaryKeyColumn(PRIMARY_COLUMN_YHID_QD_USERID, PrimaryKeyValue.fromString(yhid + "_" + qd + "_" + userid));
            primaryKeyBuilder.addPrimaryKeyColumn(PRIMARY_COLUMN_UUID, PrimaryKeyValue.INF_MAX);
            rangeRowQueryCriteria.setExclusiveEndPrimaryKey(primaryKeyBuilder.build());

            rangeRowQueryCriteria.setMaxVersions(1);
            while (true) {
                GetRangeResponse getRangeResponse = client.getRange(new GetRangeRequest(rangeRowQueryCriteria));
                List<Row> rows = getRangeResponse.getRows();
                if (CollectionUtils.isEmpty(rows)) {
                    break;
                }
                for (Row row : rows) {
                    String ksrq = null;
                    String jsrq = null;
                    String jhid = null;
                    if (row.getLatestColumn(COLUMN_KSRQ) != null && row.getLatestColumn(COLUMN_KSRQ).getValue() != null) {
                        ksrq = row.getLatestColumn(COLUMN_KSRQ).getValue().asString();
                    }
                    if (row.getLatestColumn(COLUMN_JSRQ) != null && row.getLatestColumn(COLUMN_JSRQ).getValue() != null) {
                        jsrq = row.getLatestColumn(COLUMN_JSRQ).getValue().asString();
                    }
                    if (row.getLatestColumn(COLUMN_JHID) != null && row.getLatestColumn(COLUMN_JHID).getValue() != null) {
                        jhid = row.getLatestColumn(COLUMN_JHID).getValue().asString();
                    }

                    if (row.getLatestColumn(COLUMN_SPCONFIG) == null 
                            || row.getLatestColumn(COLUMN_SPCONFIG).getValue() == null 
                            || !StringUtil.isJsonArray2(row.getLatestColumn(COLUMN_SPCONFIG).getValue().asString())) {
                        continue;
                    }
                    String spconfig = row.getLatestColumn(COLUMN_SPCONFIG).getValue().asString();
                    JSONArray jsonArray = JSONArray.parseArray(spconfig);
                    for (int i = 0; i < jsonArray.size(); i++) {
                        JSONObject jsonObject = jsonArray.getJSONObject(i);
                        JSONArray goodsConfig = jsonObject.getJSONArray("goodsConfig");
                        if (CollectionUtils.isEmpty(goodsConfig)) {
                            continue;
                        }

                        String videoId = jsonObject.getString("videoId");
                        String cyId = jsonObject.getString("cyId");
                        HfShareBase hfShareBase = new HfShareBase();
                        hfShareBase.setLx(lx);
                        hfShareBase.setYhid(yhid);
                        hfShareBase.setQd(qd);
                        hfShareBase.setUserid(userid);
                        hfShareBase.setType((int) row.getLatestColumn(COLUMN_TYPE).getValue().asLong());
                        hfShareBase.setUuid(row.getPrimaryKey().getPrimaryKeyColumn(PRIMARY_COLUMN_UUID).getValue().asString());
                        hfShareBase.setKsrq(ksrq);
                        hfShareBase.setJsrq(jsrq);
                        hfShareBase.setJhid(jhid);
                        hfShareBase.setVideoId(videoId);
                        hfShareBase.setCyId(cyId);

                        List<HfShareGoodsConfig> configList = new ArrayList<>();
                        for (int k = 0; k < goodsConfig.size(); k++) {
                            JSONObject goodsConfigJSONObject = goodsConfig.getJSONObject(k);

                            String goodsId = goodsConfigJSONObject.getString("goodsId");
                            double ratio = goodsConfigJSONObject.getDoubleValue("ratio");

                            HfShareGoodsConfig hfShareGoodsConfig = new HfShareGoodsConfig();
                            hfShareGoodsConfig.setGoodsId(goodsId);
                            hfShareGoodsConfig.setRatio(ratio);

                            configList.add(hfShareGoodsConfig);
                        }

                        hfShareBase.setConfigList(configList);
                        total.add(hfShareBase);
                    }
                }
                //如果NextStartPrimaryKey不为null，则继续读取。
                if (getRangeResponse.getNextStartPrimaryKey() != null) {
                    rangeRowQueryCriteria.setInclusiveStartPrimaryKey(getRangeResponse.getNextStartPrimaryKey());
                } else {
                    break;
                }
            }
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
        return total;
    }

    public void updateRows(List<HfShareBase> hfShareBaseList) {
        if (CollectionUtils.isEmpty(hfShareBaseList)) {
            return;
        }

        List<List<HfShareBase>> partition = Lists.partition(hfShareBaseList, 100);
        for (List<HfShareBase> list : partition) {
            updateRows2(list);
        }
    }

    private void updateRows2(List<HfShareBase> list) {
        BatchWriteRowRequest batchWriteRowRequest = new BatchWriteRowRequest();

        for (HfShareBase hfShareBase : list) {
            PrimaryKeyBuilder pk1Builder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
            pk1Builder.addPrimaryKeyColumn(PRIMARY_COLUMN_LX, PrimaryKeyValue.fromLong(hfShareBase.getLx()));
            pk1Builder.addPrimaryKeyColumn(PRIMARY_COLUMN_YHID_QD_USERID, PrimaryKeyValue.fromString(hfShareBase.getYhid() + "_" + hfShareBase.getQd() + "_" + hfShareBase.getUserid()));
            pk1Builder.addPrimaryKeyColumn(PRIMARY_COLUMN_UUID, PrimaryKeyValue.fromString(hfShareBase.getUuid()));
            RowUpdateChange rowUpdateChange = new RowUpdateChange(TABLE_NAME, pk1Builder.build());

            if (StringUtils.isBlank(hfShareBase.getSxsj())) {
                rowUpdateChange.put(COLUMN_SXSJ, ColumnValue.fromString(DataprobiDateUtil.getCurrentTime()));
            }
            if (StringUtils.isNotBlank(hfShareBase.getJhid())) {
                rowUpdateChange.put(COLUMN_JHID, ColumnValue.fromString(hfShareBase.getJhid()));
            }
            if (StringUtils.isNotBlank(hfShareBase.getJhmc())) {
                rowUpdateChange.put(COLUMN_JHMC, ColumnValue.fromString(hfShareBase.getJhmc()));
            }
            if (StringUtils.isNotBlank(hfShareBase.getCymc())) {
                rowUpdateChange.put(COLUMN_CYMC, ColumnValue.fromString(hfShareBase.getCymc()));
            }

            rowUpdateChange.put(COLUMN_GXSJ, ColumnValue.fromString(DataprobiDateUtil.getCurrentTime()));
            batchWriteRowRequest.addRowChange(rowUpdateChange);
        }

        int maxRetryTime = 0;
        BatchWriteRowRequest retryRequest = null;
        while (maxRetryTime < 10) {
            BatchWriteRowResponse batchWriteRowResponse = client.batchWriteRow(retryRequest == null ? batchWriteRowRequest : retryRequest);
            if (batchWriteRowResponse.isAllSucceed()) {
                break;
            }

            retryRequest = batchWriteRowRequest.createRequestForRetry(batchWriteRowResponse.getFailedRows());
            maxRetryTime++;
        }
    }

    public void updateRow(HfShareBase hfShareBase) {
        try {
            PrimaryKeyBuilder pk1Builder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
            pk1Builder.addPrimaryKeyColumn(PRIMARY_COLUMN_LX, PrimaryKeyValue.fromLong(hfShareBase.getLx()));
            pk1Builder.addPrimaryKeyColumn(PRIMARY_COLUMN_YHID_QD_USERID, PrimaryKeyValue.fromString(hfShareBase.getYhid() + "_" + hfShareBase.getQd() + "_" + hfShareBase.getUserid()));
            pk1Builder.addPrimaryKeyColumn(PRIMARY_COLUMN_UUID, PrimaryKeyValue.fromString(hfShareBase.getUuid()));

            PrimaryKey primaryKey = pk1Builder.build();
            RowUpdateChange rowUpdateChange = new RowUpdateChange(TABLE_NAME, primaryKey);
            if (StringUtils.isBlank(hfShareBase.getSxsj())) {
                rowUpdateChange.put(COLUMN_SXSJ, ColumnValue.fromString(DataprobiDateUtil.getCurrentTime()));
            }
            if (StringUtils.isNotBlank(hfShareBase.getJhid())) {
                rowUpdateChange.put(COLUMN_JHID, ColumnValue.fromString(hfShareBase.getJhid()));
            }
            if (StringUtils.isNotBlank(hfShareBase.getJhmc())) {
                rowUpdateChange.put(COLUMN_JHMC, ColumnValue.fromString(hfShareBase.getJhmc()));
            }
            if (StringUtils.isNotBlank(hfShareBase.getCymc())) {
                rowUpdateChange.put(COLUMN_CYMC, ColumnValue.fromString(hfShareBase.getCymc()));
            }

            rowUpdateChange.put(COLUMN_GXSJ, ColumnValue.fromString(DataprobiDateUtil.getCurrentTime()));
            rowUpdateChange.put(COLUMN_TYPE, ColumnValue.fromLong(hfShareBase.getType()));

            client.updateRow(new UpdateRowRequest(rowUpdateChange));
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
    }
}
