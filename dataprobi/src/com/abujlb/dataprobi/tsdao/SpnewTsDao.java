package com.abujlb.dataprobi.tsdao;

import com.abujlb.dao.v2.TsDao2;
import com.abujlb.dataprobi.bean.SpInit;
import com.abujlb.dataprobi.util.DataprobiDateUtil;
import com.abujlb.util.Md5;
import com.alibaba.fastjson.JSONArray;
import com.alicloud.openservices.tablestore.SyncClient;
import com.alicloud.openservices.tablestore.model.*;
import com.alicloud.openservices.tablestore.model.search.SearchQuery;
import com.alicloud.openservices.tablestore.model.search.SearchRequest;
import com.alicloud.openservices.tablestore.model.search.SearchResponse;
import com.alicloud.openservices.tablestore.model.search.query.BoolQuery;
import com.alicloud.openservices.tablestore.model.search.query.TermQuery;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/4
 */
@Component
public class SpnewTsDao {

    private static final Logger LOG = Logger.getLogger(SpnewTsDao.class);

    private static final String TABLE_NAME = "spnew";

    /***************** 主键 **************************/
    private static final String PK_1 = "md516_yhid_qd_userid_bbid";//md5_16(yhid_qd_userid_bbid)
    private static final String PK_2 = "yhid";

    /***************** 数据键 **************************/
    public static final String COLUMN_USERID = "userid";
    public static final String COLUMN_QD = "qd";
    public static final String COLUMN_BBID = "bbid";
    public static final String COLUMN_QD_USERID = "qd_userid";
    public static final String COLUMN_IMG = "img";
    public static final String COLUMN_TITLE = "title";
    public static final String COLUMN_CJSJ = "cjsj";
    public static final String COLUMN_CJRQ = "cjrq";
    public static final String COLUMN_PRICE = "price";
    public static final String COLUMN_YHID = "yhid";
    public static final String COLUMN_SFZS = "sfzs";
    public static final String COLUMN_ZHXGSJ = "zhxgsj";
    public static final String COLUMN_ZHXGRQ = "zhxgrq";
    public static final String COLUMN_CID = "cid";
    public static final String COLUMN_SCSJSJ = "scsjsj";
    public static final String COLUMN_MAINSKUID = "mainskuid";
    public static final String COLUMN_SPUID = "spuid";

    private SyncClient client = null;

//    private SyncClient clientTest = null;

    @Autowired
    private TsDao2 tsDao2;

    @PostConstruct
    public void init() {
        client = tsDao2.getClient("abujlb6@jushita").getClient();
//        clientTest = tsDao2.getClient("abujlb6test@jushita").getClient();
    }

    public List<String> getMNbbidList(String mnUserid) {
        List<String> list = new ArrayList<>();

        SearchQuery searchQuery = new SearchQuery();
        TermQuery useridQuery = new TermQuery(); //设置查询类型为TermQuery。
        useridQuery.setFieldName(COLUMN_USERID); //设置要匹配的字段。
        useridQuery.setTerm(ColumnValue.fromString(mnUserid)); //设置要匹配的值。

        TermQuery qdQuery = new TermQuery(); //设置查询类型为TermQuery。
        qdQuery.setFieldName(COLUMN_QD); //设置要匹配的字段。
        qdQuery.setTerm(ColumnValue.fromString("MN")); //设置要匹配的值。

        BoolQuery boolQuery = new BoolQuery();
        boolQuery.setMustQueries(Arrays.asList(useridQuery, qdQuery));
        searchQuery.setQuery(boolQuery);
        searchQuery.setLimit(100);

        SearchRequest searchRequest = new SearchRequest(TABLE_NAME, "spnew_index", searchQuery);

        SearchRequest.ColumnsToGet columnsToGet = new SearchRequest.ColumnsToGet();
        columnsToGet.setColumns(Collections.singletonList(COLUMN_BBID));
        searchRequest.setColumnsToGet(columnsToGet);

        while (true) {
            SearchResponse resp = client.search(searchRequest);

            for (Row row : resp.getRows()) {
                if (row.getLatestColumn(COLUMN_BBID) != null && row.getLatestColumn(COLUMN_BBID).getValue() != null) {
                    list.add(row.getLatestColumn(COLUMN_BBID).getValue().asString());
                }
            }

            byte[] next = resp.getNextToken();
            if (next != null) {
                searchRequest.getSearchQuery().setToken(next);
            } else {
                break;
            }
        }
        return list;
    }


    public List<String> getWPHbbidList(String mnUserid) {
        List<String> list = new ArrayList<>();

        SearchQuery searchQuery = new SearchQuery();
        TermQuery useridQuery = new TermQuery(); //设置查询类型为TermQuery。
        useridQuery.setFieldName(COLUMN_USERID); //设置要匹配的字段。
        useridQuery.setTerm(ColumnValue.fromString(mnUserid)); //设置要匹配的值。

        TermQuery qdQuery = new TermQuery(); //设置查询类型为TermQuery。
        qdQuery.setFieldName(COLUMN_QD); //设置要匹配的字段。
        qdQuery.setTerm(ColumnValue.fromString("WPH")); //设置要匹配的值。

        BoolQuery boolQuery = new BoolQuery();
        boolQuery.setMustQueries(Arrays.asList(useridQuery, qdQuery));
        searchQuery.setQuery(boolQuery);
        searchQuery.setLimit(100);

        SearchRequest searchRequest = new SearchRequest(TABLE_NAME, "spnew_index", searchQuery);

        SearchRequest.ColumnsToGet columnsToGet = new SearchRequest.ColumnsToGet();
        columnsToGet.setColumns(Arrays.asList(COLUMN_BBID,COLUMN_QD_USERID,COLUMN_QD));
//        columnsToGet.setColumns(Collections.singletonList(COLUMN_QD_USERID));
//        columnsToGet.setColumns(Collections.singletonList(COLUMN_YHID));
        searchRequest.setColumnsToGet(columnsToGet);

        while (true) {
            SearchResponse resp = client.search(searchRequest);

            for (Row row : resp.getRows()) {
                if (row.getLatestColumn(COLUMN_BBID) != null && row.getLatestColumn(COLUMN_BBID).getValue() != null) {
                    System.out.println("COLUMN_QD_USERID----"+row.getLatestColumn(COLUMN_QD_USERID).getValue().asString());
                    System.out.println("COLUMN_BBID---"+row.getLatestColumn(COLUMN_BBID).getValue().asString());
                    System.out.println("COLUMN_QD---"+row.getLatestColumn(COLUMN_QD).getValue().asString());
                    list.add(row.getLatestColumn(COLUMN_BBID).getValue().asString());
                }
            }

            byte[] next = resp.getNextToken();
            if (next != null) {
                searchRequest.getSearchQuery().setToken(next);
            } else {
                break;
            }
        }
        return list;
    }

    public void initSp(int yhid, String qd, String userid, JSONArray jsonArray) {
        try {
            if (CollectionUtils.isEmpty(jsonArray)) {
                return;
            }
            List<SpInit> list = new ArrayList<>(jsonArray.size());
            for (int i = 0; i < jsonArray.size(); i++) {
                SpInit spInit = new SpInit(qd, jsonArray.getJSONObject(i));
                spInit.setYhid(yhid);
                spInit.setUserid(userid);
                list.add(spInit);
            }

            List<List<SpInit>> all = Lists.partition(list, 200);
            for (List<SpInit> temp : all) {
                initSp(temp);
            }
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
    }

    public void initSp(List<SpInit> list) {
        BatchWriteRowRequest batchWriteRowRequest = new BatchWriteRowRequest();

        String currTime = DataprobiDateUtil.getCurrentTime();
        String currDate = DataprobiDateUtil.getCurrentDate();
        for (SpInit spInit : list) {
            if (StringUtils.isBlank(spInit.getBbid())) {
                continue;
            }
            PrimaryKeyBuilder pk1Builder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
            String key = spInit.getYhid() + "_" + spInit.getQd() + "_" + spInit.getUserid() + "_" + spInit.getBbid();
            pk1Builder.addPrimaryKeyColumn(PK_1, PrimaryKeyValue.fromString(Md5.md516(key) + "_" + key));
            pk1Builder.addPrimaryKeyColumn(PK_2, PrimaryKeyValue.fromLong(spInit.getYhid()));
            RowUpdateChange rowUpdateChange = new RowUpdateChange(TABLE_NAME, pk1Builder.build());

            rowUpdateChange.put(COLUMN_BBID, ColumnValue.fromString(spInit.getBbid()));
            rowUpdateChange.put(COLUMN_QD_USERID, ColumnValue.fromString(spInit.getQd() + "_" + spInit.getUserid()));
            if (StringUtils.isNotBlank(spInit.getImg())) {
                rowUpdateChange.put(COLUMN_IMG, ColumnValue.fromString(spInit.getImg()));
            }
            if (StringUtils.isNotBlank(spInit.getBbmc())) {
                rowUpdateChange.put(COLUMN_TITLE, ColumnValue.fromString(spInit.getBbmc()));
            }
            if (StringUtils.isNotBlank(spInit.getUserid())) {
                rowUpdateChange.put(COLUMN_USERID, ColumnValue.fromString(spInit.getUserid()));
            }
            if (StringUtils.isNotBlank(spInit.getCjsj())) {
                rowUpdateChange.put(COLUMN_CJSJ, ColumnValue.fromString(spInit.getCjsj()));
                rowUpdateChange.put(COLUMN_CJRQ, ColumnValue.fromString(spInit.getCjsj().substring(0, 10)));
            }
            if (StringUtils.isNotBlank(spInit.getPrice())) {
                rowUpdateChange.put(COLUMN_PRICE, ColumnValue.fromString(spInit.getPrice()));
            }
            if (StringUtils.isNotBlank(spInit.getQd())) {
                rowUpdateChange.put(COLUMN_QD, ColumnValue.fromString(spInit.getQd()));
            }

            rowUpdateChange.put(COLUMN_SFZS, ColumnValue.fromLong(spInit.getSjzt()));
            rowUpdateChange.put(COLUMN_ZHXGSJ, ColumnValue.fromString(currTime));
            rowUpdateChange.put(COLUMN_ZHXGRQ, ColumnValue.fromString(currDate));
            batchWriteRowRequest.addRowChange(rowUpdateChange);
        }


        int maxRetryTime = 0;
        BatchWriteRowRequest retryRequest = null;
        while (maxRetryTime < 10) {
            BatchWriteRowResponse batchWriteRowResponse = client.batchWriteRow(retryRequest == null ? batchWriteRowRequest : retryRequest);
            if (batchWriteRowResponse.isAllSucceed()) {
                break;
            }

            retryRequest = batchWriteRowRequest.createRequestForRetry(batchWriteRowResponse.getFailedRows());
            maxRetryTime++;
        }
    }


    public void initSpV2(List<SpInit> list) {
        try {
            if (CollectionUtils.isEmpty(list)) {
                return;
            }

            List<List<SpInit>> all = Lists.partition(list, 200);
            for (List<SpInit> temp : all) {
                initSpV2Page(temp);
            }
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
    }


    private void initSpV2Page(List<SpInit> list) {
        BatchWriteRowRequest batchWriteRowRequest = new BatchWriteRowRequest();

        String currTime = DataprobiDateUtil.getCurrentTime();
        String currDate = DataprobiDateUtil.getCurrentDate();
        for (SpInit spInit : list) {
            if (StringUtils.isBlank(spInit.getBbid())) {
                continue;
            }

            PrimaryKeyBuilder pk1Builder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
            String key = spInit.getYhid() + "_" + spInit.getQd() + "_" + spInit.getUserid() + "_" + spInit.getBbid();
            pk1Builder.addPrimaryKeyColumn(PK_1, PrimaryKeyValue.fromString(Md5.md516(key) + "_" + key));
            pk1Builder.addPrimaryKeyColumn(PK_2, PrimaryKeyValue.fromLong(spInit.getYhid()));
            RowUpdateChange rowUpdateChange = new RowUpdateChange(TABLE_NAME, pk1Builder.build());
            rowUpdateChange.put(COLUMN_QD_USERID, ColumnValue.fromString(spInit.getQd() + "_" + spInit.getUserid()));
            rowUpdateChange.put(COLUMN_BBID, ColumnValue.fromString(spInit.getBbid()));
            if (StringUtils.isNotBlank(spInit.getImg())) {
                rowUpdateChange.put(COLUMN_IMG, ColumnValue.fromString(spInit.getImg()));
            }
            if (StringUtils.isNotBlank(spInit.getBbmc())) {
                rowUpdateChange.put(COLUMN_TITLE, ColumnValue.fromString(spInit.getBbmc()));
            }
            if (StringUtils.isNotBlank(spInit.getCid())) {
                rowUpdateChange.put(COLUMN_CID, ColumnValue.fromString(spInit.getCid()));
            }
            if (StringUtils.isNotBlank(spInit.getUserid())) {
                rowUpdateChange.put(COLUMN_USERID, ColumnValue.fromString(spInit.getUserid()));
            }
            if (StringUtils.isNotBlank(spInit.getCjsj())) {
                rowUpdateChange.put(COLUMN_CJSJ, ColumnValue.fromString(spInit.getCjsj()));
            }
            if (StringUtils.isNotBlank(spInit.getCjsj())) {
                rowUpdateChange.put(COLUMN_CJRQ, ColumnValue.fromString(spInit.getCjsj().substring(0, 10)));
            }
            if (StringUtils.isNotBlank(spInit.getQd())) {
                rowUpdateChange.put(COLUMN_QD, ColumnValue.fromString(spInit.getQd()));
            }
            if (StringUtils.isNotBlank(spInit.getPrice())) {
                rowUpdateChange.put(COLUMN_PRICE, ColumnValue.fromString(spInit.getPrice()));
            }
            if (StringUtils.isNotBlank(spInit.getScsjsj())) {
                rowUpdateChange.put(COLUMN_SCSJSJ, ColumnValue.fromString(spInit.getScsjsj()));
            }
            rowUpdateChange.put(COLUMN_SFZS, ColumnValue.fromLong(spInit.getSjzt()));
            rowUpdateChange.put(COLUMN_ZHXGSJ, ColumnValue.fromString(currTime));
            rowUpdateChange.put(COLUMN_ZHXGRQ, ColumnValue.fromString(currDate));
            batchWriteRowRequest.addRowChange(rowUpdateChange);
        }

        int maxRetryTime = 0;
        BatchWriteRowRequest retryRequest = null;
        while (maxRetryTime < 10) {
            BatchWriteRowResponse batchWriteRowResponse = client.batchWriteRow(retryRequest == null ? batchWriteRowRequest : retryRequest);
            if (batchWriteRowResponse.isAllSucceed()) {
                break;
            }

            retryRequest = batchWriteRowRequest.createRequestForRetry(batchWriteRowResponse.getFailedRows());
            maxRetryTime++;
        }
    }

    public void initSpV3(int yhid, String qd, String userid, JSONArray jsonArray) {
        try {
            if (CollectionUtils.isEmpty(jsonArray)) {
                return;
            }
            List<SpInit> list = new ArrayList<>(jsonArray.size());
            for (int i = 0; i < jsonArray.size(); i++) {
                SpInit spInit = new SpInit(qd, jsonArray.getJSONObject(i));
                spInit.setYhid(yhid);
                spInit.setUserid(userid);
                list.add(spInit);
            }

            List<List<SpInit>> all = Lists.partition(list, 200);
            for (List<SpInit> temp : all) {
                initSpV3Page(temp);
            }
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
    }


    private void initSpV3Page(List<SpInit> list) {
        BatchWriteRowRequest batchWriteRowRequest = new BatchWriteRowRequest();

        String currTime = DataprobiDateUtil.getCurrentTime();
        String currDate = DataprobiDateUtil.getCurrentDate();
        for (SpInit spInit : list) {
            if (StringUtils.isBlank(spInit.getBbid())) {
                continue;
            }

            PrimaryKeyBuilder pk1Builder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
            String key = spInit.getYhid() + "_" + spInit.getQd() + "_" + spInit.getUserid() + "_" + spInit.getBbid();
            pk1Builder.addPrimaryKeyColumn(PK_1, PrimaryKeyValue.fromString(Md5.md516(key) + "_" + key));
            pk1Builder.addPrimaryKeyColumn(PK_2, PrimaryKeyValue.fromLong(spInit.getYhid()));
            RowUpdateChange rowUpdateChange = new RowUpdateChange(TABLE_NAME, pk1Builder.build());
            rowUpdateChange.put(COLUMN_QD_USERID, ColumnValue.fromString(spInit.getQd() + "_" + spInit.getUserid()));
            rowUpdateChange.put(COLUMN_BBID, ColumnValue.fromString(spInit.getBbid()));
            if (StringUtils.isNotBlank(spInit.getImg())) {
                rowUpdateChange.put(COLUMN_IMG, ColumnValue.fromString(spInit.getImg()));
            }
            if (StringUtils.isNotBlank(spInit.getBbmc())) {
                rowUpdateChange.put(COLUMN_TITLE, ColumnValue.fromString(spInit.getBbmc()));
            }
            if (StringUtils.isNotBlank(spInit.getCid())) {
                rowUpdateChange.put(COLUMN_CID, ColumnValue.fromString(spInit.getCid()));
            }
            if (StringUtils.isNotBlank(spInit.getUserid())) {
                rowUpdateChange.put(COLUMN_USERID, ColumnValue.fromString(spInit.getUserid()));
            }
            if (StringUtils.isNotBlank(spInit.getCjsj())) {
                rowUpdateChange.put(COLUMN_CJSJ, ColumnValue.fromString(spInit.getCjsj()));
            }
            if (StringUtils.isNotBlank(spInit.getCjsj())) {
                rowUpdateChange.put(COLUMN_CJRQ, ColumnValue.fromString(spInit.getCjsj().substring(0, 10)));
            }
            if (StringUtils.isNotBlank(spInit.getQd())) {
                rowUpdateChange.put(COLUMN_QD, ColumnValue.fromString(spInit.getQd()));
            }
            if (StringUtils.isNotBlank(spInit.getPrice())) {
                rowUpdateChange.put(COLUMN_PRICE, ColumnValue.fromString(spInit.getPrice()));
            }
            if (StringUtils.isNotBlank(spInit.getScsjsj())) {
                rowUpdateChange.put(COLUMN_SCSJSJ, ColumnValue.fromString(spInit.getScsjsj()));
            }

            if (StringUtils.isNotBlank(spInit.getMainskuid())) {
                rowUpdateChange.put(COLUMN_MAINSKUID, ColumnValue.fromString(spInit.getMainskuid()));
            }
            if (StringUtils.isNotBlank(spInit.getSpuid())) {
                rowUpdateChange.put(COLUMN_SPUID, ColumnValue.fromString(spInit.getSpuid()));
            }
            rowUpdateChange.put(COLUMN_SFZS, ColumnValue.fromLong(spInit.getSjzt()));
            rowUpdateChange.put(COLUMN_ZHXGSJ, ColumnValue.fromString(currTime));
            rowUpdateChange.put(COLUMN_ZHXGRQ, ColumnValue.fromString(currDate));
            batchWriteRowRequest.addRowChange(rowUpdateChange);
        }

        int maxRetryTime = 0;
        BatchWriteRowRequest retryRequest = null;
        while (maxRetryTime < 10) {
            BatchWriteRowResponse batchWriteRowResponse = client.batchWriteRow(retryRequest == null ? batchWriteRowRequest : retryRequest);
            if (batchWriteRowResponse.isAllSucceed()) {
                break;
            }

            retryRequest = batchWriteRowRequest.createRequestForRetry(batchWriteRowResponse.getFailedRows());
            maxRetryTime++;
        }
    }

    public String getMNUseridByBbid(String bbid) {
        try {
            SearchQuery searchQuery = new SearchQuery();
            TermQuery bbidQuery = new TermQuery(); //设置查询类型为TermQuery。
            bbidQuery.setFieldName(COLUMN_BBID); //设置要匹配的字段。
            bbidQuery.setTerm(ColumnValue.fromString(bbid)); //设置要匹配的值。

            TermQuery qdQuery = new TermQuery(); //设置查询类型为TermQuery。
            qdQuery.setFieldName(COLUMN_QD); //设置要匹配的字段。
            qdQuery.setTerm(ColumnValue.fromString("MN")); //设置要匹配的值。

            BoolQuery boolQuery = new BoolQuery();
            boolQuery.setMustQueries(Arrays.asList(bbidQuery, qdQuery));
            searchQuery.setQuery(boolQuery);
            searchQuery.setLimit(100);

            SearchRequest searchRequest = new SearchRequest(TABLE_NAME, "spnew_index", searchQuery);

            SearchRequest.ColumnsToGet columnsToGet = new SearchRequest.ColumnsToGet();
            columnsToGet.setColumns(Arrays.asList(COLUMN_TITLE, "dpmc", COLUMN_USERID));
            searchRequest.setColumnsToGet(columnsToGet);
            while (true) {
                SearchResponse resp = client.search(searchRequest);

                for (Row row : resp.getRows()) {
                    if (row.getLatestColumn(COLUMN_TITLE) != null && row.getLatestColumn(COLUMN_TITLE).getValue() != null
                            && row.getLatestColumn("dpmc") != null && row.getLatestColumn("dpmc").getValue() != null) {
                        return row.getLatestColumn(COLUMN_USERID).getValue().asString();
                    }
                }

                byte[] next = resp.getNextToken();
                if (next != null) {
                    searchRequest.getSearchQuery().setToken(next);
                } else {
                    break;
                }
            }
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
        return null;
    }


    public List<Row> getRowsByUserid(String userid, String qd) {
        List<Row> rows = new ArrayList<>();
        try {
            SearchQuery searchQuery = new SearchQuery();
            TermQuery useridQuery = new TermQuery(); //设置查询类型为TermQuery。
            useridQuery.setFieldName(COLUMN_USERID); //设置要匹配的字段。
            useridQuery.setTerm(ColumnValue.fromString(userid)); //设置要匹配的值。

            TermQuery qdQuery = new TermQuery(); //设置查询类型为TermQuery。
            qdQuery.setFieldName(COLUMN_QD); //设置要匹配的字段。
            qdQuery.setTerm(ColumnValue.fromString(qd)); //设置要匹配的值。

            BoolQuery boolQuery = new BoolQuery();
            boolQuery.setMustQueries(Arrays.asList(useridQuery, qdQuery));
            searchQuery.setQuery(boolQuery);
            searchQuery.setLimit(100);

            SearchRequest searchRequest = new SearchRequest(TABLE_NAME, "spnew_index", searchQuery);

            SearchRequest.ColumnsToGet columnsToGet = new SearchRequest.ColumnsToGet();
            columnsToGet.setColumns(Arrays.asList(COLUMN_IMG, COLUMN_USERID, COLUMN_BBID));
            searchRequest.setColumnsToGet(columnsToGet);
            while (true) {
                SearchResponse resp = client.search(searchRequest);
                rows.addAll(resp.getRows());
                byte[] next = resp.getNextToken();
                if (next != null) {
                    searchRequest.getSearchQuery().setToken(next);
                } else {
                    break;
                }
            }
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
        return null;
    }

    public void delRow(int yhid, String qd, String userid, String bbid) {
        String key = yhid + "_" + qd + "_" + userid + "_" + bbid;
        String pkValue = Md5.md516(key) + "_" + key;

        //构造主键。
        PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
        primaryKeyBuilder.addPrimaryKeyColumn(PK_1, PrimaryKeyValue.fromString(pkValue));
        primaryKeyBuilder.addPrimaryKeyColumn(PK_2, PrimaryKeyValue.fromLong(yhid));
        PrimaryKey primaryKey = primaryKeyBuilder.build();
        //设置数据表名称。
        RowDeleteChange rowDeleteChange = new RowDeleteChange("spnew", primaryKey);
        client.deleteRow(new DeleteRowRequest(rowDeleteChange));
    }

    public void updateItemImg(Row row, String img) {
        PrimaryKeyBuilder pk1Builder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
        pk1Builder.addPrimaryKeyColumn(PK_1, row.getPrimaryKey().getPrimaryKeyColumn(PK_1).getValue());
        pk1Builder.addPrimaryKeyColumn(PK_2, row.getPrimaryKey().getPrimaryKeyColumn(PK_2).getValue());
        RowUpdateChange rowUpdateChange = new RowUpdateChange(TABLE_NAME, pk1Builder.build());
        rowUpdateChange.put(COLUMN_IMG, ColumnValue.fromString(img));
        client.updateRow(new UpdateRowRequest(rowUpdateChange));
    }
}
