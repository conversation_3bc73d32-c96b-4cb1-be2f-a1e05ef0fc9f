package com.abujlb.dataprobi.tsdao;

import com.abujlb.dao.v2.TsDao2;
import com.abujlb.dataprobi.bean.DataprobiBaseMsgBean;
import com.abujlb.dataprobi.server.DataUploader;
import com.abujlb.dataprobi.util.DataprobiDateUtil;
import com.alicloud.openservices.tablestore.SyncClient;
import com.alicloud.openservices.tablestore.model.*;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

@Component
public class DataprobiMsgLogTsDao {

    /***************** 主键 **************************/

    public static final String PK_YHID_QD_USERID = "yhid_qd_userid";
    public static final String PK_CJRQ = "rq";
    public static final String PK_UUID = "uuid";
    private static final Logger log = Logger.getLogger(DataprobiMsgLogTsDao.class);
    private static final String TABLE_NAME = "log_dataprobi_msg";
    private SyncClient client = null;

    @Autowired
    private TsDao2 tsDao2;

    @PostConstruct
    public void init() {
        client = tsDao2.getClient("abujlb@jushita").getClient();
    }

    private static final String IP = DataUploader.getIp();

    public <T extends DataprobiBaseMsgBean> void start(T t) {
        try {
            PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
            primaryKeyBuilder.addPrimaryKeyColumn(PK_YHID_QD_USERID, PrimaryKeyValue.fromString(t.getYhid() + "_" + t.getQd() + "_" + t.getUserid()));
            primaryKeyBuilder.addPrimaryKeyColumn(PK_CJRQ, PrimaryKeyValue.fromString(DataprobiDateUtil.getCurrentDate()));
            primaryKeyBuilder.addPrimaryKeyColumn(PK_UUID, PrimaryKeyValue.fromString(t.getUuid()));

            PrimaryKey primaryKey = primaryKeyBuilder.build();
            RowUpdateChange rowUpdateChange = new RowUpdateChange(TABLE_NAME, primaryKey);
            rowUpdateChange.put(new Column("yhid", ColumnValue.fromLong(t.getYhid())));
            rowUpdateChange.put(new Column("qd", ColumnValue.fromString(t.getQd())));
            rowUpdateChange.put(new Column("userid", ColumnValue.fromString(t.getUserid())));
            rowUpdateChange.put(new Column("dpmc", ColumnValue.fromString(t.getDpmc())));
            rowUpdateChange.put(new Column("infoid", ColumnValue.fromLong(t.getBiinfoId())));
            rowUpdateChange.put(new Column("ip", ColumnValue.fromString(IP)));
            rowUpdateChange.put(new Column("msgbody", ColumnValue.fromString(t.toString())));
            rowUpdateChange.put(new Column("kssj", ColumnValue.fromString(DataprobiDateUtil.getCurrentTime())));

            client.updateRow(new UpdateRowRequest(rowUpdateChange));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    public <T extends DataprobiBaseMsgBean> void end(T t) {
        try {
            if (t == null || t.getBiinfoId() == 0) {
                return;
            }
            PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
            primaryKeyBuilder.addPrimaryKeyColumn(PK_YHID_QD_USERID, PrimaryKeyValue.fromString(t.getYhid() + "_" + t.getQd() + "_" + t.getUserid()));
            primaryKeyBuilder.addPrimaryKeyColumn(PK_CJRQ, PrimaryKeyValue.fromString(DataprobiDateUtil.getCurrentDate()));
            primaryKeyBuilder.addPrimaryKeyColumn(PK_UUID, PrimaryKeyValue.fromString(t.getUuid()));

            PrimaryKey primaryKey = primaryKeyBuilder.build();
            RowUpdateChange rowUpdateChange = new RowUpdateChange(TABLE_NAME, primaryKey);
            rowUpdateChange.put(new Column("jssj", ColumnValue.fromString(DataprobiDateUtil.getCurrentTime())));

            client.updateRow(new UpdateRowRequest(rowUpdateChange));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }
}
