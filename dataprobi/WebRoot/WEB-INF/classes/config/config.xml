<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE properties SYSTEM "http://java.sun.com/dtd/properties.dtd">
<properties>
    <entry key="syscode">dataprobi</entry>
    <!-- CDN加速域名 -->
    <entry key="cdnDomain">http://localhost/dataprobi</entry>
    <!-- 临时文件夹 -->
    <entry key="tempdir">d:/temp/</entry><!-- <entry key="tempdir">/data/temp/</entry> -->

    <!-- 赤兔名品oss文件下载临时文件地址 -->
    <entry key="ctmpFilePath">D:/ctmp/</entry>

    <!-- OSS地址配置信息 -->
    <entry key="bidata-oss-bucketname">ecbisjson</entry>

    <!-- service2接口 -->
    <entry key="serverAppid">zdcj</entry>
    <entry key="serverAppkey">123123132132132asdfasdf</entry>
    <entry key="serverUrl">https://service2.abujlb.com</entry>

    <!-- 腾讯云消息队列 -->
    <entry key="txyun-secretId">AKIDNYUAuRJsmTwBG9qIXaP1PaVAZibD7dS1</entry>
    <entry key="txyun-secretKey">RcXCaibFcjaAhz1nJq78tOcgW8VAeuzs</entry>
    <entry key="txyun-endpoint">https://cmq-gz.public.tencenttdmq.com
    </entry> <!-- 内网配置 <entry key="txyun-endpoint">http://gz.mqadapter.cmq.tencentyun.com</entry> -->
    <entry key="queueName">dataprobi</entry>
    <entry key="threadCount">10</entry><!-- 线程池线程数配置 -->
    <entry key="threadpoolsize">5</entry><!-- 线程池线程数配置 -->
    <entry key="debug">true</entry>
    <entry key="jdwx_appkey">5f1e6e7307b0819ed9b60e18e907485f</entry>

    <entry key="logoff-mysql">true</entry>
    <entry key="logoff-mongodb">true</entry>
    <entry key="logoff-tablestore">true</entry>
    <entry key="usercontroller-off">true</entry>
    <entry key="service-off">true</entry>
</properties>