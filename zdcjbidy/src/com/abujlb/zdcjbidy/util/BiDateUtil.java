package com.abujlb.zdcjbidy.util;

import org.apache.commons.lang3.StringUtils;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2022/6/1 11:34
 */
public class BiDateUtil {


    public final static DateTimeFormatter pattern = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    public final static DateTimeFormatter time_pattern = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    public final static DateTimeFormatter time_pattern2 = DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss");

    public static String getLastday() {
        LocalDate yesterday = LocalDate.now().minusDays(1);
        return yesterday.format(pattern);
    }

    public static Date getLastdayDate() {
        LocalDate yesterday = LocalDate.now().minusDays(1);
        return Date.from(LocalDateTime.of(yesterday, LocalTime.NOON).atZone(ZoneId.systemDefault()).toInstant());
    }

    public static String getTimestamp(String rq) {
        LocalDate date = LocalDate.parse(rq, pattern);
        return Date.from(date.atStartOfDay(ZoneId.systemDefault()).toInstant()).getTime() / 1000 + "";
    }

    public static String getTimestamp(String rq, DateTimeFormatter pattern) {
        LocalDateTime date = LocalDateTime.parse(rq, pattern);
        return Date.from(date.withNano(999).atZone(ZoneId.systemDefault()).toInstant()).getTime() / 1000 + "";
    }

    public static String getTimestampMs(String rq) {
        LocalDateTime date = LocalDateTime.parse(rq, time_pattern);
        return Date.from(date.withNano(999).atZone(ZoneId.systemDefault()).toInstant()).getTime() + "";
    }

    public static String getDayBefore(String rq, int diff) {
        return LocalDate.parse(rq, pattern).minusDays(Math.abs(diff)).format(pattern);
    }

    public static String getDayAfter(String rq, int diff) {
        return LocalDate.parse(rq, pattern).plusDays(Math.abs(diff)).format(pattern);
    }

    public static List<String> getCjsj(Date startDate, String lastCjsj) {
        return getCjsj2(startDate, lastCjsj);
    }

    public static List<String> getCjsj2(Date startDate, String lastCjsj) {
        List<String> list = new ArrayList<>();
        LocalDate start = startDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate startCjRq = null;
        LocalDate endCjRq = LocalDate.now().minusDays(1);//昨日
        LocalDate maxSupplyDate = LocalDate.now().minusMonths(2).withDayOfMonth(1);//最大补采日期
        boolean containStart = false;
        try {
            if (StringUtils.isBlank(lastCjsj) && (start.isBefore(maxSupplyDate) || start.isEqual(maxSupplyDate))) {
                startCjRq = maxSupplyDate;
                containStart = true;
            } else if (StringUtils.isBlank(lastCjsj) && start.isAfter(maxSupplyDate)) {
                startCjRq = start;
                containStart = true;
            } else if (StringUtils.isNotBlank(lastCjsj) && LocalDate.parse(lastCjsj, pattern).isBefore(maxSupplyDate)) {
                startCjRq = maxSupplyDate;
                containStart = true;
            } else if (StringUtils.isNotBlank(lastCjsj) && LocalDate.parse(lastCjsj, pattern).isEqual(maxSupplyDate)) {
                startCjRq = maxSupplyDate;
            } else if (StringUtils.isNotBlank(lastCjsj) && LocalDate.parse(lastCjsj, pattern).isAfter(maxSupplyDate)) {
                startCjRq = LocalDate.parse(lastCjsj, pattern);
            }

            if (startCjRq == null || startCjRq.isEqual(endCjRq)) {
                return list;
            }

            LocalDate temp = null;
            LocalDate curr = null;
            while (temp == null || temp.isBefore(endCjRq) || temp.isEqual(endCjRq)) {
                if (temp == null) {
                    if (containStart) {
                        list.add(startCjRq.format(pattern));
                    }
                    temp = startCjRq.plusDays(1);
                } else {
                    list.add(temp.format(pattern));
                    temp = temp.plusDays(1);
                }
            }
            return list;
        } catch (Exception e) {
            return Collections.emptyList();
        }
    }


    /**
     * @param avvidStart     千川开始日期
     * @param lastCjsjModule 模块上次采集日期  bi_info表的rqjson里的字段
     * @param lastCjsjAvvid  模块-千川上次采集日期 bi_jlqc_avvids表里rqjson里的字段
     * @return
     */
    public static List<String> getCjsjV2(String avvidStart, String lastCjsjModule, String lastCjsjAvvid) {
        if (lastCjsjModule == null || lastCjsjAvvid == null) {
            return BiDateUtil.getBetweenDate(avvidStart, BiDateUtil.getLastday());
        }

        return BiDateUtil.getCjsj(avvidStart, lastCjsjModule.compareTo(lastCjsjAvvid) < 0 ? lastCjsjModule : lastCjsjAvvid);
    }


    public static List<String> getCjsj(String start, String lastCjsj) {
        Date startDate = null;
        if (StringUtils.isNotBlank(start)) {
            startDate = Date.from(LocalDate.parse(start, pattern).atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
        }
        List<String> list = new ArrayList<>();
        try {
            LocalDate startLocalDate = null;
            if (StringUtils.isNotBlank(lastCjsj)) {
                startLocalDate = LocalDate.parse(lastCjsj, pattern);
            } else {
                if (startDate == null) {
                    startLocalDate = LocalDate.now().withDayOfMonth(1);
                } else {
                    startLocalDate = startDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                }
            }

            LocalDate yesterday = LocalDate.now().minusDays(1);
            LocalDate curr = null;
            while (startLocalDate.isBefore(yesterday) || startLocalDate.isEqual(yesterday)) {
                curr = startLocalDate;
                list.add(curr.format(pattern));
                startLocalDate = startLocalDate.plusDays(1);
            }

            if (StringUtils.isNotBlank(lastCjsj) && !list.isEmpty()) {
                list.remove(0);
            }
            return list;
        } catch (Exception e) {
            return Collections.emptyList();
        }
    }

    /**
     * 包含start和end
     *
     * @param sycmStart
     * @param start
     * @param end
     * @return
     */
    public static List<String> getRqfwDayList(Date sycmStart, String start, String end) {
        if (end == null) {
            return Collections.emptyList();
        }
        List<String> list = new ArrayList<>();
        try {
            LocalDate startLocaldate = StringUtils.isBlank(start) ? sycmStart.toInstant().atZone(ZoneId.systemDefault()).toLocalDate() : LocalDate.parse(start, pattern);
            LocalDate endLocaldate = LocalDate.parse(end, pattern);

            if (startLocaldate.isEqual(endLocaldate)) {
                return StringUtils.isBlank(start) ? Collections.singletonList(end) : Collections.emptyList();
            }

            LocalDate temp = null;
            while (startLocaldate.isBefore(endLocaldate) || startLocaldate.isEqual(endLocaldate)) {
                if (temp == null) {
                    if (StringUtils.isBlank(start)) {
                        temp = startLocaldate;
                    } else {
                        startLocaldate = startLocaldate.plusDays(1);
                        temp = startLocaldate;
                    }
                } else {
                    startLocaldate = startLocaldate.plusDays(1);
                    temp = startLocaldate;
                }
                if (startLocaldate.isAfter(endLocaldate)) {
                    break;
                }
                list.add(temp.format(pattern));
            }

            return list;
        } catch (Exception e) {
            return Collections.emptyList();
        }
    }


    public static String getFirstDayOfMonth(String rq) {
        LocalDate firstdayOfMonth = LocalDate.parse(rq, pattern).withDayOfMonth(1);
        return firstdayOfMonth.format(pattern);
    }

    public static Date getFirstDayOfMonthDate() {
        LocalDate firstdayOfMonth = LocalDate.now().withDayOfMonth(1).minusMonths(1);
        return Date.from(firstdayOfMonth.atStartOfDay(ZoneId.systemDefault()).toInstant());
    }

    public static String getCurrtime() {
        return LocalDateTime.now().format(time_pattern);
    }

    public static String getCurrtimeWithPattern(DateTimeFormatter pattern) {
        return LocalDateTime.now().format(pattern);
    }

    public static boolean isLastDayForMonth(String rq) {
        LocalDate localDate = LocalDate.parse(rq, pattern);
        int monthDays = localDate.getMonth().length(localDate.isLeapYear());
        int value = localDate.getDayOfMonth();
        return value == monthDays;
    }


    public static List<String> recent24Months() {
        List<String> list = new ArrayList<>(12);
        try {
            LocalDate now = LocalDate.now();
            for (int i = 0; i < 12; i++) {
                now = now.minusMonths(1);
                int monthDays = now.getMonth().length(now.isLeapYear());
                LocalDate temp = LocalDate.of(now.getYear(), now.getMonth(), monthDays);
                list.add(temp.format(pattern));
            }
            return list;
        } catch (Exception e) {
            return Collections.emptyList();
        }
    }

    public static String date2Str(Date start) {
        if (Objects.isNull(start)) {
            return null;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(start);
        LocalDate localDate = LocalDate.of(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH) + 1, calendar.get(Calendar.DAY_OF_MONTH));
        return DateTimeFormatter.ofPattern("yyyy-MM-dd").format(localDate);
    }

    public static String getTimestampCurr(String rq) {
        LocalDate date = LocalDate.parse(rq, pattern);
        LocalTime localTime = LocalTime.now();
        LocalDateTime localDateTime = LocalDateTime.of(date, localTime);
        return Date.from(localDateTime.toInstant(ZoneOffset.of("+08:00"))).getTime() / 1000 + "";
    }

    public static String getToday() {
        LocalDate yesterday = LocalDate.now();
        return yesterday.format(pattern);
    }

    public static String timestampToStrdate(long timestamp) {
        return Instant.ofEpochSecond(timestamp).atZone(ZoneId.systemDefault()).toLocalDate().format(pattern);
    }

    public static List<String> getBetweenDate(String start, String end) {
        List<String> list = new ArrayList<String>();
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDate startLocalDate = LocalDate.parse(start, formatter);
            LocalDate endLocalDate = LocalDate.parse(end, formatter);
            while (startLocalDate.isBefore(endLocalDate) || startLocalDate.isEqual(endLocalDate)) {
                list.add(startLocalDate.format(formatter));
                startLocalDate = startLocalDate.plusDays(1);
            }
            return list;
        } catch (Exception e) {
        }
        return Collections.emptyList();
    }

    public static void main(String[] args) {
//        String timestamp = getTimestamp("2023-09-18");
//        System.out.println("timestamp = " + timestamp);
//
//        List<String> cjsj = BiDateUtil.getBetweenDate("2023-10-01", "2023-10-25");
//        System.out.println("cjsj = " + cjsj);

        System.out.println("timestampToStrdate(4102415999L) = " + timestampToStrdate(4102415999L));

    }

    public static String nowDate() {
        return LocalDate.now().format(pattern);
    }

    public static String getMonthBeforeAsString(String month, int diff) {
        try {
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDate localDate = LocalDate.parse(month + "-01", dateTimeFormatter).minusMonths(Math.abs(diff));
            return localDate.getYear() + "-" + (localDate.getMonthValue() < 10 ? ("0" + localDate.getMonthValue()) : localDate.getMonthValue());
        } catch (Exception e) {
        }
        return "";
    }

    public static String getMonthBeforeCurrMonthAsFirstday() {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate now = LocalDate.now();
        return getMonthBeforeAsString(now.format(dateTimeFormatter).substring(0, 7), 2) + "-01";
    }

    public static String getStrDateByDate(Date date) {
        if (date == null) {
            return LocalDate.now().withDayOfMonth(1).format(pattern);
        }
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(pattern);
    }


    public static int currHour() {
        return LocalDateTime.now().getHour();
    }
}
