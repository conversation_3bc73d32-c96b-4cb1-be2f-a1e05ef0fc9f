package com.abujlb.zdcjbidy.bean;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

public class ZbjBean {

    //直播开始时间
    private String liveBeginTime;
    //直播时长
    private String liveDuration;
    //直播间ID
    private String liveRoomId;
    //直播APPID
    private String liveAppId;
    //直播间标题
    private String liveRoomTitle;
    //直播间成交金额
    private double payAmt;
    //直播间成交件数
    private int payComboCnt;
    //直播间成交人数
    private int payUcnt;

    //类型 0自营 1合作
    private int aType;
    //主播头像
    private String authorAvatar;
    //主播抖音号
    private String authorAwemeId;
    //主播id
    private String authorId;
    //主播昵称
    private String authorNickName;

    public ZbjBean() {
    }

    public ZbjBean(JSONObject jsonObject) {
        if (jsonObject == null) {
            throw new RuntimeException("jsonObject为null");
        }

        if (jsonObject.containsKey("author")) {
            JSONObject author = jsonObject.getJSONObject("author");
            this.aType = author.getIntValue("a_type");
            this.authorAvatar = author.getString("author_avatar");
            this.authorAwemeId = author.getString("author_aweme_id");
            this.authorId = author.getString("author_id");
            this.authorNickName = author.getString("author_nick_name");
        }
        if (jsonObject.containsKey("live_begin_time")) {
            JSONObject live_begin_time = jsonObject.getJSONObject("live_begin_time");
            this.liveBeginTime = live_begin_time.getString("begin_time");
            this.liveDuration = live_begin_time.getString("live_duration");
        }
        if (jsonObject.containsKey("operation")) {
            JSONObject operation = jsonObject.getJSONObject("operation");
            this.liveRoomId = operation.getString("live_room_id");
            this.liveAppId = operation.getString("live_app_id");
        }
        if (jsonObject.containsKey("live_room_info")) {
            JSONObject live_room_info = jsonObject.getJSONObject("live_room_info");
            this.liveRoomTitle = live_room_info.getString("room_title");
        }

        this.payAmt = Double.parseDouble(jsonObject.getString("pay_amt").replaceAll("¥", "").replaceAll(",", ""));
        this.payComboCnt = jsonObject.getIntValue("pay_combo_cnt");
        this.payUcnt = jsonObject.getIntValue("pay_ucnt");
    }

    public ZbjBean(Object o) {
    }

    public String getLiveBeginTime() {
        return liveBeginTime;
    }

    public void setLiveBeginTime(String liveBeginTime) {
        this.liveBeginTime = liveBeginTime;
    }

    public String getLiveDuration() {
        return liveDuration;
    }

    public void setLiveDuration(String liveDuration) {
        this.liveDuration = liveDuration;
    }

    public String getLiveRoomId() {
        return liveRoomId;
    }

    public void setLiveRoomId(String liveRoomId) {
        this.liveRoomId = liveRoomId;
    }

    public String getLiveAppId() {
        return liveAppId;
    }

    public void setLiveAppId(String liveAppId) {
        this.liveAppId = liveAppId;
    }

    public double getPayAmt() {
        return payAmt;
    }

    public void setPayAmt(double payAmt) {
        this.payAmt = payAmt;
    }

    public int getPayComboCnt() {
        return payComboCnt;
    }

    public void setPayComboCnt(int payComboCnt) {
        this.payComboCnt = payComboCnt;
    }

    public int getPayUcnt() {
        return payUcnt;
    }

    public void setPayUcnt(int payUcnt) {
        this.payUcnt = payUcnt;
    }

    public int getaType() {
        return aType;
    }

    public void setaType(int aType) {
        this.aType = aType;
    }

    public String getAuthorAvatar() {
        return authorAvatar;
    }

    public void setAuthorAvatar(String authorAvatar) {
        this.authorAvatar = authorAvatar;
    }

    public String getAuthorAwemeId() {
        return authorAwemeId;
    }

    public void setAuthorAwemeId(String authorAwemeId) {
        this.authorAwemeId = authorAwemeId;
    }

    public String getAuthorId() {
        return authorId;
    }

    public void setAuthorId(String authorId) {
        this.authorId = authorId;
    }

    public String getAuthorNickName() {
        return authorNickName;
    }

    public void setAuthorNickName(String authorNickName) {
        this.authorNickName = authorNickName;
    }

    public String getLiveRoomTitle() {
        return liveRoomTitle;
    }

    public void setLiveRoomTitle(String liveRoomTitle) {
        this.liveRoomTitle = liveRoomTitle;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }

}
