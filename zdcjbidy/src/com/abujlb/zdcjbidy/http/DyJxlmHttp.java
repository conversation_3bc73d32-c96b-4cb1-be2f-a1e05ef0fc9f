package com.abujlb.zdcjbidy.http;

import com.abujlb.CommonConfig;
import com.abujlb.zdcjbidy.cookie.DyZh;
import com.abujlb.zdcjbidy.util.BiDateUtil;
import com.abujlb.zdcjbidy.util.DyCommonUtil;
import com.abujlb.zdcjbidy.util.DyConfig;
import com.abujlb.zdcjbidy.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.CookieSpecs;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.apache.log4j.Logger;

import java.io.FileOutputStream;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023/1/10 10:43
 */
public class DyJxlmHttp {

    private static final Logger log = Logger.getLogger(DyJlqcHttp.class);

    public static boolean jxlmIndex(DyZh cjzh) {
        CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cjzh.getCookieStore()).build();

        RequestConfig.Builder requestConfig = RequestConfig.custom().setSocketTimeout(50000).setConnectTimeout(50000).setConnectionRequestTimeout(50000).setCookieSpec(CookieSpecs.NETSCAPE);
        requestConfig.setCircularRedirectsAllowed(true);
        HttpGet httpget = null;
        HttpResponse httpresponse;
        String url = "https://fxg.jinritemai.com/alliance/common/redirectBuyin?target_url=%2Fdashboard&btm_pre=a2427.b76571.c4158.header_new_menu_2";
        httpget = new HttpGet(url);
        httpget.setHeader("accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9");
        httpget.setHeader("accept-encoding", "gzip, deflate, br");
        httpget.setHeader("accept-language", "zh-CN,zh;q=0.9,en;q=0.8");
        httpget.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.198 Safari/537.36");
        httpget.setConfig(requestConfig.build());
        try {
            httpresponse = httpClient.execute(httpget);
            HttpEntity entity = httpresponse.getEntity();
            EntityUtils.toString(entity);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return false;
        } finally {
            httpget.releaseConnection();
        }
        return true;
    }

    public static String jxlmdp(DyZh dyZh, String start, String end) {
        CloseableHttpClient httpClient = HttpClients.custom().build();
        String msToken = DyCommonUtil.getCompassToken(dyZh, "msToken");
        String url = "https://buyin.jinritemai.com/api/dataBoard/getShopPlanOverview?tab=overview&start_time_ms=" + BiDateUtil.getTimestampMs(start + " 00:00:00") + "&end_time_ms=" + BiDateUtil.getTimestampMs(end + " 23:59:59");
        String x_bogus = DyCommonUtil.getXBogus(DyConfig.SIGN_URL, url.substring(url.indexOf("?") + 1) + "&msToken=" + msToken, 1);
        try {
            String _signature = DyCommonUtil.getSignature(DyConfig.SIGN_URL, url.substring(url.indexOf("?") + 1) + "&msToken=" + msToken + "&X-Bogus=" + x_bogus);
            url = url + "&" + URLEncoder.encode("msToken=" + msToken + "&X-Bogus=" + x_bogus + "&_signature=" + _signature, "utf-8");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
        HttpGet httpPost = new HttpGet(url);
        httpPost.setHeader("accept", "application/json, text/plain, */*");
        httpPost.setHeader("accept-language", "zh-CN,zh;q=0.9");
        httpPost.setHeader("Cookie", dyZh.getDocumentCookie());
        httpPost.setHeader("referer", "https://buyin.jinritemai.com/dashboard/data/plan-data");
        httpPost.setHeader("sec-ch-ua", "\"Chromium\";v=\"92\", \" Not A;Brand\";v=\"99\", \"Google Chrome\";v=\"92\"");
        httpPost.setHeader("sec-ch-ua-mobile", "?0");
        httpPost.setHeader("sec-fetch-dest", "empty");
        httpPost.setHeader("sec-fetch-mode", "cors");
        httpPost.setHeader("sec-fetch-site", "same-origin");
        httpPost.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36");
        RequestConfig.Builder requestConfig = RequestConfig.custom().setSocketTimeout(100000).setConnectTimeout(100000).setConnectionRequestTimeout(100000).setCookieSpec(CookieSpecs.NETSCAPE);
        httpPost.setConfig(requestConfig.build());
        HttpResponse httpresponse;
        String body = null;
        try {
            httpresponse = httpClient.execute(httpPost);
            HttpEntity entity = httpresponse.getEntity();
            body = EntityUtils.toString(entity, "utf-8");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        } finally {
            httpPost.releaseConnection();
        }

        if (!StrUtil.isJson(body)) {
            return null;
        }

        JSONObject jsonObj = JSONObject.parseObject(body);
        int code = jsonObj.getIntValue("code");
        int st = jsonObj.getIntValue("st");
//        if (code == 10000 && st == 10000) {
//            throw new AuthErrorException("精选联盟无权限");
//        }

        if (st != 0 || code != 0) {
            return null;
        }

        if (jsonObj.containsKey("data")) {
            return jsonObj.getJSONObject("data").toJSONString();
        }
        return "{}";

    }

    public static JSONArray jxlm(DyZh dyZh, String start, String end) {
        JSONArray total = null;
        int pageNo = 1;
        final int pageSize = 20;
        while (true) {
            JSONArray jxlm = jxlm(dyZh, start, end, pageNo, pageSize);
            if (jxlm == null) {
                return null;
            }
            if (total == null) {
                total = new JSONArray();
            }
            total.addAll(jxlm);
            if (jxlm.size() < pageSize) {
                break;
            }
            pageNo++;
        }
        return total;
    }

    private static JSONArray jxlm(DyZh dyZh, String start, String end, int pageNo, int pageSize) {
        CloseableHttpClient httpClient = HttpClients.custom().build();
        String msToken = DyCommonUtil.getCompassToken(dyZh, "msToken");
        String url = "https://buyin.jinritemai.com/api/dataBoard/getShopPlanItemDetail?tab=overview&start_time_ms=" + BiDateUtil.getTimestampMs(start + " 00:00:00") + "&end_time_ms=" + BiDateUtil.getTimestampMs(end + " 23:59:59") + "&page=" + pageNo + "&page_size=" + pageSize + "&order_by=gmv&desc=true";
        String x_bogus = DyCommonUtil.getXBogus(DyConfig.SIGN_URL, url.substring(url.indexOf("?") + 1) + "&msToken=" + msToken, 1);
        try {
            String _signature = DyCommonUtil.getSignature(DyConfig.SIGN_URL, url.substring(url.indexOf("?") + 1) + "&msToken=" + msToken + "&X-Bogus=" + x_bogus);
            url = url + "&" + URLEncoder.encode("msToken=" + msToken + "&X-Bogus=" + x_bogus + "&_signature=" + _signature, "utf-8");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
        HttpGet httpPost = new HttpGet(url);
        httpPost.setHeader("accept", "application/json, text/plain, */*");
        httpPost.setHeader("accept-language", "zh-CN,zh;q=0.9");
        httpPost.setHeader("Cookie", dyZh.getDocumentCookie());
        httpPost.setHeader("referer", "https://buyin.jinritemai.com/dashboard/data/plan-data");
        httpPost.setHeader("sec-ch-ua", "\"Chromium\";v=\"92\", \" Not A;Brand\";v=\"99\", \"Google Chrome\";v=\"92\"");
        httpPost.setHeader("sec-ch-ua-mobile", "?0");
        httpPost.setHeader("sec-fetch-dest", "empty");
        httpPost.setHeader("sec-fetch-mode", "cors");
        httpPost.setHeader("sec-fetch-site", "same-origin");
        httpPost.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36");
        RequestConfig.Builder requestConfig = RequestConfig.custom().setCookieSpec(CookieSpecs.NETSCAPE).setSocketTimeout(100000).setConnectTimeout(100000).setConnectionRequestTimeout(100000);
        httpPost.setConfig(requestConfig.build());
        HttpResponse httpresponse;
        String body = null;
        try {
            httpresponse = httpClient.execute(httpPost);
            HttpEntity entity = httpresponse.getEntity();
            body = EntityUtils.toString(entity, "utf-8");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        } finally {
            httpPost.releaseConnection();
        }

        if (!StrUtil.isJson(body)) {
            return null;
        }

        JSONObject jsonObj = JSONObject.parseObject(body);
        int code = jsonObj.getIntValue("code");
        int st = jsonObj.getIntValue("st");
//        if (code == 10000 && st == 10000) {
//            throw new AuthErrorException("精选联盟无权限");
//        }

        if (st != 0 || code != 0) {
            return null;
        }

        if (jsonObj.containsKey("data")) {
            JSONObject data = jsonObj.getJSONObject("data");
            if (data.containsKey("item_detail")) {
                return data.getJSONArray("item_detail");
            }
        }
        return new JSONArray();
    }

    public static JSONArray orderList(DyZh cjzh, String start, String end) {
        int pageNo = 1;
        final int pageSize = 100;
        JSONArray total = new JSONArray();
        while (true) {
            JSONArray temp = orderList(cjzh, start, end, pageNo, pageSize);
            if (temp == null) {
                return null;
            }
            total.addAll(temp);
            if (temp.size() < pageSize) {
                break;
            }
            pageNo++;
        }
        return total;
    }

    private static JSONArray orderList(DyZh cjzh, String start, String end, int pageNo, int pageSize) {
        CloseableHttpClient httpClient = HttpClients.custom().build();
        String msToken = DyCommonUtil.getCompassToken(cjzh, "msToken");
        String verifyFp = DyCommonUtil.getCookieValueByName(cjzh.getCookieStore(), "s_v_web_id", "buyin.jinritemai.com");
        String url = "https://buyin.jinritemai.com/api/dataBoard/getShopOrderList?time_type=pay_success&media_type_group=-1&traffic_source=0&page=" + pageNo + "&pageSize=" + pageSize + "&start_day=" + start.replace("-", "%2F") + "+00%3A00%3A00&end_day=" + end.replace("-", "%2F") + "+23%3A59%3A59&query_order_type=0&verifyFp=" + verifyFp + "&fp=" + verifyFp + "&msToken=" + msToken;
        HttpGet httpPost = new HttpGet(url);
        httpPost.setHeader("accept", "application/json, text/plain, */*");
        httpPost.setHeader("accept-language", "zh-CN,zh;q=0.9");
        httpPost.setHeader("Cookie", cjzh.getDocumentCookie());
        httpPost.setHeader("referer", "https://buyin.jinritemai.com/dashboard/data/financial/list");
        httpPost.setHeader("sec-ch-ua", "\"Chromium\";v=\"92\", \" Not A;Brand\";v=\"99\", \"Google Chrome\";v=\"92\"");
        httpPost.setHeader("sec-ch-ua-mobile", "?0");
        httpPost.setHeader("sec-fetch-dest", "empty");
        httpPost.setHeader("sec-fetch-mode", "cors");
        httpPost.setHeader("sec-fetch-site", "same-origin");
        httpPost.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36");
        RequestConfig.Builder requestConfig = RequestConfig.custom().setCookieSpec(CookieSpecs.NETSCAPE).setSocketTimeout(100000).setConnectTimeout(100000).setConnectionRequestTimeout(100000);
        httpPost.setConfig(requestConfig.build());
        HttpResponse httpresponse;
        String body = null;
        try {
            httpresponse = httpClient.execute(httpPost);
            HttpEntity entity = httpresponse.getEntity();
            body = EntityUtils.toString(entity, "utf-8");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        } finally {
            httpPost.releaseConnection();
        }

        if (!StrUtil.isJson(body)) {
            return null;
        }

        JSONObject jsonObj = JSONObject.parseObject(body);
        int code = jsonObj.getIntValue("code");
        int st = jsonObj.getIntValue("st");
        if (st != 0 || code != 0) {
            return null;
        }

        if (jsonObj.containsKey("data")) {
            return jsonObj.getJSONArray("data");
        }
        return new JSONArray();
    }


    public static int orderListTotalNum(DyZh cjzh, String start, String end) {
        CloseableHttpClient httpClient = HttpClients.custom().build();
        String msToken = DyCommonUtil.getCompassToken(cjzh, "msToken");
        String verifyFp = DyCommonUtil.getCookieValueByName(cjzh.getCookieStore(), "s_v_web_id", "buyin.jinritemai.com");
        String url = "https://buyin.jinritemai.com/api/dataBoard/getShopOrderList?time_type=pay_success&media_type_group=-1&traffic_source=0&page=" + 1 + "&pageSize=" + 100 + "&start_day=" + start.replace("-", "%2F") + "+00%3A00%3A00&end_day=" + end.replace("-", "%2F") + "+23%3A59%3A59&query_order_type=0&verifyFp=" + verifyFp + "&fp=" + verifyFp + "&msToken=" + msToken;
        HttpGet httpPost = new HttpGet(url);
        httpPost.setHeader("accept", "application/json, text/plain, */*");
        httpPost.setHeader("accept-language", "zh-CN,zh;q=0.9");
        httpPost.setHeader("Cookie", cjzh.getDocumentCookie());
        httpPost.setHeader("referer", "https://buyin.jinritemai.com/dashboard/data/financial/list");
        httpPost.setHeader("sec-ch-ua", "\"Chromium\";v=\"92\", \" Not A;Brand\";v=\"99\", \"Google Chrome\";v=\"92\"");
        httpPost.setHeader("sec-ch-ua-mobile", "?0");
        httpPost.setHeader("sec-fetch-dest", "empty");
        httpPost.setHeader("sec-fetch-mode", "cors");
        httpPost.setHeader("sec-fetch-site", "same-origin");
        httpPost.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36");
        RequestConfig.Builder requestConfig = RequestConfig.custom().setCookieSpec(CookieSpecs.NETSCAPE).setSocketTimeout(100000).setConnectTimeout(100000).setConnectionRequestTimeout(100000);
        httpPost.setConfig(requestConfig.build());
        HttpResponse httpresponse;
        String body = null;
        try {
            httpresponse = httpClient.execute(httpPost);
            HttpEntity entity = httpresponse.getEntity();
            body = EntityUtils.toString(entity, "utf-8");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return -1;
        } finally {
            httpPost.releaseConnection();
        }

        if (!StrUtil.isJson(body)) {
            return -1;
        }

        JSONObject jsonObj = JSONObject.parseObject(body);
        int code = jsonObj.getIntValue("code");
        int st = jsonObj.getIntValue("st");
        if (st != 0 || code != 0) {
            return -1;
        }

        return jsonObj.getIntValue("total");
    }

    public static boolean export(DyZh cjzh, String start, String end) {
        CloseableHttpClient httpClient = HttpClients.custom().build();
        String msToken = DyCommonUtil.getCompassToken(cjzh, "msToken");
        String verifyFp = DyCommonUtil.getCookieValueByName(cjzh.getCookieStore(), "s_v_web_id", "buyin.jinritemai.com");
        String url = "https://buyin.jinritemai.com/api/dataBoard/exportShopOrderSync?time_type=pay_success&media_type_group=-1&traffic_source=0&start_day=" + start.replace("-", "%2F") + "+00%3A00%3A00&end_day=" + end.replace("-", "%2F") + "+23%3A59%3A59&only_check=false&query_order_type=0&verifyFp=" + verifyFp + "&fp=" + verifyFp + "&msToken=" + msToken;
        HttpGet httpPost = new HttpGet(url);
        httpPost.setHeader("accept", "application/json, text/plain, */*");
        httpPost.setHeader("accept-language", "zh-CN,zh;q=0.9");
        httpPost.setHeader("Cookie", cjzh.getDocumentCookie());
        httpPost.setHeader("referer", "https://buyin.jinritemai.com/dashboard/data/financial/list");
        httpPost.setHeader("sec-ch-ua", "\"Chromium\";v=\"92\", \" Not A;Brand\";v=\"99\", \"Google Chrome\";v=\"92\"");
        httpPost.setHeader("sec-ch-ua-mobile", "?0");
        httpPost.setHeader("sec-fetch-dest", "empty");
        httpPost.setHeader("sec-fetch-mode", "cors");
        httpPost.setHeader("sec-fetch-site", "same-origin");
        httpPost.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36");
        RequestConfig.Builder requestConfig = RequestConfig.custom().setCookieSpec(CookieSpecs.NETSCAPE).setSocketTimeout(100000).setConnectTimeout(100000).setConnectionRequestTimeout(100000);
        httpPost.setConfig(requestConfig.build());
        HttpResponse httpresponse;
        String body = null;
        try {
            httpresponse = httpClient.execute(httpPost);
            HttpEntity entity = httpresponse.getEntity();
            body = EntityUtils.toString(entity, "utf-8");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return false;
        } finally {
            httpPost.releaseConnection();
        }

        if (!StrUtil.isJson(body)) {
            return false;
        }

        JSONObject jsonObj = JSONObject.parseObject(body);
        int code = jsonObj.getIntValue("code");
        int st = jsonObj.getIntValue("st");
        return st == 0 && code == 0;
    }

    public static String checkReportFinish(DyZh cjzh) {
        final int maxSecond = 600;
        int sleepSeconds = 30;
        int currSeconds = 0;
        while (currSeconds <= maxSecond) {
            String s = checkReportFinish2(cjzh);
            if (s == null) {
                return null;
            }

            if (StringUtils.isNotBlank(s)) {
                return s;
            }

            try {
                TimeUnit.SECONDS.sleep(sleepSeconds);
                currSeconds = currSeconds + sleepSeconds;
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
        return null;
    }

    private static String checkReportFinish2(DyZh cjzh) {
        CloseableHttpClient httpClient = HttpClients.custom().build();
        String msToken = DyCommonUtil.getCompassToken(cjzh, "msToken");
        String verifyFp = DyCommonUtil.getCookieValueByName(cjzh.getCookieStore(), "s_v_web_id", "buyin.jinritemai.com");
        String url = "https://buyin.jinritemai.com/api/download/list?page=1&page_size=20&verifyFp=" + verifyFp + "&fp=" + verifyFp + "&msToken=" + msToken;
        HttpGet httpPost = new HttpGet(url);
        httpPost.setHeader("accept", "application/json, text/plain, */*");
        httpPost.setHeader("accept-language", "zh-CN,zh;q=0.9");
        httpPost.setHeader("Cookie", cjzh.getDocumentCookie());
        httpPost.setHeader("referer", "https://buyin.jinritemai.com/dashboard/data/financial/list");
        httpPost.setHeader("sec-ch-ua", "\"Chromium\";v=\"92\", \" Not A;Brand\";v=\"99\", \"Google Chrome\";v=\"92\"");
        httpPost.setHeader("sec-ch-ua-mobile", "?0");
        httpPost.setHeader("sec-fetch-dest", "empty");
        httpPost.setHeader("sec-fetch-mode", "cors");
        httpPost.setHeader("sec-fetch-site", "same-origin");
        httpPost.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36");
        RequestConfig.Builder requestConfig = RequestConfig.custom().setCookieSpec(CookieSpecs.NETSCAPE).setSocketTimeout(100000).setConnectTimeout(100000).setConnectionRequestTimeout(100000);
        httpPost.setConfig(requestConfig.build());
        HttpResponse httpresponse;
        String body = null;
        try {
            httpresponse = httpClient.execute(httpPost);
            HttpEntity entity = httpresponse.getEntity();
            body = EntityUtils.toString(entity, "utf-8");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        } finally {
            httpPost.releaseConnection();
        }

        if (!StrUtil.isJson(body)) {
            return null;
        }

        JSONObject jsonObj = JSONObject.parseObject(body);
        int code = jsonObj.getIntValue("code");
        int st = jsonObj.getIntValue("st");
        if (st != 0 || code != 0) {
            return null;
        }

        try {
            JSONArray data = jsonObj.getJSONArray("data");
            for (int i = 0; i < data.size(); i++) {
                JSONObject jsonObject = data.getJSONObject(i);
                int type = jsonObject.getIntValue("type");
                if (type != 1) {
                    continue;
                }
                if (jsonObject.getIntValue("status") == 2) {
                    return jsonObject.getString("batch_no");
                } else {
                    return StringUtils.EMPTY;
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }


    public static String orderListReportDownloadUrl(DyZh cjzh, String batchNo) {
        CloseableHttpClient httpClient = HttpClients.custom().build();
        String msToken = DyCommonUtil.getCompassToken(cjzh, "msToken");
        String verifyFp = DyCommonUtil.getCookieValueByName(cjzh.getCookieStore(), "s_v_web_id", "buyin.jinritemai.com");
        String url = "https://buyin.jinritemai.com/api/download/file?batch_no=" + batchNo + "&verifyFp=" + verifyFp + "&fp=" + verifyFp + "&msToken=" + msToken;
        HttpGet httpPost = new HttpGet(url);
        httpPost.setHeader("accept", "application/json, text/plain, */*");
        httpPost.setHeader("accept-language", "zh-CN,zh;q=0.9");
        httpPost.setHeader("Cookie", cjzh.getDocumentCookie());
        httpPost.setHeader("referer", "https://buyin.jinritemai.com/dashboard/data/financial/list");
        httpPost.setHeader("sec-ch-ua", "\"Chromium\";v=\"92\", \" Not A;Brand\";v=\"99\", \"Google Chrome\";v=\"92\"");
        httpPost.setHeader("sec-ch-ua-mobile", "?0");
        httpPost.setHeader("sec-fetch-dest", "empty");
        httpPost.setHeader("sec-fetch-mode", "cors");
        httpPost.setHeader("sec-fetch-site", "same-origin");
        httpPost.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36");
        RequestConfig.Builder requestConfig = RequestConfig.custom().setCookieSpec(CookieSpecs.NETSCAPE).setSocketTimeout(100000).setConnectTimeout(100000).setConnectionRequestTimeout(100000);
        httpPost.setConfig(requestConfig.build());
        HttpResponse httpresponse;
        String body = null;
        try {
            httpresponse = httpClient.execute(httpPost);
            HttpEntity entity = httpresponse.getEntity();
            body = EntityUtils.toString(entity, "utf-8");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        } finally {
            httpPost.releaseConnection();
        }

        if (!StrUtil.isJson(body)) {
            return null;
        }

        JSONObject jsonObj = JSONObject.parseObject(body);
        int code = jsonObj.getIntValue("code");
        int st = jsonObj.getIntValue("st");
        if (st != 0 || code != 0) {
            return null;
        }

        return jsonObj.getJSONObject("data").getString("url");
    }

    public static String reportDownload(DyZh cjzh, String url) {
        HttpGet httppost = null;
        HttpResponse httpresponse = null;
        FileOutputStream os = null;
        InputStream is = null;
        try {
            CloseableHttpClient httpClient = HttpClients.custom().build();

            httppost = new HttpGet(url);
            httppost.setHeader("accept", "application/json, text/plain, */*");
            httppost.setHeader("accept-Language", "zh-CN,zh;q=0.9");
            httppost.setHeader("cookie", cjzh.getDocumentCookie());
            httppost.setHeader("referer", "https://compass.jinritemai.com/shop/commodity/product-list");
            httppost.setHeader("sec-ch-ua", "\" Not A;Brand\";v=\"99\", \"Chromium\";v=\"102\", \"Google Chrome\";v=\"102\"");
            httppost.setHeader("sec-ch-ua-mobile", "?0");
            httppost.setHeader("sec-fetch-dest", "empty");
            httppost.setHeader("sec-fetch-mode", "cors");
            httppost.setHeader("sec-fetch-site", "same-origin");
            httppost.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.131 Safari/537.36 SLBrowser/8.0.1.4031 SLBChan/25");
            RequestConfig.Builder requestConfig = RequestConfig.custom().setSocketTimeout(300000).setConnectTimeout(300000).setConnectionRequestTimeout(300000);
            httppost.setConfig(requestConfig.build());
            httpresponse = httpClient.execute(httppost);
            HttpEntity httpEntity = httpresponse.getEntity();
            is = httpEntity.getContent();
            String filePath = CommonConfig.getString("tempdir");
            String fileName = cjzh.getUserid() + "-" + UUID.randomUUID() + "_dyjxlmdd.xlsx";
            os = new FileOutputStream(filePath + fileName);
            byte[] b = new byte[4096];
            int count = 0;
            while ((count = is.read(b)) > 0) {
                os.write(b, 0, count);
            }
            os.flush();

            return filePath + fileName;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            try {
                os.close();
            } catch (Exception e) {
            }
            try {
                is.close();
            } catch (Exception e) {
            }
            if (httppost != null) {
                httppost.releaseConnection();
            }
        }
        return null;
    }
}
