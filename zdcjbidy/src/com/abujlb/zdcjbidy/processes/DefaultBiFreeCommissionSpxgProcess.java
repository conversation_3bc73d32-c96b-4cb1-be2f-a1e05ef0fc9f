package com.abujlb.zdcjbidy.processes;

import com.abujlb.zdcjbidy.annos.BiPro;
import com.abujlb.zdcjbidy.bean.BiDpConfig;
import com.abujlb.zdcjbidy.bean.BiInfo;
import com.abujlb.zdcjbidy.constants.ZdcjbidyConstant;
import com.abujlb.zdcjbidy.cookie.DyZh;
import com.abujlb.zdcjbidy.http.BiDyHttp;
import com.abujlb.zdcjbidy.oss.BiDataOssUtil;
import com.abujlb.zdcjbidy.thread.BiThreadLocalObjects;
import com.abujlb.zdcjbidy.util.BiDateUtil;
import com.abujlb.zdcjbidy.util.DySpxgXlsReader;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 抖音 商品卡预估佣金、图文预估佣金 采集
 * 关联tapd：https://www.tapd.cn/47708728/prong/stories/view/1147708728001002716
 *
 * <AUTHOR>
 * @date 2023/12/12
 */
@Component
@BiPro(value = ZdcjbidyConstant.INDEX_19, authType = ZdcjbidyConstant.FREE_COMMISSION_SPXG, localRunNotInclude = true)
public class DefaultBiFreeCommissionSpxgProcess extends AbstractBiProcess {

    private static final Map<String, Integer> map = new HashMap<>();

    static {
        //content_type
        map.put("all", 1);//全部
        map.put("zb", 2);//直播
        map.put("dsp", 3);//短视频
        map.put("spk", 4);//商品卡
        map.put("tw", 5);//图文
    }

    /**
     * <ul>
     *     <li>采集页面：https://compass.jinritemai.com/shop/commodity/product-list</li>
     *     <li>按日采集载体渠道为全部、直播、短视频、商品卡、图文的商品效果</li>
     *
     *     <ul>选项：
     *          <li>售卖类型：全部</li>
     *          <li>载体渠道：分别选择全部、直播、短视频、商品卡、图文</li>
     *          <li>商品分类：全部</li>
     *          <li>按照自然日，一天一天的去采集</li>
     *     </ul>
     *      <li>采集方式：下载报表,一次性最多采集5天的数据</li>
     *      <li>文件名： 全部：spxg_all.xlsx   直播：spxg_zb.xlsx  短视频：spxg_dsp.xlsx  商品卡：spxg_spk.xlsx  图文：spxg_tw.xlsx</li>
     * </ul>
     */
    @Override
    public void cj() {
        int maxday = 0;
        for (String rq : getCjzh().getCjrqMapRqList(ZdcjbidyConstant.FREE_COMMISSION_SPXG)) {
            if (cj(rq)) {
                getBiInfo().getCjrq().setFreeCommissionSpxg(rq);
                BiThreadLocalObjects.getMsgBean().getFreeCommissionSpxg().add(rq);
                maxday++;
                if (maxday >= 5) {
                    break;
                }
            } else {
                break;
            }
        }
    }


    @Override
    public boolean cjrqCheck() {
        BiInfo biInfo = BiThreadLocalObjects.getBiInfo();

        List<String> rqList = BiDateUtil.getCjsj(biInfo.getStart(), biInfo.getCjrq().getFreeCommissionSpxg());
        if (CollectionUtils.isEmpty(rqList)) {
            return false;
        }

        if (BiDateUtil.currHour() < 10) {
            return false;
        }

        if (getBiInfo().getCjrq().getSpxg().compareTo(BiDateUtil.getLastday()) == 0
                && CollectionUtils.isEmpty(BiThreadLocalObjects.getMsgBean().getSpxg())
                && getBiInfo().getCjrq().getDr_zy_spxg().compareTo(BiDateUtil.getLastday()) == 0
                && CollectionUtils.isEmpty(BiThreadLocalObjects.getMsgBean().getDr_zy_spxg())) {
            DyZh cjzh = BiThreadLocalObjects.getCjzh();
            cjzh.putCjrqMap(ZdcjbidyConstant.FREE_COMMISSION_SPXG, rqList);
            return true;
        } else {
            return false;
        }
    }

    @Override
    public boolean dpconfigCheck() {
        BiDpConfig biDpConfig = BiThreadLocalObjects.getBiInfo().getBiDpConfig();
        return biDpConfig.getFreeCommissionSpxg() == 0;
    }

    @Override
    public void notCj() {
        String lastday = BiDateUtil.getLastday();
        getBiInfo().getCjrq().setFreeCommissionSpxg(lastday);
        BiThreadLocalObjects.getMsgBean().getFreeCommissionSpxg().addAll(getCjzh().getCjrqMapRqList(ZdcjbidyConstant.FREE_COMMISSION_SPXG));
    }


    /**
     * <p>采集预估佣金</p>
     *
     * @param rq 日期
     * @return true采集成功 false采集失败
     */
    private boolean cj(String rq) {
        for (Map.Entry<String, Integer> entry : map.entrySet()) {
            int contentType = entry.getValue();
            //下载报表
            String filepath = BiDyHttp.downloadSpxgXls2(getCjzh(), rq, rq, 1, contentType);
            if (Objects.isNull(filepath)) {
                return false;
            }
            File file = null;
            try {
                file = new File(filepath);
                if (!file.exists()) {
                    return false;
                }
                //上传oss
                BiDataOssUtil.upload(file, "bi_dy/" + getCjzh().getUserid() + "/" + rq + "/spxg_" + entry.getKey() + ".xlsx");

                //简单解析一下
                //因为有时候会下载失败
                //所以如果解析结果不为null 则认为采集成功 反之则失败
                if (!DySpxgXlsReader.checkXLS(file)) {
                    return false;
                }
            } finally {
                if (file != null && file.exists()) {
                    file.delete();
                }
            }

            try {
                TimeUnit.SECONDS.sleep(30);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
        return true;
    }
}
