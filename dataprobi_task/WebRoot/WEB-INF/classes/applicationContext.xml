<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	   xmlns:context="http://www.springframework.org/schema/context"
	   xmlns:mvc="http://www.springframework.org/schema/mvc" xmlns:task="http://www.springframework.org/schema/task"
	   xmlns:tx="http://www.springframework.org/schema/tx" xmlns:aop="http://www.springframework.org/schema/aop"
	   xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
        http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-4.1.xsd
        http://www.springframework.org/schema/mvc http://www.springframework.org/schema/mvc/spring-mvc-4.1.xsd
        http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-4.1.xsd
        http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-4.1.xsd
        http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-4.1.xsd">

	<context:component-scan base-package="com.abujlb"/>
	<aop:aspectj-autoproxy proxy-target-class="true"/>
	<mvc:default-servlet-handler/>
	<task:annotation-driven/>
	<mvc:annotation-driven>
		<mvc:message-converters register-defaults="true">
			<bean class="org.springframework.http.converter.StringHttpMessageConverter">
				<constructor-arg value="UTF-8"/>
			</bean>
		</mvc:message-converters>
	</mvc:annotation-driven>

	<!--事务管理 -->
	<bean id="transactionManager"
		  class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
		<property name="dataSource" ref="dataSource"/>
	</bean>

	<!--事务管理注解驱动 -->
	<tx:annotation-driven transaction-manager="transactionManager"/>

	<!--引入外部资源文件 -->
	<context:property-placeholder location="classpath:/config/db_2.properties"/>

	<!--第三方jar中的bean定义在xml中 -->
	<bean id="dataSource" class="com.alibaba.druid.pool.DruidDataSource">
		<property name="driverClassName" value="${jdbc.driver}"/>
		<property name="url" value="${jdbc.url}"/>
		<property name="username" value="${jdbc.username}"/>
		<property name="password" value="${jdbc.password}"/>
		<property name="maxActive" value="${jdbc.maxActive}"/>
		<property name="validationQuery" value="select 1"/>
		<property name="testWhileIdle" value="true"/>
	</bean>

	<!--SqlSessionFactory对象应该放到Spring容器中作为单例对象管理 原来mybaits中sqlSessionFactory的构建是需要素材的：SqlMapConfig.xml中的内容 -->
	<bean id="sqlSessionFactory"
		  class="com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean">
		<!--别名映射扫描 -->
		<property name="typeAliasesPackage" value="com.abujlb.dataprobi.pojo"/>
		<!--数据源dataSource -->
		<property name="dataSource" ref="dataSource"/>
		<property name="configLocation" value="classpath:mybatis-config.xml"/>
	</bean>

	<!--Mapper动态代理对象交给Spring管理，我们从Spring容器中直接获得Mapper的代理对象 -->
	<!--扫描mapper接口，生成代理对象，生成的代理对象会存储在ioc容器中 -->
	<bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
		<!--mapper接口包路径配置 -->
		<property name="basePackage" value="com.abujlb.dataprobi.mapper"/>
		<property name="sqlSessionFactoryBeanName" value="sqlSessionFactory"/>
	</bean>

	<bean
			class="org.springframework.web.servlet.view.InternalResourceViewResolver"
			id="internalResourceViewResolver">
		<property name="prefix" value="/WEB-INF/jsp/"/>
		<property name="suffix" value=".jsp"/>
	</bean>

	<bean id="multipartResolver"
		  class="org.springframework.web.multipart.commons.CommonsMultipartResolver">
		<property name="maxUploadSize" value="104857600"/>
		<property name="maxInMemorySize" value="4096"/>
		<property name="defaultEncoding" value="UTF-8"></property>
	</bean>

	<mvc:interceptors>
		<mvc:interceptor>
			<mvc:mapping path="/**"/>
			<mvc:exclude-mapping path="/css/**"/>
			<mvc:exclude-mapping path="/images/**"/>
			<mvc:exclude-mapping path="/js/**"/>
			<bean class="com.abujlb.interceptor.SpringContextInterceptor"></bean>
		</mvc:interceptor>
	</mvc:interceptors>
</beans>
