package com.abujlb.dataprobitask.tsdao;

import com.abujlb.dao.v2.TsDao2;
import com.abujlb.dataprobitask.bean.sycmperformance.*;
import com.abujlb.dataprobitask.constants.RqlxConst;
import com.alicloud.openservices.tablestore.SyncClient;
import com.alicloud.openservices.tablestore.model.*;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/9/23
 */
@Component
public class BiPerformanceCustomerDataTsDao {

    private static final Logger LOG = Logger.getLogger(BiPerformanceCustomerDataTsDao.class);
    private static final String TABLE_NAME = "bi_performance_customer_data";
    private SyncClient client = null;
    private SyncClient clientTest = null;

    @Autowired
    private TsDao2 tsDao2;

    @PostConstruct
    public void init() {
        client = tsDao2.getClient("abujlb7@jushita").getClient();
        clientTest = tsDao2.getClient("abujlb7test@jushita").getClient();
    }

    public void updateRows(List<CustomerCoreMonitor> list) {
        try {
            if (CollectionUtils.isEmpty(list)) {
                return;
            }
            List<List<CustomerCoreMonitor>> partition = Lists.partition(list, 100);
            for (List<CustomerCoreMonitor> customerCoreMonitors : partition) {
                write(customerCoreMonitors);
                writeTest(customerCoreMonitors);
            }

        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
    }

    public void updateRows2(List<CustomerPerformanceSummary> list) {
        try {
            if (CollectionUtils.isEmpty(list)) {
                return;
            }

            List<List<CustomerPerformanceSummary>> partition = Lists.partition(list, 100);
            for (List<CustomerPerformanceSummary> customerPerformanceSummaries : partition) {
                write2(customerPerformanceSummaries);
                writeTest2(customerPerformanceSummaries);
            }
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
    }

    public void updateRows3(List<CustomerReceptionData2> list) {
        try {
            if (CollectionUtils.isEmpty(list)) {
                return;
            }

            List<List<CustomerReceptionData2>> partition = Lists.partition(list, 100);
            for (List<CustomerReceptionData2> customerReceptionData : partition) {
                write3(customerReceptionData);
                writeTest3(customerReceptionData);
            }
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
    }

    public void updateRows4(List<CustomerReceptionData> list) {
        try {
            if (CollectionUtils.isEmpty(list)) {
                return;
            }

            List<List<CustomerReceptionData>> partition = Lists.partition(list, 100);
            for (List<CustomerReceptionData> customerReceptionData : partition) {
                write4(customerReceptionData);
                writeTest4(customerReceptionData);
            }
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
    }

    public void updateRows44(List<CustomerReceptionData> list) {
        try {
            if (CollectionUtils.isEmpty(list)) {
                return;
            }

            List<List<CustomerReceptionData>> partition = Lists.partition(list, 100);
            for (List<CustomerReceptionData> customerReceptionData : partition) {
                write44(customerReceptionData);
                writeTest44(customerReceptionData);
            }
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
    }

    public void updateRowsTgcKf(List<CustomerReceptionTgcData> list) {
        try {
            if (CollectionUtils.isEmpty(list)) {
                return;
            }

            List<List<CustomerReceptionTgcData>> partition = Lists.partition(list, 100);
            for (List<CustomerReceptionTgcData> customerReceptionData : partition) {
                writeTgcKf(customerReceptionData);
                writeTgcKfTest(customerReceptionData);
            }
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
    }

    public void updateRows5(List<CustomerReceptionData> list) {
        try {
            if (CollectionUtils.isEmpty(list)) {
                return;
            }

            List<List<CustomerReceptionData>> partition = Lists.partition(list, 100);
            for (List<CustomerReceptionData> customerReceptionData : partition) {
                write5(customerReceptionData);
                writeTest5(customerReceptionData);
            }
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
    }

    public void updateRows6(List<CustomerDutyData> list) {
        try {
            if (CollectionUtils.isEmpty(list)) {
                return;
            }

            List<List<CustomerDutyData>> partition = Lists.partition(list, 100);
            for (List<CustomerDutyData> customerReceptionData : partition) {
                write6(customerReceptionData);
                write6Test(customerReceptionData);
            }
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
    }


    public void updateRows7(List<ReaData> list) {
        try {
            if (CollectionUtils.isEmpty(list)) {
                return;
            }

            List<List<ReaData>> partition = Lists.partition(list, 100);
            for (List<ReaData> customerReceptionData : partition) {
                write7(customerReceptionData);
                write7Test(customerReceptionData);
            }
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
    }


    public void updateRows8(List<InquiiryOrderData> list) {
        try {
            if (CollectionUtils.isEmpty(list)) {
                return;
            }

            List<List<InquiiryOrderData>> partition = Lists.partition(list, 100);
            for (List<InquiiryOrderData> dataList : partition) {
                write8(dataList);
                write8Test(dataList);
            }
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
    }

    public void updateRows9(List<CustomerReceptionData> list) {
        try {
            if (CollectionUtils.isEmpty(list)) {
                return;
            }

            List<List<CustomerReceptionData>> partition = Lists.partition(list, 100);
            for (List<CustomerReceptionData> customerReceptionData : partition) {
                write9(customerReceptionData);
                writeTest9(customerReceptionData);
            }
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
    }

    public void updateRows10(List<CustomerRefundData> list) {
        try {
            if (CollectionUtils.isEmpty(list)) {
                return;
            }

            List<List<CustomerRefundData>> partition = Lists.partition(list, 100);
            for (List<CustomerRefundData> customerReceptionData : partition) {
                write10(customerReceptionData);
                writeTest10(customerReceptionData);
            }
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
    }


    public void updateRows11(List<JdJmKfReceptionData> list) {
        try {
            if (CollectionUtils.isEmpty(list)) {
                return;
            }

            List<List<JdJmKfReceptionData>> partition = Lists.partition(list, 100);
            for (List<JdJmKfReceptionData> jmKfData : partition) {
                write11(jmKfData);
                writeTest11(jmKfData);
            }
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
    }


    public void updateRows12(List<JdJmKfMarketingData> list) {
        try {
            if (CollectionUtils.isEmpty(list)) {
                return;
            }

            List<List<JdJmKfMarketingData>> partition = Lists.partition(list, 100);
            for (List<JdJmKfMarketingData> jmKfData : partition) {
                write12(jmKfData);
                writeTest12(jmKfData);
            }
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
    }

    public void updateRows13(List<JdJmKfAttendanceData> list) {
        try {
            if (CollectionUtils.isEmpty(list)) {
                return;
            }

            List<List<JdJmKfAttendanceData>> partition = Lists.partition(list, 100);
            for (List<JdJmKfAttendanceData> jmKfData : partition) {
                write13(jmKfData);
                writeTest13(jmKfData);
            }
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
    }

    public void updateRows14(List<CustomerReceptionData> list) {
        try {
            if (CollectionUtils.isEmpty(list)) {
                return;
            }
            List<List<CustomerReceptionData>> partition = Lists.partition(list, 100);
            for (List<CustomerReceptionData> customerReceptionData : partition) {
                write14(customerReceptionData);
                writeTest14(customerReceptionData);
            }
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
    }

    private void write(List<CustomerCoreMonitor> list) {
        BatchWriteRowRequest batchWriteRowRequest = new BatchWriteRowRequest();

        for (CustomerCoreMonitor data : list) {
            if (StringUtils.isBlank(data.getWwzh())) {
                continue;
            }
            PrimaryKeyBuilder pk1Builder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
            pk1Builder.addPrimaryKeyColumn("yhid_qd_userid", PrimaryKeyValue.fromString(data.getYhid() + "_" + data.getQd() + "_" + data.getUserid()));
            if (data.getRqlx().equals(RqlxConst.RECENT7) || data.getRqlx().equals(RqlxConst.RECENT30)) {
                pk1Builder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(data.getRqlx()));
            } else {
                pk1Builder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(data.getRqlx() + "_" + data.getRq()));
            }
            pk1Builder.addPrimaryKeyColumn("kf", PrimaryKeyValue.fromString(data.getWwzh()));
            RowUpdateChange rowUpdateChange = new RowUpdateChange(TABLE_NAME, pk1Builder.build());
            rowUpdateChange.put("yhid", ColumnValue.fromLong(data.getYhid()));
            rowUpdateChange.put("qd", ColumnValue.fromString(data.getQd()));
            rowUpdateChange.put("userid", ColumnValue.fromString(data.getUserid()));
            if (StringUtils.isNotBlank(data.getWwzhmc())) {
                rowUpdateChange.put("kfnc", ColumnValue.fromString(data.getWwzhmc()));
            }
            rowUpdateChange.put("rqlx", ColumnValue.fromString(data.getRqlx()));
            rowUpdateChange.put("rq", ColumnValue.fromString(data.getRq()));
            rowUpdateChange.put("zxrs", ColumnValue.fromLong(data.getZxrs()));
            rowUpdateChange.put("jdrs", ColumnValue.fromLong(data.getJdrs()));
            rowUpdateChange.put("yxjdrs", ColumnValue.fromLong(data.getYxjdrs()));
            rowUpdateChange.put("kfxsrs", ColumnValue.fromLong(data.getKfxsrs()));
            rowUpdateChange.put("kfxse", ColumnValue.fromDouble(data.getKfxse()));
            rowUpdateChange.put("kfxsezb", ColumnValue.fromDouble(data.getKfxsezb()));
            rowUpdateChange.put("kfxskdj", ColumnValue.fromDouble(data.getKfxskdj()));
            if (data.getXdrss2() != null) {
                rowUpdateChange.put("xdrss", ColumnValue.fromLong(data.getXdrss2()));
            }
            if (data.getXdzhlv2() != null) {
                rowUpdateChange.put("xdzhlv", ColumnValue.fromDouble(data.getXdzhlv2()));
            }
            if (data.getPjxysj2() != null) {
                rowUpdateChange.put("pjxysj", ColumnValue.fromDouble(data.getPjxysj2()));
            }
            if (data.getKhmylv2() != null) {
                rowUpdateChange.put("khmylv", ColumnValue.fromDouble(data.getKhmylv2()));
            }
            if (data.getKhmyfwd2() != null) {
                rowUpdateChange.put("khmyfwd", ColumnValue.fromDouble(data.getKhmyfwd2()));
            }
            batchWriteRowRequest.addRowChange(rowUpdateChange);
        }
        if (batchWriteRowRequest.getRowsCount() == 0) {
            return;
        }
        int maxRetryTime = 0;
        BatchWriteRowRequest retryRequest = null;
        while (maxRetryTime < 10) {
            BatchWriteRowResponse batchWriteRowResponse = client.batchWriteRow(retryRequest == null ? batchWriteRowRequest : retryRequest);
            if (batchWriteRowResponse.isAllSucceed()) {
                break;
            }

            retryRequest = batchWriteRowRequest.createRequestForRetry(batchWriteRowResponse.getFailedRows());
            maxRetryTime++;
        }
    }

    private void writeTest(List<CustomerCoreMonitor> list) {
        BatchWriteRowRequest batchWriteRowRequest = new BatchWriteRowRequest();

        for (CustomerCoreMonitor data : list) {
            if (StringUtils.isBlank(data.getWwzh())) {
                continue;
            }
            PrimaryKeyBuilder pk1Builder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
            pk1Builder.addPrimaryKeyColumn("yhid_qd_userid", PrimaryKeyValue.fromString(data.getYhid() + "_" + data.getQd() + "_" + data.getUserid()));
            if (data.getRqlx().equals(RqlxConst.RECENT7) || data.getRqlx().equals(RqlxConst.RECENT30)) {
                pk1Builder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(data.getRqlx()));
            } else {
                pk1Builder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(data.getRqlx() + "_" + data.getRq()));
            }
            pk1Builder.addPrimaryKeyColumn("kf", PrimaryKeyValue.fromString(data.getWwzh()));
            RowUpdateChange rowUpdateChange = new RowUpdateChange(TABLE_NAME, pk1Builder.build());
            rowUpdateChange.put("yhid", ColumnValue.fromLong(data.getYhid()));
            rowUpdateChange.put("qd", ColumnValue.fromString(data.getQd()));
            rowUpdateChange.put("userid", ColumnValue.fromString(data.getUserid()));
            if (StringUtils.isNotBlank(data.getWwzhmc())) {
                rowUpdateChange.put("kfnc", ColumnValue.fromString(data.getWwzhmc()));
            }
            rowUpdateChange.put("rqlx", ColumnValue.fromString(data.getRqlx()));
            rowUpdateChange.put("rq", ColumnValue.fromString(data.getRq()));
            rowUpdateChange.put("zxrs", ColumnValue.fromLong(data.getZxrs()));
            rowUpdateChange.put("jdrs", ColumnValue.fromLong(data.getJdrs()));
            rowUpdateChange.put("yxjdrs", ColumnValue.fromLong(data.getYxjdrs()));
            rowUpdateChange.put("kfxsrs", ColumnValue.fromLong(data.getKfxsrs()));
            rowUpdateChange.put("kfxse", ColumnValue.fromDouble(data.getKfxse()));
            rowUpdateChange.put("kfxsezb", ColumnValue.fromDouble(data.getKfxsezb()));
            rowUpdateChange.put("kfxskdj", ColumnValue.fromDouble(data.getKfxskdj()));
            if (data.getXdrss2() != null) {
                rowUpdateChange.put("xdrss", ColumnValue.fromLong(data.getXdrss2()));
            }
            if (data.getXdzhlv2() != null) {
                rowUpdateChange.put("xdzhlv", ColumnValue.fromDouble(data.getXdzhlv2()));
            }
            if (data.getPjxysj2() != null) {
                rowUpdateChange.put("pjxysj", ColumnValue.fromDouble(data.getPjxysj2()));
            }
            if (data.getKhmylv2() != null) {
                rowUpdateChange.put("khmylv", ColumnValue.fromDouble(data.getKhmylv2()));
            }
            if (data.getKhmyfwd2() != null) {
                rowUpdateChange.put("khmyfwd", ColumnValue.fromDouble(data.getKhmyfwd2()));
            }
            batchWriteRowRequest.addRowChange(rowUpdateChange);
        }
        if (batchWriteRowRequest.getRowsCount() == 0) {
            return;
        }

        int maxRetryTime = 0;
        BatchWriteRowRequest retryRequest = null;
        while (maxRetryTime < 10) {
            BatchWriteRowResponse batchWriteRowResponse = clientTest.batchWriteRow(retryRequest == null ? batchWriteRowRequest : retryRequest);
            if (batchWriteRowResponse.isAllSucceed()) {
                break;
            }

            retryRequest = batchWriteRowRequest.createRequestForRetry(batchWriteRowResponse.getFailedRows());
            maxRetryTime++;
        }
    }


    private void write2(List<CustomerPerformanceSummary> list) {
        BatchWriteRowRequest batchWriteRowRequest = new BatchWriteRowRequest();

        for (CustomerPerformanceSummary data : list) {
            if (StringUtils.isBlank(data.getWwzh())) {
                continue;
            }
            PrimaryKeyBuilder pk1Builder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
            pk1Builder.addPrimaryKeyColumn("yhid_qd_userid", PrimaryKeyValue.fromString(data.getYhid() + "_" + data.getQd() + "_" + data.getUserid()));
            if (data.getRqlx().equals(RqlxConst.RECENT7) || data.getRqlx().equals(RqlxConst.RECENT30)) {
                pk1Builder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(data.getRqlx()));
            } else {
                pk1Builder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(data.getRqlx() + "_" + data.getRq()));
            }
            pk1Builder.addPrimaryKeyColumn("kf", PrimaryKeyValue.fromString(data.getWwzh()));
            RowUpdateChange rowUpdateChange = new RowUpdateChange(TABLE_NAME, pk1Builder.build());
            rowUpdateChange.put("yhid", ColumnValue.fromLong(data.getYhid()));
            rowUpdateChange.put("qd", ColumnValue.fromString(data.getQd()));
            rowUpdateChange.put("userid", ColumnValue.fromString(data.getUserid()));
            if (StringUtils.isNotBlank(data.getWwzhmc())) {
                rowUpdateChange.put("kfnc", ColumnValue.fromString(data.getWwzhmc()));
            }
            rowUpdateChange.put("rqlx", ColumnValue.fromString(data.getRqlx()));
            rowUpdateChange.put("rq", ColumnValue.fromString(data.getRq()));
            rowUpdateChange.put("xdrs", ColumnValue.fromLong(data.getXdrs()));
            rowUpdateChange.put("xdje", ColumnValue.fromDouble(data.getXdje()));
            rowUpdateChange.put("xsl", ColumnValue.fromLong(data.getXsl()));
            rowUpdateChange.put("ddl", ColumnValue.fromLong(data.getDdl()));
            rowUpdateChange.put("cgtkje", ColumnValue.fromDouble(data.getCgtkje()));
            rowUpdateChange.put("kfjxse", ColumnValue.fromDouble(data.getKfjxse()));

            batchWriteRowRequest.addRowChange(rowUpdateChange);
        }
        if (batchWriteRowRequest.getRowsCount() == 0) {
            return;
        }
        int maxRetryTime = 0;
        BatchWriteRowRequest retryRequest = null;
        while (maxRetryTime < 10) {
            BatchWriteRowResponse batchWriteRowResponse = client.batchWriteRow(retryRequest == null ? batchWriteRowRequest : retryRequest);
            if (batchWriteRowResponse.isAllSucceed()) {
                break;
            }

            retryRequest = batchWriteRowRequest.createRequestForRetry(batchWriteRowResponse.getFailedRows());
            maxRetryTime++;
        }
    }

    private void writeTest2(List<CustomerPerformanceSummary> list) {
        BatchWriteRowRequest batchWriteRowRequest = new BatchWriteRowRequest();

        for (CustomerPerformanceSummary data : list) {
            if (StringUtils.isBlank(data.getWwzh())) {
                continue;
            }
            PrimaryKeyBuilder pk1Builder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
            pk1Builder.addPrimaryKeyColumn("yhid_qd_userid", PrimaryKeyValue.fromString(data.getYhid() + "_" + data.getQd() + "_" + data.getUserid()));
            if (data.getRqlx().equals(RqlxConst.RECENT7) || data.getRqlx().equals(RqlxConst.RECENT30)) {
                pk1Builder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(data.getRqlx()));
            } else {
                pk1Builder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(data.getRqlx() + "_" + data.getRq()));
            }
            pk1Builder.addPrimaryKeyColumn("kf", PrimaryKeyValue.fromString(data.getWwzh()));
            RowUpdateChange rowUpdateChange = new RowUpdateChange(TABLE_NAME, pk1Builder.build());
            rowUpdateChange.put("yhid", ColumnValue.fromLong(data.getYhid()));
            rowUpdateChange.put("qd", ColumnValue.fromString(data.getQd()));
            rowUpdateChange.put("userid", ColumnValue.fromString(data.getUserid()));
            if (StringUtils.isNotBlank(data.getWwzhmc())) {
                rowUpdateChange.put("kfnc", ColumnValue.fromString(data.getWwzhmc()));
            }
            rowUpdateChange.put("rqlx", ColumnValue.fromString(data.getRqlx()));
            rowUpdateChange.put("rq", ColumnValue.fromString(data.getRq()));
            rowUpdateChange.put("xdrs", ColumnValue.fromLong(data.getXdrs()));
            rowUpdateChange.put("xdje", ColumnValue.fromDouble(data.getXdje()));
            rowUpdateChange.put("xsl", ColumnValue.fromLong(data.getXsl()));
            rowUpdateChange.put("ddl", ColumnValue.fromLong(data.getDdl()));
            rowUpdateChange.put("cgtkje", ColumnValue.fromDouble(data.getCgtkje()));
            rowUpdateChange.put("kfjxse", ColumnValue.fromDouble(data.getKfjxse()));

            batchWriteRowRequest.addRowChange(rowUpdateChange);
        }
        if (batchWriteRowRequest.getRowsCount() == 0) {
            return;
        }

        int maxRetryTime = 0;
        BatchWriteRowRequest retryRequest = null;
        while (maxRetryTime < 10) {
            BatchWriteRowResponse batchWriteRowResponse = clientTest.batchWriteRow(retryRequest == null ? batchWriteRowRequest : retryRequest);
            if (batchWriteRowResponse.isAllSucceed()) {
                break;
            }

            retryRequest = batchWriteRowRequest.createRequestForRetry(batchWriteRowResponse.getFailedRows());
            maxRetryTime++;
        }
    }


    private void write3(List<CustomerReceptionData2> list) {
        BatchWriteRowRequest batchWriteRowRequest = new BatchWriteRowRequest();

        for (CustomerReceptionData2 data : list) {
            if (StringUtils.isBlank(data.getWwzh())) {
                continue;
            }
            PrimaryKeyBuilder pk1Builder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
            pk1Builder.addPrimaryKeyColumn("yhid_qd_userid", PrimaryKeyValue.fromString(data.getYhid() + "_" + data.getQd() + "_" + data.getUserid()));
            if (data.getRqlx().equals(RqlxConst.RECENT7) || data.getRqlx().equals(RqlxConst.RECENT30)) {
                pk1Builder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(data.getRqlx()));
            } else {
                pk1Builder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(data.getRqlx() + "_" + data.getRq()));
            }
            pk1Builder.addPrimaryKeyColumn("kf", PrimaryKeyValue.fromString(data.getWwzh()));
            RowUpdateChange rowUpdateChange = new RowUpdateChange(TABLE_NAME, pk1Builder.build());
            rowUpdateChange.put("yhid", ColumnValue.fromLong(data.getYhid()));
            rowUpdateChange.put("qd", ColumnValue.fromString(data.getQd()));
            rowUpdateChange.put("userid", ColumnValue.fromString(data.getUserid()));
            if (StringUtils.isNotBlank(data.getWwzhmc())) {
                rowUpdateChange.put("kfnc", ColumnValue.fromString(data.getWwzhmc()));
            }
            rowUpdateChange.put("rqlx", ColumnValue.fromString(data.getRqlx()));
            rowUpdateChange.put("rq", ColumnValue.fromString(data.getRq()));
            rowUpdateChange.put("whfrs", ColumnValue.fromLong(data.getWhfrs()));
            rowUpdateChange.put("zxkfrc", ColumnValue.fromLong(data.getZxkfrc()));
            rowUpdateChange.put("kfhfrc", ColumnValue.fromLong(data.getKfhfrc()));
            rowUpdateChange.put("kfwhfrc", ColumnValue.fromLong(data.getKfwhfrc()));
            rowUpdateChange.put("wwhfl", ColumnValue.fromDouble(data.getWwhfl()));
            if (data.getScxysc2() != null) {
                rowUpdateChange.put("scxysc", ColumnValue.fromDouble(data.getScxysc2()));
            }
            rowUpdateChange.put("sfzrgxylv", ColumnValue.fromDouble(data.getSfzrgxylv()));
            rowUpdateChange.put("mxyrs", ColumnValue.fromLong(data.getMxyrs()));
            rowUpdateChange.put("cjdrs", ColumnValue.fromLong(data.getCjdrs()));
            rowUpdateChange.put("zjjdrs", ColumnValue.fromLong(data.getZjjdrs()));
            rowUpdateChange.put("zrrs", ColumnValue.fromLong(data.getZrrs()));
            rowUpdateChange.put("zcrs", ColumnValue.fromLong(data.getZcrs()));
            rowUpdateChange.put("mjfqrs", ColumnValue.fromLong(data.getMjfqrs()));
            rowUpdateChange.put("kfzdgjrs", ColumnValue.fromLong(data.getKfzdgjrs()));
            rowUpdateChange.put("zxxs", ColumnValue.fromLong(data.getZxxs()));
            rowUpdateChange.put("mjxxts", ColumnValue.fromLong(data.getMjxxts()));
            rowUpdateChange.put("kfxxts", ColumnValue.fromLong(data.getKfxxts()));
            rowUpdateChange.put("dwb", ColumnValue.fromDouble(data.getDwb()));
            rowUpdateChange.put("kfzs", ColumnValue.fromLong(data.getKfzs()));
            if (data.getPjjdsc2() != null) {
                rowUpdateChange.put("pjjdsc", ColumnValue.fromDouble(data.getPjjdsc2()));
            }
            batchWriteRowRequest.addRowChange(rowUpdateChange);
        }
        if (batchWriteRowRequest.getRowsCount() == 0) {
            return;
        }
        int maxRetryTime = 0;
        BatchWriteRowRequest retryRequest = null;
        while (maxRetryTime < 10) {
            BatchWriteRowResponse batchWriteRowResponse = client.batchWriteRow(retryRequest == null ? batchWriteRowRequest : retryRequest);
            if (batchWriteRowResponse.isAllSucceed()) {
                break;
            }

            retryRequest = batchWriteRowRequest.createRequestForRetry(batchWriteRowResponse.getFailedRows());
            maxRetryTime++;
        }
    }

    private void writeTest3(List<CustomerReceptionData2> list) {
        BatchWriteRowRequest batchWriteRowRequest = new BatchWriteRowRequest();

        for (CustomerReceptionData2 data : list) {
            if (StringUtils.isBlank(data.getWwzh())) {
                continue;
            }
            PrimaryKeyBuilder pk1Builder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
            pk1Builder.addPrimaryKeyColumn("yhid_qd_userid", PrimaryKeyValue.fromString(data.getYhid() + "_" + data.getQd() + "_" + data.getUserid()));
            if (data.getRqlx().equals(RqlxConst.RECENT7) || data.getRqlx().equals(RqlxConst.RECENT30)) {
                pk1Builder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(data.getRqlx()));
            } else {
                pk1Builder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(data.getRqlx() + "_" + data.getRq()));
            }
            pk1Builder.addPrimaryKeyColumn("kf", PrimaryKeyValue.fromString(data.getWwzh()));
            RowUpdateChange rowUpdateChange = new RowUpdateChange(TABLE_NAME, pk1Builder.build());
            rowUpdateChange.put("yhid", ColumnValue.fromLong(data.getYhid()));
            rowUpdateChange.put("qd", ColumnValue.fromString(data.getQd()));
            rowUpdateChange.put("userid", ColumnValue.fromString(data.getUserid()));
            if (StringUtils.isNotBlank(data.getWwzhmc())) {
                rowUpdateChange.put("kfnc", ColumnValue.fromString(data.getWwzhmc()));
            }
            rowUpdateChange.put("rqlx", ColumnValue.fromString(data.getRqlx()));
            rowUpdateChange.put("rq", ColumnValue.fromString(data.getRq()));
            rowUpdateChange.put("whfrs", ColumnValue.fromLong(data.getWhfrs()));
            rowUpdateChange.put("zxkfrc", ColumnValue.fromLong(data.getZxkfrc()));
            rowUpdateChange.put("kfhfrc", ColumnValue.fromLong(data.getKfhfrc()));
            rowUpdateChange.put("kfwhfrc", ColumnValue.fromLong(data.getKfwhfrc()));
            rowUpdateChange.put("wwhfl", ColumnValue.fromDouble(data.getWwhfl()));
            if (data.getScxysc2() != null) {
                rowUpdateChange.put("scxysc", ColumnValue.fromDouble(data.getScxysc2()));
            }
            rowUpdateChange.put("mxyrs", ColumnValue.fromLong(data.getMxyrs()));
            rowUpdateChange.put("cjdrs", ColumnValue.fromLong(data.getCjdrs()));
            rowUpdateChange.put("zjjdrs", ColumnValue.fromLong(data.getZjjdrs()));
            rowUpdateChange.put("zrrs", ColumnValue.fromLong(data.getZrrs()));
            rowUpdateChange.put("zcrs", ColumnValue.fromLong(data.getZcrs()));
            rowUpdateChange.put("mjfqrs", ColumnValue.fromLong(data.getMjfqrs()));
            rowUpdateChange.put("kfzdgjrs", ColumnValue.fromLong(data.getKfzdgjrs()));
            rowUpdateChange.put("zxxs", ColumnValue.fromLong(data.getZxxs()));
            rowUpdateChange.put("mjxxts", ColumnValue.fromLong(data.getMjxxts()));
            rowUpdateChange.put("kfxxts", ColumnValue.fromLong(data.getKfxxts()));
            rowUpdateChange.put("dwb", ColumnValue.fromDouble(data.getDwb()));
            rowUpdateChange.put("kfzs", ColumnValue.fromLong(data.getKfzs()));
            if (data.getPjjdsc2() != null) {
                rowUpdateChange.put("pjjdsc", ColumnValue.fromDouble(data.getPjjdsc2()));
            }
            batchWriteRowRequest.addRowChange(rowUpdateChange);
        }
        if (batchWriteRowRequest.getRowsCount() == 0) {
            return;
        }

        int maxRetryTime = 0;
        BatchWriteRowRequest retryRequest = null;
        while (maxRetryTime < 10) {
            BatchWriteRowResponse batchWriteRowResponse = clientTest.batchWriteRow(retryRequest == null ? batchWriteRowRequest : retryRequest);
            if (batchWriteRowResponse.isAllSucceed()) {
                break;
            }

            retryRequest = batchWriteRowRequest.createRequestForRetry(batchWriteRowResponse.getFailedRows());
            maxRetryTime++;
        }
    }

    private void write4(List<CustomerReceptionData> list) {
        BatchWriteRowRequest batchWriteRowRequest = new BatchWriteRowRequest();

        for (CustomerReceptionData data : list) {
            if (StringUtils.isBlank(data.getWwzh())) {
                continue;
            }
            PrimaryKeyBuilder pk1Builder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
            pk1Builder.addPrimaryKeyColumn("yhid_qd_userid", PrimaryKeyValue.fromString(data.getYhid() + "_" + data.getQd() + "_" + data.getUserid()));
            if (data.getRqlx().equals(RqlxConst.RECENT7) || data.getRqlx().equals(RqlxConst.RECENT30)) {
                pk1Builder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(data.getRqlx()));
            } else {
                pk1Builder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(data.getRqlx() + "_" + data.getRq()));
            }
            pk1Builder.addPrimaryKeyColumn("kf", PrimaryKeyValue.fromString(data.getWwzh()));
            RowUpdateChange rowUpdateChange = new RowUpdateChange(TABLE_NAME, pk1Builder.build());
            rowUpdateChange.put("yhid", ColumnValue.fromLong(data.getYhid()));
            rowUpdateChange.put("qd", ColumnValue.fromString(data.getQd()));
            rowUpdateChange.put("userid", ColumnValue.fromString(data.getUserid()));
            if (StringUtils.isNotBlank(data.getWwzhmc())) {
                rowUpdateChange.put("kfnc", ColumnValue.fromString(data.getWwzhmc()));
            }
            rowUpdateChange.put("rqlx", ColumnValue.fromString(data.getRqlx()));
            rowUpdateChange.put("rq", ColumnValue.fromString(data.getRq()));
            rowUpdateChange.put("kfid", ColumnValue.fromString(data.getKfid()));
            rowUpdateChange.put("onlineDays", ColumnValue.fromLong(data.getOnlineDays()));
            rowUpdateChange.put("zjjdrs", ColumnValue.fromLong(data.getZjjdrs()));
            rowUpdateChange.put("whfrs", ColumnValue.fromLong(data.getWhfrs()));
            rowUpdateChange.put("ypjhhl", ColumnValue.fromLong(data.getYpjhhl()));
            rowUpdateChange.put("bmyl", ColumnValue.fromDouble(data.getBmyl()));
            rowUpdateChange.put("bmyhhl", ColumnValue.fromLong(data.getBmyhhl()));
            rowUpdateChange.put("tdelhhl", ColumnValue.fromLong(data.getTdelhhl()));
            rowUpdateChange.put("fylshhl", ColumnValue.fromLong(data.getFylshhl()));
            rowUpdateChange.put("mrhhl", ColumnValue.fromLong(data.getMrhhl()));
            rowUpdateChange.put("wjjhhl", ColumnValue.fromLong(data.getWjjhhl()));
            rowUpdateChange.put("ffmshhl", ColumnValue.fromLong(data.getFfmshhl()));
            rowUpdateChange.put("otherhhl", ColumnValue.fromLong(data.getOtherhhl()));
            if (data.getPjjdsc2() != null) {
                rowUpdateChange.put("pjjdsc", ColumnValue.fromDouble(data.getPjjdsc2()));
            }
            batchWriteRowRequest.addRowChange(rowUpdateChange);
        }
        if (batchWriteRowRequest.getRowsCount() == 0) {
            return;
        }
        int maxRetryTime = 0;
        BatchWriteRowRequest retryRequest = null;
        while (maxRetryTime < 10) {
            BatchWriteRowResponse batchWriteRowResponse = client.batchWriteRow(retryRequest == null ? batchWriteRowRequest : retryRequest);
            if (batchWriteRowResponse.isAllSucceed()) {
                break;
            }

            retryRequest = batchWriteRowRequest.createRequestForRetry(batchWriteRowResponse.getFailedRows());
            maxRetryTime++;
        }
    }

    private void write44(List<CustomerReceptionData> list) {
        BatchWriteRowRequest batchWriteRowRequest = new BatchWriteRowRequest();

        for (CustomerReceptionData data : list) {
            if (StringUtils.isBlank(data.getWwzh())) {
                continue;
            }
            PrimaryKeyBuilder pk1Builder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
            pk1Builder.addPrimaryKeyColumn("yhid_qd_userid", PrimaryKeyValue.fromString(data.getYhid() + "_" + data.getQd() + "_" + data.getUserid()));
            if (data.getRqlx().equals(RqlxConst.RECENT7) || data.getRqlx().equals(RqlxConst.RECENT30)) {
                pk1Builder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(data.getRqlx()));
            } else {
                pk1Builder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(data.getRqlx() + "_" + data.getRq()));
            }
            pk1Builder.addPrimaryKeyColumn("kf", PrimaryKeyValue.fromString(data.getWwzh()));
            RowUpdateChange rowUpdateChange = new RowUpdateChange(TABLE_NAME, pk1Builder.build());
            rowUpdateChange.put("yhid", ColumnValue.fromLong(data.getYhid()));
            rowUpdateChange.put("qd", ColumnValue.fromString(data.getQd()));
            rowUpdateChange.put("userid", ColumnValue.fromString(data.getUserid()));
            if (StringUtils.isNotBlank(data.getWwzhmc())) {
                rowUpdateChange.put("kfnc", ColumnValue.fromString(data.getWwzhmc()));
            }
            rowUpdateChange.put("rqlx", ColumnValue.fromString(data.getRqlx()));
            rowUpdateChange.put("rq", ColumnValue.fromString(data.getRq()));
            rowUpdateChange.put("rgjdrs", ColumnValue.fromLong(data.getRgjdrs()));
            rowUpdateChange.put("bmyl", ColumnValue.fromDouble(data.getBmyl()));
            rowUpdateChange.put("smrghfl", ColumnValue.fromDouble(data.getSmrghfl()));
            rowUpdateChange.put("kfxse", ColumnValue.fromDouble(data.getKfxse()));
            rowUpdateChange.put("xdrs", ColumnValue.fromLong(data.getXdrs()));
            rowUpdateChange.put("xdzhlv", ColumnValue.fromDouble(data.getXdzhlv()));
            rowUpdateChange.put("scxysc", ColumnValue.fromDouble(data.getScxysc()));
            rowUpdateChange.put("pjrgxysc", ColumnValue.fromDouble(data.getPjrgxysc()));
            batchWriteRowRequest.addRowChange(rowUpdateChange);
        }
        if (batchWriteRowRequest.getRowsCount() == 0) {
            return;
        }
        int maxRetryTime = 0;
        BatchWriteRowRequest retryRequest = null;
        while (maxRetryTime < 10) {
            BatchWriteRowResponse batchWriteRowResponse = client.batchWriteRow(retryRequest == null ? batchWriteRowRequest : retryRequest);
            if (batchWriteRowResponse.isAllSucceed()) {
                break;
            }

            retryRequest = batchWriteRowRequest.createRequestForRetry(batchWriteRowResponse.getFailedRows());
            maxRetryTime++;
        }
    }

    private void writeTgcKf(List<CustomerReceptionTgcData> list) {
        BatchWriteRowRequest batchWriteRowRequest = new BatchWriteRowRequest();

        for (CustomerReceptionTgcData data : list) {
            if (StringUtils.isBlank(data.getWwzh())) {
                continue;
            }
            PrimaryKeyBuilder pk1Builder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
            pk1Builder.addPrimaryKeyColumn("yhid_qd_userid", PrimaryKeyValue.fromString(data.getYhid() + "_" + data.getQd() + "_" + data.getUserid()));
            if (data.getRqlx().equals(RqlxConst.RECENT7) || data.getRqlx().equals(RqlxConst.RECENT30)) {
                pk1Builder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(data.getRqlx()));
            } else {
                pk1Builder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(data.getRqlx() + "_" + data.getRq()));
            }
            pk1Builder.addPrimaryKeyColumn("kf", PrimaryKeyValue.fromString(data.getWwzh()));
            RowUpdateChange rowUpdateChange = new RowUpdateChange(TABLE_NAME, pk1Builder.build());
            rowUpdateChange.put("yhid", ColumnValue.fromLong(data.getYhid()));
            rowUpdateChange.put("qd", ColumnValue.fromString(data.getQd()));
            rowUpdateChange.put("userid", ColumnValue.fromString(data.getUserid()));
            if (StringUtils.isNotBlank(data.getWwzhmc())) {
                rowUpdateChange.put("kfnc", ColumnValue.fromString(data.getWwzhmc()));
            }
            rowUpdateChange.put("rqlx", ColumnValue.fromString(data.getRqlx()));
            rowUpdateChange.put("rq", ColumnValue.fromString(data.getRq()));
            rowUpdateChange.put("kfid", ColumnValue.fromString(data.getKfid()));
            rowUpdateChange.put("jdrs", ColumnValue.fromLong(data.getJdrs()));
            rowUpdateChange.put("kfhpl", ColumnValue.fromDouble(data.getKfhpl()));
            rowUpdateChange.put("pjxysc", ColumnValue.fromDouble(data.getPjxysc()));
            rowUpdateChange.put("kfxse", ColumnValue.fromDouble(data.getKfxse()));
            rowUpdateChange.put("xdzhddl", ColumnValue.fromDouble(data.getXdzhddl()));
            rowUpdateChange.put("xdzhrs", ColumnValue.fromDouble(data.getXdzhrs()));
            rowUpdateChange.put("xdzhlv", ColumnValue.fromDouble(data.getXdzhlv()));
            batchWriteRowRequest.addRowChange(rowUpdateChange);
        }
        if (batchWriteRowRequest.getRowsCount() == 0) {
            return;
        }
        int maxRetryTime = 0;
        BatchWriteRowRequest retryRequest = null;
        while (maxRetryTime < 10) {
            BatchWriteRowResponse batchWriteRowResponse = client.batchWriteRow(retryRequest == null ? batchWriteRowRequest : retryRequest);
            if (batchWriteRowResponse.isAllSucceed()) {
                break;
            }

            retryRequest = batchWriteRowRequest.createRequestForRetry(batchWriteRowResponse.getFailedRows());
            maxRetryTime++;
        }
    }

    private void writeTest4(List<CustomerReceptionData> list) {
        BatchWriteRowRequest batchWriteRowRequest = new BatchWriteRowRequest();

        for (CustomerReceptionData data : list) {
            if (StringUtils.isBlank(data.getWwzh())) {
                continue;
            }
            PrimaryKeyBuilder pk1Builder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
            pk1Builder.addPrimaryKeyColumn("yhid_qd_userid", PrimaryKeyValue.fromString(data.getYhid() + "_" + data.getQd() + "_" + data.getUserid()));
            if (data.getRqlx().equals(RqlxConst.RECENT7) || data.getRqlx().equals(RqlxConst.RECENT30)) {
                pk1Builder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(data.getRqlx()));
            } else {
                pk1Builder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(data.getRqlx() + "_" + data.getRq()));
            }
            pk1Builder.addPrimaryKeyColumn("kf", PrimaryKeyValue.fromString(data.getWwzh()));
            RowUpdateChange rowUpdateChange = new RowUpdateChange(TABLE_NAME, pk1Builder.build());
            rowUpdateChange.put("yhid", ColumnValue.fromLong(data.getYhid()));
            rowUpdateChange.put("qd", ColumnValue.fromString(data.getQd()));
            rowUpdateChange.put("userid", ColumnValue.fromString(data.getUserid()));
            if (StringUtils.isNotBlank(data.getWwzhmc())) {
                rowUpdateChange.put("kfnc", ColumnValue.fromString(data.getWwzhmc()));
            }
            rowUpdateChange.put("rqlx", ColumnValue.fromString(data.getRqlx()));
            rowUpdateChange.put("rq", ColumnValue.fromString(data.getRq()));
            rowUpdateChange.put("kfid", ColumnValue.fromString(data.getKfid()));
            rowUpdateChange.put("onlineDays", ColumnValue.fromLong(data.getOnlineDays()));
            rowUpdateChange.put("zjjdrs", ColumnValue.fromLong(data.getZjjdrs()));
            rowUpdateChange.put("whfrs", ColumnValue.fromLong(data.getWhfrs()));
            rowUpdateChange.put("ypjhhl", ColumnValue.fromLong(data.getKfhfrc()));
            rowUpdateChange.put("bmyl", ColumnValue.fromDouble(data.getBmyl()));
            rowUpdateChange.put("bmyhhl", ColumnValue.fromLong(data.getBmyhhl()));
            rowUpdateChange.put("tdelhhl", ColumnValue.fromLong(data.getTdelhhl()));
            rowUpdateChange.put("fylshhl", ColumnValue.fromLong(data.getFylshhl()));
            rowUpdateChange.put("mrhhl", ColumnValue.fromLong(data.getMrhhl()));
            rowUpdateChange.put("wjjhhl", ColumnValue.fromLong(data.getWjjhhl()));
            rowUpdateChange.put("ffmshhl", ColumnValue.fromLong(data.getFfmshhl()));
            rowUpdateChange.put("otherhhl", ColumnValue.fromLong(data.getOtherhhl()));
            if (data.getPjjdsc2() != null) {
                rowUpdateChange.put("pjjdsc", ColumnValue.fromDouble(data.getPjjdsc2()));
            }
            batchWriteRowRequest.addRowChange(rowUpdateChange);
        }
        if (batchWriteRowRequest.getRowsCount() == 0) {
            return;
        }
        int maxRetryTime = 0;
        BatchWriteRowRequest retryRequest = null;
        while (maxRetryTime < 10) {
            BatchWriteRowResponse batchWriteRowResponse = clientTest.batchWriteRow(retryRequest == null ? batchWriteRowRequest : retryRequest);
            if (batchWriteRowResponse.isAllSucceed()) {
                break;
            }

            retryRequest = batchWriteRowRequest.createRequestForRetry(batchWriteRowResponse.getFailedRows());
            maxRetryTime++;
        }
    }

    private void writeTest44(List<CustomerReceptionData> list) {
        BatchWriteRowRequest batchWriteRowRequest = new BatchWriteRowRequest();

        for (CustomerReceptionData data : list) {
            if (StringUtils.isBlank(data.getWwzh())) {
                continue;
            }
            PrimaryKeyBuilder pk1Builder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
            pk1Builder.addPrimaryKeyColumn("yhid_qd_userid", PrimaryKeyValue.fromString(data.getYhid() + "_" + data.getQd() + "_" + data.getUserid()));
            if (data.getRqlx().equals(RqlxConst.RECENT7) || data.getRqlx().equals(RqlxConst.RECENT30)) {
                pk1Builder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(data.getRqlx()));
            } else {
                pk1Builder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(data.getRqlx() + "_" + data.getRq()));
            }
            pk1Builder.addPrimaryKeyColumn("kf", PrimaryKeyValue.fromString(data.getWwzh()));
            RowUpdateChange rowUpdateChange = new RowUpdateChange(TABLE_NAME, pk1Builder.build());
            rowUpdateChange.put("yhid", ColumnValue.fromLong(data.getYhid()));
            rowUpdateChange.put("qd", ColumnValue.fromString(data.getQd()));
            rowUpdateChange.put("userid", ColumnValue.fromString(data.getUserid()));
            if (StringUtils.isNotBlank(data.getWwzhmc())) {
                rowUpdateChange.put("kfnc", ColumnValue.fromString(data.getWwzhmc()));
            }
            rowUpdateChange.put("rqlx", ColumnValue.fromString(data.getRqlx()));
            rowUpdateChange.put("rq", ColumnValue.fromString(data.getRq()));
            rowUpdateChange.put("rgjdrs", ColumnValue.fromLong(data.getRgjdrs()));
            rowUpdateChange.put("bmyl", ColumnValue.fromDouble(data.getBmyl()));
            rowUpdateChange.put("smrghfl", ColumnValue.fromDouble(data.getSmrghfl()));
            rowUpdateChange.put("kfxse", ColumnValue.fromDouble(data.getKfxse()));
            rowUpdateChange.put("xdrs", ColumnValue.fromLong(data.getXdrs()));
            rowUpdateChange.put("xdzhlv", ColumnValue.fromDouble(data.getXdzhlv()));
            rowUpdateChange.put("scxysc", ColumnValue.fromDouble(data.getScxysc()));
            rowUpdateChange.put("pjrgxysc", ColumnValue.fromDouble(data.getPjrgxysc()));
            batchWriteRowRequest.addRowChange(rowUpdateChange);
        }
        if (batchWriteRowRequest.getRowsCount() == 0) {
            return;
        }
        int maxRetryTime = 0;
        BatchWriteRowRequest retryRequest = null;
        while (maxRetryTime < 10) {
            BatchWriteRowResponse batchWriteRowResponse = clientTest.batchWriteRow(retryRequest == null ? batchWriteRowRequest : retryRequest);
            if (batchWriteRowResponse.isAllSucceed()) {
                break;
            }

            retryRequest = batchWriteRowRequest.createRequestForRetry(batchWriteRowResponse.getFailedRows());
            maxRetryTime++;
        }
    }

    private void writeTgcKfTest(List<CustomerReceptionTgcData> list) {
        BatchWriteRowRequest batchWriteRowRequest = new BatchWriteRowRequest();

        for (CustomerReceptionTgcData data : list) {
            if (StringUtils.isBlank(data.getWwzh())) {
                continue;
            }
            PrimaryKeyBuilder pk1Builder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
            pk1Builder.addPrimaryKeyColumn("yhid_qd_userid", PrimaryKeyValue.fromString(data.getYhid() + "_" + data.getQd() + "_" + data.getUserid()));
            if (data.getRqlx().equals(RqlxConst.RECENT7) || data.getRqlx().equals(RqlxConst.RECENT30)) {
                pk1Builder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(data.getRqlx()));
            } else {
                pk1Builder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(data.getRqlx() + "_" + data.getRq()));
            }
            pk1Builder.addPrimaryKeyColumn("kf", PrimaryKeyValue.fromString(data.getWwzh()));
            RowUpdateChange rowUpdateChange = new RowUpdateChange(TABLE_NAME, pk1Builder.build());
            rowUpdateChange.put("yhid", ColumnValue.fromLong(data.getYhid()));
            rowUpdateChange.put("qd", ColumnValue.fromString(data.getQd()));
            rowUpdateChange.put("userid", ColumnValue.fromString(data.getUserid()));
            if (StringUtils.isNotBlank(data.getWwzhmc())) {
                rowUpdateChange.put("kfnc", ColumnValue.fromString(data.getWwzhmc()));
            }
            rowUpdateChange.put("rqlx", ColumnValue.fromString(data.getRqlx()));
            rowUpdateChange.put("rq", ColumnValue.fromString(data.getRq()));
            rowUpdateChange.put("kfid", ColumnValue.fromString(data.getKfid()));
            rowUpdateChange.put("jdrs", ColumnValue.fromLong(data.getJdrs()));
            rowUpdateChange.put("kfhpl", ColumnValue.fromDouble(data.getKfhpl()));
            rowUpdateChange.put("pjxysc", ColumnValue.fromDouble(data.getPjxysc()));
            rowUpdateChange.put("kfxse", ColumnValue.fromDouble(data.getKfxse()));
            rowUpdateChange.put("xdzhddl", ColumnValue.fromDouble(data.getXdzhddl()));
            rowUpdateChange.put("xdzhrs", ColumnValue.fromDouble(data.getXdzhrs()));
            rowUpdateChange.put("xdzhlv", ColumnValue.fromDouble(data.getXdzhlv()));
            batchWriteRowRequest.addRowChange(rowUpdateChange);
        }
        if (batchWriteRowRequest.getRowsCount() == 0) {
            return;
        }
        int maxRetryTime = 0;
        BatchWriteRowRequest retryRequest = null;
        while (maxRetryTime < 10) {
            BatchWriteRowResponse batchWriteRowResponse = clientTest.batchWriteRow(retryRequest == null ? batchWriteRowRequest : retryRequest);
            if (batchWriteRowResponse.isAllSucceed()) {
                break;
            }

            retryRequest = batchWriteRowRequest.createRequestForRetry(batchWriteRowResponse.getFailedRows());
            maxRetryTime++;
        }
    }


    private void write5(List<CustomerReceptionData> list) {
        BatchWriteRowRequest batchWriteRowRequest = new BatchWriteRowRequest();

        for (CustomerReceptionData data : list) {
            if (StringUtils.isBlank(data.getWwzh())) {
                continue;
            }
            PrimaryKeyBuilder pk1Builder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
            pk1Builder.addPrimaryKeyColumn("yhid_qd_userid", PrimaryKeyValue.fromString(data.getYhid() + "_" + data.getQd() + "_" + data.getUserid()));
            if (data.getRqlx().equals(RqlxConst.RECENT7) || data.getRqlx().equals(RqlxConst.RECENT30)) {
                pk1Builder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(data.getRqlx()));
            } else {
                pk1Builder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(data.getRqlx() + "_" + data.getRq()));
            }
            pk1Builder.addPrimaryKeyColumn("kf", PrimaryKeyValue.fromString(data.getWwzh()));

            RowUpdateChange rowUpdateChange = new RowUpdateChange(TABLE_NAME, pk1Builder.build());
            rowUpdateChange.put("yhid", ColumnValue.fromLong(data.getYhid()));
            rowUpdateChange.put("qd", ColumnValue.fromString(data.getQd()));
            rowUpdateChange.put("userid", ColumnValue.fromString(data.getUserid()));
            if (StringUtils.isNotBlank(data.getWwzhmc())) {
                rowUpdateChange.put("kfnc", ColumnValue.fromString(data.getWwzhmc()));
            }
            rowUpdateChange.put("rqlx", ColumnValue.fromString(data.getRqlx()));
            rowUpdateChange.put("rq", ColumnValue.fromString(data.getRq()));
            rowUpdateChange.put("zxkfrc", ColumnValue.fromLong(data.getZxkfrc()));
            rowUpdateChange.put("xdrs", ColumnValue.fromLong(data.getXdrs()));
            rowUpdateChange.put("kffwf", ColumnValue.fromDouble(data.getKffwf()));
            rowUpdateChange.put("zzctrs", ColumnValue.fromLong(data.getZzctrs()));
            rowUpdateChange.put("qtxse", ColumnValue.fromDouble(data.getQtxse()));
            rowUpdateChange.put("xyrghfzxrs", ColumnValue.fromLong(data.getXyrghfzxrs()));
            rowUpdateChange.put("xdzhlv", ColumnValue.fromDouble(data.getXdzhlv()));
            rowUpdateChange.put("kfxse", ColumnValue.fromDouble(data.getKfxse()));
            rowUpdateChange.put("rgjdrs", ColumnValue.fromLong(data.getRgjdrs()));
            rowUpdateChange.put("smwhfrs", ColumnValue.fromLong(data.getSmwhfrs()));
            rowUpdateChange.put("smrghfl", ColumnValue.fromDouble(data.getSmrghfl()));
            rowUpdateChange.put("sssmydl", ColumnValue.fromDouble(data.getSssmydl()));
            rowUpdateChange.put("pjrgxysc", ColumnValue.fromDouble(data.getPjrgxysc()));
            rowUpdateChange.put("pfxsdds", ColumnValue.fromLong(data.getPfxsdds()));
            rowUpdateChange.put("jftks", ColumnValue.fromLong(data.getJftks()));
            rowUpdateChange.put("tss", ColumnValue.fromLong(data.getTss()));

            batchWriteRowRequest.addRowChange(rowUpdateChange);
        }

        if (batchWriteRowRequest.getRowsCount() == 0) {
            return;
        }

        int maxRetryTime = 0;
        BatchWriteRowRequest retryRequest = null;
        while (maxRetryTime < 10) {
            BatchWriteRowResponse batchWriteRowResponse = client.batchWriteRow(retryRequest == null ? batchWriteRowRequest : retryRequest);
            if (batchWriteRowResponse.isAllSucceed()) {
                break;
            }

            retryRequest = batchWriteRowRequest.createRequestForRetry(batchWriteRowResponse.getFailedRows());
            maxRetryTime++;
        }
    }

    private void writeTest5(List<CustomerReceptionData> list) {
        BatchWriteRowRequest batchWriteRowRequest = new BatchWriteRowRequest();

        for (CustomerReceptionData data : list) {
            if (StringUtils.isBlank(data.getWwzh())) {
                continue;
            }
            PrimaryKeyBuilder pk1Builder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
            pk1Builder.addPrimaryKeyColumn("yhid_qd_userid", PrimaryKeyValue.fromString(data.getYhid() + "_" + data.getQd() + "_" + data.getUserid()));
            if (data.getRqlx().equals(RqlxConst.RECENT7) || data.getRqlx().equals(RqlxConst.RECENT30)) {
                pk1Builder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(data.getRqlx()));
            } else {
                pk1Builder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(data.getRqlx() + "_" + data.getRq()));
            }
            pk1Builder.addPrimaryKeyColumn("kf", PrimaryKeyValue.fromString(data.getWwzh()));

            RowUpdateChange rowUpdateChange = new RowUpdateChange(TABLE_NAME, pk1Builder.build());
            rowUpdateChange.put("yhid", ColumnValue.fromLong(data.getYhid()));
            rowUpdateChange.put("qd", ColumnValue.fromString(data.getQd()));
            rowUpdateChange.put("userid", ColumnValue.fromString(data.getUserid()));
            if (StringUtils.isNotBlank(data.getWwzhmc())) {
                rowUpdateChange.put("kfnc", ColumnValue.fromString(data.getWwzhmc()));
            }
            rowUpdateChange.put("rqlx", ColumnValue.fromString(data.getRqlx()));
            rowUpdateChange.put("rq", ColumnValue.fromString(data.getRq()));
            rowUpdateChange.put("zxkfrc", ColumnValue.fromLong(data.getZxkfrc()));
            rowUpdateChange.put("xdrs", ColumnValue.fromLong(data.getXdrs()));
            rowUpdateChange.put("kffwf", ColumnValue.fromDouble(data.getKffwf()));
            rowUpdateChange.put("zzctrs", ColumnValue.fromLong(data.getZzctrs()));
            rowUpdateChange.put("qtxse", ColumnValue.fromDouble(data.getQtxse()));
            rowUpdateChange.put("xyrghfzxrs", ColumnValue.fromLong(data.getXyrghfzxrs()));
            rowUpdateChange.put("xdzhlv", ColumnValue.fromDouble(data.getXdzhlv()));
            rowUpdateChange.put("kfxse", ColumnValue.fromDouble(data.getKfxse()));
            rowUpdateChange.put("rgjdrs", ColumnValue.fromLong(data.getRgjdrs()));
            rowUpdateChange.put("smwhfrs", ColumnValue.fromLong(data.getSmwhfrs()));
            rowUpdateChange.put("smrghfl", ColumnValue.fromDouble(data.getSmrghfl()));
            rowUpdateChange.put("sssmydl", ColumnValue.fromDouble(data.getSssmydl()));
            rowUpdateChange.put("pjrgxysc", ColumnValue.fromDouble(data.getPjrgxysc()));
            rowUpdateChange.put("pfxsdds", ColumnValue.fromLong(data.getPfxsdds()));
            rowUpdateChange.put("jftks", ColumnValue.fromLong(data.getJftks()));
            rowUpdateChange.put("tss", ColumnValue.fromLong(data.getTss()));

            batchWriteRowRequest.addRowChange(rowUpdateChange);
        }

        if (batchWriteRowRequest.getRowsCount() == 0) {
            return;
        }

        int maxRetryTime = 0;
        BatchWriteRowRequest retryRequest = null;
        while (maxRetryTime < 10) {
            BatchWriteRowResponse batchWriteRowResponse = clientTest.batchWriteRow(retryRequest == null ? batchWriteRowRequest : retryRequest);
            if (batchWriteRowResponse.isAllSucceed()) {
                break;
            }

            retryRequest = batchWriteRowRequest.createRequestForRetry(batchWriteRowResponse.getFailedRows());
            maxRetryTime++;
        }
    }


    private void write6(List<CustomerDutyData> list) {
        BatchWriteRowRequest batchWriteRowRequest = new BatchWriteRowRequest();

        for (CustomerDutyData data : list) {
            if (StringUtils.isBlank(data.getWwzh())) {
                continue;
            }
            PrimaryKeyBuilder pk1Builder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
            pk1Builder.addPrimaryKeyColumn("yhid_qd_userid", PrimaryKeyValue.fromString(data.getYhid() + "_" + data.getQd() + "_" + data.getUserid()));
            if (data.getRqlx().equals(RqlxConst.RECENT7) || data.getRqlx().equals(RqlxConst.RECENT30)) {
                pk1Builder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(data.getRqlx()));
            } else {
                pk1Builder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(data.getRqlx() + "_" + data.getRq()));
            }
            pk1Builder.addPrimaryKeyColumn("kf", PrimaryKeyValue.fromString(data.getWwzh()));

            RowUpdateChange rowUpdateChange = new RowUpdateChange(TABLE_NAME, pk1Builder.build());
            rowUpdateChange.put("yhid", ColumnValue.fromLong(data.getYhid()));
            rowUpdateChange.put("qd", ColumnValue.fromString(data.getQd()));
            rowUpdateChange.put("userid", ColumnValue.fromString(data.getUserid()));
            if (StringUtils.isNotBlank(data.getWwzhmc())) {
                rowUpdateChange.put("kfnc", ColumnValue.fromString(data.getWwzhmc()));
            }
            rowUpdateChange.put("rqlx", ColumnValue.fromString(data.getRqlx()));
            rowUpdateChange.put("rq", ColumnValue.fromString(data.getRq()));
            rowUpdateChange.put("zxzsc", ColumnValue.fromLong(data.getZxzsc()));

            batchWriteRowRequest.addRowChange(rowUpdateChange);
        }

        if (batchWriteRowRequest.getRowsCount() == 0) {
            return;
        }

        int maxRetryTime = 0;
        BatchWriteRowRequest retryRequest = null;
        while (maxRetryTime < 10) {
            BatchWriteRowResponse batchWriteRowResponse = client.batchWriteRow(retryRequest == null ? batchWriteRowRequest : retryRequest);
            if (batchWriteRowResponse.isAllSucceed()) {
                break;
            }

            retryRequest = batchWriteRowRequest.createRequestForRetry(batchWriteRowResponse.getFailedRows());
            maxRetryTime++;
        }
    }


    private void write6Test(List<CustomerDutyData> list) {
        BatchWriteRowRequest batchWriteRowRequest = new BatchWriteRowRequest();

        for (CustomerDutyData data : list) {
            if (StringUtils.isBlank(data.getWwzh())) {
                continue;
            }
            PrimaryKeyBuilder pk1Builder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
            pk1Builder.addPrimaryKeyColumn("yhid_qd_userid", PrimaryKeyValue.fromString(data.getYhid() + "_" + data.getQd() + "_" + data.getUserid()));
            if (data.getRqlx().equals(RqlxConst.RECENT7) || data.getRqlx().equals(RqlxConst.RECENT30)) {
                pk1Builder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(data.getRqlx()));
            } else {
                pk1Builder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(data.getRqlx() + "_" + data.getRq()));
            }
            pk1Builder.addPrimaryKeyColumn("kf", PrimaryKeyValue.fromString(data.getWwzh()));

            RowUpdateChange rowUpdateChange = new RowUpdateChange(TABLE_NAME, pk1Builder.build());
            rowUpdateChange.put("yhid", ColumnValue.fromLong(data.getYhid()));
            rowUpdateChange.put("qd", ColumnValue.fromString(data.getQd()));
            rowUpdateChange.put("userid", ColumnValue.fromString(data.getUserid()));
            if (StringUtils.isNotBlank(data.getWwzhmc())) {
                rowUpdateChange.put("kfnc", ColumnValue.fromString(data.getWwzhmc()));
            }
            rowUpdateChange.put("rqlx", ColumnValue.fromString(data.getRqlx()));
            rowUpdateChange.put("rq", ColumnValue.fromString(data.getRq()));
            rowUpdateChange.put("zxzsc", ColumnValue.fromLong(data.getZxzsc()));

            batchWriteRowRequest.addRowChange(rowUpdateChange);
        }

        if (batchWriteRowRequest.getRowsCount() == 0) {
            return;
        }

        int maxRetryTime = 0;
        BatchWriteRowRequest retryRequest = null;
        while (maxRetryTime < 10) {
            BatchWriteRowResponse batchWriteRowResponse = clientTest.batchWriteRow(retryRequest == null ? batchWriteRowRequest : retryRequest);
            if (batchWriteRowResponse.isAllSucceed()) {
                break;
            }

            retryRequest = batchWriteRowRequest.createRequestForRetry(batchWriteRowResponse.getFailedRows());
            maxRetryTime++;
        }
    }

    public void delData() {
        //根据userid删除recent
        RangeRowQueryCriteria rangeRowQueryCriteria = new RangeRowQueryCriteria(TABLE_NAME);

        //设置起始主键。
        PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
        primaryKeyBuilder.addPrimaryKeyColumn("yhid_qd_userid", PrimaryKeyValue.INF_MIN);
        primaryKeyBuilder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.INF_MIN);
        primaryKeyBuilder.addPrimaryKeyColumn("kf", PrimaryKeyValue.INF_MIN);
        rangeRowQueryCriteria.setInclusiveStartPrimaryKey(primaryKeyBuilder.build());

        //设置结束主键。
        primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
        primaryKeyBuilder.addPrimaryKeyColumn("yhid_qd_userid", PrimaryKeyValue.INF_MAX);
        primaryKeyBuilder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.INF_MAX);
        primaryKeyBuilder.addPrimaryKeyColumn("kf", PrimaryKeyValue.INF_MAX);

        rangeRowQueryCriteria.setExclusiveEndPrimaryKey(primaryKeyBuilder.build());
        rangeRowQueryCriteria.setMaxVersions(1);

        while (true) {
            GetRangeResponse getRangeResponse = client.getRange(new GetRangeRequest(rangeRowQueryCriteria));
            delRow(getRangeResponse.getRows());
            //如果NextStartPrimaryKey不为null，则继续读取。
            if (getRangeResponse.getNextStartPrimaryKey() != null) {
                rangeRowQueryCriteria.setInclusiveStartPrimaryKey(getRangeResponse.getNextStartPrimaryKey());
            } else {
                break;
            }
        }
    }

    public void delRow(List<Row> rows) {
        List<List<Row>> partition = Lists.partition(rows, 200);
        for (List<Row> rowList : partition) {
            BatchWriteRowRequest batchWriteRowRequest = new BatchWriteRowRequest();

            for (Row row : rowList) {
                PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();

                for (PrimaryKeyColumn primaryKeyColumn : row.getPrimaryKey().getPrimaryKeyColumns()) {
                    primaryKeyBuilder.addPrimaryKeyColumn(primaryKeyColumn.getName(), primaryKeyColumn.getValue());
                }

                RowDeleteChange rowDeleteChange = new RowDeleteChange(TABLE_NAME, primaryKeyBuilder.build());
                batchWriteRowRequest.addRowChange(rowDeleteChange);
            }

            BatchWriteRowResponse response = client.batchWriteRow(batchWriteRowRequest);
            int cscs = 0;
            while (cscs < 3 && !response.isAllSucceed()) {
                BatchWriteRowRequest retryRequest = batchWriteRowRequest.createRequestForRetry(response.getFailedRows());
                response = client.batchWriteRow(retryRequest);
                cscs++;
            }
        }
    }

    public Map<String, String> getDatas(int yhid, String qd, String userid) {
        RangeRowQueryCriteria rangeRowQueryCriteria = new RangeRowQueryCriteria("bi_performance_account_list");

        Map<String, String> map = new HashMap<>();

        //设置起始主键。
        PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
        primaryKeyBuilder.addPrimaryKeyColumn("yhid_qd_userid", PrimaryKeyValue.fromString(yhid + "_" + qd + "_" + userid));
        primaryKeyBuilder.addPrimaryKeyColumn("kf", PrimaryKeyValue.INF_MIN);
        rangeRowQueryCriteria.setInclusiveStartPrimaryKey(primaryKeyBuilder.build());

        //设置结束主键。
        primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
        primaryKeyBuilder.addPrimaryKeyColumn("yhid_qd_userid", PrimaryKeyValue.fromString(yhid + "_" + qd + "_" + userid));
        primaryKeyBuilder.addPrimaryKeyColumn("kf", PrimaryKeyValue.INF_MAX);

        rangeRowQueryCriteria.setExclusiveEndPrimaryKey(primaryKeyBuilder.build());
        rangeRowQueryCriteria.setMaxVersions(1);
        while (true) {
            GetRangeResponse getRangeResponse = client.getRange(new GetRangeRequest(rangeRowQueryCriteria));
            for (Row row : getRangeResponse.getRows()) {
                String kf = row.getPrimaryKey().getPrimaryKeyColumn("kf").getValue().asString();
                String kfnc = row.getLatestColumn("kfnc").getValue().asString();
                if (StringUtils.isNotBlank(kfnc)) {
                    map.put(kf, kfnc);
                }
            }
            //如果NextStartPrimaryKey不为null，则继续读取。
            if (getRangeResponse.getNextStartPrimaryKey() != null) {
                rangeRowQueryCriteria.setInclusiveStartPrimaryKey(getRangeResponse.getNextStartPrimaryKey());
            } else {
                break;
            }
        }
        return map;
    }


    private void write7(List<ReaData> list) {
        BatchWriteRowRequest batchWriteRowRequest = new BatchWriteRowRequest();

        for (ReaData data : list) {
            if (StringUtils.isBlank(data.getWwzh())) {
                continue;
            }
            PrimaryKeyBuilder pk1Builder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
            pk1Builder.addPrimaryKeyColumn("yhid_qd_userid", PrimaryKeyValue.fromString(data.getYhid() + "_" + data.getQd() + "_" + data.getUserid()));
            if (data.getRqlx().equals(RqlxConst.RECENT7) || data.getRqlx().equals(RqlxConst.RECENT30)) {
                pk1Builder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(data.getRqlx()));
            } else {
                pk1Builder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(data.getRqlx() + "_" + data.getRq()));
            }
            pk1Builder.addPrimaryKeyColumn("kf", PrimaryKeyValue.fromString(data.getWwzh()));
            RowUpdateChange rowUpdateChange = new RowUpdateChange(TABLE_NAME, pk1Builder.build());
            rowUpdateChange.put("yhid", ColumnValue.fromLong(data.getYhid()));
            rowUpdateChange.put("qd", ColumnValue.fromString(data.getQd()));
            rowUpdateChange.put("userid", ColumnValue.fromString(data.getUserid()));
            if (StringUtils.isNotBlank(data.getWwzhmc())) {
                rowUpdateChange.put("kfnc", ColumnValue.fromString(data.getWwzhmc()));
            }
            rowUpdateChange.put("rqlx", ColumnValue.fromString(data.getRqlx()));
            rowUpdateChange.put("rq", ColumnValue.fromString(data.getRq()));
            rowUpdateChange.put("sdpjs", ColumnValue.fromLong(data.getSdpjs()));
            rowUpdateChange.put("hmy", ColumnValue.fromLong(data.getHmy()));
            rowUpdateChange.put("my", ColumnValue.fromLong(data.getMy()));
            rowUpdateChange.put("yb", ColumnValue.fromLong(data.getYb()));
            rowUpdateChange.put("bm", ColumnValue.fromLong(data.getBm()));
            rowUpdateChange.put("hbm", ColumnValue.fromLong(data.getHbm()));
            rowUpdateChange.put("bm2", ColumnValue.fromLong(data.getBm2()));
            batchWriteRowRequest.addRowChange(rowUpdateChange);
        }
        if (batchWriteRowRequest.getRowsCount() == 0) {
            return;
        }
        int maxRetryTime = 0;
        BatchWriteRowRequest retryRequest = null;
        while (maxRetryTime < 10) {
            BatchWriteRowResponse batchWriteRowResponse = client.batchWriteRow(retryRequest == null ? batchWriteRowRequest : retryRequest);
            if (batchWriteRowResponse.isAllSucceed()) {
                break;
            }

            retryRequest = batchWriteRowRequest.createRequestForRetry(batchWriteRowResponse.getFailedRows());
            maxRetryTime++;
        }
    }


    private void write7Test(List<ReaData> list) {
        BatchWriteRowRequest batchWriteRowRequest = new BatchWriteRowRequest();

        for (ReaData data : list) {
            if (StringUtils.isBlank(data.getWwzh())) {
                continue;
            }
            PrimaryKeyBuilder pk1Builder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
            pk1Builder.addPrimaryKeyColumn("yhid_qd_userid", PrimaryKeyValue.fromString(data.getYhid() + "_" + data.getQd() + "_" + data.getUserid()));
            if (data.getRqlx().equals(RqlxConst.RECENT7) || data.getRqlx().equals(RqlxConst.RECENT30)) {
                pk1Builder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(data.getRqlx()));
            } else {
                pk1Builder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(data.getRqlx() + "_" + data.getRq()));
            }
            pk1Builder.addPrimaryKeyColumn("kf", PrimaryKeyValue.fromString(data.getWwzh()));

            RowUpdateChange rowUpdateChange = new RowUpdateChange(TABLE_NAME, pk1Builder.build());
            rowUpdateChange.put("yhid", ColumnValue.fromLong(data.getYhid()));
            rowUpdateChange.put("qd", ColumnValue.fromString(data.getQd()));
            rowUpdateChange.put("userid", ColumnValue.fromString(data.getUserid()));
            if (StringUtils.isNotBlank(data.getWwzhmc())) {
                rowUpdateChange.put("kfnc", ColumnValue.fromString(data.getWwzhmc()));
            }
            rowUpdateChange.put("rqlx", ColumnValue.fromString(data.getRqlx()));
            rowUpdateChange.put("rq", ColumnValue.fromString(data.getRq()));
            rowUpdateChange.put("sdpjs", ColumnValue.fromLong(data.getSdpjs()));
            rowUpdateChange.put("hmy", ColumnValue.fromLong(data.getHmy()));
            rowUpdateChange.put("my", ColumnValue.fromLong(data.getMy()));
            rowUpdateChange.put("yb", ColumnValue.fromLong(data.getYb()));
            rowUpdateChange.put("bm", ColumnValue.fromDouble(data.getBm()));
            rowUpdateChange.put("hbm", ColumnValue.fromDouble(data.getHbm()));
            rowUpdateChange.put("bm2", ColumnValue.fromLong(data.getBm2()));
            batchWriteRowRequest.addRowChange(rowUpdateChange);
        }

        if (batchWriteRowRequest.getRowsCount() == 0) {
            return;
        }

        int maxRetryTime = 0;
        BatchWriteRowRequest retryRequest = null;
        while (maxRetryTime < 10) {
            BatchWriteRowResponse batchWriteRowResponse = clientTest.batchWriteRow(retryRequest == null ? batchWriteRowRequest : retryRequest);
            if (batchWriteRowResponse.isAllSucceed()) {
                break;
            }

            retryRequest = batchWriteRowRequest.createRequestForRetry(batchWriteRowResponse.getFailedRows());
            maxRetryTime++;
        }
    }


    private void write8(List<InquiiryOrderData> list) {
        BatchWriteRowRequest batchWriteRowRequest = new BatchWriteRowRequest();

        for (InquiiryOrderData data : list) {
            if (StringUtils.isBlank(data.getWwzh())) {
                continue;
            }
            PrimaryKeyBuilder pk1Builder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
            pk1Builder.addPrimaryKeyColumn("yhid_qd_userid", PrimaryKeyValue.fromString(data.getYhid() + "_" + data.getQd() + "_" + data.getUserid()));
            if (data.getRqlx().equals(RqlxConst.RECENT7) || data.getRqlx().equals(RqlxConst.RECENT30)) {
                pk1Builder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(data.getRqlx()));
            } else {
                pk1Builder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(data.getRqlx() + "_" + data.getRq()));
            }
            pk1Builder.addPrimaryKeyColumn("kf", PrimaryKeyValue.fromString(data.getWwzh()));
            RowUpdateChange rowUpdateChange = new RowUpdateChange(TABLE_NAME, pk1Builder.build());
            rowUpdateChange.put("yhid", ColumnValue.fromLong(data.getYhid()));
            rowUpdateChange.put("qd", ColumnValue.fromString(data.getQd()));
            rowUpdateChange.put("userid", ColumnValue.fromString(data.getUserid()));
            if (StringUtils.isNotBlank(data.getWwzhmc())) {
                rowUpdateChange.put("kfnc", ColumnValue.fromString(data.getWwzhmc()));
            }
            rowUpdateChange.put("rqlx", ColumnValue.fromString(data.getRqlx()));
            rowUpdateChange.put("rq", ColumnValue.fromString(data.getRq()));
            rowUpdateChange.put("zzxdrs", ColumnValue.fromLong(data.getZzxdrs()));
            batchWriteRowRequest.addRowChange(rowUpdateChange);
        }
        if (batchWriteRowRequest.getRowsCount() == 0) {
            return;
        }
        int maxRetryTime = 0;
        BatchWriteRowRequest retryRequest = null;
        while (maxRetryTime < 10) {
            BatchWriteRowResponse batchWriteRowResponse = client.batchWriteRow(retryRequest == null ? batchWriteRowRequest : retryRequest);
            if (batchWriteRowResponse.isAllSucceed()) {
                break;
            }

            retryRequest = batchWriteRowRequest.createRequestForRetry(batchWriteRowResponse.getFailedRows());
            maxRetryTime++;
        }
    }


    private void write8Test(List<InquiiryOrderData> list) {
        BatchWriteRowRequest batchWriteRowRequest = new BatchWriteRowRequest();

        for (InquiiryOrderData data : list) {
            if (StringUtils.isBlank(data.getWwzh())) {
                continue;
            }
            PrimaryKeyBuilder pk1Builder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
            pk1Builder.addPrimaryKeyColumn("yhid_qd_userid", PrimaryKeyValue.fromString(data.getYhid() + "_" + data.getQd() + "_" + data.getUserid()));
            if (data.getRqlx().equals(RqlxConst.RECENT7) || data.getRqlx().equals(RqlxConst.RECENT30)) {
                pk1Builder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(data.getRqlx()));
            } else {
                pk1Builder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(data.getRqlx() + "_" + data.getRq()));
            }
            pk1Builder.addPrimaryKeyColumn("kf", PrimaryKeyValue.fromString(data.getWwzh()));

            RowUpdateChange rowUpdateChange = new RowUpdateChange(TABLE_NAME, pk1Builder.build());
            rowUpdateChange.put("yhid", ColumnValue.fromLong(data.getYhid()));
            rowUpdateChange.put("qd", ColumnValue.fromString(data.getQd()));
            rowUpdateChange.put("userid", ColumnValue.fromString(data.getUserid()));
            if (StringUtils.isNotBlank(data.getWwzhmc())) {
                rowUpdateChange.put("kfnc", ColumnValue.fromString(data.getWwzhmc()));
            }
            rowUpdateChange.put("rqlx", ColumnValue.fromString(data.getRqlx()));
            rowUpdateChange.put("rq", ColumnValue.fromString(data.getRq()));
            rowUpdateChange.put("zzxdrs", ColumnValue.fromLong(data.getZzxdrs()));
            batchWriteRowRequest.addRowChange(rowUpdateChange);
        }

        if (batchWriteRowRequest.getRowsCount() == 0) {
            return;
        }

        int maxRetryTime = 0;
        BatchWriteRowRequest retryRequest = null;
        while (maxRetryTime < 10) {
            BatchWriteRowResponse batchWriteRowResponse = clientTest.batchWriteRow(retryRequest == null ? batchWriteRowRequest : retryRequest);
            if (batchWriteRowResponse.isAllSucceed()) {
                break;
            }

            retryRequest = batchWriteRowRequest.createRequestForRetry(batchWriteRowResponse.getFailedRows());
            maxRetryTime++;
        }
    }

    private void write9(List<CustomerReceptionData> list) {
        BatchWriteRowRequest batchWriteRowRequest = new BatchWriteRowRequest();

        for (CustomerReceptionData data : list) {
            if (StringUtils.isBlank(data.getWwzh())) {
                continue;
            }
            PrimaryKeyBuilder pk1Builder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
            pk1Builder.addPrimaryKeyColumn("yhid_qd_userid", PrimaryKeyValue.fromString(data.getYhid() + "_" + data.getQd() + "_" + data.getUserid()));
            if (data.getRqlx().equals(RqlxConst.RECENT7) || data.getRqlx().equals(RqlxConst.RECENT30)) {
                pk1Builder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(data.getRqlx()));
            } else {
                pk1Builder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(data.getRqlx() + "_" + data.getRq()));
            }
            pk1Builder.addPrimaryKeyColumn("kf", PrimaryKeyValue.fromString(data.getWwzh()));
            RowUpdateChange rowUpdateChange = new RowUpdateChange(TABLE_NAME, pk1Builder.build());
            rowUpdateChange.put("yhid", ColumnValue.fromLong(data.getYhid()));
            rowUpdateChange.put("qd", ColumnValue.fromString(data.getQd()));
            rowUpdateChange.put("userid", ColumnValue.fromString(data.getUserid()));
            if (StringUtils.isNotBlank(data.getWwzhmc())) {
                rowUpdateChange.put("kfnc", ColumnValue.fromString(data.getWwzhmc()));
            }
            rowUpdateChange.put("rqlx", ColumnValue.fromString(data.getRqlx()));
            rowUpdateChange.put("rq", ColumnValue.fromString(data.getRq()));
            rowUpdateChange.put("zxsc", ColumnValue.fromDouble(data.getZxsc()));
            rowUpdateChange.put("mlsc", ColumnValue.fromDouble(data.getMlsc()));
            rowUpdateChange.put("hhl", ColumnValue.fromDouble(data.getHhl()));
            rowUpdateChange.put("khhfl", ColumnValue.fromDouble(data.getKhhfl()));
            rowUpdateChange.put("smhfl", ColumnValue.fromDouble(data.getSmhfl()));
            rowUpdateChange.put("fortyfivesxl", ColumnValue.fromDouble(data.getFortyfivesxl()));
            rowUpdateChange.put("pjs", ColumnValue.fromLong(data.getPjs()));
            rowUpdateChange.put("hppjs", ColumnValue.fromLong(data.getHppjs()));
            rowUpdateChange.put("cppjs", ColumnValue.fromLong(data.getCppjs()));
            rowUpdateChange.put("pjl", ColumnValue.fromDouble(data.getPjl()));
            rowUpdateChange.put("kfcpl", ColumnValue.fromDouble(data.getKfcpl()));
            rowUpdateChange.put("kfhpl", ColumnValue.fromDouble(data.getKfhpl()));
            rowUpdateChange.put("xgzhdds", ColumnValue.fromLong(data.getXgzhdds()));
            rowUpdateChange.put("xgzhddje", ColumnValue.fromDouble(data.getXgzhddje()));
            rowUpdateChange.put("xgzhl", ColumnValue.fromDouble(data.getXgzhl()));
            rowUpdateChange.put("xgzhljezb", ColumnValue.fromDouble(data.getXgzhljezb()));
            batchWriteRowRequest.addRowChange(rowUpdateChange);
        }
        if (batchWriteRowRequest.getRowsCount() == 0) {
            return;
        }
        int maxRetryTime = 0;
        BatchWriteRowRequest retryRequest = null;
        while (maxRetryTime < 10) {
            BatchWriteRowResponse batchWriteRowResponse = client.batchWriteRow(retryRequest == null ? batchWriteRowRequest : retryRequest);
            if (batchWriteRowResponse.isAllSucceed()) {
                break;
            }

            retryRequest = batchWriteRowRequest.createRequestForRetry(batchWriteRowResponse.getFailedRows());
            maxRetryTime++;
        }
    }

    private void writeTest9(List<CustomerReceptionData> list) {
        BatchWriteRowRequest batchWriteRowRequest = new BatchWriteRowRequest();

        for (CustomerReceptionData data : list) {
            if (StringUtils.isBlank(data.getWwzh())) {
                continue;
            }
            PrimaryKeyBuilder pk1Builder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
            pk1Builder.addPrimaryKeyColumn("yhid_qd_userid", PrimaryKeyValue.fromString(data.getYhid() + "_" + data.getQd() + "_" + data.getUserid()));
            if (data.getRqlx().equals(RqlxConst.RECENT7) || data.getRqlx().equals(RqlxConst.RECENT30)) {
                pk1Builder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(data.getRqlx()));
            } else {
                pk1Builder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(data.getRqlx() + "_" + data.getRq()));
            }
            pk1Builder.addPrimaryKeyColumn("kf", PrimaryKeyValue.fromString(data.getWwzh()));
            RowUpdateChange rowUpdateChange = new RowUpdateChange(TABLE_NAME, pk1Builder.build());
            rowUpdateChange.put("yhid", ColumnValue.fromLong(data.getYhid()));
            rowUpdateChange.put("qd", ColumnValue.fromString(data.getQd()));
            rowUpdateChange.put("userid", ColumnValue.fromString(data.getUserid()));
            if (StringUtils.isNotBlank(data.getWwzhmc())) {
                rowUpdateChange.put("kfnc", ColumnValue.fromString(data.getWwzhmc()));
            }
            rowUpdateChange.put("rqlx", ColumnValue.fromString(data.getRqlx()));
            rowUpdateChange.put("rq", ColumnValue.fromString(data.getRq()));
            rowUpdateChange.put("zxsc", ColumnValue.fromDouble(data.getZxsc()));
            rowUpdateChange.put("mlsc", ColumnValue.fromDouble(data.getMlsc()));
            rowUpdateChange.put("hhl", ColumnValue.fromDouble(data.getHhl()));
            rowUpdateChange.put("khhfl", ColumnValue.fromDouble(data.getKhhfl()));
            rowUpdateChange.put("smhfl", ColumnValue.fromDouble(data.getSmhfl()));
            rowUpdateChange.put("fortyfivesxl", ColumnValue.fromDouble(data.getFortyfivesxl()));
            rowUpdateChange.put("pjs", ColumnValue.fromLong(data.getPjs()));
            rowUpdateChange.put("hppjs", ColumnValue.fromLong(data.getHppjs()));
            rowUpdateChange.put("cppjs", ColumnValue.fromLong(data.getCppjs()));
            rowUpdateChange.put("pjl", ColumnValue.fromDouble(data.getPjl()));
            rowUpdateChange.put("kfcpl", ColumnValue.fromDouble(data.getKfcpl()));
            rowUpdateChange.put("kfhpl", ColumnValue.fromDouble(data.getKfhpl()));
            rowUpdateChange.put("xgzhdds", ColumnValue.fromLong(data.getXgzhdds()));
            rowUpdateChange.put("xgzhddje", ColumnValue.fromDouble(data.getXgzhddje()));
            rowUpdateChange.put("xgzhl", ColumnValue.fromDouble(data.getXgzhl()));
            rowUpdateChange.put("xgzhljezb", ColumnValue.fromDouble(data.getXgzhljezb()));
            batchWriteRowRequest.addRowChange(rowUpdateChange);
            batchWriteRowRequest.addRowChange(rowUpdateChange);
        }
        if (batchWriteRowRequest.getRowsCount() == 0) {
            return;
        }
        int maxRetryTime = 0;
        BatchWriteRowRequest retryRequest = null;
        while (maxRetryTime < 10) {
            BatchWriteRowResponse batchWriteRowResponse = clientTest.batchWriteRow(retryRequest == null ? batchWriteRowRequest : retryRequest);
            if (batchWriteRowResponse.isAllSucceed()) {
                break;
            }

            retryRequest = batchWriteRowRequest.createRequestForRetry(batchWriteRowResponse.getFailedRows());
            maxRetryTime++;
        }
    }


    private void writeTest10(List<CustomerRefundData> list) {
        BatchWriteRowRequest batchWriteRowRequest = new BatchWriteRowRequest();

        for (CustomerRefundData data : list) {
            if (StringUtils.isBlank(data.getWwzh())) {
                continue;
            }
            PrimaryKeyBuilder pk1Builder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
            pk1Builder.addPrimaryKeyColumn("yhid_qd_userid", PrimaryKeyValue.fromString(data.getYhid() + "_" + data.getQd() + "_" + data.getUserid()));
            if (data.getRqlx().equals(RqlxConst.RECENT7) || data.getRqlx().equals(RqlxConst.RECENT30)) {
                pk1Builder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(data.getRqlx()));
            } else {
                pk1Builder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(data.getRqlx() + "_" + data.getRq()));
            }
            pk1Builder.addPrimaryKeyColumn("kf", PrimaryKeyValue.fromString(data.getWwzh()));
            RowUpdateChange rowUpdateChange = new RowUpdateChange(TABLE_NAME, pk1Builder.build());
            rowUpdateChange.put("yhid", ColumnValue.fromLong(data.getYhid()));
            rowUpdateChange.put("qd", ColumnValue.fromString(data.getQd()));
            rowUpdateChange.put("userid", ColumnValue.fromString(data.getUserid()));
            if (StringUtils.isNotBlank(data.getWwzhmc())) {
                rowUpdateChange.put("kfnc", ColumnValue.fromString(data.getWwzhmc()));
            }
            rowUpdateChange.put("rqlx", ColumnValue.fromString(data.getRqlx()));
            rowUpdateChange.put("rq", ColumnValue.fromString(data.getRq()));

            rowUpdateChange.put("wjtkbs", ColumnValue.fromLong(data.getWjtkbs()));
            rowUpdateChange.put("tkpjwjsc", ColumnValue.fromDouble(data.getTkpjwjsc()));
            rowUpdateChange.put("tkczzcs", ColumnValue.fromLong(data.getTkczzcs()));
            rowUpdateChange.put("tysqcs", ColumnValue.fromLong(data.getTysqcs()));
            rowUpdateChange.put("jjsqcs", ColumnValue.fromLong(data.getJjsqcs()));
            rowUpdateChange.put("tksqpjclsc", ColumnValue.fromDouble(data.getTksqpjclsc()));
            rowUpdateChange.put("qrshcs", ColumnValue.fromLong(data.getQrshcs()));
            rowUpdateChange.put("jjqrshcs", ColumnValue.fromLong(data.getJjqrshcs()));
            rowUpdateChange.put("qrshpjclsc", ColumnValue.fromDouble(data.getQrshpjclsc()));
            rowUpdateChange.put("jffql", ColumnValue.fromLong(data.getJffql()));
            rowUpdateChange.put("jffqlv", ColumnValue.fromDouble(data.getJffql()));
            rowUpdateChange.put("xerjrl", ColumnValue.fromLong(data.getXerjrl()));
            rowUpdateChange.put("xerjrlv", ColumnValue.fromDouble(data.getXerjrlv()));
            rowUpdateChange.put("jfpzl", ColumnValue.fromLong(data.getJfpzl()));
            rowUpdateChange.put("jfpzlv", ColumnValue.fromDouble(data.getJfpzlv()));

            batchWriteRowRequest.addRowChange(rowUpdateChange);
            batchWriteRowRequest.addRowChange(rowUpdateChange);
        }
        if (batchWriteRowRequest.getRowsCount() == 0) {
            return;
        }
        int maxRetryTime = 0;
        BatchWriteRowRequest retryRequest = null;
        while (maxRetryTime < 10) {
            BatchWriteRowResponse batchWriteRowResponse = clientTest.batchWriteRow(retryRequest == null ? batchWriteRowRequest : retryRequest);
            if (batchWriteRowResponse.isAllSucceed()) {
                break;
            }

            retryRequest = batchWriteRowRequest.createRequestForRetry(batchWriteRowResponse.getFailedRows());
            maxRetryTime++;
        }
    }

    private void write10(List<CustomerRefundData> list) {
        BatchWriteRowRequest batchWriteRowRequest = new BatchWriteRowRequest();

        for (CustomerRefundData data : list) {
            if (StringUtils.isBlank(data.getWwzh())) {
                continue;
            }
            PrimaryKeyBuilder pk1Builder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
            pk1Builder.addPrimaryKeyColumn("yhid_qd_userid", PrimaryKeyValue.fromString(data.getYhid() + "_" + data.getQd() + "_" + data.getUserid()));
            if (data.getRqlx().equals(RqlxConst.RECENT7) || data.getRqlx().equals(RqlxConst.RECENT30)) {
                pk1Builder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(data.getRqlx()));
            } else {
                pk1Builder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(data.getRqlx() + "_" + data.getRq()));
            }
            pk1Builder.addPrimaryKeyColumn("kf", PrimaryKeyValue.fromString(data.getWwzh()));
            RowUpdateChange rowUpdateChange = new RowUpdateChange(TABLE_NAME, pk1Builder.build());
            rowUpdateChange.put("yhid", ColumnValue.fromLong(data.getYhid()));
            rowUpdateChange.put("qd", ColumnValue.fromString(data.getQd()));
            rowUpdateChange.put("userid", ColumnValue.fromString(data.getUserid()));
            if (StringUtils.isNotBlank(data.getWwzhmc())) {
                rowUpdateChange.put("kfnc", ColumnValue.fromString(data.getWwzhmc()));
            }
            rowUpdateChange.put("rqlx", ColumnValue.fromString(data.getRqlx()));
            rowUpdateChange.put("rq", ColumnValue.fromString(data.getRq()));

            rowUpdateChange.put("wjtkbs", ColumnValue.fromLong(data.getWjtkbs()));
            rowUpdateChange.put("tkpjwjsc", ColumnValue.fromDouble(data.getTkpjwjsc()));
            rowUpdateChange.put("tkczzcs", ColumnValue.fromLong(data.getTkczzcs()));
            rowUpdateChange.put("tysqcs", ColumnValue.fromLong(data.getTysqcs()));
            rowUpdateChange.put("jjsqcs", ColumnValue.fromLong(data.getJjsqcs()));
            rowUpdateChange.put("tksqpjclsc", ColumnValue.fromDouble(data.getTksqpjclsc()));
            rowUpdateChange.put("qrshcs", ColumnValue.fromLong(data.getQrshcs()));
            rowUpdateChange.put("jjqrshcs", ColumnValue.fromLong(data.getJjqrshcs()));
            rowUpdateChange.put("qrshpjclsc", ColumnValue.fromDouble(data.getQrshpjclsc()));
            rowUpdateChange.put("jffql", ColumnValue.fromLong(data.getJffql()));
            rowUpdateChange.put("jffqlv", ColumnValue.fromDouble(data.getJffql()));
            rowUpdateChange.put("xerjrl", ColumnValue.fromLong(data.getXerjrl()));
            rowUpdateChange.put("xerjrlv", ColumnValue.fromDouble(data.getXerjrlv()));
            rowUpdateChange.put("jfpzl", ColumnValue.fromLong(data.getJfpzl()));
            rowUpdateChange.put("jfpzlv", ColumnValue.fromDouble(data.getJfpzlv()));

            batchWriteRowRequest.addRowChange(rowUpdateChange);
            batchWriteRowRequest.addRowChange(rowUpdateChange);
        }
        if (batchWriteRowRequest.getRowsCount() == 0) {
            return;
        }
        int maxRetryTime = 0;
        BatchWriteRowRequest retryRequest = null;
        while (maxRetryTime < 10) {
            BatchWriteRowResponse batchWriteRowResponse = client.batchWriteRow(retryRequest == null ? batchWriteRowRequest : retryRequest);
            if (batchWriteRowResponse.isAllSucceed()) {
                break;
            }

            retryRequest = batchWriteRowRequest.createRequestForRetry(batchWriteRowResponse.getFailedRows());
            maxRetryTime++;
        }
    }


    private void write11(List<JdJmKfReceptionData> list) {
        BatchWriteRowRequest batchWriteRowRequest = new BatchWriteRowRequest();

        for (JdJmKfReceptionData data : list) {
            if (StringUtils.isBlank(data.getWwzh())) {
                continue;
            }
            PrimaryKeyBuilder pk1Builder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
            pk1Builder.addPrimaryKeyColumn("yhid_qd_userid", PrimaryKeyValue.fromString(data.getYhid() + "_" + data.getQd() + "_" + data.getUserid()));
            if (data.getRqlx().equals(RqlxConst.RECENT7) || data.getRqlx().equals(RqlxConst.RECENT30)) {
                pk1Builder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(data.getRqlx()));
            } else {
                pk1Builder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(data.getRqlx() + "_" + data.getRq()));
            }
            pk1Builder.addPrimaryKeyColumn("kf", PrimaryKeyValue.fromString(data.getWwzh()));
            RowUpdateChange rowUpdateChange = new RowUpdateChange(TABLE_NAME, pk1Builder.build());
            rowUpdateChange.put("yhid", ColumnValue.fromLong(data.getYhid()));
            rowUpdateChange.put("qd", ColumnValue.fromString(data.getQd()));
            rowUpdateChange.put("userid", ColumnValue.fromString(data.getUserid()));
            if (StringUtils.isNotBlank(data.getWwzhmc())) {
                rowUpdateChange.put("kfnc", ColumnValue.fromString(data.getWwzhmc()));
            }
            rowUpdateChange.put("rqlx", ColumnValue.fromString(data.getRqlx()));
            rowUpdateChange.put("rq", ColumnValue.fromString(data.getRq()));

            rowUpdateChange.put("fwsc", ColumnValue.fromDouble(data.getFwsc()));
            rowUpdateChange.put("zxunl", ColumnValue.fromLong(data.getZxunl()));
            rowUpdateChange.put("jdl", ColumnValue.fromLong(data.getJdl()));
            rowUpdateChange.put("ydlv", ColumnValue.fromDouble(data.getYdlv()));
            rowUpdateChange.put("sssmydlv", ColumnValue.fromDouble(data.getSssmydlv()));
            rowUpdateChange.put("scxysc", ColumnValue.fromDouble(data.getScxysc()));
            rowUpdateChange.put("pjxysc", ColumnValue.fromDouble(data.getPjxysc()));
            rowUpdateChange.put("pjhhsc", ColumnValue.fromDouble(data.getPjhhsc()));
            rowUpdateChange.put("pjhfxxs", ColumnValue.fromDouble(data.getPjhfxxs()));
            rowUpdateChange.put("wxyl", ColumnValue.fromLong(data.getWxyl()));
            rowUpdateChange.put("sfzrgxylv", ColumnValue.fromDouble(data.getSfzrgxylv()));
            rowUpdateChange.put("kfsfzrgxylv", ColumnValue.fromDouble(data.getKfsfzrgxylv()));
            rowUpdateChange.put("yps", ColumnValue.fromLong(data.getYps()));
            rowUpdateChange.put("yplv", ColumnValue.fromDouble(data.getYplv()));
            rowUpdateChange.put("pjs", ColumnValue.fromLong(data.getPjs()));
            rowUpdateChange.put("hppjs", ColumnValue.fromLong(data.getHppjs()));
            rowUpdateChange.put("kfhpl", ColumnValue.fromDouble(data.getKfhpl()));
            rowUpdateChange.put("cppjs", ColumnValue.fromLong(data.getCppjs()));
            rowUpdateChange.put("kfcpl", ColumnValue.fromDouble(data.getKfcpl()));
            rowUpdateChange.put("yb", ColumnValue.fromLong(data.getYb()));
            rowUpdateChange.put("zxcplv", ColumnValue.fromDouble(data.getZxcplv()));
            rowUpdateChange.put("jjl", ColumnValue.fromLong(data.getJjl()));
            rowUpdateChange.put("wjjl", ColumnValue.fromLong(data.getWjjl()));
            rowUpdateChange.put("jjlv", ColumnValue.fromDouble(data.getJjlv()));
            rowUpdateChange.put("qtlyl", ColumnValue.fromLong(data.getQtlyl()));
            rowUpdateChange.put("lylv", ColumnValue.fromDouble(data.getLylv()));
            rowUpdateChange.put("lyjdl", ColumnValue.fromLong(data.getLyjdl()));
            rowUpdateChange.put("lyfpl", ColumnValue.fromLong(data.getLyfpl()));
            rowUpdateChange.put("lyhflv", ColumnValue.fromDouble(data.getLyhflv()));
            rowUpdateChange.put("lyxylv", ColumnValue.fromDouble(data.getLyxylv()));

            batchWriteRowRequest.addRowChange(rowUpdateChange);
        }
        if (batchWriteRowRequest.getRowsCount() == 0) {
            return;
        }
        int maxRetryTime = 0;
        BatchWriteRowRequest retryRequest = null;
        while (maxRetryTime < 10) {
            BatchWriteRowResponse batchWriteRowResponse = client.batchWriteRow(retryRequest == null ? batchWriteRowRequest : retryRequest);
            if (batchWriteRowResponse.isAllSucceed()) {
                break;
            }

            retryRequest = batchWriteRowRequest.createRequestForRetry(batchWriteRowResponse.getFailedRows());
            maxRetryTime++;
        }
    }

    private void writeTest11(List<JdJmKfReceptionData> list) {
        BatchWriteRowRequest batchWriteRowRequest = new BatchWriteRowRequest();

        for (JdJmKfReceptionData data : list) {
            if (StringUtils.isBlank(data.getWwzh())) {
                continue;
            }
            PrimaryKeyBuilder pk1Builder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
            pk1Builder.addPrimaryKeyColumn("yhid_qd_userid", PrimaryKeyValue.fromString(data.getYhid() + "_" + data.getQd() + "_" + data.getUserid()));
            if (data.getRqlx().equals(RqlxConst.RECENT7) || data.getRqlx().equals(RqlxConst.RECENT30)) {
                pk1Builder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(data.getRqlx()));
            } else {
                pk1Builder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(data.getRqlx() + "_" + data.getRq()));
            }
            pk1Builder.addPrimaryKeyColumn("kf", PrimaryKeyValue.fromString(data.getWwzh()));
            RowUpdateChange rowUpdateChange = new RowUpdateChange(TABLE_NAME, pk1Builder.build());
            rowUpdateChange.put("yhid", ColumnValue.fromLong(data.getYhid()));
            rowUpdateChange.put("qd", ColumnValue.fromString(data.getQd()));
            rowUpdateChange.put("userid", ColumnValue.fromString(data.getUserid()));
            if (StringUtils.isNotBlank(data.getWwzhmc())) {
                rowUpdateChange.put("kfnc", ColumnValue.fromString(data.getWwzhmc()));
            }
            rowUpdateChange.put("rqlx", ColumnValue.fromString(data.getRqlx()));
            rowUpdateChange.put("rq", ColumnValue.fromString(data.getRq()));

            rowUpdateChange.put("fwsc", ColumnValue.fromDouble(data.getFwsc()));
            rowUpdateChange.put("zxunl", ColumnValue.fromLong(data.getZxunl()));
            rowUpdateChange.put("jdl", ColumnValue.fromLong(data.getJdl()));
            rowUpdateChange.put("ydlv", ColumnValue.fromDouble(data.getYdlv()));
            rowUpdateChange.put("sssmydlv", ColumnValue.fromDouble(data.getSssmydlv()));
            rowUpdateChange.put("scxysc", ColumnValue.fromDouble(data.getScxysc()));
            rowUpdateChange.put("pjxysc", ColumnValue.fromDouble(data.getPjxysc()));
            rowUpdateChange.put("pjhhsc", ColumnValue.fromDouble(data.getPjhhsc()));
            rowUpdateChange.put("pjhfxxs", ColumnValue.fromDouble(data.getPjhfxxs()));
            rowUpdateChange.put("wxyl", ColumnValue.fromLong(data.getWxyl()));
            rowUpdateChange.put("sfzrgxylv", ColumnValue.fromDouble(data.getSfzrgxylv()));
            rowUpdateChange.put("kfsfzrgxylv", ColumnValue.fromDouble(data.getKfsfzrgxylv()));
            rowUpdateChange.put("yps", ColumnValue.fromLong(data.getYps()));
            rowUpdateChange.put("yplv", ColumnValue.fromDouble(data.getYplv()));
            rowUpdateChange.put("pjs", ColumnValue.fromLong(data.getPjs()));
            rowUpdateChange.put("hppjs", ColumnValue.fromLong(data.getHppjs()));
            rowUpdateChange.put("kfhpl", ColumnValue.fromDouble(data.getKfhpl()));
            rowUpdateChange.put("cppjs", ColumnValue.fromLong(data.getCppjs()));
            rowUpdateChange.put("kfcpl", ColumnValue.fromDouble(data.getKfcpl()));
            rowUpdateChange.put("yb", ColumnValue.fromLong(data.getYb()));
            rowUpdateChange.put("zxcplv", ColumnValue.fromDouble(data.getZxcplv()));
            rowUpdateChange.put("jjl", ColumnValue.fromLong(data.getJjl()));
            rowUpdateChange.put("wjjl", ColumnValue.fromLong(data.getWjjl()));
            rowUpdateChange.put("jjlv", ColumnValue.fromDouble(data.getJjlv()));
            rowUpdateChange.put("qtlyl", ColumnValue.fromLong(data.getQtlyl()));
            rowUpdateChange.put("lylv", ColumnValue.fromDouble(data.getLylv()));
            rowUpdateChange.put("lyjdl", ColumnValue.fromLong(data.getLyjdl()));
            rowUpdateChange.put("lyfpl", ColumnValue.fromLong(data.getLyfpl()));
            rowUpdateChange.put("lyhflv", ColumnValue.fromDouble(data.getLyhflv()));
            rowUpdateChange.put("lyxylv", ColumnValue.fromDouble(data.getLyxylv()));

            batchWriteRowRequest.addRowChange(rowUpdateChange);
        }
        if (batchWriteRowRequest.getRowsCount() == 0) {
            return;
        }
        int maxRetryTime = 0;
        BatchWriteRowRequest retryRequest = null;
        while (maxRetryTime < 10) {
            BatchWriteRowResponse batchWriteRowResponse = clientTest.batchWriteRow(retryRequest == null ? batchWriteRowRequest : retryRequest);
            if (batchWriteRowResponse.isAllSucceed()) {
                break;
            }

            retryRequest = batchWriteRowRequest.createRequestForRetry(batchWriteRowResponse.getFailedRows());
            maxRetryTime++;
        }
    }


    private void write12(List<JdJmKfMarketingData> list) {
        BatchWriteRowRequest batchWriteRowRequest = new BatchWriteRowRequest();

        for (JdJmKfMarketingData data : list) {
            if (StringUtils.isBlank(data.getWwzh())) {
                continue;
            }
            PrimaryKeyBuilder pk1Builder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
            pk1Builder.addPrimaryKeyColumn("yhid_qd_userid", PrimaryKeyValue.fromString(data.getYhid() + "_" + data.getQd() + "_" + data.getUserid()));
            if (data.getRqlx().equals(RqlxConst.RECENT7) || data.getRqlx().equals(RqlxConst.RECENT30)) {
                pk1Builder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(data.getRqlx()));
            } else {
                pk1Builder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(data.getRqlx() + "_" + data.getRq()));
            }
            pk1Builder.addPrimaryKeyColumn("kf", PrimaryKeyValue.fromString(data.getWwzh()));
            RowUpdateChange rowUpdateChange = new RowUpdateChange(TABLE_NAME, pk1Builder.build());
            rowUpdateChange.put("yhid", ColumnValue.fromLong(data.getYhid()));
            rowUpdateChange.put("qd", ColumnValue.fromString(data.getQd()));
            rowUpdateChange.put("userid", ColumnValue.fromString(data.getUserid()));
            if (StringUtils.isNotBlank(data.getWwzhmc())) {
                rowUpdateChange.put("kfnc", ColumnValue.fromString(data.getWwzhmc()));
            }
            rowUpdateChange.put("rqlx", ColumnValue.fromString(data.getRqlx()));
            rowUpdateChange.put("rq", ColumnValue.fromString(data.getRq()));

            rowUpdateChange.put("sqjdrs", ColumnValue.fromLong(data.getSqjdrs()));
            rowUpdateChange.put("ccxdrs", ColumnValue.fromLong(data.getCcxdrs()));
            rowUpdateChange.put("ccxddds", ColumnValue.fromLong(data.getCcxddds()));
            rowUpdateChange.put("ccxdsps", ColumnValue.fromLong(data.getCcxdsps()));
            rowUpdateChange.put("ccxdspje", ColumnValue.fromDouble(data.getCcxdspje()));
            rowUpdateChange.put("sqzxxdzhlv", ColumnValue.fromDouble(data.getSqzxxdzhlv()));
            rowUpdateChange.put("ccxdkdj", ColumnValue.fromDouble(data.getCcxdkdj()));
            rowUpdateChange.put("ccxdkjs", ColumnValue.fromDouble(data.getCcxdkjs()));
            rowUpdateChange.put("tjrs", ColumnValue.fromLong(data.getTjrs()));
            rowUpdateChange.put("ccxdspjj", ColumnValue.fromDouble(data.getCcxdspjj()));
            rowUpdateChange.put("ccckrs", ColumnValue.fromLong(data.getCcckrs()));
            rowUpdateChange.put("sqzxckzhlv", ColumnValue.fromDouble(data.getSqzxckzhlv()));
            rowUpdateChange.put("ccckspjj", ColumnValue.fromDouble(data.getCcckspjj()));
            rowUpdateChange.put("ccckspje", ColumnValue.fromDouble(data.getCcckspje()));
            rowUpdateChange.put("cccksps", ColumnValue.fromLong(data.getCccksps()));
            rowUpdateChange.put("ccckdds", ColumnValue.fromLong(data.getCcckdds()));
            rowUpdateChange.put("xdckzhlv", ColumnValue.fromDouble(data.getXdckzhlv()));
            rowUpdateChange.put("ccckkjs", ColumnValue.fromDouble(data.getCcckkjs()));
            rowUpdateChange.put("ccckkdj", ColumnValue.fromDouble(data.getCcckkdj()));

            batchWriteRowRequest.addRowChange(rowUpdateChange);
        }
        if (batchWriteRowRequest.getRowsCount() == 0) {
            return;
        }
        int maxRetryTime = 0;
        BatchWriteRowRequest retryRequest = null;
        while (maxRetryTime < 10) {
            BatchWriteRowResponse batchWriteRowResponse = client.batchWriteRow(retryRequest == null ? batchWriteRowRequest : retryRequest);
            if (batchWriteRowResponse.isAllSucceed()) {
                break;
            }

            retryRequest = batchWriteRowRequest.createRequestForRetry(batchWriteRowResponse.getFailedRows());
            maxRetryTime++;
        }
    }

    private void writeTest12(List<JdJmKfMarketingData> list) {
        BatchWriteRowRequest batchWriteRowRequest = new BatchWriteRowRequest();

        for (JdJmKfMarketingData data : list) {
            if (StringUtils.isBlank(data.getWwzh())) {
                continue;
            }
            PrimaryKeyBuilder pk1Builder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
            pk1Builder.addPrimaryKeyColumn("yhid_qd_userid", PrimaryKeyValue.fromString(data.getYhid() + "_" + data.getQd() + "_" + data.getUserid()));
            if (data.getRqlx().equals(RqlxConst.RECENT7) || data.getRqlx().equals(RqlxConst.RECENT30)) {
                pk1Builder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(data.getRqlx()));
            } else {
                pk1Builder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(data.getRqlx() + "_" + data.getRq()));
            }
            pk1Builder.addPrimaryKeyColumn("kf", PrimaryKeyValue.fromString(data.getWwzh()));
            RowUpdateChange rowUpdateChange = new RowUpdateChange(TABLE_NAME, pk1Builder.build());
            rowUpdateChange.put("yhid", ColumnValue.fromLong(data.getYhid()));
            rowUpdateChange.put("qd", ColumnValue.fromString(data.getQd()));
            rowUpdateChange.put("userid", ColumnValue.fromString(data.getUserid()));
            if (StringUtils.isNotBlank(data.getWwzhmc())) {
                rowUpdateChange.put("kfnc", ColumnValue.fromString(data.getWwzhmc()));
            }
            rowUpdateChange.put("rqlx", ColumnValue.fromString(data.getRqlx()));
            rowUpdateChange.put("rq", ColumnValue.fromString(data.getRq()));

            rowUpdateChange.put("sqjdrs", ColumnValue.fromLong(data.getSqjdrs()));
            rowUpdateChange.put("ccxdrs", ColumnValue.fromLong(data.getCcxdrs()));
            rowUpdateChange.put("ccxddds", ColumnValue.fromLong(data.getCcxddds()));
            rowUpdateChange.put("ccxdsps", ColumnValue.fromLong(data.getCcxdsps()));
            rowUpdateChange.put("ccxdspje", ColumnValue.fromDouble(data.getCcxdspje()));
            rowUpdateChange.put("sqzxxdzhlv", ColumnValue.fromDouble(data.getSqzxxdzhlv()));
            rowUpdateChange.put("ccxdkdj", ColumnValue.fromDouble(data.getCcxdkdj()));
            rowUpdateChange.put("ccxdkjs", ColumnValue.fromDouble(data.getCcxdkjs()));
            rowUpdateChange.put("tjrs", ColumnValue.fromLong(data.getTjrs()));
            rowUpdateChange.put("ccxdspjj", ColumnValue.fromDouble(data.getCcxdspjj()));
            rowUpdateChange.put("ccckrs", ColumnValue.fromLong(data.getCcckrs()));
            rowUpdateChange.put("sqzxckzhlv", ColumnValue.fromDouble(data.getSqzxckzhlv()));
            rowUpdateChange.put("ccckspjj", ColumnValue.fromDouble(data.getCcckspjj()));
            rowUpdateChange.put("ccckspje", ColumnValue.fromDouble(data.getCcckspje()));
            rowUpdateChange.put("cccksps", ColumnValue.fromLong(data.getCccksps()));
            rowUpdateChange.put("ccckdds", ColumnValue.fromLong(data.getCcckdds()));
            rowUpdateChange.put("xdckzhlv", ColumnValue.fromDouble(data.getXdckzhlv()));
            rowUpdateChange.put("ccckkjs", ColumnValue.fromDouble(data.getCcckkjs()));
            rowUpdateChange.put("ccckkdj", ColumnValue.fromDouble(data.getCcckkdj()));

            batchWriteRowRequest.addRowChange(rowUpdateChange);
        }
        if (batchWriteRowRequest.getRowsCount() == 0) {
            return;
        }
        int maxRetryTime = 0;
        BatchWriteRowRequest retryRequest = null;
        while (maxRetryTime < 10) {
            BatchWriteRowResponse batchWriteRowResponse = clientTest.batchWriteRow(retryRequest == null ? batchWriteRowRequest : retryRequest);
            if (batchWriteRowResponse.isAllSucceed()) {
                break;
            }

            retryRequest = batchWriteRowRequest.createRequestForRetry(batchWriteRowResponse.getFailedRows());
            maxRetryTime++;
        }
    }


    private void write13(List<JdJmKfAttendanceData> list) {
        BatchWriteRowRequest batchWriteRowRequest = new BatchWriteRowRequest();

        for (JdJmKfAttendanceData data : list) {
            if (StringUtils.isBlank(data.getWwzh())) {
                continue;
            }
            PrimaryKeyBuilder pk1Builder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
            pk1Builder.addPrimaryKeyColumn("yhid_qd_userid", PrimaryKeyValue.fromString(data.getYhid() + "_" + data.getQd() + "_" + data.getUserid()));
            if (data.getRqlx().equals(RqlxConst.RECENT7) || data.getRqlx().equals(RqlxConst.RECENT30)) {
                pk1Builder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(data.getRqlx()));
            } else {
                pk1Builder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(data.getRqlx() + "_" + data.getRq()));
            }
            pk1Builder.addPrimaryKeyColumn("kf", PrimaryKeyValue.fromString(data.getWwzh()));
            RowUpdateChange rowUpdateChange = new RowUpdateChange(TABLE_NAME, pk1Builder.build());
            rowUpdateChange.put("yhid", ColumnValue.fromLong(data.getYhid()));
            rowUpdateChange.put("qd", ColumnValue.fromString(data.getQd()));
            rowUpdateChange.put("userid", ColumnValue.fromString(data.getUserid()));
            if (StringUtils.isNotBlank(data.getWwzhmc())) {
                rowUpdateChange.put("kfnc", ColumnValue.fromString(data.getWwzhmc()));
            }
            rowUpdateChange.put("rqlx", ColumnValue.fromString(data.getRqlx()));
            rowUpdateChange.put("rq", ColumnValue.fromString(data.getRq()));

            rowUpdateChange.put("dlsc", ColumnValue.fromDouble(data.getDlsc()));

            batchWriteRowRequest.addRowChange(rowUpdateChange);
        }
        if (batchWriteRowRequest.getRowsCount() == 0) {
            return;
        }
        int maxRetryTime = 0;
        BatchWriteRowRequest retryRequest = null;
        while (maxRetryTime < 10) {
            BatchWriteRowResponse batchWriteRowResponse = client.batchWriteRow(retryRequest == null ? batchWriteRowRequest : retryRequest);
            if (batchWriteRowResponse.isAllSucceed()) {
                break;
            }

            retryRequest = batchWriteRowRequest.createRequestForRetry(batchWriteRowResponse.getFailedRows());
            maxRetryTime++;
        }
    }


    private void writeTest13(List<JdJmKfAttendanceData> list) {
        BatchWriteRowRequest batchWriteRowRequest = new BatchWriteRowRequest();

        for (JdJmKfAttendanceData data : list) {
            if (StringUtils.isBlank(data.getWwzh())) {
                continue;
            }
            PrimaryKeyBuilder pk1Builder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
            pk1Builder.addPrimaryKeyColumn("yhid_qd_userid", PrimaryKeyValue.fromString(data.getYhid() + "_" + data.getQd() + "_" + data.getUserid()));
            if (data.getRqlx().equals(RqlxConst.RECENT7) || data.getRqlx().equals(RqlxConst.RECENT30)) {
                pk1Builder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(data.getRqlx()));
            } else {
                pk1Builder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(data.getRqlx() + "_" + data.getRq()));
            }
            pk1Builder.addPrimaryKeyColumn("kf", PrimaryKeyValue.fromString(data.getWwzh()));
            RowUpdateChange rowUpdateChange = new RowUpdateChange(TABLE_NAME, pk1Builder.build());
            rowUpdateChange.put("yhid", ColumnValue.fromLong(data.getYhid()));
            rowUpdateChange.put("qd", ColumnValue.fromString(data.getQd()));
            rowUpdateChange.put("userid", ColumnValue.fromString(data.getUserid()));
            if (StringUtils.isNotBlank(data.getWwzhmc())) {
                rowUpdateChange.put("kfnc", ColumnValue.fromString(data.getWwzhmc()));
            }
            rowUpdateChange.put("rqlx", ColumnValue.fromString(data.getRqlx()));
            rowUpdateChange.put("rq", ColumnValue.fromString(data.getRq()));

            rowUpdateChange.put("dlsc", ColumnValue.fromDouble(data.getDlsc()));

            batchWriteRowRequest.addRowChange(rowUpdateChange);
        }
        if (batchWriteRowRequest.getRowsCount() == 0) {
            return;
        }
        int maxRetryTime = 0;
        BatchWriteRowRequest retryRequest = null;
        while (maxRetryTime < 10) {
            BatchWriteRowResponse batchWriteRowResponse = clientTest.batchWriteRow(retryRequest == null ? batchWriteRowRequest : retryRequest);
            if (batchWriteRowResponse.isAllSucceed()) {
                break;
            }

            retryRequest = batchWriteRowRequest.createRequestForRetry(batchWriteRowResponse.getFailedRows());
            maxRetryTime++;
        }
    }

    private void write14(List<CustomerReceptionData> list) {
        BatchWriteRowRequest batchWriteRowRequest = new BatchWriteRowRequest();

        for (CustomerReceptionData data : list) {
            if (StringUtils.isBlank(data.getWwzh())) {
                continue;
            }
            PrimaryKeyBuilder pk1Builder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
            pk1Builder.addPrimaryKeyColumn("yhid_qd_userid", PrimaryKeyValue.fromString(data.getYhid() + "_" + data.getQd() + "_" + data.getUserid()));
            if (data.getRqlx().equals(RqlxConst.RECENT7) || data.getRqlx().equals(RqlxConst.RECENT30)) {
                pk1Builder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(data.getRqlx()));
            } else {
                pk1Builder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(data.getRqlx() + "_" + data.getRq()));
            }
            pk1Builder.addPrimaryKeyColumn("kf", PrimaryKeyValue.fromString(data.getWwzh()));

            RowUpdateChange rowUpdateChange = new RowUpdateChange(TABLE_NAME, pk1Builder.build());
            rowUpdateChange.put("yhid", ColumnValue.fromLong(data.getYhid()));
            rowUpdateChange.put("qd", ColumnValue.fromString(data.getQd()));
            rowUpdateChange.put("userid", ColumnValue.fromString(data.getUserid()));
            if (StringUtils.isNotBlank(data.getWwzhmc())) {
                rowUpdateChange.put("kfnc", ColumnValue.fromString(data.getWwzhmc()));
            }
            rowUpdateChange.put("rqlx", ColumnValue.fromString(data.getRqlx()));
            rowUpdateChange.put("rq", ColumnValue.fromString(data.getRq()));
//            rowUpdateChange.put("smrghfl", ColumnValue.fromDouble(data.getSmrghfl()));
//            rowUpdateChange.put("kfhpl", ColumnValue.fromDouble(data.getKfhpl()));
//            rowUpdateChange.put("zxrs", ColumnValue.fromLong(data.getZxrs()));
//            rowUpdateChange.put("zxrc", ColumnValue.fromLong(data.getZxrc()));
//            rowUpdateChange.put("rgpjhfsc", ColumnValue.fromDouble(data.getRgpjhfsc()));
            rowUpdateChange.put("hhl", ColumnValue.fromLong(data.getHhl()));
            rowUpdateChange.put("smhfl", ColumnValue.fromDouble(data.getSmhfl()));
            rowUpdateChange.put("pjrgxysc", ColumnValue.fromDouble(data.getPjrgxysc()));
            rowUpdateChange.put("bfwl", ColumnValue.fromDouble(data.getBfwl()));
            rowUpdateChange.put("bmyl", ColumnValue.fromDouble(data.getBmyl()));
            rowUpdateChange.put("xdzhje", ColumnValue.fromDouble(data.getXdzhje()));
            batchWriteRowRequest.addRowChange(rowUpdateChange);
        }

        if (batchWriteRowRequest.getRowsCount() == 0) {
            return;
        }

        int maxRetryTime = 0;
        BatchWriteRowRequest retryRequest = null;
        while (maxRetryTime < 10) {
            BatchWriteRowResponse batchWriteRowResponse = client.batchWriteRow(retryRequest == null ? batchWriteRowRequest : retryRequest);
            if (batchWriteRowResponse.isAllSucceed()) {
                break;
            }

            retryRequest = batchWriteRowRequest.createRequestForRetry(batchWriteRowResponse.getFailedRows());
            maxRetryTime++;
        }
    }

    private void writeTest14(List<CustomerReceptionData> list) {
        BatchWriteRowRequest batchWriteRowRequest = new BatchWriteRowRequest();

        for (CustomerReceptionData data : list) {
            if (StringUtils.isBlank(data.getWwzh())) {
                continue;
            }
            PrimaryKeyBuilder pk1Builder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
            pk1Builder.addPrimaryKeyColumn("yhid_qd_userid", PrimaryKeyValue.fromString(data.getYhid() + "_" + data.getQd() + "_" + data.getUserid()));
            if (data.getRqlx().equals(RqlxConst.RECENT7) || data.getRqlx().equals(RqlxConst.RECENT30)) {
                pk1Builder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(data.getRqlx()));
            } else {
                pk1Builder.addPrimaryKeyColumn("rqlx_rq", PrimaryKeyValue.fromString(data.getRqlx() + "_" + data.getRq()));
            }
            pk1Builder.addPrimaryKeyColumn("kf", PrimaryKeyValue.fromString(data.getWwzh()));

            RowUpdateChange rowUpdateChange = new RowUpdateChange(TABLE_NAME, pk1Builder.build());
            rowUpdateChange.put("yhid", ColumnValue.fromLong(data.getYhid()));
            rowUpdateChange.put("qd", ColumnValue.fromString(data.getQd()));
            rowUpdateChange.put("userid", ColumnValue.fromString(data.getUserid()));
            if (StringUtils.isNotBlank(data.getWwzhmc())) {
                rowUpdateChange.put("kfnc", ColumnValue.fromString(data.getWwzhmc()));
            }
            rowUpdateChange.put("rqlx", ColumnValue.fromString(data.getRqlx()));
            rowUpdateChange.put("rq", ColumnValue.fromString(data.getRq()));
//            rowUpdateChange.put("smrghfl", ColumnValue.fromDouble(data.getSmrghfl()));
//            rowUpdateChange.put("kfhpl", ColumnValue.fromDouble(data.getKfhpl()));
//            rowUpdateChange.put("zxrs", ColumnValue.fromLong(data.getZxrs()));
//            rowUpdateChange.put("zxrc", ColumnValue.fromLong(data.getZxrc()));
//            rowUpdateChange.put("rgpjhfsc", ColumnValue.fromDouble(data.getRgpjhfsc()));
            rowUpdateChange.put("hhl", ColumnValue.fromLong(data.getHhl()));
            rowUpdateChange.put("smhfl", ColumnValue.fromDouble(data.getSmhfl()));
            rowUpdateChange.put("pjrgxysc", ColumnValue.fromDouble(data.getPjrgxysc()));
            rowUpdateChange.put("bfwl", ColumnValue.fromDouble(data.getBfwl()));
            rowUpdateChange.put("bmyl", ColumnValue.fromDouble(data.getBmyl()));
            rowUpdateChange.put("xdzhje", ColumnValue.fromDouble(data.getXdzhje()));
            batchWriteRowRequest.addRowChange(rowUpdateChange);
        }

        if (batchWriteRowRequest.getRowsCount() == 0) {
            return;
        }

        int maxRetryTime = 0;
        BatchWriteRowRequest retryRequest = null;
        while (maxRetryTime < 10) {
            BatchWriteRowResponse batchWriteRowResponse = clientTest.batchWriteRow(retryRequest == null ? batchWriteRowRequest : retryRequest);
            if (batchWriteRowResponse.isAllSucceed()) {
                break;
            }

            retryRequest = batchWriteRowRequest.createRequestForRetry(batchWriteRowResponse.getFailedRows());
            maxRetryTime++;
        }
    }
}
