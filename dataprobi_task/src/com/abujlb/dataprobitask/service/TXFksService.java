package com.abujlb.dataprobitask.service;


import com.abujlb.dataprobitask.bean.DataprobiTaskMsg;
import com.abujlb.dataprobitask.bean.TxFksBean;
import com.abujlb.dataprobitask.constants.RqlxConst;
import com.abujlb.dataprobitask.oss.BiDataOssUtil;
import com.abujlb.dataprobitask.sqlite.BiSqliteDb;
import com.abujlb.dataprobitask.sqlite.SqlProvider;
import com.abujlb.dataprobitask.tsdao.TxFksTsDao;
import com.abujlb.dataprobitask.utils.DateUtil;
import com.abujlb.dataprobitask.utils.FileUtil;
import com.abujlb.dataprobitask.utils.SqliteDbUtil;
import com.abujlb.dataprobitask.utils.SycmFlowXlsReader;
import com.abujlb.sqlite.SqliteStatement;
import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.sql.PreparedStatement;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 处理淘系访客数
 *
 * <AUTHOR>
 * @date 2024-03-11
 */
@Component
public class TXFksService {
    private static final Logger LOG = Logger.getLogger(TXFksService.class);
    private static final List<String> typeList = new ArrayList<>();

    static {
        //直通车
        typeList.add("ztc");
        //引力魔发
        typeList.add("ylmf");
        //万相台
        typeList.add("aizt");
        //淘宝客
        typeList.add("tbk");
        //手淘推荐
        typeList.add("sttj");
        //手淘搜索
        typeList.add("stss");
    }

    @Autowired
    private TxFksTsDao txFksTsDao;
    @Autowired
    private BiDataOssUtil biDataOssUtil;

    public void fksDetail(DataprobiTaskMsg taskMsg) {
        long start = System.currentTimeMillis();

        String userid = taskMsg.getUserid();
        int yhid = taskMsg.getYhid();
        try {
            LOG.info("渠道：" + taskMsg.getQd() + "，店铺名：" + taskMsg.getDpmc() + ">>访客数详情>>" + taskMsg.getRqList() + ">>开始：" + DateUtil.getCurrentTime());
            for (String rq : taskMsg.getRqList()) {
                //处理日
                dealDay(rq, userid, yhid);
                //汇总周
                if (DateUtil.isLastDayForWeek(rq)) {
                    List<String> weekDays = DateUtil.getDaysBefore(rq, -6);
                    dealSum(weekDays.get(0), weekDays, userid, yhid, "week");
                }

                if (taskMsg.getRqList().indexOf(rq) == taskMsg.getRqList().size() - 1 || DateUtil.isLastDayForMonth(rq)) {
                    String monthRq = rq.substring(0, 7);
                    List<String> monthDays = DateUtil.getMonthDays(rq.substring(0, 7));
                    dealSum(monthRq, monthDays, userid, yhid, "month");
                }

                //汇总7天 30天
                if (taskMsg.getRqList().indexOf(rq) == taskMsg.getRqList().size() - 1 && (rq.compareTo(DateUtil.getLastday()) >= 0)) {
                    List<String> recent7Days = DateUtil.getDaysBefore(rq, 6);
                    dealSum(rq, recent7Days, userid, yhid, "recent7");

                    List<String> recent30Days = DateUtil.getDaysBefore(rq, 29);
                    dealSum(rq, recent30Days, userid, yhid, "recent30");
                }
            }
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
        long end = System.currentTimeMillis();
        LOG.info("渠道：" + taskMsg.getQd() + "，店铺名：" + taskMsg.getDpmc() + ">>访客数详情>>" + taskMsg.getRqList() + ">>结束：" + DateUtil.getCurrentTime() + ">>耗时：" + (end - start) / 1000 + "秒");
    }

    private void dealDay(String rq, String userid, int yhid) {
        String OSS_PATTERN = "sycmflow/%s/%s/day_shopsource_%s.xls";
        Map<String, TxFksBean> txFksBeanMap = new HashMap<>();
        for (String type : typeList) {
            String osskey = String.format(OSS_PATTERN, userid, rq, type);
            if (!biDataOssUtil.existSpecificOne(osskey, BiDataOssUtil.ABUJLBJSON)) {
                continue;
            }
            String temppath = FileUtil.createXlsFilePath();
            biDataOssUtil.downloadSpecificOne(osskey, temppath, BiDataOssUtil.ABUJLBJSON);
            SycmFlowXlsReader.parseXls2(temppath, type, txFksBeanMap);
        }
        if (txFksBeanMap.isEmpty()) {
            return;
        }
        List<TxFksBean> txFksBeanList = new ArrayList<>(txFksBeanMap.values());
        for (TxFksBean txFksBean : txFksBeanList) {
            txFksBean.setQd("TX");
            txFksBean.setYhid(yhid);
            txFksBean.setRqlx("day");
            txFksBean.setUserid(userid);
            txFksBean.setRq(rq);
        }
        txFksTsDao.updateRows(txFksBeanList);
        dayToDb(txFksBeanList, userid, rq, yhid);
    }

    private void dayToDb(List<TxFksBean> txFksBeanList, String userid, String rq, int yhid) {
        SqliteStatement<TxFksBean> sqliteStatement = null;
        String dbFilePath = FileUtil.createDBFilePath();
        final String SQL_PATTERN = "insert into tx_fks (bbid,rq,stssfks,sttjfks,tbkfks,aiztfks,ylmffks,ztcfks) values('%s','%s','%s','%s','%s','%s','%s','%s') ;";
        BiSqliteDb biSqliteDb = null;
        try {
            biSqliteDb = new BiSqliteDb(dbFilePath, SqlProvider.readSql(SqlProvider.TX_FKS_SQL), null);
            List<String> sqls = new ArrayList<>(txFksBeanList.size());
            for (TxFksBean txFksBean : txFksBeanList) {
                String sql = String.format(SQL_PATTERN, txFksBean.getBbid(), txFksBean.getRq(), txFksBean.getStssfks(), txFksBean.getSttjfks(), txFksBean.getTbkfks(), txFksBean.getAiztfks(), txFksBean.getYlmffks(), txFksBean.getZtcfks());
                sqls.add(sql);
            }
            SqliteDbUtil.executeSqls(biSqliteDb, sqls);

            // 存商品汇总到店铺维度的数据
            String sql = "select sum(stssfks) as stssfks,sum(tbkfks) as tbkfks,sum(ylmffks) as ylmffks,sum(ztcfks) as ztcfks,sum(sttjfks) as sttjfks,sum(aiztfks) as aiztfks from tx_fks ;";
            sqliteStatement = biSqliteDb.createSqliteStatement(sql, TxFksBean.class);
            TxFksBean txFksSum = sqliteStatement.queryForObject(null, TxFksBean.class);
            if (txFksSum != null) {
                txFksSum.setYhid(yhid);
                txFksSum.setQd("TX");
                txFksSum.setUserid(userid);
                txFksSum.setRqlx(RqlxConst.DAY);
                txFksSum.setRq(rq);
                txFksTsDao.updateRow(txFksSum);
            }
            File file = new File(biSqliteDb.getFile());
            biDataOssUtil.upload(file, "bi_jysj/" + userid + "/" + rq + "/txspfks.db");
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        } finally {
            SqliteDbUtil.closeSqliteStatements(sqliteStatement);
            SqliteDbUtil.close(biSqliteDb);
        }
    }

    private void dealSum(String currDay, List<String> rqList, String userid, int yhid, String rqlx) {
        SqliteStatement<TxFksBean> sqliteStatement = null;
        BiSqliteDb biSqliteDbSum = null;
        try {
            List<String> dbFilePathList = new ArrayList<>();
            for (String rq : rqList) {
                String ossPath = "bi_jysj/" + userid + "/" + rq + "/txspfks.db";
                if (!biDataOssUtil.exist(ossPath)) {
                    continue;
                }

                String dbFilePath = FileUtil.createDBFilePath();
                biDataOssUtil.download(ossPath, dbFilePath);
                dbFilePathList.add(dbFilePath);
            }
            if (dbFilePathList.isEmpty()) {
                return;
            }

            String dbFileSumPath = FileUtil.createDBFilePath();
            biSqliteDbSum = new BiSqliteDb(dbFileSumPath, SqlProvider.readSql(SqlProvider.TX_FKS_SQL), null);
            mergeSqliteDb(biSqliteDbSum, dbFilePathList);

            String sql = "select bbid,sum(stssfks) as stssfks,sum(tbkfks) as tbkfks,sum(ylmffks) as ylmffks,sum(ztcfks) as ztcfks,sum(sttjfks) as sttjfks,sum(aiztfks) as aiztfks from tx_fks group by bbid ;";
            sqliteStatement = biSqliteDbSum.createSqliteStatement(sql, TxFksBean.class);
            List<TxFksBean> txFksBeanList = sqliteStatement.queryForList(null, TxFksBean.class);
            if (CollectionUtils.isEmpty(txFksBeanList)) {
                return;
            }

            for (TxFksBean txFksBean : txFksBeanList) {
                txFksBean.setQd("TX");
                txFksBean.setYhid(yhid);
                txFksBean.setRqlx(rqlx);
                txFksBean.setUserid(userid);
                txFksBean.setRq(currDay);
            }

            txFksTsDao.updateRows(txFksBeanList);

            // 存商品汇总到店铺维度的数据
            String sqlSum = "select sum(stssfks) as stssfks,sum(tbkfks) as tbkfks,sum(ylmffks) as ylmffks,sum(ztcfks) as ztcfks,sum(sttjfks) as sttjfks,sum(aiztfks) as aiztfks from tx_fks ;";
            sqliteStatement = biSqliteDbSum.createSqliteStatement(sqlSum, TxFksBean.class);
            TxFksBean txFksSum = sqliteStatement.queryForObject(null, TxFksBean.class);
            if (txFksSum != null) {
                txFksSum.setYhid(yhid);
                txFksSum.setQd("TX");
                txFksSum.setUserid(userid);
                txFksSum.setRqlx(rqlx);
                txFksSum.setRq(currDay);
                txFksTsDao.updateRow(txFksSum);
            }
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        } finally {
            SqliteDbUtil.closeSqliteStatements(sqliteStatement);
            SqliteDbUtil.close(biSqliteDbSum);
        }
    }

    public void mergeSqliteDb(BiSqliteDb biSqliteDb, List<String> sqliteDbList) {
        Statement statement = null;
        PreparedStatement preparedStatement = null;
        try {
            statement = biSqliteDb.createStatement();
            for (String sqliteDb : sqliteDbList) {
                preparedStatement = biSqliteDb.prepareStatement("ATTACH DATABASE ? AS db2;");
                preparedStatement.setString(1, sqliteDb);
                preparedStatement.executeUpdate();
                statement.executeUpdate("INSERT INTO tx_fks  SELECT * FROM db2.tx_fks;");
                // 分离数据库
                statement.executeUpdate("DETACH DATABASE db2;");
                File file = new File(sqliteDb);
                file.delete();
            }
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        } finally {
            try {
                if (preparedStatement != null) {
                    preparedStatement.close();
                }
                if (statement != null) {
                    statement.close();
                }
            } catch (Exception e) {
                LOG.error(e.getMessage(), e);
            }
        }
    }
}
