package com.abujlb.dataprobitask.service.yghf;


import com.abujlb.dataprobitask.bean.BiRuleReRunTask;
import com.abujlb.dataprobitask.bean.BiWphYghfdd;
import com.abujlb.dataprobitask.bean.BiYghfdd;
import com.abujlb.dataprobitask.bean.DataprobiTaskMsg;
import com.abujlb.dataprobitask.utils.*;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 唯品会-唯享客-订单处理
 *
 * <AUTHOR>
 * @date 2024-03-13
 */
@Component
public class WPHWxkDdYghfService extends YghfService {

    public static final String SIGN = "ygyjzc";
    public static final String SIGN_DESC = "唯享客订单预估佣金支出";

    @Override
    protected void dealDay(DataprobiTaskMsg dataprobiBaseMsg, String rq, String SIGN) {
        String osskey = "biwph/" + dataprobiBaseMsg.getUserid() + "/" + rq + "/wxkhf.json";
        if (!biDataOssUtil.exist(osskey)) {
            return;
        }

        String filepath = FileUtil.createJSONFilePath();
        biDataOssUtil.download(osskey, filepath);
        String json = FileToJson.toJson(filepath);
        JSONArray jsonArray = JSONArray.parseArray(json);

        // 第一步：根据rq和jsonArray里的update_time字段比较，取出该rq的所有数据
        List<BiWphYghfdd> rqMatchedList = processRqMatchedDataByUpdateTime(jsonArray, rq);

        if (!rqMatchedList.isEmpty()) {
            // 第二步：将第一步取到的数据根据主键去tablestore里查询，比较字段值
            Set<String> updatedRqSet = compareAndProcessData(dataprobiBaseMsg, rqMatchedList);

            // 第三步：提交重算任务，使用表格存储里查到的wxk_dd_jsrq作为日期
            for (String jsrq : updatedRqSet) {
                if (StringUtils.isNotBlank(jsrq)) {
                    submitWphRerunTask(dataprobiBaseMsg, jsrq, "wph_wxkygdd_", "唯享客预估订单重算");
                }
            }
        }
    }

    /**
     * 第一步：根据rq和jsonArray里的update_time字段比较，取出该rq的所有数据
     * update_time格式：2025-06-01 00:00:00，需要转换为日期格式与rq比较
     */
    private List<BiWphYghfdd> processRqMatchedDataByUpdateTime(JSONArray jsonArray, String rq) {
        List<BiWphYghfdd> list = new ArrayList<>();

        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            String updateTime = jsonObject.getString("update_time");

            // 将update_time转换为日期格式并与rq比较
            if (StringUtils.isNotBlank(updateTime)) {
                String convertedDate = convertUpdateTimeToDate(updateTime);
                if (rq.equals(convertedDate)) {
                    BiWphYghfdd biYghfdd = createBiWphYghfddFromJson(jsonObject);
                    if (isValidData(biYghfdd)) {
                        list.add(biYghfdd);
                    }
                }
            }
        }

        return list;
    }

    /**
     * 第二步和第三步：将数据根据主键去tablestore里查询，比较并处理
     * @param dataprobiBaseMsg 任务消息
     * @param newDataList 新数据列表
     * @return 涉及更新的wxk_dd_jsrq日期集合
     */
    private Set<String> compareAndProcessData(DataprobiTaskMsg dataprobiBaseMsg, List<BiWphYghfdd> newDataList) {
        Set<String> jsrqSet = new HashSet<>();
        if (newDataList.isEmpty()) {
            return jsrqSet;
        }

        // 从tablestore批量查询现有数据
        List<BiWphYghfdd> existingDataList = batchQueryExistingData(dataprobiBaseMsg, newDataList);

        // 创建现有数据的映射，便于查找
        Map<String, BiWphYghfdd> existingDataMap = new HashMap<>();
        for (BiWphYghfdd existing : existingDataList) {
            String key = existing.getTid() + "_" + existing.getBbid();
            existingDataMap.put(key, existing);
        }

        // 分别处理需要插入和更新的数据
        List<BiWphYghfdd> needInsertList = new ArrayList<>();
        List<BiWphYghfdd> needUpdateList = new ArrayList<>();

        for (BiWphYghfdd newData : newDataList) {
            String key = newData.getTid() + "_" + newData.getBbid();
            BiWphYghfdd existingData = existingDataMap.get(key);

            if (existingData == null) {
                // 没有查到对应记录，需要插入
                needInsertList.add(newData);
                // 插入数据时，使用新数据的日期（转换为年月日格式）
                String jsrq = convertUpdateTimeToDateOnly(newData.getWxk_dd_jsrq());
                if (StringUtils.isNotBlank(jsrq)) {
                    jsrqSet.add(jsrq);
                }
            } else {
                // 查到对应记录，比较Wxk_dd_tgyj和Wxk_dd_fwf字段
                if (hasFieldDifferences(newData, existingData)) {
                    needUpdateList.add(newData);
                    // 更新数据时，使用表格存储里查到的wxk_dd_jsrq
                    String jsrq = convertUpdateTimeToDateOnly(existingData.getWxk_dd_jsrq());
                    if (StringUtils.isNotBlank(jsrq)) {
                        jsrqSet.add(jsrq);
                    }
                }
            }
        }

        // 批量插入新数据
        if (!needInsertList.isEmpty()) {
            saveDataInBatches(dataprobiBaseMsg, needInsertList, SIGN);
        }

        // 批量更新有差异的数据（只更新金额字段）
        if (!needUpdateList.isEmpty()) {
            updateDifferentData(dataprobiBaseMsg, needUpdateList);
        }

        return jsrqSet;
    }

    /**
     * 从JSON对象创建BiWphYghfdd对象
     */
    private BiWphYghfdd createBiWphYghfddFromJson(JSONObject jsonObject) {
        BiWphYghfdd biYghfdd = new BiWphYghfdd();
        biYghfdd.setTid(jsonObject.getString("order_id"));
        biYghfdd.setBbid(jsonObject.getString("product_id"));
        biYghfdd.setYgyjzc(jsonObject.containsKey("yj_cos_fee") ? jsonObject.getDoubleValue("yj_cos_fee") : 0.0);
        biYghfdd.setYgfwfzc(jsonObject.containsKey("fwf_cos_fee") ? jsonObject.getDoubleValue("fwf_cos_fee") : 0.0);
        biYghfdd.setWxk_dd_tgyj(jsonObject.containsKey("yj_cos_fee") ? jsonObject.getDoubleValue("yj_cos_fee") : 0.0);
        biYghfdd.setWxk_dd_fwf(jsonObject.containsKey("fwf_cos_fee") ? jsonObject.getDoubleValue("fwf_cos_fee") : 0.0);
        // 将update_time转换为年月日格式
        String updateTime = jsonObject.getString("update_time");
        String jsrq = convertUpdateTimeToDateOnly(updateTime);
        biYghfdd.setWxk_dd_jsrq(jsrq);
        return biYghfdd;
    }

    /**
     * 验证数据是否有效
     */
    private boolean isValidData(BiWphYghfdd biYghfdd) {
        return StringUtils.isNotBlank(biYghfdd.getTid()) &&
               StringUtils.isNotBlank(biYghfdd.getBbid());
    }

    /**
     * 转换update_time格式：2025-06-01 00:00:00 -> 2025-06-01
     */
    private String convertUpdateTimeToDate(String updateTime) {
        try {
            if (StringUtils.isBlank(updateTime)) {
                return null;
            }

            // 解析格式：2025-06-01 00:00:00
            SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd");

            return outputFormat.format(inputFormat.parse(updateTime));
        } catch (Exception e) {
            // 如果解析失败，尝试其他格式
            try {
                // 尝试解析格式：2025.05.13 17:09:48
                SimpleDateFormat inputFormat2 = new SimpleDateFormat("yyyy.MM.dd HH:mm:ss");
                SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd");
                return outputFormat.format(inputFormat2.parse(updateTime));
            } catch (Exception e2) {
                // 如果都解析失败，返回null
                return null;
            }
        }
    }

    /**
     * 转换update_time为年月日格式，用于存储到tablestore的wxk_dd_jsrq字段
     * 2025-06-01 00:00:00 -> 2025-06-01
     */
    private String convertUpdateTimeToDateOnly(String updateTime) {
        return convertUpdateTimeToDate(updateTime);
    }

    /**
     * 批量保存数据到tablestore
     */
    private void saveDataInBatches(DataprobiTaskMsg dataprobiBaseMsg, List<BiWphYghfdd> list, String SIGN) {
        if (list.isEmpty()) {
            return;
        }

        // 分批保存，每批50条
        for (int i = 0; i < list.size(); i += 50) {
            int endIndex = Math.min(i + 50, list.size());
            List<BiWphYghfdd> batch = list.subList(i, endIndex);
            super.saveWphData(dataprobiBaseMsg, new ArrayList<>(batch), SIGN);
        }
    }



    /**
     * 批量查询现有数据
     */
    private List<BiWphYghfdd> batchQueryExistingData(DataprobiTaskMsg dataprobiBaseMsg, List<BiWphYghfdd> queryList) {
        return biWphYghfddTsDao.batchGetRowsWithMultipleFields(queryList, dataprobiBaseMsg.getQd(), dataprobiBaseMsg.getUserid());
    }



    /**
     * 检查字段是否有差异
     */
    private boolean hasFieldDifferences(BiWphYghfdd newData, BiWphYghfdd existingData) {
        // 使用MathUtil.minus来比较double值，避免精度问题
        return MathUtil.minus(newData.getWxk_dd_tgyj(), existingData.getWxk_dd_tgyj()) != 0 ||
               MathUtil.minus(newData.getWxk_dd_fwf(), existingData.getWxk_dd_fwf()) != 0;
    }

    /**
     * 更新有差异的数据
     */
    private void updateDifferentData(DataprobiTaskMsg dataprobiBaseMsg, List<BiWphYghfdd> updateList) {
        // 使用现有的saveWphData方法来更新数据
        saveDataInBatches(dataprobiBaseMsg, updateList, SIGN);
    }

    private void submitWphRerunTask(DataprobiTaskMsg dataprobiBaseMsg, String rq, String prefix, String reason) {
        BiRuleReRunTask task = new BiRuleReRunTask();
        task.setId(prefix + UUID.randomUUID());
        task.setYhid(dataprobiBaseMsg.getYhid());
        task.setQd(dataprobiBaseMsg.getQd());
        task.setUserid(dataprobiBaseMsg.getUserid());
        task.setKsrq(rq);
        task.setJsrq(rq);
        task.setCjsj(DateUtil.getCurrentTime());
        task.setZt(0);
        task.setType(22);
        task.setCjrxm("唯享客花费处理");
        task.setReason(reason);
        DataUploader.rerunTask(task);
    }
}
