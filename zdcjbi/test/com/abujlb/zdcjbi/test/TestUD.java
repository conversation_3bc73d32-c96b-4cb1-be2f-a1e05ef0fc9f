package com.abujlb.zdcjbi.test;

import com.abujlb.BaseTest;
import com.abujlb.zdcjbi.auth.BiAuth;
import com.abujlb.zdcjbi.cookie.AbujlbCookieStore;
import com.abujlb.zdcjbi.cookie.Cjzh;
import com.abujlb.zdcjbi.http.AiztOneHttpClientV2;
import com.abujlb.zdcjbi.util.ShopInfoUtil;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

public class TestUD extends BaseTest {

    @Autowired
    private AbujlbCookieStore abujlbCookieStore;

    @Test
    public void ud_auth() {
        List<Cjzh> cjzhs = abujlbCookieStore.allCjzh(1, 0, "", 0);
        for (Cjzh cjzh : cjzhs) {
            String ck = cjzh.getCk();
            Cjzh temp = abujlbCookieStore.getCjzhForKey(ck);
            if (temp != null) {
                BiAuth.udzht(temp);
            }
        }
    }

    @Test
    public void aiztone_records() {
        String cookieKey = "ck_e2a7869004b74d35ade96caab8209157";
//        String cookieKey = "ck_cd7c1d99595a4496bb0292c5061889d7";
        Cjzh cjzh = abujlbCookieStore.getCjzhForKey(cookieKey);
        if (cjzh == null) {
            return;
        }

        ShopInfoUtil.initShopOverSeas(cjzh);
        AiztOneHttpClientV2.alimamaIndex(cjzh);
        BiAuth.adbrainOne(cjzh);
//        List<String> TYPE_LIST = Arrays.asList("tx_ztc","tx_ylmf","tx_aizt","ztc_units","ztc_keywords","ztc_crowds","dmbzt","qztg","xstg");
        List<String> TYPE_LIST = Collections.singletonList("qztg");
        String start = "2024-11-01";
        String end = "2024-11-30";
        String splitType = "day";
        for (String type : TYPE_LIST) {
            int recordsCount = AiztOneHttpClientV2.recordsCount(cjzh, type, start, end, splitType);
            System.out.println("type:" + type + "-->recordsCount:" + recordsCount);
        }

    }
}
