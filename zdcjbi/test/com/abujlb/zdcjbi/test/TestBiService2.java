package com.abujlb.zdcjbi.test;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.apache.log4j.Logger;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.abujlb.AbujlbBeanFactory;
import com.abujlb.BaseTest;
import com.abujlb.mq.AliyunMq2;
import com.abujlb.mq.JobMessage;
import com.abujlb.util.DateUtil;
import com.abujlb.util.StringUtil;
import com.abujlb.zdcjbi.bean.msg.JysjMsg;
import com.abujlb.zdcjbi.service.BiService;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

/**
 * local采集测试
 * 
 * <AUTHOR>
 * @date 2024-07-02 11:44:18
 */
public class TestBiService2 extends BaseTest {
	
	private static final Logger log = Logger.getLogger(TestBiService2.class);

	@Autowired
	private AbujlbBeanFactory abujlbBeanFactory;
	@Autowired
    private AliyunMq2 aliyunMq2;
	
	private String cookie = "tfstk=f80KUYwm9dv3VrCOXgtgEgV7w7dgd4hEXvlfr82hVAHtCYq3VBJU2Uh7I2quRwD8VviXTQkkTAe-LYEuqBkoQgMmZgcHTwfEbxcf-2xUraUUzz9DnEYDTXzzP08QQW1Uf7NPAUq7VFsUPX52nEYmOV4kEdvm-kWWKJV7PWZCROOTZSb5RYZ55fwaa76QNwO967y_O9NQdfZ_N7sCPYaW6fwaNb9W9R5Q9anlYy2QiG8hAMgT1LPj9OIQ3DPLJ-GIJa7ycWELhXw93sVBwlGz245PyuhI0YPjd998UVhsRmMXIM2ID7hm2XTC5y0EOmZSlLSSTy2L52iANaGTJJD-yAWpB-gE1qz_jEOx1VknTVhlNUNinRGERzLXgyUQdPFZrd7QeciS-kzPdLerWfgI2g8KoqFBCt2YZMO96gSzA5yyMjNMqWxY_5eDOksP4uVNrM3Pvgs5I5PTnB_54gru_; rsid=8b10e8ab4ac54c4a97561279deb208e772cae0e567421b6854";
	
	private String qd = "TM";
	private String startDate = "2024-11-01";
	private String endDate = "2024-12-18";

	@Test
	public void batch() {
		try {
			// 分页采集
			int count = 0;
			int pagecount = 1; // 总页数
			// int pagenow = 1;
			int pageSize = 400;
			String json = null;
			
			BiService biService = abujlbBeanFactory.getBean(BiService.class);
			
			for (int i = pagecount; i <= pagecount; i++) {
				System.out.println("第" + i + "页");
				json = this.allBiInfo(cookie, i, pageSize, qd, startDate, endDate);
				if (!StringUtil.isJson2(json)) {
					return;
				}
				JSONObject jsonObject = JSONObject.parseObject(json);
				if (i == pagecount) {
					count = jsonObject.getIntValue("listCount");
					if (count > 0) {
						pagecount = (count + pageSize - 1) / pageSize; // 总页数;
					}
				}
				JSONArray arr = jsonObject.getJSONArray("cjrqList");
				for (int j = 0; arr != null && j < arr.size(); j++) {
					JSONObject obj = arr.getJSONObject(j);
					String json2 = this.ckck(cookie, obj.getString("qd"), obj.getIntValue("jlbdpid"), obj.getString("userid"), obj.getString("dpmc"));
					if (!StringUtil.isJson2(json2)) {
						continue;
					}
					String cookieKey = JSONObject.parseObject(json2).getJSONObject("cjrq").getString("cookieKey");
					System.out.println("当前key-----》" + cookieKey + "，渠道：" + obj.getString("qd") + "，俱乐部店铺id：" + obj.getIntValue("jlbdpid") + "，userid：" + obj.getString("userid") + "，店铺名称：" + obj.getString("dpmc") + "，当前时间：" + DateUtil.formatTime(new Date()));
					JysjMsg jysjMsg = new JysjMsg();
					// 1登录/上午补发 2下午补采
					jysjMsg.setType(1);
					jysjMsg.setCookieKey(cookieKey);
					biService.process(jysjMsg);
				}
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		System.out.println("-------------完成");
	}
	
	@Test
	public void batchAllCookieKey() {
		try {
			// 分页采集
			int count = 0;
			int pagecount = 1; // 总页数
			// int pagenow = 1;
			int pageSize = 400;
			String json = null;
			
			for (int i = pagecount; i <= pagecount; i++) {
				System.out.println("第" + i + "页");
				json = this.allBiInfo(cookie, i, pageSize, qd, startDate, endDate);
				if (!StringUtil.isJson2(json)) {
					return;
				}
				JSONObject jsonObject = JSONObject.parseObject(json);
				if (i == pagecount) {
					count = jsonObject.getIntValue("listCount");
					if (count > 0) {
						pagecount = (count + pageSize - 1) / pageSize; // 总页数;
					}
				}
				JSONArray arr = jsonObject.getJSONArray("cjrqList");
				for (int j = 0; arr != null && j < arr.size(); j++) {
					JSONObject obj = arr.getJSONObject(j);
					String json2 = this.ckck(cookie, obj.getString("qd"), obj.getIntValue("jlbdpid"), obj.getString("userid"), obj.getString("dpmc"));
					if (!StringUtil.isJson2(json2)) {
						continue;
					}
					String cookieKey = JSONObject.parseObject(json2).getJSONObject("cjrq").getString("cookieKey");
					System.out.println(cookieKey);
				}
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		System.out.println("-------------完成");
	}
	
	@Test
	public void batchAllCookieKeySendMsg() {
		try {
			// 分页采集
			int count = 0;
			int pagecount = 1; // 总页数
			// int pagenow = 1;
			int pageSize = 100;
			String json = null;
			
			for (int i = pagecount; i <= pagecount; i++) {
				System.out.println("第" + i + "页");
				json = this.allBiInfo(cookie, i, pageSize, qd, startDate, endDate);
				if (!StringUtil.isJson2(json)) {
					return;
				}
				JSONObject jsonObject = JSONObject.parseObject(json);
				if (i == pagecount) {
					count = jsonObject.getIntValue("listCount");
					if (count > 0) {
						pagecount = (count + pageSize - 1) / pageSize; // 总页数;
					}
				}
				JSONArray arr = jsonObject.getJSONArray("cjrqList");
				for (int j = 0; arr != null && j < arr.size(); j++) {
					JSONObject obj = arr.getJSONObject(j);
					String json2 = this.ckck(cookie, obj.getString("qd"), obj.getIntValue("jlbdpid"), obj.getString("userid"), obj.getString("dpmc"));
					if (!StringUtil.isJson2(json2)) {
						continue;
					}
					String cookieKey = JSONObject.parseObject(json2).getJSONObject("cjrq").getString("cookieKey");
					JysjMsg jysjMsg = new JysjMsg();
			        jysjMsg.setCookieKey(cookieKey);
			        //type为5时  spxg不检测list为空的情况
			        jysjMsg.setType(1);
			        jysjMsg.setT(String.valueOf(System.currentTimeMillis()));
					aliyunMq2.send("zdcjbitx_other", "", new JobMessage("zdcjbitxotherprocesser", jysjMsg));
				}
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		System.out.println("-------------完成");
	}
	
	/**
	 * batchHighMsgAndChongdeng 批量发生重新登录消息，并且重新登录
	 * 
	 * <AUTHOR>
	 */
	@Test
	public void batchHighMsgAndChongdeng() {
		try {
			List<String> qdArr = Arrays.asList("TM", "TB");
			// List<String> qdArr = Arrays.asList("TB");
			for (String qd2 : qdArr) {
				log.info("**************************************************** 启动，渠道：" + qd2);
				
				// 分页采集
				int count = 0;
				int pagecount = 1; // 总页数
				// int pagenow = 1;
				int pageSize = 100;
				String json = null;
				for (int i = pagecount; i <= pagecount; i++) {
					log.info("************************** 渠道：" + qd2 + "，第" + i + "页");
					json = this.allBiInfo(cookie, i, pageSize, qd2, startDate, endDate);
					if (!StringUtil.isJson2(json)) {
						return;
					}
					JSONObject jsonObject = JSONObject.parseObject(json);
					if (i == pagecount) {
						count = jsonObject.getIntValue("listCount");
						if (count > 0) {
							pagecount = (count + pageSize - 1) / pageSize; // 总页数;
						}
					}
					JSONArray arr = jsonObject.getJSONArray("cjrqList");
					for (int j = 0; arr != null && j < arr.size(); j++) {
						JSONObject obj = arr.getJSONObject(j);
						
						int code = this.highMsg(cookie, obj.getString("qd"), obj.getIntValue("jlbdpid"), obj.getString("userid"), obj.getString("dpmc"));
						if (code == 0) {
							log.info("------- high消息发生成功：渠道：" + obj.getString("qd") + "，俱乐部店铺id：" + obj.getIntValue("jlbdpid") + "，userid：" + obj.getString("userid") + "，店铺名称：" + obj.getString("dpmc"));
							continue;
						}
						if (code == 101) {
							this.chongdeng(cookie, obj.getString("qd"), obj.getIntValue("jlbdpid"), obj.getString("userid"), obj.getIntValue("yhid"));
						}
					}
				}
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		log.info("-------------完成");
	}

	/**
	 * allBiInfo 所有采集店铺
	 *
	 */
	private String allBiInfo(String cookie, int pagenow, int pagesize, String qd, String startDate, String endDate) {
		HttpPost httpPost = null;
		HttpResponse httpResponse = null;
		try {
			String url = "https://cjglxt.ecbis.cn/api/cjrq/getCjrqList";
			CloseableHttpClient httpClient = HttpClients.custom().build();
			httpPost = new HttpPost(url);
			httpPost.setHeader("accept", "*/*");
			httpPost.setHeader("accept-encoding", "gzip, deflate, br, zstd");
			httpPost.setHeader("accept-language", "zh-CN,zh;q=0.9,en;q=0.8");
			httpPost.setHeader("Content-Type", "application/x-www-form-urlencoded;charset=UTF-8");
			httpPost.setHeader("Cookie", cookie);
			httpPost.setHeader("Host", "cjglxt.ecbis.cn");
			httpPost.setHeader("origin", "https://cjglxt.ecbis.cn");
			httpPost.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
			RequestConfig.Builder requestConfig = RequestConfig.custom().setSocketTimeout(30000).setConnectTimeout(30000).setConnectionRequestTimeout(30000);
			httpPost.setConfig(requestConfig.build());

			// pagesize=10&pagenow=1&yhid=&userid=&jlbdpid=&dpmc=&sfty=0&sfyc=0&qd=TB&startDate=2024-05-01&endDate=2024-06-30
			StringEntity param = new StringEntity("pagesize=" + pagesize + "&pagenow=" + pagenow + "&yhid=&userid=&jlbdpid=&dpmc=&sfty=0&sfyc=0&qd=" + qd + "&startDate=" + startDate + "&endDate=" + endDate);
			httpPost.setEntity(param);

			httpResponse = httpClient.execute(httpPost);
			HttpEntity entity = httpResponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			// System.out.println(body);
			if (!StringUtil.isJson2(body)) {
				log.info("接口返回失败，不是json！-->" + body);
				return null;
			}
			JSONObject jsonObject = JSONObject.parseObject(body);
			if (!jsonObject.containsKey("code") || jsonObject.getIntValue("code") != 0) {
				log.info("接口返回失败！-->" + body);
				return null;
			}
			return body;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return null;
		} finally {
			httpPost.releaseConnection();
		}
	}
	
	/**
	 * allBiInfo 所有采集店铺
	 *
	 */
	private String ckck(String cookie, String qd, int jlbdpid, String userid, String dpmc) {
		HttpPost httpPost = null;
		HttpResponse httpResponse = null;
		try {
			String url = "https://cjglxt.ecbis.cn/api/cjrq/ckck";
			CloseableHttpClient httpClient = HttpClients.custom().build();
			httpPost = new HttpPost(url);
			httpPost.setHeader("accept", "*/*");
			httpPost.setHeader("accept-encoding", "gzip, deflate, br, zstd");
			httpPost.setHeader("accept-language", "zh-CN,zh;q=0.9,en;q=0.8");
			httpPost.setHeader("Content-Type", "application/x-www-form-urlencoded;charset=UTF-8");
			httpPost.setHeader("Cookie", cookie);
			httpPost.setHeader("Host", "cjglxt.ecbis.cn");
			httpPost.setHeader("origin", "https://cjglxt.ecbis.cn");
			httpPost.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
			RequestConfig.Builder requestConfig = RequestConfig.custom().setSocketTimeout(30000).setConnectTimeout(30000).setConnectionRequestTimeout(30000);
			httpPost.setConfig(requestConfig.build());
			
			// qd=TB&jlbdpid=5001903&userid=2601369044&dpmc=%E5%88%AB%E4%BA%BA%E5%AE%B6%E7%94%9F%E6%B4%BB%E9%A6%86qd=TB&jlbdpid=5001903&userid=2601369044&dpmc=%E5%88%AB%E4%BA%BA%E5%AE%B6%E7%94%9F%E6%B4%BB%E9%A6%86
			StringEntity param = new StringEntity("qd=" + qd + "&jlbdpid=" + jlbdpid + "&userid=" + userid + "&dpmc=" + dpmc);
			httpPost.setEntity(param);
			
			httpResponse = httpClient.execute(httpPost);
			HttpEntity entity = httpResponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			// System.out.println(body);
			if (!StringUtil.isJson2(body)) {
				log.info("接口返回失败，不是json！-->" + body);
				return null;
			}
			JSONObject jsonObject = JSONObject.parseObject(body);
			if (!jsonObject.containsKey("code") || jsonObject.getIntValue("code") != 0) {
				log.info("-------------------- 当前店铺没有cookieKey，渠道：" + qd + "，俱乐部店铺id：" + jlbdpid + "，userid：" + userid + "，店铺名称：" + dpmc);
				log.info("接口返回失败！-->" + body);
				return null;
			}
			return body;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return null;
		} finally {
			httpPost.releaseConnection();
		}
	}
	
	/**
	 * highMsg 发生high消息
	 *
	 */
	private int highMsg(String cookie, String qd, int jlbdpid, String userid, String dpmc) {
		HttpPost httpPost = null;
		HttpResponse httpResponse = null;
		try {
			String url = "https://cjglxt.ecbis.cn/api/cjrq/fsxx";
			CloseableHttpClient httpClient = HttpClients.custom().build();
			httpPost = new HttpPost(url);
			httpPost.setHeader("accept", "*/*");
			httpPost.setHeader("accept-encoding", "gzip, deflate, br, zstd");
			httpPost.setHeader("accept-language", "zh-CN,zh;q=0.9,en;q=0.8");
			httpPost.setHeader("Content-Type", "application/x-www-form-urlencoded;charset=UTF-8");
			httpPost.setHeader("Cookie", cookie);
			httpPost.setHeader("Host", "cjglxt.ecbis.cn");
			httpPost.setHeader("origin", "https://cjglxt.ecbis.cn");
			httpPost.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
			RequestConfig.Builder requestConfig = RequestConfig.custom().setSocketTimeout(30000).setConnectTimeout(30000).setConnectionRequestTimeout(30000);
			httpPost.setConfig(requestConfig.build());
			
			StringEntity param = new StringEntity("qd=" + qd + "&jlbdpid=" + jlbdpid + "&userid=" + userid + "&dpmc=" + dpmc);
			httpPost.setEntity(param);
			
			httpResponse = httpClient.execute(httpPost);
			HttpEntity entity = httpResponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			// System.out.println(body);
			if (!StringUtil.isJson2(body)) {
				log.info("接口返回失败，不是json！-->" + body);
				return -1;
			}
			JSONObject jsonObject = JSONObject.parseObject(body);
			if (jsonObject.containsKey("code")) {
				// {"code":"0","content":"成功","data":{"messageId":"AC1A344AEE7F78E67E0A4A3598A608DF","topic":"zdcj"}}
				if (jsonObject.getIntValue("code") == 0) {
					return jsonObject.getIntValue("code");
				}
				// {"code":"101","content":"查看ck失败"}
				if (jsonObject.getIntValue("code") == 101) {
					log.info("-------------------- 当前店铺不能发送high消息，没有ck，渠道：" + qd + "，俱乐部店铺id：" + jlbdpid + "，userid：" + userid + "，店铺名称：" + dpmc + "，接口内容：" + body);
					return jsonObject.getIntValue("code");
				}
				log.info("-------------------- 1111当前店铺不能发送high消息，渠道：" + qd + "，俱乐部店铺id：" + jlbdpid + "，userid：" + userid + "，店铺名称：" + dpmc);
				log.info("接口返回失败！-->" + body);
				return jsonObject.getIntValue("code");
			}
			log.info("-------------------- 2222当前店铺不能发送high消息，渠道：" + qd + "，俱乐部店铺id：" + jlbdpid + "，userid：" + userid + "，店铺名称：" + dpmc);
			log.info("接口返回失败！-->" + body);
			return -99;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return -99;
		} finally {
			httpPost.releaseConnection();
		}
	}
	
	/**
	 * chongdeng 重新登录
	 *
	 */
	private int chongdeng(String cookie, String qd, int jlbdpid, String userid, int yhid) {
		HttpPost httpPost = null;
		HttpResponse httpResponse = null;
		try {
			String url = "https://cjglxt.ecbis.cn/api/cjrq/chongdeng";
			CloseableHttpClient httpClient = HttpClients.custom().build();
			httpPost = new HttpPost(url);
			httpPost.setHeader("accept", "*/*");
			httpPost.setHeader("accept-encoding", "gzip, deflate, br, zstd");
			httpPost.setHeader("accept-language", "zh-CN,zh;q=0.9,en;q=0.8");
			httpPost.setHeader("Content-Type", "application/x-www-form-urlencoded;charset=UTF-8");
			httpPost.setHeader("Cookie", cookie);
			httpPost.setHeader("Host", "cjglxt.ecbis.cn");
			httpPost.setHeader("origin", "https://cjglxt.ecbis.cn");
			httpPost.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
			RequestConfig.Builder requestConfig = RequestConfig.custom().setSocketTimeout(30000).setConnectTimeout(30000).setConnectionRequestTimeout(30000);
			httpPost.setConfig(requestConfig.build());
			
			StringEntity param = new StringEntity("qd=" + qd + "&jlbdpid=" + jlbdpid + "&userid=" + userid + "&yhid=" + yhid);
			httpPost.setEntity(param);
			
			httpResponse = httpClient.execute(httpPost);
			HttpEntity entity = httpResponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			// System.out.println(body);
			if (!StringUtil.isJson2(body)) {
				log.info("接口返回失败，不是json！-->" + body);
				return -1;
			}
			JSONObject jsonObject = JSONObject.parseObject(body);
			if (jsonObject.containsKey("code")) {
				// {"code":"0","content":"设置成功"}
				if (jsonObject.getIntValue("code") == 0) {
					return jsonObject.getIntValue("code");
				}
				// {"code":"101","content":"子账号异常"}
				if (jsonObject.getIntValue("code") == 101) {
					log.info("-------------------- 当前店铺不能重新登录，可能原因今日未登录或者子账号已经异常，渠道：" + qd + "，俱乐部店铺id：" + jlbdpid + "，userid：" + userid + "，yhid：" + yhid + "，接口内容：" + body);
					return jsonObject.getIntValue("code");
				}
				log.info("-------------------- 1111当前店铺不能重新登录，渠道：" + qd + "，俱乐部店铺id：" + jlbdpid + "，userid：" + userid + "，yhid：" + yhid);
				log.info("接口返回失败！-->" + body);
				return jsonObject.getIntValue("code");
			}
			log.info("-------------------- 2222当前店铺不能重新登录，渠道：" + qd + "，俱乐部店铺id：" + jlbdpid + "，userid：" + userid + "，yhid：" + yhid);
			log.info("接口返回失败！-->" + body);
			return -99;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return -99;
		} finally {
			httpPost.releaseConnection();
		}
	}

}
