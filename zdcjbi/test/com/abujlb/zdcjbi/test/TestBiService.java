package com.abujlb.zdcjbi.test;

import com.abujlb.AbujlbBeanFactory;
import com.abujlb.BaseTest;
import com.abujlb.zdcjbi.bean.msg.JysjMsg;
import com.abujlb.zdcjbi.service.BiService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/10 17:16
 * -
 */
public class TestBiService extends BaseTest {

    @Autowired
    private AbujlbBeanFactory abujlbBeanFactory;

    @Test
    public void process() {
        String cookieKey = "ck_7a7613d1fa724f429b1857b7dc81c7fc";
        JysjMsg jysjMsg = new JysjMsg();
        //1登录/上午补发 2下午补采
        jysjMsg.setType(1);
        jysjMsg.setCookieKey(cookieKey);
        BiService biService = abujlbBeanFactory.getBean(BiService.class);
        biService.process(jysjMsg);
    }

    @Test
    public void batch() {
        List<String> list = new ArrayList<>();
        list.add("ck_a392646dc933465d91cd0429e67ff7ba");
        list.add("ck_446b8607435d4e218fb4fb1ed77b5598");

        BiService biService = abujlbBeanFactory.getBean(BiService.class);
        for (String cookieKey : list) {
            System.out.println("当前key-----》" + cookieKey);
            JysjMsg jysjMsg = new JysjMsg();
            //1登录/上午补发 2下午补采
            jysjMsg.setType(1);
            jysjMsg.setCookieKey(cookieKey);
            biService.process(jysjMsg);
        }
        System.out.println("**************** 完成");
    }

}
