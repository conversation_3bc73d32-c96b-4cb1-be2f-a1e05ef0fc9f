package com.abujlb.zdcjbi.test;

import com.abujlb.BaseTest;
import com.abujlb.zdcjbi.bean.BiInfo;
import com.abujlb.zdcjbi.bean.BiYhdp;
import com.abujlb.zdcjbi.bean.DataprobiMsg;
import com.abujlb.zdcjbi.cookie.AbujlbCookieStore;
import com.abujlb.zdcjbi.cookie.Cjzh;
import com.abujlb.zdcjbi.http.DataUploader;
import com.abujlb.zdcjbi.http.SycmPerformanceHttpClient;
import com.abujlb.zdcjbi.processes.*;
import com.abujlb.zdcjbi.thread.BiThreadLocalObjects;
import com.abujlb.zdcjbi.util.ShopInfoUtil;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.lang.reflect.InvocationTargetException;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/11 9:12
 */
public class TestBiSycmPerformanceProcesses extends BaseTest {

    @Autowired
    private AbujlbCookieStore abujlbCookieStore;

    @Autowired
    private DefaultBiSycmPerformanceCoreMonitorProcess defaultBiSycmPerformanceCoreMonitorProcess;
    @Autowired
    private DefaultBiSycmPerformanConfigProcess defaultBiSycmPerformanConfigProcess;
    @Autowired
    private DefaultBiSycmPerformanCustomerJdpjfxProcess defaultBiSycmPerformanCustomerJdpjfxProcess;
    @Autowired
    private DefaultBiSycmPerformanCustomerZxjdfxProcess defaultBiSycmPerformanCustomerZxjdfxProcess;
    @Autowired
    private DefaultBiSycmPerformanServiceAccountProcess defaultBiSycmPerformanServiceAccountProcess;
    @Autowired
    private DefaultBiSycmPerformanCustomerShfxShtkclProcess defaultBiSycmPerformanCustomerShfxShtkclProcess;
    @Autowired
    private DefaultBiSycmPerformanDefailSpxsmxProcess defaultBiSycmPerformanDefailSpxsmxProcess;
    @Autowired
    private DefaultBiSycmPerformanCustomerYjfxhzProcess defaultBiSycmPerformanCustomerYjfxhzProcess;
    @Autowired
    private DefaultBiSycmPerformanShopYjfxHzKfProcess defaultBiSycmPerformanShopYjfxHzKfProcess;
    String cookiekey = "ck_7bbe2c9cfdd246d19d575db2cf9b7f28";
    Cjzh cjzh = null;
    BiInfo biInfo = null;

    @Before
    public void init() {
        cjzh = abujlbCookieStore.getCjzhForKey(cookiekey);
        if (cjzh == null) {
            return;
        }
        cjzh.setMsgType(1);
        biInfo = DataUploader.getBiInfoJlbdpid(cjzh.getUserid(), cjzh.getJlbdpid());
        if (biInfo == null) {
            return;
        }
        ShopInfoUtil.initShopOverSeas(cjzh);
        BiThreadLocalObjects.addCjzh(cjzh);
        BiThreadLocalObjects.addBiInfo(biInfo);

        DataprobiMsg dataprobiMsg = new DataprobiMsg();
        try {
            dataprobiMsg.initial();
        } catch (NoSuchMethodException e) {
            throw new RuntimeException(e);
        } catch (InvocationTargetException e) {
            throw new RuntimeException(e);
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        }

        List<BiYhdp> biYhdps = DataUploader.getBiYhdpListJst(cjzh.getUserid() + "", biInfo.getQd());
        if (CollectionUtils.isEmpty(biYhdps)) {
            return;
        }
        cjzh.setBiYhdps(biYhdps);

        BiThreadLocalObjects.addMsgBean(dataprobiMsg);
    }

    //询单时长配置
    @Test
    public void sycm_performance_config() {
        defaultBiSycmPerformanConfigProcess.cjAll();
    }

    //旺旺列表
    @Test
    public void sycm_performance_service_account() {
        defaultBiSycmPerformanServiceAccountProcess.cjAll();
    }

    //核心监控
    @Test
    public void sycm_performance_core_monitor() {
        BiThreadLocalObjects.getBiInfo().getCjrq().setSycm_performance_core_monitor("2024-09-26");
        BiThreadLocalObjects.getBiInfo().getCjrq().setSycm_performance_core_monitor_dp("2024-09-26");
//        BiThreadLocalObjects.getBiInfo().getCjrq().setSycm_performance_core_monitor_dp(BiDateUtil.getLastday());
        defaultBiSycmPerformanceCoreMonitorProcess.cjAll();
        System.out.println(BiThreadLocalObjects.getBiInfo().getCjrq().getSycm_performance_core_monitor());
        System.out.println(BiThreadLocalObjects.getBiInfo().getCjrq().getSycm_performance_core_monitor_dp());
    }

    //客服绩效 业绩分析 汇总分析
    @Test
    public void sycm_performance_customer_summary() {
        BiThreadLocalObjects.getBiInfo().getCjrq().setSycm_user_sale_summary("2024-09-26");
        defaultBiSycmPerformanCustomerYjfxhzProcess.cjAll();
        System.out.println(BiThreadLocalObjects.getBiInfo().getCjrq().getSycm_user_sale_summary());
    }

    //接待评价分析
    @Test
    public void sycm_performance_customer_jdpjfx() {
        BiThreadLocalObjects.getBiInfo().getBiDpConfig().setSycm_performance(1);
        BiThreadLocalObjects.getBiInfo().getCjrq().setSycm_spcecial_rea("2024-09-27");
        defaultBiSycmPerformanCustomerJdpjfxProcess.cjAll();
    }

    //咨询接待分析
    @Test
    public void sycm_performance_customer_zxjdfx() {
        BiThreadLocalObjects.getBiInfo().getBiDpConfig().setSycm_performance(1);
        BiThreadLocalObjects.getBiInfo().getCjrq().setSycm_spcecial_cra("2024-09-27");
        defaultBiSycmPerformanCustomerZxjdfxProcess.cjAll();
    }

    //售后分析-售后退款处理
    @Test
    public void sycm_performance_customer_shfx_shtkcl() {
        BiThreadLocalObjects.getBiInfo().getCjrq().setSycm_performance_refund("2024-09-18");
        defaultBiSycmPerformanCustomerShfxShtkclProcess.cjAll();
    }

    @Test
    public void sycm_performance_detail_spxsmx() {
        BiThreadLocalObjects.getBiInfo().getCjrq().setSycm_performance_item_sale_detail("2024-09-18");
        defaultBiSycmPerformanDefailSpxsmxProcess.cjAll();
    }

    @Test
    public void sycm_performance_shop_sale_summary(){
        BiThreadLocalObjects.getBiInfo().getCjrq().setSycm_performance_shop_sale_summary("2024-09-30");
        defaultBiSycmPerformanShopYjfxHzKfProcess.cjAll();
    }

    @Test
    public void delete() {
        SycmPerformanceHttpClient.sycmPerformanceItemSaleDetailExcelDelete(cjzh, "8215360");
    }
}