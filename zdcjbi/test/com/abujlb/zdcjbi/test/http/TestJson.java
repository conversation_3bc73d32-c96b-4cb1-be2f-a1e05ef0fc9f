package com.abujlb.zdcjbi.test.http;

import com.abujlb.zdcjbi.util.FileToJson;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;

import java.io.File;

public class TestJson {

    public static void main(String[] args) {
        String str = FileToJson.toJson(new File("D:\\company\\tinghai\\temp\\bzj.json"));

        JSONArray jsonArray = JSONArray.parseArray(str);
        String s1 = "其他支出-交易赔付（保证金扣款）";
        String s2 = "交易退款-保证金退款";

        long count1 = 0;
        long count2 = 0;
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            String bizDesc = jsonObject.getString("bizDesc");
            System.out.println("bizDesc = " + bizDesc);
            if (StringUtils.isBlank(bizDesc) || !bizDesc.contains("|")) {
                continue;
            }
            String[] split = bizDesc.split("\\|");

            String desc = split[1];
            System.out.println("desc = " + desc);
            if (s1.equalsIgnoreCase(desc)) {
                count1++;
            } else if (s2.equalsIgnoreCase(desc)) {
                count2++;
            }
        }
        System.out.println("count1 = " + count1);
        System.out.println("count2 = " + count2);
    }
}
