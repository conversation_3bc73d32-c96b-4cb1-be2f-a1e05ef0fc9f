package com.abujlb.zdcjbi.test.http;

import com.abujlb.BaseTest;
import com.abujlb.zdcjbi.cookie.AbujlbCookieStore;
import com.abujlb.zdcjbi.cookie.Cjzh;
import com.abujlb.zdcjbi.http.DmpHttpClient;
import com.abujlb.zdcjbi.util.ShopInfoUtil;
import com.alibaba.fastjson.JSONArray;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class TestDmpHttpClient extends BaseTest {


    @Autowired
    private AbujlbCookieStore abujlbCookieStore;

    @Test
    public void list() {
        String cookieKey = "ck_d5914f6eed064d2dbb2feedb473eaff0";
        Cjzh cjzh = abujlbCookieStore.getCjzhForKey(cookieKey);
        if (cjzh != null) {
            ShopInfoUtil.initShopOverSeas(cjzh);

            JSONArray hkBzjList = DmpHttpClient.dpmhpdcqddplb(cjzh, "2024-07-28");
            System.out.println("hkBzjList = " + hkBzjList);
        }
    }

    @Test
    public void maxRq(){
        String cookieKey = "ck_d5914f6eed064d2dbb2feedb473eaff0";
        Cjzh cjzh = abujlbCookieStore.getCjzhForKey(cookieKey);
        if (cjzh != null) {
            ShopInfoUtil.initShopOverSeas(cjzh);

            String maxRq = DmpHttpClient.getMaxRq(cjzh);
            System.out.println("maxRq = " + maxRq);
        }
    }
}
