package com.abujlb.zdcjbi.test.http;

import com.abujlb.BaseTest;
import com.abujlb.zdcjbi.cookie.AbujlbCookieStore;
import com.abujlb.zdcjbi.cookie.Cjzh;
import com.abujlb.zdcjbi.http.PjylHttpClient;
import com.abujlb.zdcjbi.util.ShopInfoUtil;
import com.alibaba.fastjson.JSONArray;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class TestPjylHttpClient extends BaseTest {


    @Autowired
    private AbujlbCookieStore abujlbCookieStore;

    @Test
    public void redPacketList() {
        String cookieKey = "ck_e0a32cc0e140444f802330820b124331";
        Cjzh cjzh = abujlbCookieStore.getCjzhForKey(cookieKey);
        if (cjzh != null) {
            ShopInfoUtil.initShopOverSeas(cjzh);

            JSONArray hkBzjList = PjylHttpClient.redPacketList(cjzh,"2024-04-01","2024-04-30");
            System.out.println("hkBzjList = " + hkBzjList);
        }
    }
}
