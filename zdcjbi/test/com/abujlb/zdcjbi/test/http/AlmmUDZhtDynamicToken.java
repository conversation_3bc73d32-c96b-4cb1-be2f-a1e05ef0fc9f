package com.abujlb.zdcjbi.test.http;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import common.Logger;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * @packageName com.abujlb.zdcjbi.test.http
 * @ClassName Tess
 * @Description 阿里妈妈UD智慧投
 * <AUTHOR>
 * @Date 2024/11/5
 */
public class AlmmUDZhtDynamicToken {
    private static final Logger log = Logger.getLogger(AlmmUDZhtDynamicToken.class);

    /**
     * 进制转换
     *
     * @param num 待转换的数字
     * @param b   目标进制
     * @return 转换后的字符串
     */
    public static String baseN(long num, int b) {
        if (num == 0) return "0";
        String chars = "0123456789abcdefghijklmnopqrstuvwxyz";
        StringBuilder result = new StringBuilder();
        while (num > 0) {
            result.insert(0, chars.charAt((int) (num % b)));
            num = num / b;
        }
        return result.toString();
    }

    /**
     * 解密核心逻辑
     *
     * @param key 输入字符串
     * @return 处理后的字符串
     */
    public static String parseCore(String key) {
        StringBuilder result = new StringBuilder();
        for (char c : key.toCharArray()) {
            result.append((int) c * 4);
        }
        return result.toString();
    }

    /**
     * 获取动态令牌
     *
     * @param seedToken 种子令牌
     * @param timestr   时间戳
     * @return 动态令牌
     */
    public static String getDynamicToken(String seedToken, long timestr) {
        String a1 = "";
        if (StrUtil.isNotBlank(seedToken)) {
            a1 = seedToken.substring(3, 11);
        }
        String a2 = baseN(timestr, 36);
        return parseCore(a1) + parseCore(a2);
    }


    public static Boolean hasAuth(String cookie, String dynamicToken, Long timeStr) {
        JSONObject req = JSONUtil.createObj();
        req.set("timeStr", timeStr);
        req.set("dynamicToken", dynamicToken);
        req.set("csrfId", "");
        req.set("bizCode", "udOneBP");
        HttpResponse res = HttpUtil.createPost("https://ud.alimama.com/member/checkAccess.json")
                .addHeaders(initHead(cookie, "https://ud.alimama.com/index.html?mxredirectUrl=https%3A%2F%2Fud.alimama.com%2Fredirect.action%3FredirectURL%3Dhttps%253A%252F%252Fud.alimama.com%252Findex.html"))
                .form(req).execute();
        if (200 != res.getStatus()) {
            log.error("获取权限接口请求失败！" + res.body());
            return false;
        }
        JSONObject resJson = JSONUtil.parseObj(res.body());
        JSONObject errorInfo = resJson.getByPath("$.data.errorInfo", JSONObject.class);
        return Objects.isNull(errorInfo);
    }


    /**
     * 获取优惠券
     *
     * @param cookie: return Boolean
     * <AUTHOR>
     * @date 2024/11/7
     * {@link Boolean}
     * @description
     */
    public static JSONArray getPromotionList(String cookie) {
        HttpResponse res = HttpUtil.createGet("https://stlacct.alimama.com/settleAccount/promotion/new/getPromotionList.json?csrfId=15122e96a3c6d11b62e2cc8acd2f286f_1_1_1&pageIndex=1&number=100&bizCode=udOneBP")
                .addHeaders(initHead(cookie, "https://ud.alimama.com")).execute();
        if (200 != res.getStatus()) {
            log.error("获取优惠券接口请求失败！" + res.body());
            return null;
        }
        JSONObject resJson = JSONUtil.parseObj(res.body());
        return resJson.getByPath("$.data.list", JSONArray.class);
    }


    private static Map<String, String> initHead(String cookie, String referer) {
        Map<String, String> headers = new HashMap<>();
        headers.put("accept", "application/json, text/javascript, */*; q=0.01");
        headers.put("accept-encoding", "gzip, deflate, br, zstd");
        headers.put("accept-language", "zh-CN,zh;q=0.9");
        if (StrUtil.isNotBlank(cookie)) {
            headers.put("cookie", cookie);
        }
        headers.put("content-type", "application/x-www-form-urlencoded; charset=UTF-8");
        headers.put("origin", "https://ud.alimama.com");
        headers.put("priority", "u=1, i");
        headers.put("referer", referer);
        headers.put("sec-ch-ua", "\"Not)A;Brand\";v=\"99\", \"Google Chrome\";v=\"127\", \"Chromium\";v=\"127\"");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("sec-ch-ua-platform", "\"Windows\"");
        headers.put("sec-fetch-dest", "empty");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-site", "same-origin");
        headers.put("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
        return headers;
    }

    public static void main(String[] args) {

//        String seedToken = "";
//        long timestamp = DateUtil.current();
//        String token = ParseDynamicToken.getDynamicToken(seedToken, timestamp);
////        System.out.println("示例1 - 生成的令牌: " + token);
//
//        String cookie = "dnk=; t=d94b0f2ca60eb78edd23e1c7487776a1; lgc=; _tb_token_=f34e3ebe97e8e; cookie2=11e7383751fec92922132c99b3bdaae2; _nk_=; cna=w2uwHwfrAEABASQOA2MpxrvQ; xlly_s=1; cancelledSubSites=empty; uc1=cookie14=UoYdXSekeEQDAw%3D%3D&cookie21=W5iHLLyFfoaZ; lid=carbinkoneer%E6%97%97%E8%88%B0%E5%BA%97%3A%E6%95%B0%E6%8D%AE%E5%88%86%E6%9E%90; unb=2217488939377; sgcookie=E100NgUeCgyQJk%2Bsamwstx8CX%2FDSrnLjLZGn0y46%2BaNVP93wFY9FF6W9B0DMGahP4v6igv4E9ax%2BQB68LH91AIHwCKKCPalFq7nvMcp7r1T%2BEe1KuyvYQz3lnX2Pxswd3sIZ; csg=e848a97e; sn=carbinkoneer%E6%97%97%E8%88%B0%E5%BA%97%3A%E6%95%B0%E6%8D%AE%E5%88%86%E6%9E%90; tfstk=f3WofEx1D_R5QIhDmSJ7tmtothVAk09BvwHpJpLUgE8jw9IUNk2hkwA8VMz5-9YVrBBLVvCmxG_DpTPkUpYh8ZGJ9epy-wjOYUddKMMHYM7QVeE7MgsWdpzTW92OVgaCZQZ4XQoq0itoaafhSTSWdpzxDvPTIg_wyi6FLprD3HKoapRrUxrDxhYeLUREuj-wup8F8BSq33tB40ReLKrDAE8eLpks8785LtW4PVKGku6SXKtkZeDpuvbF7AKJ7VLxKvYkrQcR4EDELtfvY_qJz5ivRLL5V3bQCY9yt6WJ3Tz4Ld51o9AwIWEWQTsF16SjrkYPDa6MUO4oendHrsWyiDkFbg8Di6burbTPwZA60Io3CnQ9ug6PikgV4NLkUnRYQlj2TsBW1TUqEd512LCF7rhJz1xF4xlq_Vs-dnrd3XGBantDW5EJtEurB2j3mocaAQ-XVFE0mXGBantDWoqm_JOyc3TO.; isg=BJaWRNBh7UuVz9nsTa25Bu5H50yYN9pxB1ewHgDrI3lKwxVdaceWgaP1W18v69KJ";
//        Boolean b = hasAuth(cookie, token, timestamp);
//        System.out.println(b);

//        JSONArray promotionList = getPromotionList(cookie);
//        System.out.println(promotionList);
    }
}
