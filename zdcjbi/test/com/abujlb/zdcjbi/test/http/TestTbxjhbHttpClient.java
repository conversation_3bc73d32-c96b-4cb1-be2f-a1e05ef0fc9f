package com.abujlb.zdcjbi.test.http;

import com.abujlb.BaseTest;
import com.abujlb.zdcjbi.auth.BiAuthResult;
import com.abujlb.zdcjbi.cookie.AbujlbCookieStore;
import com.abujlb.zdcjbi.cookie.Cjzh;
import com.abujlb.zdcjbi.http.TbxjhbHttpClient;
import com.abujlb.zdcjbi.util.ShopInfoUtil;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class TestTbxjhbHttpClient extends BaseTest {


    @Autowired
    private AbujlbCookieStore abujlbCookieStore;

    @Test
    public void redPacketList() {
        String cookieKey = "ck_dbea45b2670e48f9bc67f43b446f6f77";
        Cjzh cjzh = abujlbCookieStore.getCjzhForKey(cookieKey);
        if (cjzh != null) {
            ShopInfoUtil.initShopOverSeas(cjzh);

            BiAuthResult biAuthResult = TbxjhbHttpClient.hasPower(cjzh);
            System.out.println("biAuthResult = " + biAuthResult);

            String daysum = TbxjhbHttpClient.daysum(cjzh, "2024-07-01");
            System.out.println("daysum = " + daysum);
        }
    }
}
