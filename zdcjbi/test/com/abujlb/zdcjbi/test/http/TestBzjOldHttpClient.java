package com.abujlb.zdcjbi.test.http;

import com.abujlb.BaseTest;
import com.abujlb.zdcjbi.cookie.AbujlbCookieStore;
import com.abujlb.zdcjbi.cookie.Cjzh;
import com.abujlb.zdcjbi.http.BzjOldHttpClient;
import com.abujlb.zdcjbi.util.ShopInfoUtil;
import com.alibaba.fastjson.JSONArray;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class TestBzjOldHttpClient extends BaseTest {


    @Autowired
    private AbujlbCookieStore abujlbCookieStore;

    @Test
    public void bzjOld() {
        String cookieKey = "ck_ff5e15c415df4ca5aca26ab9f0735c81";
        Cjzh cjzh = abujlbCookieStore.getCjzhForKey(cookieKey);
        if (cjzh != null) {
            ShopInfoUtil.initShopOverSeas(cjzh);

            JSONArray hkBzjList = BzjOldHttpClient.getBzjList(cjzh, "2024-07-07","TB");
            System.out.println("hkBzjList = " + hkBzjList);
        }
    }

    @Test
    public void bzj_new_createTask(){
        String cookieKey = "ck_ff5e15c415df4ca5aca26ab9f0735c81";
        Cjzh cjzh = abujlbCookieStore.getCjzhForKey(cookieKey);
        if (cjzh != null) {
            ShopInfoUtil.initShopOverSeas(cjzh);

            JSONArray hkBzjList = BzjOldHttpClient.getBzjList(cjzh, "2024-07-07","TB");
            System.out.println("hkBzjList = " + hkBzjList);
        }
    }
}
