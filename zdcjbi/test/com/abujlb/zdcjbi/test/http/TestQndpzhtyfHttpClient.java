package com.abujlb.zdcjbi.test.http;

import com.abujlb.BaseTest;
import com.abujlb.zdcjbi.bean.QndpZhtyf;
import com.abujlb.zdcjbi.cookie.AbujlbCookieStore;
import com.abujlb.zdcjbi.cookie.Cjzh;
import com.abujlb.zdcjbi.http.QndpZhtyfClient;
import com.abujlb.zdcjbi.util.ShopInfoUtil;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class TestQndpzhtyfHttpClient extends BaseTest {


    @Autowired
    private AbujlbCookieStore abujlbCookieStore;

    @Test
    public void redPacketList() {
        String cookieKey = "ck_aac19634e5034892bee83e74871bc344";
        Cjzh cjzh = abujlbCookieStore.getCjzhForKey(cookieKey);
        if (cjzh != null) {
            ShopInfoUtil.initShopOverSeas(cjzh);

            QndpZhtyf qndpZhtyf = QndpZhtyfClient.getQndpZhtyf("TB",cjzh,"20240629","20240728");
            System.out.println("qndpZhtyf = " + qndpZhtyf);
        }
    }
}
