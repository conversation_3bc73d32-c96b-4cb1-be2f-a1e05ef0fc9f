package com.abujlb.zdcjbi.test.http;

import com.abujlb.BaseTest;
import com.abujlb.zdcjbi.cookie.AbujlbCookieStore;
import com.abujlb.zdcjbi.cookie.Cjzh;
import com.abujlb.zdcjbi.http.NewItemHttpClient;
import com.abujlb.zdcjbi.util.ShopInfoUtil;
import com.alibaba.fastjson.JSONArray;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class TestNewitemHttpClient extends BaseTest {


    @Autowired
    private AbujlbCookieStore abujlbCookieStore;

    @Test
    public void getOpItemList() {
        String cookieKey = "ck_37faff65180e4790b2ad5788126af350";
        Cjzh cjzh = abujlbCookieStore.getCjzhForKey(cookieKey);
        if (cjzh != null) {
            ShopInfoUtil.initShopOverSeas(cjzh);

            JSONArray hkBzjList = NewItemHttpClient.getOpItemList(cjzh);
            System.out.println("hkBzjList = " + hkBzjList);
        }
    }
}
