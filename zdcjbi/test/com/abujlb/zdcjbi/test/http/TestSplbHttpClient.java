package com.abujlb.zdcjbi.test.http;

import com.abujlb.BaseTest;
import com.abujlb.CommonConfig;
import com.abujlb.sqlite.SqliteDb;
import com.abujlb.zdcjbi.cookie.AbujlbCookieStore;
import com.abujlb.zdcjbi.cookie.Cjzh;
import com.abujlb.zdcjbi.http.SplbHttpClient;
import com.abujlb.zdcjbi.util.ShopInfoUtil;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.UUID;

public class TestSplbHttpClient extends BaseTest {


    @Autowired
    private AbujlbCookieStore abujlbCookieStore;

    @Test
    public void redPacketList() {
        String cookieKey = "ck_aac19634e5034892bee83e74871bc344";
        Cjzh cjzh = abujlbCookieStore.getCjzhForKey(cookieKey);
        if (cjzh != null) {
            ShopInfoUtil.initShopOverSeas(cjzh);

            SqliteDb sqliteDb = null;
            try {
                String filepath = CommonConfig.getString("tempdir") + UUID.randomUUID() + ".db";

                String INIT_SQL = "CREATE TABLE tb_splb(`bbid` VARCHAR(200),`data` TEXT,PRIMARY KEY(bbid)) ;";
                sqliteDb = new SqliteDb(filepath, INIT_SQL, null);
                cjzh.setTb(true);
                boolean splb = SplbHttpClient.getSplb(cjzh, sqliteDb);
                System.out.println("splb = " + splb);
                System.out.println("filepath = " + filepath);
            }catch (Exception e){
                e.printStackTrace();
            }
        }
    }
}
