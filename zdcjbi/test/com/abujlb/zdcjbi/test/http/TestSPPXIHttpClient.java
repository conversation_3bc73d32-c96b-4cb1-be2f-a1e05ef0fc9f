package com.abujlb.zdcjbi.test.http;

import com.abujlb.BaseTest;
import com.abujlb.zdcjbi.cookie.AbujlbCookieStore;
import com.abujlb.zdcjbi.cookie.Cjzh;
import com.abujlb.zdcjbi.http.SPPXIHttpClient;
import com.abujlb.zdcjbi.util.ShopInfoUtil;
import com.alibaba.fastjson.JSONArray;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class TestSPPXIHttpClient extends BaseTest {


    @Autowired
    private AbujlbCookieStore abujlbCookieStore;

    @Test
    public void redPacketList() {
        String cookieKey = "ck_c3e226b1da004cf89a947fdc5b7325f1";
        Cjzh cjzh = abujlbCookieStore.getCjzhForKey(cookieKey);
        if (cjzh != null) {
            ShopInfoUtil.initShopOverSeas(cjzh);

            JSONArray jsonArray = SPPXIHttpClient.goodsList(cjzh, "2024-07-29");
            System.out.println("jsonArray = " + jsonArray);
        }
    }
}
