package com.abujlb.zdcjbi.test.http;

import com.abujlb.BaseTest;
import com.abujlb.zdcjbi.auth.BiAuth;
import com.abujlb.zdcjbi.auth.BiAuthResult;
import com.abujlb.zdcjbi.cookie.AbujlbCookieStore;
import com.abujlb.zdcjbi.cookie.Cjzh;
import com.abujlb.zdcjbi.util.ShopInfoUtil;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class TestXedkHttpClient extends BaseTest {


    @Autowired
    private AbujlbCookieStore abujlbCookieStore;

    @Test
    public void redPacketList() {
        String cookieKey = "ck_aac19634e5034892bee83e74871bc344";
        Cjzh cjzh = abujlbCookieStore.getCjzhForKey(cookieKey);
        if (cjzh != null) {
            ShopInfoUtil.initShopOverSeas(cjzh);

            BiAuthResult xedk = BiAuth.xedk(cjzh);
            System.out.println("xedk = " + xedk);

//            XedkHttpClient.recordExport()
        }
    }
}
