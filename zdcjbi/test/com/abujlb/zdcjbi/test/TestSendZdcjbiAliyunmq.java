package com.abujlb.zdcjbi.test;

import com.abujlb.BaseTest;
import com.abujlb.mq.AliyunMq2;
import com.abujlb.mq.JobMessage;
import com.abujlb.zdcjbi.bean.BiInfo;
import com.abujlb.zdcjbi.bean.msg.JysjMsg;
import com.abujlb.zdcjbi.bean.msg.RlyqMsgBean;
import com.abujlb.zdcjbi.bean.msg.SycmflowMsgBean;
import com.abujlb.zdcjbi.cookie.AbujlbCookieStore;
import com.abujlb.zdcjbi.cookie.Cjzh;
import com.abujlb.zdcjbi.http.DataUploader;
import com.abujlb.zdcjbi.util.BiDateUtil;
import com.aliyun.openservices.ons.api.SendResult;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2022/8/1 11:18
 */
public class TestSendZdcjbiAliyunmq extends BaseTest {

    @Autowired
    private AliyunMq2 aliyunMq2;
    @Autowired
    private AbujlbCookieStore abujlbCookieStore;

    @Test
    public void send() {
        JysjMsg jysjMsg = new JysjMsg();
        jysjMsg.setCookieKey("ck_38acc924ea8e42f8a8e4dcd59483fe6f");
        //type为5时  spxg不检测list为空的情况
        // jysjMsg.setType(5);
        jysjMsg.setType(1);
        aliyunMq2.send("zdcjbi", "", new JobMessage("zdcjbiprocesser", jysjMsg));
    }

    @Test
    public void sendHigh() {
        JysjMsg jysjMsg = new JysjMsg();
        jysjMsg.setCookieKey("ck_7fea8eb2c5ed4b6a9b52112bf6828e6e");
        //type为5时  spxg不检测list为空的情况
        // jysjMsg.setType(5);
        jysjMsg.setType(1);
        aliyunMq2.send("zdcjbi_high", "", new JobMessage("zdcjbi_high_priority_processer", jysjMsg));
    }

    @Test
    public void sendRlyq() {
        RlyqMsgBean rlyqMsgBean = new RlyqMsgBean();
        rlyqMsgBean.setCookieKey("ck_5b409d37537746f28b985e791e438777");
        rlyqMsgBean.setType(1);
        rlyqMsgBean.setQd("TM");
        SendResult sendResult = aliyunMq2.send("zdcjcookiereback", UUID.randomUUID().toString(), new JobMessage("rlyqprocessor", rlyqMsgBean));
        System.out.println(sendResult.getMessageId());
    }

    @Test
    public void sendSycmFlowAll() {
        List<String> rqList = BiDateUtil.getBetweenDate("2024-07-01", "2024-09-05");
        List<BiInfo> allBiInfo = DataUploader.getAllBiInfo();
        for (BiInfo biInfo : allBiInfo) {
            if (!biInfo.getUserid().equalsIgnoreCase("2121402114")) {
                continue;
            }
            if (biInfo.getQd().equals("TM") || biInfo.getQd().equals("TB")) {
                String uuid = UUID.randomUUID().toString();
                SycmflowMsgBean sycmflowMsgBean = new SycmflowMsgBean();
                sycmflowMsgBean.setUuid(uuid);
                sycmflowMsgBean.setUserid(biInfo.getUserid());
                sycmflowMsgBean.setRqList(rqList);
                sycmflowMsgBean.setType(1);
                aliyunMq2.send("datapro", sycmflowMsgBean.getUuid(), new JobMessage("sycmflow_processor", sycmflowMsgBean));
            }
        }
    }

    @Test
    public void sendSycmFlowOne() {
        BiInfo biInfo = DataUploader.getBiInfoJlbdpid(0L, 0L);
        if (biInfo == null) {
            return;
        }
        if (biInfo.getQd().equals("TM") || biInfo.getQd().equals("TB")) {
            String uuid = UUID.randomUUID().toString();
            SycmflowMsgBean sycmflowMsgBean = new SycmflowMsgBean();
            sycmflowMsgBean.setUuid(uuid);
            sycmflowMsgBean.setUserid(biInfo.getUserid());
            sycmflowMsgBean.setRqList(Arrays.asList("2024-09-18", "2024-09-19"));
            sycmflowMsgBean.setType(1);
            aliyunMq2.send("datapro", sycmflowMsgBean.getUuid(), new JobMessage("sycmflow_processor", sycmflowMsgBean));
        }
    }

    @Test
    public void sendBatch() {
        List<String> cookieList = new ArrayList<>();
        cookieList.add("ck_de7e11f56e084e28ab83638bb26aa0e8");
        cookieList.add("ck_cd1c3231448f44a3a4fe7d5247c48f8c");
        cookieList.add("ck_066a910ad0a44842b4ab2748346a9f6b");
        cookieList.add("ck_0ba223311655488884ca55a383764dca");
        cookieList.add("ck_24c141458ba04a4cb8c8342a407bf977");
//        cookieList.add("") ;
//        cookieList.add("") ;
        for (int i = 0; i < cookieList.size(); i++) {
            JysjMsg jysjMsg = new JysjMsg();
            jysjMsg.setCookieKey(cookieList.get(i));
            jysjMsg.setType(1);
            SendResult sendResult = aliyunMq2.send("zdcjbi", "", new JobMessage("zdcjbiprocesser", jysjMsg));
            System.out.println(sendResult.getMessageId());
        }
    }


    @Test
    public void sendHighBatch() {
        List<String> cookieList = new ArrayList<>();

        cookieList.add("ck_7833a39cfe454d09a0976b3c3f19930f");
        for (int i = 0; i < cookieList.size(); i++) {
            JysjMsg jysjMsg = new JysjMsg();
            jysjMsg.setCookieKey(cookieList.get(i));
            jysjMsg.setType(1);
            aliyunMq2.send("zdcjbi_high", "", new JobMessage("zdcjbi_high_priority_processer", jysjMsg));
        }
    }

    @Test
    public void sendAll() {
        List<Cjzh> cjzhs = abujlbCookieStore.allCjzh(1, 0, "", 0);
        for (Cjzh cjzh : cjzhs) {
            JysjMsg jysjMsg = new JysjMsg();
            jysjMsg.setCookieKey(cjzh.getCk());
            jysjMsg.setType(2);
            SendResult sendResult = aliyunMq2.send("zdcjbi", "", new JobMessage("zdcjbi_high_priority_processer", jysjMsg));
            System.out.println(sendResult.getMessageId());
        }
    }

    @Test
    public void sendAll_TX_OTHER() {
        List<Cjzh> cjzhs = abujlbCookieStore.allCjzh(1, 0, "", 0);
        for (Cjzh cjzh : cjzhs) {
            JysjMsg jysjMsg = new JysjMsg();
            jysjMsg.setCookieKey(cjzh.getCk());
            jysjMsg.setType(1);
            SendResult sendResult = aliyunMq2.send("zdcjbitx_other", "", new JobMessage("zdcjbibzjprocesser", jysjMsg));
            System.out.println(sendResult.getMessageId());
        }
    }


    @Test
    public void sendBzj() {
        JysjMsg jysjMsg = new JysjMsg();
        jysjMsg.setCookieKey("ck_28fb445ec7dd4e98b21905a5a4e42380");
        jysjMsg.setType(1);
        SendResult sendResult = aliyunMq2.send("zdcjbitx_other", "", new JobMessage("zdcjbibzjprocesser", jysjMsg));
        System.out.println(sendResult.getMessageId());
    }
}
