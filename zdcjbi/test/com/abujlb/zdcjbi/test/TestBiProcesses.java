package com.abujlb.zdcjbi.test;

import com.abujlb.BaseTest;
import com.abujlb.zdcjbi.bean.BiInfo;
import com.abujlb.zdcjbi.bean.BiYhdp;
import com.abujlb.zdcjbi.bean.DataprobiMsg;
import com.abujlb.zdcjbi.cookie.AbujlbCookieStore;
import com.abujlb.zdcjbi.cookie.Cjzh;
import com.abujlb.zdcjbi.http.DataUploader;
import com.abujlb.zdcjbi.processes.*;
import com.abujlb.zdcjbi.service.BiService;
import com.abujlb.zdcjbi.tasks.GjbTask;
import com.abujlb.zdcjbi.thread.BiThreadLocalObjects;
import com.abujlb.zdcjbi.util.ShopInfoUtil;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.lang.reflect.InvocationTargetException;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/11 9:12
 */
public class TestBiProcesses extends BaseTest {

    @Autowired
    private AbujlbCookieStore abujlbCookieStore;
    @Autowired
    private DefaultBiSpxgProcess defaultBiSpxgProcess;
    @Autowired
    private DefaultBiCashRedPacketsProcess defaultBiCashRedPacketsProcess;
    @Autowired
    private DefaultBiRlyqProcess defaultBiRlyqProcess;
    @Autowired
    private DefaultBiSplbProcess defaultBiSplbProcess;
    @Autowired
    private DefaultBiNewItemProcess defaultBiNewItemProcess;
    @Autowired
    private SyjBiSpxgProcess syjBiSpxgProcess;
    @Autowired
    private DefaultBiSycmVersionProcess defaultBiSycmVersionProcess;
    @Autowired
    private DefaultBiJhsProcess defaultBiJhsProcess;
    @Autowired
    private DefaultBiSycmFlowProcess sycmFlowProcess;
    @Autowired
    private DefaultBiPxbProcess defaultBiPxbProcess;
    @Autowired
    private DefaultBiPpxxProcess defaultBiPpxxProcess;
    @Autowired
    private DefaultBiXedkProcess defaultBiXedkProcess;
    @Autowired
    private DefaultBiBzjProcess defaultBiBzjProcess;
    @Autowired
    private DefaultBiBzjOldProcess defaultBiBzjOldProcess;
    @Autowired
    private DefaultBiSycmFwCustomerSummaryProcess defaultBiSycmFwCustomerSummaryProcess;
    @Autowired
    private DefaultBiTbkProcess defaultBiTbkProcess;
    @Autowired
    private DefaultBiSycmLevelProcess defaultBiSycmLevelProcess;
    @Autowired
    private DefaultBiDmpHpdcQddplbProcess defaultBiDmpHpdcQddplbProcess;
    @Autowired
    private DefaultBiSycmLlzhYmfxProcess defaultBiSycmLlzhYmfxProcess;
    @Autowired
    private GjbTask gjbTask;
    @Autowired
    private DefaultBiPjylProcess defaultBiPjylProcess;
    @Autowired
    private DefaultBiZhtyfProcess defaultBiZhtyfProcess;
    @Autowired
    private DefaultBiContentGhspProcess defaultBiContentGhspProcess;
    @Autowired
    private DefaultBiContentGhtwProcess defaultBiContentGhtwProcess;
    @Autowired
    private DefaultBiSpPXIProcess defaultBiSpPXIProcess;
    @Autowired
    private DefaultBiMsyqProcess defaultBiMsyqProcess;
    @Autowired
    private BiService biService;
    @Autowired
    private DefaultBiUnionPayBillProcess defaultBiUnionPayBillProcess;
    @Autowired
    private DefaultBiUDProcess defaultBiUDProcess;
    @Autowired
    private DefaultBiSycmFlowNewProcess defaultBiNewSycmFlowProcess;
    @Autowired
    private DefaultBiPjProcess defaultBiPjProcess;

    String cookiekey = "ck_7e3e618e7b134bf6b1e9c82572488400";
    Cjzh cjzh = null;
    BiInfo biInfo = null;

    @Before
    public void init() {
        cjzh = abujlbCookieStore.getCjzhForKey(cookiekey);
        if (cjzh == null) {
            return;
        }
        cjzh.setMsgType(1);
        biInfo = DataUploader.getBiInfoJlbdpid(cjzh.getUserid(), cjzh.getJlbdpid());
        if (biInfo == null) {
            return;
        }
        ShopInfoUtil.initShopOverSeas(cjzh);
        BiThreadLocalObjects.addCjzh(cjzh);
        BiThreadLocalObjects.addBiInfo(biInfo);

        DataprobiMsg dataprobiMsg = new DataprobiMsg();
        try {
            dataprobiMsg.initial();
        } catch (NoSuchMethodException e) {
            throw new RuntimeException(e);
        } catch (InvocationTargetException e) {
            throw new RuntimeException(e);
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        }


        List<BiYhdp> biYhdps = DataUploader.getBiYhdpListJst(cjzh.getUserid() + "", biInfo.getQd());
        if (CollectionUtils.isEmpty(biYhdps)) {
            return;
        }
        cjzh.setBiYhdps(biYhdps);
        biService.msyq(cjzh, biInfo);
        ShopInfoUtil.initShopOverSeas(cjzh);
        BiThreadLocalObjects.addMsgBean(dataprobiMsg);
    }

    @Test
    public void spxg() {
//        BiThreadLocalObjects.getBiInfo().getCjrq().setSpxg("2024-04-01");
        BiThreadLocalObjects.getBiInfo().getCjrq().setSpxgdp("2023-12-31");
        defaultBiSpxgProcess.cjAll();
        System.out.println("采集完成spxg:" + BiThreadLocalObjects.getBiInfo().getCjrq().getSpxg());
        System.out.println("采集完成spxgdp:" + BiThreadLocalObjects.getBiInfo().getCjrq().getSpxgdp());
        System.out.println("采集完成，spxg_msg：" + BiThreadLocalObjects.getMsgBean().getSpxg());
        System.out.println("采集完成，spxgdp_msg：" + BiThreadLocalObjects.getMsgBean().getSpxgdp());
    }

    @Test
    public void newitem() {
        // BiThreadLocalObjects.getBiInfo().getCjrq().setNewitem("2023-02-01");
        defaultBiNewItemProcess.cjAll();
        System.out.println("采集完成newitem:" + BiThreadLocalObjects.getBiInfo().getCjrq().getNewitem());
    }


    @Test
    public void pxb() {
        // BiThreadLocalObjects.getBiInfo().getCjrq().setPxb("2022-10-30");
        // BiThreadLocalObjects.getBiInfo().getCjrq().setPxbdp("2022-11-29");
        defaultBiPxbProcess.cjAll();
        System.out.println("采集完成pxb:" + BiThreadLocalObjects.getBiInfo().getCjrq().getPxb());
        System.out.println("采集完成pxbdp:" + BiThreadLocalObjects.getBiInfo().getCjrq().getPxbdp());
    }

    @Test
    public void sppxi() {
        biInfo.getBiDpConfig().setSppxi(1);
        BiThreadLocalObjects.getBiInfo().getCjrq().setSppxi("2024-06-30");
        defaultBiSpPXIProcess.cjAll();
    }


    @Test
    public void tbxjhb() {
        BiThreadLocalObjects.getBiInfo().getCjrq().setTbxjhb("2023-08-31");
//        BiThreadLocalObjects.getBiInfo().getCjrq().setTbxjhbdp("2022-12-05");
        defaultBiCashRedPacketsProcess.cjAll();
        System.out.println("采集完成tbxjhb:" + BiThreadLocalObjects.getBiInfo().getCjrq().getTbxjhb());
        System.out.println("采集完成tbxjhbdp:" + BiThreadLocalObjects.getBiInfo().getCjrq().getTbxjhbdp());
    }

    @Test
    public void syj() {
        BiThreadLocalObjects.getBiInfo().getCjrq().setSpxg("2022-11-08");
        BiThreadLocalObjects.getBiInfo().getCjrq().setSpxgdp(null);
        BiThreadLocalObjects.getBiInfo().setStart(null);
        syjBiSpxgProcess.cjAll();
        System.out.println("采集完成syj:" + BiThreadLocalObjects.getBiInfo().getCjrq().getSpxg());
        System.out.println("采集完成syjdp:" + BiThreadLocalObjects.getBiInfo().getCjrq().getSpxgdp());
    }


    @Test
    public void splb() {
        BiThreadLocalObjects.getBiInfo().setSplb(0);
        defaultBiSplbProcess.cjAll();
        System.out.println("采集完成splb:" + BiThreadLocalObjects.getBiInfo().getSplb());
    }

    @Test
    public void rlyq() {
        defaultBiRlyqProcess.cjAll();
        System.out.println("采集完成rlyq:" + BiThreadLocalObjects.getBiInfo().getCjrq().getRlyq());
        System.out.println("采集完成rlyqdp:" + BiThreadLocalObjects.getBiInfo().getCjrq().getRlyqdp());
    }

    @Test
    public void jhs() {
        // BiThreadLocalObjects.getBiInfo().getCjrq().setJhs("2023-04-18");
        defaultBiJhsProcess.cjAll();
        System.out.println(BiThreadLocalObjects.getBiInfo().getCjrq().getJhs());
    }

    @Test
    public void sycmflow() {
        BiThreadLocalObjects.getCjzh().setMsgType(2);
        BiThreadLocalObjects.getBiInfo().getCjrq().setSycmflow("2024-03-26");
        sycmFlowProcess.cjAll();
    }

    @Test
    public void sycmflowNew() {
        BiThreadLocalObjects.getCjzh().setMsgType(2);
        BiThreadLocalObjects.getBiInfo().getCjrq().setSpxg("2024-12-02");
        BiThreadLocalObjects.getBiInfo().getCjrq().setSycmflow("2024-12-02");
        defaultBiNewSycmFlowProcess.cjAll();
        System.out.println(BiThreadLocalObjects.getBiInfo().getCjrq().getSycmflow());
    }

    @Test
    public void sycmverisonnew() {
        defaultBiSycmVersionProcess.cj();
    }

    @Test
    public void xedk() {
        BiThreadLocalObjects.getBiInfo().getCjrq().setXedk("2024-03-31");
        defaultBiXedkProcess.cjAll();
    }

    @Test
    public void ppxx() {
//        BiThreadLocalObjects.getBiInfo().getCjrq().setPpxxdp("2024-06-19");
//        BiThreadLocalObjects.getBiInfo().getCjrq().setPpxx("2023-07-30");
        BiThreadLocalObjects.getBiInfo().getBiDpConfig().setPpxx(0);
        BiThreadLocalObjects.getCjzh().setMsgType(1);
        defaultBiPpxxProcess.cjAll();
    }

    @Test
    public void fwCustomer() {
//        BiThreadLocalObjects.getBiInfo().getCjrq().setFwCustomer("2023-11-30");
        defaultBiSycmFwCustomerSummaryProcess.cjAll();
    }

    @Test
    public void bzj() {
//        BiThreadLocalObjects.getCjzh().setOverseas(false);
//        BiThreadLocalObjects.getBiInfo().getCjrq().setBzj("2024-08-20");
        defaultBiBzjProcess.cjAll();
        System.out.println(biInfo.getCjrq().getBzj());
    }

    @Test
    public void bzjOld() {
//        BiThreadLocalObjects.getBiInfo().getCjrq().setBzj("2023-12-31");
        defaultBiBzjOldProcess.cjAll();
        System.out.println(biInfo.getCjrq().getBzj());
    }

    @Test
    public void tbk() {
//        BiThreadLocalObjects.getBiInfo().getCjrq().setTbk("2024-01-01");
//        BiThreadLocalObjects.getBiInfo().getCjrq().setTbkdp("2024-01-01");
        defaultBiTbkProcess.cjAll();

        System.out.println(BiThreadLocalObjects.getMsgBean().getTbk());
        System.out.println(BiThreadLocalObjects.getMsgBean().getTbkdp());
    }

    @Test
    public void sycmlevel() {
//        BiThreadLocalObjects.getBiInfo().getCjrq().setSycmlevel("2023-12-01");
        defaultBiSycmLevelProcess.cjAll();

        System.out.println(BiThreadLocalObjects.getMsgBean().getSycmlevel());
    }

    @Test
    public void dmpHpdcQddplb() {
        BiThreadLocalObjects.getBiInfo().getCjrq().setDpmhpdcqddplb("2024-04-26");
        defaultBiDmpHpdcQddplbProcess.cjAll();
    }


    @Test
    public void llzh_ymfx() {
        BiThreadLocalObjects.getBiInfo().getBiDpConfig().setLlzhymfx(1);
        BiThreadLocalObjects.getBiInfo().getCjrq().setLlzhymfx("2024-04-09");
        defaultBiSycmLlzhYmfxProcess.cjAll();
    }

    @Test
    public void gjb() {
        BiThreadLocalObjects.getBiInfo().getBiDpConfig().setGjb(1);
        BiThreadLocalObjects.getBiInfo().getCjrq().setGjb("2024-06-09");
        gjbTask.gjb();
    }

    @Test
    public void pjyl() {
        BiThreadLocalObjects.getBiInfo().getCjrq().setPjyl("2024-04-01");
        defaultBiPjylProcess.cjAll();
    }

//    @Test
//    public void aiztonejhzt() {
////        BiThreadLocalObjects.getBiInfo().getCjrq().setSpxg("2024-04-01");
//        BiThreadLocalObjects.getBiInfo().getCjrq().setAiztonejhzt("2024-09-27");
//        defaultAiztTgfxProcess.cjAll();
//        System.out.println("采集完成tgfx:" + BiThreadLocalObjects.getBiInfo().getCjrq().getAiztonejhzt());
//    }

    @Test
    public void qndpzhty() {
//        BiThreadLocalObjects.getBiInfo().getCjrq().setSpxg("2024-04-01");
        BiThreadLocalObjects.getBiInfo().getCjrq().setQndpzhtyf("2025-07-07");
        defaultBiZhtyfProcess.cjAll();
        System.out.println("采集完成qndpzhty:" + BiThreadLocalObjects.getBiInfo().getCjrq().getQndpzhtyf());
    }

    @Test
    public void sycmghsp() {
//        BiThreadLocalObjects.getBiInfo().getCjrq().setSycmghsp("2024-06-24");
//        BiThreadLocalObjects.getBiInfo().getCjrq().setSycmghspdp("2024-06-24");
        defaultBiContentGhspProcess.cjAll();
        System.out.println("采集完成sycmghsp:" + BiThreadLocalObjects.getBiInfo().getCjrq().getSycmghsp());
        System.out.println("采集完成sycmghsp:" + BiThreadLocalObjects.getMsgBean().getSycmghsp());
        System.out.println("采集完成sycmghspdp:" + BiThreadLocalObjects.getBiInfo().getCjrq().getSycmghspdp());
        System.out.println("采集完成sycmghspdp:" + BiThreadLocalObjects.getMsgBean().getSycmghspdp());

    }

    @Test
    public void sycmghtw() {
//        BiThreadLocalObjects.getBiInfo().getCjrq().setSpxg("2024-04-01");
        BiThreadLocalObjects.getBiInfo().getCjrq().setSycmghtw("2024-06-25");
        BiThreadLocalObjects.getBiInfo().getCjrq().setSycmghtwdp("2024-06-24");
        defaultBiContentGhtwProcess.cjAll();
        System.out.println("采集完成光合图文:" + BiThreadLocalObjects.getBiInfo().getCjrq().getSycmghtw());
        System.out.println("采集完成光合图文:" + BiThreadLocalObjects.getMsgBean().getSycmghtw());
        System.out.println("采集完成光合图文店铺:" + BiThreadLocalObjects.getBiInfo().getCjrq().getSycmghtwdp());
        System.out.println("采集完成光合图文店铺:" + BiThreadLocalObjects.getMsgBean().getSycmghtwdp());

    }

    @Test
    public void msyq() {
        BiInfo biInfo = BiThreadLocalObjects.getBiInfo();
        biInfo.getCjrq().setMsyqJlgg("2023-09-25");
        defaultBiMsyqProcess.cjAll();
    }

    @Test
    public void unionpaybill() {
        //SEASON四月七
//        BiThreadLocalObjects.getBiInfo().getCjrq().setUnion_pay_bill("2024-09-04");
//        BiThreadLocalObjects.getBiInfo().getCjrq().setUnion_pay_bill("2024-10-04");
        BiThreadLocalObjects.getBiInfo().getCjrq().setUnion_pay_bill("2024-11-02");
//        BiThreadLocalObjects.getBiInfo().getCjrq().setUnion_pay_bill("2024-10-31");
        defaultBiUnionPayBillProcess.cjAll();
        System.out.println(BiThreadLocalObjects.getBiInfo().getCjrq().getUnion_pay_bill());
    }

    @Test
    public void udzht() {
        BiThreadLocalObjects.getBiInfo().getCjrq().setUdzht("2024-10-31");
        defaultBiUDProcess.cjAll();
        System.out.println(BiThreadLocalObjects.getBiInfo().getCjrq().getUdzht());
    }


    @Test
    public void pj() {
        BiThreadLocalObjects.getBiInfo().getBiDpConfig().setPj(1);
        BiThreadLocalObjects.getBiInfo().getCjrq().setPj("2024-11-30");
        defaultBiPjProcess.cjAll();
        System.out.println("===============采集完成");
    }
}