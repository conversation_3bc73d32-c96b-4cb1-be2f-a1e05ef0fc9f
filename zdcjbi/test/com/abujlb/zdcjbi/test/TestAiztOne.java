package com.abujlb.zdcjbi.test;

import com.abujlb.AbujlbBeanFactory;
import com.abujlb.BaseTest;
import com.abujlb.zdcjbi.bean.BiInfo;
import com.abujlb.zdcjbi.bean.BiYhdp;
import com.abujlb.zdcjbi.bean.DataprobiMsg;
import com.abujlb.zdcjbi.cookie.AbujlbCookieStore;
import com.abujlb.zdcjbi.cookie.Cjzh;
import com.abujlb.zdcjbi.http.DataUploader;
import com.abujlb.zdcjbi.processes.*;
import com.abujlb.zdcjbi.thread.BiThreadLocalObjects;
import com.abujlb.zdcjbi.util.ShopInfoUtil;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.lang.reflect.InvocationTargetException;
import java.util.List;

public class TestAiztOne extends BaseTest {

    @Autowired
    private AbujlbCookieStore abujlbCookieStore;
    @Autowired
    private AbujlbBeanFactory abujlbBeanFactory;

    //    String cookiekey = "ck_901dd1833e254d60b7015007c9e333de";//mootaa旗舰店
    String cookiekey = "ck_19be8da1ca9b47b781d77553ab00a334";//壹念旗舰店
//    String cookiekey = "ck_7e08d28a72c747ff837d546e2382b38a";//pissa旗舰店

    Cjzh cjzh = null;
    BiInfo biInfo = null;

    @Before
    public void init() {
        cjzh = abujlbCookieStore.getCjzhForKey(cookiekey);
        if (cjzh == null) {
            return;
        }
        cjzh.setMsgType(2);
        biInfo = DataUploader.getBiInfoJlbdpid(cjzh.getUserid(), cjzh.getJlbdpid());
        if (biInfo == null) {
            return;
        }
        ShopInfoUtil.initShopOverSeas(cjzh);
        BiThreadLocalObjects.addCjzh(cjzh);
        BiThreadLocalObjects.addBiInfo(biInfo);

        DataprobiMsg dataprobiMsg = new DataprobiMsg();
        try {
            dataprobiMsg.initial();
        } catch (NoSuchMethodException | InvocationTargetException | IllegalAccessException e) {
            throw new RuntimeException(e);
        }

        List<BiYhdp> biYhdps = DataUploader.getBiYhdpListJst(cjzh.getUserid() + "", biInfo.getQd());
        if (CollectionUtils.isEmpty(biYhdps)) {
            return;
        }
        cjzh.setBiYhdps(biYhdps);
        ShopInfoUtil.initShopOverSeas(cjzh);
        BiThreadLocalObjects.addMsgBean(dataprobiMsg);
    }


    @Test
    public void aizt_item() {
        BiThreadLocalObjects.getBiInfo().getCjrq().setAizt_item("2024-11-30");
        DefaultBiAdbrainOneNormalAiztItemProcess bean = abujlbBeanFactory.getBean(DefaultBiAdbrainOneNormalAiztItemProcess.class);
        bean.cjAll();
    }

    @Test
    public void aizt() {
        BiThreadLocalObjects.getBiInfo().getCjrq().setAizt("2024-12-14");
        DefaultBiAdbrainOneNormalAiztProcess bean = abujlbBeanFactory.getBean(DefaultBiAdbrainOneNormalAiztProcess.class);
        bean.cjAll();
    }

    @Test
    public void aizt_shop() {
        BiThreadLocalObjects.getBiInfo().getCjrq().setAizt_shop("2024-12-18");
        BiThreadLocalObjects.getBiInfo().getCjrq().setAiztone_dpsummary("2024-12-14");
        DefaultBiAdbrainOneNormalAiztShopProcess bean = abujlbBeanFactory.getBean(DefaultBiAdbrainOneNormalAiztShopProcess.class);
        bean.cjAll();
    }

    @Test
    public void dmbzt() {
        BiThreadLocalObjects.getBiInfo().getCjrq().setDmbzt("2024-12-14");
        DefaultBiAdbrainOneNormalDmbztProcess bean = abujlbBeanFactory.getBean(DefaultBiAdbrainOneNormalDmbztProcess.class);
        bean.cjAll();
    }


    @Test
    public void nryxdsp() {
        BiThreadLocalObjects.getBiInfo().getCjrq().setNryxdsp("2024-12-14");
        DefaultBiAdbrainOneNormalNryxDspProcess bean = abujlbBeanFactory.getBean(DefaultBiAdbrainOneNormalNryxDspProcess.class);
        bean.cjAll();
    }

    @Test
    public void nryx() {
        BiThreadLocalObjects.getBiInfo().getCjrq().setNryxdp("2024-12-10");
        DefaultBiAdbrainOneNormalNryxProcess bean = abujlbBeanFactory.getBean(DefaultBiAdbrainOneNormalNryxProcess.class);
        bean.cjAll();
    }

    @Test
    public void qztg() {
        BiThreadLocalObjects.getBiInfo().getCjrq().setQztg("2024-12-14");
        DefaultBiAdbrainOneNormalQztgProcess bean = abujlbBeanFactory.getBean(DefaultBiAdbrainOneNormalQztgProcess.class);
        bean.cjAll();
    }

    @Test
    public void xstg() {
        BiThreadLocalObjects.getBiInfo().getCjrq().setXstg("2024-12-14");
        DefaultBiAdbrainOneNormalXstgProcess bean = abujlbBeanFactory.getBean(DefaultBiAdbrainOneNormalXstgProcess.class);
        bean.cjAll();
    }

    @Test
    public void ylmf() {
        BiThreadLocalObjects.getBiInfo().getCjrq().setYlmf("2024-12-14");
        DefaultBiAdbrainOneNormalYlmfProcess bean = abujlbBeanFactory.getBean(DefaultBiAdbrainOneNormalYlmfProcess.class);
        bean.cjAll();
    }

    @Test
    public void ztc() {
        BiThreadLocalObjects.getBiInfo().getCjrq().setZtc("2024-12-14");
        DefaultBiAdbrainOneNormalZtcProcess bean = abujlbBeanFactory.getBean(DefaultBiAdbrainOneNormalZtcProcess.class);
        bean.cjAll();
    }

    @Test
    public void cybb() {
        BiThreadLocalObjects.getBiInfo().getCjrq().setAiztcybb("2024-11-30");
        DefaultBiAdbrainOneNormalCybbProcess bean = abujlbBeanFactory.getBean(DefaultBiAdbrainOneNormalCybbProcess.class);
        bean.cjAll();
    }


    //---backtrace---//
    @Test
    public void aizt_item_backtrace() {
        BiThreadLocalObjects.getBiInfo().getCjrq().setAizt_item("2024-12-15");
        BiThreadLocalObjects.getBiInfo().getCjrq().setAizt_item_backtrace("2024-11-30");
        DefaultBiAdbrainOneBackTraceAiztItemProcess bean = abujlbBeanFactory.getBean(DefaultBiAdbrainOneBackTraceAiztItemProcess.class);
        bean.cjAll();
    }

    @Test
    public void aizt_backtrace() {
        BiThreadLocalObjects.getBiInfo().getCjrq().setAizt("2024-12-15");
        BiThreadLocalObjects.getBiInfo().getCjrq().setAizt_backtrace("2024-11-30");
        DefaultBiAdbrainOneBackTraceAiztProcess bean = abujlbBeanFactory.getBean(DefaultBiAdbrainOneBackTraceAiztProcess.class);
        bean.cjAll();
    }

    @Test
    public void aizt_shop_backtrace() {
        BiThreadLocalObjects.getBiInfo().getCjrq().setAizt_shop("2024-12-15");
        BiThreadLocalObjects.getBiInfo().getCjrq().setAizt_shop_backtrace("2024-11-30");
        DefaultBiAdbrainOneBackTraceAiztShopProcess bean = abujlbBeanFactory.getBean(DefaultBiAdbrainOneBackTraceAiztShopProcess.class);
        bean.cjAll();
    }

    @Test
    public void dmbzt_backtrace() {
        BiThreadLocalObjects.getBiInfo().getCjrq().setDmbzt("2024-12-15");
        BiThreadLocalObjects.getBiInfo().getCjrq().setDmbzt_backtrace("2024-11-30");
        DefaultBiAdbrainOneBackTraceDmbztProcess bean = abujlbBeanFactory.getBean(DefaultBiAdbrainOneBackTraceDmbztProcess.class);
        bean.cjAll();
    }

    @Test
    public void qztg_backtrace() {
        BiThreadLocalObjects.getBiInfo().getCjrq().setQztg("2024-12-15");
        BiThreadLocalObjects.getBiInfo().getCjrq().setQztg_backtrace("2024-11-30");
        DefaultBiAdbrainOneBackTraceQztgProcess bean = abujlbBeanFactory.getBean(DefaultBiAdbrainOneBackTraceQztgProcess.class);
        bean.cjAll();
    }


    @Test
    public void xstg_backtrace() {
        BiThreadLocalObjects.getBiInfo().getCjrq().setXstg("2024-12-15");
        BiThreadLocalObjects.getBiInfo().getCjrq().setXstg_backtrace("2024-11-30");
        DefaultBiAdbrainOneBackTraceXstgProcess bean = abujlbBeanFactory.getBean(DefaultBiAdbrainOneBackTraceXstgProcess.class);
        bean.cjAll();
        System.out.println(BiThreadLocalObjects.getBiInfo().getCjrq().getXstg_backtrace());
    }

    @Test
    public void ylmf_backtrace() {
        BiThreadLocalObjects.getBiInfo().getCjrq().setYlmf("2024-12-15");
        BiThreadLocalObjects.getBiInfo().getCjrq().setYlmf_backtrace("2024-11-30");
        DefaultBiAdbrainOneBackTraceYlmfProcess bean = abujlbBeanFactory.getBean(DefaultBiAdbrainOneBackTraceYlmfProcess.class);
        bean.cjAll();
        System.out.println(BiThreadLocalObjects.getBiInfo().getCjrq().getYlmf_backtrace());
    }


    @Test
    public void ztc_backtrace() {
        BiThreadLocalObjects.getBiInfo().getCjrq().setZtc("2024-12-15");
        BiThreadLocalObjects.getBiInfo().getCjrq().setZtc_backtrace("2024-11-30");
        DefaultBiAdbrainOneBackTraceZtcProcess bean = abujlbBeanFactory.getBean(DefaultBiAdbrainOneBackTraceZtcProcess.class);
        bean.cjAll();
        System.out.println(BiThreadLocalObjects.getBiInfo().getCjrq().getZtc_backtrace());
    }


    //-----------tgfx----//
    @Test
    public void aizt_item_tgfx() {
        BiThreadLocalObjects.getBiInfo().getCjrq().setAizt_item("2024-12-15");
        BiThreadLocalObjects.getBiInfo().getCjrq().setAizt_item_tgfx("2024-12-14");
        DefaultBiAdbrainOneTgfxAiztItemProcess bean = abujlbBeanFactory.getBean(DefaultBiAdbrainOneTgfxAiztItemProcess.class);
        bean.cjAll();
        System.out.println(BiThreadLocalObjects.getBiInfo().getCjrq().getAizt_item_tgfx());
    }

    @Test
    public void aizt_tgfx() {
        BiThreadLocalObjects.getBiInfo().getCjrq().setAizt("2024-12-15");
        BiThreadLocalObjects.getBiInfo().getCjrq().setAizt_tgfx("2024-12-14");
        DefaultBiAdbrainOneTgfxAiztProcess bean = abujlbBeanFactory.getBean(DefaultBiAdbrainOneTgfxAiztProcess.class);
        bean.cjAll();
        System.out.println(BiThreadLocalObjects.getBiInfo().getCjrq().getAizt_tgfx());
    }

    @Test
    public void aizt_shop_tgfx() {
        BiThreadLocalObjects.getBiInfo().getCjrq().setAizt_shop("2024-12-15");
        BiThreadLocalObjects.getBiInfo().getCjrq().setAizt_shop_tgfx("2024-12-14");
        DefaultBiAdbrainOneTgfxAiztShopProcess bean = abujlbBeanFactory.getBean(DefaultBiAdbrainOneTgfxAiztShopProcess.class);
        bean.cjAll();
        System.out.println(BiThreadLocalObjects.getBiInfo().getCjrq().getAizt_shop());
    }

    @Test
    public void dmbzt_tgfx() {
        BiThreadLocalObjects.getBiInfo().getCjrq().setDmbzt("2024-12-15");
        BiThreadLocalObjects.getBiInfo().getCjrq().setDmbzt_tgfx("2024-12-14");
        DefaultBiAdbrainOneTgfxDmbztProcess bean = abujlbBeanFactory.getBean(DefaultBiAdbrainOneTgfxDmbztProcess.class);
        bean.cjAll();
        System.out.println(BiThreadLocalObjects.getBiInfo().getCjrq().getDmbzt_tgfx());
    }

    @Test
    public void qztg_tgfx() {
        BiThreadLocalObjects.getBiInfo().getCjrq().setQztg("2024-12-15");
        BiThreadLocalObjects.getBiInfo().getCjrq().setQztg_tgfx("2024-12-14");
        DefaultBiAdbrainOneTgfxQztgProcess bean = abujlbBeanFactory.getBean(DefaultBiAdbrainOneTgfxQztgProcess.class);
        bean.cjAll();
        System.out.println(BiThreadLocalObjects.getBiInfo().getCjrq().getQztg_tgfx());
    }

    @Test
    public void xstg_tgfx() {
        BiThreadLocalObjects.getBiInfo().getCjrq().setXstg("2024-12-15");
        BiThreadLocalObjects.getBiInfo().getCjrq().setXstg_tgfx("2024-12-14");
        DefaultBiAdbrainOneTgfxXstgProcess bean = abujlbBeanFactory.getBean(DefaultBiAdbrainOneTgfxXstgProcess.class);
        bean.cjAll();
        System.out.println(BiThreadLocalObjects.getBiInfo().getCjrq().getXstg_tgfx());
    }

    @Test
    public void ylmf_tgfx() {
        BiThreadLocalObjects.getBiInfo().getCjrq().setYlmf("2024-12-15");
        BiThreadLocalObjects.getBiInfo().getCjrq().setYlmf_tgfx("2024-12-14");
        DefaultBiAdbrainOneTgfxYlmfProcess bean = abujlbBeanFactory.getBean(DefaultBiAdbrainOneTgfxYlmfProcess.class);
        bean.cjAll();
        System.out.println(BiThreadLocalObjects.getBiInfo().getCjrq().getYlmf_tgfx());
    }


    @Test
    public void ztc_crowd_tgfx() {
        BiThreadLocalObjects.getBiInfo().getCjrq().setZtc("2024-12-15");
        BiThreadLocalObjects.getBiInfo().getCjrq().setZtc_crowd_tgfx("2024-12-14");
        DefaultBiAdbrainOneTgfxZtcCrowdsProcess bean = abujlbBeanFactory.getBean(DefaultBiAdbrainOneTgfxZtcCrowdsProcess.class);
        bean.cjAll();
        System.out.println(BiThreadLocalObjects.getBiInfo().getCjrq().getZtc_crowd_tgfx());
    }


    @Test
    public void ztc_keyword_tgfx() {
        BiThreadLocalObjects.getBiInfo().getCjrq().setZtc("2024-12-15");
        BiThreadLocalObjects.getBiInfo().getCjrq().setZtc_keyword_tgfx("2024-12-14");
        DefaultBiAdbrainOneTgfxZtcKeywordsProcess bean = abujlbBeanFactory.getBean(DefaultBiAdbrainOneTgfxZtcKeywordsProcess.class);
        bean.cjAll();
        System.out.println(BiThreadLocalObjects.getBiInfo().getCjrq().getZtc_keyword_tgfx());
    }

    @Test
    public void ztc_unit_tgfx() {
        BiThreadLocalObjects.getBiInfo().getCjrq().setZtc("2024-12-22");
        BiThreadLocalObjects.getBiInfo().getCjrq().setZtc_unit_tgfx("2024-12-19");
        DefaultBiAdbrainOneTgfxZtcUnitProcess bean = abujlbBeanFactory.getBean(DefaultBiAdbrainOneTgfxZtcUnitProcess.class);
        bean.cjAll();
        System.out.println(BiThreadLocalObjects.getBiInfo().getCjrq().getZtc_unit_tgfx());
    }
}
