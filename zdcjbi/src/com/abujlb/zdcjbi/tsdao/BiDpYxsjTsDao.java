package com.abujlb.zdcjbi.tsdao;


import com.abujlb.dao.v2.TsDao2;
import com.abujlb.dao.v2.ts.Ts2Client;
import com.alicloud.openservices.tablestore.SyncClient;
import com.alicloud.openservices.tablestore.model.*;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2021-8-26
 */
@Component
public class BiDpYxsjTsDao {


    final Logger LOG = Logger.getLogger(BiDpYxsjTsDao.class);

    private final String TABLE_NAME = "bi_sycmdpdata";

    /***************** 主键 **************************/
    private final String COLUMN_USERID = "qd_userid";
    private final String COLUMN_DATE = "rq";
    public static final String COLUMN_SPXG = "tx_spxg";
    public static final String COLUMN_JYSJ = "tx_jysj";
    public static final String COLUMN_ZTC = "tx_ztc";
    public static final String COLUMN_AIZT = "tx_aizt";
    public static final String COLUMN_AIZT2 = "tx_aizt2";
    public static final String COLUMN_AIZT3 = "tx_aizt3";
    public static final String COLUMN_PXB = "tx_pxb";
    public static final String COLUMN_SYCMLEVEL = "tx_sycmlevel";
    public static final String COLUMN_YLMF = "tx_ylmf";
    public static final String COLUMN_TBXJHB = "tx_tbxjhb";
    public static final String COLUMN_DMBZT = "tx_dmbzt";
    public static final String COLUMN_FW_CUSTOMER = "tx_fwcustomer";
    public static final String COLUMN_TBK = "tx_tbk";
    public static final String COLUMN_QZTG = "tx_qztg";
    public static final String COLUMN_ZHTYF = "tx_dpzhtyf";
    public static final String COLUMN_GHSP = "tx_ghsp";
    public static final String COLUMN_GHTW = "tx_ghtw";
    public static final String COLUMN_SYCM_PERFORMANCE_CORE_MONITOR = "tx_sycm_performance_core_monitor";

    @Autowired
    private TsDao2 tsDao2;

    public void updateRow(String qd, String userid, String rq, String column, String val) {
        Ts2Client client = tsDao2.getClient("abujlb6@jushita");
        updateRow2(client.getClient(), qd, userid, rq, column, val);
    }

    private void updateRow2(SyncClient client, String qd, String userid, String rq, String column, String val) {
        try {
            // 构造主键
            PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
            primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_USERID, PrimaryKeyValue.fromString(qd + "_" + userid));
            primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_DATE, PrimaryKeyValue.fromString(rq));
            PrimaryKey primaryKey = primaryKeyBuilder.build();
            // 设置表名
            RowUpdateChange rowUpdateChange = new RowUpdateChange(TABLE_NAME, primaryKey);
            rowUpdateChange.put(new Column(column, ColumnValue.fromString(val == null ? "" : val)));
            // 更新
            client.updateRow(new UpdateRowRequest(rowUpdateChange));
        } catch (Throwable e) {
            LOG.error(e.getMessage(), e);
        }
    }

    public void updateRow(String qd, String userid, String rq, String[] column, String[] vals) {
        Ts2Client client = tsDao2.getClient("abujlb6@jushita");
        updateRow2(client.getClient(), qd, userid, rq, column, vals);
    }

    private void updateRow2(SyncClient client, String qd, String userid, String rq, String[] columns, String[] vals) {
        try {
            // 构造主键
            PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
            primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_USERID, PrimaryKeyValue.fromString(qd + "_" + userid));
            primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_DATE, PrimaryKeyValue.fromString(rq));
            PrimaryKey primaryKey = primaryKeyBuilder.build();
            // 设置表名
            RowUpdateChange rowUpdateChange = new RowUpdateChange(TABLE_NAME, primaryKey);
            for (int i = 0; i < columns.length; i++) {
                rowUpdateChange.put(new Column(columns[i], ColumnValue.fromString(vals[i] == null ? "" : vals[i])));
            }
            // 更新
            client.updateRow(new UpdateRowRequest(rowUpdateChange));
        } catch (Throwable e) {
            LOG.error(e.getMessage(), e);
        }
    }

    public String getRow(String qd, long userid, String rq, String column) {
        Ts2Client client = tsDao2.getClient("abujlb6@jushita");
        return getRow(client.getClient(), qd, userid, rq, column);
    }

    private String getRow(SyncClient client, String qd, long userid, String rq, String column) {
        try {
            PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
            primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_USERID, PrimaryKeyValue.fromString(qd + "_" + userid));
            primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_DATE, PrimaryKeyValue.fromString(rq));
            PrimaryKey primaryKey = primaryKeyBuilder.build();
            SingleRowQueryCriteria criteria = new SingleRowQueryCriteria(TABLE_NAME, primaryKey);
            criteria.setMaxVersions(1);
            criteria.addColumnsToGet(column);
            GetRowResponse getRowResponse = client.getRow(new GetRowRequest(criteria));
            Row row = getRowResponse.getRow();
            if (row == null || row.isEmpty() || row.getLatestColumn(column) == null || row.getLatestColumn(column).getValue() == null) {
                return null;
            }
            return row.getLatestColumn(column).getValue().asString();
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
            return null;
        }
    }

}
