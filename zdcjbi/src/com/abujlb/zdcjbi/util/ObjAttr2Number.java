package com.abujlb.zdcjbi.util;

import com.abujlb.util.StringUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.log4j.Logger;

/**
 * json对象属性转数字
 * 
 * <AUTHOR>
 * @date 2021-08-08 09:57:23
 */
public class ObjAttr2Number {

	private static Logger log = Logger.getLogger(ObjAttr2Number.class);

	/**
	 * 对象属性转int
	 * 
	 * @param obj json对象
	 * @param key 对象的某个属性
	 */
	public static int toInt(JSONObject obj, String key) {
		int result = 0;
		try {
			if (obj == null || obj.isEmpty()) {
				return result;
			}
			if (obj.containsKey(key) && !StringUtil.isNull2(obj.getString(key))) {
				result = Integer.parseInt(obj.getString(key).replaceAll(",", ""));
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		return result;
	}
	
	/**
	 * 对象属性转Long
	 * 
	 * @param obj json对象
	 * @param key 对象的某个属性
	 */
	public static long toLong(JSONObject obj, String key) {
		long result = 0L;
		try {
			if (obj == null || obj.isEmpty()) {
				return result;
			}
			if (obj.containsKey(key) && !StringUtil.isNull2(obj.getString(key))) {
				result = Long.parseLong(obj.getString(key).replaceAll(",", ""));
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		return result;
	}
	
	/**
	 * 对象属性转double
	 * 
	 * @param obj json对象
	 * @param key 对象的某个属性
	 */
	public static double toDouble(JSONObject obj, String key) {
		double result = 0D;
		try {
			if (obj == null || obj.isEmpty()) {
				return result;
			}
			if (obj.containsKey(key) && !StringUtil.isNull2(obj.getString(key))) {
				String str = obj.getString(key).replaceAll(",", "");
				if (str.contains("%")) {
					result = StringToNumber.transform(str);
				} else {
					result = Double.parseDouble(str);
				}
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		return result;
	}
	
	/**
	 * 对象属性转String
	 * 
	 * @param obj json对象
	 * @param key 对象的某个属性
	 */
	public static String toString(JSONObject obj, String key) {
		String result = "";
		try {
			if (obj == null || obj.isEmpty()) {
				return result;
			}
			if (obj.containsKey(key) && !StringUtil.isNull2(obj.getString(key))) {
				result = obj.getString(key);
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		return result;
	}
	
	/**
	 * 对象属性转Long数组
	 * 
	 * @param obj json对象
	 * @param key 对象的某个属性
	 */
	public static long[] toLongArr(JSONObject obj, String key) {
		long[] result = null;
		try {
			if (obj == null || obj.isEmpty()) {
				return result;
			}
			if (obj.containsKey(key) && !StringUtil.isNull2(obj.getString(key))) {
				JSONArray arr = obj.getJSONArray(key);
				if (arr != null && arr.size() > 0) {
					result = new long[arr.size()];
					for (int i = 0; i < arr.size(); i++) {
						result[i] = arr.getLongValue(i);
					}
				}
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		return result;
	}
	
}
