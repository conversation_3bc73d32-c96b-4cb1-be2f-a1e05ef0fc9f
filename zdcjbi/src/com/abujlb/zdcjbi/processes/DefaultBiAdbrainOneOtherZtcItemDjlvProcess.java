package com.abujlb.zdcjbi.processes;

import com.abujlb.zdcjbi.annos.BiPro;
import com.abujlb.zdcjbi.auth.BiAuthResult;
import com.abujlb.zdcjbi.bean.BiDpConfig;
import com.abujlb.zdcjbi.bean.BiInfo;
import com.abujlb.zdcjbi.constant.BiModuleConstant;
import com.abujlb.zdcjbi.constant.BiProcessIndex;
import com.abujlb.zdcjbi.cookie.Cjzh;
import com.abujlb.zdcjbi.http.AiztOneHttpClientV2;
import com.abujlb.zdcjbi.oss.BiDataOssUtil;
import com.abujlb.zdcjbi.thread.BiThreadLocalObjects;
import com.abujlb.zdcjbi.util.BiDateUtil;
import com.alibaba.fastjson.JSONArray;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * BI 万相台无界-直通车-宝贝 近7天搜索点击率采集<p/>
 * <p>
 * 仅商品维度
 * <p>
 * https://www.tapd.cn/47708728/prong/stories/view/1147708728001005922
 *
 * <AUTHOR>
 * @date 2024/5/18
 */
@Component
@BiPro(value = BiProcessIndex.AIZTONE_ZTCITEMDJLV,  localRunNotInclude = true)
public class DefaultBiAdbrainOneOtherZtcItemDjlvProcess extends AbstractBiProcess {


    /**
     * 采集页面地址：https://one.alimama.com/index.html#!/main/index?bizCode=onebpSearch
     * 无界 --》 推广 --》关键词推广 --》新建关键词推广 --》 《选择卡位方案 --添加宝贝》
     * 接口分页采集  一页40
     */
    @Override
    public void cj() {
        JSONArray jsonArray = AiztOneHttpClientV2.materialItemList(getCjzh());
        if (jsonArray == null) {
            return;
        }

        String key = "bi_jysj/" + getCjzh().getUserid() + "/" + BiDateUtil.getLastday() + "/" + "bi_aiztone_ztcitemdjlv.json";
        BiDataOssUtil.upload(jsonArray.toJSONString(), key);

        jsonArray.clear();

        getBiInfo().getCjrq().setAiztoneztcitemdjlv(BiDateUtil.getLastday());
        BiThreadLocalObjects.getMsgBean().getAiztoneztcitemdjlv().add(BiDateUtil.getLastday());
    }

    @Override
    public void noauth() {
        if (send(BiModuleConstant.AIZTONEZTCITEMDJLV)) {
            BiThreadLocalObjects.getMsgBean().getAiztonejhzt().add(BiDateUtil.getLastday());
        }
    }

    @Override
    public BiAuthResult authCheck(Cjzh cjzh) {
        return aiztoneAuth(cjzh);
    }

    @Override
    public boolean dpconfig(BiDpConfig biDpConfig) {
        return biDpConfig.getAiztoneztcitemdjlv() == 0;
    }

    @Override
    protected boolean saveCjrq2Cjzh() {
        BiInfo biInfo = getBiInfo();
        List<String> rqList = BiDateUtil.getCjsj(biInfo.getStart(), biInfo.getCjrq().getAiztoneztcitemdjlv());
        return !CollectionUtils.isEmpty(rqList);
    }
}
