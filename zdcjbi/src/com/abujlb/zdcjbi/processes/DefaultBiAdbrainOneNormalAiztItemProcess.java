package com.abujlb.zdcjbi.processes;

import com.abujlb.zdcjbi.annos.BiPro;
import com.abujlb.zdcjbi.auth.BiAuthResult;
import com.abujlb.zdcjbi.bean.BiDpConfig;
import com.abujlb.zdcjbi.bean.BiInfo;
import com.abujlb.zdcjbi.constant.*;
import com.abujlb.zdcjbi.cookie.Cjzh;
import com.abujlb.zdcjbi.thread.BiThreadLocalObjects;
import com.abujlb.zdcjbi.util.BiDateUtil;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * BI 万相台无界-万相台-货品运营 数据采集<p/>
 * <p>
 * 核算到单品。
 * <p>
 *
 * <AUTHOR>
 * @date 2024/12/10
 */
@Component
@BiPro(value = BiProcessIndex.AIZTONE_AIZT_ITEM)
public class DefaultBiAdbrainOneNormalAiztItemProcess extends AbstractBiProcess {


    /**
     * <ul>
     *     <li>采集地址：https://one.alimama.com/index.html#!/report/item_promotion?rptType=item_promotion&queryDomains=%5B%22promotion%22%2C%22date%22%2C%22campaign%22%5D&offset=20&pageSize=20</li>
     *     <li>采集页面描述：万相台无界-》报表-》宝贝主体报表-》宝贝主体数据明细</li>
     *     <li>采集方式：报表采集</li>
     *     <li>采集选项：</li>
     *     <ul>
     *         <li>报表模板：货品运营</li>
     *         <li>末次点击归因</li>
     *         <li>15天累计数据</li>
     *         <li>汇总周期：传入开始和结束日期</li>
     *         <li>时间粒度：分天</li>
     *         <li>维度：主体，时间，计划</li>
     *         <li>数据指标：全部指标数据</li>
     *     </ul>
     * </ul>
     */
    @Override
    public void cj() {
        try {
            List<String> rqList = getCjzh().getCjrqMapRqList(BiModuleConstant.AIZT_ITEM);
            if (CollectionUtils.isEmpty(rqList)) {
                return;
            }

            List<List<String>> lists = Lists.partition(rqList, 31);
            for (List<String> list : lists) {
                if (cjAdbrainOneReport(AiztoneConst.TYPE_AIZTONE_AIZT_ITEM, RqlxConst.DAY, list, AIZTONE_REPORT_SPLIT_TYPE_DAY)) {
                    getBiInfo().getCjrq().setAizt_item(list.get(list.size() - 1));
                    BiThreadLocalObjects.getMsgBean().getAizt_item().addAll(list);
                    saveRPA(getBiInfo(), getCjzh(), CodeConst.AIZTONE_AIZT, list);
                } else {
                    break;
                }
            }
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
    }

    @Override
    public void noauth() {
        if (send(BiModuleConstant.AIZTONE_AIZT_ITEM)) {
            BiThreadLocalObjects.getMsgBean().getAizt_item().add(BiDateUtil.getLastday());
        }
    }

    @Override
    public BiAuthResult authCheck(Cjzh cjzh) {
        return aiztoneAuth(cjzh);
    }

    @Override
    public void notCj() {
        BiThreadLocalObjects.getBiInfo().getCjrq().setAizt_item(BiDateUtil.getLastday());
        BiThreadLocalObjects.getMsgBean().getAizt_item().add(BiDateUtil.getLastday());
    }

    @Override
    public boolean dpconfig(BiDpConfig biDpConfig) {
        return biDpConfig.getAizt() == 0;
    }

    @Override
    protected boolean saveCjrq2Cjzh() {
        Cjzh cjzh = getCjzh();
        if (cjzh.getAiztOne() == 0) {
            return false;
        }
        BiInfo biInfo = getBiInfo();
        List<String> rqList = BiDateUtil.getCjsj(biInfo.getStart(), biInfo.getCjrq().getAizt_item());
        if (CollectionUtils.isEmpty(rqList)) {
            return false;
        }
        cjzh.putCjrqMap(BiModuleConstant.AIZT_ITEM, rqList);
        return true;
    }
}
