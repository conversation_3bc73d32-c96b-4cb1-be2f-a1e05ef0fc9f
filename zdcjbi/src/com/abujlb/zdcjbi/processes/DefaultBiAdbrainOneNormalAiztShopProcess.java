package com.abujlb.zdcjbi.processes;

import com.abujlb.zdcjbi.annos.BiPro;
import com.abujlb.zdcjbi.auth.BiAuthResult;
import com.abujlb.zdcjbi.bean.BiDpConfig;
import com.abujlb.zdcjbi.bean.BiInfo;
import com.abujlb.zdcjbi.constant.*;
import com.abujlb.zdcjbi.cookie.Cjzh;
import com.abujlb.zdcjbi.http.AiztOneHttpClientV2;
import com.abujlb.zdcjbi.thread.BiThreadLocalObjects;
import com.abujlb.zdcjbi.util.BiDateUtil;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * BI 万相台无界-万相台-店铺运营 数据采集<p/>
 * <AUTHOR>
 * @date 2024/12/10
 */
@Component
@BiPro(value = BiProcessIndex.AIZTONE_AIZT_SHOP)
public class DefaultBiAdbrainOneNormalAiztShopProcess extends AbstractBiProcess {


    /**
     * <ul>
     *     <li>采集地址：https://one.alimama.com/index.html#!/report/item_promotion?rptType=item_promotion&queryDomains=%5B%22promotion%22%2C%22date%22%2C%22campaign%22%5D&offset=20&pageSize=20</li>
     *     <li>采集页面描述：万相台无界-》报表-》宝贝主体报表-》宝贝主体数据明细</li>
     *     <li>采集方式：报表采集</li>
     *     <li>采集选项：</li>
     *     <ul>
     *         <li>报表模板：店铺运营</li>
     *         <li>末次点击归因</li>
     *         <li>15天累计数据</li>
     *         <li>汇总周期：传入开始和结束日期</li>
     *         <li>时间粒度：分天</li>
     *         <li>维度：主体，时间，计划</li>
     *         <li>数据指标：全部指标数据</li>
     *     </ul>
     * </ul>
     */
    @Override
    public void cj() {
        try {
            List<String> rqList = getCjzh().getCjrqMapRqList(BiModuleConstant.AIZT_SHOP);
            if (CollectionUtils.isEmpty(rqList)) {
                return;
            }

            List<List<String>> lists = Lists.partition(rqList, 31);
            for (List<String> list : lists) {
                if (cjAdbrainOneReport(AiztoneConst.TYPE_AIZTONE_AIZT_SHOP, RqlxConst.DAY, list, AIZTONE_REPORT_SPLIT_TYPE_DAY)) {
                    getBiInfo().getCjrq().setAizt_shop(list.get(list.size() - 1));
                    BiThreadLocalObjects.getMsgBean().getAizt_shop().addAll(list);
                    saveRPA(getBiInfo(), getCjzh(), CodeConst.AIZTONE_AIZT, list);
                } else {
                    break;
                }
            }
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
    }


    /**
     * 2024-12-19日
     * 营销场景：全店智投（有商品维度）和店铺直达（没有商品维度）
     * 所以：店铺运营店铺维度 不一定等于  商品维度
     */
    @Override
    public void cjdp() {
        try {
            List<String> rqList = getCjzh().getCjrqMapRqList(BiModuleConstant.AIZTONE_DPSUMMARY);
            if (CollectionUtils.isEmpty(rqList)) {
                return;
            }

            for (String rq : rqList) {
                String dpdata = AiztOneHttpClientV2.query(getCjzh(), rq);
                if (dpdata == null) {
                    return;
                }

                String dpdata2 = AiztOneHttpClientV2.chargeSum(getCjzh(), rq);
                if (dpdata2 == null) {
                    return;
                }

                biDpYxsjTsDao.updateRow(getBiInfo().getQd(), getBiInfo().getUserid(), rq, "tx_aiztone", dpdata2);
                biDpYxsjTsDao.updateRow(getBiInfo().getQd(), getBiInfo().getUserid(), rq, "tx_aiztone_dpyy", dpdata);
                getBiInfo().getCjrq().setAiztone_dpsummary(rq);
                BiThreadLocalObjects.getMsgBean().getAiztone_dpsummary().add(rq);
            }
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
    }

    @Override
    public void noauth() {
        if (send(BiModuleConstant.AIZTONE_AIZT_SHOP)) {
            BiThreadLocalObjects.getMsgBean().getAizt_shop().add(BiDateUtil.getLastday());
            BiThreadLocalObjects.getMsgBean().getAiztone_dpsummary().add(BiDateUtil.getLastday());
        }
    }

    @Override
    public BiAuthResult authCheck(Cjzh cjzh) {
        return aiztoneAuth(cjzh);
    }

    @Override
    public void notCj() {
        BiThreadLocalObjects.getBiInfo().getCjrq().setAizt_shop(BiDateUtil.getLastday());
        BiThreadLocalObjects.getBiInfo().getCjrq().setAiztone_dpsummary(BiDateUtil.getLastday());
        BiThreadLocalObjects.getMsgBean().getAizt_shop().add(BiDateUtil.getLastday());
        BiThreadLocalObjects.getMsgBean().getAiztone_dpsummary().add(BiDateUtil.getLastday());
    }

    @Override
    public boolean dpconfig(BiDpConfig biDpConfig) {
        return biDpConfig.getAizt() == 0;
    }

    @Override
    protected boolean saveCjrq2Cjzh() {
        Cjzh cjzh = getCjzh();
        if (cjzh.getAiztOne() == 0) {
            return false;
        }
        BiInfo biInfo = getBiInfo();
        List<String> rqList1 = BiDateUtil.getCjsj(biInfo.getStart(), biInfo.getCjrq().getAizt_shop());
        List<String> rqList2 = BiDateUtil.getCjsj(biInfo.getStart(), biInfo.getCjrq().getAiztone_dpsummary());
        if (CollectionUtils.isEmpty(rqList1) && CollectionUtils.isEmpty(rqList2)) {
            return false;
        }
        cjzh.putCjrqMap(BiModuleConstant.AIZT_SHOP, rqList1);
        cjzh.putCjrqMap(BiModuleConstant.AIZTONE_DPSUMMARY, rqList2);
        return true;
    }
}
