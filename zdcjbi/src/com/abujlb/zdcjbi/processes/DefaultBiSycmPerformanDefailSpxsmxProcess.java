package com.abujlb.zdcjbi.processes;

import com.abujlb.zdcjbi.annos.BiPro;
import com.abujlb.zdcjbi.auth.BiAuthResult;
import com.abujlb.zdcjbi.bean.BiDpConfig;
import com.abujlb.zdcjbi.bean.BiInfo;
import com.abujlb.zdcjbi.constant.BiModuleConstant;
import com.abujlb.zdcjbi.constant.BiProcessIndex;
import com.abujlb.zdcjbi.cookie.Cjzh;
import com.abujlb.zdcjbi.http.SycmPerformanceHttpClient;
import com.abujlb.zdcjbi.oss.BiDataOssUtil;
import com.abujlb.zdcjbi.util.BiDateUtil;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import redis.clients.jedis.Jedis;

import java.io.File;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * BI 生参-客服数据<p/>
 * <p>
 * 生意参谋-服务-绩效明细 成交明细 商品销售明细 页面数据采集
 *
 * <AUTHOR>
 * @date 2024/9/14
 */
@Component
@BiPro(value = BiProcessIndex.SYCM_PERFORMANCE_DETAIL_SPXSMX, localRunNotInclude = true)
public class DefaultBiSycmPerformanDefailSpxsmxProcess extends AbstractBiProcess {

    @Override
    public void cj() {
        int max = 3;
        int curr = 1;
        for (String rq : getCjzh().getCjrqMapRqList(BiModuleConstant.SYCM_PERFORMANCE_ITEM_SALE_DETAIL)) {
            String excelId = getExcelId(getCjzh(), rq);
            if (excelId == null) {
                return;
            }

            String status = SycmPerformanceHttpClient.sycmPerformanceItemSaleDetailAsyncExcelStatus(getCjzh(), excelId);
            if (status == null || !status.equalsIgnoreCase("ok")) {
                return;
            }

            String url = SycmPerformanceHttpClient.sycmPerformanceItemSaleDetailAsyncExcelUrl(getCjzh(), excelId);
            if (url == null) {
                return;
            }

            String temppath = SycmPerformanceHttpClient.sycmPerformanceItemSaleDetailAsyncExcelDownload(getCjzh(), url);
            if (temppath == null) {
                return;
            }

            File file = null;
            try {
                file = new File(temppath);
                BiDataOssUtil.upload(file, "bi_jysj/" + getCjzh().getUserid() + "/" + rq + "/sycm_performance_item_sale_detail.xlsx");

                getBiInfo().getCjrq().setSycm_performance_item_sale_detail(rq);

                delExcelId(getCjzh(), rq, excelId);

                curr++;
                if (curr > max) {
                    break;
                }

                TimeUnit.SECONDS.sleep(10);
            } catch (Exception e) {
                LOG.error(e.getMessage(), e);
                return;
            } finally {
                if (file != null && file.exists()) {
                    file.delete();
                }
            }
        }
    }

    private void delExcelId(Cjzh cjzh, String rq, String excelId) {
        Jedis jedis = null;
        try {
            jedis = redisDao.getJedis();
            String key = "sycm_permance_item_sale_detail_" + cjzh.getUserid() + "_" + rq;
            jedis.del(key);

            SycmPerformanceHttpClient.sycmPerformanceItemSaleDetailExcelDelete(getCjzh(), excelId);
        } catch (Exception e) {
            redisDao.returnBrokenResource(jedis);
            jedis = null;
            LOG.error(e.getMessage(), e);
        } finally {
            redisDao.returnResource(jedis);
        }
    }

    private String getExcelId(Cjzh cjzh, String rq) {
        Jedis jedis = null;
        try {
            jedis = redisDao.getJedis();
            String key = "sycm_permance_item_sale_detail_" + cjzh.getUserid() + "_" + rq;
            if (jedis.exists(key)) {
                return jedis.get(key);
            } else {
                String excelId = SycmPerformanceHttpClient.sycmPerformanceItemSaleDetailAsyncExcel(getCjzh(), rq);
                try {
                    TimeUnit.SECONDS.sleep(30);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }

                if (excelId != null) {
                    jedis.set(key, excelId);
                }
                return excelId;
            }
        } catch (Exception e) {
            redisDao.returnBrokenResource(jedis);
            jedis = null;
            LOG.error(e.getMessage(), e);
        } finally {
            redisDao.returnResource(jedis);
        }
        return null;
    }

    @Override
    public BiAuthResult authCheck(Cjzh cjzh) {
        return new BiAuthResult(BiAuthResult.SUCCESS, "");
    }

    @Override
    public boolean dpconfig(BiDpConfig biDpConfig) {
        return biDpConfig.getSycm_performance() == 1;
    }

    @Override
    protected boolean saveCjrq2Cjzh() {
        if (BiDateUtil.currHour() < 12 || getBiInfo().getCjrq().getSycm_performance_service_account()==null || !getBiInfo().getCjrq().getSycm_performance_service_account().equals(BiDateUtil.getLastday())) {
            return false;
        }
        Cjzh cjzh = getCjzh();
        BiInfo biInfo = getBiInfo();

        List<String> rqList1 = BiDateUtil.getCjsj(biInfo.getStart(), biInfo.getCjrq().getSycm_performance_item_sale_detail());
        if (CollectionUtils.isEmpty(rqList1)) {
            return false;
        }
        cjzh.putCjrqMap(BiModuleConstant.SYCM_PERFORMANCE_ITEM_SALE_DETAIL, rqList1);
        return true;
    }

}
