package com.abujlb.zdcjbi.processes;

import com.abujlb.zdcjbi.annos.BiPro;
import com.abujlb.zdcjbi.auth.BiAuthResult;
import com.abujlb.zdcjbi.bean.BiDpConfig;
import com.abujlb.zdcjbi.bean.BiInfo;
import com.abujlb.zdcjbi.constant.AiztoneConst;
import com.abujlb.zdcjbi.constant.BiModuleConstant;
import com.abujlb.zdcjbi.constant.BiProcessIndex;
import com.abujlb.zdcjbi.cookie.Cjzh;
import com.abujlb.zdcjbi.thread.BiThreadLocalObjects;
import com.abujlb.zdcjbi.util.BiDateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * BI直通车推广效果分析采集
 *
 * <AUTHOR>
 */
@Component
@BiPro(value = BiProcessIndex.AIZTONE_ZTC_CROWD_TGFX, localRunNotInclude = true)
public class DefaultBiAdbrainOneTgfxZtcCrowdsProcess extends AbstractBiProcess {

    @Override
    public void cj() {
        if (BiDateUtil.currHour() < 10) {
            return;
        }

        List<String> rqList = getCjzh().getCjrqMapRqList(BiModuleConstant.ZTCCROWDTGFX);

        if (rqList.size() == 1 && rqList.get(0).equalsIgnoreCase(BiDateUtil.getLastday())) {
            //多采集一天是为了自然流量曝光量和自然流量曝光金额字段
            //这两个字段会延迟一天
            rqList.add(0, BiDateUtil.getDayBefore(BiDateUtil.getLastday(), 1));
            // https://www.tapd.cn/47708728/prong/stories/view/1147708728001004604
            rqList.add(0, BiDateUtil.getDayBefore(BiDateUtil.getLastday(), 15));
        }

        List<String> msgRqList = cjAdbrainAiztone(AiztoneConst.TYPE_AIZTONE_ZTC_CROWD, rqList,true);
        if (msgRqList!=null && !msgRqList.isEmpty()) {
            BiThreadLocalObjects.getMsgBean().getZtc_crowd_tgfx().addAll(rqList);

            if (getBiInfo().getCjrq().getZtc_crowd_tgfx() == null || getBiInfo().getCjrq().getZtc_crowd_tgfx().compareTo(msgRqList.get(msgRqList.size() - 1)) < 0) {
                getBiInfo().getCjrq().setZtc_crowd_tgfx(msgRqList.get(msgRqList.size() - 1));
            }
        }
    }

    @Override
    public void notCj() {
        BiThreadLocalObjects.getBiInfo().getCjrq().setZtc_crowd_tgfx(BiDateUtil.getLastday());
    }

    @Override
    public BiAuthResult authCheck(Cjzh cjzh) {
        return aiztoneAuth(cjzh);
    }

    @Override
    public boolean dpconfig(BiDpConfig biDpConfig) {
        return biDpConfig.getZtc() == 0;
    }

    @Override
    protected boolean saveCjrq2Cjzh() {
        Cjzh cjzh = getCjzh();
        BiInfo biInfo = getBiInfo();
        List<String> rqList = null;
        if (StringUtils.isBlank(biInfo.getCjrq().getZtc())) {
            return false;
        } else if (StringUtils.isBlank(biInfo.getCjrq().getZtc_crowd_tgfx())) {
            rqList = BiDateUtil.getCjsj(biInfo.getStart(), biInfo.getCjrq().getZtc_crowd_tgfx());
        } else if (biInfo.getCjrq().getZtc_crowd_tgfx().compareTo(biInfo.getCjrq().getZtc()) >= 0) {
            return false;
        } else {
            rqList = BiDateUtil.getBetweenDateSkipStart(biInfo.getCjrq().getZtc_crowd_tgfx(), biInfo.getCjrq().getZtc());
        }
        if (CollectionUtils.isEmpty(rqList)) {
            return false;
        }
        cjzh.putCjrqMap(BiModuleConstant.ZTCCROWDTGFX, rqList);
        return true;
    }
}
