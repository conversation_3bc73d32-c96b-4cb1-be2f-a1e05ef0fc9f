package com.abujlb.zdcjbi.processes;

import com.abujlb.zdcjbi.annos.BiPro;
import com.abujlb.zdcjbi.auth.BiAuth;
import com.abujlb.zdcjbi.auth.BiAuthResult;
import com.abujlb.zdcjbi.bean.BiDpConfig;
import com.abujlb.zdcjbi.bean.BiInfo;
import com.abujlb.zdcjbi.constant.*;
import com.abujlb.zdcjbi.cookie.Cjzh;
import com.abujlb.zdcjbi.thread.BiThreadLocalObjects;
import com.abujlb.zdcjbi.util.BiDateUtil;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * BI 全站推广采集<p/>
 *
 * <AUTHOR>
 * @date 2024/1/16
 */
@Component
@BiPro(value = BiProcessIndex.AIZTONE_QZTG)
public class DefaultBiAdbrainOneNormalQztgProcess extends AbstractBiProcess {

    /**
     * <ul>
     *     <li>采集地址：https://myseller.taobao.com/home.htm/alltaopromotion/</li>
     *     <li>采集页面描述：万相台无界-》报表-》宝贝主体报表-》宝贝主体数据明细</li>
     *     <li>采集方式：报表采集</li>
     *     <li>采集选项：</li>
     *     <ul>
     *         <li>报表模板：全部计划</li>
     *         <li>末次点击归因</li>
     *         <li>15天累计数据</li>
     *         <li>汇总周期：传入开始和结束日期</li>
     *         <li>时间粒度：分天</li>
     *         <li>更多筛选：
     *              <ul>
     *                  <li>按场景：多目标直投</li>
     *                  <li>其余：默认</li>
     *              </ul>
     *         </li>
     *     </ul>
     * </ul>
     */
    @Override
    public void cj() {
        List<String> rqList = getCjzh().getCjrqMapRqList(BiModuleConstant.QZTG);
        if (CollectionUtils.isEmpty(rqList)) {
            return;
        }

        List<List<String>> partition = Lists.partition(rqList, 31);
        for (List<String> rqList2 : partition) {
            if (cjAdbrainOneReport(AiztoneConst.TYPE_AIZTONE_QZTG, RqlxConst.DAY, rqList2, AIZTONE_REPORT_SPLIT_TYPE_DAY)) {
                getBiInfo().getCjrq().setQztg(rqList2.get(rqList2.size() - 1));
                BiThreadLocalObjects.getMsgBean().getQztg().addAll(rqList2);
                saveRPA(getBiInfo(), getCjzh(), CodeConst.AIZTONE_QZTG, rqList2);
            } else {
                break;
            }
        }
    }

    @Override
    public void noauth() {
        if (send(BiModuleConstant.QZTG)) {
            BiThreadLocalObjects.getMsgBean().getQztg().add(BiDateUtil.getLastday());
        }
    }

    @Override
    public void notCj() {
        BiThreadLocalObjects.getBiInfo().getCjrq().setQztg(BiDateUtil.getLastday());
        BiThreadLocalObjects.getMsgBean().getQztg().add(BiDateUtil.getLastday());
    }

    @Override
    public BiAuthResult authCheck(Cjzh cjzh) {
        BiAuthResult biAuthResult = aiztoneAuth(cjzh);
        if (biAuthResult != null && biAuthResult.authSuccess()) {
            return BiAuth.qztg(cjzh);
        }
        return biAuthResult;
    }

    @Override
    public boolean dpconfig(BiDpConfig biDpConfig) {
        return biDpConfig.getQztg() == 1;
    }

    @Override
    protected boolean saveCjrq2Cjzh() {
        Cjzh cjzh = getCjzh();
        BiInfo biInfo = getBiInfo();
        List<String> rqList = BiDateUtil.getCjsj(biInfo.getStart(), biInfo.getCjrq().getQztg());
        if (CollectionUtils.isEmpty(rqList)) {
            return false;
        }
        cjzh.putCjrqMap(BiModuleConstant.QZTG, rqList);
        return true;
    }
}
