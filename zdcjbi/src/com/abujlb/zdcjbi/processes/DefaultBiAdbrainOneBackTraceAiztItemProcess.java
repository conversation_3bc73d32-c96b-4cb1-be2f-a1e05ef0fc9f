package com.abujlb.zdcjbi.processes;

import com.abujlb.zdcjbi.annos.BiPro;
import com.abujlb.zdcjbi.auth.BiAuthResult;
import com.abujlb.zdcjbi.bean.BiDpConfig;
import com.abujlb.zdcjbi.bean.BiInfo;
import com.abujlb.zdcjbi.bean.DataprobiMsg;
import com.abujlb.zdcjbi.constant.*;
import com.abujlb.zdcjbi.cookie.Cjzh;
import com.abujlb.zdcjbi.util.BiDateUtil;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * BI 万相台无界-万相台-货品运营数据采集 回溯<p/>
 *
 * <AUTHOR>
 * @date 2024/12/10
 */
@Component
@BiPro(value = BiProcessIndex.AIZTONE_AIZT_ITEM_BACKTRACE)
public class DefaultBiAdbrainOneBackTraceAiztItemProcess extends AbstractBiProcess {

    @Override
    public void cj() {
        try {
            List<String> rqList = getCjzh().getCjrqMapRqList(BiModuleConstant.AIZT_ITEM_BACKTRACE);
            if (CollectionUtils.isEmpty(rqList)) {
                return;
            }

            List<List<String>> lists = Lists.partition(rqList, 31);
            for (List<String> list : lists) {
                if (cjAdbrainOneReport(AiztoneConst.TYPE_AIZTONE_AIZT_ITEM, RqlxConst.DAY, list, AIZTONE_REPORT_SPLIT_TYPE_DAY)) {
                    getBiInfo().getCjrq().setAizt_item_backtrace(list.get(list.size() - 1));
                    saveRPA(getBiInfo(), getCjzh(), CodeConst.AIZTONE_AIZT, list);

                    DataprobiMsg dataprobiMsg = initMsgBean(getBiInfo());
                    dataprobiMsg.setAizt_item(list);
                    sendDataprobiMq(dataprobiMsg);
                } else {
                    break;
                }
            }
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
    }

    @Override
    public BiAuthResult authCheck(Cjzh cjzh) {
        return aiztoneAuth(cjzh);
    }

    @Override
    public void notCj() {
        String end = BiDateUtil.getDayBefore(BiDateUtil.getLastday(), default_brack_trace_days);
        getBiInfo().getCjrq().setAizt_item_backtrace(end);
    }

    @Override
    public boolean dpconfig(BiDpConfig biDpConfig) {
        return biDpConfig.getAizt() == 0;
    }

    @Override
    protected boolean saveCjrq2Cjzh() {
        Cjzh cjzh = getCjzh();
        if (cjzh.getAiztOne() == 0 || getCjzh().getMsgType() == 1) {
            return false;
        }
        BiInfo biInfo = getBiInfo();

        String end = BiDateUtil.getDayBefore(BiDateUtil.today(), default_brack_trace_days);
        String aizt_item_rq = getBiInfo().getCjrq().getAizt_item();
        if (aizt_item_rq == null || aizt_item_rq.compareTo(end) <= 0) {
            return false;
        }

        List<String> rqList = null;
        if (StringUtils.isNotBlank(getBiInfo().getCjrq().getAizt_item_backtrace())) {
            rqList = BiDateUtil.getBetweenDateSkipStart(biInfo.getCjrq().getAizt_item_backtrace(), end);
        } else if (getBiInfo().getCjrq().getAizt_item() != null && getBiInfo().getCjrq().getAizt_item().compareTo(end) >= 0) {
            String rq = BiDateUtil.getDayBefore(end, 1);
            getBiInfo().getCjrq().setAizt_item_backtrace(rq);
            rqList = BiDateUtil.getBetweenDateSkipStart(biInfo.getCjrq().getAizt_item_backtrace(), end);
        } else if (getBiInfo().getCjrq().getAizt_item() != null && getBiInfo().getCjrq().getAizt_item().compareTo(end) < 0) {
            //不做任何操作
        } else if (getBiInfo().getCjrq().getAizt_item() == null) {
            //不做任何操作
        }

        if (CollectionUtils.isEmpty(rqList)) {
            return false;
        }
        cjzh.putCjrqMap(BiModuleConstant.AIZT_ITEM_BACKTRACE, rqList);
        return true;
    }
}
