package com.abujlb.zdcjbi.processes;

import com.abujlb.mq.AliyunMq2;
import com.abujlb.mq.JobMessage;
import com.abujlb.zdcjbi.annos.BiPro;
import com.abujlb.zdcjbi.auth.BiAuth;
import com.abujlb.zdcjbi.auth.BiAuthResult;
import com.abujlb.zdcjbi.bean.BiDpConfig;
import com.abujlb.zdcjbi.bean.BiInfo;
import com.abujlb.zdcjbi.bean.msg.SycmflowMsgBean;
import com.abujlb.zdcjbi.constant.AiztoneConst;
import com.abujlb.zdcjbi.constant.BiModuleConstant;
import com.abujlb.zdcjbi.constant.BiProcessIndex;
import com.abujlb.zdcjbi.cookie.Cjzh;
import com.abujlb.zdcjbi.thread.BiThreadLocalObjects;
import com.abujlb.zdcjbi.util.BiDateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * BI 全站推广分析数据采集<p/>
 * <p>
 * 核算到单品。
 *
 * <AUTHOR>
 * @date 2024/6/3
 */
@Component
@BiPro(value = BiProcessIndex.AIZTONE_QZTG_TGFX, localRunNotInclude = true)
public class DefaultBiAdbrainOneTgfxQztgProcess extends AbstractBiProcess {


    @Autowired
    private AliyunMq2 aliyunMq2;

    @Override
    public void cj() {
        if (BiDateUtil.currHour() < 10) {
            return;
        }

        List<String> rqList = getCjzh().getCjrqMapRqList(BiModuleConstant.QZTGTGFX);
        if (rqList.size() == 1 && rqList.get(0).equalsIgnoreCase(BiDateUtil.getLastday())) {
            //需求： https://www.tapd.cn/47708728/prong/stories/view/1147708728001004604
            rqList.add(0, BiDateUtil.getDayBefore(BiDateUtil.getLastday(), 15));
        }

        List<String> msgRqList = cjAdbrainAiztone(AiztoneConst.TYPE_AIZTONE_QZTG, rqList);
        if (!msgRqList.isEmpty()) {
            BiThreadLocalObjects.getMsgBean().getQztg_tgfx().addAll(rqList);

            if (getBiInfo().getCjrq().getQztg_tgfx() == null || getBiInfo().getCjrq().getQztg_tgfx().compareTo(msgRqList.get(msgRqList.size() - 1)) < 0) {
                getBiInfo().getCjrq().setQztg_tgfx(msgRqList.get(msgRqList.size() - 1));
            }
        }

        if (CollectionUtils.isNotEmpty(BiThreadLocalObjects.getMsgBean().getQztg_tgfx())) {
            //发送datapro消息
            SycmflowMsgBean sycmflowMsgBean = new SycmflowMsgBean();
            sycmflowMsgBean.setType(1);
            sycmflowMsgBean.setRqList(BiThreadLocalObjects.getMsgBean().getQztg_tgfx());
            sycmflowMsgBean.setUserid(String.valueOf(getCjzh().getUserid()));
            aliyunMq2.send("datapro", sycmflowMsgBean.getUuid(), new JobMessage("sycmflow_processor", sycmflowMsgBean));
        }
    }

    @Override
    public void notCj() {
        BiThreadLocalObjects.getBiInfo().getCjrq().setQztg_tgfx(BiDateUtil.getLastday());
    }

    @Override
    public BiAuthResult authCheck(Cjzh cjzh) {
        BiAuthResult biAuthResult = aiztoneAuth(cjzh);
        if (biAuthResult != null && biAuthResult.authSuccess()) {
            return BiAuth.qztg(cjzh);
        }
        return biAuthResult;
    }

    @Override
    public boolean dpconfig(BiDpConfig biDpConfig) {
        return biDpConfig.getQztg() == 1;
    }

    @Override
    protected boolean saveCjrq2Cjzh() {
        Cjzh cjzh = getCjzh();
        BiInfo biInfo = getBiInfo();
        List<String> rqList = null;
        if (StringUtils.isBlank(biInfo.getCjrq().getQztg())) {
            return false;
        } else if (StringUtils.isBlank(biInfo.getCjrq().getQztg_tgfx())) {
            rqList = BiDateUtil.getCjsj(biInfo.getStart(), biInfo.getCjrq().getQztg_tgfx());
        } else if (biInfo.getCjrq().getQztg_tgfx().compareTo(biInfo.getCjrq().getQztg()) >= 0) {
            return false;
        } else {
            rqList = BiDateUtil.getBetweenDateSkipStart(biInfo.getCjrq().getQztg_tgfx(), biInfo.getCjrq().getQztg());
        }
        if (CollectionUtils.isEmpty(rqList)) {
            return false;
        }
        cjzh.putCjrqMap(BiModuleConstant.QZTGTGFX, rqList);
        return true;
    }
}
