package com.abujlb.zdcjbi.processes;

import com.abujlb.zdcjbi.annos.BiPro;
import com.abujlb.zdcjbi.auth.BiAuthResult;
import com.abujlb.zdcjbi.bean.BiDpConfig;
import com.abujlb.zdcjbi.bean.BiInfo;
import com.abujlb.zdcjbi.constant.BiModuleConstant;
import com.abujlb.zdcjbi.constant.BiProcessIndex;
import com.abujlb.zdcjbi.cookie.Cjzh;
import com.abujlb.zdcjbi.http.SycmPerformanceHttpClient;
import com.abujlb.zdcjbi.oss.BiDataOssUtil;
import com.abujlb.zdcjbi.thread.BiThreadLocalObjects;
import com.abujlb.zdcjbi.tsdao.BiDpYxsjTsDao;
import com.abujlb.zdcjbi.util.BiDateUtil;
import com.alibaba.fastjson.JSONArray;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.List;

/**
 * BI 生参-客服数据<p/>
 * <p>
 * 生意参谋-服务-核心监控 页面数据采集
 * <p>
 * 根据配置管理里  询单有效时长配置的不同  这里暂且定义为 n  这里需要对 DefaultBiSycmPerformanceCoreMonitorProcess里采集的数据进行回溯 具体回溯天数按照 配置管理里的来进行
 * <p>
 * 所以这个日期 一般比 真实采集日期要小n天
 *
 * <AUTHOR>
 * @date 2024/9/14
 */
@Component
@BiPro(value = BiProcessIndex.SYCM_PERFORMANCE_CORE_MONITOR_BACKTRACE, localRunNotInclude = true)
public class DefaultBiSycmPerformanceCoreMonitorBackTrackProcess extends AbstractBiProcess {

    @Override
    public void cj() {
        int max = 3;
        int curr = 1;
        for (String rq : getCjzh().getCjrqMapRqList(BiModuleConstant.SYCM_PERFORMANCE_BACKTRACE)) {
            String url = SycmPerformanceHttpClient.coreMonitorRankExcel(getCjzh(), rq);
            if (url == null) {
                return;
            }

            String temppath = SycmPerformanceHttpClient.coreMonitorRankExcelDownload(getCjzh(), url);
            if (temppath == null) {
                return;
            }

            File file = null;
            try {
                file = new File(temppath);
                BiDataOssUtil.upload(file, "bi_jysj/" + getCjzh().getUserid() + "/" + rq + "/core_monitor_rank.xlsx");

                getBiInfo().getCjrq().setSycm_performance_core_monitor_backtrace(rq);
                BiThreadLocalObjects.getMsgBean().getSycm_performance_core_monitor().add(rq);
                curr++;
                if (curr > max) {
                    break;
                }
            } catch (Exception e) {
                LOG.error(e.getMessage(), e);
                return;
            } finally {
                if (file != null && file.exists()) {
                    file.delete();
                }
            }
        }
    }

    @Override
    public void cjdp() {
        int max = 3;
        int curr = 1;
        for (String rq : getCjzh().getCjrqMapRqList(BiModuleConstant.SYCM_PERFORMANCE_DP_BACKTRACE)) {
            JSONArray dayData = SycmPerformanceHttpClient.coreMonitorDayData(getCjzh(), rq);
            if (dayData == null) {
                return;
            }
            biDpYxsjTsDao.updateRow(getBiInfo().getQd(), getCjzh().getUserid() + "", rq, BiDpYxsjTsDao.COLUMN_SYCM_PERFORMANCE_CORE_MONITOR, dayData.toJSONString());
            getBiInfo().getCjrq().setSycm_performance_core_monitor_dp_backtrace(rq);
            BiThreadLocalObjects.getMsgBean().getSycm_performance_core_monitor_dp().add(rq);
            curr++;
            if (curr > max) {
                break;
            }
        }
    }

    @Override
    public BiAuthResult authCheck(Cjzh cjzh) {
        return new BiAuthResult(BiAuthResult.SUCCESS, "");
    }

    @Override
    public boolean dpconfig(BiDpConfig biDpConfig) {
        return biDpConfig.getSycm_performance() == 1;
    }

    @Override
    protected boolean saveCjrq2Cjzh() {
        if (BiDateUtil.currHour() < 12 || getBiInfo().getCjrq().getSycm_performance_service_account()==null || !getBiInfo().getCjrq().getSycm_performance_service_account().equals(BiDateUtil.getLastday())) {
            return false;
        }

        if (getCjzh().getValid_cst_day() == 0) {
            return false;
        }

        Cjzh cjzh = getCjzh();
        BiInfo biInfo = getBiInfo();

        String lastday = BiDateUtil.getLastday();
        String rq = BiDateUtil.getDayBefore(lastday, getCjzh().getValid_cst_day());

        List<String> rqList1 = null;
        if (StringUtils.isNotBlank(getBiInfo().getCjrq().getSycm_performance_core_monitor_backtrace())) {
            rqList1 = BiDateUtil.getBetweenDate(biInfo.getCjrq().getSycm_performance_core_monitor_backtrace(), rq);
        } else if (getBiInfo().getCjrq().getSycm_performance_core_monitor() != null && getBiInfo().getCjrq().getSycm_performance_core_monitor().compareTo(rq) >= 0) {
            getBiInfo().getCjrq().setSycm_performance_core_monitor_backtrace(rq);
        } else if (getBiInfo().getCjrq().getSycm_performance_core_monitor() != null && getBiInfo().getCjrq().getSycm_performance_core_monitor().compareTo(rq) < 0) {
            //不做任何操作
        } else if (getBiInfo().getCjrq().getSycm_performance_core_monitor() == null) {
            //不做任何操作
        }

        List<String> rqList2 = null;
        if (StringUtils.isNotBlank(getBiInfo().getCjrq().getSycm_performance_core_monitor_dp_backtrace())) {
            rqList2 = BiDateUtil.getBetweenDate(biInfo.getCjrq().getSycm_performance_core_monitor_dp_backtrace(), rq);
        } else if (getBiInfo().getCjrq().getSycm_performance_core_monitor_dp() != null && getBiInfo().getCjrq().getSycm_performance_core_monitor_dp().compareTo(rq) >= 0) {
            getBiInfo().getCjrq().setSycm_performance_core_monitor_backtrace(rq);
        } else if (getBiInfo().getCjrq().getSycm_performance_core_monitor_dp() != null && getBiInfo().getCjrq().getSycm_performance_core_monitor_dp().compareTo(rq) < 0) {
            //不做任何操作
        } else if (getBiInfo().getCjrq().getSycm_performance_core_monitor_dp() == null) {
            //不做任何操作
        }

        if (CollectionUtils.isEmpty(rqList2) && CollectionUtils.isEmpty(rqList1)) {
            return false;
        }
        cjzh.putCjrqMap(BiModuleConstant.SYCM_PERFORMANCE_BACKTRACE, rqList1);
        cjzh.putCjrqMap(BiModuleConstant.SYCM_PERFORMANCE_DP_BACKTRACE, rqList2);
        return true;
    }
}
