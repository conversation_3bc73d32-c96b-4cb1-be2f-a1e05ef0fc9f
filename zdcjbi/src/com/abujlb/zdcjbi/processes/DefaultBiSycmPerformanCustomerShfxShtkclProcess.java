package com.abujlb.zdcjbi.processes;

import com.abujlb.zdcjbi.annos.BiPro;
import com.abujlb.zdcjbi.auth.BiAuthResult;
import com.abujlb.zdcjbi.bean.BiDpConfig;
import com.abujlb.zdcjbi.bean.BiInfo;
import com.abujlb.zdcjbi.constant.BiModuleConstant;
import com.abujlb.zdcjbi.constant.BiProcessIndex;
import com.abujlb.zdcjbi.cookie.Cjzh;
import com.abujlb.zdcjbi.http.SycmPerformanceHttpClient;
import com.abujlb.zdcjbi.oss.BiDataOssUtil;
import com.abujlb.zdcjbi.util.BiDateUtil;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * BI 生参-客服数据<p/>
 *
 * 生意参谋-服务-客服绩效 售后分析 售后退款处理 页面数据采集
 *
 * <AUTHOR>
 * @date 2024/9/14
 */
@Component
@BiPro(value = BiProcessIndex.SYCM_PERFORMANCE_CUSTOMER_SHFXSHTKCL,localRunNotInclude = true)
public class DefaultBiSycmPerformanCustomerShfxShtkclProcess extends AbstractBiProcess {

    @Override
    public void cj() {
        int max = 3;
        int curr = 1;
        for (String rq : getCjzh().getCjrqMapRqList(BiModuleConstant.SYCM_PERFORMANCE_REFUND)) {
            String url = SycmPerformanceHttpClient.sycmPerformanceRefundExcel(getCjzh(), rq);
            if (url == null) {
                return;
            }

            String temppath = SycmPerformanceHttpClient.sycmPerformanceRefundExcelDownload(getCjzh(), url);
            if (temppath == null) {
                return;
            }

            File file = null;
            try {
                file = new File(temppath);
                BiDataOssUtil.upload(file, "bi_jysj/" + getCjzh().getUserid() + "/" + rq + "/sycm_performance_refund.xlsx");

                getBiInfo().getCjrq().setSycm_performance_refund(rq);
                curr++;
                if (curr > max) {
                    break;
                }

                TimeUnit.SECONDS.sleep(10);
            } catch (Exception e) {
                LOG.error(e.getMessage(), e);
                return;
            } finally {
                if (file != null && file.exists()) {
                    file.delete();
                }
            }
        }
    }

    @Override
    public BiAuthResult authCheck(Cjzh cjzh) {
        return new BiAuthResult(BiAuthResult.SUCCESS, "");
    }

    @Override
    public boolean dpconfig(BiDpConfig biDpConfig) {
        return biDpConfig.getSycm_performance() == 1;
    }

    @Override
    protected boolean saveCjrq2Cjzh() {
        if (BiDateUtil.currHour() < 12 || getBiInfo().getCjrq().getSycm_performance_service_account()==null || !getBiInfo().getCjrq().getSycm_performance_service_account().equals(BiDateUtil.getLastday())) {
            return false;
        }
        Cjzh cjzh = getCjzh();
        BiInfo biInfo = getBiInfo();

        List<String> rqList1 = BiDateUtil.getCjsj(biInfo.getStart(), biInfo.getCjrq().getSycm_performance_refund());
        if (CollectionUtils.isEmpty(rqList1) ) {
            return false;
        }
        cjzh.putCjrqMap(BiModuleConstant.SYCM_PERFORMANCE_REFUND, rqList1);
        return true;
    }

}
