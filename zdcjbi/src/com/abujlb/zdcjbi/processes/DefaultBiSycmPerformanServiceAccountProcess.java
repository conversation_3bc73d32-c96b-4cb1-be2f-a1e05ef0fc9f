package com.abujlb.zdcjbi.processes;

import com.abujlb.CommonConfig;
import com.abujlb.sqlite.SqliteDb;
import com.abujlb.sqlite.SqliteStatement;
import com.abujlb.zdcjbi.annos.BiPro;
import com.abujlb.zdcjbi.auth.BiAuthResult;
import com.abujlb.zdcjbi.bean.BiDpConfig;
import com.abujlb.zdcjbi.bean.BiInfo;
import com.abujlb.zdcjbi.bean.CustomerAccount;
import com.abujlb.zdcjbi.constant.BiModuleConstant;
import com.abujlb.zdcjbi.constant.BiProcessIndex;
import com.abujlb.zdcjbi.cookie.Cjzh;
import com.abujlb.zdcjbi.http.SycmPerformanceHttpClient;
import com.abujlb.zdcjbi.oss.BiDataOssUtil;
import com.abujlb.zdcjbi.tsdao.BiPerformanceAccountListTsDao;
import com.abujlb.zdcjbi.util.BiDateUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.sql.SQLException;
import java.util.*;

/**
 * https://sycm.taobao.com/qos/service/config/manage
 * <p>
 * BI 生参-客服配置<p/>
 * <p>
 * 生意参谋-服务-配置管理 客服配置 已计算旺旺 页面数据采集
 *
 * <AUTHOR>
 * @date 2024/9/19
 */
@Component
@BiPro(value = BiProcessIndex.SYCM_PERFORMANCE_SERVICE_ACCOUNT, localRunNotInclude = true)
public class DefaultBiSycmPerformanServiceAccountProcess extends AbstractBiProcess {

    @Autowired
    private BiPerformanceAccountListTsDao biPerformanceAccountListTsDao;

    /**
     * 采集页面：https://sycm.taobao.com/qos/service/config/manage
     * 描述：生意参谋-服务-配置管理 客服配置 已计算旺旺 页面数据采集
     * 接口分页采集
     */
    @Override
    public void cj() {
        JSONArray jsonArray = SycmPerformanceHttpClient.serviceAccountList(getCjzh());
        if (jsonArray == null) {
            return;
        }

        BiDataOssUtil.upload(jsonArray.toJSONString(), "bi_jysj/" + getCjzh().getUserid() + "/" + BiDateUtil.getLastday() + "/performance_service_account_list.json");

        List<CustomerAccount> list = new ArrayList<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            CustomerAccount account = new CustomerAccount();
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            account.setWwzh(jsonObject.getString("subName"));
            account.setWwzhmc(jsonObject.getString("nickName"));
            list.add(account);
        }

        //获取原来保存的DB
        saveDB(list);

        getBiInfo().getCjrq().setSycm_performance_service_account(BiDateUtil.getLastday());
    }

    private void saveDB(List<CustomerAccount> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        SqliteDb biSqliteDb = null;
        SqliteStatement<CustomerAccount> sqliteStatement = null;
        File file = null;
        String tempdbpath = CommonConfig.getString("tempdir") + UUID.randomUUID() + "_performance_service_account_list.db";
        try {
            file = new File(tempdbpath);
            String dbKey = "bi_jysj/" + getBiInfo().getUserid() + "/performance_service_account_list.db";
            if (BiDataOssUtil.exist(dbKey)) {
                BiDataOssUtil.download(dbKey, tempdbpath);
                biSqliteDb = new SqliteDb(tempdbpath);
                sqliteStatement = biSqliteDb.createSqliteStatement("select * from performance_service_account_list", CustomerAccount.class);
                List<CustomerAccount> customerAccounts = sqliteStatement.queryForList(null, CustomerAccount.class);
                Map<String, CustomerAccount> map = new HashMap<>();
                for (CustomerAccount customerAccount : customerAccounts) {
                    map.put(customerAccount.getWwzh(), customerAccount);
                }
                sqliteStatement.close();

                sqliteStatement = biSqliteDb.createSqliteStatement("insert into performance_service_account_list(`wwzh`,`wwzhmc`) values ({wwzh},{wwzhmc})", CustomerAccount.class);
                for (CustomerAccount account : list) {
                    if (!map.containsKey(account.getWwzh())) {
                        account.setJrsj(BiDateUtil.getCurrdateStr());
                        sqliteStatement.insert(account);
                    } else if (map.get(account.getWwzh()) != null && !map.get(account.getWwzh()).getWwzhmc().equals(account.getWwzhmc())) {
                        sqliteStatement.insert(account);
                    }
                }
                BiDataOssUtil.upload(file, dbKey);
            } else {
                biSqliteDb = new SqliteDb(tempdbpath, "CREATE TABLE performance_service_account_list (`wwzh`,`wwzhmc`) ;", null);

                sqliteStatement = biSqliteDb.createSqliteStatement("insert into performance_service_account_list(`wwzh`,`wwzhmc`) values ({wwzh},{wwzhmc})", CustomerAccount.class);
                biSqliteDb.startTransaction();
                sqliteStatement.insert(list);
                biSqliteDb.commit();
                BiDataOssUtil.upload(file, dbKey);

                for (CustomerAccount account : list) {
                    account.setJrsj(BiDateUtil.getCurrdateStr());
                }
            }

            biPerformanceAccountListTsDao.updateRows(list, getBiInfo().getYhid(), getBiInfo().getQd(), getBiInfo().getUserid());
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        } finally {
            if (sqliteStatement != null) {
                try {
                    sqliteStatement.close();
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            }
            if (biSqliteDb != null) {
                try {
                    biSqliteDb.close();
                } catch (SQLException e) {
                    throw new RuntimeException(e);
                }
            }
            if (file != null && file.exists()) {
                file.delete();
            }
        }
    }

    @Override
    public BiAuthResult authCheck(Cjzh cjzh) {
        return new BiAuthResult(BiAuthResult.SUCCESS, "");
    }

    @Override
    public boolean dpconfig(BiDpConfig biDpConfig) {
        return biDpConfig.getSycm_performance() == 1;
    }

    @Override
    protected boolean saveCjrq2Cjzh() {
        if (BiDateUtil.currHour() < 12) {
            return false;
        }
        Cjzh cjzh = getCjzh();
        BiInfo biInfo = getBiInfo();

        List<String> rqList1 = BiDateUtil.getCjsj(biInfo.getStart(), biInfo.getCjrq().getSycm_performance_service_account());
        if (CollectionUtils.isEmpty(rqList1)) {
            return false;
        }
        cjzh.putCjrqMap(BiModuleConstant.SYCM_PERFORMANCE_SERVICE_ACCOUNT, rqList1);
        return true;
    }
}
