package com.abujlb.zdcjbi.processes;

import com.abujlb.zdcjbi.annos.BiPro;
import com.abujlb.zdcjbi.auth.BiAuth;
import com.abujlb.zdcjbi.auth.BiAuthResult;
import com.abujlb.zdcjbi.bean.BiDpConfig;
import com.abujlb.zdcjbi.bean.BiInfo;
import com.abujlb.zdcjbi.bean.DataprobiMsg;
import com.abujlb.zdcjbi.constant.*;
import com.abujlb.zdcjbi.cookie.Cjzh;
import com.abujlb.zdcjbi.util.BiDateUtil;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * BI 万相台无界-多目标直投回溯<p/>
 *
 * <AUTHOR>
 * @date 2024/10/11
 */
@Component
@BiPro(value = BiProcessIndex.AIZTONE_DMBZT_BACKTRACE)
public class DefaultBiAdbrainOneBackTraceDmbztProcess extends AbstractBiProcess {


    @Override
    public void cj() {
        try {
            List<String> rqList = getCjzh().getCjrqMapRqList(BiModuleConstant.DMBZT_BACKTRACE);
            if (CollectionUtils.isEmpty(rqList)) {
                return;
            }

            List<List<String>> lists = Lists.partition(rqList, 31);
            for (List<String> list : lists) {
                if (cjAdbrainOneReport(AiztoneConst.TYPE_AIZTONE_DMBZT, RqlxConst.DAY, list, AIZTONE_REPORT_SPLIT_TYPE_DAY)) {
                    getBiInfo().getCjrq().setDmbzt_backtrace(list.get(list.size() - 1));
                    saveRPA(getBiInfo(), getCjzh(), CodeConst.AIZTONE_DMBZT, list);

                    DataprobiMsg dataprobiMsg = initMsgBean(getBiInfo());
                    dataprobiMsg.setDmbzt(list);
                    sendDataprobiMq(dataprobiMsg);
                } else {
                    break;
                }
            }
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
    }

    @Override
    public BiAuthResult authCheck(Cjzh cjzh) {
        BiAuthResult biAuthResult = aiztoneAuth(cjzh);
        if (biAuthResult != null && biAuthResult.authSuccess()) {
            return BiAuth.dmbzt(cjzh);
        }
        return biAuthResult;
    }

    @Override
    public void notCj() {
        String end = BiDateUtil.getDayBefore(BiDateUtil.getLastday(), default_brack_trace_days);
        getBiInfo().getCjrq().setDmbzt_backtrace(end);
    }

    @Override
    public boolean dpconfig(BiDpConfig biDpConfig) {
        return biDpConfig.getDmbzt() == 0;
    }

    @Override
    protected boolean saveCjrq2Cjzh() {
        Cjzh cjzh = getCjzh();
        if (cjzh.getAiztOne() == 0 || getCjzh().getMsgType() == 1) {
            return false;
        }
        BiInfo biInfo = getBiInfo();

        String end = BiDateUtil.getDayBefore(BiDateUtil.today(), default_brack_trace_days);
        String dmbzt_rq = getBiInfo().getCjrq().getDmbzt();
        if (dmbzt_rq == null || dmbzt_rq.compareTo(end) <= 0) {
            return false;
        }

        List<String> rqList = null;
        if (StringUtils.isNotBlank(getBiInfo().getCjrq().getDmbzt_backtrace())) {
            rqList = BiDateUtil.getBetweenDateSkipStart(biInfo.getCjrq().getDmbzt_backtrace(), end);
        } else if (getBiInfo().getCjrq().getDmbzt() != null && getBiInfo().getCjrq().getDmbzt().compareTo(end) >= 0) {
            String rq = BiDateUtil.getDayBefore(end, 1);
            getBiInfo().getCjrq().setDmbzt_backtrace(rq);
            rqList = BiDateUtil.getBetweenDateSkipStart(biInfo.getCjrq().getDmbzt_backtrace(), end);
        } else if (getBiInfo().getCjrq().getDmbzt() != null && getBiInfo().getCjrq().getDmbzt().compareTo(end) < 0) {
            //不做任何操作
        } else if (getBiInfo().getCjrq().getDmbzt() == null) {
            //不做任何操作
        }

        if (CollectionUtils.isEmpty(rqList)) {
            return false;
        }
        cjzh.putCjrqMap(BiModuleConstant.AIZTONE_DMBZT_BACKTRACE, rqList);
        return true;
    }

}
