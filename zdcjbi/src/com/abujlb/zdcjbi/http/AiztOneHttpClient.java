package com.abujlb.zdcjbi.http;

import com.abujlb.CommonConfig;
import com.abujlb.util.StringUtil;
import com.abujlb.zdcjbi.auth.BiAuthResult;
import com.abujlb.zdcjbi.bean.AiztoneJhzt;
import com.abujlb.zdcjbi.constant.AiztoneConst;
import com.abujlb.zdcjbi.cookie.Cjzh;
import com.abujlb.zdcjbi.log.SpiderLog;
import com.abujlb.zdcjbi.script.JsvmJSEngine;
import com.abujlb.zdcjbi.util.BiDateUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.CookieStore;
import org.apache.http.client.config.CookieSpecs;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.config.RequestConfig.Builder;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.BasicCookieStore;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.apache.log4j.Logger;

import java.io.FileOutputStream;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;


/**
 * 万相台无界http
 *
 * <AUTHOR>
 * @date 2023-7-10
 */
public class AiztOneHttpClient {

    private static final Logger LOG = Logger.getLogger(AiztOneHttpClient.class);
    private static final String TEMPPATH = CommonConfig.getString("tempdir");

    /**
     * 获取无界多天店铺维度数据
     *
     * @param cjzh
     * @param start
     * @param end
     * @param type  tx_ztc tx_ylmf tx_aizt
     * @return
     */
    public static JSONArray dayList(Cjzh cjzh, String start, String end, String type) {
        CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cjzh.getCookieStore()).build();
        HttpPost httpPost = null;
        HttpResponse httpresponse;
        try {
            String url = "https://one.alimama.com/report/query.json?r=mx_7977&bizCode=universalBP&csrfId=" + cjzh.getData("adbrain_one_csrfId");
            httpPost = new HttpPost(url);
            httpPost.setHeader("accept", "application/json, text/javascript, */*; q=0.01");
            httpPost.setHeader("accept-encoding", "gzip, deflate, br");
            httpPost.setHeader("accept-language", "zh-CN,zh;q=0.9");
            httpPost.setHeader("referer", "https://one.alimama.com/index.html");
            httpPost.setHeader("origin", "https://one.alimama.com");
            httpPost.setHeader("bx-v", "2.5.1");
            httpPost.setHeader("content-type", "application/json;charset=UTF-8");
            httpPost.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36");
            httpPost.setHeader("x-requested-with", "XMLHttpRequest");

            Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000).setConnectionRequestTimeout(20000).setCookieSpec(CookieSpecs.NETSCAPE);
            httpPost.setConfig(requestConfig.build());

            String param = null;
            switch (type) {
                case "tx_ztc":
                    param = "{\"bizCode\":\"universalBP\",\"fromRealTime\":false,\"source\":\"baseReport\",\"endTime\":\"" + end + "\",\"unifyType\":\"zhai\",\"effectEqual\":15,\"startTime\":\"" + start + "\",\"splitType\":\"day\",\"bizCodeIn\":[\"onebpSearch\"],\"queryFieldIn\":[\"adPv\",\"click\",\"charge\",\"ctr\",\"ecpc\",\"alipayInshopAmt\",\"alipayInshopNum\",\"cvr\",\"cartInshopNum\",\"itemColInshopNum\",\"shopColDirNum\",\"colNum\",\"itemColInshopCost\"],\"vsType\":\"off\",\"vsTime\":\"\",\"queryDomains\":[\"date\"],\"moduleSource\":\"foldLine\",\"rptType\":\"campaign\",\"csrfId\":\"" + cjzh.getData("adbrain_one_csrfId") + "\"}";
                    break;
                case "tx_ylmf":
                    param = "{\"bizCode\":\"universalBP\",\"fromRealTime\":false,\"source\":\"baseReport\",\"endTime\":\"" + end + "\",\"unifyType\":\"zhai\",\"effectEqual\":15,\"startTime\":\"" + start + "\",\"splitType\":\"day\",\"bizCodeIn\":[\"onebpDisplay\"],\"queryFieldIn\":[\"adPv\",\"click\",\"charge\",\"ctr\",\"ecpc\",\"alipayInshopAmt\",\"alipayInshopNum\",\"cvr\",\"cartInshopNum\",\"itemColInshopNum\",\"shopColDirNum\",\"colNum\",\"itemColInshopCost\"],\"vsType\":\"off\",\"vsTime\":\"\",\"queryDomains\":[\"date\"],\"moduleSource\":\"foldLine\",\"rptType\":\"campaign\",\"csrfId\":\"" + cjzh.getData("adbrain_one_csrfId") + "\"}";
                    break;
                case "tx_aizt":
                    param = "{\"bizCode\":\"universalBP\",\"fromRealTime\":false,\"source\":\"baseReport\",\"endTime\":\"" + end + "\",\"unifyType\":\"zhai\",\"effectEqual\":15,\"startTime\":\"" + start + "\",\"splitType\":\"day\",\"bizCodeIn\":[\"adStrategyDkx\",\"adStrategyRuHui\",\"adStrategyWholeShop\",\"onebpItemMarketing\",\"adStrategyFans\",\"wxtAgencyAI\",\"adStrategyYuRe\",\"wxtAgencySmart\",\"adStrategyCrowd\"],\"queryFieldIn\":[\"adPv\",\"click\",\"charge\",\"ctr\",\"ecpc\",\"alipayInshopAmt\",\"alipayInshopNum\",\"cvr\",\"cartInshopNum\",\"itemColInshopNum\",\"shopColDirNum\",\"colNum\",\"itemColInshopCost\"],\"vsType\":\"off\",\"vsTime\":\"\",\"queryDomains\":[\"date\"],\"moduleSource\":\"foldLine\",\"rptType\":\"campaign\",\"csrfId\":\"" + cjzh.getData("adbrain_one_csrfId") + "\"}";
                    break;
                default:
                    return null;
            }

            StringEntity stringEntity = new StringEntity(param);

            httpPost.setEntity(stringEntity);
            httpresponse = httpClient.execute(httpPost);
            HttpEntity entity = httpresponse.getEntity();
            String body = EntityUtils.toString(entity, "utf-8");
            if (!StringUtil.isJson2(body)) {
                throw new RuntimeException("AiztOneHttpClient.dayList请求失败，返回的不是json格式！" + body);
            }
            JSONObject jsonObj = JSONObject.parseObject(body);
            if (!jsonObj.containsKey("info") || !jsonObj.getJSONObject("info").getBoolean("ok")) {
                throw new RuntimeException("AiztOneHttpClient.dayList请求失败！" + body);
            }
            if (jsonObj.getJSONObject("data").containsKey("list")) {
                return jsonObj.getJSONObject("data").getJSONArray("list");
            }

            SpiderLog.addTime("aiztone_AiztOneHttpClient.dayList", true);
            return new JSONArray();
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
            SpiderLog.addTime("aiztone_AiztOneHttpClient.dayList", false);
            return null;
        } finally {
            if (httpPost != null) {
                httpPost.releaseConnection();
            }
        }
    }


    /**
     * 创建无界报表下载任务
     *
     * @param cjzh
     * @param start
     * @param end
     * @param type
     * @param splitType
     * @return
     */
    public static String createDownloadTask(Cjzh cjzh, String start, String end, String type, String splitType) {
        CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cjzh.getCookieStore()).build();
        HttpPost httpPost = null;
        HttpResponse httpresponse;
        try {
            String url = "https://one.alimama.com/report/createDownLoadTask.json?csrfId=" + cjzh.getData("adbrain_one_csrfId") + "&bizCode=universalBP&r=mx_11289";
            httpPost = new HttpPost(url);
            httpPost.setHeader("accept", "application/json, text/javascript, */*; q=0.01");
            httpPost.setHeader("accept-encoding", "gzip, deflate, br");
            httpPost.setHeader("accept-language", "zh-CN,zh;q=0.9");
            httpPost.setHeader("referer", "https://one.alimama.com/index.html");
            httpPost.setHeader("origin", "https://one.alimama.com");
            httpPost.setHeader("bx-v", "2.5.1");
            httpPost.setHeader("content-type", "application/json;charset=UTF-8");
            httpPost.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36");
            httpPost.setHeader("x-requested-with", "XMLHttpRequest");

            Builder requestConfig = RequestConfig.custom().setSocketTimeout(60000).setConnectTimeout(60000).setConnectionRequestTimeout(60000).setCookieSpec(CookieSpecs.NETSCAPE);
            httpPost.setConfig(requestConfig.build());

            StringEntity stringEntity = null;
            String param = null;
            switch (type) {
                case AiztoneConst.TYPE_AIZTONE_ZTC:
                    param = "{\"excelName\":\"bi_aiztone_ztc_" + end + "\",\"pageSize\":20,\"offset\":0,\"havingList\":[],\"endTime\":\"" + end + "\",\"unifyType\":\"zhai\",\"effectEqual\":15,\"startTime\":\"" + start + "\",\"splitType\":\"" + (splitType == null ? "day" : splitType) + "\",\"bizCodeIn\":[\"onebpSearch\"],\"subPromotionTypes\":[\"ITEM\"],\"queryFieldIn\":[\"adPv\",\"click\",\"charge\",\"ctr\",\"ecpc\",\"alipayInshopAmt\",\"alipayInshopNum\",\"cvr\",\"cartInshopNum\",\"itemColInshopNum\",\"shopColDirNum\",\"colNum\",\"itemColInshopCost\"],\"vsType\":\"off\",\"vsTime\":\"\",\"searchValue\":\"\",\"searchKey\":\"itemIdOrName\",\"queryDomains\":[\"promotion\",\"date\",\"campaign\"],\"fieldType\":\"all\",\"rptType\":\"item_promotion\",\"parentAdcName\":\"report_frame_item_promotion\",\"byPage\":false,\"fromRealTime\":false,\"source\":\"async_dowdload\",\"csrfId\":\"" + cjzh.getData("adbrain_one_csrfId") + "\",\"bizCode\":\"universalBP\"}";
                    break;
                case AiztoneConst.TYPE_AIZTONE_YLMF:
                    param = "{\"excelName\":\"bi_aiztone_ylmf_" + end + "\",\"pageSize\":20,\"offset\":0,\"havingList\":[],\"endTime\":\"" + end + "\",\"unifyType\":\"zhai\",\"effectEqual\":15,\"startTime\":\"" + start + "\",\"splitType\":\"" + (splitType == null ? "day" : splitType) + "\",\"bizCodeIn\":[\"onebpDisplay\"],\"subPromotionTypes\":[\"ITEM\"],\"queryFieldIn\":[\"adPv\",\"click\",\"charge\",\"ctr\",\"ecpc\",\"alipayInshopAmt\",\"alipayInshopNum\",\"cvr\",\"cartInshopNum\",\"itemColInshopNum\",\"shopColDirNum\",\"colNum\",\"itemColInshopCost\"],\"vsType\":\"off\",\"vsTime\":\"\",\"searchValue\":\"\",\"searchKey\":\"itemIdOrName\",\"queryDomains\":[\"promotion\",\"date\",\"campaign\"],\"fieldType\":\"all\",\"rptType\":\"item_promotion\",\"parentAdcName\":\"report_frame_item_promotion\",\"byPage\":false,\"fromRealTime\":false,\"source\":\"async_dowdload\",\"csrfId\":\"" + cjzh.getData("adbrain_one_csrfId") + "\",\"bizCode\":\"universalBP\"}";
                    break;
                case AiztoneConst.TYPE_AIZTONE_AIZT:
                    param = "{\"excelName\":\"bi_aiztone_aizt_" + end + "\",\"pageSize\":20,\"offset\":0,\"havingList\":[],\"endTime\":\"" + end + "\",\"unifyType\":\"zhai\",\"effectEqual\":15,\"startTime\":\"" + start + "\",\"splitType\":\"" + (splitType == null ? "day" : splitType) + "\",\"bizCodeIn\":[\"adStrategyDkx\",\"adStrategyRuHui\",\"adStrategyWholeShop\",\"onebpItemMarketing\",\"adStrategyFans\",\"wxtAgencyAI\",\"adStrategyYuRe\",\"wxtAgencySmart\",\"adStrategyCrowd\"],\"subPromotionTypes\":[\"ITEM\"],\"queryFieldIn\":[\"adPv\",\"click\",\"charge\",\"ctr\",\"ecpc\",\"alipayInshopAmt\",\"alipayInshopNum\",\"cvr\",\"cartInshopNum\",\"itemColInshopNum\",\"shopColDirNum\",\"colNum\",\"itemColInshopCost\"],\"vsType\":\"off\",\"vsTime\":\"\",\"searchValue\":\"\",\"searchKey\":\"itemIdOrName\",\"queryDomains\":[\"promotion\",\"date\",\"campaign\"],\"fieldType\":\"all\",\"rptType\":\"item_promotion\",\"parentAdcName\":\"report_frame_item_promotion\",\"byPage\":false,\"fromRealTime\":false,\"source\":\"async_dowdload\",\"csrfId\":\"" + cjzh.getData("adbrain_one_csrfId") + "\",\"bizCode\":\"universalBP\"}";
                    break;
                case AiztoneConst.TYPE_AIZTONE_ZTC_KEYWORD:
                    //直通车关键词
                    param = "{\"lite2\":false,\"excelName\":\"bi_aiztone_ztc_keywords_" + end + "\",\"queryFieldIn\":[\"adPv\",\"click\",\"charge\",\"colNum\",\"itemColInshopNum\",\"shopColDirNum\",\"cartInshopNum\",\"cartDirNum\",\"cartIndirNum\",\"alipayInshopAmt\",\"alipayDirAmt\",\"alipayIndirAmt\",\"alipayInshopNum\",\"alipayDirNum\",\"alipayIndirNum\",\"alipayInshopUv\",\"newAlipayInshopUv\",\"inshopPotentialUv\",\"inshopUv\"],\"_list\":{\"pageSize\":20,\"offset\":0,\"havingList\":[],\"bidWordTypeIn\":[\"word\"],\"searchValue\":\"\",\"searchKey\":\"strategyBidwordNameLike\",\"queryDomains\":[\"word\",\"adgroup\"],\"bizCodeIn\":[\"onebpSearch\"]},\"pageSize_mx_list\":20,\"offset\":0,\"offset_mx_list\":0,\"havingList\":[],\"havingList_mx_list\":[],\"bidWordTypeIn\":[\"word\"],\"bidWordTypeIn_mx_list\":[\"word\"],\"endTime\":\"" + end + "\",\"unifyType\":\"zhai\",\"effectEqual\":15,\"startTime\":\"" + start + "\",\"splitType\":\"" + splitType + "\",\"bizCodeIn\":[\"onebpSearch\"],\"_sum\":{\"bizCodeIn\":[\"onebpSearch\",\"onebpStarShop\"],\"vsType\":\"off\",\"vsTime\":\"\"},\"bizCodeIn_mx_sum\":[\"onebpSearch\",\"onebpStarShop\"],\"isKeyWordNotContainChase\":\"true\",\"vsType\":\"off\",\"vsType_mx_sum\":\"off\",\"vsTime_mx_sum\":\"\",\"searchValue\":\"\",\"searchValue_mx_list\":\"\",\"searchKey_mx_list\":\"strategyBidwordNameLike\",\"queryDomains\":[\"word\",\"adgroup\",\"date\"],\"queryDomains_mx_list\":[\"word\",\"adgroup\"],\"bizCodeIn_mx_list\":[\"onebpSearch\"],\"pageSize\":20,\"searchKey\":\"strategyBidwordNameLike\",\"fieldType\":\"selected\",\"rptType\":\"bidword\",\"parentAdcName\":\"report_frame_bidword\",\"byPage\":false,\"fromRealTime\":false,\"source\":\"async_dowdload\",\"csrfId\":\"" + cjzh.getData("adbrain_one_csrfId") + "\",\"bizCode\":\"universalBP\",\"loginPointId\":\"" + cjzh.getData("adbrain_one_loginPointId") + "\"}";
                    break;
                case AiztoneConst.TYPE_AIZTONE_ZTC_CROWD:
                    //直通车人群
                    param = "{\"excelName\":\"bi_aiztone_ztc_crowds_" + end + "\",\"pageSize\":20,\"offset\":0,\"havingList\":[],\"endTime\":\"" + end + "\",\"unifyType\":\"zhai\",\"effectEqual\":15,\"startTime\":\"" + start + "\",\"splitType\":\"" + (splitType == null ? "day" : splitType) + "\",\"bizCodeIn\":[\"onebpSearch\"],\"filterAppendSubwayChannel\":true,\"filterNullCrowdSubwayTag\":true,\"queryFieldIn\":[\"adPv\",\"click\",\"charge\",\"ctr\",\"ecpc\",\"alipayInshopAmt\",\"alipayInshopNum\",\"cvr\",\"cartInshopNum\",\"itemColInshopNum\",\"shopColDirNum\",\"colNum\",\"itemColInshopCost\"],\"vsType\":\"off\",\"vsTime\":\"\",\"searchValue\":\"\",\"searchKey\":\"strategyTargetTitleLike\",\"queryDomains\":[\"crowd\",\"campaign\",\"adgroup\",\"promotion\",\"date\"],\"fieldType\":\"all\",\"rptType\":\"crowd\",\"parentAdcName\":\"report_frame_crowd\",\"byPage\":false,\"fromRealTime\":false,\"source\":\"async_dowdload\",\"csrfId\":\"" + cjzh.getData("adbrain_one_csrfId") + "\",\"bizCode\":\"universalBP\"}";
                    break;
                case AiztoneConst.TYPE_AIZTONE_ZTC_UNIT:
                    //直通车单元
                    param = "{\"excelName\":\"bi_aiztone_ztc_units_" + end + "\",\"queryFieldIn\":[\"adPv\",\"click\",\"charge\",\"ctr\",\"ecpc\",\"alipayInshopAmt\",\"alipayInshopNum\",\"cvr\",\"cartInshopNum\",\"itemColInshopNum\",\"shopColDirNum\",\"colNum\",\"itemColInshopCost\"],\"pageSize\":20,\"offset\":0,\"havingList\":[],\"endTime\":\"" + end + "\",\"unifyType\":\"zhai\",\"effectEqual\":15,\"startTime\":\"" + start + "\",\"splitType\":\"" + (splitType == null ? "day" : splitType) + "\",\"bizCodeIn\":[\"onebpSearch\"],\"vsType\":\"off\",\"vsTime\":\"\",\"searchValue\":\"\",\"searchKey\":\"strategyCampaignIdOrName\",\"queryDomains\":[\"adgroup\"],\"fieldType\":\"all\",\"rptType\":\"adgroup\",\"parentAdcName\":\"report_frame_adgroup\",\"byPage\":false,\"fromRealTime\":false,\"source\":\"async_dowdload\",\"csrfId\":\"" + cjzh.getData("adbrain_one_csrfId") + "\",\"bizCode\":\"universalBP\"}";
                    break;
                case AiztoneConst.TYPE_AIZTONE_DMBZT:
                    param = "{\"excelName\":\"bi_aiztone_dmbzt_" + end + "\",\"pageSize\":20,\"offset\":0,\"havingList\":[],\"endTime\":\"" + end + "\",\"unifyType\":\"zhai\",\"effectEqual\":15,\"startTime\":\"" + start + "\",\"splitType\":\"" + (splitType == null ? "day" : splitType) + "\",\"bizCodeIn\":[\"adStrategyMultiAim\"],\"subPromotionTypes\":[\"ITEM\"],\"queryFieldIn\":[\"adPv\",\"click\",\"charge\",\"ctr\",\"ecpc\",\"alipayInshopAmt\",\"alipayInshopNum\",\"cvr\",\"cartInshopNum\",\"itemColInshopNum\",\"shopColDirNum\",\"colNum\",\"itemColInshopCost\"],\"vsType\":\"off\",\"vsTime\":\"\",\"searchValue\":\"\",\"searchKey\":\"itemIdOrName\",\"queryDomains\":[\"promotion\",\"date\",\"campaign\"],\"fieldType\":\"all\",\"rptType\":\"item_promotion\",\"parentAdcName\":\"report_frame_item_promotion\",\"byPage\":false,\"fromRealTime\":false,\"source\":\"async_dowdload\",\"csrfId\":\"" + cjzh.getData("adbrain_one_csrfId") + "\",\"bizCode\":\"universalBP\"}";
                    break;
                case AiztoneConst.TYPE_AIZTONE_QZTG:
                    param = "{\"lite2\":false,\"excelName\":\"bi_aiztone_qztg_" + end + "\",\"bizCode\":\"universalBP\",\"tab\":\"item\",\"startTime\":\"" + start + "\",\"endTime\":\"" + end + "\",\"effectEqual\":\"15\",\"unifyType\":\"kuan\",\"pageSize\":40,\"offset\":0,\"searchKey\":\"\",\"searchValue\":\"\",\"bizCodeIn\":[\"onebpSite\"],\"fromRealTime\":false,\"source\":\"async_dowdload\",\"byPage\":false,\"totalTag\":false,\"rptType\":\"item_promotion\",\"havingList\":[],\"splitType\":\"" + (splitType == null ? "day" : splitType) + "\",\"subPromotionTypes\":[\"ITEM\"],\"queryDomains\":[\"promotion\",\"campaign\",\"date\"],\"queryFieldIn\":[\"adPv\",\"click\",\"charge\",\"alipayDirAmt\",\"alipayDirNum\",\"alipayDirCvr\",\"dirRoi\"],\"fieldType\":\"all\",\"parentAdcName\":\"onebp_site_report_field\",\"csrfId\":\"" + cjzh.getData("adbrain_one_csrfId") + "\"}";
                    break;
                case AiztoneConst.TYPE_AIZTONE_XSTG:
                    param = "{\"lite2\":false,\"excelName\":\"bi_aiztone_xstg_" + end + "\",\"fromRealTime\":false,\"rptType\":\"download_scene_report\",\"startTime\":\"" + start + "\",\"endTime\":\"" + end + "\",\"unifyType\":\"zhai\",\"effectIn\":[15],\"queryDomainType\":\"queryDomain\",\"queryDomains\":[\"account\",\"scene\",\"campaign\",\"date\",\"promotion\"],\"bizCodeIn\":[\"onebpAdStrategyLiuZi\"],\"splitType\":\"" + (splitType == null ? "day" : splitType) + "\",\"csrfId\":\"" + cjzh.getData("adbrain_one_csrfId") + "\",\"bizCode\":\"universalBP\"}";
                    break;
                case AiztoneConst.TYPE_AIZTONE_AIZT_ITEM:
                    param = "{\"lite2\":false,\"excelName\":\"bi_aiztone_aizt_item_" + end + "\",\"queryFieldIn\":[\"adPv\",\"click\",\"charge\",\"alipayInshopAmt\",\"alipayInshopNum\"],\"pageSize\":20,\"offset\":0,\"havingList\":[],\"endTime\":\"" + end + "\",\"unifyType\":\"zhai\",\"effectEqual\":15,\"startTime\":\"" + start + "\",\"splitType\":\"" + splitType + "\",\"bizCodeIn\":[\"onebpItemMarketing\"],\"subPromotionTypes\":[],\"vsType\":\"off\",\"vsTime\":\"\",\"searchValue\":\"\",\"searchKey\":\"itemIdOrName\",\"queryDomains\":[\"promotion\",\"date\",\"campaign\"],\"fieldType\":\"all\",\"rptType\":\"item_promotion\",\"parentAdcName\":\"report_frame_item_promotion\",\"byPage\":false,\"fromRealTime\":false,\"source\":\"async_dowdload\",\"csrfId\":\"" + cjzh.getData("adbrain_one_csrfId") + "\",\"bizCode\":\"universalBP\",\"loginPointId\":\"" + cjzh.getData("adbrain_one_loginPointId") + "\"}";
                    break;
                case AiztoneConst.TYPE_AIZTONE_AIZT_SHOP:
                    param = "{\"lite2\":false,\"excelName\":\"bi_aiztone_aizt_shop_" + end + "\",\"pageSize\":20,\"offset\":0,\"havingList\":[],\"endTime\":\"" + end + "\",\"unifyType\":\"zhai\",\"effectEqual\":15,\"startTime\":\"" + start + "\",\"splitType\":\"" + splitType + "\",\"bizCodeIn\":[\"onebpAdStrategyWholeShop\",\"onebpStarShop\"],\"subPromotionTypes\":[],\"queryFieldIn\":[\"adPv\",\"click\",\"charge\",\"alipayInshopAmt\",\"alipayInshopNum\",\"cvr\"],\"vsType\":\"off\",\"vsTime\":\"\",\"searchValue\":\"\",\"searchKey\":\"itemIdOrName\",\"queryDomains\":[\"promotion\",\"date\",\"campaign\"],\"fieldType\":\"all\",\"rptType\":\"item_promotion\",\"parentAdcName\":\"report_frame_item_promotion\",\"byPage\":false,\"fromRealTime\":false,\"source\":\"async_dowdload\",\"csrfId\":\"" + cjzh.getData("adbrain_one_csrfId") + "\",\"bizCode\":\"universalBP\",\"loginPointId\":\"" + cjzh.getData("adbrain_one_loginPointId") + "\"}";
                    break;
                case AiztoneConst.TYPE_AIZTONE_CYBB:
                    param = "{\"lite2\":false,\"excelName\":\"bi_aiztone_cybb_" + end + "\",\"pageSize\":20,\"offset\":0,\"havingList\":[],\"endTime\":\"" + end + "\",\"unifyType\":\"zhai\",\"effectEqual\":15,\"startTime\":\"" + start + "\",\"splitType\":\"" + splitType + "\",\"queryFieldIn\":[\"adPv\",\"click\",\"charge\",\"ctr\",\"ecpc\",\"alipayInshopAmt\",\"alipayInshopNum\",\"cvr\",\"cartInshopNum\",\"itemColInshopNum\",\"shopColDirNum\",\"colNum\",\"itemColInshopCost\"],\"vsType\":\"off\",\"vsTime\":\"\",\"searchValue\":\"\",\"searchKey\":\"strategyCreativeIdOrName\",\"queryDomains\":[\"creative\",\"promotion\",\"date\"],\"fieldType\":\"all\",\"rptType\":\"creative\",\"parentAdcName\":\"report_frame_creative\",\"byPage\":false,\"fromRealTime\":false,\"source\":\"async_dowdload\",\"csrfId\":\"" + cjzh.getData("adbrain_one_csrfId") + "\",\"bizCode\":\"universalBP\",\"loginPointId\":\"" + cjzh.getData("adbrain_one_loginPointId") + "\"}";
                    break;
                default:
                    return null;
            }

            stringEntity = new StringEntity(param);
            httpPost.setEntity(stringEntity);
            httpresponse = httpClient.execute(httpPost);
            HttpEntity entity = httpresponse.getEntity();
            String body = EntityUtils.toString(entity, "utf-8");
            if (!StringUtil.isJson2(body)) {
                throw new RuntimeException("AiztOneHttpClient.createDownloadTask请求失败，返回的不是json格式！" + body);
            }

            JSONObject jsonObj = JSONObject.parseObject(body);
            if (!jsonObj.containsKey("info") || !jsonObj.getJSONObject("info").getBoolean("ok")) {
                String message = jsonObj.getJSONObject("info").getString("message");
                //这里暂时没有遇到其他的情况 ，不知道返回值如何。
                //所以这里先选择强匹配 2023-10-22
                if (message != null && message.equals("对不起，您没有可供下载的数据。")) {
                    SpiderLog.addTime("aiztone_AiztOneHttpClient.createDownloadTask", true);
                    return "";
                }

                SpiderLog.addTime("aiztone_AiztOneHttpClient.createDownloadTask", false);
                return null;
            }
            if (jsonObj.getJSONObject("data").containsKey("taskId")) {
                SpiderLog.addTime("aiztone_AiztOneHttpClient.createDownloadTask", true);
                return jsonObj.getJSONObject("data").getString("taskId");
            }
            return null;
        } catch (Exception e) {
            SpiderLog.addTime("aiztone_AiztOneHttpClient.createDownloadTask", false);
            LOG.error(e.getMessage(), e);
            return null;
        } finally {
            if (httpPost != null) {
                httpPost.releaseConnection();
            }
        }
    }

    /**
     * 检测创建的无界报表是否已经生成
     * <p>
     * * 检测创建的无界报表是否已经生成
     * * 任务完成状态：
     * * 1完成
     * * 2进行中
     * * 3失败
     * * 4未查到记录 （有可能被删除了）
     * * -1其他 （接口异常等）
     *
     * @param cjzh
     * @param downloadTaskId
     * @param delay          true 用在推广分析中  false用在花费采集中
     * @return
     */
    public static int checkDownloadTaskCreateFinished(Cjzh cjzh, String downloadTaskId, boolean delay, boolean fromRedis) {
        try {
            if (!fromRedis) {
                TimeUnit.SECONDS.sleep(10);
            }
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }

        if (!delay) {
            return checkDownloadTaskCreateFinishedByPage(cjzh, downloadTaskId);
        }

        int curr = 1;
        final int max = 3;
        final int sleepSeconds = 10;
        int status = 2;
        do {
            status = checkDownloadTaskCreateFinishedByPage(cjzh, downloadTaskId);
            if (status != 2) {
                break;
            }

            curr++;
            try {
                TimeUnit.SECONDS.sleep(sleepSeconds);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        } while (curr < max);

        return status;
    }

    /**
     * 根据页码 检测创建的无界报表是否已经生成
     * <p>
     * 最多只检查钱5页 。如果前5页中没有记录，则认为被删除了，需要重新创建
     *
     * @param cjzh
     * @param downloadTaskId
     * @return
     */
    private static int checkDownloadTaskCreateFinishedByPage(Cjzh cjzh, String downloadTaskId) {
        int pageNo = 1;
        int pageSize = 60;
        final int maxPageNo = 5;
        int status = 2;
        do {
            status = checkDownloadTaskCreateFinished(cjzh, downloadTaskId, pageNo, pageSize);
            if (status == 4) {
                pageNo++;
            } else {
                break;
            }
        } while (pageNo < maxPageNo);
        return status;
    }

    /**
     * 检测创建的无界报表是否已经生成
     * 报表状态：
     * 1完成
     * 2进行中
     * 3失败
     * 4未查到记录
     * -1其他
     *
     * @param cjzh
     * @param downloadTaskId
     * @return
     */
    private static int checkDownloadTaskCreateFinished(Cjzh cjzh, String downloadTaskId, int pageNo, int pageSize) {
        CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cjzh.getCookieStore()).build();
        HttpGet httpGet = null;
        HttpResponse httpresponse;
        boolean found = false;
        try {
            String url = "https://bpcommon.alimama.com/commonapi/report/async/findPage.json?r=mx_4214&relationType=1&bizCode=universalBP&endTime=" + BiDateUtil.getCurrdateStr() + "&startTime=" + BiDateUtil.getLastday() + "&pageSize=" + pageSize + "&offset=" + (pageNo - 1) * pageSize + "&csrfId=" + cjzh.getData("adbrain_one_csrfId");
            httpGet = new HttpGet(url);
            httpGet.setHeader("accept", "application/json, text/javascript, */*; q=0.01");
            httpGet.setHeader("accept-encoding", "gzip, deflate, br");
            httpGet.setHeader("accept-language", "zh-CN,zh;q=0.9");
            httpGet.setHeader("referer", "https://one.alimama.com/");
            httpGet.setHeader("origin", "https://one.alimama.com");
            httpGet.setHeader("bx-v", "2.5.1");
            httpGet.setHeader("content-type", "application/json;charset=UTF-8");
            httpGet.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36");
            httpGet.setHeader("x-requested-with", "XMLHttpRequest");

            Builder requestConfig = RequestConfig.custom().setSocketTimeout(60000).setConnectTimeout(60000).setConnectionRequestTimeout(60000).setCookieSpec(CookieSpecs.NETSCAPE);
            httpGet.setConfig(requestConfig.build());

            httpresponse = httpClient.execute(httpGet);
            HttpEntity entity = httpresponse.getEntity();
            String body = EntityUtils.toString(entity, "utf-8");
            if (!StringUtil.isJson2(body)) {
                throw new RuntimeException("AiztOneHttpClient.checkDownloadTaskCreateFinished请求失败，返回的不是json格式！" + body);
            }


            JSONObject jsonObj = JSONObject.parseObject(body);
            JSONObject info = jsonObj.getJSONObject("info");
            if (info != null && !info.getBooleanValue("ok")) {
                throw new RuntimeException("AiztOneHttpClient.checkDownloadTaskCreateFinished请求失败！" + body);
            }

            SpiderLog.addTime("aiztone_AiztOneHttpClient.checkDownloadTaskCreateFinished", true);

            JSONObject data = jsonObj.getJSONObject("data");
            JSONArray list = data.getJSONArray("list");
            for (int i = 0; i < list.size(); i++) {
                JSONObject record = list.getJSONObject(i);
                if (record.getString("id").equals(downloadTaskId)) {
                    found = true;

                    String status = record.getString("status");
                    if (status.equals("SUCCESS")) {
                        return 1;
                    }
                    if (status.equals("RUNNING")) {
                        return 2;
                    }
                    if (status.equals("FAIL")) {
                        return 3;
                    }
                }
            }

            if (!found) {
                return 4;
            }


        } catch (Exception e) {
            SpiderLog.addTime("aiztone_AiztOneHttpClient.checkDownloadTaskCreateFinished", false);
            LOG.error(e.getMessage(), e);
        } finally {
            if (httpGet != null) {
                httpGet.releaseConnection();
            }
        }
        return -1;
    }

    /**
     * 获取报表的下载地址
     *
     * @param cjzh
     * @param downloadTaskId
     * @return
     */
    public static String getDownloadUrl(Cjzh cjzh, String downloadTaskId) {
        CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cjzh.getCookieStore()).build();
        HttpGet httpGet = null;
        HttpResponse httpresponse;
        try {
            String url = "https://bpcommon.alimama.com/commonapi/report/async/getDownloadUrl.json?r=mx_4354&taskId=" + downloadTaskId + "&bizCode=universalBP&csrfId=" + cjzh.getData("adbrain_one_csrfId");
            httpGet = new HttpGet(url);
            httpGet.setHeader("accept", "application/json, text/javascript, */*; q=0.01");
            httpGet.setHeader("accept-encoding", "gzip, deflate, br");
            httpGet.setHeader("accept-language", "zh-CN,zh;q=0.9");
            httpGet.setHeader("referer", "https://one.alimama.com/index.html?spm=a21dvs.24266564.c94d4ac7b_top_qianniu_universalBP_subway.d44bd94f1.71d62d69wD04za");
            httpGet.setHeader("origin", "https://one.alimama.com");
            httpGet.setHeader("bx-v", "2.5.1");
            httpGet.setHeader("content-type", "application/json;charset=UTF-8");
            httpGet.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36");

            Builder requestConfig = RequestConfig.custom().setSocketTimeout(60000).setConnectTimeout(60000).setConnectionRequestTimeout(60000).setCookieSpec(CookieSpecs.NETSCAPE);
            httpGet.setConfig(requestConfig.build());

            httpresponse = httpClient.execute(httpGet);
            HttpEntity entity = httpresponse.getEntity();
            String body = EntityUtils.toString(entity, "utf-8");
            if (!StringUtil.isJson2(body)) {
                throw new RuntimeException("AiztOneHttpClient.getDownloadUrl请求失败，返回的不是json格式！" + body);
            }
            JSONObject jsonObj = JSONObject.parseObject(body);
            if (!jsonObj.containsKey("data")) {
                throw new RuntimeException("AiztOneHttpClient.getDownloadUrl请求失败，返回json不含data字段！" + body);
            }
            JSONObject data = jsonObj.getJSONObject("data");
            if (!data.containsKey("result") || data.get("result") == null) {
                throw new RuntimeException("AiztOneHttpClient.getDownloadUrl请求失败，返回json不含result字段！" + body);
            }
            JSONObject result = data.getJSONObject("result");

            SpiderLog.addTime("aiztone_AiztOneHttpClient.getDownloadUrl", true);
            return result.getString("downloadUrl");
        } catch (Exception e) {
            SpiderLog.addTime("aiztone_AiztOneHttpClient.getDownloadUrl", false);
            LOG.error(e.getMessage(), e);
        } finally {
            if (httpGet != null) {
                httpGet.releaseConnection();
            }
        }
        return null;
    }

    /**
     * 根据无界报表下载地址 下载报表
     *
     * @param cjzh
     * @param downloadUrl
     * @return
     */
    public static String download(Cjzh cjzh, String downloadUrl) {
        SpiderLog.addTime("aiztone_AiztOneHttpClient.download", true);

        CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cjzh.getCookieStore()).build();
        HttpGet httpget = new HttpGet(downloadUrl);
        httpget.setHeader("accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9");
        httpget.setHeader("accept-encoding", "gzip, deflate, br");
        httpget.setHeader("accept-language", "zh-CN,zh;q=0.8");
        httpget.setHeader("upgrade-insecure-requests", "1");
        httpget.setHeader("Sec-Fetch-Site", "cross-site");
        httpget.setHeader("Sec-Fetch-Mode", "navigate");
        httpget.setHeader("Sec-Fetch-User", "?1");
        httpget.setHeader("Sec-Fetch-Dest", "document");
        httpget.setHeader("sec-ch-ua", "\" Not;A Brand\";v=\"99\", \"Google Chrome\";v=\"97\", \"Chromium\";v=\"97\"");
        httpget.setHeader("sec-ch-ua-mobile", "?0");
        httpget.setHeader("sec-ch-ua-platform", "\"Windows\"");
        httpget.setHeader("referer", "https://one.alimama.com/index.html?spm=a21dvs.24266564.c94d4ac7b_top_qianniu_universalBP_subway.d44bd94f1.71d62d69wD04za");
        httpget.setHeader("user-agent", "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/55.0.2883.75 Safari/537.36");
        Builder requestConfig = RequestConfig.custom().setSocketTimeout(60000).setConnectTimeout(60000).setConnectionRequestTimeout(60000).setCookieSpec(CookieSpecs.NETSCAPE);
        httpget.setConfig(requestConfig.build());

        HttpResponse httpresponse;
        InputStream is = null;
        FileOutputStream os = null;
        try {
            httpresponse = httpClient.execute(httpget);
            String filename = System.currentTimeMillis() + "_" + cjzh.getUserid() + "_" + UUID.randomUUID() + ".zip";
            String result = TEMPPATH + filename;
            HttpEntity entity = httpresponse.getEntity();
            is = entity.getContent();
            os = new FileOutputStream(result);
            byte[] b = new byte[4096];
            int count = 0;
            while ((count = is.read(b)) > 0) {
                os.write(b, 0, count);
            }
            os.flush();
            return result;
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        } finally {
            httpget.releaseConnection();
            try {
                if (os != null) {
                    os.close();
                }
            } catch (Exception e) {
                LOG.error(e.getMessage(), e);
            }
            try {
                if (is != null) {
                    is.close();
                }
            } catch (Exception e) {
                LOG.error(e.getMessage(), e);
            }
        }
        return null;
    }

    /**
     * 删除已经下载完成的报表
     *
     * @param cjzh
     * @param downloadTaskId
     */
    public static void deleteTask(Cjzh cjzh, String downloadTaskId) {
        SpiderLog.addTime("aiztone_AiztOneHttpClient.deleteTask", true);

        CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cjzh.getCookieStore()).build();
        HttpGet httpGet = null;
        try {
            String url = "https://bpcommon.alimama.com/commonapi/report/async/deleteTask.json?r=mx_3019&taskId=" + downloadTaskId + "&bizCode=universalBP&csrfId=" + cjzh.getData("adbrain_one_csrfId");
            httpGet = new HttpGet(url);
            httpGet.setHeader("accept", "application/json, text/javascript, */*; q=0.01");
            httpGet.setHeader("accept-encoding", "gzip, deflate, br");
            httpGet.setHeader("accept-language", "zh-CN,zh;q=0.9");
            httpGet.setHeader("referer", "https://one.alimama.com/");
            httpGet.setHeader("origin", "https://one.alimama.com");
            httpGet.setHeader("bx-v", "2.5.1");
            httpGet.setHeader("content-type", "application/json;charset=UTF-8");
            httpGet.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36");

            Builder requestConfig = RequestConfig.custom().setSocketTimeout(60000).setConnectTimeout(60000).setConnectionRequestTimeout(60000).setCookieSpec(CookieSpecs.NETSCAPE);
            httpGet.setConfig(requestConfig.build());

            httpClient.execute(httpGet);
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        } finally {
            if (httpGet != null) {
                httpGet.releaseConnection();
            }
        }
    }

    /**
     * 批量删除下载完成的报表
     *
     * @param cjzh
     * @param downloadTaskIdList
     */
    public static void batchDeleteTask(Cjzh cjzh, List<String> downloadTaskIdList) {
        StringBuilder taskIdSet = new StringBuilder();
        for (int i = 0; i < downloadTaskIdList.size(); i++) {
            if (i == 0) {
                taskIdSet.append("[");
            }
            String str = downloadTaskIdList.get(i);
            taskIdSet.append("\"").append(str).append("\"");
            if (i == downloadTaskIdList.size() - 1) {
                taskIdSet.append("]");
            } else {
                taskIdSet.append(",");
            }
        }

        SpiderLog.addTime("aiztone_AiztOneHttpClient.batchDeleteTask", true);

        CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cjzh.getCookieStore()).build();
        HttpGet httpGet = null;
        try {
            String url = "https://bpcommon.alimama.com/commonapi/report/async/deleteTask.json?r=mx_3019&taskIdSet=" + URLEncoder.encode(taskIdSet.toString(), StandardCharsets.UTF_8.toString()) + "&bizCode=universalBP&csrfId=" + cjzh.getData("adbrain_one_csrfId");
            httpGet = new HttpGet(url);
            httpGet.setHeader("accept", "application/json, text/javascript, */*; q=0.01");
            httpGet.setHeader("accept-encoding", "gzip, deflate, br");
            httpGet.setHeader("accept-language", "zh-CN,zh;q=0.9");
            httpGet.setHeader("referer", "https://one.alimama.com/");
            httpGet.setHeader("origin", "https://one.alimama.com");
            httpGet.setHeader("bx-v", "2.5.1");
            httpGet.setHeader("content-type", "application/json;charset=UTF-8");
            httpGet.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36");

            Builder requestConfig = RequestConfig.custom().setSocketTimeout(60000).setConnectTimeout(60000).setConnectionRequestTimeout(60000).setCookieSpec(CookieSpecs.NETSCAPE);
            httpGet.setConfig(requestConfig.build());

            httpClient.execute(httpGet);
        } catch (Exception e) {
            LOG.error(cjzh.getZzh() + "AiztOneHttpClient.deleteTask，失败原因：" + e.getMessage(), e);
        } finally {
            if (httpGet != null) {
                httpGet.releaseConnection();
            }
        }
    }

    /**
     * 获取无界优惠券记录
     *
     * @param cjzh
     * @param start
     * @param end
     * @return
     */
    public static JSONArray yhq(Cjzh cjzh, String start, String end) {
        final int pageSize = 40;
        int curr = 1;
        JSONArray total = new JSONArray();
        do {
            JSONArray temp = yhq2(cjzh, start, end, curr, pageSize);
            if (temp == null) {
                return null;
            }
            total.addAll(temp);
            if (temp.size() < pageSize) {
                break;
            }
            curr++;
        } while (true);
        return total;
    }

    private static JSONArray yhq2(Cjzh cjzh, String start, String end, int pageNo, int pageSize) {
        CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cjzh.getCookieStore()).build();
        HttpGet httpPost = null;
        HttpResponse httpresponse;
        try {
            String url = "https://stlacct.alimama.com/settleAccount/promotion/promotionChanges.json?csrfId=" + cjzh.getData("adbrain_one_csrfId") + "&currentPage=" + pageNo + "&pageSize=" + pageSize + "&from=&bizCode=universalBP&minSettleTime=" + start + "&maxSettleTime=" + end + "&changeType=4";
            httpPost = new HttpGet(url);
            httpPost.setHeader("accept", "application/json, text/javascript, */*; q=0.01");
            httpPost.setHeader("accept-encoding", "gzip, deflate, br");
            httpPost.setHeader("accept-language", "zh-CN,zh;q=0.9");
            httpPost.setHeader("referer", "https://one.alimama.com/");
            httpPost.setHeader("origin", "https://one.alimama.com");
            httpPost.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36");
            httpPost.setHeader("x-requested-with", "XMLHttpRequest");

            Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000).setConnectionRequestTimeout(20000).setCookieSpec(CookieSpecs.NETSCAPE);
            httpPost.setConfig(requestConfig.build());

            httpresponse = httpClient.execute(httpPost);
            HttpEntity entity = httpresponse.getEntity();
            String body = EntityUtils.toString(entity, "utf-8");
            if (!StringUtil.isJson2(body)) {
                throw new RuntimeException("AiztOneHttpClient.yhq2请求失败，返回的不是json格式！" + body);
            }

            JSONObject jsonObj = JSONObject.parseObject(body);
            if (!jsonObj.containsKey("info") || !jsonObj.getJSONObject("info").getBoolean("ok")) {
                throw new RuntimeException("AiztOneHttpClient.yhq2请求失败，" + body);
            }

            SpiderLog.addTime("aiztone_AiztOneHttpClient.yhq2", true);
            if (jsonObj.getJSONObject("data").containsKey("list")) {
                return jsonObj.getJSONObject("data").getJSONArray("list");
            }
            return new JSONArray();
        } catch (Exception e) {
            SpiderLog.addTime("aiztone_AiztOneHttpClient.yhq2", false);
            LOG.error(e.getMessage(), e);
            return null;
        } finally {
            if (httpPost != null) {
                httpPost.releaseConnection();
            }
        }
    }

    /**
     * 获取无界账户里所有的优惠券
     *
     * @param cjzh
     * @return
     */
    public static JSONArray allYhq(Cjzh cjzh) {
        JSONArray total = new JSONArray();
        JSONArray temp1 = allYhq(cjzh, 1);
        if (temp1 == null) {
            return null;
        }
        JSONArray temp2 = allYhq(cjzh, 2);
        if (temp2 == null) {
            return null;
        }
        total.addAll(temp1);
        total.addAll(temp2);
        return total;
    }

    /**
     * @param cjzh
     * @param type 1 生效中 2 已失效
     * @return
     */
    private static JSONArray allYhq(Cjzh cjzh, int type) {
        final int pageSize = 40;
        int curr = 1;
        JSONArray total = new JSONArray();
        do {
            JSONArray temp = allYhq2(cjzh, type, curr, pageSize);
            if (temp == null) {
                return null;
            }
            total.addAll(temp);
            if (temp.size() < pageSize) {
                break;
            }
            curr++;
        } while (true);
        return total;
    }

    private static JSONArray allYhq2(Cjzh cjzh, int type, int pageNo, int pageSize) {
        CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cjzh.getCookieStore()).build();
        HttpGet httpPost = null;
        HttpResponse httpresponse;
        try {
            String url = "https://stlacct.alimama.com/settleAccount/promotion/new/getPromotionList.json?csrfId=" + cjzh.getData("adbrain_one_csrfId") + "&pageIndex=" + pageNo + "&number=" + pageSize + "&status=" + type + "&bizCode=universalBP";
            httpPost = new HttpGet(url);
            httpPost.setHeader("accept", "application/json, text/javascript, */*; q=0.01");
            httpPost.setHeader("accept-encoding", "gzip, deflate, br");
            httpPost.setHeader("accept-language", "zh-CN,zh;q=0.9");
            httpPost.setHeader("referer", "https://one.alimama.com/");
            httpPost.setHeader("origin", "https://one.alimama.com");
            httpPost.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36");
            httpPost.setHeader("x-requested-with", "XMLHttpRequest");

            Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000).setConnectionRequestTimeout(20000).setCookieSpec(CookieSpecs.NETSCAPE);
            httpPost.setConfig(requestConfig.build());

            httpresponse = httpClient.execute(httpPost);
            HttpEntity entity = httpresponse.getEntity();
            String body = EntityUtils.toString(entity, "utf-8");
            if (!StringUtil.isJson2(body)) {
                throw new RuntimeException("AiztOneHttpClient.allYhq2请求失败，返回的不是json格式！" + body);
            }
            JSONObject jsonObj = JSONObject.parseObject(body);
            if (!jsonObj.containsKey("info") || !jsonObj.getJSONObject("info").getBoolean("ok")) {
                throw new RuntimeException("AiztOneHttpClient.allYhq2请求失败，" + body);
            }

            SpiderLog.addTime("aiztone_AiztOneHttpClient.allYhq2", true);
            if (jsonObj.getJSONObject("data").containsKey("list")) {
                return jsonObj.getJSONObject("data").getJSONArray("list");
            }
            return new JSONArray();
        } catch (Exception e) {
            SpiderLog.addTime("aiztone_AiztOneHttpClient.allYhq2", false);
            LOG.error(e.getMessage(), e);
            return null;
        } finally {
            if (httpPost != null) {
                httpPost.releaseConnection();
            }
        }
    }

    /**
     * 获取无界-内容营销-超级直播
     *
     * @param cjzh
     * @param start
     * @param end
     * @return
     */
    public static JSONArray nryxCjzbDayList(Cjzh cjzh, String start, String end) {
        CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cjzh.getCookieStore()).build();
        HttpGet httpPost = null;
        HttpResponse httpresponse;
        String t = String.valueOf(System.currentTimeMillis());
        try {
            String url = "https://adbrain.alimama.com/api/account/report/findOverProductAccountDayList.json?queryUDF=false&campaignId=&startTime=" + start + "&endTime=" + end + "&effect=15&effectType=click&bizCode=fastLive&marketSceneListNotIn=%5B%22fast_live_hot_wave%22%2C%22fast_live_help_card%22%2C%22fast_live_gas%22%5D&timeStr=" + t + "&dynamicToken=" + JsvmJSEngine.getDynamicToken(t, "", cjzh.getData("adbrain_one_csrfId2")) + "&csrfID=" + cjzh.getData("adbrain_one_csrfId2");
            httpPost = new HttpGet(url);
            httpPost.setHeader("accept", "application/json, text/javascript, */*; q=0.01");
            httpPost.setHeader("accept-encoding", "gzip, deflate, br");
            httpPost.setHeader("accept-language", "zh-CN,zh;q=0.9");
            httpPost.setHeader("referer", "https://one.alimama.com/");
            httpPost.setHeader("origin", "https://one.alimama.com");
            httpPost.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36");
            httpPost.setHeader("x-requested-with", "XMLHttpRequest");

            Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000).setConnectionRequestTimeout(20000).setCookieSpec(CookieSpecs.NETSCAPE);
            httpPost.setConfig(requestConfig.build());

            httpresponse = httpClient.execute(httpPost);
            HttpEntity entity = httpresponse.getEntity();
            String body = EntityUtils.toString(entity, "utf-8");
            if (!StringUtil.isJson2(body)) {
                throw new RuntimeException("AiztOneHttpClient.nryxCjzbDayList请求失败，返回的不是json格式！" + body);
            }
            JSONObject jsonObj = JSONObject.parseObject(body);
            if (!jsonObj.containsKey("info") || !jsonObj.getJSONObject("info").getBoolean("ok")) {
                throw new RuntimeException("AiztOneHttpClient.nryxCjzbDayList请求失败， " + body);
            }

            SpiderLog.addTime("aiztone_AiztOneHttpClient.nryxCjzbDayList", true);
            if (jsonObj.getJSONObject("data").containsKey("list")) {
                JSONArray jsonArray = jsonObj.getJSONObject("data").getJSONArray("list");
                return jsonArray == null ? new JSONArray(0) : jsonArray;
            }
            return new JSONArray();
        } catch (Exception e) {
            SpiderLog.addTime("aiztone_AiztOneHttpClient.nryxCjzbDayList", false);
            LOG.error(e.getMessage(), e);
            return null;
        } finally {
            if (httpPost != null) {
                httpPost.releaseConnection();
            }
        }
    }

    /**
     * 获取无界-内容营销-短视频列表
     *
     * @param cjzh
     * @param start
     * @param end
     * @return
     */
    public static JSONArray nryxDspDayList(Cjzh cjzh, String start, String end) {
        CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cjzh.getCookieStore()).build();
        HttpGet httpPost = null;
        HttpResponse httpresponse;
        String t = String.valueOf(System.currentTimeMillis());
        try {
            String url = "https://adbrain.alimama.com/api/account/report/daySumList.json?r=mx_10354&marketScene=ad_strategy_short_video_rtb&bizCode=adStrategyShortVideoRtb&marketAim=&startTime=" + start + "&endTime=" + end + "&effectType=impression&shortVideoCampaignType=&effect=15&tab=campaign&unifyType=zhai&timeStr=" + t + "&dynamicToken=" + JsvmJSEngine.getDynamicToken(t, "", cjzh.getData("adbrain_one_csrfId2")) + "&csrfID=" + cjzh.getData("adbrain_one_csrfId2");
            httpPost = new HttpGet(url);
            httpPost.setHeader("accept", "application/json, text/javascript, */*; q=0.01");
            httpPost.setHeader("accept-encoding", "gzip, deflate, br");
            httpPost.setHeader("accept-language", "zh-CN,zh;q=0.9");
            httpPost.setHeader("referer", "https://one.alimama.com/");
            httpPost.setHeader("origin", "https://one.alimama.com");
            httpPost.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36");
            httpPost.setHeader("x-requested-with", "XMLHttpRequest");

            Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000).setConnectionRequestTimeout(20000).setCookieSpec(CookieSpecs.NETSCAPE);
            httpPost.setConfig(requestConfig.build());

            httpresponse = httpClient.execute(httpPost);
            HttpEntity entity = httpresponse.getEntity();
            String body = EntityUtils.toString(entity, "utf-8");
            if (!StringUtil.isJson2(body)) {
                throw new RuntimeException("AiztOneHttpClient.nryxDspDayList请求失败，返回的不是json格式！" + body);
            }
            JSONObject jsonObj = JSONObject.parseObject(body);
            if (!jsonObj.containsKey("info") || !jsonObj.getJSONObject("info").getBoolean("ok")) {
                throw new RuntimeException("AiztOneHttpClient.nryxDspDayList请求失败， " + body);
            }

            SpiderLog.addTime("aiztone_AiztOneHttpClient.nryxDspDayList", true);
            if (jsonObj.getJSONObject("data").containsKey("list")) {
                JSONArray jsonArray = jsonObj.getJSONObject("data").getJSONArray("list");
                return jsonArray == null ? new JSONArray(0) : jsonArray;
            }
            return new JSONArray();
        } catch (Exception e) {
            SpiderLog.addTime("aiztone_AiztOneHttpClient.nryxDspDayList", false);
            LOG.error(e.getMessage(), e);
            return null;
        } finally {
            if (httpPost != null) {
                httpPost.releaseConnection();
            }
        }
    }

    /**
     * 获取无界 内容营销 短直连动
     *
     * @param cjzh
     * @param start
     * @param end
     * @return
     */
    public static JSONArray nryxDzldDayList(Cjzh cjzh, String start, String end) {
        CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cjzh.getCookieStore()).build();
        HttpGet httpPost = null;
        HttpResponse httpresponse;
        String t = String.valueOf(System.currentTimeMillis());
        try {
            String url = "https://adbrain.alimama.com/api/wxt/union/queryReport.json?r=mx_13692&bizCode=adStrategyUnion&queryUDF=false&campaignId=&startDate=" + start + "&endDate=" + end + "&effect=15&effectType=click&timeStr=" + t + "&dynamicToken=" + JsvmJSEngine.getDynamicToken(t, "", cjzh.getData("adbrain_one_csrfId2")) + "&csrfID=" + cjzh.getData("adbrain_one_csrfId2");
            httpPost = new HttpGet(url);
            httpPost.setHeader("accept", "application/json, text/javascript, */*; q=0.01");
            httpPost.setHeader("accept-encoding", "gzip, deflate, br");
            httpPost.setHeader("accept-language", "zh-CN,zh;q=0.9");
            httpPost.setHeader("referer", "https://one.alimama.com/");
            httpPost.setHeader("origin", "https://one.alimama.com");
            httpPost.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36");
            httpPost.setHeader("x-requested-with", "XMLHttpRequest");

            Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000).setConnectionRequestTimeout(20000).setCookieSpec(CookieSpecs.NETSCAPE);
            httpPost.setConfig(requestConfig.build());

            httpresponse = httpClient.execute(httpPost);
            HttpEntity entity = httpresponse.getEntity();
            String body = EntityUtils.toString(entity, "utf-8");
            if (!StringUtil.isJson2(body)) {
                throw new RuntimeException("AiztOneHttpClient.nryxDzldDayList请求失败，返回的不是json格式！" + body);
            }

            JSONObject jsonObj = JSONObject.parseObject(body);
            if (!jsonObj.containsKey("info") || !jsonObj.getJSONObject("info").getBoolean("ok")) {
                throw new RuntimeException("AiztOneHttpClient.nryxDzldDayList请求失败， " + body);
            }

            SpiderLog.addTime("aiztone_AiztOneHttpClient.nryxDzldDayList", true);
            if (jsonObj.getJSONObject("data").containsKey("list")) {
                JSONArray jsonArray = jsonObj.getJSONObject("data").getJSONArray("list");
                JSONObject jsonObject0 = CollectionUtils.isEmpty(jsonArray) ? new JSONObject(0) : jsonArray.getJSONObject(0);
                return jsonObject0.getJSONArray("dayList") == null ? new JSONArray(0) : jsonObject0.getJSONArray("dayList");
            }
            return new JSONArray();
        } catch (Exception e) {
            SpiderLog.addTime("aiztone_AiztOneHttpClient.nryxDzldDayList", false);
            LOG.error(e.getMessage(), e);
            return null;
        } finally {
            if (httpPost != null) {
                httpPost.releaseConnection();
            }
        }
    }

    /**
     * 获取万相台csrfid（不是无界的）
     *
     * @param cjzh
     * @return
     */
    public static boolean adbrainCSRFID(Cjzh cjzh) {
        CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cjzh.getCookieStore()).build();
        String t = String.valueOf(System.currentTimeMillis());
        HttpGet httpGet = new HttpGet("https://adbrain.alimama.com/api/member/getInfo.json?timeStr=" + t + "&dynamicToken=" + JsvmJSEngine.getDynamicToken(t, "", "") + "&csrfID=&webOpSessionId=hlqbilfvd94&bizCode=adStrategy");
        httpGet.setHeader("accept", "application/json, text/javascript, */*; q=0.01");
        httpGet.setHeader("accept-encoding", "gzip, deflate, br");
        httpGet.setHeader("accept-language", "zh-CN,zh;q=0.9");
        httpGet.setHeader("content-type", "application/json");
        httpGet.setHeader("origin", "https://one.alimama.com");
        httpGet.setHeader("referer", "https://one.alimama.com/");
        httpGet.setHeader("sec-ch-ua", "\"Google Chrome\";v=\"95\", \"Chromium\";v=\"95\", \";Not A Brand\";v=\"99\"");
        httpGet.setHeader("sec-ch-ua-mobile", "?0");
        httpGet.setHeader("sec-ch-ua-platform", "\"Windows\"");
        httpGet.setHeader("sec-fetch-dest", "empty");
        httpGet.setHeader("sec-fetch-mode", "cors");
        httpGet.setHeader("sec-fetch-site", "same-origin");
        httpGet.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");
        // httpGet.setHeader("x-requested-with", "XMLHttpRequest");
        RequestConfig.Builder requestConfig = RequestConfig.custom().setCookieSpec(CookieSpecs.NETSCAPE).setSocketTimeout(20000).setConnectTimeout(20000).setConnectionRequestTimeout(20000);
        httpGet.setConfig(requestConfig.build());
        HttpResponse httpResponse;
        try {
            httpResponse = httpClient.execute(httpGet);
            HttpEntity entity = httpResponse.getEntity();
            String body = EntityUtils.toString(entity, "utf-8");
            if (!StringUtil.isJson2(body)) {
                throw new RuntimeException("AiztOneHttpClient.adbrainCSRFID请求失败，返回的不是json格式！" + body);
            }
            JSONObject jsonObject = JSONObject.parseObject(body);
            if (jsonObject.containsKey("info") && jsonObject.getJSONObject("info").getBooleanValue("ok")) {
                String csrfid = jsonObject.getJSONObject("data").getString("csrfID");
                cjzh.putData("adbrain_one_csrfId2", csrfid);
                SpiderLog.addTime("aiztone_AiztOneHttpClient.adbrainCSRFID", true);
                return true;
            }
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        } finally {
            httpGet.releaseConnection();
        }
        SpiderLog.addTime("aiztone_AiztOneHttpClient.adbrainCSRFID", false);
        return false;
    }

    /**
     * 获取无界店铺维度花费
     *
     * @param cjzh
     * @param rq
     * @return
     */
    public static String chargeSum(Cjzh cjzh, String rq) {
        CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cjzh.getCookieStore()).build();
        HttpPost httpPost = null;
        HttpResponse httpresponse;
        try {
            String url = "https://one.alimama.com/report/chargeSum.json?csrfId=" + cjzh.getData("adbrain_one_csrfId") + "&bizCode=universalBP";
            httpPost = new HttpPost(url);
            httpPost.setHeader("accept", "application/json, text/javascript, */*; q=0.01");
            httpPost.setHeader("accept-encoding", "gzip, deflate, br");
            httpPost.setHeader("accept-language", "zh-CN,zh;q=0.9");
            httpPost.setHeader("referer", "https://one.alimama.com/index.html");
            httpPost.setHeader("origin", "https://one.alimama.com");
            httpPost.setHeader("bx-v", "2.5.3");
            httpPost.setHeader("content-type", "application/json;charset=UTF-8");
            httpPost.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36");
            httpPost.setHeader("x-requested-with", "XMLHttpRequest");

            Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000).setConnectionRequestTimeout(20000).setCookieSpec(CookieSpecs.NETSCAPE);
            httpPost.setConfig(requestConfig.build());

            String param = "{\"fromRealTime\":false,\"source\":\"baseReport\",\"byPage\":true,\"totalTag\":true,\"rptType\":\"account\",\"pageSize\":20,\"offset\":0,\"havingList\":[],\"endTime\":\"" + rq + "\",\"unifyType\":\"zhai\",\"effectEqual\":15,\"startTime\":\"" + rq + "\",\"splitType\":\"day\",\"queryFieldIn\":[\"adPv\",\"click\",\"charge\",\"ctr\",\"ecpc\",\"alipayInshopAmt\",\"alipayInshopNum\",\"cvr\",\"cartInshopNum\",\"itemColInshopNum\",\"shopColDirNum\",\"colNum\",\"itemColInshopCost\"],\"queryDomains\":[],\"csrfId\":\"" + cjzh.getData("adbrain_one_csrfId") + "\",\"bizCode\":\"universalBP\"}";
            StringEntity stringEntity = new StringEntity(param);
            httpPost.setEntity(stringEntity);
            httpresponse = httpClient.execute(httpPost);
            HttpEntity entity = httpresponse.getEntity();
            String body = EntityUtils.toString(entity, "utf-8");
            if (!StringUtil.isJson2(body)) {
                throw new RuntimeException("AiztOneHttpClient.chargeSum请求失败，返回的不是json格式！" + body);
            }
            JSONObject jsonObj = JSONObject.parseObject(body);
            if (!jsonObj.containsKey("info") || !jsonObj.getJSONObject("info").getBoolean("ok")) {
                throw new RuntimeException("AiztOneHttpClient.chargeSum请求失败， " + body);
            }

            SpiderLog.addTime("aiztone_AiztOneHttpClient.chargeSum", true);
            return jsonObj.getJSONObject("data").toJSONString();
        } catch (Exception e) {
            SpiderLog.addTime("aiztone_AiztOneHttpClient.chargeSum", true);
            LOG.error(e.getMessage(), e);
            return null;
        } finally {
            if (httpPost != null) {
                httpPost.releaseConnection();
            }
        }
    }

    /**
     * 删除无界创建失败的报表
     *
     * @param cjzh
     */
    public static void deleteFailBiTask(Cjzh cjzh) {
        int pageNo = 1;
        final int pageSize = 60;
        do {
            if (deleteFailBiTaskByPage(cjzh, pageNo, pageSize)) {
                pageNo++;
            } else {
                break;
            }
        } while (true);
    }

    private static boolean deleteFailBiTaskByPage(Cjzh cjzh, int pageNo, int pageSize) {
        CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cjzh.getCookieStore()).build();
        HttpGet httpGet = null;
        HttpResponse httpresponse;
        try {
            String url = "https://bpcommon.alimama.com/commonapi/report/async/findPage.json?r=mx_4214&relationType=1&bizCode=universalBP&endTime=" + BiDateUtil.getCurrdateStr() + "&startTime=" + BiDateUtil.getLastday() + "&pageSize=" + pageSize + "&offset=" + (pageNo - 1) * pageSize + "&csrfId=" + cjzh.getData("adbrain_one_csrfId");
            httpGet = new HttpGet(url);
            httpGet.setHeader("accept", "application/json, text/javascript, */*; q=0.01");
            httpGet.setHeader("accept-encoding", "gzip, deflate, br");
            httpGet.setHeader("accept-language", "zh-CN,zh;q=0.9");
            httpGet.setHeader("referer", "https://one.alimama.com/");
            httpGet.setHeader("origin", "https://one.alimama.com");
            httpGet.setHeader("bx-v", "2.5.1");
            httpGet.setHeader("content-type", "application/json;charset=UTF-8");
            httpGet.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36");
            httpGet.setHeader("x-requested-with", "XMLHttpRequest");

            Builder requestConfig = RequestConfig.custom().setSocketTimeout(60000).setConnectTimeout(60000).setConnectionRequestTimeout(60000).setCookieSpec(CookieSpecs.NETSCAPE);
            httpGet.setConfig(requestConfig.build());

            httpresponse = httpClient.execute(httpGet);
            HttpEntity entity = httpresponse.getEntity();
            String body = EntityUtils.toString(entity, "utf-8");
            if (!StringUtil.isJson2(body)) {
                throw new RuntimeException("AiztOneHttpClient.deleteFailBiTaskByPage请求失败，返回的不是json格式！" + body);
            }

            SpiderLog.addTime("aiztone_AiztOneHttpClient.deleteFailBiTaskByPage", true);

            JSONObject jsonObj = JSONObject.parseObject(body);
            JSONObject data = jsonObj.getJSONObject("data");
            if (data == null) {
                return false;
            }

            JSONArray list = data.getJSONArray("list");
            if (CollectionUtils.isEmpty(list)) {
                return false;
            }
            for (int i = 0; i < list.size(); i++) {
                JSONObject record = list.getJSONObject(i);
                String status = record.getString("status");
                String fileName = record.getJSONObject("ext").getString("fileName");
                if (status.equals("FAIL") && fileName.startsWith("bi")) {
                    deleteTask(cjzh, record.getString("id"));
                }
            }

            return list.size() == pageSize;
        } catch (Exception e) {
            SpiderLog.addTime("aiztone_AiztOneHttpClient.deleteFailBiTaskByPage", false);
            LOG.error(e.getMessage(), e);
        } finally {
            if (httpGet != null) {
                httpGet.releaseConnection();
            }
        }
        return false;
    }

    /**
     * 下载无界 内容营销短视频
     *
     * @param cjzh
     * @param logDate
     * @return
     */
    public static String dayCampaignVideoSumList(Cjzh cjzh, String logDate) {
        SpiderLog.addTime("aiztone_AiztOneHttpClient.dayCampaignVideoSumList", true);

        String t = String.valueOf(System.currentTimeMillis());
        CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cjzh.getCookieStore()).build();
        HttpGet httpget = new HttpGet("https://adbrain.alimama.com/api/account/report/dayCampaignVideoSumListDownload.json?marketScene=ad_strategy_short_video_rtb&bizCode=adStrategyShortVideoRtb&marketAim=&startTime=" + logDate + "&endTime=" + logDate + "&effectType=impression&shortVideoCampaignType=&effect=15&tab=adgroup&unifyType=zhai&timeStr=" + t + "&dynamicToken=" + JsvmJSEngine.getDynamicToken(t, "", cjzh.getData("adbrain_one_csrfId2")) + "&csrfID=" + cjzh.getData("adbrain_one_csrfId2"));
        httpget.setHeader("accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9");
        httpget.setHeader("accept-encoding", "gzip, deflate, br");
        httpget.setHeader("accept-language", "zh-CN,zh;q=0.8");
        httpget.setHeader("upgrade-insecure-requests", "1");
        httpget.setHeader("Sec-Fetch-Site", "cross-site");
        httpget.setHeader("Sec-Fetch-Mode", "navigate");
        httpget.setHeader("Sec-Fetch-User", "?1");
        httpget.setHeader("Sec-Fetch-Dest", "document");
        httpget.setHeader("sec-ch-ua", "\" Not;A Brand\";v=\"99\", \"Google Chrome\";v=\"97\", \"Chromium\";v=\"97\"");
        httpget.setHeader("sec-ch-ua-mobile", "?0");
        httpget.setHeader("sec-ch-ua-platform", "\"Windows\"");
        httpget.setHeader("referer", "https://one.alimama.com/index.html");
        httpget.setHeader("user-agent", "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/55.0.2883.75 Safari/537.36");
        Builder requestConfig = RequestConfig.custom().setSocketTimeout(60000).setConnectTimeout(60000).setConnectionRequestTimeout(60000).setCookieSpec(CookieSpecs.NETSCAPE);
        httpget.setConfig(requestConfig.build());

        HttpResponse httpresponse;
        InputStream is = null;
        FileOutputStream os = null;
        try {
            httpresponse = httpClient.execute(httpget);
            String filename = System.currentTimeMillis() + "_" + cjzh.getUserid() + "_" + UUID.randomUUID() + ".xls";
            String result = TEMPPATH + filename;
            HttpEntity entity = httpresponse.getEntity();
            try {
                is = entity.getContent();
                os = new FileOutputStream(result);
                byte[] b = new byte[4096];
                int count = 0;
                while ((count = is.read(b)) > 0) {
                    os.write(b, 0, count);
                }
                os.flush();
                return result;
            } finally {
                try {
                    os.close();
                } catch (Exception e) {
                }
                try {
                    is.close();
                } catch (Exception e) {
                }
            }
        } catch (Exception e) {
            LOG.error(cjzh.getZzh() + "下载万相台无界-内容营销-短视频报表失败", e);
        } finally {
            httpget.releaseConnection();
        }
        return null;
    }

    /**
     * 获取无界 -计划状态
     *
     * @param cjzh
     * @param params
     * @return
     */
    public static List<AiztoneJhzt> queryAiztoneTgfx(Cjzh cjzh, Map<String, Object> params) {
        CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cjzh.getCookieStore()).build();
        HttpPost httpPost = null;
        HttpResponse httpResponse;
        List<AiztoneJhzt> tgfxList = new ArrayList<>();

        try {
            String csrfId = cjzh.getData("adbrain_one_csrfId");
            String bizCode = (String) params.get("bizCode");
            int pageSize = (Integer) params.get("pageSize");
            int offset = (Integer) params.get("offset");
            String startTime = (String) params.get("startTime");
            String endTime = (String) params.get("endTime");

            String url = "https://one.alimama.com/campaign/horizontal/findPage.json?lite2=true&csrfId=" + csrfId + "&bizCode=" + bizCode;
            httpPost = new HttpPost(url);
            httpPost.setHeader("accept", "application/json, text/javascript, */*; q=0.01");
            httpPost.setHeader("accept-encoding", "gzip, deflate, br");
            httpPost.setHeader("accept-language", "zh-CN,zh;q=0.9");
            httpPost.setHeader("referer", "https://one.alimama.com/");
            httpPost.setHeader("origin", "https://one.alimama.com");
            httpPost.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36");
            httpPost.setHeader("content-type", "application/json;charset=UTF-8");

            // 构造请求体
            JSONObject jsonBodyObj = new JSONObject();
            jsonBodyObj.put("lite2", true);
            jsonBodyObj.put("bizCode", bizCode);
            jsonBodyObj.put("offset", offset);
            jsonBodyObj.put("pageSize", pageSize);
            jsonBodyObj.put("orderField", "");
            jsonBodyObj.put("orderBy", "");
            jsonBodyObj.put("adgroupRequired", false);

            JSONObject rptQuery = new JSONObject();
            rptQuery.put("fields", "charge,adPv,click,cartInshopNum,wwNum,cartRate,ctr,ecpc,alipayInshopAmt,alipayInshopNum,cvr,itemColInshopNum,shopColDirNum,colNum,itemColInshopCost,roi,alipayIndirAmt,alipayIndirNum,alipayDirAmt");

            JSONObject condition = new JSONObject();
            condition.put("startTime", startTime);
            condition.put("endTime", endTime);
            condition.put("isRt", true);

            JSONArray conditionList = new JSONArray();
            conditionList.add(condition);
            rptQuery.put("conditionList", conditionList);

            jsonBodyObj.put("rptQuery", rptQuery);
            jsonBodyObj.put("csrfId", csrfId);

            httpPost.setEntity(new StringEntity(jsonBodyObj.toJSONString(), "UTF-8"));

            // 执行请求并获取响应
            httpResponse = httpClient.execute(httpPost);
            HttpEntity entity = httpResponse.getEntity();
            String responseBody = EntityUtils.toString(entity, "UTF-8");
            if (!StringUtil.isJson2(responseBody)) {
                throw new RuntimeException("返回数据格式错误：非JSON格式");
            }
            // 解析JSON响应
            JSONObject responseJson = JSONObject.parseObject(responseBody);
            if (!responseJson.containsKey("info") || responseJson.getJSONObject("info") == null || !responseJson.getJSONObject("info").containsKey("ok") || !responseJson.getJSONObject("info").getBooleanValue("ok")) {
                throw new RuntimeException("接口请求失败");
            }

            JSONArray list = null;
            if (!responseJson.containsKey("data") || responseJson.get("data") == null || responseJson.getJSONObject("data") == null || !responseJson.getJSONObject("data").containsKey("list") || responseJson.getJSONObject("data").getJSONArray("list") == null) {
                list = new JSONArray();
            } else {
                list = responseJson.getJSONObject("data").getJSONArray("list");
            }

            for (int i = 0; i < list.size(); i++) {
                JSONObject item = list.getJSONObject(i);
                String jhid = item.getString("campaignId");
                String displayStatus = item.getString("displayStatus");
                String gmtModified = BiDateUtil.formatDateString(item.getString("gmtModified"));
                int zt = "start".equals(displayStatus) ? 1 : 0;

                AiztoneJhzt tgfx = new AiztoneJhzt(jhid, cjzh.getUserid(), cjzh.getDpid(), zt, gmtModified);
                tgfxList.add(tgfx);
            }

            return tgfxList;
        } catch (Exception e) {
            LOG.error("接口查询失败：" + e.getMessage());
            return new ArrayList<>();
        } finally {
            if (httpPost != null) {
                httpPost.releaseConnection();
            }
            try {
                httpClient.close();
            } catch (Exception e) {
                LOG.error("关闭HTTP客户端失败");
            }
        }
    }

    /**
     * 获取 无界 直通车点击率
     *
     * @param cjzh
     * @return
     */
    public static JSONArray materialItemList(Cjzh cjzh) {
        int pageNo = 1;
        int pageSize = 40;
        JSONArray jsonArray = new JSONArray();
        do {
            JSONArray temp = materialItemList(cjzh, pageNo, pageSize);
            if (temp == null) {
                return null;
            }
            jsonArray.addAll(temp);
            if (temp.size() < pageSize) {
                break;
            }
            pageNo++;
            try {
                TimeUnit.SECONDS.sleep(1);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        } while (true);
        return jsonArray;
    }

    private static JSONArray materialItemList(Cjzh cjzh, int pageNo, int pageSize) {
        CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cjzh.getCookieStore()).build();
        HttpPost httpPost = null;
        HttpResponse httpresponse;
        try {
            String url = "https://one.alimama.com/material/item/findPage.json?lite2=false&csrfId=" + cjzh.getData("adbrain_one_csrfId") + "&bizCode=onebpSearch";
            httpPost = new HttpPost(url);
            httpPost.setHeader("accept", "application/json, text/javascript, */*; q=0.01");
            httpPost.setHeader("accept-encoding", "gzip, deflate, br");
            httpPost.setHeader("accept-language", "zh-CN,zh;q=0.9");
            httpPost.setHeader("referer", "https://one.alimama.com/");
            httpPost.setHeader("origin", "https://one.alimama.com");
            httpPost.setHeader("bx-v", "2.5.1");
            httpPost.setHeader("content-type", "application/json;charset=UTF-8");
            httpPost.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36");
            httpPost.setHeader("x-requested-with", "XMLHttpRequest");

            Builder requestConfig = RequestConfig.custom().setSocketTimeout(60000).setConnectTimeout(60000).setConnectionRequestTimeout(60000).setCookieSpec(CookieSpecs.NETSCAPE);
            httpPost.setConfig(requestConfig.build());

            String json = "{\"lite2\":false,\"needQualification\":true,\"materialType\":1,\"bizCode\":\"onebpSearch\",\"promotionScene\":\"promotion_scene_search_detent\",\"itemSelectedMode\":\"search_detent\",\"subPromotionType\":\"item\",\"promotionType\":\"item\",\"tagId\":\"1\",\"offset\":" + (pageNo - 1) * pageSize + ",\"pageSize\":" + pageSize + ",\"csrfId\":\"" + cjzh.getData("adbrain_one_csrfId") + "\"}";
            StringEntity param = new StringEntity(json);
            httpPost.setEntity(param);

            httpresponse = httpClient.execute(httpPost);
            HttpEntity entity = httpresponse.getEntity();
            String body = EntityUtils.toString(entity, "utf-8");
            if (!StringUtil.isJson2(body)) {
                throw new RuntimeException("AiztOneHttpClient.materialItemList请求失败，返回的不是json格式！" + body);
            }

            SpiderLog.addTime("aiztone_AiztOneHttpClient.materialItemList", true);
            JSONObject jsonObj = JSONObject.parseObject(body);
            JSONObject data = jsonObj.getJSONObject("data");
            return data.getJSONArray("list");
        } catch (Exception e) {
            SpiderLog.addTime("aiztone_AiztOneHttpClient.materialItemList", false);
            LOG.error(e.getMessage(), e);
        } finally {
            if (httpPost != null) {
                httpPost.releaseConnection();
            }
        }
        return null;
    }

    /**
     * 无界权限判断
     *
     * @param cjzh
     * @return
     */
    public static BiAuthResult adbrainOne(Cjzh cjzh) {
        // https://sycm.taobao.com/oneauth/api/permission.json?p_url=http%3A%2F%2Fsycm.taobao.com%2Fcc%2Fitem_rank&_=1636010721759&token=1ced73e20
        // 返回code不为0 则说明生意参谋异常
        CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cjzh.getCookieStore()).build();
        HttpPost httpGet = new HttpPost("https://one.alimama.com/member/checkAccess.json?bizCode=universalBP&r=mx_13");
        httpGet.setHeader("accept", "application/json, text/javascript, */*; q=0.01");
        httpGet.setHeader("accept-encoding", "gzip, deflate, br");
        httpGet.setHeader("accept-language", "zh-CN,zh;q=0.9");
        httpGet.setHeader("bx-v", "2.5.1");
        httpGet.setHeader("content-type", "application/json");
        httpGet.setHeader("origin", "https://one.alimama.com");
        httpGet.setHeader("referer", "https://one.alimama.com/index.html?mxredirectUrl=https%3A%2F%2Fone.alimama.com%2Fredirect.action%3FredirectURL%3Dhttps%253A%252F%252Fone.alimama.com%252Findex.html");
        httpGet.setHeader("sec-ch-ua", "\"Google Chrome\";v=\"95\", \"Chromium\";v=\"95\", \";Not A Brand\";v=\"99\"");
        httpGet.setHeader("sec-ch-ua-mobile", "?0");
        httpGet.setHeader("sec-ch-ua-platform", "\"Windows\"");
        httpGet.setHeader("sec-fetch-dest", "empty");
        httpGet.setHeader("sec-fetch-mode", "cors");
        httpGet.setHeader("sec-fetch-site", "same-origin");
        httpGet.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");
        httpGet.setHeader("x-requested-with", "XMLHttpRequest");
        RequestConfig.Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000).setConnectionRequestTimeout(20000);
        httpGet.setConfig(requestConfig.build());
        HttpResponse httpResponse;
        try {
            StringEntity param = new StringEntity("{\"bizCode\":\"universalBP\"}");
            httpGet.setEntity(param);

            httpResponse = httpClient.execute(httpGet);
            HttpEntity entity = httpResponse.getEntity();
            String body = EntityUtils.toString(entity, "utf-8");
            if (!StringUtil.isJson2(body)) {
                throw new RuntimeException("BiAuth.adbrainOne请求失败，返回的不是json格式！" + body);
            }
            //{"data":{"meta":{"nickName":"vitagrow海外旗舰店:推广分析","accountType":3,"memberId":**********},"errorInfo":{"code":"INTERNATIONAL_REDIRECT","internationalLink":"https://one.alimama.hk/index.html"},"accessInfo":{"csrfId":"a8ce3c38d35628c1ed13ba0904250b22_1_1_1"}},"info":{"lockSla":false,"redirectUrl":null,"errorEntityIdList":null,"httpStatus":null,"errorCode":null,"unlockUrl":null,"ok":true,"message":null,"disableTime":false}}
            net.sf.json.JSONObject jsonObject = net.sf.json.JSONObject.fromObject(body);
            if (jsonObject.containsKey("data")) {
                net.sf.json.JSONObject data = jsonObject.getJSONObject("data");
                if (data.containsKey("accessPassed")) {
                    boolean accessPassed = data.getBoolean("accessPassed");
                    if (!accessPassed) {
                        return new BiAuthResult(BiAuthResult.NO_AUTH, body);
                    }
                }
                if (data.containsKey("errorInfo")) {
                    net.sf.json.JSONObject errorInfo = data.getJSONObject("errorInfo");
                    if (errorInfo.getString("code").equals("USER_UNAUTHORIZED")) {
                        //无权限
                        return new BiAuthResult(BiAuthResult.NO_AUTH, body);
                    }
                    return new BiAuthResult(BiAuthResult.OTHER, body);
                }
                if (StringUtils.isNotBlank(data.getString("loginPointId"))) {
                    cjzh.putData("adbrain_one_loginPointId", data.getString("loginPointId"));
                }
                if (data.containsKey("accessInfo")) {
                    net.sf.json.JSONObject accessInfo = data.getJSONObject("accessInfo");
                    if (StringUtils.isNotBlank(accessInfo.getString("csrfId"))) {
                        cjzh.putData("adbrain_one_csrfId", accessInfo.getString("csrfId"));
                        return new BiAuthResult(BiAuthResult.SUCCESS, "");
                    }
                }
            }
            SpiderLog.addTime("aiztone_BiAuth.adbrainOne", true);
            return new BiAuthResult(BiAuthResult.OTHER, body);
        } catch (Exception e) {
            SpiderLog.addTime("aiztone_BiAuth.adbrainOne", false);
            return new BiAuthResult(BiAuthResult.EXCEPTION, e.getMessage());
        } finally {
            httpGet.releaseConnection();
        }
    }

    public static void loginAlimamaAizt(Cjzh cjzh) {
        CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cjzh.getCookieStore()).build();
        // 访问万相台无界首页：如果没有domain为.alimama.com的cookie，会连续重定向n次
        // 初始网页：https://one.alimama.com/index.html
        // 第1次重定向（获取所有domain以taobao.com结尾的cookie）：https://login.taobao.com/jump?group=tao&target=https%3A%2F%2Fone.alimama.com%2Findex.html%3Ftbpm%3D1
        // 第2次重定向（请求状态码307）：https://pass.alimama.com/add?enc=……
        // 第3次重定向（获取domain为.alimama.com的cookie）：https://pass.alimama.com/add?enc=……
        // 第4次重定向：https://one.alimama.com/index.html?tbpm=1
        // 第5次重定向：https://one.alimama.com/index.html

        RequestConfig.Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000).setConnectionRequestTimeout(20000);
        requestConfig.setCircularRedirectsAllowed(true);
        HttpGet httpget = null;
        HttpResponse httpresponse;
        String url = "https://one.alimama.com/index.html";
        httpget = new HttpGet(url);
        httpget.setHeader("accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9");
        httpget.setHeader("accept-encoding", "gzip, deflate, br");
        httpget.setHeader("accept-language", "zh-CN,zh;q=0.9,en;q=0.8");
        // httpget.setHeader("cache-control", "max-age=0");
        // httpget.setHeader("upgrade-insecure-requests", "1");
        httpget.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.198 Safari/537.36");
        httpget.setConfig(requestConfig.build());
        try {
            httpresponse = httpClient.execute(httpget);
            HttpEntity entity = httpresponse.getEntity();
            EntityUtils.toString(entity);
            SpiderLog.addTime("aiztone_AiztOneHttpClient.loginAlimamaAizt", true);
        } catch (Exception e) {
            SpiderLog.addTime("aiztone_AiztOneHttpClient.loginAlimamaAizt", false);
            LOG.error(e.getMessage(), e);
        } finally {
            httpget.releaseConnection();
        }
    }

    /**
     * 多目表直投权限
     *
     * @param cjzh
     * @return
     */
    public static BiAuthResult dmbzt(Cjzh cjzh) {
        // https://sycm.taobao.com/oneauth/api/permission.json?p_url=http%3A%2F%2Fsycm.taobao.com%2Fcc%2Fitem_rank&_=1636010721759&token=1ced73e20
        // 返回code不为0 则说明生意参谋异常
        CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cjzh.getCookieStore()).build();
        HttpPost httpGet = new HttpPost("https://one.alimama.com/component/findList.json?lite2=false&csrfId=" + cjzh.getData("adbrain_one_csrfId"));
        httpGet.setHeader("accept", "application/json, text/javascript, */*; q=0.01");
        httpGet.setHeader("accept-encoding", "gzip, deflate, br");
        httpGet.setHeader("accept-language", "zh-CN,zh;q=0.9");
        httpGet.setHeader("bx-v", "2.5.1");
        httpGet.setHeader("content-type", "application/json");
        httpGet.setHeader("origin", "https://one.alimama.com");
        httpGet.setHeader("referer", "https://one.alimama.com/index.html");
        httpGet.setHeader("sec-ch-ua", "\"Google Chrome\";v=\"95\", \"Chromium\";v=\"95\", \";Not A Brand\";v=\"99\"");
        httpGet.setHeader("sec-ch-ua-mobile", "?0");
        httpGet.setHeader("sec-ch-ua-platform", "\"Windows\"");
        httpGet.setHeader("sec-fetch-dest", "empty");
        httpGet.setHeader("sec-fetch-mode", "cors");
        httpGet.setHeader("sec-fetch-site", "same-origin");
        httpGet.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");
        httpGet.setHeader("x-requested-with", "XMLHttpRequest");
        RequestConfig.Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000).setConnectionRequestTimeout(20000);
        httpGet.setConfig(requestConfig.build());
        HttpResponse httpResponse;
        try {
            StringEntity param = new StringEntity("{\"lite2\":false,\"componentCode\":\"report_frame_account\",\"bizCode\":\"universalBP\",\"filterMap\":{\"rptType\":\"account\"},\"csrfId\":\"" + cjzh.getData("adbrain_one_csrfId") + "\"}");
            httpGet.setEntity(param);

            httpResponse = httpClient.execute(httpGet);
            HttpEntity entity = httpResponse.getEntity();
            String body = EntityUtils.toString(entity, "utf-8");
            if (!StringUtil.isJson2(body)) {
                SpiderLog.addTime("aiztone_AiztOneHttpClient.dmbzt", false);
                return new BiAuthResult(BiAuthResult.OTHER, body);
            }
            SpiderLog.addTime("aiztone_AiztOneHttpClient.dmbzt", true);
            if (body.contains("multiAimSceneCharge")) {
                //{"data":{"list":[{"webType":null,"subComponentFilterCodeList":null,"code":"report_frame_account","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"Block","webProperties":null,"filterExpression":null,"name":"报表","id":6256029,"subComponentList":[{"webType":null,"subComponentFilterCodeList":null,"code":"filterCard","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"<a target=\"_blank\" class=\"color-brand\" href=\"https://www.yuque.com/maratlinlinsen/qnqph6/xbdvw0o4838npv0t?spm=a2e1zy.********.cf3a7f6bd_filterCard.1.26367e1bTQeQOS\" style=\"display: flex;align-items: center;\" data-spm-anchor-id=\"a2e1zy.********.cf3a7f6bd_filterCard.1\">如何快速使用报表<i class=\"mx-iconfont fontsize-12 ml4\"></i></a>","filters":null,"sort":null,"type":"Block","webProperties":null,"filterExpression":null,"name":"账户报表","id":6238020,"subComponentList":[{"webType":null,"subComponentFilterCodeList":null,"code":"filterList","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"Block","webProperties":null,"filterExpression":null,"name":"全局筛选项","id":6958650,"subComponentList":[{"webType":"dropdown","subComponentFilterCodeList":null,"code":"unifyType","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"归因模型","id":6958651,"subComponentList":[{"webType":null,"subComponentFilterCodeList":null,"code":"zhai","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"末次触点归因：转化周期内1BP广告作为最后一个触点被消费者点击，可选择7天/15天/30天归因窗口。","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"末次点击归因","id":6958652,"subComponentList":null,"value":null,"properties":null,"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"mta","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"MTA归因","id":6958653,"subComponentList":null,"value":null,"properties":{"tag":"推荐"},"status":1}],"value":null,"properties":{"defaultValue":"zhai","paramsScope":"common"},"status":1},{"webType":"dropdown","subComponentFilterCodeList":null,"code":"effectEqual","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"从用户点击广告开始，跟踪并统计该用户的浏览、购买、收藏等点击后续行为，称之为转化数据。举例：点击发生15日内产生的购买、收藏等数据，归类为15天转化数据，以此类推。","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"归因周期","id":6958654,"subComponentList":[{"webType":null,"subComponentFilterCodeList":null,"code":"1","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"1天累计数据","id":6958655,"subComponentList":null,"value":null,"properties":null,"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"3","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"3天累计数据","id":6958656,"subComponentList":null,"value":null,"properties":null,"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"7","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"7天累计数据","id":6958657,"subComponentList":null,"value":null,"properties":null,"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"15","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"15天累计数据","id":6958658,"subComponentList":null,"value":null,"properties":null,"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"30","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"30天累计数据","id":6958659,"subComponentList":null,"value":null,"properties":null,"status":1}],"value":null,"properties":{"paramsScope":"common"},"status":1},{"webType":"daterange","subComponentFilterCodeList":null,"code":"report_date","defaultValue":null,"bizCode":null,"assistant":null,"rule":"com.alibaba.ad.adplus.site.domain.domainobject.component.upgrade.UpgradeComponentRule@1dcb8f5","description":"统计在所选时间范围内，各类指标的汇总数据","filters":null,"sort":null,"type":"Block","webProperties":null,"filterExpression":null,"name":"汇总周期","id":6958660,"subComponentList":null,"value":null,"properties":{"default":7,"min":180,"max-gap":90,"max":1,"shortKeys":"yesterday,passed7,preWeekMon,passed15,passedThisMonth,passed30,preMonth","paramsScope":"common"},"status":1},{"webType":"dropdown","subComponentFilterCodeList":null,"code":"splitType","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"时间粒度","id":6958661,"subComponentList":[{"webType":null,"subComponentFilterCodeList":null,"code":"sum","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"转化周期是指消费者通过点击广告展位之后，分别统计在1天/3天/7天/15天/30天带来的收藏加购成交等效果数据。","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"汇总","id":6958662,"subComponentList":null,"value":null,"properties":null,"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"hour","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"分时","id":6958663,"subComponentList":null,"value":null,"properties":null,"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"day","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"支持分日数据","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"分日","id":6958664,"subComponentList":null,"value":null,"properties":null,"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"week","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"支持分周数据","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"分周","id":6958665,"subComponentList":null,"value":null,"properties":null,"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"month","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"支持分月数据","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"分月","id":6958666,"subComponentList":null,"value":null,"properties":null,"status":1}],"value":null,"properties":{"defaultValue":"day","paramsScope":"common"},"status":1},{"webType":"cascade-checkbox","subComponentFilterCodeList":null,"code":"bizCodeIn","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"营销场景","id":6958667,"subComponentList":[{"webType":null,"subComponentFilterCodeList":null,"code":"onebpCustomerMarketing","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"消费者运营","id":6968714,"subComponentList":[{"webType":null,"subComponentFilterCodeList":null,"code":"adStrategyDkx","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"拉新快","id":6968715,"subComponentList":null,"value":null,"properties":null,"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"adStrategyFans","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"粉丝快","id":7340039,"subComponentList":null,"value":null,"properties":null,"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"adStrategyRuHui","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"会员快","id":6968716,"subComponentList":null,"value":null,"properties":null,"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"adStrategyCrowd","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"人群击穿","id":7812114,"subComponentList":null,"value":null,"properties":null,"status":1}],"value":null,"properties":null,"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"onebpItemMarketing","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"货品运营","id":6968717,"subComponentList":[{"webType":null,"subComponentFilterCodeList":null,"code":"adStrategyShangXin","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"上新快","id":6968718,"subComponentList":null,"value":null,"properties":null,"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"adStrategyProductSpeed","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"货品加速","id":6968719,"subComponentList":null,"value":null,"properties":null,"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"adStrategyCeKuan","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"测款快","id":6968720,"subComponentList":null,"value":null,"properties":null,"status":1}],"value":null,"properties":null,"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"adStrategyYuRe","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"活动加速","id":6968721,"subComponentList":null,"value":null,"properties":null,"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"onebpShopMarketing","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"店铺运营","id":7378002,"subComponentList":[{"webType":null,"subComponentFilterCodeList":null,"code":"adStrategyWholeShop","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"全店智投","id":7382001,"subComponentList":null,"value":null,"properties":null,"status":1}],"value":null,"properties":null,"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"onebpSearch","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"自定义-关键词推广","id":6968722,"subComponentList":null,"value":null,"properties":null,"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"onebpDisplay","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"自定义-人群推广","id":6968723,"subComponentList":null,"value":null,"properties":null,"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"onebpAimMarketing","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"多目标直投","id":7816013,"subComponentList":[{"webType":null,"subComponentFilterCodeList":null,"code":"adStrategyMultiAim","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"多目标直投","id":7834046,"subComponentList":null,"value":null,"properties":null,"status":1}],"value":null,"properties":null,"status":1}],"value":null,"properties":{"paramsScope":"commonMore","paramsGroupName":"按场景"},"status":1},{"webType":"dropdown-checkbox","subComponentFilterCodeList":null,"code":"strategyPromotionModeIn","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"推广模式","id":7368003,"subComponentList":[{"webType":null,"subComponentFilterCodeList":null,"code":"daily","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"持续推广","id":7368004,"subComponentList":null,"value":null,"properties":null,"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"order","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"套餐包","id":7368005,"subComponentList":null,"value":null,"properties":null,"status":1}],"value":null,"properties":{"paramsScope":"commonMore","paramsGroupName":"按场景"},"status":1}],"value":null,"properties":null,"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"template","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"报表模版","id":7068055,"subComponentList":[{"webType":null,"subComponentFilterCodeList":null,"code":"allCampaign","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"Block","webProperties":null,"filterExpression":null,"name":"全部计划","id":7204026,"subComponentList":null,"value":null,"properties":{"templateValue":"{\"havingList\":[],\"unifyType\":\"zhai\",\"effectEqual\":15,\"splitType\":\"day\",\"dateDefault\":7}"},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"subway","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"Block","webProperties":null,"filterExpression":null,"name":"关键词","id":7112079,"subComponentList":null,"value":null,"properties":{"templateValue":"{\"havingList\":[],\"unifyType\":\"zhai\",\"effectEqual\":15,\"splitType\":\"day\",\"bizCodeIn\":[\"onebpSearch\"],\"dateDefault\":7}"},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"yinli","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"Block","webProperties":null,"filterExpression":null,"name":"人群推广","id":7112080,"subComponentList":null,"value":null,"properties":{"templateValue":"{\"havingList\":[],\"unifyType\":\"zhai\",\"effectEqual\":15,\"splitType\":\"day\",\"bizCodeIn\":[\"onebpDisplay\"],\"dateDefault\":30}"},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"wxt","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"Block","webProperties":null,"filterExpression":null,"name":"场景推广","id":7112081,"subComponentList":null,"value":null,"properties":{"templateValue":"{\"havingList\":[],\"unifyType\":\"zhai\",\"effectEqual\":15,\"splitType\":\"day\",\"bizCodeIn\":[\"adStrategyDkx\",\"adStrategyRuHui\",\"adStrategyWholeShop\",\"adStrategyShangXin\",\"adStrategyProductSpeed\",\"adStrategyCeKuan\",\"adStrategyYuRe\",\"adStrategyFans\",\"wxtAgencyAI\",\"wxtAgencySmart\",\"adStrategyCrowd\"],\"dateDefault\":30}"},"status":1}],"value":null,"properties":null,"status":1}],"value":null,"properties":null,"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"chargeCard","defaultValue":null,"bizCode":null,"assistant":null,"rule":"com.alibaba.ad.adplus.site.domain.domainobject.component.upgrade.UpgradeComponentRule@2aa84459","description":"花费数据汇总板块暂不支持「持续推广」和「套餐包」的数据筛选","filters":null,"sort":null,"type":"Block","webProperties":null,"filterExpression":null,"name":"花费数据汇总","id":7530034,"subComponentList":[{"webType":null,"subComponentFilterCodeList":null,"code":"fieldList","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"Block","webProperties":null,"filterExpression":null,"name":"字段列表","id":7530035,"subComponentList":[{"webType":null,"subComponentFilterCodeList":null,"code":"totalCharge","defaultValue":null,"bizCode":null,"assistant":null,"rule":"com.alibaba.ad.adplus.site.domain.domainobject.component.upgrade.UpgradeComponentRule@5b3ffac1","description":"","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"总花费","id":7530036,"subComponentList":[{"webType":null,"subComponentFilterCodeList":null,"code":"guaranteePackCharge","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"含核算完成确定性保障套餐包","id":7390009,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"基础指标","default":"false","unit":"元","formater":"formatFloat","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","defOrderBy":"","disableSort":"false"},"status":1}],"value":null,"properties":{"disableHaving":"false","parent":"基础指标","default":"true","unit":"元","formater":"formatFloat","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","defOrderBy":"","disableSort":"false"},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"crowdSceneCharge","defaultValue":null,"bizCode":null,"assistant":null,"rule":"com.alibaba.ad.adplus.site.domain.domainobject.component.upgrade.UpgradeComponentRule@575369d2","description":"","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"消费者运营花费","id":7530037,"subComponentList":[{"webType":null,"subComponentFilterCodeList":null,"code":"xinXiangGiftCharge","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"含确超新计划","id":7530038,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"基础指标","default":"false","unit":"元","formater":"formatFloat","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","defOrderBy":"","disableSort":"false"},"status":1}],"value":null,"properties":{"disableHaving":"false","parent":"基础指标","default":"true","unit":"元","formater":"formatFloat","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","defOrderBy":"","disableSort":"false"},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"itemSceneCharge","defaultValue":null,"bizCode":null,"assistant":null,"rule":"com.alibaba.ad.adplus.site.domain.domainobject.component.upgrade.UpgradeComponentRule@d4a7177","description":"","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"货品运营花费","id":7530039,"subComponentList":[{"webType":null,"subComponentFilterCodeList":null,"code":"ybqGiftCharge","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"含新品一笔钱","id":7530040,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"基础指标","default":"false","unit":"元","formater":"formatFloat","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","disableSort":"false"},"status":1}],"value":null,"properties":{"disableHaving":"false","parent":"基础指标","default":"true","unit":"元","formater":"formatFloat","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","disableSort":"false"},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"activitySceneCharge","defaultValue":null,"bizCode":null,"assistant":null,"rule":"com.alibaba.ad.adplus.site.domain.domainobject.component.upgrade.UpgradeComponentRule@4e960a","description":"","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"活动运营花费","id":7530041,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"基础指标","default":"true","unit":"元","formater":"formatFloat","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","disableSort":"false"},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"displayCharge","defaultValue":null,"bizCode":null,"assistant":null,"rule":"com.alibaba.ad.adplus.site.domain.domainobject.component.upgrade.UpgradeComponentRule@19b12d1a","description":"","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"人群场景花费","id":7530042,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"基础指标","default":"true","unit":"元","formater":"formatFloat","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","disableSort":"false"},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"searchCharge","defaultValue":null,"bizCode":null,"assistant":null,"rule":"com.alibaba.ad.adplus.site.domain.domainobject.component.upgrade.UpgradeComponentRule@38a40306","description":"","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"关键词花费","id":7530043,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"基础指标","default":"true","unit":"元","formater":"formatFloat","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","defOrderBy":"","disableSort":"false"},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"contentSceneCharge","defaultValue":null,"bizCode":null,"assistant":null,"rule":"com.alibaba.ad.adplus.site.domain.domainobject.component.upgrade.UpgradeComponentRule@75dfbcc6","description":"","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"内容场景花费","id":7530044,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"基础指标","default":"true","unit":"元","formater":"formatFloat","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","disableSort":"false"},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"shopSceneCharge","defaultValue":null,"bizCode":null,"assistant":null,"rule":"com.alibaba.ad.adplus.site.domain.domainobject.component.upgrade.UpgradeComponentRule@69a5f8ca","description":"","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"店铺运营场景花费","id":7530045,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"基础指标","default":"true","unit":"元","formater":"formatFloat","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","disableSort":"false"},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"multiAimSceneCharge","defaultValue":null,"bizCode":null,"assistant":null,"rule":"com.alibaba.ad.adplus.site.domain.domainobject.component.upgrade.UpgradeComponentRule@39388345","description":"","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"多目标直投花费","id":7892004,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"基础指标","default":"true","unit":"元","formater":"formatFloat","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","defOrderBy":"","disableSort":"false"},"status":1}],"value":null,"properties":{"paramsScope":"common"},"status":1}],"value":null,"properties":null,"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"sumDataCard","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"以下数据不包括「直播、短视频、获客易、拉新快-超新计划、确定性保障套餐包」计划","filters":null,"sort":null,"type":"Block","webProperties":null,"filterExpression":null,"name":"数据汇总","id":6236012,"subComponentList":[{"webType":null,"subComponentFilterCodeList":null,"code":"filterList","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"Block","webProperties":null,"filterExpression":null,"name":"汇总筛选项","id":6246008,"subComponentList":[{"webType":null,"subComponentFilterCodeList":null,"code":"fieldList","defaultValue":null,"bizCode":null,"assistant":null,"rule":"com.alibaba.ad.adplus.site.domain.domainobject.component.upgrade.UpgradeComponentRule@2a66267a","description":null,"filters":null,"sort":null,"type":"Block","webProperties":null,"filterExpression":null,"name":"字段列表","id":6752016,"subComponentList":[{"webType":null,"subComponentFilterCodeList":null,"code":"adPv","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"推广内容被用户看到的次数（去除因用户快速划过、主图未完全展现等情况产生的无效曝光）","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"展现量","id":6818126,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"基础指标","default":"true","unit":"","formater":"formatInt","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","defOrderBy":"","disableSort":"false"},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"click","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"推广内容被用户点击的次数。说明：虚假点击会被反作弊体系过滤，该数据为反作弊过滤后的数据。","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"点击量","id":6872489,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"基础指标","default":"true","unit":"","formater":"formatInt","is_uv_field":"0","disabled":"false","tag":"","disableSort":"false"},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"charge","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"推广内容被用户点击或发生其他约定计费的行为所花费的金额。说明：如存在报表和实际结算数据不一致，请以财务记录中实际扣款为准。","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"花费","id":6818154,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"基础指标","default":"true","unit":"元","formater":"formatFloat","is_uv_field":"0","disabled":"false","tag":"","disableSort":"false"},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"ctr","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"点击率 = 点击量 / 展现量，可直观表示推广主体的吸引程度，点击率越高，说明推广主体对用户的吸引力越大。","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"点击率","id":6824032,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"基础指标","default":"true","unit":"","formater":"formatPer","is_uv_field":"0","disabled":"false","tag":"","disableSort":"false"},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"ecpc","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"平均点击花费 = 花费 / 点击量，即每一次点击产生的平均花费金额。","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"平均点击花费","id":6800058,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"基础指标","default":"true","unit":"元","formater":"formatFloat","is_uv_field":"0","disabled":"false","tag":"","disableSort":"false"},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"ecpm","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"千次展现花费 = 花费 / 展现量*1000，即每千次展现产生的平均花费金额，用于评估推广主体的展现成本。","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"千次展现花费","id":6830013,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"基础指标","default":"false","unit":"元","formater":"formatFloat","is_uv_field":"0","disabled":"false","tag":"","disableSort":"false"},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"prepayInshopAmt","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"总预售成交金额 = 直接预售成交金额 + 间接预售成交金额","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"总预售成交金额","id":6866057,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"成交意向","default":"false","unit":"元","formater":"formatFloat","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","defOrderBy":"","disableSort":"false"},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"prepayInshopNum","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"总预售成交笔数 = 直接预售成交笔数 + 间接预售成交笔数","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"总预售成交笔数","id":6830090,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"成交意向","default":"false","unit":"","formater":"formatInt","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","defOrderBy":"","disableSort":"false"},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"prepayDirAmt","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"用户直接在该预售商品的详情页面拍下并支付的成交金额（含定金、尾款及运费）","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"直接预售成交金额","id":6808036,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"成交意向","default":"false","unit":"元","formater":"formatFloat","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","defOrderBy":"","disableSort":"false"},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"prepayDirNum","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"用户直接在该预售商品详情页面拍下并支付的成交笔数","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"直接预售成交笔数","id":6810009,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"成交意向","default":"false","unit":"","formater":"formatInt","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","defOrderBy":"","disableSort":"false"},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"prepayIndirAmt","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"用户通过推广内容跳转至店铺内其他预售商品详情页面拍下并支付的成交金额（含定金、尾款及运费）","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"间接预售成交金额","id":6824033,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"成交意向","default":"false","unit":"元","formater":"formatFloat","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","defOrderBy":"","disableSort":"false"},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"prepayIndirNum","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"用户通过推广内容跳转至店铺内其他预售商品详情页面拍下并支付的成交笔数","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"间接预售成交笔数","id":6804504,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"成交意向","default":"false","unit":"","formater":"formatInt","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","defOrderBy":"","disableSort":"false"},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"alipayDirAmt","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"推广内容引导用户直接在推广宝贝的详情页面拍下并通过支付宝交易的成交金额","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"直接成交金额","id":6818155,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"成交转化","default":"false","unit":"元","formater":"formatFloat","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","defOrderBy":"","disableSort":"false"},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"alipayIndirAmt","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"推广内容引导用户通过跳转至店铺内其他宝贝的详情页面拍下并通过支付宝交易的成交金额（含运费）","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"间接成交金额","id":6820189,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"成交转化","default":"false","unit":"元","formater":"formatFloat","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","defOrderBy":"","disableSort":"false"},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"alipayInshopAmt","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"总成交金额 = 直接成交金额 + 间接成交金额，即推广内容引导用户产生的所有支付宝交易的总成交金额","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"总成交金额","id":6848016,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"成交转化","default":"true","unit":"元","formater":"formatFloat","is_uv_field":"0","disabled":"false","tag":"","disableSort":"false"},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"alipayInshopNum","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"总成交笔数 = 直接成交笔数 + 间接成交笔数，即推广内容引导用户产生的所有支付宝交易的总成交笔数","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"总成交笔数","id":6830014,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"成交转化","default":"true","unit":"","formater":"formatInt","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","disableSort":"false"},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"alipayDirNum","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"推广内容引导用户直接在推广宝贝的详情页面拍下并通过支付宝交易的成交笔数","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"直接成交笔数","id":6860009,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"成交转化","default":"false","unit":"","formater":"formatInt","is_uv_field":"0","disabled":"false","tag":"","disableSort":"false"},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"alipayIndirNum","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"推广内容引导用户通过跳转至店铺内其他宝贝的详情页面拍下并通过支付宝交易的成交笔数","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"间接成交笔数","id":6836015,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"成交转化","default":"false","unit":"","formater":"formatInt","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","defOrderBy":"","disableSort":"false"},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"cvr","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"非内容推广：点击转化率 = 总成交笔数 / 点击量","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"点击转化率","id":6852024,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"成交转化","default":"true","unit":"","formater":"formatPer","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","defOrderBy":"","disableSort":"false"},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"roi","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"投入产出比 = 总成交金额 / 花费，反映花费在所选转化周期内带来支付宝成交金额的比例","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"投入产出比","id":6848017,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"成交转化","default":"false","unit":"","formater":"formatFloat","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","disableSort":"false"},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"alipayInshopCost","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"总成交成本 = 花费 / 总成交笔数","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"总成交成本","id":6842007,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"成交转化","default":"false","unit":"元","formater":"formatFloat","is_uv_field":"0","disabled":"false","tag":"","disableSort":"false"},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"cartInshopNum","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"总购物车数 = 直接购物车数 + 间接购物车数，即推广内容引导用户产生加入购物车行为的总次数。（当天实时数据包含预售支付定金数据，次日剔除）","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"总购物车数","id":6810010,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"潜在转化","default":"true","unit":"","formater":"formatInt","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","defOrderBy":"","disableSort":"false"},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"cartDirNum","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"用户对该推广宝贝加入购物车的次数（当天实时数据包含直接预售支付定金数据，次日剔除）","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"直接购物车数","id":6860010,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"潜在转化","default":"false","unit":"","formater":"formatInt","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","defOrderBy":"","disableSort":"false"},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"cartIndirNum","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"推广内容引导用户跳转至店铺其他宝贝详情页后发生宝贝加入购物车的次数（当天实时数据包含间接预售支付定金数据，次日剔除）","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"间接购物车数","id":6794408,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"潜在转化","default":"false","unit":"","formater":"formatInt","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","defOrderBy":"","disableSort":"false"},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"cartRate","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"加购率 = 总购物车数 / 点击量","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"加购率","id":6806453,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"潜在转化","default":"false","unit":"","formater":"formatPer","is_uv_field":"0","disabled":"false","tag":"","disableSort":"false"},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"itemColInshopNum","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"推广内容引导用户收藏宝贝的次数","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"收藏宝贝数","id":6832174,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"潜在转化","default":"true","unit":"","formater":"formatInt","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","disableSort":"false"},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"shopColDirNum","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"推广内容引导用户收藏本店铺的次数","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"收藏店铺数","id":6862125,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"潜在转化","default":"true","unit":"","formater":"formatInt","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","disableSort":"false"},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"shopColInshopCost","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"店铺收藏成本 = 花费 / 收藏店铺数","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"店铺收藏成本","id":6870007,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"潜在转化","default":"false","unit":"元","formater":"formatFloat","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","defOrderBy":"","disableSort":"false"},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"colCartNum","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"总收藏加购数 = 总收藏数 + 总购物车数","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"总收藏加购数","id":6800059,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"潜在转化","default":"false","unit":"","formater":"formatInt","is_uv_field":"0","disabled":"false","tag":"","disableSort":"false"},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"colCartCost","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"总收藏加购成本 = 花费 / 总收藏加购数","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"总收藏加购成本","id":6800060,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"潜在转化","default":"false","unit":"元","formater":"formatFloat","is_uv_field":"0","disabled":"false","tag":"","disableSort":"false"},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"itemColCart","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"宝贝收藏加购数 = 宝贝收藏数 + 总购物车数","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"宝贝收藏加购数","id":6842008,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"潜在转化","default":"false","unit":"","formater":"formatInt","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","disableSort":"false"},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"itemColCartCost","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"宝贝收藏加购成本 = 花费 / 宝贝收藏加购数","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"宝贝收藏加购成本","id":6860012,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"潜在转化","default":"false","unit":"元","formater":"formatFloat","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","disableSort":"false"},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"colNum","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"总收藏数 = 总收藏宝贝数+收藏店铺数，即推广内容引导用户收藏宝贝和店铺的总次数","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"总收藏数","id":6872491,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"潜在转化","default":"true","unit":"","formater":"formatInt","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","defOrderBy":"","disableSort":"false"},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"itemColInshopCost","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"宝贝收藏成本 = 花费 / 收藏宝贝数","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"宝贝收藏成本","id":6804506,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"潜在转化","default":"true","unit":"元","formater":"formatFloat","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","disableSort":"false"},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"itemColInshopRate","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"宝贝收藏率 = 收藏宝贝数 / 点击量","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"宝贝收藏率","id":6856031,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"潜在转化","default":"false","unit":"","formater":"formatPer","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","disableSort":"false"},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"cartCost","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"加购成本 = 花费 / 总购物车数","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"加购成本","id":6836016,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"潜在转化","default":"false","unit":"元","formater":"formatFloat","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","disableSort":"false"},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"gmvInshopNum","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"用户拍下订单总数（含未付款）","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"拍下订单笔数","id":6846004,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"成交意向","default":"false","unit":"","formater":"formatInt","is_uv_field":"0","disabled":"false","tag":"","disableSort":"false"},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"gmvInshopAmt","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"用户拍下订单总数（含未付款）","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"拍下订单金额","id":6820180,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"成交意向","default":"false","unit":"元","formater":"formatFloat","is_uv_field":"0","disabled":"false","tag":"","disableSort":"false"},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"itemColDirNum","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"用户收藏该推广宝贝的次数","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"直接收藏宝贝数","id":6820181,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"潜在转化","default":"false","unit":"","formater":"formatInt","is_uv_field":"0","disabled":"false","tag":"","disableSort":"false"},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"itemColIndirNum","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"推广内容引导用户跳转至店铺其他宝贝详情页后发生宝贝收藏行为的次数","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"间接收藏宝贝数","id":6810012,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"潜在转化","default":"false","unit":"","formater":"formatInt","is_uv_field":"0","disabled":"false","tag":"","disableSort":"false"},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"couponShopNum","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"用户领取优惠券的总数量（包含店铺和宝贝优惠券）","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"优惠券领取量","id":6830015,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"潜在转化","default":"false","unit":"","formater":"formatInt","is_uv_field":"0","disabled":"false","tag":"","disableSort":"false"},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"shoppingNum","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"推广内容引导充值店铺购物金的笔数","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"购物金充值笔数","id":6824034,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"潜在转化","default":"false","unit":"","formater":"formatInt","is_uv_field":"0","disabled":"false","tag":"","disableSort":"false"},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"shoppingAmt","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"推广内容引导充值店铺购物金的金额","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"购物金充值金额","id":6844199,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"潜在转化","default":"false","unit":"元","formater":"formatFloat","is_uv_field":"0","disabled":"false","tag":"","disableSort":"false"},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"wwNum","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"推广引导的用户通过旺旺发起的咨询量","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"旺旺咨询量","id":6858009,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"线索信息","default":"false","unit":"","formater":"formatInt","is_uv_field":"0","disabled":"false","tag":"","disableSort":"false"},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"inshopPv","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"用户访问创意落地页、店铺首页及宝贝详情页等，总浏览页面数量","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"引导访问量","id":6860013,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"引导访问","default":"false","unit":"","formater":"formatInt","is_uv_field":"0","disabled":"false","tag":"","disableSort":"false"},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"inshopUv","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"用户访问创意落地页、店铺首页及宝贝详情页等页面的总浏览人数","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"引导访问人数","id":6830016,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"引导访问","default":"false","unit":"","formater":"formatInt","is_uv_field":"1","disabled":"false","tag":"","disableSort":"false"},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"inshopPotentialUv","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"用户访问创意落地页、店铺首页及宝贝详情页等页面的总浏览潜客人数。 潜客定义：15天内无推广点击内容渠道（如淘宝头条、微淘）浏览互动/进店/搜索/点击行为；且90天内无收藏/加购店铺/宝贝行为；且365天内无店铺下单/购买行为的用户","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"引导访问潜客数","id":6866058,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"引导访问","default":"false","unit":"","formater":"formatInt","is_uv_field":"1","disabled":"false","tag":"","disableSort":"false"},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"inshopPotentialUvRate","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"引导访问潜客占⽐ = 引导访问潜客人数 / 引导访问人数","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"引导访问潜客占比","id":6790195,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"引导访问","default":"false","unit":"","formater":"formatPer","is_uv_field":"1","disabled":"false","tag":"","disableSort":"false"},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"rhRate","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"入会率 = 入会量 / 点击量","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"入会率","id":6842010,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"会员获取","default":"false","unit":"","formater":"formatPer","is_uv_field":"0","disabled":"false","tag":"","disableSort":"false"},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"rhNum","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"推广内容引导用户成为会员的人次","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"入会量","id":6804507,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"会员获取","default":"false","unit":"","formater":"formatInt","is_uv_field":"0","disabled":"false","tag":"","disableSort":"false"},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"inshopPvRate","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"引导访问率 = 引导访问量 / 展现量","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"引导访问率","id":6828116,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"引导访问","default":"false","unit":"","formater":"formatPer","is_uv_field":"1","disabled":"false","tag":"","disableSort":"false"},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"deepInshopPv","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"用户访问创意落地页、店铺首页及宝贝详情页等，浏览页面数量>1的访问次数","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"深度访问量","id":6854009,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"引导访问","default":"false","unit":"","formater":"formatInt","is_uv_field":"0","disabled":"false","tag":"","disableSort":"false"},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"avgAccessPageNum","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"平均访问页面数 = 引导访问量 / 引导访问人数","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"平均访问页面数","id":6806454,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"引导访问","default":"false","unit":"","formater":"formatInt","is_uv_field":"0","disabled":"false","tag":"","disableSort":"false"},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"newAlipayInshopUv","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"引导成交的用户中新成交的用户数量（365天无成交），人数去重","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"成交新客数","id":6806455,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"人群效果","default":"false","unit":"","formater":"formatInt","is_uv_field":"1","disabled":"false","tag":"","disableSort":"false"},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"newAlipayInshopUvRate","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"成交新客占比 = 成交新客数 / 成交用户数","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"成交新客占比","id":6856032,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"人群效果","default":"false","unit":"","formater":"formatPer","is_uv_field":"1","disabled":"false","tag":"","disableSort":"false"},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"hySgUv","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"店铺未购买会员（365天内店铺维度未购）首次成交的人数，人数去重","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"会员首购人数","id":6816237,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"会员获取","default":"false","unit":"","formater":"formatInt","is_uv_field":"1","disabled":"false","tag":"","disableSort":"false"},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"hyPayAmt","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"推广内容引导店铺会员成交的订单金额。预售订单付尾款前不会出现在成交金额中","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"会员成交金额","id":6820185,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"会员获取","default":"false","unit":"","formater":"formatFloat","is_uv_field":"0","disabled":"false","tag":"","disableSort":"false"},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"hyPayNum","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"推广内容引导店铺会员成交的订单笔数","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"会员成交笔数","id":6846005,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"会员获取","default":"false","unit":"","formater":"formatInt","is_uv_field":"0","disabled":"false","tag":"","disableSort":"false"},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"alipayInshopUv","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"推广内容引导通过支付宝交易并支付成功的用户人数。人数去重","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"成交人数","id":6852029,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"成交转化","default":"false","unit":"","formater":"formatInt","is_uv_field":"1","disabled":"false","tag":"","caseFillField":"","defOrderBy":"","disableSort":"false"},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"alipayInshopNumAvg","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"人均成交笔数 = 总成交笔数 / 成交人数。人数去重","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"人均成交笔数","id":6868056,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"成交转化","default":"false","unit":"","formater":"formatInt","is_uv_field":"1","disabled":"false","tag":"","disableSort":"false"},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"alipayInshopAmtAvg","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"人均成交金额 = 总成交金额 / 成交人数。人数去重","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"人均成交金额","id":6808091,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"成交转化","default":"false","unit":"","formater":"formatFloat","is_uv_field":"1","disabled":"false","tag":"","disableSort":"false"},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"naturalPayAmt","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"以推广拉动的自然流量曝光数据为基础，根据自然流量的平均点击转化率及笔单价，计算得出带来的转化金额数据。这是由推广拉动的自然流量带来的转化增益。注意：不支持细分至定向/资源位/创意维度","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"自然流量转化金额","id":7274260,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"广义效果","default":"false","unit":"","formater":"formatFloat","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","disableSort":"false"},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"orgNaturalPv","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"推广内容拉动了宝贝或店铺在手淘站内推荐渠道和搜索渠道中的自然流量，从而带来的曝光量。注意：不支持细分至定向/资源位/创意维度","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"自然流量曝光量","id":7320552,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"广义效果","default":"false","unit":"","formater":"formatInt","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","disableSort":"false"},"status":1}],"value":null,"properties":{"paramsScope":"common"},"status":1},{"webType":"dropdown","subComponentFilterCodeList":null,"code":"vsType","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"周期对比","id":6698153,"subComponentList":[{"webType":null,"subComponentFilterCodeList":null,"code":"off","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"关闭","id":6742024,"subComponentList":null,"value":null,"properties":null,"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"day","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"日环比","id":6698155,"subComponentList":null,"value":null,"properties":null,"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"week","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"周环比","id":6698156,"subComponentList":null,"value":null,"properties":null,"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"month","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"月环比","id":6698157,"subComponentList":null,"value":null,"properties":null,"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"halfYear","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"半年环比","id":6764449,"subComponentList":null,"value":null,"properties":null,"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"custom","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"自定义日期","id":6758018,"subComponentList":null,"value":null,"properties":null,"status":1}],"value":null,"properties":{"defaultValue":"week","paramsScope":"sum"},"status":1},{"webType":"date","subComponentFilterCodeList":null,"code":"vsTime","defaultValue":null,"bizCode":null,"assistant":null,"rule":"com.alibaba.ad.adplus.site.domain.domainobject.component.upgrade.UpgradeComponentRule@3f8a6ee9","description":null,"filters":null,"sort":null,"type":"Block","webProperties":null,"filterExpression":null,"name":"报表日期","id":6734037,"subComponentList":null,"value":null,"properties":{"default":1,"min":180,"max":1,"paramsScope":"sum"},"status":1}],"value":null,"properties":null,"status":1}],"value":null,"properties":null,"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"listDataCard","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"Block","webProperties":null,"filterExpression":null,"name":"账户数据明细","id":6254012,"subComponentList":[{"webType":null,"subComponentFilterCodeList":null,"code":"filterList","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"Block","webProperties":null,"filterExpression":null,"name":"列表筛选项","id":6254015,"subComponentList":[{"webType":"dropdown-checkbox","subComponentFilterCodeList":null,"code":"queryDomains","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"Block","webProperties":null,"filterExpression":null,"name":"维度","id":6746021,"subComponentList":[{"webType":null,"subComponentFilterCodeList":null,"code":"date","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"Block","webProperties":null,"filterExpression":null,"name":"时间","id":6936015,"subComponentList":null,"value":null,"properties":{"width":120},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"scene","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"Block","webProperties":null,"filterExpression":null,"name":"营销场景","id":6958645,"subComponentList":null,"value":null,"properties":null,"status":1}],"value":null,"properties":{"defaultValue":[],"paramsScope":"list"},"status":1}],"value":null,"properties":null,"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"columnList","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"Block","webProperties":null,"filterExpression":null,"name":"列表维度列","id":6966283,"subComponentList":[{"webType":null,"subComponentFilterCodeList":null,"code":"thedate","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"Block","webProperties":null,"filterExpression":null,"name":"时间","id":6966284,"subComponentList":null,"value":null,"properties":{"queryDomain":"date","width":120},"status":1},{"webType":null,"subComponentFilterCodeList":null,"code":"scene","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"Block","webProperties":null,"filterExpression":null,"name":"营销场景","id":6996004,"subComponentList":null,"value":null,"properties":{"queryDomain":"scene","width":96},"status":1}],"value":null,"properties":null,"status":1},{"webType":"daterange","subComponentFilterCodeList":null,"code":"download_date","defaultValue":null,"bizCode":null,"assistant":null,"rule":"com.alibaba.ad.adplus.site.domain.domainobject.component.upgrade.UpgradeComponentRule@4854a77a","description":null,"filters":null,"sort":null,"type":"Block","webProperties":null,"filterExpression":null,"name":"下载报表日期","id":6806828,"subComponentList":null,"value":null,"properties":{"min":360,"max-gap":180,"max":1,"shortKeys":"yesterday,passed7,preWeekMon,passed15,passedThisMonth,passed30,preMonth","paramsScope":"common"},"status":1}],"value":null,"properties":null,"status":1}],"value":null,"properties":null,"status":1}]},"info":{"lockSla":false,"redirectUrl":null,"errorEntityIdList":null,"httpStatus":null,"errorCode":null,"ok":true,"unlockUrl":null,"message":null,"disableTime":false}}
                return new BiAuthResult(BiAuthResult.SUCCESS, "");
            } else {
                //{"data":{"list":[{"subComponentFilterCodeList":null,"webType":null,"code":"report_frame_account","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"Block","webProperties":null,"filterExpression":null,"name":"报表","id":6256029,"subComponentList":[{"subComponentFilterCodeList":null,"webType":null,"code":"filterCard","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"<a target=\"_blank\" class=\"color-brand\" href=\"https://www.yuque.com/maratlinlinsen/qnqph6/xbdvw0o4838npv0t?spm=a2e1zy.********.cf3a7f6bd_filterCard.1.26367e1bTQeQOS\" style=\"display: flex;align-items: center;\" data-spm-anchor-id=\"a2e1zy.********.cf3a7f6bd_filterCard.1\">如何快速使用报表<i class=\"mx-iconfont fontsize-12 ml4\"></i></a>","filters":null,"sort":null,"type":"Block","webProperties":null,"filterExpression":null,"name":"账户报表","id":6238020,"subComponentList":[{"subComponentFilterCodeList":null,"webType":null,"code":"filterList","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"Block","webProperties":null,"filterExpression":null,"name":"全局筛选项","id":6958650,"subComponentList":[{"subComponentFilterCodeList":null,"webType":"dropdown","code":"unifyType","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"归因模型","id":6958651,"subComponentList":[{"subComponentFilterCodeList":null,"webType":null,"code":"zhai","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"末次触点归因：转化周期内1BP广告作为最后一个触点被消费者点击，可选择7天/15天/30天归因窗口。","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"末次点击归因","id":6958652,"subComponentList":null,"value":null,"properties":null,"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"mta","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"MTA归因","id":6958653,"subComponentList":null,"value":null,"properties":{"tag":"推荐"},"status":1}],"value":null,"properties":{"defaultValue":"zhai","paramsScope":"common"},"status":1},{"subComponentFilterCodeList":null,"webType":"dropdown","code":"effectEqual","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"从用户点击广告开始，跟踪并统计该用户的浏览、购买、收藏等点击后续行为，称之为转化数据。举例：点击发生15日内产生的购买、收藏等数据，归类为15天转化数据，以此类推。","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"归因周期","id":6958654,"subComponentList":[{"subComponentFilterCodeList":null,"webType":null,"code":"1","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"1天累计数据","id":6958655,"subComponentList":null,"value":null,"properties":null,"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"3","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"3天累计数据","id":6958656,"subComponentList":null,"value":null,"properties":null,"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"7","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"7天累计数据","id":6958657,"subComponentList":null,"value":null,"properties":null,"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"15","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"15天累计数据","id":6958658,"subComponentList":null,"value":null,"properties":null,"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"30","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"30天累计数据","id":6958659,"subComponentList":null,"value":null,"properties":null,"status":1}],"value":null,"properties":{"paramsScope":"common"},"status":1},{"subComponentFilterCodeList":null,"webType":"daterange","code":"report_date","defaultValue":null,"bizCode":null,"assistant":null,"rule":"com.alibaba.ad.adplus.site.domain.domainobject.component.upgrade.UpgradeComponentRule@af8bc51","description":"统计在所选时间范围内，各类指标的汇总数据","filters":null,"sort":null,"type":"Block","webProperties":null,"filterExpression":null,"name":"汇总周期","id":6958660,"subComponentList":null,"value":null,"properties":{"default":7,"min":180,"max-gap":90,"max":1,"shortKeys":"yesterday,passed7,preWeekMon,passed15,passedThisMonth,passed30,preMonth","paramsScope":"common"},"status":1},{"subComponentFilterCodeList":null,"webType":"dropdown","code":"splitType","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"时间粒度","id":6958661,"subComponentList":[{"subComponentFilterCodeList":null,"webType":null,"code":"sum","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"转化周期是指消费者通过点击广告展位之后，分别统计在1天/3天/7天/15天/30天带来的收藏加购成交等效果数据。","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"汇总","id":6958662,"subComponentList":null,"value":null,"properties":null,"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"hour","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"分时","id":6958663,"subComponentList":null,"value":null,"properties":null,"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"day","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"支持分日数据","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"分日","id":6958664,"subComponentList":null,"value":null,"properties":null,"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"week","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"支持分周数据","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"分周","id":6958665,"subComponentList":null,"value":null,"properties":null,"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"month","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"支持分月数据","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"分月","id":6958666,"subComponentList":null,"value":null,"properties":null,"status":1}],"value":null,"properties":{"defaultValue":"day","paramsScope":"common"},"status":1},{"subComponentFilterCodeList":null,"webType":"cascade-checkbox","code":"bizCodeIn","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"营销场景","id":6958667,"subComponentList":[{"subComponentFilterCodeList":null,"webType":null,"code":"onebpCustomerMarketing","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"消费者运营","id":6968714,"subComponentList":[{"subComponentFilterCodeList":null,"webType":null,"code":"adStrategyDkx","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"拉新快","id":6968715,"subComponentList":null,"value":null,"properties":null,"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"adStrategyFans","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"粉丝快","id":7340039,"subComponentList":null,"value":null,"properties":null,"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"adStrategyRuHui","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"会员快","id":6968716,"subComponentList":null,"value":null,"properties":null,"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"adStrategyCrowd","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"人群击穿","id":7812114,"subComponentList":null,"value":null,"properties":null,"status":1}],"value":null,"properties":null,"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"onebpItemMarketing","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"货品运营","id":6968717,"subComponentList":[{"subComponentFilterCodeList":null,"webType":null,"code":"adStrategyShangXin","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"上新快","id":6968718,"subComponentList":null,"value":null,"properties":null,"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"adStrategyProductSpeed","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"货品加速","id":6968719,"subComponentList":null,"value":null,"properties":null,"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"adStrategyCeKuan","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"测款快","id":6968720,"subComponentList":null,"value":null,"properties":null,"status":1}],"value":null,"properties":null,"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"adStrategyYuRe","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"活动加速","id":6968721,"subComponentList":null,"value":null,"properties":null,"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"onebpShopMarketing","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"店铺运营","id":7378002,"subComponentList":[{"subComponentFilterCodeList":null,"webType":null,"code":"adStrategyWholeShop","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"全店智投","id":7382001,"subComponentList":null,"value":null,"properties":null,"status":1}],"value":null,"properties":null,"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"onebpSearch","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"自定义-关键词推广","id":6968722,"subComponentList":null,"value":null,"properties":null,"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"onebpDisplay","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"自定义-人群推广","id":6968723,"subComponentList":null,"value":null,"properties":null,"status":1}],"value":null,"properties":{"paramsScope":"commonMore","paramsGroupName":"按场景"},"status":1},{"subComponentFilterCodeList":null,"webType":"dropdown-checkbox","code":"strategyPromotionModeIn","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"推广模式","id":7368003,"subComponentList":[{"subComponentFilterCodeList":null,"webType":null,"code":"daily","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"持续推广","id":7368004,"subComponentList":null,"value":null,"properties":null,"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"order","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"套餐包","id":7368005,"subComponentList":null,"value":null,"properties":null,"status":1}],"value":null,"properties":{"paramsScope":"commonMore","paramsGroupName":"按场景"},"status":1}],"value":null,"properties":null,"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"template","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"报表模版","id":7068055,"subComponentList":[{"subComponentFilterCodeList":null,"webType":null,"code":"allCampaign","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"Block","webProperties":null,"filterExpression":null,"name":"全部计划","id":7204026,"subComponentList":null,"value":null,"properties":{"templateValue":"{\"havingList\":[],\"unifyType\":\"zhai\",\"effectEqual\":15,\"splitType\":\"day\",\"dateDefault\":7}"},"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"subway","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"Block","webProperties":null,"filterExpression":null,"name":"关键词","id":7112079,"subComponentList":null,"value":null,"properties":{"templateValue":"{\"havingList\":[],\"unifyType\":\"zhai\",\"effectEqual\":15,\"splitType\":\"day\",\"bizCodeIn\":[\"onebpSearch\"],\"dateDefault\":7}"},"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"yinli","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"Block","webProperties":null,"filterExpression":null,"name":"人群推广","id":7112080,"subComponentList":null,"value":null,"properties":{"templateValue":"{\"havingList\":[],\"unifyType\":\"zhai\",\"effectEqual\":15,\"splitType\":\"day\",\"bizCodeIn\":[\"onebpDisplay\"],\"dateDefault\":30}"},"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"wxt","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"Block","webProperties":null,"filterExpression":null,"name":"场景推广","id":7112081,"subComponentList":null,"value":null,"properties":{"templateValue":"{\"havingList\":[],\"unifyType\":\"zhai\",\"effectEqual\":15,\"splitType\":\"day\",\"bizCodeIn\":[\"adStrategyDkx\",\"adStrategyRuHui\",\"adStrategyWholeShop\",\"adStrategyShangXin\",\"adStrategyProductSpeed\",\"adStrategyCeKuan\",\"adStrategyYuRe\",\"adStrategyFans\",\"wxtAgencyAI\",\"wxtAgencySmart\",\"adStrategyCrowd\"],\"dateDefault\":30}"},"status":1}],"value":null,"properties":null,"status":1}],"value":null,"properties":null,"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"chargeCard","defaultValue":null,"bizCode":null,"assistant":null,"rule":"com.alibaba.ad.adplus.site.domain.domainobject.component.upgrade.UpgradeComponentRule@310bea51","description":"花费数据汇总板块暂不支持「持续推广」和「套餐包」的数据筛选","filters":null,"sort":null,"type":"Block","webProperties":null,"filterExpression":null,"name":"花费数据汇总","id":7530034,"subComponentList":[{"subComponentFilterCodeList":null,"webType":null,"code":"fieldList","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"Block","webProperties":null,"filterExpression":null,"name":"字段列表","id":7530035,"subComponentList":[{"subComponentFilterCodeList":null,"webType":null,"code":"crowdSceneCharge","defaultValue":null,"bizCode":null,"assistant":null,"rule":"com.alibaba.ad.adplus.site.domain.domainobject.component.upgrade.UpgradeComponentRule@26fb3814","description":"","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"消费者运营花费","id":7530037,"subComponentList":[{"subComponentFilterCodeList":null,"webType":null,"code":"xinXiangGiftCharge","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"含确超新计划","id":7530038,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"基础指标","default":"false","unit":"元","formater":"formatFloat","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","defOrderBy":"","disableSort":"false"},"status":1}],"value":null,"properties":{"disableHaving":"false","parent":"基础指标","default":"true","unit":"元","formater":"formatFloat","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","defOrderBy":"","disableSort":"false"},"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"itemSceneCharge","defaultValue":null,"bizCode":null,"assistant":null,"rule":"com.alibaba.ad.adplus.site.domain.domainobject.component.upgrade.UpgradeComponentRule@19265793","description":"","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"货品运营花费","id":7530039,"subComponentList":[{"subComponentFilterCodeList":null,"webType":null,"code":"ybqGiftCharge","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"含新品一笔钱","id":7530040,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"基础指标","default":"false","unit":"元","formater":"formatFloat","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","disableSort":"false"},"status":1}],"value":null,"properties":{"disableHaving":"false","parent":"基础指标","default":"true","unit":"元","formater":"formatFloat","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","disableSort":"false"},"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"activitySceneCharge","defaultValue":null,"bizCode":null,"assistant":null,"rule":"com.alibaba.ad.adplus.site.domain.domainobject.component.upgrade.UpgradeComponentRule@3759b073","description":"","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"活动运营花费","id":7530041,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"基础指标","default":"true","unit":"元","formater":"formatFloat","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","disableSort":"false"},"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"displayCharge","defaultValue":null,"bizCode":null,"assistant":null,"rule":"com.alibaba.ad.adplus.site.domain.domainobject.component.upgrade.UpgradeComponentRule@49ca074c","description":"","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"人群场景花费","id":7530042,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"基础指标","default":"true","unit":"元","formater":"formatFloat","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","disableSort":"false"},"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"searchCharge","defaultValue":null,"bizCode":null,"assistant":null,"rule":"com.alibaba.ad.adplus.site.domain.domainobject.component.upgrade.UpgradeComponentRule@113cb164","description":"","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"关键词花费","id":7530043,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"基础指标","default":"true","unit":"元","formater":"formatFloat","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","defOrderBy":"","disableSort":"false"},"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"contentSceneCharge","defaultValue":null,"bizCode":null,"assistant":null,"rule":"com.alibaba.ad.adplus.site.domain.domainobject.component.upgrade.UpgradeComponentRule@12bd0ccc","description":"","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"内容场景花费","id":7530044,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"基础指标","default":"true","unit":"元","formater":"formatFloat","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","disableSort":"false"},"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"shopSceneCharge","defaultValue":null,"bizCode":null,"assistant":null,"rule":"com.alibaba.ad.adplus.site.domain.domainobject.component.upgrade.UpgradeComponentRule@116366fd","description":"","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"店铺运营场景花费","id":7530045,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"基础指标","default":"true","unit":"元","formater":"formatFloat","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","disableSort":"false"},"status":1}],"value":null,"properties":{"paramsScope":"common"},"status":1}],"value":null,"properties":null,"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"sumDataCard","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"以下数据不包括「直播、短视频、获客易、拉新快-超新计划、确定性保障套餐包」计划","filters":null,"sort":null,"type":"Block","webProperties":null,"filterExpression":null,"name":"数据汇总","id":6236012,"subComponentList":[{"subComponentFilterCodeList":null,"webType":null,"code":"filterList","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"Block","webProperties":null,"filterExpression":null,"name":"汇总筛选项","id":6246008,"subComponentList":[{"subComponentFilterCodeList":null,"webType":null,"code":"fieldList","defaultValue":null,"bizCode":null,"assistant":null,"rule":"com.alibaba.ad.adplus.site.domain.domainobject.component.upgrade.UpgradeComponentRule@280a124","description":null,"filters":null,"sort":null,"type":"Block","webProperties":null,"filterExpression":null,"name":"字段列表","id":6752016,"subComponentList":[{"subComponentFilterCodeList":null,"webType":null,"code":"adPv","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"推广内容被用户看到的次数（去除因用户快速划过、主图未完全展现等情况产生的无效曝光）","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"展现量","id":6818126,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"基础指标","default":"true","unit":"","formater":"formatInt","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","defOrderBy":"","disableSort":"false"},"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"click","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"推广内容被用户点击的次数。说明：虚假点击会被反作弊体系过滤，该数据为反作弊过滤后的数据。","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"点击量","id":6872489,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"基础指标","default":"true","unit":"","formater":"formatInt","is_uv_field":"0","disabled":"false","tag":"","disableSort":"false"},"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"charge","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"推广内容被用户点击或发生其他约定计费的行为所花费的金额。说明：如存在报表和实际结算数据不一致，请以财务记录中实际扣款为准。","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"花费","id":6818154,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"基础指标","default":"true","unit":"元","formater":"formatFloat","is_uv_field":"0","disabled":"false","tag":"","disableSort":"false"},"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"ctr","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"点击率 = 点击量 / 展现量，可直观表示推广主体的吸引程度，点击率越高，说明推广主体对用户的吸引力越大。","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"点击率","id":6824032,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"基础指标","default":"true","unit":"","formater":"formatPer","is_uv_field":"0","disabled":"false","tag":"","disableSort":"false"},"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"ecpc","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"平均点击花费 = 花费 / 点击量，即每一次点击产生的平均花费金额。","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"平均点击花费","id":6800058,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"基础指标","default":"true","unit":"元","formater":"formatFloat","is_uv_field":"0","disabled":"false","tag":"","disableSort":"false"},"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"ecpm","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"千次展现花费 = 花费 / 展现量*1000，即每千次展现产生的平均花费金额，用于评估推广主体的展现成本。","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"千次展现花费","id":6830013,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"基础指标","default":"false","unit":"元","formater":"formatFloat","is_uv_field":"0","disabled":"false","tag":"","disableSort":"false"},"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"prepayInshopAmt","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"总预售成交金额 = 直接预售成交金额 + 间接预售成交金额","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"总预售成交金额","id":6866057,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"成交意向","default":"false","unit":"元","formater":"formatFloat","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","defOrderBy":"","disableSort":"false"},"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"prepayInshopNum","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"总预售成交笔数 = 直接预售成交笔数 + 间接预售成交笔数","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"总预售成交笔数","id":6830090,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"成交意向","default":"false","unit":"","formater":"formatInt","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","defOrderBy":"","disableSort":"false"},"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"prepayDirAmt","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"用户直接在该预售商品的详情页面拍下并支付的成交金额（含定金、尾款及运费）","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"直接预售成交金额","id":6808036,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"成交意向","default":"false","unit":"元","formater":"formatFloat","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","defOrderBy":"","disableSort":"false"},"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"prepayDirNum","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"用户直接在该预售商品详情页面拍下并支付的成交笔数","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"直接预售成交笔数","id":6810009,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"成交意向","default":"false","unit":"","formater":"formatInt","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","defOrderBy":"","disableSort":"false"},"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"prepayIndirAmt","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"用户通过推广内容跳转至店铺内其他预售商品详情页面拍下并支付的成交金额（含定金、尾款及运费）","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"间接预售成交金额","id":6824033,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"成交意向","default":"false","unit":"元","formater":"formatFloat","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","defOrderBy":"","disableSort":"false"},"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"prepayIndirNum","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"用户通过推广内容跳转至店铺内其他预售商品详情页面拍下并支付的成交笔数","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"间接预售成交笔数","id":6804504,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"成交意向","default":"false","unit":"","formater":"formatInt","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","defOrderBy":"","disableSort":"false"},"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"alipayDirAmt","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"推广内容引导用户直接在推广宝贝的详情页面拍下并通过支付宝交易的成交金额","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"直接成交金额","id":6818155,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"成交转化","default":"false","unit":"元","formater":"formatFloat","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","defOrderBy":"","disableSort":"false"},"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"alipayIndirAmt","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"推广内容引导用户通过跳转至店铺内其他宝贝的详情页面拍下并通过支付宝交易的成交金额（含运费）","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"间接成交金额","id":6820189,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"成交转化","default":"false","unit":"元","formater":"formatFloat","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","defOrderBy":"","disableSort":"false"},"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"alipayInshopAmt","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"总成交金额 = 直接成交金额 + 间接成交金额，即推广内容引导用户产生的所有支付宝交易的总成交金额","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"总成交金额","id":6848016,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"成交转化","default":"true","unit":"元","formater":"formatFloat","is_uv_field":"0","disabled":"false","tag":"","disableSort":"false"},"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"alipayInshopNum","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"总成交笔数 = 直接成交笔数 + 间接成交笔数，即推广内容引导用户产生的所有支付宝交易的总成交笔数","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"总成交笔数","id":6830014,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"成交转化","default":"true","unit":"","formater":"formatInt","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","disableSort":"false"},"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"alipayDirNum","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"推广内容引导用户直接在推广宝贝的详情页面拍下并通过支付宝交易的成交笔数","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"直接成交笔数","id":6860009,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"成交转化","default":"false","unit":"","formater":"formatInt","is_uv_field":"0","disabled":"false","tag":"","disableSort":"false"},"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"alipayIndirNum","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"推广内容引导用户通过跳转至店铺内其他宝贝的详情页面拍下并通过支付宝交易的成交笔数","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"间接成交笔数","id":6836015,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"成交转化","default":"false","unit":"","formater":"formatInt","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","defOrderBy":"","disableSort":"false"},"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"cvr","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"非内容推广：点击转化率 = 总成交笔数 / 点击量","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"点击转化率","id":6852024,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"成交转化","default":"true","unit":"","formater":"formatPer","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","defOrderBy":"","disableSort":"false"},"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"roi","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"投入产出比 = 总成交金额 / 花费，反映花费在所选转化周期内带来支付宝成交金额的比例","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"投入产出比","id":6848017,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"成交转化","default":"false","unit":"","formater":"formatFloat","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","disableSort":"false"},"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"alipayInshopCost","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"总成交成本 = 花费 / 总成交笔数","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"总成交成本","id":6842007,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"成交转化","default":"false","unit":"元","formater":"formatFloat","is_uv_field":"0","disabled":"false","tag":"","disableSort":"false"},"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"cartInshopNum","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"总购物车数 = 直接购物车数 + 间接购物车数，即推广内容引导用户产生加入购物车行为的总次数。（当天实时数据包含预售支付定金数据，次日剔除）","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"总购物车数","id":6810010,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"潜在转化","default":"true","unit":"","formater":"formatInt","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","defOrderBy":"","disableSort":"false"},"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"cartDirNum","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"用户对该推广宝贝加入购物车的次数（当天实时数据包含直接预售支付定金数据，次日剔除）","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"直接购物车数","id":6860010,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"潜在转化","default":"false","unit":"","formater":"formatInt","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","defOrderBy":"","disableSort":"false"},"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"cartIndirNum","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"推广内容引导用户跳转至店铺其他宝贝详情页后发生宝贝加入购物车的次数（当天实时数据包含间接预售支付定金数据，次日剔除）","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"间接购物车数","id":6794408,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"潜在转化","default":"false","unit":"","formater":"formatInt","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","defOrderBy":"","disableSort":"false"},"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"cartRate","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"加购率 = 总购物车数 / 点击量","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"加购率","id":6806453,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"潜在转化","default":"false","unit":"","formater":"formatPer","is_uv_field":"0","disabled":"false","tag":"","disableSort":"false"},"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"itemColInshopNum","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"推广内容引导用户收藏宝贝的次数","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"收藏宝贝数","id":6832174,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"潜在转化","default":"true","unit":"","formater":"formatInt","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","disableSort":"false"},"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"shopColDirNum","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"推广内容引导用户收藏本店铺的次数","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"收藏店铺数","id":6862125,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"潜在转化","default":"true","unit":"","formater":"formatInt","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","disableSort":"false"},"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"shopColInshopCost","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"店铺收藏成本 = 花费 / 收藏店铺数","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"店铺收藏成本","id":6870007,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"潜在转化","default":"false","unit":"元","formater":"formatFloat","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","defOrderBy":"","disableSort":"false"},"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"colCartNum","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"总收藏加购数 = 总收藏数 + 总购物车数","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"总收藏加购数","id":6800059,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"潜在转化","default":"false","unit":"","formater":"formatInt","is_uv_field":"0","disabled":"false","tag":"","disableSort":"false"},"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"colCartCost","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"总收藏加购成本 = 花费 / 总收藏加购数","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"总收藏加购成本","id":6800060,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"潜在转化","default":"false","unit":"元","formater":"formatFloat","is_uv_field":"0","disabled":"false","tag":"","disableSort":"false"},"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"itemColCart","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"宝贝收藏加购数 = 宝贝收藏数 + 总购物车数","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"宝贝收藏加购数","id":6842008,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"潜在转化","default":"false","unit":"","formater":"formatInt","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","disableSort":"false"},"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"itemColCartCost","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"宝贝收藏加购成本 = 花费 / 宝贝收藏加购数","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"宝贝收藏加购成本","id":6860012,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"潜在转化","default":"false","unit":"元","formater":"formatFloat","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","disableSort":"false"},"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"colNum","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"总收藏数 = 总收藏宝贝数+收藏店铺数，即推广内容引导用户收藏宝贝和店铺的总次数","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"总收藏数","id":6872491,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"潜在转化","default":"true","unit":"","formater":"formatInt","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","defOrderBy":"","disableSort":"false"},"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"itemColInshopCost","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"宝贝收藏成本 = 花费 / 收藏宝贝数","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"宝贝收藏成本","id":6804506,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"潜在转化","default":"true","unit":"元","formater":"formatFloat","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","disableSort":"false"},"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"itemColInshopRate","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"宝贝收藏率 = 收藏宝贝数 / 点击量","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"宝贝收藏率","id":6856031,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"潜在转化","default":"false","unit":"","formater":"formatPer","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","disableSort":"false"},"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"cartCost","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"加购成本 = 花费 / 总购物车数","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"加购成本","id":6836016,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"潜在转化","default":"false","unit":"元","formater":"formatFloat","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","disableSort":"false"},"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"gmvInshopNum","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"用户拍下订单总数（含未付款）","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"拍下订单笔数","id":6846004,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"成交意向","default":"false","unit":"","formater":"formatInt","is_uv_field":"0","disabled":"false","tag":"","disableSort":"false"},"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"gmvInshopAmt","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"用户拍下订单总数（含未付款）","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"拍下订单金额","id":6820180,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"成交意向","default":"false","unit":"元","formater":"formatFloat","is_uv_field":"0","disabled":"false","tag":"","disableSort":"false"},"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"itemColDirNum","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"用户收藏该推广宝贝的次数","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"直接收藏宝贝数","id":6820181,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"潜在转化","default":"false","unit":"","formater":"formatInt","is_uv_field":"0","disabled":"false","tag":"","disableSort":"false"},"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"itemColIndirNum","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"推广内容引导用户跳转至店铺其他宝贝详情页后发生宝贝收藏行为的次数","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"间接收藏宝贝数","id":6810012,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"潜在转化","default":"false","unit":"","formater":"formatInt","is_uv_field":"0","disabled":"false","tag":"","disableSort":"false"},"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"couponShopNum","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"用户领取优惠券的总数量（包含店铺和宝贝优惠券）","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"优惠券领取量","id":6830015,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"潜在转化","default":"false","unit":"","formater":"formatInt","is_uv_field":"0","disabled":"false","tag":"","disableSort":"false"},"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"shoppingNum","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"推广内容引导充值店铺购物金的笔数","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"购物金充值笔数","id":6824034,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"潜在转化","default":"false","unit":"","formater":"formatInt","is_uv_field":"0","disabled":"false","tag":"","disableSort":"false"},"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"shoppingAmt","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"推广内容引导充值店铺购物金的金额","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"购物金充值金额","id":6844199,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"潜在转化","default":"false","unit":"元","formater":"formatFloat","is_uv_field":"0","disabled":"false","tag":"","disableSort":"false"},"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"wwNum","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"推广引导的用户通过旺旺发起的咨询量","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"旺旺咨询量","id":6858009,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"线索信息","default":"false","unit":"","formater":"formatInt","is_uv_field":"0","disabled":"false","tag":"","disableSort":"false"},"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"inshopPv","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"用户访问创意落地页、店铺首页及宝贝详情页等，总浏览页面数量","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"引导访问量","id":6860013,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"引导访问","default":"false","unit":"","formater":"formatInt","is_uv_field":"0","disabled":"false","tag":"","disableSort":"false"},"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"inshopUv","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"用户访问创意落地页、店铺首页及宝贝详情页等页面的总浏览人数","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"引导访问人数","id":6830016,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"引导访问","default":"false","unit":"","formater":"formatInt","is_uv_field":"1","disabled":"false","tag":"","disableSort":"false"},"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"inshopPotentialUv","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"用户访问创意落地页、店铺首页及宝贝详情页等页面的总浏览潜客人数。 潜客定义：15天内无推广点击内容渠道（如淘宝头条、微淘）浏览互动/进店/搜索/点击行为；且90天内无收藏/加购店铺/宝贝行为；且365天内无店铺下单/购买行为的用户","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"引导访问潜客数","id":6866058,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"引导访问","default":"false","unit":"","formater":"formatInt","is_uv_field":"1","disabled":"false","tag":"","disableSort":"false"},"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"inshopPotentialUvRate","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"引导访问潜客占⽐ = 引导访问潜客人数 / 引导访问人数","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"引导访问潜客占比","id":6790195,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"引导访问","default":"false","unit":"","formater":"formatPer","is_uv_field":"1","disabled":"false","tag":"","disableSort":"false"},"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"rhRate","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"入会率 = 入会量 / 点击量","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"入会率","id":6842010,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"会员获取","default":"false","unit":"","formater":"formatPer","is_uv_field":"0","disabled":"false","tag":"","disableSort":"false"},"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"rhNum","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"推广内容引导用户成为会员的人次","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"入会量","id":6804507,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"会员获取","default":"false","unit":"","formater":"formatInt","is_uv_field":"0","disabled":"false","tag":"","disableSort":"false"},"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"inshopPvRate","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"引导访问率 = 引导访问量 / 展现量","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"引导访问率","id":6828116,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"引导访问","default":"false","unit":"","formater":"formatPer","is_uv_field":"1","disabled":"false","tag":"","disableSort":"false"},"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"deepInshopPv","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"用户访问创意落地页、店铺首页及宝贝详情页等，浏览页面数量>1的访问次数","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"深度访问量","id":6854009,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"引导访问","default":"false","unit":"","formater":"formatInt","is_uv_field":"0","disabled":"false","tag":"","disableSort":"false"},"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"avgAccessPageNum","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"平均访问页面数 = 引导访问量 / 引导访问人数","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"平均访问页面数","id":6806454,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"引导访问","default":"false","unit":"","formater":"formatInt","is_uv_field":"0","disabled":"false","tag":"","disableSort":"false"},"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"newAlipayInshopUv","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"引导成交的用户中新成交的用户数量（365天无成交），人数去重","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"成交新客数","id":6806455,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"人群效果","default":"false","unit":"","formater":"formatInt","is_uv_field":"1","disabled":"false","tag":"","disableSort":"false"},"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"newAlipayInshopUvRate","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"成交新客占比 = 成交新客数 / 成交用户数","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"成交新客占比","id":6856032,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"人群效果","default":"false","unit":"","formater":"formatPer","is_uv_field":"1","disabled":"false","tag":"","disableSort":"false"},"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"hySgUv","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"店铺未购买会员（365天内店铺维度未购）首次成交的人数，人数去重","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"会员首购人数","id":6816237,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"会员获取","default":"false","unit":"","formater":"formatInt","is_uv_field":"1","disabled":"false","tag":"","disableSort":"false"},"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"hyPayAmt","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"推广内容引导店铺会员成交的订单金额。预售订单付尾款前不会出现在成交金额中","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"会员成交金额","id":6820185,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"会员获取","default":"false","unit":"","formater":"formatFloat","is_uv_field":"0","disabled":"false","tag":"","disableSort":"false"},"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"hyPayNum","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"推广内容引导店铺会员成交的订单笔数","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"会员成交笔数","id":6846005,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"会员获取","default":"false","unit":"","formater":"formatInt","is_uv_field":"0","disabled":"false","tag":"","disableSort":"false"},"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"alipayInshopUv","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"推广内容引导通过支付宝交易并支付成功的用户人数。人数去重","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"成交人数","id":6852029,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"成交转化","default":"false","unit":"","formater":"formatInt","is_uv_field":"1","disabled":"false","tag":"","caseFillField":"","defOrderBy":"","disableSort":"false"},"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"alipayInshopNumAvg","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"人均成交笔数 = 总成交笔数 / 成交人数。人数去重","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"人均成交笔数","id":6868056,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"成交转化","default":"false","unit":"","formater":"formatInt","is_uv_field":"1","disabled":"false","tag":"","disableSort":"false"},"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"alipayInshopAmtAvg","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"人均成交金额 = 总成交金额 / 成交人数。人数去重","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"人均成交金额","id":6808091,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"成交转化","default":"false","unit":"","formater":"formatFloat","is_uv_field":"1","disabled":"false","tag":"","disableSort":"false"},"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"naturalPayAmt","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"以推广拉动的自然流量曝光数据为基础，根据自然流量的平均点击转化率及笔单价，计算得出带来的转化金额数据。这是由推广拉动的自然流量带来的转化增益。注意：不支持细分至定向/资源位/创意维度","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"自然流量转化金额","id":7274260,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"广义效果","default":"false","unit":"","formater":"formatFloat","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","disableSort":"false"},"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"orgNaturalPv","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":"推广内容拉动了宝贝或店铺在手淘站内推荐渠道和搜索渠道中的自然流量，从而带来的曝光量。注意：不支持细分至定向/资源位/创意维度","filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"自然流量曝光量","id":7320552,"subComponentList":null,"value":null,"properties":{"disableHaving":"false","parent":"广义效果","default":"false","unit":"","formater":"formatInt","is_uv_field":"0","disabled":"false","tag":"","caseFillField":"","disableSort":"false"},"status":1}],"value":null,"properties":{"paramsScope":"common"},"status":1},{"subComponentFilterCodeList":null,"webType":"dropdown","code":"vsType","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"周期对比","id":6698153,"subComponentList":[{"subComponentFilterCodeList":null,"webType":null,"code":"off","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"关闭","id":6742024,"subComponentList":null,"value":null,"properties":null,"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"day","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"日环比","id":6698155,"subComponentList":null,"value":null,"properties":null,"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"week","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"周环比","id":6698156,"subComponentList":null,"value":null,"properties":null,"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"month","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"月环比","id":6698157,"subComponentList":null,"value":null,"properties":null,"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"halfYear","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"半年环比","id":6764449,"subComponentList":null,"value":null,"properties":null,"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"custom","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"BizComponent","webProperties":null,"filterExpression":null,"name":"自定义日期","id":6758018,"subComponentList":null,"value":null,"properties":null,"status":1}],"value":null,"properties":{"defaultValue":"week","paramsScope":"sum"},"status":1},{"subComponentFilterCodeList":null,"webType":"date","code":"vsTime","defaultValue":null,"bizCode":null,"assistant":null,"rule":"com.alibaba.ad.adplus.site.domain.domainobject.component.upgrade.UpgradeComponentRule@2f3b8b6f","description":null,"filters":null,"sort":null,"type":"Block","webProperties":null,"filterExpression":null,"name":"报表日期","id":6734037,"subComponentList":null,"value":null,"properties":{"default":1,"min":180,"max":1,"paramsScope":"sum"},"status":1}],"value":null,"properties":null,"status":1}],"value":null,"properties":null,"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"listDataCard","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"Block","webProperties":null,"filterExpression":null,"name":"账户数据明细","id":6254012,"subComponentList":[{"subComponentFilterCodeList":null,"webType":null,"code":"filterList","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"Block","webProperties":null,"filterExpression":null,"name":"列表筛选项","id":6254015,"subComponentList":[{"subComponentFilterCodeList":null,"webType":"dropdown-checkbox","code":"queryDomains","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"Block","webProperties":null,"filterExpression":null,"name":"维度","id":6746021,"subComponentList":[{"subComponentFilterCodeList":null,"webType":null,"code":"date","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"Block","webProperties":null,"filterExpression":null,"name":"时间","id":6936015,"subComponentList":null,"value":null,"properties":{"width":120},"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"scene","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"Block","webProperties":null,"filterExpression":null,"name":"营销场景","id":6958645,"subComponentList":null,"value":null,"properties":null,"status":1}],"value":null,"properties":{"defaultValue":[],"paramsScope":"list"},"status":1}],"value":null,"properties":null,"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"columnList","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"Block","webProperties":null,"filterExpression":null,"name":"列表维度列","id":6966283,"subComponentList":[{"subComponentFilterCodeList":null,"webType":null,"code":"thedate","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"Block","webProperties":null,"filterExpression":null,"name":"时间","id":6966284,"subComponentList":null,"value":null,"properties":{"queryDomain":"date","width":120},"status":1},{"subComponentFilterCodeList":null,"webType":null,"code":"scene","defaultValue":null,"bizCode":null,"assistant":null,"rule":null,"description":null,"filters":null,"sort":null,"type":"Block","webProperties":null,"filterExpression":null,"name":"营销场景","id":6996004,"subComponentList":null,"value":null,"properties":{"queryDomain":"scene","width":96},"status":1}],"value":null,"properties":null,"status":1},{"subComponentFilterCodeList":null,"webType":"daterange","code":"download_date","defaultValue":null,"bizCode":null,"assistant":null,"rule":"com.alibaba.ad.adplus.site.domain.domainobject.component.upgrade.UpgradeComponentRule@4c8df11e","description":null,"filters":null,"sort":null,"type":"Block","webProperties":null,"filterExpression":null,"name":"下载报表日期","id":6806828,"subComponentList":null,"value":null,"properties":{"min":360,"max-gap":180,"max":1,"shortKeys":"yesterday,passed7,preWeekMon,passed15,passedThisMonth,passed30,preMonth","paramsScope":"common"},"status":1}],"value":null,"properties":null,"status":1}],"value":null,"properties":null,"status":1}]},"info":{"lockSla":false,"redirectUrl":null,"errorEntityIdList":null,"httpStatus":null,"errorCode":null,"unlockUrl":null,"ok":true,"message":null,"disableTime":false}}
                return new BiAuthResult(BiAuthResult.NO_AUTH, "");
            }
        } catch (Exception e) {
            SpiderLog.addTime("aiztone_AiztOneHttpClient.dmbzt", false);
            return new BiAuthResult(BiAuthResult.EXCEPTION, e.getMessage());
        } finally {
            httpGet.releaseConnection();
        }
    }

    /**
     * 全站推广权限
     *
     * @param cjzh
     * @return
     */
    public static BiAuthResult qztg(Cjzh cjzh) {
        CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cjzh.getCookieStore()).build();
        HttpPost httpGet = new HttpPost("https://one.alimama.com/component/findMenuList.json?bizCode=universalBP&csrfId=" + cjzh.getData("adbrain_one_csrfId"));
        httpGet.setHeader("accept", "application/json, text/javascript, */*; q=0.01");
        httpGet.setHeader("accept-encoding", "gzip, deflate, br");
        httpGet.setHeader("accept-language", "zh-CN,zh;q=0.9");
        httpGet.setHeader("bx-v", "2.5.1");
        httpGet.setHeader("content-type", "application/json");
        httpGet.setHeader("origin", "https://one.alimama.com");
        httpGet.setHeader("referer", "https://one.alimama.com/index.html");
        httpGet.setHeader("sec-ch-ua", "\"Google Chrome\";v=\"95\", \"Chromium\";v=\"95\", \";Not A Brand\";v=\"99\"");
        httpGet.setHeader("sec-ch-ua-mobile", "?0");
        httpGet.setHeader("sec-ch-ua-platform", "\"Windows\"");
        httpGet.setHeader("sec-fetch-dest", "empty");
        httpGet.setHeader("sec-fetch-mode", "cors");
        httpGet.setHeader("sec-fetch-site", "same-origin");
        httpGet.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");
        httpGet.setHeader("x-requested-with", "XMLHttpRequest");
        RequestConfig.Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000).setConnectionRequestTimeout(20000);
        httpGet.setConfig(requestConfig.build());
        HttpResponse httpResponse;
        try {
            httpResponse = httpClient.execute(httpGet);
            HttpEntity entity = httpResponse.getEntity();
            String body = EntityUtils.toString(entity, "utf-8");
            if (!StringUtil.isJson2(body)) {
                SpiderLog.addTime("aiztone_AiztOneHttpClient.qztg", false);
                return new BiAuthResult(BiAuthResult.OTHER, body);
            }
            JSONObject jsonObject = JSONObject.parseObject(body);
            if (!jsonObject.containsKey("info") || jsonObject.getJSONObject("info") == null || !jsonObject.getJSONObject("info").containsKey("ok") || !jsonObject.getJSONObject("info").getBoolean("ok") || !jsonObject.containsKey("data")) {
                SpiderLog.addTime("aiztone_AiztOneHttpClient.qztg", false);
                return new BiAuthResult(BiAuthResult.OTHER, body);
            }

            SpiderLog.addTime("aiztone_AiztOneHttpClient.qztg", true);
            JSONObject data = jsonObject.getJSONObject("data");
            if (!data.containsKey("list")) {
                return new BiAuthResult(BiAuthResult.OTHER, body);
            }

            JSONArray list = data.getJSONArray("list");
            if (CollectionUtils.isEmpty(list)) {
                return new BiAuthResult(BiAuthResult.OTHER, body);
            }

            boolean auth = false;
            JSONArray subComponentList = list.getJSONObject(0).getJSONArray("subComponentList");
            out:
            for (int i = 0; i < subComponentList.size(); i++) {
                JSONObject sub = subComponentList.getJSONObject(i);
                if (!sub.getString("code").equals("m_report")) {
                    continue;
                }

                JSONArray m_report_subComponentList = sub.getJSONArray("subComponentList");
                for (int j = 0; j < m_report_subComponentList.size(); j++) {
                    JSONObject m_report_subComponentListJSONObject = m_report_subComponentList.getJSONObject(j);
                    if (m_report_subComponentListJSONObject.getString("code").equals("m_report_case_site")) {
                        auth = true;
                        break out;
                    }
                }
            }
            if (auth) {
                return new BiAuthResult(BiAuthResult.SUCCESS, "");
            }

            return new BiAuthResult(BiAuthResult.NO_AUTH, body);
        } catch (Exception e) {
            SpiderLog.addTime("aiztone_AiztOneHttpClient.qztg", false);
            return new BiAuthResult(BiAuthResult.EXCEPTION, e.getMessage());
        } finally {
            httpGet.releaseConnection();
        }
    }


    public static String qztgDayChargeSum(Cjzh cjzh, String rq) {
        CookieStore newCookieStore = new BasicCookieStore();
        CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(newCookieStore).build();
        HttpPost httpPost = null;
        HttpResponse httpresponse;
        try {
            String url = "https://1bp.taobao.com/report/query.json?lite2=false&csrfId=" + cjzh.getData("adbrain_one_csrfId") + "&bizCode=onebpSite";
            httpPost = new HttpPost(url);
            httpPost.setHeader("accept", "application/json, text/javascript, */*; q=0.01");
            httpPost.setHeader("accept-encoding", "gzip, deflate, br");
            httpPost.setHeader("content-type", "application/json");
            httpPost.setHeader("accept-language", "zh-CN,zh;q=0.9");
            httpPost.setHeader("referer", "https://myseller.taobao.com/");
            httpPost.setHeader("origin", "https://myseller.taobao.com/home.htm/alltaopromotion/");
            // httpPost.setHeader("bx-v", "2.5.3");
            httpPost.setHeader("cookie", cjzh.getCookieStr());
            httpPost.setHeader("content-type", "application/json");
            httpPost.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
            // httpPost.setHeader("x-requested-with", "XMLHttpRequest");

            RequestConfig.Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000).setConnectionRequestTimeout(20000);
            httpPost.setConfig(requestConfig.build());

            String param = "{\"lite2\":false,\"bizCode\":\"onebpSite\",\"startTime\":\"" + rq + "\",\"endTime\":\"" + rq + "\",\"effectEqual\":\"15\",\"unifyType\":\"kuan\",\"sourceList\":[\"scene\"],\"queryFieldIn\":[\"adPv\",\"click\",\"charge\",\"alipayInshopNum\",\"alipayInshopAmt\"],\"splitType\":\"sum\",\"queryDomains\":[],\"csrfId\":\"" + cjzh.getData("adbrain_one_csrfId") + "\"}";
            StringEntity stringEntity = new StringEntity(param);
            httpPost.setEntity(stringEntity);
            httpresponse = httpClient.execute(httpPost);
            HttpEntity entity = httpresponse.getEntity();
            String body = EntityUtils.toString(entity, "utf-8");
            // 2024-12-16，李龙调整
            cjzh.backCookie(newCookieStore);
            if (!StringUtil.isJson2(body)) {
                return null;
            }

            JSONObject jsonObj = JSONObject.parseObject(body);
            if (!jsonObj.containsKey("info") || !jsonObj.getJSONObject("info").getBoolean("ok")) {
                return null;
            }

            if (!jsonObj.containsKey("data")) {
                return "";
            }

            JSONObject data = jsonObj.getJSONObject("data");
            if (!data.containsKey("list")) {
                return "";
            }

            JSONArray jsonArray = data.getJSONArray("list");
            if (jsonArray.isEmpty()) {
                return "";
            }

            return jsonArray.getJSONObject(0).toJSONString();
        } catch (Exception e) {
            LOG.error(cjzh.getZzh() + "QztgHttpClient.dayChargeSum，失败原因：" + e.getMessage(), e);
            return null;
        } finally {
            if (httpPost != null) {
                httpPost.releaseConnection();
            }
        }
    }

    public static int recordsCount(Cjzh cjzh, String type, String start, String end, String splitType) {
        String param = null;
        String bizCode = "universalBP";
        switch (type) {
            case AiztoneConst.TYPE_AIZTONE_ZTC:
                param = "{\"lite2\":false,\"bizCode\":\"universalBP\",\"fromRealTime\":false,\"source\":\"baseReport\",\"byPage\":true,\"totalTag\":true,\"rptType\":\"item_promotion\",\"pageSize\":20,\"havingList\":[],\"endTime\":\"" + end + "\",\"unifyType\":\"zhai\",\"effectEqual\":15,\"startTime\":\"" + start + "\",\"splitType\":\"" + splitType + "\",\"bizCodeIn\":[\"onebpSearch\"],\"subPromotionTypes\":[],\"queryFieldIn\":[\"adPv\",\"click\",\"charge\",\"alipayInshopAmt\",\"alipayInshopNum\"],\"queryDomains\":[\"promotion\"],\"csrfId\":\"" + cjzh.getData("adbrain_one_csrfId") + "\",\"loginPointId\":\"" + cjzh.getData("adbrain_one_loginPointId") + "\"}";
                break;
            case AiztoneConst.TYPE_AIZTONE_YLMF:
                param = "{\"lite2\":false,\"bizCode\":\"universalBP\",\"fromRealTime\":false,\"source\":\"baseReport\",\"byPage\":true,\"totalTag\":true,\"rptType\":\"item_promotion\",\"pageSize\":20,\"havingList\":[],\"endTime\":\"" + end + "\",\"unifyType\":\"zhai\",\"effectEqual\":15,\"startTime\":\"" + start + "\",\"splitType\":\"" + splitType + "\",\"bizCodeIn\":[\"onebpDisplay\"],\"subPromotionTypes\":[],\"queryFieldIn\":[\"adPv\",\"click\",\"charge\", \"alipayInshopAmt\",\"alipayInshopNum\"],\"queryDomains\":[\"promotion\"],\"csrfId\":\"" + cjzh.getData("adbrain_one_csrfId") + "\",\"loginPointId\":\"" + cjzh.getData("adbrain_one_loginPointId") + "\"}";
                break;
            case AiztoneConst.TYPE_AIZTONE_AIZT:
                return 99;
            case AiztoneConst.TYPE_AIZTONE_ZTC_KEYWORD:
                //直通车关键词
                param = "{\"lite2\":false,\"bizCode\":\"universalBP\",\"fromRealTime\":false,\"source\":\"baseReport\",\"byPage\":true,\"totalTag\":true,\"rptType\":\"bidword\",\"pageSize\":20,\"offset\":0,\"havingList\":[],\"endTime\":\"" + end + "\",\"unifyType\":\"zhai\",\"effectEqual\":15,\"startTime\":\"" + start + "\",\"splitType\":\"" + splitType + "\",\"isKeyWordNotContainChase\":\"true\",\"queryFieldIn\":[\"adPv\",\"click\",\"charge\",\"alipayInshopAmt\",\"alipayInshopNum\"],\"queryDomains\":[\"word\"],\"bizCodeIn\":[\"onebpSearch\"],\"csrfId\":\"" + cjzh.getData("adbrain_one_csrfId") + "\",\"loginPointId\":\"" + cjzh.getData("adbrain_one_loginPointId") + "\"}";
                break;
            case AiztoneConst.TYPE_AIZTONE_ZTC_CROWD:
                //直通车人群
                param = "{\"lite2\":false,\"bizCode\":\"universalBP\",\"fromRealTime\":false,\"source\":\"baseReport\",\"byPage\":true,\"totalTag\":true,\"rptType\":\"crowd\",\"pageSize\":20,\"havingList\":[],\"endTime\":\"" + end + "\",\"unifyType\":\"zhai\",\"effectEqual\":15,\"startTime\":\"" + start + "\",\"splitType\":\"" + splitType + "\",\"filterAppendSubwayChannel\":true,\"filterNullCrowdSubwayTag\":true,\"queryFieldIn\":[\"adPv\",\"click\",\"charge\",\"alipayInshopAmt\",\"alipayInshopNum\"],\"queryDomains\":[\"crowd\"],\"csrfId\":\"" + cjzh.getData("adbrain_one_csrfId") + "\",\"loginPointId\":\"" + cjzh.getData("adbrain_one_loginPointId") + "\"}";
                break;
            case AiztoneConst.TYPE_AIZTONE_ZTC_UNIT:
                //直通车人群
                param = "{\"lite2\":false,\"bizCode\":\"universalBP\",\"fromRealTime\":false,\"source\":\"baseReport\",\"byPage\":true,\"totalTag\":true,\"rptType\":\"adgroup\",\"pageSize\":20,\"havingList\":[],\"endTime\":\"" + end + "\",\"unifyType\":\"zhai\",\"effectEqual\":15,\"startTime\":\"" + start + "\",\"splitType\":\"" + splitType + "\",\"bizCodeIn\":[\"onebpSearch\"],\"queryFieldIn\":[\"adPv\",\"click\",\"charge\",\"ctr\",\"ecpc\",\"alipayInshopAmt\",\"alipayInshopNum\",\"cvr\",\"cartInshopNum\",\"itemColInshopNum\",\"shopColDirNum\",\"colNum\",\"itemColInshopCost\"],\"queryDomains\":[\"adgroup\",\"date\",\"campaign\"],\"csrfId\":\"" + cjzh.getData("adbrain_one_csrfId") + "\",\"loginPointId\":\"" + cjzh.getData("adbrain_one_loginPointId") + "\"}";
                break;
            case AiztoneConst.TYPE_AIZTONE_DMBZT:
                param = "{\"lite2\":false,\"bizCode\":\"universalBP\",\"fromRealTime\":false,\"source\":\"baseReport\",\"byPage\":true,\"totalTag\":true,\"rptType\":\"item_promotion\",\"pageSize\":20,\"havingList\":[],\"endTime\":\"" + end + "\",\"unifyType\":\"zhai\",\"effectEqual\":15,\"startTime\":\"" + start + "\",\"splitType\":\"" + splitType + "\",\"bizCodeIn\":[\"onebpMultiAim\"],\"subPromotionTypes\":[],\"queryFieldIn\":[\"adPv\",\"click\",\"charge\",\"alipayInshopAmt\",\"alipayInshopNum\"],\"queryDomains\":[\"promotion\"],\"csrfId\":\"" + cjzh.getData("adbrain_one_csrfId") + "\",\"loginPointId\":\"" + cjzh.getData("adbrain_one_loginPointId") + "\"}";
                break;
            case AiztoneConst.TYPE_AIZTONE_QZTG:
                param = "{\"lite2\":false,\"bizCode\":\"universalBP\",\"fromRealTime\":false,\"source\":\"baseReport\",\"byPage\":true,\"totalTag\":true,\"rptType\":\"item_promotion\",\"pageSize\":20,\"havingList\":[],\"endTime\":\"" + end + "\",\"unifyType\":\"zhai\",\"effectEqual\":15,\"startTime\":\"" + start + "\",\"splitType\":\"" + splitType + "\",\"bizCodeIn\":[\"onebpSite\"],\"subPromotionTypes\":[],\"queryFieldIn\":[\"adPv\",\"click\",\"charge\",\"alipayInshopAmt\",\"alipayInshopNum\"],\"queryDomains\":[\"promotion\"],\"csrfId\":\"" + cjzh.getData("adbrain_one_csrfId") + "\",\"loginPointId\":\"" + cjzh.getData("adbrain_one_loginPointId") + "\"}";
                break;
            case AiztoneConst.TYPE_AIZTONE_XSTG:
                param = "{\"lite2\":false,\"bizCode\":\"onebpAdStrategyLiuZi\",\"fromRealTime\":false,\"source\":\"baseReport\",\"byPage\":true,\"totalTag\":true,\"rptType\":\"liuzi\",\"pageSize\":20,\"havingList\":[],\"endTime\":\"" + end + "\",\"unifyType\":\"zhai\",\"effectEqual\":15,\"startTime\":\"" + start + "\",\"splitType\":\"" + splitType + "\",\"queryFieldIn\":[\"charge\",\"adPv\",\"click\",\"alipayInshopNum\",\"alipayInshopAmt\"],\"queryDomains\":[\"campaign\",\"promotion\"],\"csrfId\":\"" + cjzh.getData("adbrain_one_csrfId") + "\",\"loginPointId\":\"" + cjzh.getData("adbrain_one_loginPointId") + "\"}";
                bizCode = "onebpAdStrategyLiuZi";
                break;
            case AiztoneConst.TYPE_AIZTONE_AIZT_ITEM:
                param = "{\"lite2\":false,\"bizCode\":\"universalBP\",\"fromRealTime\":false,\"source\":\"baseReport\",\"byPage\":true,\"totalTag\":true,\"rptType\":\"item_promotion\",\"pageSize\":20,\"offset\":0,\"havingList\":[],\"endTime\":\"" + end + "\",\"unifyType\":\"zhai\",\"effectEqual\":15,\"startTime\":\"" + start + "\",\"splitType\":\"" + splitType + "\",\"bizCodeIn\":[\"onebpItemMarketing\"],\"subPromotionTypes\":[],\"queryFieldIn\":[\"charge\",\"adPv\",\"click\",\"alipayInshopNum\",\"alipayInshopAmt\"],\"queryDomains\":[\"promotion\",\"date\",\"campaign\"],\"csrfId\":\"" + cjzh.getData("adbrain_one_csrfId") + "\",\"loginPointId\":\"" + cjzh.getData("adbrain_one_loginPointId") + "\"}";
                break;
            case AiztoneConst.TYPE_AIZTONE_AIZT_SHOP:
                param = "{\"lite2\":false,\"bizCode\":\"universalBP\",\"fromRealTime\":false,\"source\":\"baseReport\",\"byPage\":true,\"totalTag\":true,\"rptType\":\"item_promotion\",\"pageSize\":20,\"offset\":0,\"havingList\":[],\"endTime\":\"" + end + "\",\"unifyType\":\"zhai\",\"effectEqual\":15,\"startTime\":\"" + start + "\",\"splitType\":\"" + splitType + "\",\"bizCodeIn\":[\"onebpAdStrategyWholeShop\",\"onebpStarShop\"],\"subPromotionTypes\":[],\"queryFieldIn\":[\"charge\",\"adPv\",\"click\",\"alipayInshopNum\",\"alipayInshopAmt\"],\"queryDomains\":[\"promotion\",\"date\",\"campaign\"],\"csrfId\":\"" + cjzh.getData("adbrain_one_csrfId") + "\",\"loginPointId\":\"" + cjzh.getData("adbrain_one_loginPointId") + "\"}";
                break;
            case AiztoneConst.TYPE_AIZTONE_CYBB:
                param = "{\"lite2\":false,\"bizCode\":\"universalBP\",\"fromRealTime\":false,\"source\":\"baseReport\",\"byPage\":true,\"totalTag\":true,\"rptType\":\"creative\",\"pageSize\":20,\"havingList\":[],\"endTime\":\"" + end + "\",\"unifyType\":\"zhai\",\"effectEqual\":15,\"startTime\":\"" + start + "\",\"splitType\":\"" + splitType + "\",\"queryFieldIn\":[\"adPv\",\"click\",\"charge\",\"ctr\",\"ecpc\",\"alipayInshopAmt\",\"alipayInshopNum\",\"cvr\",\"cartInshopNum\",\"itemColInshopNum\",\"shopColDirNum\",\"colNum\",\"itemColInshopCost\"],\"queryDomains\":[\"creative\",\"promotion\"],\"csrfId\":\"" + cjzh.getData("adbrain_one_csrfId") + "\",\"loginPointId\":\"" + cjzh.getData("adbrain_one_loginPointId") + "\"}";
                break;
            default:
                return -1;
        }

        CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cjzh.getCookieStore()).build();
        HttpPost httpPost = null;
        HttpResponse httpresponse;
        try {
            String url = "https://one.alimama.com/report/query.json?lite2=false&csrfId=" + cjzh.getData("adbrain_one_csrfId") + "&bizCode=" + bizCode;
            httpPost = new HttpPost(url);
            httpPost.setHeader("accept", "application/json, text/javascript, */*; q=0.01");
            httpPost.setHeader("accept-encoding", "gzip, deflate, br");
            httpPost.setHeader("content-type", "application/json");
            httpPost.setHeader("accept-language", "zh-CN,zh;q=0.9");
            httpPost.setHeader("referer", "https://one.alimama.com/index.html");
            httpPost.setHeader("origin", "https://one.alimama.com");
            httpPost.setHeader("bx-v", "2.5.3");
            httpPost.setHeader("content-type", "application/json;charset=UTF-8");
            httpPost.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36");
            httpPost.setHeader("x-requested-with", "XMLHttpRequest");

            RequestConfig.Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000).setConnectionRequestTimeout(20000).setCookieSpec(CookieSpecs.NETSCAPE);
            httpPost.setConfig(requestConfig.build());

            StringEntity stringEntity = new StringEntity(param);
            httpPost.setEntity(stringEntity);
            httpresponse = httpClient.execute(httpPost);
            HttpEntity entity = httpresponse.getEntity();
            String body = EntityUtils.toString(entity, "utf-8");
            if (!StringUtil.isJson2(body)) {
                return -1;
            }

            JSONObject jsonObj = JSONObject.parseObject(body);
            if (!jsonObj.containsKey("info") || !jsonObj.getJSONObject("info").getBoolean("ok")) {
                return -1;
            }

            if (!jsonObj.containsKey("data") || jsonObj.get("data") == null) {
                return 0;
            }

            return jsonObj.getJSONObject("data").getIntValue("count");
        } catch (Exception e) {
            LOG.error(cjzh.getZzh() + "AiztOneHttpClient.recordsCount，失败原因：" + e.getMessage(), e);
            return -1;
        } finally {
            if (httpPost != null) {
                httpPost.releaseConnection();
            }
        }
    }


    /**
     * 获取无界店铺维度-计划花费
     *
     * @param cjzh
     * @param rq
     * @return
     */
    public static String campaignDpData(Cjzh cjzh, String rq) {
        CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cjzh.getCookieStore()).build();
        HttpPost httpPost = null;
        HttpResponse httpresponse;
        try {
            String url = "https://one.alimama.com/report/query.json?lite2=false&csrfId=" + cjzh.getData("adbrain_one_csrfId") + "&bizCode=universalBP";
            httpPost = new HttpPost(url);
            httpPost.setHeader("accept", "application/json, text/javascript, */*; q=0.01");
            httpPost.setHeader("accept-encoding", "gzip, deflate, br");
            httpPost.setHeader("accept-language", "zh-CN,zh;q=0.9");
            httpPost.setHeader("referer", "https://one.alimama.com/index.html");
            httpPost.setHeader("origin", "https://one.alimama.com");
            httpPost.setHeader("bx-v", "2.5.3");
            httpPost.setHeader("content-type", "application/json;charset=UTF-8");
            httpPost.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36");
            httpPost.setHeader("x-requested-with", "XMLHttpRequest");

            Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000).setConnectionRequestTimeout(20000).setCookieSpec(CookieSpecs.NETSCAPE);
            httpPost.setConfig(requestConfig.build());

            String param = "{\"lite2\":false,\"bizCode\":\"universalBP\",\"fromRealTime\":false,\"source\":\"baseReport\",\"endTime\":\"" + rq + "\",\"unifyType\":\"zhai\",\"effectEqual\":15,\"startTime\":\"" + rq + "\",\"splitType\":\"day\",\"bizCodeIn\":[\"onebpAdStrategyWholeShop\",\"onebpStarShop\"],\"queryFieldIn\":[\"adPv\",\"click\",\"charge\",\"roi\",\"alipayInshopAmt\"],\"vsType\":\"off\",\"vsTime\":\"\",\"queryDomains\":[\"account\"],\"rptType\":\"campaign\",\"csrfId\":\"" + cjzh.getData("adbrain_one_csrfId") + "\",\"loginPointId\":\"" + cjzh.getData("adbrain_one_loginPointId") + "\"}";
            StringEntity stringEntity = new StringEntity(param);
            httpPost.setEntity(stringEntity);
            httpresponse = httpClient.execute(httpPost);
            HttpEntity entity = httpresponse.getEntity();
            String body = EntityUtils.toString(entity, "utf-8");
            if (!StringUtil.isJson2(body)) {
                throw new RuntimeException("AiztOneHttpClient.campaignDpData请求失败，返回的不是json格式！" + body);
            }
            JSONObject jsonObj = JSONObject.parseObject(body);
            if (!jsonObj.containsKey("info") || !jsonObj.getJSONObject("info").getBoolean("ok")) {
                throw new RuntimeException("AiztOneHttpClient.campaignDpData请求失败， " + body);
            }

            SpiderLog.addTime("aiztone_AiztOneHttpClient.campaignDpData", true);
            JSONObject data = jsonObj.getJSONObject("data");
            if (!data.containsKey("list") || data.get("list") == null || data.getJSONArray("list").isEmpty()) {
                return "{}";
            }
            JSONArray jsonArray = data.getJSONArray("list");
            return jsonArray.getJSONObject(0).toJSONString();
        } catch (Exception e) {
            SpiderLog.addTime("aiztone_AiztOneHttpClient.campaignDpData", true);
            LOG.error(e.getMessage(), e);
            return null;
        } finally {
            if (httpPost != null) {
                httpPost.releaseConnection();
            }
        }
    }
}
