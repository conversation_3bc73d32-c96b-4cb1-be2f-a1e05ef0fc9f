package com.abujlb.zdcjbi.http;

import com.abujlb.CommonConfig;
import com.abujlb.zdcjbi.bean.JhsBean;
import com.abujlb.zdcjbi.cookie.Cjzh;
import com.abujlb.zdcjbi.log.CjLog;
import com.abujlb.zdcjbi.util.BiDateUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.CookieStore;
import org.apache.http.client.config.CookieSpecs;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.cookie.Cookie;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.apache.log4j.Logger;

import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2023/4/19 14:59
 */
public class JhsHttpClient {

    private static final Logger log = Logger.getLogger(JhsHttpClient.class);
    private static final String TEMPPATH = CommonConfig.getString("tempdir");

    public static List<JhsBean> recordList(Cjzh cjzh, List<String> rqList) {
        List<JhsBean> total = new ArrayList<>();
        int curr = 1;
        final int pageSize = 10;
        out:
        do {
            JSONArray temp = page(cjzh, curr, pageSize);
            if (temp == null) {
                return null;
            }

            boolean breakOut = temp.size() < pageSize;
            Iterator<Object> iterator = temp.iterator();
            while (iterator.hasNext()) {
                Object next = iterator.next();

                JSONObject jsonObject = (JSONObject) next;
                String onlineStartTime = jsonObject.getString("onlineStartTime");
                String onlineEndTime = jsonObject.getString("onlineEndTime");

                //计算日期
                List<String> activityRqs = BiDateUtil.getBetweenDate(onlineStartTime.substring(0, 10), onlineEndTime.substring(0, 10));
                boolean flag = false;
                for (String activityRq : activityRqs) {
                    if (rqList.contains(activityRq)) {
                        flag = true;
                        break;
                    }
                }

                if (!flag) {
                    iterator.remove();
                    if (onlineEndTime.compareTo(rqList.get(0)) < 0) {
                        break out;
                    }
                    continue;
                }

                //去获取商品信息和保底花费
                String bizId = jsonObject.getString("bizId");
                String recordDetailUrl = jsonObject.getString("recordDetailUrl");
                if (recordDetailUrl == null) {
                    continue;
                }
                JSONObject payInfo = null;
                if (recordDetailUrl.contains("itemdetail.htm?juId")) {
                    //单品团
                    payInfo = payInfoItem(cjzh, bizId);
                }
                if (recordDetailUrl.contains("signdetail.htm?signRecordId")) {
                    //品牌团
                    payInfo = payInfo(cjzh, bizId);
                }

                if (payInfo == null) {
                    return null;
                }

                JhsBean jhsBean = new JhsBean();
                jhsBean.setRecord(jsonObject);
                jhsBean.setPayInfo(payInfo);
                jhsBean.setBizId(bizId);
                if (recordDetailUrl.contains("itemdetail.htm?juId")) {
                    //单品团单独有接口获取单个商品
                    String itemid = jhItemDetail(cjzh, bizId);
                    if (StringUtils.isBlank(itemid)) {
                        return null;
                    }

                    jhsBean.setGoodsIdList(Collections.singletonList(itemid));
                } else {
                    //获取商品
                    String downloadId = createDownload(cjzh, bizId);
                    if (StringUtils.isBlank(downloadId)) {
                        return null;
                    }

                    String tempPath = download(cjzh, downloadId, bizId);
                    if (StringUtils.isBlank(tempPath)) {
                        return null;
                    }
                    jhsBean.setGoodsFilepath(tempPath);
                }

                // //计算日期
                jhsBean.setRqList(activityRqs);
                total.add(jhsBean);
            }

            if (breakOut) {
                break;
            }

            curr++;
        } while (true);
        return total;
    }

    private static String jhItemDetail(Cjzh cjzh, String bizId) {
        CjLog.addTime("jhs_http.jhItemDetail");
        HttpGet httpget = null;
        HttpResponse httpresponse;
        try {
            String url = "https://sale.tmall.com/activity/detail/queryComponentData.do?signRecordId=&juId=" + bizId + "&signRecordType=2&activityId=&componentId=itemInfo-Tab&";
            CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cjzh.getCookieStore()).build();
            RequestConfig.Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000).setConnectionRequestTimeout(20000).setCookieSpec(CookieSpecs.NETSCAPE);
            httpget = new HttpGet(url);
            httpget.setHeader("accept", "application/json");
            httpget.setHeader("accept-encoding", "gzip, deflate, br");
            httpget.setHeader("accept-language", "zh-CN,zh;q=0.9,en;q=0.8");
            httpget.setHeader("content-type", "application/x-www-form-urlencoded; charset=UTF-8");
            httpget.setHeader("referer", "https://sale.tmall.com/page/campaign/itemdetail.htm?juId=" + bizId);
            httpget.setHeader("sec-ch-ua", "\"Google Chrome\";v=\"107\", \"Chromium\";v=\"107\", \"Not=A?Brand\";v=\"24\"");
            httpget.setHeader("sec-ch-ua-mobile", "?0");
            httpget.setHeader("sec-ch-ua-platform", "\"Windows\"");
            httpget.setHeader("sec-fetch-dest", "empty");
            httpget.setHeader("sec-fetch-mode", "cors");
            httpget.setHeader("sec-fetch-site", "same-origin");
            httpget.setHeader("user-agent", "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/55.0.2883.75 Safari/537.36");
            httpget.setConfig(requestConfig.build());

            httpresponse = httpClient.execute(httpget);
            HttpEntity entity = httpresponse.getEntity();
            String body = EntityUtils.toString(entity);
            JSONObject jsonObject = JSONObject.parseObject(body);
            if (jsonObject.containsKey("success") && jsonObject.getBooleanValue("success") && jsonObject.containsKey("data") && jsonObject.getJSONObject("data").containsKey("baseInfo")) {
                return jsonObject.getJSONObject("data").getJSONObject("baseInfo").getString("itemId");
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            if (httpget != null) {
                httpget.releaseConnection();
            }
        }
        return null;
    }

    private static JSONObject payInfoItem(Cjzh cjzh, String bizId) {
        CjLog.addTime("jhs_http.payInfoItem");
        HttpGet httpget = null;
        HttpResponse httpresponse;
        try {
            String url = "https://sale.tmall.com/fee/getFeeDetailList.do?type=1&juId=" + bizId + "&page=1&pageSize=20";
            CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cjzh.getCookieStore()).build();
            RequestConfig.Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000).setConnectionRequestTimeout(20000).setCookieSpec(CookieSpecs.NETSCAPE);
            httpget = new HttpGet(url);
            httpget.setHeader("accept", "application/json");
            httpget.setHeader("accept-encoding", "gzip, deflate, br");
            httpget.setHeader("accept-language", "zh-CN,zh;q=0.9,en;q=0.8");
            httpget.setHeader("content-type", "application/x-www-form-urlencoded; charset=UTF-8");
            httpget.setHeader("referer", "https://sale.tmall.com/sale/seller/fee_detail.htm?juId=" + bizId);
            httpget.setHeader("sec-ch-ua", "\"Google Chrome\";v=\"107\", \"Chromium\";v=\"107\", \"Not=A?Brand\";v=\"24\"");
            httpget.setHeader("sec-ch-ua-mobile", "?0");
            httpget.setHeader("sec-ch-ua-platform", "\"Windows\"");
            httpget.setHeader("sec-fetch-dest", "empty");
            httpget.setHeader("sec-fetch-mode", "cors");
            httpget.setHeader("sec-fetch-site", "same-origin");
            httpget.setHeader("user-agent", "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/55.0.2883.75 Safari/537.36");
            httpget.setConfig(requestConfig.build());

            httpresponse = httpClient.execute(httpget);
            HttpEntity entity = httpresponse.getEntity();
            String body = EntityUtils.toString(entity);
            JSONObject jsonObject = JSONObject.parseObject(body);
            if (jsonObject.containsKey("success") && jsonObject.getBooleanValue("success") && jsonObject.containsKey("data") && jsonObject.getJSONObject("data").containsKey("payInfo")) {
                return jsonObject.getJSONObject("data").getJSONObject("payInfo");
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            if (httpget != null) {
                httpget.releaseConnection();
            }
        }
        return null;
    }

    private static JSONArray page(Cjzh cjzh, int page, int pageSize) {
        CjLog.addTime("jhs_http.page");
        HttpGet httpget = null;
        HttpResponse httpresponse;
        try {
            long t = System.currentTimeMillis();
            String url = "https://v-blank.sale.tmall.com/list/feeList/queryListData.do?feeStatus=2&siteId=1&page=" + page + "&pageSize=" + pageSize;
            CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cjzh.getCookieStore()).build();
            RequestConfig.Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000).setConnectionRequestTimeout(20000).setCookieSpec(CookieSpecs.NETSCAPE);
            httpget = new HttpGet(url);
            httpget.setHeader("accept", "application/json");
            httpget.setHeader("accept-encoding", "gzip, deflate, br");
            httpget.setHeader("accept-language", "zh-CN,zh;q=0.9,en;q=0.8");
            httpget.setHeader("bx-v", "2.2.3");
            httpget.setHeader("referer", "https://v-blank.sale.tmall.com/market/fee/activity_list.htm");
            httpget.setHeader("sec-ch-ua", "\"Google Chrome\";v=\"107\", \"Chromium\";v=\"107\", \"Not=A?Brand\";v=\"24\"");
            httpget.setHeader("sec-ch-ua-mobile", "?0");
            httpget.setHeader("sec-ch-ua-platform", "\"Windows\"");
            httpget.setHeader("sec-fetch-dest", "empty");
            httpget.setHeader("sec-fetch-mode", "cors");
            httpget.setHeader("sec-fetch-site", "same-origin");
            httpget.setHeader("user-agent", "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/55.0.2883.75 Safari/537.36");
            httpget.setConfig(requestConfig.build());

            httpresponse = httpClient.execute(httpget);
            HttpEntity entity = httpresponse.getEntity();
            String body = EntityUtils.toString(entity);
            JSONObject jsonObject = JSONObject.parseObject(body);
            if (jsonObject.containsKey("success") && jsonObject.getBooleanValue("success") && jsonObject.containsKey("data") && jsonObject.getJSONObject("data").containsKey("list")) {
                return jsonObject.getJSONObject("data").getJSONArray("list");
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            if (httpget != null) {
                httpget.releaseConnection();
            }
        }
        return null;
    }

    private static JSONObject payInfo(Cjzh cjzh, String signId) {
        CjLog.addTime("jhs_http.payInfo");
        HttpGet httpget = null;
        HttpResponse httpresponse;
        try {
            String url = "https://v-blank-jhs.sale.tmall.com/fee/getFeeDetailList.do?type=1&signId=" + signId + "&page=1&pageSize=20";
            CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cjzh.getCookieStore()).build();
            RequestConfig.Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000).setConnectionRequestTimeout(20000).setCookieSpec(CookieSpecs.NETSCAPE);
            httpget = new HttpGet(url);
            httpget.setHeader("accept", "application/json");
            httpget.setHeader("accept-encoding", "gzip, deflate, br");
            httpget.setHeader("accept-language", "zh-CN,zh;q=0.9,en;q=0.8");
            httpget.setHeader("content-type", "application/x-www-form-urlencoded; charset=UTF-8");
            httpget.setHeader("referer", "https://v-blank-jhs.sale.tmall.com/market/fee/fee_detail.htm?signId=" + signId);
            httpget.setHeader("sec-ch-ua", "\"Google Chrome\";v=\"107\", \"Chromium\";v=\"107\", \"Not=A?Brand\";v=\"24\"");
            httpget.setHeader("sec-ch-ua-mobile", "?0");
            httpget.setHeader("sec-ch-ua-platform", "\"Windows\"");
            httpget.setHeader("sec-fetch-dest", "empty");
            httpget.setHeader("sec-fetch-mode", "cors");
            httpget.setHeader("sec-fetch-site", "same-origin");
            httpget.setHeader("user-agent", "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/55.0.2883.75 Safari/537.36");
            httpget.setConfig(requestConfig.build());

            httpresponse = httpClient.execute(httpget);
            HttpEntity entity = httpresponse.getEntity();
            String body = EntityUtils.toString(entity);
            JSONObject jsonObject = JSONObject.parseObject(body);
            if (jsonObject.containsKey("success") && jsonObject.getBooleanValue("success") && jsonObject.containsKey("data") && jsonObject.getJSONObject("data").containsKey("payInfo")) {
                return jsonObject.getJSONObject("data").getJSONObject("payInfo");
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            if (httpget != null) {
                httpget.releaseConnection();
            }
        }
        return null;
    }

    private static String createDownload(Cjzh cjzh, String signId) {
        CjLog.addTime("jhs_report.create");
        HttpPost httpget = null;
        HttpResponse httpresponse;
        try {
            String url = "https://sale.tmall.com/asyncTask/create.do?processorKey=downloadItemProcessor&domainId=" + signId + "&signRecordId=" + signId;
            CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cjzh.getCookieStore()).build();
            RequestConfig.Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000).setConnectionRequestTimeout(20000).setCookieSpec(CookieSpecs.NETSCAPE);
            httpget = new HttpPost(url);
            httpget.setHeader("accept", "application/json");
            httpget.setHeader("accept-encoding", "gzip, deflate, br");
            httpget.setHeader("accept-language", "zh-CN,zh;q=0.9,en;q=0.8");
            httpget.setHeader("content-type", "application/x-www-form-urlencoded; charset=UTF-8");
            httpget.setHeader("origin", "https://sale.tmall.com");
            httpget.setHeader("referer", "https://sale.tmall.com/page/campaign/signdetail.htm?signRecordId=" + signId);
            httpget.setHeader("sec-ch-ua", "\"Google Chrome\";v=\"107\", \"Chromium\";v=\"107\", \"Not=A?Brand\";v=\"24\"");
            httpget.setHeader("sec-ch-ua-mobile", "?0");
            httpget.setHeader("sec-ch-ua-platform", "\"Windows\"");
            httpget.setHeader("sec-fetch-dest", "empty");
            httpget.setHeader("sec-fetch-mode", "cors");
            httpget.setHeader("sec-fetch-site", "same-origin");
            httpget.setHeader("user-agent", "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/55.0.2883.75 Safari/537.36");
            httpget.setConfig(requestConfig.build());
            List<NameValuePair> list = new ArrayList<>();
            list.add(new BasicNameValuePair("itemStatusList", ""));
            list.add(new BasicNameValuePair("exportType", "1"));
            list.add(new BasicNameValuePair("_tb_token_", getAndRefreshTbToken(cjzh)));
            StringEntity stringEntity = new UrlEncodedFormEntity(list, StandardCharsets.UTF_8);
            httpget.setEntity(stringEntity);
            httpresponse = httpClient.execute(httpget);
            HttpEntity entity = httpresponse.getEntity();
            String body = EntityUtils.toString(entity);
            JSONObject jsonObject = JSONObject.parseObject(body);
            if (jsonObject.containsKey("success") && jsonObject.getBooleanValue("success")) {
                if (jsonObject.containsKey("data")) {
                    return jsonObject.getJSONObject("data").getString("taskId");
                }
            }

        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            if (httpget != null) {
                httpget.releaseConnection();
            }
        }
        return null;
    }


    private static String download(Cjzh cjzh, String downloadId, String signId) {
        CjLog.addTime("jhs_report.download");
        HttpGet httpget = null;
        HttpResponse httpresponse;
        InputStream is = null;
        OutputStream os = null;
        try {
            long t = System.currentTimeMillis();
            CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cjzh.getCookieStore()).build();
            RequestConfig.Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000).setConnectionRequestTimeout(20000).setCookieSpec(CookieSpecs.NETSCAPE);
            httpget = new HttpGet("https://sale.tmall.com/asyncTask/result/" + downloadId);
            httpget.setHeader("accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7");
            httpget.setHeader("accept-encoding", "gzip, deflate, br");
            httpget.setHeader("accept-language", "zh-CN,zh;q=0.9,en;q=0.8");
            httpget.setHeader("referer", "https://sale.tmall.com/page/campaign/signdetail.htm?signRecordId=" + signId);
            httpget.setHeader("sec-ch-ua", "\"Google Chrome\";v=\"107\", \"Chromium\";v=\"107\", \"Not=A?Brand\";v=\"24\"");
            httpget.setHeader("sec-ch-ua-mobile", "?0");
            httpget.setHeader("sec-ch-ua-platform", "\"Windows\"");
            httpget.setHeader("sec-fetch-dest", "document");
            httpget.setHeader("sec-fetch-mode", "navigate");
            httpget.setHeader("sec-fetch-site", "same-origin");
            httpget.setHeader("Sec-Fetch-User", "?1");
            httpget.setHeader("user-agent", "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/55.0.2883.75 Safari/537.36");
            httpget.setConfig(requestConfig.build());

            httpresponse = httpClient.execute(httpget);
            String filename = "jhs_" + cjzh.getUserid() + "_" + System.currentTimeMillis() + "_" + "_temp.xls";
            String result = TEMPPATH + filename;
            HttpEntity entity = httpresponse.getEntity();

            is = entity.getContent();
            os = new FileOutputStream(result);
            byte[] b = new byte[4096];
            int count = 0;
            while ((count = is.read(b)) > 0) {
                os.write(b, 0, count);
            }
            os.flush();
            return result;
        } catch (Exception e) {
            log.error(cjzh.getZzh() + "下载聚划算商品数据失败，当前时间：" + BiDateUtil.getCurrtime(), e);
        } finally {
            try {
                if (os != null) {
                    os.close();
                }
            } catch (Exception e) {
            }
            try {
                if (is != null) {
                    is.close();
                }
            } catch (Exception e) {
            }
            if (httpget != null) {
                httpget.releaseConnection();
            }
        }
        return null;
    }

    private static String getAndRefreshTbToken(Cjzh cjzh) {
        CookieStore cookieStore = cjzh.getCookieStore();
        List<Cookie> cookies = cookieStore.getCookies();
        final List<String> DOMAINS = Arrays.asList("tmall.com", ".tmall.com");
        final String NAME = "_tb_token_";
        for (Cookie cookie : cookies) {
            String cookieName = cookie.getName();
            String cookieDomain = cookie.getDomain();
            if (cookieName.equals(NAME) && DOMAINS.contains(cookieDomain)) {
                return cookie.getValue();
            }
        }
        return null;
    }
}
