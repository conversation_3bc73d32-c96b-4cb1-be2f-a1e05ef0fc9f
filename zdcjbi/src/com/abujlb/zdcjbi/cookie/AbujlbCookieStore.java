package com.abujlb.zdcjbi.cookie;

import com.abujlb.Config;
import com.abujlb.Result;
import com.abujlb.util.AbujlbTrustStrategy;
import com.abujlb.util.CookieString;
import com.abujlb.util.Md5;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import net.sf.json.JSONObject;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.CookieStore;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.util.EntityUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.net.ssl.SSLContext;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;

@Component("abujlbCookieStore")
public class AbujlbCookieStore {
	private static final Logger log = Logger.getLogger(AbujlbCookieStore.class);
	private String SERVER_COOKIE;
	@Autowired
	private Config config;

	@PostConstruct
	public void init() {
		SERVER_COOKIE = config.getString("cookieserver");
	}

	/**
	 * 根据cookieKey获取采集帐号
	 * 
	 * @param cookieKey
	 * @return
	 */
	public Cjzh getCjzhForKey(String cookieKey) {
		Cjzh result = null;
		String url = SERVER_COOKIE + "hqzh_getCjzhByKey.action";
		SSLContext sslContext;
		HttpPost httppost = null;
		try {
			Gson gson = new Gson();
			String stamp = String.valueOf(System.currentTimeMillis());
			String password = Md5.password(cookieKey + stamp);

			sslContext = new SSLContextBuilder().loadTrustMaterial(null, new AbujlbTrustStrategy()).build();

			SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslContext);
			CloseableHttpClient httpClient = HttpClients.custom().setSSLSocketFactory(sslsf).build();

			httppost = new HttpPost(url);
			ArrayList<BasicNameValuePair> pairs = new ArrayList<BasicNameValuePair>();
			pairs.add(new BasicNameValuePair("cookieKey", cookieKey));
			pairs.add(new BasicNameValuePair("stamp", stamp));
			pairs.add(new BasicNameValuePair("password", password));

			HttpResponse httpresponse;

			UrlEncodedFormEntity urlEncodedFormEntity = new UrlEncodedFormEntity(pairs, "utf-8");
			httppost.setEntity(urlEncodedFormEntity);
			httpresponse = httpClient.execute(httppost);
			HttpEntity entity = httpresponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			JSONObject jsonObj = JSONObject.fromObject(body);
			int code = jsonObj.getInt("code");
			if (code == 0 && jsonObj.has("cjzh")) {
				String cjzhJson = jsonObj.getString("cjzh");
				String cookie = jsonObj.getString("cookie");
				result = gson.fromJson(cjzhJson, Cjzh.class);
				if (result != null) {
					CookieStore ck = CookieString.strToCookies(cookie);
					result.setCookieStore(ck);
				}
			}
			return result;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		} finally {
			if (httppost != null) {
				httppost.releaseConnection();
			}
		}
		return result;
	}

	/**
	 * 帐号退出登录，BI帐号当天数据采集完成了之后，将不再保持登录状态，主动退出帐号
	 * 
	 * @param cjzh
	 */
	public boolean logout(Cjzh cjzh) {
		String url = SERVER_COOKIE + "logoutByKey.action";
		SSLContext sslContext;
		HttpPost httppost = null;
		try {
			String stamp = String.valueOf(System.currentTimeMillis());
			String password = Md5.password(cjzh.getCookieKey() + stamp);

			sslContext = new SSLContextBuilder().loadTrustMaterial(null, new AbujlbTrustStrategy()).build();

			SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslContext);
			CloseableHttpClient httpClient = HttpClients.custom().setSSLSocketFactory(sslsf).build();

			httppost = new HttpPost(url);
			ArrayList<BasicNameValuePair> pairs = new ArrayList<BasicNameValuePair>();
			pairs.add(new BasicNameValuePair("cookieKey", cjzh.getCookieKey()));
			pairs.add(new BasicNameValuePair("stamp", stamp));
			pairs.add(new BasicNameValuePair("password", password));

			HttpResponse httpresponse;

			UrlEncodedFormEntity urlEncodedFormEntity = new UrlEncodedFormEntity(pairs, "utf-8");
			httppost.setEntity(urlEncodedFormEntity);
			httpresponse = httpClient.execute(httppost);
			HttpEntity entity = httpresponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			JSONObject jsonObj = JSONObject.fromObject(body);
			int code = jsonObj.getInt("code");
			if (code == 0) {
				return true;
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		} finally {
			if (httppost != null) {
				httppost.releaseConnection();
			}
		}
		return false;
	}


	/**
	 * allCjzh获取在线采集账号
	 *
	 * @param lx      账号类型：0.所有账号、1.仅BI账号、2.仅俱乐部账号
	 * @param jlbdpid 俱乐部店铺id
	 * @param dpmc    店铺名称
	 * @param userid  店铺userid
	 * @return
	 */
	public List<Cjzh> allCjzh(int lx, long jlbdpid, String dpmc, long userid) {
		String url = SERVER_COOKIE + "hqzh_allCjzh.action";
		SSLContext sslContext;
		HttpPost httppost = null;
		try {
			String stamp = String.valueOf(System.currentTimeMillis());
			String password = Md5.password(lx + jlbdpid + dpmc + userid + stamp);

			sslContext = new SSLContextBuilder().loadTrustMaterial(null, new AbujlbTrustStrategy()).build();

			SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslContext);
			CloseableHttpClient httpClient = HttpClients.custom().setSSLSocketFactory(sslsf).build();

			httppost = new HttpPost(url);
			ArrayList<BasicNameValuePair> pairs = new ArrayList<BasicNameValuePair>();
			pairs.add(new BasicNameValuePair("lx", lx + ""));
			pairs.add(new BasicNameValuePair("jlbdpid", jlbdpid + ""));
			pairs.add(new BasicNameValuePair("dpmc", dpmc));
			pairs.add(new BasicNameValuePair("userid", userid + ""));
			pairs.add(new BasicNameValuePair("stamp", stamp));
			pairs.add(new BasicNameValuePair("password", password));

			HttpResponse httpresponse;
			UrlEncodedFormEntity urlEncodedFormEntity = new UrlEncodedFormEntity(pairs, "utf-8");
			httppost.setEntity(urlEncodedFormEntity);
			httpresponse = httpClient.execute(httppost);
			HttpEntity entity = httpresponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			List<Cjzh> list = null;
			if (Result.isTrue(body)) {
				Type type = new TypeToken<ArrayList<Cjzh>>() {
				}.getType();
				list = Result.getValueFromJson(body, "list", type);
			}
			return list;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		} finally {
			if (httppost != null) {
				httppost.releaseConnection();
			}
		}
		return null;
	}
}
