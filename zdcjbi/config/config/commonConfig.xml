<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE properties SYSTEM "http://java.sun.com/dtd/properties.dtd">
<properties>
	<entry key="logindomain">localhost</entry>
	
	<!-- 公共session -->
	<entry key="commonSessionUrl">http://localhost/login/</entry>
	
	<!-- 单点登录用 -->
	<entry key="loginurl">http://localhost/login/Login.action</entry>
	<entry key="checkloginurl">http://localhost/login/CheckLogin.action</entry>
	
	<!-- 基础平台地址，提供获取组织机构用户、数据字典等内容 -->
	<entry key="jcpt">http://localhost/jcpt/</entry>
	
	<!-- session实现方式 -->
	<entry key="sessiontype">redis</entry>
	<!-- session失效时间，此失效时间只有当实现方式为redis才有效，当有tomcat时，失效时间以web.xml配置 -->
	<entry key="sessiontimeout">600</entry>

	<!-- 上传OSS以及发送短信所用密钥 -->
	<entry key="accesskeyid">LTAIBS17fPX8VxN0</entry> 
	<entry key="accesskeysecret">4L1imCT4YO4V0ATD5vUZilbGKruGma</entry>

	<!-- OSS地址配置信息 -->
	<entry key="oss-endpoint">oss-cn-shanghai.aliyuncs.com</entry>
	<entry key="oss-bucketname">tbsjcj</entry>
	<entry key="oss-domain">https://image.abujlb.com/</entry>
	<entry key="oss-domain2">http://tbsjcj.oss-cn-shanghai.aliyuncs.com/</entry>
	
	<!-- 加密验证oss -->
	<entry key="auth-oss-bucketname">abujlbjson</entry>
	<entry key="auth-oss-domain">https://json.abujlb.com/</entry>
	<entry key="auth-cdn-domain">https://json.abujlb.com</entry>
	<entry key="auth-cdn-domain-regex">["https"|"http"]://json.abujlb.com(.*)</entry>
	<entry key="auth-cdn-uid">0</entry>
	<entry key="auth-cdn-privatekey">abujlbJSON698654</entry>

	<!-- redis相关配置 -->
	<entry key="redis-maxactive">100</entry>
	<entry key="redis-maxidle">5</entry>
	<entry key="redis-maxwait">10000</entry>
	<entry key="redis-ip">**************</entry>
	<entry key="redis-port">6379</entry>
	<entry key="redis-auth">123456</entry>
	
	<entry key="mqtopic">MQ_TEST_YQ</entry>
	<entry key="mqtags">spider</entry>
	<entry key="check">spider</entry>
	<entry key="mqchecktags">spider</entry>
	
	<!-- mongodb相关配置 -->
	<entry key="log-off">true</entry>
	<entry key="mongodb-address">127.0.0.1:27017</entry>
	<entry key="mongodb-conns">20</entry>
	<entry key="mongodb-timeout">1200000</entry>
	<entry key="mongodb-maxwaittime">30000</entry>
	<entry key="mongodb-threadsallowed">100</entry>
	<entry key="mongodb-user">log</entry>
	<entry key="mongodb-db">log</entry>
	<entry key="mongodb-password">log</entry>

	<!-- 线程池线程数配置 -->
	<entry key="threadpoolsize">5</entry>

	<!-- 序列默认缓冲值 -->
	<entry key="sequencebuffer">20</entry>
	
	<entry key="loginserver">false</entry>
	<entry key="serverurl">https://www.abujlb.com/LoginService.action</entry>
	
	<!-- 临时文件夹 -->
	<entry key="tempdir">/data/temp/</entry>
	
	<!-- 腾讯云配置 -->
	<entry key="txyun-mq-secretId">AKIDNYUAuRJsmTwBG9qIXaP1PaVAZibD7dS1</entry>
	<entry key="txuun-mq-secretKey">RcXCaibFcjaAhz1nJq78tOcgW8VAeuzs</entry>
	<entry key="txyun-mq-endpoint">https://cmq-sh.public.tencenttdmq.com</entry>
	<entry key="txyun-mq-queueName_JobsTxYun">jobs-TXYun</entry>
	<entry key="txyun-mq-queueName_RedisWriteBack">RedisWriteBack</entry>
	
	
	<entry key="mjzhcserver">http://193.112.96.62/mjzhcserver/</entry>
</properties>