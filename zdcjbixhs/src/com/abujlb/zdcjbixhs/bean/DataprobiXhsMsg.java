package com.abujlb.zdcjbixhs.bean;

import com.abujlb.zdcjbixhs.util.StrUtil;
import com.alibaba.fastjson.JSON;
import org.apache.commons.collections.CollectionUtils;

import java.io.Serializable;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2024/4/1
 */
public class DataprobiXhsMsg implements Serializable {

    private static final long serialVersionUID = 1L;

    //当前时间戳
    private String t;
    //随机uuid
    private String uuid;

    //用户id
    private int yhid;
    //店铺userid
    private String userid;
    //渠道
    private String qd;
    //对应bi_info表的id
    private int biinfoId;
    //店铺名称
    private String dpmc;
    //1登录 2下午补发
    private int type;

    //是否需要处理商品列表数据 0不需要处理 1需要处理
    private int splb;

    //商品效果
    private List<String> spxg;
    //商品效果-店铺维度
    private List<String> spxgdp;
    //千帆笔记营销
    private List<String> qfbjyx;
    //千帆视频营销
    private List<String> qfzbyxdp;
    //聚光平台笔记推广
    private List<String> jgptbjtg;
    //聚光平台笔记推广店铺
    private List<String> jgptbjtgdp;
    //聚光平台直播店铺
    private List<String> jgptzbfxdp;

    //流量数据店铺
    private List<String> llsjdp;

    //首次补采24个月的经营数据  元素为月末的最后一天
    private List<String> dpMonths;

    //千帆推广计划
    private List<String> qftgjh;
    //聚光推广计划
    private List<String> jgtgjh;

    //千帆推广分析
    private List<String> qftgfx;
    //聚光推广分析
    private List<String> jgtgfx;

    //小红书乘风-商品推广
    private List<String> cfsptg;

    //小红书乘风-商品推广店铺
    private List<String> cfsptgdp;

    //小红书乘风-直播推广店铺
    private List<String> cfzbtgdp;

    //小红书乘风-直播推广
    private List<String> cfzbtg;
    //店铺账户金额
    private List<String> money;
    //商家体验分
    private List<String> xhssjtyf;

    //客服绩效
    private List<String> kfperformance;
    //客服分析
    private List<String> kfanalysis;

    //蒲公英-达人佣金
    private List<String> pgydryj;

    //蒲公英-笔记报告
    private List<String> pgybjbg;

    //蒲公英-笔记报告店铺
    private List<String> pgybjbgdp;

    public DataprobiXhsMsg() {
        this.t = String.valueOf(System.currentTimeMillis());
        this.uuid = UUID.randomUUID().toString();
    }

    public List<String> getKfanalysis() {
        return kfanalysis;
    }

    public void setKfanalysis(List<String> kfanalysis) {
        this.kfanalysis = kfanalysis;
    }

    public List<String> getKfperformance() {
        return kfperformance;
    }

    public void setKfperformance(List<String> kfperformance) {
        this.kfperformance = kfperformance;
    }

    public List<String> getXhssjtyf() {
        return xhssjtyf;
    }

    public void setXhssjtyf(List<String> xhssjtyf) {
        this.xhssjtyf = xhssjtyf;
    }

    public String getT() {
        return t;
    }

    public void setT(String t) {
        this.t = t;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public List<String> getMoney() {
        return money;
    }

    public void setMoney(List<String> money) {
        this.money = money;
    }

    public int getYhid() {
        return yhid;
    }

    public void setYhid(int yhid) {
        this.yhid = yhid;
    }

    public String getUserid() {
        return userid;
    }

    public void setUserid(String userid) {
        this.userid = userid;
    }

    public String getQd() {
        return qd;
    }

    public void setQd(String qd) {
        this.qd = qd;
    }

    public int getBiinfoId() {
        return biinfoId;
    }

    public void setBiinfoId(int biinfoId) {
        this.biinfoId = biinfoId;
    }

    public String getDpmc() {
        return dpmc;
    }

    public void setDpmc(String dpmc) {
        this.dpmc = dpmc;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getSplb() {
        return splb;
    }

    public void setSplb(int splb) {
        this.splb = splb;
    }

    public List<String> getSpxg() {
        return spxg;
    }

    public void setSpxg(List<String> spxg) {
        this.spxg = spxg;
    }

    public List<String> getSpxgdp() {
        return spxgdp;
    }

    public void setSpxgdp(List<String> spxgdp) {
        this.spxgdp = spxgdp;
    }

    public List<String> getQfbjyx() {
        return qfbjyx;
    }

    public void setQfbjyx(List<String> qfbjyx) {
        this.qfbjyx = qfbjyx;
    }

    public List<String> getQfzbyxdp() {
        return qfzbyxdp;
    }

    public void setQfzbyxdp(List<String> qfzbyxdp) {
        this.qfzbyxdp = qfzbyxdp;
    }

    public List<String> getJgptbjtg() {
        return jgptbjtg;
    }

    public void setJgptbjtg(List<String> jgptbjtg) {
        this.jgptbjtg = jgptbjtg;
    }

    public List<String> getDpMonths() {
        return dpMonths;
    }

    public void setDpMonths(List<String> dpMonths) {
        this.dpMonths = dpMonths;
    }

    public List<String> getJgptzbfxdp() {
        return jgptzbfxdp;
    }

    public void setJgptzbfxdp(List<String> jgptzbfxdp) {
        this.jgptzbfxdp = jgptzbfxdp;
    }

    public List<String> getLlsjdp() {
        return llsjdp;
    }

    public void setLlsjdp(List<String> llsjdp) {
        this.llsjdp = llsjdp;
    }

    public List<String> getQftgjh() {
        return qftgjh;
    }

    public void setQftgjh(List<String> qftgjh) {
        this.qftgjh = qftgjh;
    }

    public List<String> getJgtgjh() {
        return jgtgjh;
    }

    public void setJgtgjh(List<String> jgtgjh) {
        this.jgtgjh = jgtgjh;
    }

    public List<String> getQftgfx() {
        return qftgfx;
    }

    public void setQftgfx(List<String> qftgfx) {
        this.qftgfx = qftgfx;
    }

    public List<String> getJgtgfx() {
        return jgtgfx;
    }

    public void setJgtgfx(List<String> jgtgfx) {
        this.jgtgfx = jgtgfx;
    }

    public List<String> getJgptbjtgdp() {
        return jgptbjtgdp;
    }

    public void setJgptbjtgdp(List<String> jgptbjtgdp) {
        this.jgptbjtgdp = jgptbjtgdp;
    }

    public List<String> getCfsptg() {
        return cfsptg;
    }

    public void setCfsptg(List<String> cfsptg) {
        this.cfsptg = cfsptg;
    }

    public List<String> getCfsptgdp() {
        return cfsptgdp;
    }

    public void setCfsptgdp(List<String> cfsptgdp) {
        this.cfsptgdp = cfsptgdp;
    }

    public List<String> getCfzbtgdp() {
        return cfzbtgdp;
    }

    public void setCfzbtgdp(List<String> cfzbtgdp) {
        this.cfzbtgdp = cfzbtgdp;
    }

    public List<String> getCfzbtg() {
        return cfzbtg;
    }

    public void setCfzbtg(List<String> cfzbtg) {
        this.cfzbtg = cfzbtg;
    }

    public List<String> getPgydryj() {
        return pgydryj;
    }

    public void setPgydryj(List<String> pgydryj) {
        this.pgydryj = pgydryj;
    }

    public List<String> getPgybjbg() {
        return pgybjbg;
    }

    public void setPgybjbg(List<String> pgybjbg) {
        this.pgybjbg = pgybjbg;
    }

    public List<String> getPgybjbgdp() {
        return pgybjbgdp;
    }

    public void setPgybjbgdp(List<String> pgybjbgdp) {
        this.pgybjbgdp = pgybjbgdp;
    }

    public void initial() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        Field[] declaredFields = DataprobiXhsMsg.class.getDeclaredFields();
        for (Field declaredField : declaredFields) {
            Class<?> type = declaredField.getType();
            if (type == List.class) {
                String name = declaredField.getName();
                Method setMethod = DataprobiXhsMsg.class.getMethod("set" + StrUtil.strFirstCharUpper(name), List.class);
                setMethod.invoke(this, new ArrayList<>());
            }
        }
    }

    public boolean checkSend() {
        boolean send = false;
        Field[] declaredFields = DataprobiXhsMsg.class.getDeclaredFields();
        for (Field declaredField : declaredFields) {
            Class<?> type = declaredField.getType();
            if (type == List.class) {
                String name = declaredField.getName();
                try {
                    Method getMethod = DataprobiXhsMsg.class.getMethod("get" + StrUtil.strFirstCharUpper(name));
                    List<String> rqList = (List<String>) getMethod.invoke(this);
                    if (CollectionUtils.isNotEmpty(rqList)) {
                        send = true;
                        break;
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        if (!send) {
            send = splb == 1;
        }
        return send;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
