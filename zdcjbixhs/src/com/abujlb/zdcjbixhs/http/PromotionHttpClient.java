package com.abujlb.zdcjbixhs.http;

import com.abujlb.CommonConfig;
import com.abujlb.zdcjbixhs.bean.JgbjDp;
import com.abujlb.zdcjbixhs.cookie.Cjzh;
import com.abujlb.zdcjbixhs.log.CjLog;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.CookieSpecs;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.protocol.BasicHttpContext;
import org.apache.http.protocol.HttpContext;
import org.apache.http.util.EntityUtils;
import org.apache.log4j.Logger;

import java.io.ByteArrayOutputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;

public class PromotionHttpClient {

    static final Logger LOG = Logger.getLogger(PromotionHttpClient.class);

    private static final int MAX_RETRIES = 3;

    public static JSONArray getPromotionDetails(Cjzh cjzh, String startTime, String endTime, String type,String dataType) {
        int marketingTarget;
        List<Integer> marketingTargetList = new ArrayList<>();

        if ("qfbj".equals(type)) {
            marketingTarget = 3;
            marketingTargetList.add(3);
            marketingTargetList.add(15);
        } else if ("qfzb".equals(type)) {
            marketingTarget = 8;
            marketingTargetList.add(8);
            marketingTargetList.add(14);
        } else {
            // 未知类型，返回null
            return null;
        }

        JSONArray allDetails = new JSONArray();
        JSONArray campaignIds = fetchAllCampaignIds(cjzh, startTime, endTime, marketingTarget, marketingTargetList);

        if (campaignIds == null) {
            return null;
        }

        for (Object campaignId : campaignIds) {
            JSONArray details = fetchCampaignDetailsByDate(cjzh, startTime,(Integer) campaignId,dataType);
            if (details == null) {
                return null;
            }
            allDetails.addAll(details);
        }

        return allDetails;
    }


    private static JSONArray fetchAllCampaignIds(Cjzh cjzh, String startTime, String endTime, int marketingTarget, List<Integer> marketingTargetList) {
        JSONArray campaignIds = new JSONArray();
        int pageIndex = 1;
        int pageSize = 50;

        while (true) {
            JSONArray pageData = fetchCampaignPage(cjzh, startTime, endTime, pageIndex, pageSize, marketingTarget, marketingTargetList);
            if (pageData == null) {
                return null;
            }
            if (pageData.isEmpty()) {
                break;
            }
            for (Object obj : pageData) {
                JSONObject campaign = (JSONObject) obj;
                campaignIds.add(campaign.getInteger("campaignId"));
            }
            if (pageData.size() < pageSize) {
                break;
            }
            pageIndex++;
        }

        return campaignIds;
    }



    private static JSONArray fetchCampaignPage(Cjzh cjzh, String startTime, String endTime, int pageIndex, int pageSize, int marketingTarget, List<Integer> marketingTargetList) {
        CjLog.addTime("tg_http.fetchCampaignPage");
        CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cjzh.getCookieStore()).build();
        String url = "https://ark.xiaohongshu.com/api/ec_ads/query_ec_ad_list";
        HttpPost httpPost = new HttpPost(url);

        // 设置头部信息
        httpPost.setHeader("Accept", "application/json, text/plain, */*");
        httpPost.setHeader("Accept-Encoding", "gzip, deflate, br, zstd");
        httpPost.setHeader("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
        httpPost.setHeader("Content-Type", "application/json;charset=UTF-8");
        httpPost.setHeader("Origin", "https://ark.xiaohongshu.com");
        httpPost.setHeader("Priority", "u=1, i");
        httpPost.setHeader("Referer", "https://ark.xiaohongshu.com/app-ad/ad/manage");
        httpPost.setHeader("Sec-Ch-Ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Google Chrome\";v=\"126\"");
        httpPost.setHeader("Sec-Ch-Ua-Mobile", "?0");
        httpPost.setHeader("Sec-Fetch-Dest", "empty");
        httpPost.setHeader("Sec-Fetch-Mode", "cors");
        //httpPost.setHeader("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
//        httpPost.setHeader("X-B3-Traceid", "80b4c06a762a30fa");
        httpPost.setHeader("X-Subsystem", "ark");


        JSONObject requestParams = new JSONObject();
        requestParams.put("campaignName", "");
        requestParams.put("startTime", startTime);
        requestParams.put("endTime", endTime);
        JSONObject page = new JSONObject();
        page.put("pageIndex", pageIndex);
        page.put("pageSize", pageSize);
        requestParams.put("page", page);
        requestParams.put("marketingTarget", marketingTarget);

        JSONArray marketingTargetJSONArray = new JSONArray();
        marketingTargetJSONArray.addAll(marketingTargetList);
        requestParams.put("marketingTargetList", marketingTargetJSONArray);
        requestParams.put("optimizeTargetList", new JSONArray());

        try {
            StringEntity entity = new StringEntity(requestParams.toJSONString(), "UTF-8");
            httpPost.setEntity(entity);
            RequestConfig requestConfig = RequestConfig.custom()
                    .setSocketTimeout(20000)
                    .setConnectTimeout(20000)
                    .setConnectionRequestTimeout(20000)
                    .setCookieSpec(CookieSpecs.NETSCAPE)
                    .build();
            httpPost.setConfig(requestConfig);

            HttpResponse httpResponse = httpClient.execute(httpPost);
            HttpEntity responseEntity = httpResponse.getEntity();
            String body = EntityUtils.toString(responseEntity);

            JSONObject jsonObject = JSONObject.parseObject(body);
            if (jsonObject.getBooleanValue("success")) {
                return jsonObject.getJSONObject("data").getJSONArray("campaignDTOs");
            }
        } catch (Exception e) {
            LOG.error("获取推广计划列表失败", e);
        } finally {
            try {
                httpClient.close();
                TimeUnit.SECONDS.sleep(1);
            } catch (Exception e) {
                LOG.error("关闭httpClient失败", e);
            }
        }

        return null;
    }





    public static JSONArray fetchCampaignDetailsByDate(Cjzh cjzh, String date, int campaignId,String dataType) {
        int pageIndex = 1;
        int pageSize = 50;
        JSONArray resultArray = new JSONArray();

        while (true) {
            JSONArray pageData = fetchCampaignDetails(cjzh, campaignId, date, date, pageIndex, pageSize,dataType);
            if (pageData == null) {
                return null;
            }
            if (pageData.isEmpty() || pageData.size() < pageSize) {
                resultArray.addAll(pageData);
                break;
            }
            resultArray.addAll(pageData);
            pageIndex++;
        }

        return resultArray;
    }




    private static JSONArray fetchCampaignDetails(Cjzh cjzh, int campaignId, String startTime, String endTime, int pageIndex, int pageSize, String dataType) {
        CjLog.addTime("tg_http.fetchCampaignDetails");
        int retryCount = 0;

        while (retryCount < MAX_RETRIES) {
            try (
                    CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cjzh.getCookieStore()).build()) {
                String url = "https://ark.xiaohongshu.com/api/ec_ads/query_ec_campaign_detail";
                HttpPost httpPost = new HttpPost(url);
                setHeaders(httpPost);

                JSONObject requestParams = new JSONObject();
                requestParams.put("campaignId", campaignId);
                requestParams.put("startTime", startTime);
                requestParams.put("endTime", endTime);
                JSONObject pageParams = new JSONObject();
                pageParams.put("pageIndex", pageIndex);
                pageParams.put("pageSize", pageSize);
                requestParams.put("page", pageParams);

                StringEntity entity = new StringEntity(requestParams.toJSONString(), "UTF-8");
                httpPost.setEntity(entity);
                RequestConfig requestConfig = RequestConfig.custom()
                        .setSocketTimeout(20000)
                        .setConnectTimeout(20000)
                        .setConnectionRequestTimeout(20000)
                        .setCookieSpec(CookieSpecs.NETSCAPE)
                        .build();
                httpPost.setConfig(requestConfig);

                HttpResponse httpResponse = httpClient.execute(httpPost);
                HttpEntity responseEntity = httpResponse.getEntity();
                String body = EntityUtils.toString(responseEntity);

                JSONObject jsonObject = JSONObject.parseObject(body);
                if (jsonObject.getBooleanValue("success")) {
                    JSONObject dataObject = jsonObject.getJSONObject("data");
                    JSONArray creativities = dataObject
                            .getJSONObject("EcAdItemDetailDTO")
                            .getJSONArray("creativities");

                    JSONArray resultArray = new JSONArray();

                    for (int i = 0; i < creativities.size(); i++) {
                        JSONObject creativity = creativities.getJSONObject(i);
                        JSONObject result = new JSONObject();

                        if ("hf".equals(dataType)) {
                            // 默认解析方式
                            String dataValueJson = creativity.getString("dataValueJson");
                            if (dataValueJson != null) {
                                JSONObject dataValue = JSONObject.parseObject(dataValueJson);
                                creativity.put("fee", dataValue.getString("fee"));
                            }
                            creativity.remove("dataValueJson");
                            resultArray.add(creativity);
                        } else if ("all".equals(dataType)) {
                            // 解析所有字段
                            result.put("itemId", creativity.getString("itemId"));
                            result.put("enable", creativity.getInteger("enable"));
                            result.put("noteId", creativity.getString("noteId"));
                            result.put("creativityId", creativity.getLong("creativityId"));

                            // 解析 dataValueJson 字段中的值
                            String dataValueJson = creativity.getString("dataValueJson");
                            if (dataValueJson != null) {
                                JSONObject dataValue = JSONObject.parseObject(dataValueJson);
                                result.put("fee", dataValue.getString("fee"));
                                result.put("impression", dataValue.getString("impression"));
                                result.put("click", dataValue.getString("click"));
                                result.put("ctr", dataValue.getString("ctr"));
                                result.put("acp", dataValue.getString("acp"));
                                result.put("cpm", dataValue.getString("cpm"));
                                result.put("like", dataValue.getString("like"));
                                result.put("comment", dataValue.getString("comment"));
                                result.put("collect", dataValue.getString("collect"));
                                result.put("follow", dataValue.getString("follow"));
                                result.put("share", dataValue.getString("share"));
                                result.put("interaction", dataValue.getString("interaction"));
                            }

                            // 添加 EcAdItemDetailDTO 中的字段
                            result.put("campaignId", dataObject.getJSONObject("EcAdItemDetailDTO").getLong("campaignId"));
                            result.put("campaignDayBudget", dataObject.getJSONObject("EcAdItemDetailDTO").getDouble("campaignDayBudget"));
                            result.put("campaignName", dataObject.getJSONObject("EcAdItemDetailDTO").getString("campaignName"));

                            resultArray.add(result);
                        }
                    }
                    return resultArray;
                } else {
                    LOG.error("请求失败，响应内容: " + body);
                }
            } catch (Exception e) {
                LOG.error("获取推广详情失败，重试次数: " + retryCount, e);
                retryCount++;
                if (retryCount < MAX_RETRIES) {
                    try {
                        // 生成随机数
                        int sleepTime = ThreadLocalRandom.current().nextInt(5, 11);
                        TimeUnit.SECONDS.sleep(sleepTime);
                    } catch (InterruptedException ie) {
                        LOG.error("线程被中断", ie);
                        break;
                    }
                }
            }
        }
        return null;
    }


    private static void setHeaders(HttpPost httpPost) {
        httpPost.setHeader("Accept", "application/json, text/plain, */*");
        httpPost.setHeader("Accept-Encoding", "gzip, deflate, br, zstd");
        httpPost.setHeader("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
        httpPost.setHeader("Content-Type", "application/json;charset=UTF-8");
        httpPost.setHeader("Origin", "https://ark.xiaohongshu.com");
        httpPost.setHeader("Priority", "u=1, i");
        httpPost.setHeader("Referer", "https://ark.xiaohongshu.com/app-ad/ad/manage");
        httpPost.setHeader("Sec-Ch-Ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Google Chrome\";v=\"126\"");
        httpPost.setHeader("Sec-Ch-Ua-Mobile", "?0");
        httpPost.setHeader("Sec-Fetch-Dest", "empty");
        httpPost.setHeader("Sec-Fetch-Mode", "cors");
        //httpPost.setHeader("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
//        httpPost.setHeader("X-B3-Traceid", "80b4c06a762a30fa");
        httpPost.setHeader("X-Subsystem", "ark");
    }

    public static JSONArray queryJgptTgNoteList(Cjzh cjzh, String startDate, String endDate, String reportType) {
        JSONArray resultArray = new JSONArray();

        int pageNum = 1;
        int pageSize = 200;
        while (true) {
            JSONArray dataList = fetchTgNoteList(cjzh, startDate, endDate, pageNum, pageSize, reportType);
            //
            if (dataList == null) {
                return null;
            } else if (dataList.isEmpty()) {
                break;
            }

            resultArray.addAll(dataList);

            if (dataList.size() < pageSize) {
                break;
            }
            pageNum++;
        }
        return resultArray;
    }

    private static JSONArray fetchTgNoteList(Cjzh cjzh, String startDate, String endDate, int pageNum, int pageSize, String reportType) {
        String url = "https://ad.xiaohongshu.com/api/leona/rtb/data/report";
        //CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cjzh.getJgptcjzh().getCookieStore()).build();
        CloseableHttpClient httpClient = null;
        HttpPost httpPost = new HttpPost(url);
        JSONArray resultArray = new JSONArray();
        // 根据账号类型决定使用哪个cookieStore
        if ("0".equals(cjzh.getIsAccount())) {
            // 品牌主账号使用自己的cookieStore
            httpClient = HttpClients.custom()
                    .setDefaultCookieStore(cjzh.getJgptcjzh().getCookieStore())
                    .build();
        } else {
            // 子账号使用关联的cookieStore
            httpClient = HttpClients.custom()
                    .setDefaultCookieStore(cjzh.getCookieStore())
                    .build();
        }

        httpPost.setHeader("Accept", "application/json, text/plain, */*");
        httpPost.setHeader("Accept-Encoding", "gzip, deflate, br, zstd");
        httpPost.setHeader("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
        httpPost.setHeader("Content-Type", "application/json;charset=UTF-8");
        httpPost.setHeader("Origin", "https://ad.xiaohongshu.com");
        httpPost.setHeader("Referer", "https://ad.xiaohongshu.com/aurora/ad/ec-datareports/note?uba_pre=18.aurora_ec_campaign_datareports..*************&uba_ppre=18.aurora_ec_live_history_datareports..1720065842246&uba_index=13");
        httpPost.setHeader("Sec-Ch-Ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Google Chrome\";v=\"126\"");
        httpPost.setHeader("Sec-Ch-Ua-Mobile", "?0");
        httpPost.setHeader("Sec-Ch-Ua-Platform", "\"macOS\"");
        httpPost.setHeader("Sec-Fetch-Dest", "empty");
        httpPost.setHeader("Sec-Fetch-Mode", "cors");
        httpPost.setHeader("Sec-Fetch-Site", "same-origin");
        httpPost.setHeader("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");

        List<String> columns;
        if ("LIVE".equals(reportType)) {
            columns = Arrays.asList("roomId", "fee", "impression", "click", "ctr", "acp", "cpm", "like", "comment", "collect", "follow", "share", "interaction", "cpi", "actionButtonClick", "actionButtonCtr", "screenshot", "picSave", "clkLiveEntryPv", "clkLiveEntryPvCost", "clkLiveAvgViewTime", "clkLiveAllFollow", "clkLive5sEntryPv", "clkLive5sEntryUvCost", "clkLiveComment", "clkLiveRoomOrderNum", "liveAverageOrderCost", "clkLiveRoomRgmv", "clkLiveRoomRoi");
        } else {
            columns = Arrays.asList("noteId", "fee", "impression", "click", "ctr", "acp", "cpm", "like", "comment", "collect", "follow", "share", "interaction", "cpi", "actionButtonClick", "actionButtonCtr", "screenshot", "picSave", "reservePV", "liveSubscribeCnt", "liveSubscribeCntCost", "clkLiveEntryPv", "clkLiveEntryPvCost", "clkLiveAvgViewTime", "clkLiveAllFollow", "clkLive5sEntryPv", "clkLive5sEntryUvCost", "clkLiveComment", "goodsVisit", "goodsVisitPrice", "sellerVisit", "sellerVisitPrice", "shoppingCartAdd", "addCartPrice", "presaleOrderNum7d", "presaleOrderGmv7d", "goodsOrder", "goodsOrderPrice", "rgmv", "roi", "successGoodsOrder", "clickOrderCvr", "purchaseOrderPrice7d", "purchaseOrderGmv7d", "purchaseOrderRoi7d", "clkLiveRoomOrderNum", "liveAverageOrderCost", "clkLiveRoomRgmv", "clkLiveRoomRoi", "newSellerGoodsViewCnt", "newSellerPurchaseOrderNum7d", "newSellerPurchaseOrderGmv7d");
        }

        try {
            JSONObject requestBody = new JSONObject();
            requestBody.put("vSellerId", "646d80ed80cd88000151d03c");
            requestBody.put("columns", columns);
            requestBody.put("splitColumns", new JSONArray());
            requestBody.put("needTotal", true);
            requestBody.put("needList", true);
            requestBody.put("needSize", true);
            requestBody.put("timeUnit", "DAY");
            requestBody.put("pageSize", pageSize);
            requestBody.put("pageNum", pageNum);
            requestBody.put("sorts", new JSONArray());
            requestBody.put("reportType", reportType);
            requestBody.put("startDate", startDate);
            requestBody.put("endDate", endDate);

            JSONArray filters = new JSONArray();
            JSONObject filter = new JSONObject();
            filter.put("column", "marketingTarget");
            filter.put("operator", "in");
            filter.put("values", Arrays.asList(3, 15, 8, 14));
            filters.add(filter);
            requestBody.put("filters", filters);

            StringEntity entity = new StringEntity(requestBody.toJSONString(), "UTF-8");
            httpPost.setEntity(entity);

            RequestConfig requestConfig = RequestConfig.custom()
                    .setSocketTimeout(20000)
                    .setConnectTimeout(20000)
                    .setConnectionRequestTimeout(20000)
                    .setCookieSpec(CookieSpecs.NETSCAPE)
                    .build();
            httpPost.setConfig(requestConfig);

            HttpResponse httpResponse = httpClient.execute(httpPost);
            HttpEntity responseEntity = httpResponse.getEntity();
            String body = EntityUtils.toString(responseEntity);

            JSONObject responseJson = JSONObject.parseObject(body);
            if (responseJson.getBoolean("success") && responseJson.getInteger("code") == 0) {
                JSONArray dataList = responseJson.getJSONObject("data").getJSONArray("list");
                if ("NOTE".equals(reportType)) {
                    for (int i = 0; i < dataList.size(); i++) {
                        JSONObject item = dataList.getJSONObject(i);
                        JSONObject filteredItem = new JSONObject();
                        filteredItem.put("noteId", item.getString("noteId"));
                        filteredItem.put("impression", item.getString("impression"));//展现量
                        filteredItem.put("click", item.getString("click"));//点击量
                        filteredItem.put("ctr", item.getString("ctr"));//点击率
                        filteredItem.put("fee", item.getString("fee"));//花费
                        resultArray.add(filteredItem);
                    }
                }
                return resultArray;
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                httpClient.close();
                TimeUnit.SECONDS.sleep(2);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    public static JSONObject queryJgptZbfx(Cjzh cjzh, String startDate, String endDate) {
        //CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cjzh.getJgptcjzh().getCookieStore()).build();
        CloseableHttpClient httpClient = null;
        String url = "https://ad.xiaohongshu.com/api/leona/rtb/data/hour/distribution";
        HttpPost httpPost = null;
        // 根据账号类型决定使用哪个cookieStore
        if ("0".equals(cjzh.getIsAccount())) {
            // 品牌主账号使用自己的cookieStore
            httpClient = HttpClients.custom()
                    .setDefaultCookieStore(cjzh.getJgptcjzh().getCookieStore())
                    .build();
        } else {
            // 子账号使用关联的cookieStore
            httpClient = HttpClients.custom()
                    .setDefaultCookieStore(cjzh.getCookieStore())
                    .build();
        }

        try {
            // 构建POST请求
            httpPost = new HttpPost(url);
            httpPost.setHeader("Accept", "application/json, text/plain, */*");
            httpPost.setHeader("Accept-Encoding", "gzip, deflate, br, zstd");
            httpPost.setHeader("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
            httpPost.setHeader("Content-Type", "application/json;charset=UTF-8");
            httpPost.setHeader("Origin", "https://ad.xiaohongshu.com");
            httpPost.setHeader("Referer", "https://ad.xiaohongshu.com/aurora/ad/ec-datareports/live/history");
            httpPost.setHeader("Sec-Ch-Ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Google Chrome\";v=\"126\"");
            httpPost.setHeader("Sec-Ch-Ua-Mobile", "?0");
            httpPost.setHeader("Sec-Ch-Ua-Platform", "\"macOS\"");
            httpPost.setHeader("Sec-Fetch-Dest", "empty");
            httpPost.setHeader("Sec-Fetch-Mode", "cors");
            httpPost.setHeader("Sec-Fetch-Site", "same-origin");
            httpPost.setHeader("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");

            // 构建请求体
            String jsonBody = String.format("{\"source\":1,\"reportType\":\"LIVE\",\"startDate\":\"%s\",\"endDate\":\"%s\",\"filters\":[],\"topType\":0,\"columns\":[\"fee\",\"clkLiveRoomRgmv\",\"clkLiveRoomRoi\",\"clkLiveRoomOrderNum\",\"liveAverageOrderCost\",\"clkLiveEntryPv\",\"clkLive5sEntryPv\",\"clkLiveAvgViewTime\",\"clkLiveAllFollow\",\"clkLiveComment\"]}",
                    startDate, endDate);
            StringEntity entity = new StringEntity(jsonBody, "UTF-8");
            httpPost.setEntity(entity);

            // 设置请求配置
            RequestConfig.Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000).setConnectionRequestTimeout(20000).setCookieSpec(CookieSpecs.NETSCAPE);
            httpPost.setConfig(requestConfig.build());

            // 发送请求并处理响应
            HttpResponse httpResponse = httpClient.execute(httpPost);
            HttpEntity responseEntity = httpResponse.getEntity();
            String body = EntityUtils.toString(responseEntity);

            // 解析响应体
            JSONObject jsonObject = JSONObject.parseObject(body);
            if (jsonObject.getBooleanValue("success") && jsonObject.getInteger("code") == 0) {
                if (jsonObject.containsKey("data")) {
                    JSONObject dataObject = jsonObject.getJSONObject("data");
                    if (dataObject.containsKey("aggregationData")) {
                        JSONObject aggregationData = dataObject.getJSONObject("aggregationData");
                        return aggregationData != null ? aggregationData : new JSONObject();
                    }
                }
            }

        } catch (IOException e) {
            LOG.error("获取聚光平台直播分析失败", e);
        } finally {
            if (httpPost != null) {
                httpPost.releaseConnection();
            }
        }
        return null;
    }


    public static JSONObject queryQfZbyx(Cjzh cjzh, String startDate, String endDate) {
        CloseableHttpClient httpClient =null;
        httpClient = HttpClients.custom()
                .setDefaultCookieStore(cjzh.getCookieStore())
                .build();
        String url = "https://ark.xiaohongshu.com/api/ec_ads/search_data_report";
        HttpPost httpPost = null;
        try {
            // 构建POST请求
            httpPost = new HttpPost(url);
            httpPost.setHeader("Accept", "application/json, text/plain, */*");
            httpPost.setHeader("Accept-Encoding", "gzip, deflate, br, zstd");
            httpPost.setHeader("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
            httpPost.setHeader("Authorization", cjzh.getAccessToken());
            httpPost.setHeader("Content-Type", "application/json;charset=UTF-8");
            httpPost.setHeader("Origin", "https://ark.xiaohongshu.com");
            httpPost.setHeader("Priority", "u=1, i");
            httpPost.setHeader("Referer", "https://ark.xiaohongshu.com/app-ad/home");
            httpPost.setHeader("Sec-Ch-Ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Google Chrome\";v=\"126\"");
            httpPost.setHeader("Sec-Ch-Ua-Mobile", "?0");
            httpPost.setHeader("Sec-Fetch-Dest", "empty");
            httpPost.setHeader("Sec-Fetch-Mode", "cors");
            httpPost.setHeader("X-Subsystem", "ark");

            // 构建请求体
            String jsonBody = String.format("{\"type\":8,\"marketingTargetList\":[8,14],\"start_date\":\"%s\",\"end_date\":\"%s\",\"platform\":2}", startDate, endDate);
            StringEntity entity = new StringEntity(jsonBody, "UTF-8");
            httpPost.setEntity(entity);

            // 设置请求配置
            RequestConfig.Builder requestConfig = RequestConfig.custom()
                    .setSocketTimeout(20000)
                    .setConnectTimeout(20000)
                    .setConnectionRequestTimeout(20000)
                    .setCookieSpec(CookieSpecs.STANDARD);
            httpPost.setConfig(requestConfig.build());

            // 发送请求并处理响应
            HttpResponse httpResponse = httpClient.execute(httpPost);
            HttpEntity responseEntity = httpResponse.getEntity();
            String body = EntityUtils.toString(responseEntity);

            // 解析响应体
            JSONObject jsonObject = JSONObject.parseObject(body);
            if (jsonObject.getBooleanValue("success") && jsonObject.getInteger("code") == 0) {
                if (jsonObject.containsKey("data")) {
                    JSONObject dataObject = jsonObject.getJSONObject("data");
                    if (dataObject.containsKey("response")) {
                        JSONObject response = dataObject.getJSONObject("response");
                        if (!response.getBooleanValue("success") && "广告主不存在".equals(response.getString("msg")) && response.getInteger("code") == 10040002) {
                            return new JSONObject();
                        }
                        if (dataObject.containsKey("ads_data")) {
                            JSONObject adsData = dataObject.getJSONObject("ads_data");
                            return adsData != null ? adsData : new JSONObject();
                        }
                    }
                }
            }
        } catch (IOException e) {
            LOG.error("获取数据失败", e);
        } finally {
            if (httpPost != null) {
                httpPost.releaseConnection();
            }
        }
        return null;
    }



    /**
     * 调用导出接口生成导出任务
     *
     * @param cjzh      Cjzh对象，包含Cookie信息
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 返回任务ID
     */
    public static String commitTask(Cjzh cjzh, String startTime, String endTime) {
        CjLog.addTime("jgtgjh_http.commitTask");
//        CloseableHttpClient httpClient = HttpClients.custom()
//                .setDefaultCookieStore(cjzh.getJgptcjzh().getCookieStore())
//                .build();
        String url = "https://ad.xiaohongshu.com/api/leona/longTask/download/commit_task";
        HttpPost httpPost = new HttpPost(url);
        CloseableHttpClient httpClient =null;
        if ("0".equals(cjzh.getIsAccount())) {
            // 品牌主账号使用自己的cookieStore
            httpClient = HttpClients.custom()
                    .setDefaultCookieStore(cjzh.getJgptcjzh().getCookieStore())
                    .build();
        } else {
            // 子账号使用关联的cookieStore
            httpClient = HttpClients.custom()
                    .setDefaultCookieStore(cjzh.getCookieStore())
                    .build();
        }

        try {
            // 设置请求配置
            RequestConfig.Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000).setConnectionRequestTimeout(20000).setCookieSpec(CookieSpecs.NETSCAPE);
            httpPost.setConfig(requestConfig.build());



            // 设置请求头
            httpPost.setHeader("Accept", "application/json, text/plain, */*");
            httpPost.setHeader("Accept-Encoding", "gzip, deflate, br, zstd");
            httpPost.setHeader("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
            httpPost.setHeader("Content-Type", "application/json;charset=UTF-8");
            httpPost.setHeader("Origin", "https://ark.xiaohongshu.com");
            //httpPost.setHeader("Cookie", "abRequestId=f62b7748-d3fc-5dd7-9cf2-7867ff8a645a; a1=18f0f250e06fx2zj5xws0geyhmzbiazum7pf7eigr30000287644; webId=bb326281ddea0adfa8c61a4c9b4e584a; gid=yYi8iJ2yJi6WyYi8iJ28dWd38KiCJuV2CEM8UkFkdvx6uiq8D30u7W888JYWK448i00DS0yW; unread={%22ub%22:%22660a1f64000000001b00f214%22%2C%22ue%22:%226619435200000000040198d1%22%2C%22uc%22:29}; customerClientId=077563393420169; web_session=030037a11be9c26a2a3f6f5f19214a8bc816c4; x-user-id-school.xiaohongshu.com=66593d1fe300000000000001; x-user-id-ad.xiaohongshu.com=646d80ed7500000000000003; webBuild=4.24.2; x-user-id-ark.xiaohongshu.com=; access-token-ark.xiaohongshu.com=customer.ark.AT-68c517389558887163380760j2dee8vtn8rnaerk; access-token-ark.beta.xiaohongshu.com=customer.ark.AT-68c517389558887163380760j2dee8vtn8rnaerk; xsecappid=aurora-shell; beaker.session.id=b7eb962f88d5b1745edf2c6f40719917b3d2d866gAJ9cQEoWA4AAAByYS11c2VyLWlkLWFya3ECTlUIX2V4cGlyZXNxA2NkYXRldGltZQpkYXRldGltZQpxBFUKB+gIDgIKMQAKnoVScQVYCwAAAGFyay1saWFzLWlkcQZOWA4AAABfYWNjZXNzZWRfdGltZXEHR0HZpTU2YKKxVQNfaWRxCFggAAAAODcyZWNhZDAzNzQxNDBmZWI0Zjk0MGViNjMwODA3YWNxCVgRAAAAcmEtYXV0aC10b2tlbi1hcmtxCk5YDgAAAF9jcmVhdGlvbl90aW1lcQtHQdmjPyWG+dt1Lg==; acw_tc=0bf5c86a503bedf73e3003ac76821bd133e8cbc5bc0780e23419182da9f49075; websectiga=f47eda31ec99545da40c2f731f0630efd2b0959e1dd10d5fedac3dce0bd1e04d; sec_poison_id=9415b567-94a9-4834-bf25-26ef94d50d3e; customer-sso-sid=68c51739471021080878209395fd07320993442c; ares.beaker.session.id=1721715138961098847337; access-token-ad.xiaohongshu.com=customer.ares.AT-68c517394710210808782096lzmlc1cslmwm2ayu; access-token-ad.beta.xiaohongshu.com=customer.ares.AT-68c517394710210808782096lzmlc1cslmwm2ayu; access-token-idea.xiaohongshu.com=customer.idea.AT-36457044f35f437bafec1db031074257-20f9d9ff2def4046ad8655386ed18e25");
            httpPost.setHeader("Referer", "https://ark.xiaohongshu.com/app-merchant/order-settle/service");
            httpPost.setHeader("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");

            // 设置请求体
            JSONObject jsonRequest = new JSONObject();
            JSONObject input = new JSONObject();
            JSONObject extra = new JSONObject();
            extra.put("start_time", startTime);
            extra.put("end_time", endTime);
            //extra.put("v_seller_id", "646d80ed80cd88000151d03c");
            extra.put("model_type", 1);
            extra.put("need_data", true);
            extra.put("columns", new String[]{
                    "campaignName", "marketingTarget", "placement", "campaignFilterState", "combineAuditStatus",
                    "labelTypeList", "originCampaignDayBudget", "optimizeTarget", "biddingStrategy", "campaignId",
                    "campaignCreateTime", "deliveryMode", "fee", "impression", "click", "ctr", "acp", "cpm", "like",
                    "comment", "collect", "follow", "share", "interaction", "cpi", "actionButtonClick",
                    "actionButtonCtr", "screenshot", "picSave", "reservePV", "liveSubscribeCnt", "liveSubscribeCntCost",
                    "searchCmtClick", "searchCmtClickCvr", "searchCmtAfterReadAvg", "searchCmtAfterRead"
            });
            input.put("extra", extra);
            jsonRequest.put("input", input);
            jsonRequest.put("task_name", "leona_adcenter_data_download");
            jsonRequest.put("module_name", "leona");
            jsonRequest.put("source", "web");

            StringEntity requestEntity = new StringEntity(jsonRequest.toString(), "UTF-8");
            httpPost.setEntity(requestEntity);

            HttpResponse httpResponse = httpClient.execute(httpPost);
            HttpEntity responseEntity = httpResponse.getEntity();
            String body = EntityUtils.toString(responseEntity);


            JSONObject jsonObject = JSONObject.parseObject(body);
            if (jsonObject.getBooleanValue("success") && jsonObject.getInteger("result") == 0) {
                JSONObject data = jsonObject.getJSONObject("data");
                if (data != null) {
                    String taskId = data.getString("task_id");
                    return taskId != null ? taskId : "";
                }
            }
        } catch (Exception e) {
            LOG.error("调用提交导出任务失败", e);
        } finally {
            if (httpPost != null) {
                httpPost.releaseConnection();
            }
            try {
                httpClient.close();
                TimeUnit.SECONDS.sleep(3);
            } catch (Exception e) {
                LOG.error("关闭httpClient失败", e);
            }
        }
        return null;
    }


    /**
     * 获取任务状态
     *
     * @param cjzh    Cjzh对象，包含Cookie信息
     * @param taskId  任务ID
     * @return 返回任务状态
     */
    public static String getTaskStatus(Cjzh cjzh, String taskId) {
        CjLog.addTime("jgtgjh_http.getTaskStatus");
        CloseableHttpClient httpClient =null;
        if ("0".equals(cjzh.getIsAccount())) {
            // 品牌主账号使用自己的cookieStore
            httpClient = HttpClients.custom()
                    .setDefaultCookieStore(cjzh.getJgptcjzh().getCookieStore())
                    .build();
        } else {
            // 子账号使用关联的cookieStore
            httpClient = HttpClients.custom()
                    .setDefaultCookieStore(cjzh.getCookieStore())
                    .build();
        }
        String url = "https://ad.xiaohongshu.com/api/leona/longTask/download/task/status?task_id=" + taskId;
        HttpGet httpGet = new HttpGet(url);

        try {
            RequestConfig requestConfig = RequestConfig.custom()
                    .setSocketTimeout(20000)
                    .setConnectTimeout(20000)
                    .setConnectionRequestTimeout(20000)
                    .setCookieSpec(CookieSpecs.NETSCAPE)
                    .build();
            httpGet.setConfig(requestConfig);

            // 设置请求头
            httpGet.setHeader("Accept", "application/json, text/plain, */*");
            httpGet.setHeader("Accept-Encoding", "gzip, deflate, br, zstd");
            httpGet.setHeader("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
            httpGet.setHeader("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");

            HttpResponse httpResponse = httpClient.execute(httpGet);
            HttpEntity responseEntity = httpResponse.getEntity();
            String body = EntityUtils.toString(responseEntity);

            JSONObject jsonObject = JSONObject.parseObject(body);
            if (jsonObject.getBooleanValue("success") && jsonObject.getInteger("result") == 0) {
                JSONObject data = jsonObject.getJSONObject("data");
                if (data != null) {
                    String status = data.getString("status");
                    return status != null ? status : "";
                }
            }
        } catch (Exception e) {
            LOG.error("获取任务状态失败", e);
        } finally {
            if (httpGet != null) {
                httpGet.releaseConnection();
            }
            try {
                httpClient.close();
                TimeUnit.SECONDS.sleep(3);
            } catch (Exception e) {
                LOG.error("关闭httpClient失败", e);
            }
        }
        return null;
    }

    /**
     * 获取任务结果
     *
     * @param cjzh    Cjzh对象，包含Cookie信息
     * @param taskId  任务ID
     * @return 返回文件URL
     */
    public static String getTaskResult(Cjzh cjzh, String taskId) {
        CjLog.addTime("jgtgjh_http.getTaskResult");
        CloseableHttpClient httpClient =null;
        if ("0".equals(cjzh.getIsAccount())) {
            // 品牌主账号使用自己的cookieStore
            httpClient = HttpClients.custom()
                    .setDefaultCookieStore(cjzh.getJgptcjzh().getCookieStore())
                    .build();
        } else {
            // 子账号使用关联的cookieStore
            httpClient = HttpClients.custom()
                    .setDefaultCookieStore(cjzh.getCookieStore())
                    .build();
        }
        String url = "https://ad.xiaohongshu.com/api/leona/longTask/download/task/result?task_id=" + taskId;
        HttpGet httpGet = new HttpGet(url);

        try {
            RequestConfig requestConfig = RequestConfig.custom()
                    .setSocketTimeout(20000)
                    .setConnectTimeout(20000)
                    .setConnectionRequestTimeout(20000)
                    .setCookieSpec(CookieSpecs.NETSCAPE)
                    .build();
            httpGet.setConfig(requestConfig);

            // 设置请求头
            httpGet.setHeader("Accept", "application/json, text/plain, */*");
            httpGet.setHeader("Accept-Encoding", "gzip, deflate, br, zstd");
            httpGet.setHeader("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
            httpGet.setHeader("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");

            HttpResponse httpResponse = httpClient.execute(httpGet);
            HttpEntity responseEntity = httpResponse.getEntity();
            String body = EntityUtils.toString(responseEntity);

            JSONObject jsonObject = JSONObject.parseObject(body);
            if (jsonObject.getBooleanValue("success") && jsonObject.getInteger("result") == 0) {
                JSONObject data = jsonObject.getJSONObject("data");
                if (data != null) {
                    JSONObject result = data.getJSONObject("result");
                    if (result != null) {
                        String fileUrl = result.getString("file_url");
                        return fileUrl != null ? fileUrl : "";
                    }
                }
            }
        } catch (Exception e) {
            LOG.error("获取任务结果失败", e);
        } finally {
            if (httpGet != null) {
                httpGet.releaseConnection();
            }
            try {
                httpClient.close();
                TimeUnit.SECONDS.sleep(3);
            } catch (Exception e) {
                LOG.error("关闭httpClient失败", e);
            }
        }
        return null;
    }


    /**
     * 聚光根据下载URL下载文件到本地
     *
     * @param url      下载URL
     * @param date     日期，用于构建文件名
     * @param fileName 文件名，用于替换默认的文件名部分
     * @return 返回文件的完整路径
     */
    public static String downloadExcel(Cjzh cjzh,String url, String date, String fileName) {
        CloseableHttpClient httpClient =null;
        if ("0".equals(cjzh.getIsAccount())) {
            // 品牌主账号使用自己的cookieStore
            httpClient = HttpClients.custom()
                    .setDefaultCookieStore(cjzh.getJgptcjzh().getCookieStore())
                    .build();
        } else {
            // 子账号使用关联的cookieStore
            httpClient = HttpClients.custom()
                    .setDefaultCookieStore(cjzh.getCookieStore())
                    .build();
        }
        HttpGet httpGet = new HttpGet(url);
        HttpResponse httpResponse = null;
        InputStream is = null;
        ByteArrayOutputStream output = null;
        FileOutputStream fos = null;
        try {
            RequestConfig.Builder requestConfig = RequestConfig.custom()
                    .setSocketTimeout(120000)
                    .setConnectTimeout(120000)
                    .setConnectionRequestTimeout(120000)
                    .setCookieSpec(CookieSpecs.NETSCAPE);
            httpGet.setConfig(requestConfig.build());
            HttpContext httpContext = new BasicHttpContext();
            httpResponse = httpClient.execute(httpGet, httpContext);
            HttpEntity httpEntity = httpResponse.getEntity();

            // 根据InputStream 下载文件
            is = httpEntity.getContent();
            String filePath = CommonConfig.getString("tempdir") + date + "_" + fileName;
            output = new ByteArrayOutputStream();
            byte[] buffer = new byte[4096];
            int r;
            while ((r = is.read(buffer)) > 0) {
                output.write(buffer, 0, r);
            }
            fos = new FileOutputStream(filePath);
            output.writeTo(fos);
            output.flush();

            EntityUtils.consume(httpEntity);
            return filePath;
        } catch (Exception e) {
            LOG.error("下载文件失败", e);
        } finally {
            try {
                if (is != null) {
                    is.close();
                }
                if (output != null) {
                    output.close();
                }
                if (fos != null) {
                    fos.close();
                }
                if (httpResponse != null) {
                    EntityUtils.consume(httpResponse.getEntity());
                }
                httpClient.close();
            } catch (Exception e) {
                LOG.error("关闭资源失败", e);
            }
        }
        return null;
    }


    public static String commitTask1(Cjzh cjzh, String startTime, String endTime) {
        CjLog.addTime("jgtgjh_http.commitTask1");
        CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cjzh.getCookieStore()).build();
        String url = "https://ark.xiaohongshu.com/api/ec_ads/long_task/submit";
        HttpPost httpPost = new HttpPost(url);

        try {
            RequestConfig requestConfig = RequestConfig.custom()
                    .setSocketTimeout(20000)
                    .setConnectTimeout(20000)
                    .setConnectionRequestTimeout(20000)
                    .setCookieSpec(CookieSpecs.NETSCAPE)
                    .build();
            httpPost.setConfig(requestConfig);

            // 设置请求头
            httpPost.setHeader("Accept", "application/json, text/plain, */*");
            httpPost.setHeader("Accept-Encoding", "gzip, deflate, br, zstd");
            httpPost.setHeader("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
            httpPost.setHeader("Content-Type", "application/json;charset=UTF-8");
            httpPost.setHeader("Origin", "https://ark.xiaohongshu.com");
            httpPost.setHeader("Referer", "https://ark.xiaohongshu.com/app-merchant/order-settle/service");
            httpPost.setHeader("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");

            // 设置请求体
            JSONObject jsonRequest = new JSONObject();
            jsonRequest.put("task_name", "ark_ec_ad_data_download_task");
            jsonRequest.put("module_name", "ark_data_center");
            jsonRequest.put("subsystem_alias", "ark");

            JSONObject input = new JSONObject();
            JSONObject extra = new JSONObject();
            extra.put("startTime", startTime);
            extra.put("endTime", endTime);
            JSONObject page = new JSONObject();
            page.put("pageIndex", "1");
            page.put("pageSize", "10");
            extra.put("page", page);
            extra.put("marketingTarget", "3");
            extra.put("marketingTargetList", Arrays.asList(3, 15));
            extra.put("columns", Arrays.asList(
                    "optimizeTarget", "bidding", "fee", "impression", "click", "follow", "interaction",
                    "goodsOrder", "rgmv", "roi", "successGoodsOrder", "purchaseOrderGmv7d", "purchaseOrderRoi7d",
                    "newSellerGoodsViewCnt", "newSellerPurchaseOrderNum7d", "newSellerPurchaseOrderGmv7d"
            ));
            extra.put("optimizeTargetList", new JSONArray());
            input.put("extra", extra);
            jsonRequest.put("input", input);

            StringEntity requestEntity = new StringEntity(jsonRequest.toString(), "UTF-8");
            httpPost.setEntity(requestEntity);

            HttpResponse httpResponse = httpClient.execute(httpPost);
            HttpEntity responseEntity = httpResponse.getEntity();
            String body = EntityUtils.toString(responseEntity);

            JSONObject jsonObject = JSONObject.parseObject(body);
            if (jsonObject.getBooleanValue("success") && jsonObject.getInteger("code") == 0) {
                JSONObject data = jsonObject.getJSONObject("data");
                if (data != null) {
                    String taskId = data.getString("task_id");
                    return taskId != null ? taskId : "";
                }
            }
        } catch (Exception e) {
            LOG.error("调用提交导出任务失败", e);
        } finally {
            if (httpPost != null) {
                httpPost.releaseConnection();
            }
            try {
                httpClient.close();
                TimeUnit.SECONDS.sleep(3);
            } catch (Exception e) {
                LOG.error("关闭httpClient失败", e);
            }
        }
        return null;
    }


    public static String getTaskFileUrl(Cjzh cjzh, String taskId) {
        CjLog.addTime("jgtgjh_http.getTaskFileUrl");
        CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cjzh.getCookieStore()).build();
        String url = "https://ark.xiaohongshu.com/api/edith/long_task/task/detail?task_id=" + taskId;
        HttpGet httpGet = new HttpGet(url);
        int retryCount = 0;
        final int maxRetries = 5;

        try {
            RequestConfig requestConfig = RequestConfig.custom()
                    .setSocketTimeout(20000)
                    .setConnectTimeout(20000)
                    .setConnectionRequestTimeout(20000)
                    .setCookieSpec(CookieSpecs.NETSCAPE)
                    .build();
            httpGet.setConfig(requestConfig);

            // 设置请求头
            httpGet.setHeader("Accept", "application/json, text/plain, */*");
            httpGet.setHeader("Accept-Encoding", "gzip, deflate, br, zstd");
            httpGet.setHeader("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
            httpGet.setHeader("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
            httpGet.setHeader("Authorization", cjzh.getAccessToken());
            httpGet.setHeader("Priority", "u=1, i");
            httpGet.setHeader("Referer", "https://ark.xiaohongshu.com/app-ad/ad/manage");
            httpGet.setHeader("Sec-Ch-Ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Google Chrome\";v=\"126\"");
            httpGet.setHeader("Sec-Ch-Ua-Mobile", "?0");
            httpGet.setHeader("Sec-Ch-Ua-Platform", "\"macOS\"");
            httpGet.setHeader("Sec-Fetch-Dest", "empty");
            httpGet.setHeader("Sec-Fetch-Mode", "cors");
            httpGet.setHeader("Sec-Fetch-Site", "same-origin");
            //httpGet.setHeader("X-B3-Traceid", "d1f869d9f80f6318");
            httpGet.setHeader("X-Subsystem", "ark");

            while (retryCount < maxRetries) {
                HttpResponse httpResponse = httpClient.execute(httpGet);
                HttpEntity responseEntity = httpResponse.getEntity();
                String body = EntityUtils.toString(responseEntity);

                JSONObject jsonObject = JSONObject.parseObject(body);
                if (jsonObject.getBooleanValue("success") && jsonObject.getInteger("code") == 0) {
                    JSONObject data = jsonObject.getJSONObject("data");
                    if (data != null) {
                        JSONObject task = data.getJSONObject("task");
                        if (task != null && task.getInteger("status") == 3) {
                            JSONObject result = task.getJSONObject("result");
                            if (result != null) {
                                String fileUrl = result.getString("file_url");
                                return fileUrl != null ? fileUrl : "";
                            }
                        }
                    }
                }

                retryCount++;
                TimeUnit.SECONDS.sleep(1); // 每次重试前等待1秒
            }
        } catch (Exception e) {
            LOG.error("获取任务详情失败", e);
        } finally {
            if (httpGet != null) {
                httpGet.releaseConnection();
            }
            try {
                httpClient.close();
            } catch (Exception e) {
                LOG.error("关闭httpClient失败", e);
            }
        }
        return null;
    }


    /**
     * 千帆下载URL下载文件到本地
     *
     * @param url      下载URL
     * @param date     日期，用于构建文件名
     * @param fileName 文件名，用于替换默认的文件名部分
     * @return 返回文件的完整路径
     */
    public static String downloadExcel1(Cjzh cjzh,String url, String date, String fileName) {
        CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cjzh.getCookieStore()).build();
        HttpGet httpGet = new HttpGet(url);
        HttpResponse httpResponse = null;
        InputStream is = null;
        ByteArrayOutputStream output = null;
        FileOutputStream fos = null;
        try {
            RequestConfig.Builder requestConfig = RequestConfig.custom()
                    .setSocketTimeout(120000)
                    .setConnectTimeout(120000)
                    .setConnectionRequestTimeout(120000)
                    .setCookieSpec(CookieSpecs.NETSCAPE);
            httpGet.setConfig(requestConfig.build());
            HttpContext httpContext = new BasicHttpContext();
            httpResponse = httpClient.execute(httpGet, httpContext);
            HttpEntity httpEntity = httpResponse.getEntity();

            // 根据InputStream 下载文件
            is = httpEntity.getContent();
            String filePath = CommonConfig.getString("tempdir") + date + "_" + fileName;
            output = new ByteArrayOutputStream();
            byte[] buffer = new byte[4096];
            int r;
            while ((r = is.read(buffer)) > 0) {
                output.write(buffer, 0, r);
            }
            fos = new FileOutputStream(filePath);
            output.writeTo(fos);
            output.flush();

            EntityUtils.consume(httpEntity);
            return filePath;
        } catch (Exception e) {
            LOG.error("下载文件失败", e);
        } finally {
            try {
                if (is != null) {
                    is.close();
                }
                if (output != null) {
                    output.close();
                }
                if (fos != null) {
                    fos.close();
                }
                if (httpResponse != null) {
                    EntityUtils.consume(httpResponse.getEntity());
                }
                httpClient.close();
            } catch (Exception e) {
                LOG.error("关闭资源失败", e);
            }
        }
        return null;
    }


    public static List<String> queryPlanList(Cjzh cjzh, String startTime, String endTime) {
        List<String> allDataList = new ArrayList<>();
        int pageNum = 1;
        int pageSize = 200;

        while (true) {
            List<String> dataList = queryPlanList1(cjzh, startTime, endTime, pageNum, pageSize);
            if (dataList == null) {
                return null;
            }
            if (dataList.isEmpty() || dataList.size() < pageSize) {
                allDataList.addAll(dataList);
                break;
            }
            allDataList.addAll(dataList);
            pageNum++;
        }

        return allDataList;
    }

    public static List<String> queryPlanList1(Cjzh cjzh, String startTime, String endTime, int pageNum, int pageSize) {
        CjLog.addTime("note_http.fetchCreativeList");
        String URL = "https://ad.xiaohongshu.com/api/leona/rtb/campaign/list";
        //CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cjzh.getJgptcjzh().getCookieStore()).build();
        CloseableHttpClient httpClient =null;
        if ("0".equals(cjzh.getIsAccount())) {
            // 品牌主账号使用自己的cookieStore
            httpClient = HttpClients.custom()
                    .setDefaultCookieStore(cjzh.getJgptcjzh().getCookieStore())
                    .build();
        } else {
            // 子账号使用关联的cookieStore
            httpClient = HttpClients.custom()
                    .setDefaultCookieStore(cjzh.getCookieStore())
                    .build();
        }

        HttpPost post = new HttpPost(URL);
        post.setHeader("Accept", "application/json, text/plain, */*");
        post.setHeader("Accept-Encoding", "gzip, deflate, br, zstd");
        post.setHeader("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
        post.setHeader("Content-Type", "application/json;charset=UTF-8");
        post.setHeader("Origin", "https://ad.xiaohongshu.com");
        post.setHeader("Referer", "https://ad.xiaohongshu.com/app-datacenter/new-goods-note");
        post.setHeader("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");

        JSONObject requestBody = new JSONObject();
        requestBody.put("startTime", startTime);
        requestBody.put("endTime", endTime);
        requestBody.put("pageNum", pageNum);
        requestBody.put("pageSize", pageSize);
        requestBody.put("source", "web");

        try {
            RequestConfig requestConfig = RequestConfig.custom()
                    .setSocketTimeout(20000)
                    .setConnectTimeout(20000)
                    .setConnectionRequestTimeout(20000)
                    .setCookieSpec(CookieSpecs.NETSCAPE)
                    .build();
            post.setConfig(requestConfig);

            post.setEntity(new StringEntity(requestBody.toJSONString(), StandardCharsets.UTF_8));

            HttpResponse response = httpClient.execute(post);
            HttpEntity entity = response.getEntity();
            String responseString = EntityUtils.toString(entity, StandardCharsets.UTF_8);
            JSONObject responseObject = JSONObject.parseObject(responseString);

            if (responseObject.getInteger("code") == 0 && responseObject.getBoolean("success")) {
                JSONObject dataObject = responseObject.getJSONObject("data");
                return parsePlanData(dataObject.getJSONArray("list"));
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                httpClient.close();
                TimeUnit.SECONDS.sleep(1);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return null;
    }

    private static List<String> parsePlanData(JSONArray dataArray) {
        List<String> resultList = new ArrayList<>();
        for (int i = 0; i < dataArray.size(); i++) {
            JSONObject jsonObject = dataArray.getJSONObject(i);
            String campaignId = jsonObject.getString("campaignId");
            resultList.add(campaignId);
        }
        return resultList;
    }


    public static List<JSONObject> queryCreativeList(Cjzh cjzh, long campaignId, String startTime, String endTime) {
        List<JSONObject> allDataList = new ArrayList<>();
        int pageNum = 1;
        int pageSize = 200;

        while (true) {
            List<JSONObject> dataList = fetchCreativeList(cjzh, campaignId, startTime, endTime, pageNum, pageSize);
            if (dataList == null) {
                return null;
            }
            if (dataList.isEmpty() || dataList.size() < pageSize) {
                allDataList.addAll(dataList);
                break;
            }
            allDataList.addAll(dataList);
            pageNum++;
        }

        return allDataList;
    }

    public static List<JSONObject> fetchCreativeList(Cjzh cjzh, long campaignId, String startTime, String endTime, int pageNum, int pageSize) {
        CjLog.addTime("note_http.fetchCreativeList");
        String URL = "https://ad.xiaohongshu.com/api/leona/rtb/creativity/list";
        //CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cjzh.getJgptcjzh().getCookieStore()).build();
        CloseableHttpClient httpClient =null;
        if ("0".equals(cjzh.getIsAccount())) {
            // 品牌主账号使用自己的cookieStore
            httpClient = HttpClients.custom()
                    .setDefaultCookieStore(cjzh.getJgptcjzh().getCookieStore())
                    .build();
        } else {
            // 子账号使用关联的cookieStore
            httpClient = HttpClients.custom()
                    .setDefaultCookieStore(cjzh.getCookieStore())
                    .build();
        }

        HttpPost post = new HttpPost(URL);
        post.setHeader("Accept", "application/json, text/plain, */*");
        post.setHeader("Accept-Encoding", "gzip, deflate, br, zstd");
        post.setHeader("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
        post.setHeader("Authorization", cjzh.getAccessToken());
        post.setHeader("Content-Type", "application/json;charset=UTF-8");
        post.setHeader("Origin", "https://ad.xiaohongshu.com");
        post.setHeader("Referer", "https://ad.xiaohongshu.com/app-datacenter/new-goods-note");
        post.setHeader("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");

        JSONObject requestBody = new JSONObject();
        requestBody.put("campaignId", campaignId);
        requestBody.put("startTime", startTime);
        requestBody.put("endTime", endTime);
        requestBody.put("pageNum", pageNum);
        requestBody.put("pageSize", pageSize);

        try {

            RequestConfig requestConfig = RequestConfig.custom()
                    .setSocketTimeout(20000)
                    .setConnectTimeout(20000)
                    .setConnectionRequestTimeout(20000)
                    .setCookieSpec(CookieSpecs.NETSCAPE)
                    .build();
            post.setConfig(requestConfig);

            post.setEntity(new StringEntity(requestBody.toJSONString(), StandardCharsets.UTF_8));

            HttpResponse response = httpClient.execute(post);
            HttpEntity entity = response.getEntity();
            String responseString = EntityUtils.toString(entity, StandardCharsets.UTF_8);
            JSONObject responseObject = JSONObject.parseObject(responseString);

            if (responseObject.getInteger("code") == 0 && responseObject.getBoolean("success")) {
                JSONObject dataObject = responseObject.getJSONObject("data");
                return parseCreativeData(dataObject.getJSONArray("list"));
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                httpClient.close();
                TimeUnit.SECONDS.sleep(1);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return null;
    }

    private static List<JSONObject> parseCreativeData(JSONArray dataArray) {
        List<JSONObject> resultList = new ArrayList<>();
        for (int i = 0; i < dataArray.size(); i++) {
            JSONObject jsonObject = dataArray.getJSONObject(i);
            JSONObject result = new JSONObject();
            result.put("campaignId", jsonObject.getLong("campaignId"));
            result.put("campaignEnable", jsonObject.getInteger("campaignEnable"));
            result.put("campaignDayBudget", jsonObject.getDoubleValue("campaignDayBudget"));
            result.put("campaignName", jsonObject.getString("campaignName"));
            result.put("noteId", jsonObject.getString("noteId"));
            result.put("itemId", jsonObject.getString("itemId"));
            result.put("creativityId", jsonObject.getLong("creativityId"));
            resultList.add(result);
        }
        return resultList;
    }



    public static boolean getTgStatus(Cjzh cjzh) {
        CjLog.addTime("qf_http.getTgStatus");
        //CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cjzh.getCookieStore()).build();
        CloseableHttpClient httpClient =null;
        if ("0".equals(cjzh.getIsAccount())) {
            // 品牌主账号使用自己的cookieStore
            httpClient = HttpClients.custom()
                    .setDefaultCookieStore(cjzh.getJgptcjzh().getCookieStore())
                    .build();
        } else {
            // 子账号使用关联的cookieStore
            httpClient = HttpClients.custom()
                    .setDefaultCookieStore(cjzh.getCookieStore())
                    .build();
        }

        String url = "https://ark.xiaohongshu.com/api/ec_ads/user/query_base_status";
        HttpGet httpGet = new HttpGet(url);

        try {
            RequestConfig requestConfig = RequestConfig.custom()
                    .setSocketTimeout(20000)
                    .setConnectTimeout(20000)
                    .setConnectionRequestTimeout(20000)
                    .setCookieSpec(CookieSpecs.NETSCAPE)
                    .build();
            httpGet.setConfig(requestConfig);

            // 设置请求头
            httpGet.setHeader("Accept", "application/json, text/plain, */*");
            httpGet.setHeader("Accept-Encoding", "gzip, deflate, br, zstd");
            httpGet.setHeader("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
            httpGet.setHeader("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
            httpGet.setHeader("Authorization", cjzh.getAccessToken());
            httpGet.setHeader("Priority", "u=1, i");
            httpGet.setHeader("Referer", "https://ark.xiaohongshu.com/app-ad/ad/manage");
            httpGet.setHeader("Sec-Ch-Ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Google Chrome\";v=\"126\"");
            httpGet.setHeader("Sec-Ch-Ua-Mobile", "?0");
            httpGet.setHeader("Sec-Ch-Ua-Platform", "\"macOS\"");
            httpGet.setHeader("Sec-Fetch-Dest", "empty");
            httpGet.setHeader("Sec-Fetch-Mode", "cors");
            httpGet.setHeader("Sec-Fetch-Site", "same-origin");
            httpGet.setHeader("X-Subsystem", "ark");

            HttpResponse httpResponse = httpClient.execute(httpGet);
            HttpEntity responseEntity = httpResponse.getEntity();
            String body = EntityUtils.toString(responseEntity, "utf-8");

            JSONObject jsonObject = JSONObject.parseObject(body);
            if (jsonObject.getBooleanValue("success") && jsonObject.getInteger("code") == 0) {
                JSONObject data = jsonObject.getJSONObject("data");
                if (data != null) {
                    boolean adAccount = data.getBooleanValue("adAccount");
                    int statusCode = data.getJSONObject("proAuditStatus").getIntValue("statusCode");

                    // 判断 adAccount 是否为 true，且 statusCode 是否为 300
                    if (adAccount && statusCode == 300) {
                        return true;
                    }
                }
            }
        } catch (Exception e) {
            LOG.error("获取推广权限失败", e);
        } finally {
            httpGet.releaseConnection();
            try {
                httpClient.close();
            } catch (Exception e) {
                LOG.error("关闭httpClient失败", e);
            }
        }
        return false;
    }


    public static JSONObject queryJgptbjOverall(Cjzh cjzh, String startDate, String endDate) {
//        CloseableHttpClient httpClient = HttpClients.custom()
//                .setDefaultCookieStore(cjzh.getJgptcjzh().getCookieStore())
//                .build();
        CloseableHttpClient httpClient = null;
        String url = "https://ad.xiaohongshu.com/api/leona/rtb/data/overall";
        HttpPost httpPost = null;
        // 根据账号类型决定使用哪个cookieStore
        if ("0".equals(cjzh.getIsAccount())) {
            // 品牌主账号使用自己的cookieStore
            httpClient = HttpClients.custom()
                    .setDefaultCookieStore(cjzh.getJgptcjzh().getCookieStore())
                    .build();
        } else {
            // 子账号使用关联的cookieStore
            httpClient = HttpClients.custom()
                    .setDefaultCookieStore(cjzh.getCookieStore())
                    .build();
        }
        try {
            // 构建POST请求
            httpPost = new HttpPost(url);
            httpPost.setHeader("Accept", "application/json, text/plain, */*");
            httpPost.setHeader("Accept-Encoding", "gzip, deflate, br, zstd");
            httpPost.setHeader("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
            httpPost.setHeader("Content-Type", "application/json;charset=UTF-8");
            httpPost.setHeader("Origin", "https://ad.xiaohongshu.com");
            httpPost.setHeader("Referer", "https://ad.xiaohongshu.com/aurora/ad/datareports-basic/note?uba_pre=18.aurora_account_datareports..*************&uba_ppre=18.aurora_note_datareports..*************&uba_index=21");
            httpPost.setHeader("Sec-Ch-Ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Google Chrome\";v=\"126\"");
            httpPost.setHeader("Sec-Ch-Ua-Mobile", "?0");
            httpPost.setHeader("Sec-Ch-Ua-Platform", "\"macOS\"");
            httpPost.setHeader("Sec-Fetch-Dest", "empty");
            httpPost.setHeader("Sec-Fetch-Mode", "cors");
            httpPost.setHeader("Sec-Fetch-Site", "same-origin");
            httpPost.setHeader("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");

            // 构建请求体
            String jsonBody = String.format("{\"source\":1,\"reportType\":\"NOTE\",\"startDate\":\"%s\",\"endDate\":\"%s\",\"filters\":[],\"topType\":0}",
                    startDate, endDate);
            StringEntity entity = new StringEntity(jsonBody, "UTF-8");
            httpPost.setEntity(entity);

            // 设置请求配置
            RequestConfig.Builder requestConfig = RequestConfig.custom()
                    .setSocketTimeout(20000)
                    .setConnectTimeout(20000)
                    .setConnectionRequestTimeout(20000)
                    .setCookieSpec(CookieSpecs.NETSCAPE);
            httpPost.setConfig(requestConfig.build());

            // 发送请求并处理响应
            HttpResponse httpResponse = httpClient.execute(httpPost);
            HttpEntity responseEntity = httpResponse.getEntity();
            String body = EntityUtils.toString(responseEntity);

            // 解析响应体
            JSONObject jsonObject = JSONObject.parseObject(body);
            if (jsonObject.getBooleanValue("success") && jsonObject.getInteger("code") == 0) {
                if (jsonObject.containsKey("data")) {
                    JSONObject dataObject = jsonObject.getJSONObject("data");
                    if (dataObject.containsKey("aggregationData")) {
                        JSONObject firstItem = dataObject.getJSONObject("aggregationData");
                        if (firstItem != null && !firstItem.isEmpty()) {
                            // 封装JgbjDp对象
                            JgbjDp jgbjDp = new JgbjDp();
                            jgbjDp.setJgbjzxf(firstItem.getDoubleValue("fee"));
                            jgbjDp.setJgbjzzx(firstItem.getIntValue("impression"));
                            jgbjDp.setJgbjzdj(firstItem.getIntValue("click"));
                            jgbjDp.setJgbjzdjl(Double.parseDouble(firstItem.getString("ctr").replace("%", "")));
                            jgbjDp.setJgbjpjdjjg(firstItem.getDoubleValue("acp"));

                            return  (JSONObject) JSONObject.toJSON(jgbjDp);
                        }
                    }
                }
            }
        } catch (IOException e) {
            LOG.error("获取聚光笔记推广店铺数据失败", e);
        } finally {
            if (httpPost != null) {
                httpPost.releaseConnection();
            }
        }
        return null;
    }

}
