package com.abujlb.zdcjbixhs.http;

import com.abujlb.util.StringUtil;
import com.abujlb.zdcjbixhs.cookie.Cjzh;
import com.abujlb.zdcjbixhs.log.CjLog;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.CookieSpecs; 
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.apache.log4j.Logger;

import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.concurrent.TimeUnit;


/**
 * 聚光平台账号切换http类
 */
public class JgptHttpClient {

    static final Logger LOG = Logger.getLogger(JgptHttpClient.class);

    /**
     * 查询子账号列表
     * @param cjzh 账号信息
     * @param pageSize 每页大小
     * @return JSONArray 子账号列表数据
     */
    public static JSONArray querySubAccountList(Cjzh cjzh, int pageSize) {
        int currentPage = 1;
        JSONArray jsonArray = new JSONArray();
        
        while (true) {
            JSONArray temp = querySubAccountList1(cjzh, currentPage, pageSize);
            if (temp == null) {
                return null;
            } else if (temp.isEmpty()) {
                break;
            }
            jsonArray.addAll(temp);
            if (temp.size() < pageSize) {
                break;
            }
            currentPage++;
            try {
                TimeUnit.SECONDS.sleep(1);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
        return jsonArray;
    }

    /**
     * 分页查询子账号列表
     * @param cjzh 账号信息
     * @param pageNo 页码
     * @param pageSize 每页大小
     * @return JSONArray 单页子账号数据
     */
    public static JSONArray querySubAccountList1(Cjzh cjzh, int pageNo, int pageSize) {
        CjLog.addTime("jgpt_http.querySubAccountList1");
        CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cjzh.getJgptcjzh().getCookieStore()).build();
        String url = String.format("https://ad.xiaohongshu.com/api/leona/brand/shadow/list?page=%d&page_size=%d", 
                pageNo, pageSize);
        HttpGet httpGet = null;
        
        try {
            httpGet = new HttpGet(url);
            // 设置请求头
            httpGet.setHeader("authority", "ad.xiaohongshu.com");
            httpGet.setHeader("accept", "application/json, text/plain, */*");
            httpGet.setHeader("accept-encoding", "gzip, deflate, br, zstd");
            httpGet.setHeader("accept-language", "zh-CN,zh;q=0.9,en;q=0.8");
            httpGet.setHeader("authorization", cjzh.getAccessToken());
            httpGet.setHeader("sec-ch-ua", "\"Google Chrome\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"");
            httpGet.setHeader("sec-ch-ua-mobile", "?0");
            httpGet.setHeader("sec-ch-ua-platform", "\"Windows\"");
            httpGet.setHeader("sec-fetch-dest", "empty");
            httpGet.setHeader("sec-fetch-mode", "cors");
            httpGet.setHeader("sec-fetch-site", "same-origin");
            httpGet.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");

            RequestConfig.Builder requestConfig = RequestConfig.custom()
                    .setSocketTimeout(20000)
                    .setConnectTimeout(20000)
                    .setConnectionRequestTimeout(20000)
                    .setCookieSpec(CookieSpecs.NETSCAPE);
            httpGet.setConfig(requestConfig.build());

            HttpResponse httpResponse = httpClient.execute(httpGet);
            HttpEntity responseEntity = httpResponse.getEntity();
            String body = EntityUtils.toString(responseEntity);

            if (!StringUtil.isJson(body)) {
                return null;
            }

            JSONObject jsonObject = JSONObject.parseObject(body);
            if (jsonObject.getBooleanValue("success") && jsonObject.getInteger("code") == 0) {
                if (jsonObject.containsKey("data")) {
                    JSONObject data = jsonObject.getJSONObject("data");
                    if (data.containsKey("shadows")) {
                        JSONArray shadows = data.getJSONArray("shadows");
                        // 如果数组不为空，移除第一个元素，去掉的主账号本身的店铺
                        if (shadows != null && !shadows.isEmpty()) {
                            shadows.remove(0);
                        }
                        return shadows;
                    }
                }
                return new JSONArray();
            }
        } catch (Exception e) {
            LOG.error("查询子账号列表失败", e);
        } finally {
            if (httpGet != null) {
                httpGet.releaseConnection();
            }
            try {
                httpClient.close();
            } catch (Exception e) {
                LOG.error("Failed to close httpClient", e);
            }
        }
        return null;
    }

    /**
     * 子账号登录
     * @param cjzh 账号信息
     * @param vSellerId 子账号ID
     */
    public static void shadowLogin(Cjzh cjzh, String vSellerId) {
        CjLog.addTime("jgpt_http.shadowLogin");
        CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cjzh.getJgptcjzh().getCookieStore()).build();
        String url = "https://ad.xiaohongshu.com/api/leona/brand/shadow_login";
        HttpPost httpPost = null;
        
        try {
            httpPost = new HttpPost(url);
            // 设置请求头
            httpPost.setHeader("authority", "ad.xiaohongshu.com");
            httpPost.setHeader("accept", "application/json, text/plain, */*");
            httpPost.setHeader("accept-encoding", "gzip, deflate, br, zstd");
            httpPost.setHeader("accept-language", "zh-CN,zh;q=0.9,en;q=0.8");
            httpPost.setHeader("content-type", "application/json;charset=UTF-8");
            httpPost.setHeader("authorization", cjzh.getAccessToken());
            httpPost.setHeader("origin", "https://ad.xiaohongshu.com");
            httpPost.setHeader("sec-ch-ua", "\"Google Chrome\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"");
            httpPost.setHeader("sec-ch-ua-mobile", "?0");
            httpPost.setHeader("sec-ch-ua-platform", "\"Windows\"");
            httpPost.setHeader("sec-fetch-dest", "empty");
            httpPost.setHeader("sec-fetch-mode", "cors");
            httpPost.setHeader("sec-fetch-site", "same-origin");
            httpPost.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");

            // 设置请求参数
            JSONObject requestBody = new JSONObject();
            requestBody.put("vSellerId", vSellerId);
            StringEntity entity = new StringEntity(requestBody.toJSONString(), "UTF-8");
            httpPost.setEntity(entity);

            // 设置请求配置
            RequestConfig.Builder requestConfig = RequestConfig.custom()
                    .setSocketTimeout(20000)
                    .setConnectTimeout(20000)
                    .setConnectionRequestTimeout(20000)
                    .setCookieSpec(CookieSpecs.NETSCAPE);
            httpPost.setConfig(requestConfig.build());

            // 执行请求
            HttpResponse httpResponse = httpClient.execute(httpPost);
            HttpEntity responseEntity = httpResponse.getEntity();
            String body = EntityUtils.toString(responseEntity);                                                                                                                                                                                                                                                                                                                                      

            if (!StringUtil.isJson(body)) {
                LOG.error("子账号登录失败：返回数据格式错误");
                return;
            }

            JSONObject jsonObject = JSONObject.parseObject(body);
            if (!jsonObject.getBooleanValue("success") || jsonObject.getInteger("code") != 0) {
                LOG.error("子账号登录失败：" + jsonObject.getString("msg"));
            }
        } catch (Exception e) {
            LOG.error("子账号登录失败", e);
        } finally {
            if (httpPost != null) {
                httpPost.releaseConnection();
            }
            try {
                httpClient.close();
            } catch (Exception e) {
                LOG.error("Failed to close httpClient", e);
            }
        }
    }

    /**
     * 退出子账号登录，回到主账号
     */
    public static void exitShadowLogin(Cjzh cjzh) {
        CloseableHttpClient httpClient = null;
        HttpPost httpPost = null;
        
        try {
            httpClient = HttpClients.custom()
                    .setDefaultCookieStore(cjzh.getJgptcjzh().getCookieStore())
                    .build();
                    
            String url = "https://ad.xiaohongshu.com/api/leona/login/return";
            httpPost = new HttpPost(url);
            
            // 设置请求头
            httpPost.setHeader("Accept", "application/json, text/plain, */*");
            httpPost.setHeader("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
            httpPost.setHeader("Content-Type", "application/json;charset=UTF-8");
            httpPost.setHeader("Origin", "https://ad.xiaohongshu.com");
            httpPost.setHeader("Referer", "https://ad.xiaohongshu.com/aurora/ad/datareports-basic/note");
            httpPost.setHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/132.0.0.0 Safari/537.36");
            
            // 设置请求体 - 空对象
            StringEntity entity = new StringEntity("{}", "UTF-8");
            httpPost.setEntity(entity);
            
            // 发送请求
            HttpResponse response = httpClient.execute(httpPost);
            String responseBody = EntityUtils.toString(response.getEntity());
            
            // 检查响应
            if (!StringUtil.isJson(responseBody)) {
                LOG.error("退出子账号失败：返回数据格式错误");
                return;
            }
            
            JSONObject jsonResponse = JSONObject.parseObject(responseBody);
            if (!jsonResponse.getBooleanValue("success")) {
                LOG.error("退出子账号失败：" + jsonResponse.getString("message"));
            }
            
        } catch (Exception e) {
            LOG.error("退出子账号请求异常", e);
        } finally {
            if (httpPost != null) {
                httpPost.releaseConnection();
            }
            if (httpClient != null) {
                try {
                    httpClient.close();
                } catch (IOException e) {
                    LOG.error("关闭httpClient失败", e);
                }
            }
        }
    }

}
