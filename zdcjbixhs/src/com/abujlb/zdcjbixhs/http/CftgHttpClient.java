package com.abujlb.zdcjbixhs.http;

import com.abujlb.util.StringUtil;
import com.abujlb.zdcjbixhs.cookie.Cjzh;
import com.abujlb.zdcjbixhs.util.XsHeadersUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.CookieStore;
import org.apache.http.client.config.CookieSpecs;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.cookie.Cookie;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 乘风采集http客户端
 * 用于获取乘风平台的推广数据
 */
public class CftgHttpClient {

    private static final Logger LOG = LoggerFactory.getLogger(CftgHttpClient.class);
    //乘风平台的推广数据接口URL
    private static final String API_URL = "https://chengfeng.xiaohongshu.com/api/wind/data/report";
    private static final String X_S_URL = "/api/wind/data/report";
    private static final int DEFAULT_PAGE_SIZE = 10;
    private static final int MAX_RETRY_TIMES = 3; // 最大重试次数
    private static final int RETRY_INTERVAL = 3000; // 重试间隔时间(毫秒)

    /**
     * 数据源类型枚举
     */
    public enum DataSourceType {
        ACCOUNT("account", false),
        SPU("spu", true),
        NOTE("note", true);

        private final String value;
        private final boolean needsPaging;

        DataSourceType(String value, boolean needsPaging) {
            this.value = value;
            this.needsPaging = needsPaging;
        }

        public String getValue() {
            return value;
        }

        public boolean isNeedsPaging() {
            return needsPaging;
        }
    }

    /**
     * 查询商品推广报表数据
     *
     * @param cjzh 采集账号对象
     * @param marketingTargetValue 营销目标值
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param dataSourceType 数据源类型
     * @return JSONArray 数据列表，获取失败返回null
     */
    public static JSONArray querySpTgReport(Cjzh cjzh, int marketingTargetValue, String startTime,
                                            String endTime, DataSourceType dataSourceType) {
        Objects.requireNonNull(cjzh, "采集账号不能为空");
        Objects.requireNonNull(startTime, "开始时间不能为空");
        Objects.requireNonNull(endTime, "结束时间不能为空");
        Objects.requireNonNull(dataSourceType, "数据源类型不能为空");

        return dataSourceType.isNeedsPaging() ?
                queryWithPaging(cjzh, startTime, endTime, marketingTargetValue, dataSourceType) :
                retryQuerySpTgReport2(cjzh, startTime, endTime, marketingTargetValue, 1, DEFAULT_PAGE_SIZE, dataSourceType.getValue());
    }

    /**
     * 分页查询数据
     */
    private static JSONArray queryWithPaging(Cjzh cjzh, String startTime, String endTime,
                                             int marketingTargetValue, DataSourceType dataSourceType) {
        JSONArray resultArray = new JSONArray();
        int pageNum = 1;

        while (true) {
            JSONArray pageData = retryQuerySpTgReport2(cjzh, startTime, endTime, marketingTargetValue,
                    pageNum, DEFAULT_PAGE_SIZE, dataSourceType.getValue());

            if (pageData == null) {
                LOG.error("查询分页数据失败，页码：{}", pageNum);
                return null;
            }

            if (pageData.isEmpty()) {
                break;
            }

            resultArray.addAll(pageData);

            if (pageData.size() < DEFAULT_PAGE_SIZE) {
                break;
            }

            // 添加分 页查询间隔
            try {
                TimeUnit.SECONDS.sleep(3);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }


            pageNum++;
        }

        return resultArray;
    }

    /**
     * 执行单次查询请求
     * @param cjzh 采集账号对象
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param marketingTargetValue 营销目标值
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param dataSource 数据源类型
     * @return JSONArray 查询结果数据
     */
    private static JSONArray querySpTgReport1(Cjzh cjzh, String startDate, String endDate,
                                              int marketingTargetValue, int pageNum, int pageSize, String dataSource) {
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            // 构建请求参数
            JSONObject params = new JSONObject();
            params.put("startDate", startDate);
            params.put("endDate", endDate);
            params.put("webModule", "base_report_page");
            params.put("dataSource", dataSource);
            params.put("timeUnit", "DAY");
            params.put("dataPattern", "table");
            params.put("pageNum", pageNum);
            params.put("pageSize", pageSize);

            // 设置筛选条件
            JSONObject filter = new JSONObject();
            filter.put("column", "marketingTarget");
            filter.put("operator", "in");
            filter.put("values", new int[]{marketingTargetValue});
            params.put("filters", new JSONObject[]{filter});

            // 设置数据列
            params.put("columns", getColumns(dataSource));

            // 构建cookie数组
            JSONArray cookies = new JSONArray();

            // 根据账号类型决定使用哪个cookieStore
            CookieStore cookieStore = null;
            if ("0".equals(cjzh.getIsAccount())) {
                // 品牌主账号使用自己的cookieStore
                 cookieStore = cjzh.getJgptcjzh().getCookieStore();
            } else {
                // 子账号使用关联的cookieStore
                 cookieStore =  cjzh.getCookieStore();
            }
            //CookieStore cookieStore =  cjzh.getJgptcjzh().getCookieStore();
            String[] requiredCookies = {
                "access-token-chengfeng.xiaohongshu.com",
                "customerClientId",
                "x-user-id-chengfeng.xiaohongshu.com",
                "customer-sso-sid",
                "gid",
                "sec_poison_id",
                "websectiga",
                "webId",
                "a1",
                "xsecappid",
                "acw_tc",
                    "ad.wind.v.seller.id",
                    "ares.beaker.session.id"
            };

            // 从CookieStore中提取所需的cookie
            for (Cookie cookie : cookieStore.getCookies()) {
                if (Arrays.asList(requiredCookies).contains(cookie.getName())) {
                    JSONObject cookieObj = new JSONObject();
                    cookieObj.put("domain", cookie.getDomain());
                    cookieObj.put("name", cookie.getName());
                    cookieObj.put("path", cookie.getPath());
                    cookieObj.put("value", cookie.getValue());
                    cookies.add(cookieObj);
                }
            }

            // 构建请求体
            JSONObject requestBody = new JSONObject();
            requestBody.put("api", X_S_URL);
            requestBody.put("params", params);
            requestBody.put("cookie", cookies);

            // 创建POST请求
            HttpPost httpPost = new HttpPost("http://101.132.148.179/xhs_params/get_params");
            httpPost.setHeader("Content-Type", "application/json");

            // 设置请求超时
            RequestConfig requestConfig = RequestConfig.custom()
                    .setSocketTimeout(20000)
                    .setConnectTimeout(20000)
                    .setConnectionRequestTimeout(20000)
                    .setCookieSpec(CookieSpecs.NETSCAPE)
                    .build();
            httpPost.setConfig(requestConfig);

            // 设置请求体
            StringEntity entity = new StringEntity(requestBody.toJSONString(), StandardCharsets.UTF_8);
            httpPost.setEntity(entity);

            // 执行请求并获取响应
            HttpResponse httpResponse = httpClient.execute(httpPost);
            HttpEntity responseEntity = httpResponse.getEntity();
            String body = EntityUtils.toString(responseEntity);

            if (!StringUtil.isJson(body)) {
                LOG.error("响应数据不是有效的JSON格式");
                return null;
            }

            JSONObject jsonObject = JSONObject.parseObject(body);

            // 处理响应数据（保持原有的处理逻辑）
            return processResponse(jsonObject, dataSource);

        } catch (Exception e) {
            LOG.error("获取乘风商品推广数据出错", e);
            return null;
        }
    }

    /**
     * 处理响应数据
     */
    private static JSONArray processResponse(JSONObject responseJson, String dataSource) {
        if (!isValidResponse(responseJson)) {
            return null;
        }

        JSONObject data = responseJson.getJSONObject("data");
        if (data == null) {
            LOG.warn("响应数据为空");
            return null;
        }

        return "account".equals(dataSource) ?
                processAccountData(data) :
                processNormalData(data, dataSource);
    }

    /**
     * 验证响应是否有效
     */
    private static boolean isValidResponse(JSONObject responseJson) {
        if (responseJson == null) {
            LOG.warn("响应数据解析失败");
            return false;
        }

        //boolean isSuccess = responseJson.getBoolean("success");
        int code = responseJson.getInteger("code");

        if ( code != 0) {
            LOG.warn("响应状态异常: success={}, code={}", code);
            return false;
        }

        return true;
    }

    /**
     * 处理账户级别数据
     */
    private static JSONArray processAccountData(JSONObject data) {
        String totalDataStr = data.getString("totalData");
        if (totalDataStr == null || totalDataStr.isEmpty()) {
            LOG.warn("账户总数据为空");
            return new JSONArray();
        }

        try {
            JSONObject totalData = JSON.parseObject(totalDataStr);
            JSONObject result = new JSONObject();

            String[] fields = {"fee", "impression", "ctr", "click", "cpm", "acp", "newSellerDealOrderGmv7d"};
            for (String field : fields) {
                result.put(field, totalData.getString(field));
            }

            JSONArray resultArray = new JSONArray();
            resultArray.add(result);
            return resultArray;
        } catch (Exception e) {
            LOG.error("处理账户数据出错", e);
            return null;
        }
    }

    /**
     * 处理普通数据（SPU和Note）
     */
    private static JSONArray processNormalData(JSONObject data, String dataSource) {
        JSONArray dataList = data.getJSONArray("dataList");
        if (dataList == null || dataList.isEmpty()) {
            return new JSONArray();
        }

        JSONArray resultArray = new JSONArray();
        for (int i = 0; i < dataList.size(); i++) {
            JSONObject dataItem = dataList.getJSONObject(i);
            JSONObject result = extractData(dataItem, dataSource);
            if (result != null) {
                resultArray.add(result);
            }
        }

        return resultArray;
    }

    /**
     * 提取数据项
     */
    private static JSONObject extractData(JSONObject dataItem, String dataSource) {
        String dataValueJsonStr = dataItem.getString("dataValueJson");
        if (dataValueJsonStr == null) {
            return null;
        }

        try {
            JSONObject dataValueJson = JSON.parseObject(dataValueJsonStr);
            JSONObject result = new JSONObject();

            // 提取基础数据
            String[] baseFields = {"fee", "impression", "ctr", "click", "cpm", "acp", "newSellerDealOrderGmv7d"};
            for (String field : baseFields) {
                result.put(field, dataValueJson.getString(field));
            }

            // 根据数据源类型添加特定字段
            switch (dataSource) {
                case "spu":
                    result.put("spuId", dataItem.getString("spuId"));
                    result.put("spuName", dataItem.getString("spuName"));
                    break;
                case "note":
                    result.put("noteId", dataItem.getString("noteId"));
                    result.put("noteTitle", dataItem.getString("noteTitle"));
                    break;
            }

            return result;
        } catch (Exception e) {
            LOG.error("数据提取错误", e);
            return null;
        }
    }


    /**
     * 获取数据列定义
     */
    private static String[] getColumns(String dataSource) {
        switch (dataSource) {
            case "spu":
                return new String[]{"time", "spuId", "spuName", "fee", "impression", "ctr", "click",
                        "cpm", "acp", "newSellerDealOrderGmv7d"};
            case "note":
                return new String[]{"time", "noteId", "noteTitle", "noteImage", "noteJumpUrl", "fee",
                        "impression", "ctr", "click", "cpm", "acp", "newSellerDealOrderGmv7d"};
            default: // account
                return new String[]{"time", "fee", "impression", "ctr", "click", "newSellerDealOrderGmv7d", "acp", "cpm",
                        "liveDirectPurchaseOrderNum24h", "liveDirectPurchaseOrderNum24hCost", "liveDirectPurchaseOrderGmv24h", "liveDirectPurchaseOrderRoi24h",
                        "liveDirectDealOrderNum24h", "liveDirectDealOrderNum24hCost", "liveDirectDealOrderGmv24h", "liveDirectDealOrderRoi24h",
                        "liveWatchCnt", "liveWatchCntCost", "liveWatchDurationAvg", "liveFollowCnt", "live5sWatchCnt", "live5sWatchCntCost",
                        "liveCmtCnt", "live30sWatchCnt", "live30sWatchCntCost", "totalOrderNum7d", "totalOrderNum7dCost", "totalOrderGmv7d",
                        "totalOrderRoi7d", "dealOrderNum7d", "dealOrderNum7dCost", "dealOrderGmv7d", "dealOrderRoi7d", "dealOrderCvr7d"};
        }
    }



    public static Map<String, String> getCfXs(String api, JSONObject params,String a1) {
        Map<String, String> resultMap = new HashMap<>();
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            // 构造请求 URL
            String url = "http://101.132.148.179/xhs_params/get_params";
            HttpPost httpPost = new HttpPost(url);
            httpPost.setHeader("Content-Type", "application/json");

            // 构造请求体
            JSONObject requestBody = new JSONObject();
            requestBody.put("api", api);
            requestBody.put("params", params);
            requestBody.put("a1", a1);
            StringEntity entity = new StringEntity(requestBody.toJSONString(), "UTF-8");
            httpPost.setEntity(entity);

            // 执行请求
            HttpResponse httpResponse = httpClient.execute(httpPost);

            // 处理响应
            int statusCode = httpResponse.getStatusLine().getStatusCode();
            if (statusCode == 200) {
                String responseString = EntityUtils.toString(httpResponse.getEntity(), "UTF-8");
                JSONObject jsonResponse = JSON.parseObject(responseString);

                // 解析响应数据
                if (jsonResponse.getIntValue("code") == 0) {
                    JSONObject data = jsonResponse.getJSONObject("data");
                    String xS = data.getString("X-s");
                    String xT = data.getString("X-t");

                    // 将解析结果放入 Map
                    resultMap.put("X-s", xS);
                    resultMap.put("X-t", xT);
                } else {
                    throw new RuntimeException("API returned an error: " + jsonResponse.getString("message"));
                }
            } else {
                throw new RuntimeException("HTTP request failed with code: " + statusCode);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return resultMap;
    }

    /**
     * 添加新的重试方法
     */
    private static JSONArray retryQuerySpTgReport1(Cjzh cjzh, String startDate, String endDate,
                                                   int marketingTargetValue, int pageNum, int pageSize, String dataSource) {
        int retryCount = 0;
        JSONArray result = null;
        Exception lastException = null;
        
        // 记录查询参数，便于日志排查
        String queryInfo = String.format("查询参数[dataSource=%s, startDate=%s, endDate=%s, marketingTarget=%d, page=%d]", 
                dataSource, startDate, endDate, marketingTargetValue, pageNum);
        
        do {
            try {
                // 记录开始查询的日志
                if (retryCount > 0) {
                    LOG.info("开始第{}次重试 {}", retryCount, queryInfo);
                }
                
                result = querySpTgReport1(cjzh, startDate, endDate, marketingTargetValue, 
                        pageNum, pageSize, dataSource);
                
                // 验证结果有效性
                if (result != null) {
                    if (retryCount > 0) {
                        LOG.info("在第{}次重试后成功获取数据，获取到{}条记录", retryCount, result.size());
                    }
                    return result;
                }
                
                retryCount++;
                if (retryCount < MAX_RETRY_TIMES) {
                    // 使用指数退避策略计算等待时间
                    int waitTime = RETRY_INTERVAL * (1 << (retryCount - 1)); // 2^(retryCount-1) * 基础间隔
                    LOG.warn("第{}次查询返回null，准备进行第{}次重试，等待{}毫秒。{}", 
                            retryCount, retryCount + 1, waitTime, queryInfo);
                    Thread.sleep(waitTime);
                }
                
            } catch (InterruptedException e) {
                LOG.warn("重试等待被中断 {}", queryInfo, e);
                Thread.currentThread().interrupt();
                break;
            } catch (Exception e) {
                lastException = e;
                retryCount++;
                if (retryCount < MAX_RETRY_TIMES) {
                    // 使用指数退避策略
                    int waitTime = RETRY_INTERVAL * (1 << (retryCount - 1));
                    LOG.warn("第{}次查询发生异常，准备进行第{}次重试，等待{}毫秒。{}。异常信息: {}", 
                            retryCount, retryCount + 1, waitTime, queryInfo, e.getMessage());
                    try {
                        Thread.sleep(waitTime);
                    } catch (InterruptedException ie) {
                        LOG.warn("重试等待被中断 {}", queryInfo, ie);
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
        } while (retryCount < MAX_RETRY_TIMES);
        
        // 所有重试都失败后，提供更详细的错误信息
        if (lastException != null) {
            LOG.error("查询失败，已重试{}次，最后一次异常：{}。{}", MAX_RETRY_TIMES, lastException.getMessage(), queryInfo, lastException);
        } else {
            LOG.error("查询失败，已重试{}次，均返回null。{}", MAX_RETRY_TIMES, queryInfo);
        }
        return null;
    }

    /**
     * 使用直接HTTP请求方式查询商品推广报表数据（带重试机制）
     *
     * @param cjzh 采集账号对象
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param marketingTargetValue 营销目标值
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param dataSource 数据源类型
     * @return JSONArray 数据列表，获取失败返回null
     */
    private static JSONArray retryQuerySpTgReport2(Cjzh cjzh, String startDate, String endDate,
                                                  int marketingTargetValue, int pageNum, int pageSize, String dataSource) {
        int retryCount = 0;
        JSONArray result = null;
        Exception lastException = null;
        
        // 记录查询参数，便于日志排查
        String queryInfo = String.format("查询参数[dataSource=%s, startDate=%s, endDate=%s, marketingTarget=%d, page=%d]", 
                dataSource, startDate, endDate, marketingTargetValue, pageNum);
        
        do {
            try {
                // 记录开始查询的日志
                if (retryCount > 0) {
                    LOG.info("开始第{}次重试直接HTTP请求 {}", retryCount, queryInfo);
                }
                
                result = querySpTgReportDirect(cjzh, startDate, endDate, marketingTargetValue, 
                        pageNum, pageSize, dataSource);
                
                // 验证结果有效性
                if (result != null) {
                    if (retryCount > 0) {
                        LOG.info("在第{}次重试后成功获取数据，获取到{}条记录", retryCount, result.size());
                    }
                    return result;
                }
                
                retryCount++;
                if (retryCount < MAX_RETRY_TIMES) {
                    // 使用指数退避策略计算等待时间
                    int waitTime = RETRY_INTERVAL * (1 << (retryCount - 1)); // 2^(retryCount-1) * 基础间隔
                    LOG.warn("第{}次直接HTTP请求返回null，准备进行第{}次重试，等待{}毫秒。{}", 
                            retryCount, retryCount + 1, waitTime, queryInfo);
                    Thread.sleep(waitTime);
                }
                
            } catch (InterruptedException e) {
                LOG.warn("重试等待被中断 {}", queryInfo, e);
                Thread.currentThread().interrupt();
                break;
            } catch (Exception e) {
                lastException = e;
                retryCount++;
                if (retryCount < MAX_RETRY_TIMES) {
                    // 使用指数退避策略
                    int waitTime = RETRY_INTERVAL * (1 << (retryCount - 1));
                    LOG.warn("第{}次直接HTTP请求发生异常，准备进行第{}次重试，等待{}毫秒。{}。异常信息: {}", 
                            retryCount, retryCount + 1, waitTime, queryInfo, e.getMessage());
                    try {
                        Thread.sleep(waitTime);
                    } catch (InterruptedException ie) {
                        LOG.warn("重试等待被中断 {}", queryInfo, ie);
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
        } while (retryCount < MAX_RETRY_TIMES);
        
        // 所有重试都失败后，提供更详细的错误信息
        if (lastException != null) {
            LOG.error("直接HTTP请求查询失败，已重试{}次，最后一次异常：{}。{}", MAX_RETRY_TIMES, lastException.getMessage(), queryInfo, lastException);
        } else {
            LOG.error("直接HTTP请求查询失败，已重试{}次，均返回null。{}", MAX_RETRY_TIMES, queryInfo);
        }
        return null;
    }

    /**
     * 使用直接HTTP请求方式执行单次查询
     *
     * @param cjzh 采集账号对象
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param marketingTargetValue 营销目标值
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param dataSource 数据源类型
     * @return JSONArray 查询结果数据
     */
    private static JSONArray querySpTgReportDirect(Cjzh cjzh, String startDate, String endDate,
                                                  int marketingTargetValue, int pageNum, int pageSize, String dataSource) {

        CloseableHttpClient httpClient = null;
        httpClient = HttpClients.custom()
                .setDefaultCookieStore(cjzh.getCookieStore())
                .build();
        HttpPost httpPost = null;
        
        try {
            // 构建请求参数
            JSONObject params = new JSONObject();
            params.put("startDate", startDate);
            params.put("endDate", endDate);
            params.put("webModule", "base_report_page");
            params.put("dataSource", dataSource);
            params.put("timeUnit", "DAY");
            params.put("dataPattern", "table");
            params.put("pageNum", pageNum);
            params.put("pageSize", pageSize);

            // 设置筛选条件
            JSONObject filter = new JSONObject();
            filter.put("column", "marketingTarget");
            filter.put("operator", "in");
            filter.put("values", new int[]{marketingTargetValue});
            params.put("filters", new JSONObject[]{filter});

            // 设置数据列
            params.put("columns", getColumns(dataSource));
            
            // 从CookieStore中获取a1值
            String a1 = "";
            CookieStore cookieStore = null;
            cookieStore = cjzh.getCookieStore();
            
            // 查找a1 cookie
            for (Cookie cookie : cookieStore.getCookies()) {
                if ("a1".equals(cookie.getName())) {
                    a1 = cookie.getValue();
                    break;
                }
            }
            
            // 获取X-s和X-t请求头
            Map<String, String> xsHeaders = XsHeadersUtil.generateXsHeaders(X_S_URL, params, a1);
            if (xsHeaders == null || !xsHeaders.containsKey("X-s") || !xsHeaders.containsKey("X-t")) {
                LOG.error("获取X-s和X-t请求头失败");
                return null;
            }
            
            // 创建POST请求
            httpPost = new HttpPost(API_URL);
            
            // 设置请求头
            httpPost.setHeader("authority", "chengfeng.xiaohongshu.com");
            httpPost.setHeader("accept", "application/json, text/plain, */*");
            httpPost.setHeader("accept-encoding", "gzip, deflate, br, zstd");
            httpPost.setHeader("accept-language", "zh-CN,zh;q=0.9,en;q=0.8");
            httpPost.setHeader("content-type", "application/json;charset=UTF-8");
            httpPost.setHeader("origin", "https://chengfeng.xiaohongshu.com");
            httpPost.setHeader("referer", "https://chengfeng.xiaohongshu.com/cf/ad/base-data");
            httpPost.setHeader("sec-ch-ua", "\"Not A(Brand\";v=\"8\", \"Chromium\";v=\"132\", \"Google Chrome\";v=\"132\"");
            httpPost.setHeader("sec-ch-ua-mobile", "?0");
            httpPost.setHeader("sec-ch-ua-platform", "\"Windows\"");
            httpPost.setHeader("sec-fetch-dest", "empty");
            httpPost.setHeader("sec-fetch-mode", "cors");
            httpPost.setHeader("sec-fetch-site", "same-origin");
            httpPost.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
            httpPost.setHeader("X-s", xsHeaders.get("X-s"));
            httpPost.setHeader("X-t", xsHeaders.get("X-t"));
            
            // 设置请求体
            StringEntity entity = new StringEntity(params.toJSONString(), StandardCharsets.UTF_8);
            httpPost.setEntity(entity);
            
            // 设置请求配置
            RequestConfig requestConfig = RequestConfig.custom()
                    .setSocketTimeout(20000)
                    .setConnectTimeout(20000)
                    .setConnectionRequestTimeout(20000)
                    .setCookieSpec(CookieSpecs.NETSCAPE)
                    .build();
            httpPost.setConfig(requestConfig);
            
            // 执行请求
            HttpResponse httpResponse = httpClient.execute(httpPost);
            HttpEntity responseEntity = httpResponse.getEntity();
            String body = EntityUtils.toString(responseEntity);
            
            if (!StringUtil.isJson(body)) {
                LOG.error("响应数据不是有效的JSON格式");
                return null;
            }
            
            JSONObject jsonObject = JSONObject.parseObject(body);
            
            // 处理响应数据
            return processResponse(jsonObject, dataSource);
            
        } catch (Exception e) {
            LOG.error("直接HTTP请求获取乘风商品推广数据出错", e);
            return null;
        } finally {
            if (httpPost != null) {
                httpPost.releaseConnection();
            }
            try {
                httpClient.close();
            } catch (Exception e) {
                LOG.error("关闭HttpClient失败", e);
            }
        }
    }

}