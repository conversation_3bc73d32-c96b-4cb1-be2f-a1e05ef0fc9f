package com.abujlb.zdcjbixhs.http;

import com.abujlb.CommonConfig;
import com.abujlb.util.StringUtil;
import com.abujlb.zdcjbixhs.cookie.Cjzh;
import com.abujlb.zdcjbixhs.log.CjLog;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.CookieSpecs;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.protocol.BasicHttpContext;
import org.apache.http.protocol.HttpContext;
import org.apache.http.util.EntityUtils;
import org.apache.log4j.Logger;

import java.io.ByteArrayOutputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.time.LocalDate;
import java.util.Date;
import java.util.concurrent.TimeUnit;


/**
 * 订单明细http调用类
 */

public class OrderHttpClient {

    static final Logger LOG = Logger.getLogger(OrderHttpClient.class);


    /**
     * 分页查询资金-贷款资金支付宝微信类型
     *
     * @param cjzh              Cjzh对象，包含Cookie信息
     * @param fundType          资金类型
     * @param creditedStartTime 开始时间
     * @param creditedEndTime   结束时间
     * @return 返回包含所有分页数据的JSONArray
     */
    public static JSONArray queryStatementInRecords(Cjzh cjzh, String fundType, String creditedStartTime, String creditedEndTime) {
        JSONArray resultArray = new JSONArray();
        int pageNum = 1;
        int pageSize = 50;

        while (true) {
            JSONArray pageData = fetchData(cjzh, fundType, pageNum, pageSize, creditedStartTime, creditedEndTime);
            if (pageData == null) {
                return null;
            } else if (pageData.isEmpty()) {
                break;
            }

            resultArray.addAll(pageData);

            if (pageData.size() < pageSize) {
                break;
            }

            pageNum++;
        }

        return resultArray;
    }


    private static JSONArray fetchData(Cjzh cjzh, String fundType, int pageNum, int pageSize, String creditedStartTime, String creditedEndTime) {
        CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cjzh.getCookieStore()).build();
        String url = "https://ark.xiaohongshu.com/api/suez/finance/accountforweb/queryStatementInRecords";
        HttpPost httpPost = new HttpPost(url);
        try {
            // 设置请求头
            setHeaders(httpPost);

            // 构建请求体，包含fundType, pageNum, pageSize, creditedStartTime和creditedEndTime
            String param = String.format("{\"fundType\":\"%s\",\"pageNum\":%d,\"pageSize\":%d,\"creditedStartTime\":\"%s\",\"creditedEndTime\":\"%s\"}",
                    fundType, pageNum, pageSize, creditedStartTime, creditedEndTime);
            StringEntity entity = new StringEntity(param, "UTF-8");
            httpPost.setEntity(entity);

            RequestConfig requestConfig = RequestConfig.custom()
                    .setSocketTimeout(20000)
                    .setConnectTimeout(20000)
                    .setConnectionRequestTimeout(20000)
                    .setCookieSpec(CookieSpecs.NETSCAPE)
                    .build();
            httpPost.setConfig(requestConfig);

            HttpResponse httpResponse = httpClient.execute(httpPost);
            HttpEntity responseEntity = httpResponse.getEntity();
            String body = EntityUtils.toString(responseEntity);

            if (!StringUtil.isJson(body)) {
                return null;
            }

            JSONObject jsonObject = JSONObject.parseObject(body);
            if (jsonObject.getBooleanValue("success") && jsonObject.getInteger("statusCode") == 200) {
                JSONObject data = jsonObject.getJSONObject("data");
                if (data != null) {
                    return data.getJSONArray("list");
                }
                return new JSONArray();
            }

        } catch (Exception e) {
            LOG.error("查询小红书贷款资金列表失败", e);
        } finally {
            if (httpPost != null) {
                httpPost.releaseConnection();
            }
            try {
                httpClient.close();
            } catch (Exception e) {
                LOG.error("关闭httpClient失败", e);
            }
        }
        return null;
    }

    private static void setHeaders(HttpPost httpPost) {
        httpPost.setHeader("authority", "ark.xiaohongshu.com");
        httpPost.setHeader("method", "POST");
        httpPost.setHeader("path", "/api/suez/finance/accountforweb/queryStatementInRecords");
        httpPost.setHeader("scheme", "https");
        httpPost.setHeader("accept", "application/json, text/plain, */*");
        httpPost.setHeader("accept-encoding", "gzip, deflate, br, zstd");
        httpPost.setHeader("accept-language", "zh-CN,zh;q=0.9,en;q=0.8");
        httpPost.setHeader("content-type", "application/json;charset=UTF-8");
        httpPost.setHeader("origin", "https://ark.xiaohongshu.com");
        httpPost.setHeader("referer", "https://ark.xiaohongshu.com/app-merchant/third-settle");
        httpPost.setHeader("sec-ch-ua", "\"Google Chrome\";v=\"125\", \"Not.A/Brand\";v=\"24\", \"Chromium\";v=\"125\"");
        httpPost.setHeader("sec-ch-ua-mobile", "?0");
        httpPost.setHeader("sec-ch-ua-platform", "\"macOS\"");
        httpPost.setHeader("sec-fetch-dest", "empty");
        httpPost.setHeader("sec-fetch-mode", "cors");
        httpPost.setHeader("sec-fetch-site", "same-origin");
        httpPost.setHeader("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
    }


    /**
     * 分页查询资金-贷款资金店铺余额
     *
     * @param cjzh      Cjzh对象，包含Cookie信息
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 返回包含所有分页数据的JSONArray
     */
    public static JSONArray queryAccountRecords(Cjzh cjzh, String startTime, String endTime) {
        JSONArray resultArray = new JSONArray();
        int pageNum = 1;
        int pageSize = 50;

        while (true) {
            JSONArray pageData = fetchData1(cjzh, startTime, endTime, pageNum, pageSize);
            if (pageData == null) {
                return null;
            } else if (pageData.isEmpty()) {
                break;
            }

            resultArray.addAll(pageData);

            if (pageData.size() < pageSize) {
                break;
            }

            pageNum++;
        }
        return resultArray;
    }

    private static JSONArray fetchData1(Cjzh cjzh, String startTime, String endTime, int pageNum, int pageSize) {
        CjLog.addTime("bzj_http.qfetchData1");
        CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cjzh.getCookieStore()).build();
        String url = "https://ark.xiaohongshu.com/api/suez/finance/accountforweb/listAccountRecord";
        HttpPost httpPost = new HttpPost(url);
        try {
            // 设置请求头
            setHeaders1(httpPost);

            // 构建请求体，包含startTime, endTime, pageNum和pageSize
            String param = String.format("{\"startTime\":\"%s\",\"endTime\":\"%s\",\"pageNum\":%d,\"pageSize\":%d}",
                    startTime, endTime, pageNum, pageSize);
            StringEntity entity = new StringEntity(param, "UTF-8");
            httpPost.setEntity(entity);

            RequestConfig requestConfig = RequestConfig.custom()
                    .setSocketTimeout(20000)
                    .setConnectTimeout(20000)
                    .setConnectionRequestTimeout(20000)
                    .setCookieSpec(CookieSpecs.NETSCAPE)
                    .build();
            httpPost.setConfig(requestConfig);

            HttpResponse httpResponse = httpClient.execute(httpPost);
            HttpEntity responseEntity = httpResponse.getEntity();
            String body = EntityUtils.toString(responseEntity);

            if (!StringUtil.isJson(body)) {
                return null;
            }

            JSONObject jsonObject = JSONObject.parseObject(body);
            if (jsonObject.getBooleanValue("success") && jsonObject.getInteger("statusCode") == 200) {
                JSONObject data = jsonObject.getJSONObject("data");
                if (data != null) {
                    return data.getJSONArray("list");
                }
                return new JSONArray();
            }

        } catch (Exception e) {
            LOG.error("查询小红书贷款资金失败", e);
        } finally {
            if (httpPost != null) {
                httpPost.releaseConnection();
            }
            try {
                httpClient.close();
            } catch (Exception e) {
                LOG.error("关闭httpClient失败", e);
            }
        }
        return null;
    }

    private static void setHeaders1(HttpPost httpPost) {
        httpPost.setHeader("authority", "ark.xiaohongshu.com");
        httpPost.setHeader("method", "POST");
        httpPost.setHeader("path", "/api/suez/finance/accountforweb/listAccountRecord");
        httpPost.setHeader("scheme", "https");
        httpPost.setHeader("accept", "application/json, text/plain, */*");
        httpPost.setHeader("accept-encoding", "gzip, deflate, br, zstd");
        httpPost.setHeader("accept-language", "zh-CN,zh;q=0.9,en;q=0.8");
        httpPost.setHeader("content-type", "application/json;charset=UTF-8");
        httpPost.setHeader("origin", "https://ark.xiaohongshu.com");
        httpPost.setHeader("referer", "https://ark.xiaohongshu.com/app-merchant/third-settle");
        httpPost.setHeader("sec-ch-ua", "\"Google Chrome\";v=\"125\", \"Not.A/Brand\";v=\"24\", \"Chromium\";v=\"125\"");
        httpPost.setHeader("sec-ch-ua-mobile", "?0");
        httpPost.setHeader("sec-ch-ua-platform", "\"macOS\"");
        httpPost.setHeader("sec-fetch-dest", "empty");
        httpPost.setHeader("sec-fetch-mode", "cors");
        httpPost.setHeader("sec-fetch-site", "same-origin");
        httpPost.setHeader("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
    }

    /**
     * 分页查询资金-店铺保证金明细
     *
     * @param cjzh      Cjzh对象，包含Cookie信息
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 返回包含所有分页数据的JSONArray
     */
    public static JSONArray queryAllDepositRecords(Cjzh cjzh, String startTime, String endTime) {
        JSONArray resultArray = new JSONArray();
        int pageNum = 1;
        int pageSize = 50;

        while (true) {
            JSONArray currentPageData = queryDepositRecords(cjzh, startTime, endTime, pageNum, pageSize);
            if (currentPageData == null) {
                return null;
            } else if (currentPageData.isEmpty()) {
                break;
            }

            resultArray.addAll(currentPageData);

            if (currentPageData.size() < pageSize) {
                break;
            }
            pageNum++;
        }
        return resultArray;
    }

    /**
     * 查询指定页码和页大小的存款记录
     *
     * @param cjzh      Cjzh对象，包含Cookie信息
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param pageNum   页码
     * @param pageSize  每页大小
     * @return 返回当前页的JSONArray数据
     */
    public static JSONArray queryDepositRecords(Cjzh cjzh, String startTime, String endTime, int pageNum, int pageSize) {
        CjLog.addTime("bzj_http.queryDepositRecords");
        CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cjzh.getCookieStore()).build();
        String url = String.format("https://ark.xiaohongshu.com/api/suez/finance/accountforweb/depositRecords?startTime=%s&endTime=%s&pageNum=%d&pageSize=%d",
                startTime, endTime, pageNum, pageSize);
        HttpGet httpGet = null;
        try {
            httpGet = new HttpGet(url);
            setHeaders(httpGet);
            RequestConfig requestConfig = RequestConfig.custom()
                    .setSocketTimeout(20000)
                    .setConnectTimeout(20000)
                    .setConnectionRequestTimeout(20000)
                    .build();
            httpGet.setConfig(requestConfig);

            HttpResponse httpResponse = httpClient.execute(httpGet);
            HttpEntity responseEntity = httpResponse.getEntity();
            String body = EntityUtils.toString(responseEntity);

            JSONObject jsonObject = JSONObject.parseObject(body);
            if (jsonObject.getBooleanValue("success") && jsonObject.getInteger("statusCode") == 200) {
                JSONObject dataObject = jsonObject.getJSONObject("data");
                if (dataObject != null) {
                    return dataObject.getJSONArray("list");
                }
                return new JSONArray();
            }
        } catch (Exception e) {
            LOG.error("查询小红书保证金记录失败", e);
        } finally {
            if (httpGet != null) {
                httpGet.releaseConnection();
            }
            try {
                httpClient.close();
            } catch (Exception e) {
                LOG.error("关闭httpClient失败", e);
            }
        }
        return null;
    }

    /**
     * 设置HTTP GET请求的头部信息
     *
     * @param httpGet HttpGet对象
     */
    private static void setHeaders(HttpGet httpGet) {
        httpGet.setHeader("authority", "ark.xiaohongshu.com");
        httpGet.setHeader("method", "GET");
        httpGet.setHeader("path", httpGet.getURI().getPath());
        httpGet.setHeader("scheme", "https");
        httpGet.setHeader("accept", "application/json, text/plain, */*");
        httpGet.setHeader("accept-encoding", "gzip, deflate, br, zstd");
        httpGet.setHeader("accept-language", "zh-CN,zh;q=0.9,en;q=0.8");
        httpGet.setHeader("referer", "https://ark.xiaohongshu.com/app-merchant/deposit");
        httpGet.setHeader("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Google Chrome\";v=\"126\"");
        httpGet.setHeader("sec-ch-ua-mobile", "?0");
        httpGet.setHeader("sec-ch-ua-platform", "\"macOS\"");
        httpGet.setHeader("sec-fetch-dest", "empty");
        httpGet.setHeader("sec-fetch-mode", "cors");
        httpGet.setHeader("sec-fetch-site", "same-origin");
        httpGet.setHeader("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
    }



    /**
     * 分页查询资金-订单计算明细
     *
     * @param cjzh      Cjzh对象，包含Cookie信息
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param type      请求类型（1或2）
     * @return 返回包含所有分页数据的JSONArray
     */
    public static JSONArray queryAllDdjsmx(Cjzh cjzh, String startTime, String endTime, int type) {
        JSONArray resultArray = new JSONArray();
        int pageNum = 1;
        int pageSize = 50;

        while (true) {
            JSONArray currentPageData = queryAllDdjsmx1(cjzh, startTime, endTime, pageNum, pageSize, type);
            if (currentPageData == null) {
                return null;
            } else if (currentPageData.isEmpty()) {
                break;
            }

            resultArray.addAll(currentPageData);

            if (currentPageData.size() < pageSize) {
                break;
            }
            pageNum++;
        }
        return resultArray;
    }

    /**
     * 查询指定页码和页大小的订单记录
     *
     * @param cjzh      Cjzh对象，包含Cookie信息
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param pageNum   页码
     * @param pageSize  每页大小
     * @param type      请求类型（1或2）
     * @return 返回当前页的JSONArray数据
     */
    public static JSONArray queryAllDdjsmx1(Cjzh cjzh, String startTime, String endTime, int pageNum, int pageSize, int type) {
        CjLog.addTime("zj_http.queryAllDdjsmx1");
        CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cjzh.getCookieStore()).build();
        String url = (type == 1) ? "https://ark.xiaohongshu.com/api/suez/sellerstatementservice/ark/settle/order/page"
                : "https://ark.xiaohongshu.com/api/suez/sellerstatementservice/ark/settle/expense/page";
        HttpPost httpPost = null;
        try {
            httpPost = new HttpPost(url);
            RequestConfig requestConfig = RequestConfig.custom()
                    .setSocketTimeout(20000)
                    .setConnectTimeout(20000)
                    .setConnectionRequestTimeout(20000)
                    .build();
            httpPost.setConfig(requestConfig);

            // 设置请求头
            httpPost.setHeader("Accept", "application/json, text/plain, */*");
            httpPost.setHeader("Accept-Encoding", "gzip, deflate, br, zstd");
            httpPost.setHeader("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
            httpPost.setHeader("Content-Type", "application/json;charset=UTF-8");
            httpPost.setHeader("Origin", "https://ark.xiaohongshu.com");
            httpPost.setHeader("Referer", "https://ark.xiaohongshu.com/app-merchant/order-settle");
            httpPost.setHeader("Sec-Ch-Ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Google Chrome\";v=\"126\"");
            httpPost.setHeader("Sec-Ch-Ua-Mobile", "?0");
            httpPost.setHeader("Sec-Ch-Ua-Platform", "\"macOS\"");
            httpPost.setHeader("Sec-Fetch-Dest", "empty");
            httpPost.setHeader("Sec-Fetch-Mode", "cors");
            httpPost.setHeader("Sec-Fetch-Site", "same-origin");
            httpPost.setHeader("Sub-System", "ark");
            httpPost.setHeader("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");

            JSONObject jsonRequest = new JSONObject();
            jsonRequest.put("pageNum", pageNum);
            jsonRequest.put("pageSize", pageSize);
            jsonRequest.put("packageId", "");

            if (type == 1) {
                jsonRequest.put("timeType", "SETTLE_TIME");
                jsonRequest.put("startTime", startTime);
                jsonRequest.put("endTime", endTime);
            } else if (type == 2) {
                jsonRequest.put("settleDateFrom", startTime);
                jsonRequest.put("settleDateTo", endTime);
            }

            StringEntity requestEntity = new StringEntity(jsonRequest.toString(), "UTF-8");
            requestEntity.setContentType("application/json;charset=UTF-8");
            httpPost.setEntity(requestEntity);

            HttpResponse httpResponse = httpClient.execute(httpPost);
            HttpEntity responseEntity = httpResponse.getEntity();
            String body = EntityUtils.toString(responseEntity);

            JSONObject jsonObject = JSONObject.parseObject(body);
            if (jsonObject.getBooleanValue("success") && jsonObject.getInteger("statusCode") == 200) {
                JSONObject dataObject = jsonObject.getJSONObject("data");
                if (dataObject != null) {
                    return dataObject.getJSONArray("list");
                }
                return new JSONArray();
            }
        } catch (Exception e) {
            LOG.error("查询小红订单结算明细记录失败", e);
        } finally {
            if (httpPost != null) {
                httpPost.releaseConnection();
            }
            try {
                httpClient.close();
            } catch (Exception e) {
                LOG.error("关闭httpClient失败", e);
            }
        }
        return null;
    }


    /**
     * 调用导出接口生成导出任务
     *
     * @param cjzh      Cjzh对象，包含Cookie信息
     * @param order     导出任务类型，"order" 或 "expense"
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 返回成功或失败
     */
    public static boolean exportOrderAndExpense(Cjzh cjzh, String order, String startTime, String endTime) {
        CjLog.addTime("zj_http.exportOrderAndExpense");
        CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cjzh.getCookieStore()).build();
        String url = "https://ark.xiaohongshu.com/api/suez/sellerstatementservice/ark/settle/" + order + "/export";
        HttpPost httpPost = null;
        try {
            httpPost = new HttpPost(url);
            RequestConfig requestConfig = RequestConfig.custom()
                    .setSocketTimeout(20000)
                    .setConnectTimeout(20000)
                    .setConnectionRequestTimeout(20000)
                    .build();
            httpPost.setConfig(requestConfig);

            // 设置请求头
            httpPost.setHeader("Accept", "application/json, text/plain, */*");
            httpPost.setHeader("Accept-Encoding", "gzip, deflate, br, zstd");
            httpPost.setHeader("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
            httpPost.setHeader("Content-Type", "application/json;charset=UTF-8");
            httpPost.setHeader("Origin", "https://ark.xiaohongshu.com");
            httpPost.setHeader("Referer", "https://ark.xiaohongshu.com/app-merchant/order-settle/service");
            httpPost.setHeader("Sec-Ch-Ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Google Chrome\";v=\"126\"");
            httpPost.setHeader("Sec-Ch-Ua-Mobile", "?0");
            httpPost.setHeader("Sec-Ch-Ua-Platform", "\"macOS\"");
            httpPost.setHeader("Sec-Fetch-Dest", "empty");
            httpPost.setHeader("Sec-Fetch-Mode", "cors");
            httpPost.setHeader("Sec-Fetch-Site", "same-origin");
            httpPost.setHeader("Sub-System", "ark");
            httpPost.setHeader("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");

            JSONObject jsonRequest = new JSONObject();
            jsonRequest.put("packageId", "");

            if ("order".equals(order)) {
                jsonRequest.put("timeType", "SETTLE_TIME");
                jsonRequest.put("startTime", startTime);
                jsonRequest.put("endTime", endTime);
            } else if ("expense".equals(order)) {
                jsonRequest.put("settleDateFrom", startTime);
                jsonRequest.put("settleDateTo", endTime);
            }

            StringEntity requestEntity = new StringEntity(jsonRequest.toString(), "UTF-8");
            requestEntity.setContentType("application/json;charset=UTF-8");
            httpPost.setEntity(requestEntity);

            HttpResponse httpResponse = httpClient.execute(httpPost);
            HttpEntity responseEntity = httpResponse.getEntity();
            String body = EntityUtils.toString(responseEntity);

            JSONObject jsonObject = JSONObject.parseObject(body);
            return jsonObject.getBooleanValue("success") && jsonObject.getInteger("statusCode") == 200;
        } catch (Exception e) {
            LOG.error("调用导出任务失败", e);
        } finally {
            if (httpPost != null) {
                httpPost.releaseConnection();
            }
            try {
                httpClient.close();
                TimeUnit.SECONDS.sleep(3);
            } catch (Exception e) {
                LOG.error("关闭httpClient失败", e);
            }
        }
        return false;
    }


    /**
     * 查询当前导出任务
     *
     * @param cjzh Cjzh对象，包含Cookie信息
     * @return 返回当前页的JSONArray数据
     */
    public static JSONArray queryExportTask(Cjzh cjzh) {
        CjLog.addTime("zj_http.queryExportTask");
        CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cjzh.getCookieStore()).build();
        String url = "https://ark.xiaohongshu.com/api/suez/sellerstatementservice/ark/settle/export/page";
        HttpPost httpPost = null;
        try {
            httpPost = new HttpPost(url);
            RequestConfig requestConfig = RequestConfig.custom()
                    .setSocketTimeout(20000)
                    .setConnectTimeout(20000)
                    .setConnectionRequestTimeout(20000)
                    .build();
            httpPost.setConfig(requestConfig);

            // 设置请求头
            httpPost.setHeader("Accept", "application/json, text/plain, */*");
            httpPost.setHeader("Accept-Encoding", "gzip, deflate, br, zstd");
            httpPost.setHeader("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
            httpPost.setHeader("Content-Type", "application/json;charset=UTF-8");
            httpPost.setHeader("Origin", "https://ark.xiaohongshu.com");
            httpPost.setHeader("Referer", "https://ark.xiaohongshu.com/app-merchant/order-settle");
            httpPost.setHeader("Sec-Ch-Ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Google Chrome\";v=\"126\"");
            httpPost.setHeader("Sec-Ch-Ua-Mobile", "?0");
            httpPost.setHeader("Sec-Ch-Ua-Platform", "\"macOS\"");
            httpPost.setHeader("Sec-Fetch-Dest", "empty");
            httpPost.setHeader("Sec-Fetch-Mode", "cors");
            httpPost.setHeader("Sec-Fetch-Site", "same-origin");
            httpPost.setHeader("Sub-System", "ark");
            httpPost.setHeader("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");

            JSONObject jsonRequest = new JSONObject();
            jsonRequest.put("pageNum", 1);
            jsonRequest.put("pageSize", 10);

            StringEntity requestEntity = new StringEntity(jsonRequest.toString(), "UTF-8");
            requestEntity.setContentType("application/json;charset=UTF-8");
            httpPost.setEntity(requestEntity);

            HttpResponse httpResponse = httpClient.execute(httpPost);
            HttpEntity responseEntity = httpResponse.getEntity();
            String body = EntityUtils.toString(responseEntity);

            JSONObject jsonObject = JSONObject.parseObject(body);
            if (jsonObject.getBooleanValue("success") && jsonObject.getInteger("statusCode") == 200) {
                JSONObject dataObject = jsonObject.getJSONObject("data");
                if (dataObject != null) {
                    return dataObject.getJSONArray("list");
                }
                return new JSONArray();
            }
        } catch (Exception e) {
            LOG.error("查询导出任务失败", e);
        } finally {
            if (httpPost != null) {
                httpPost.releaseConnection();
            }
            try {
                httpClient.close();
                TimeUnit.SECONDS.sleep(1);
            } catch (Exception e) {
                LOG.error("关闭httpClient失败", e);
            }
        }
        return null;
    }


    /**
     * 获取导出任务的下载URL
     *
     * @param cjzh Cjzh对象，包含Cookie信息
     * @param id 导出任务ID
     * @return 返回下载URL字符串
     */
    public static String getDownloadUrl(Cjzh cjzh, String id) {
        CjLog.addTime("zj_http.getDownloadUrl");
        CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cjzh.getCookieStore()).build();
        String url = "https://ark.xiaohongshu.com/api/suez/sellerstatementservice/ark/settle/export/getDownloadUrl?id=" + id;
        HttpGet httpGet = null;
        try {
            httpGet = new HttpGet(url);
            RequestConfig requestConfig = RequestConfig.custom()
                    .setSocketTimeout(20000)
                    .setConnectTimeout(20000)
                    .setConnectionRequestTimeout(20000)
                    .build();
            httpGet.setConfig(requestConfig);

            // 设置请求头
            httpGet.setHeader("Accept", "application/json, text/plain, */*");
            httpGet.setHeader("Accept-Encoding", "gzip, deflate, br, zstd");
            httpGet.setHeader("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
            httpGet.setHeader("Content-Type", "application/json;charset=UTF-8");
            httpGet.setHeader("Origin", "https://ark.xiaohongshu.com");
            httpGet.setHeader("Referer", "https://ark.xiaohongshu.com/app-merchant/order-settle/service");
            httpGet.setHeader("Sec-Ch-Ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Google Chrome\";v=\"126\"");
            httpGet.setHeader("Sec-Ch-Ua-Mobile", "?0");
            httpGet.setHeader("Sec-Ch-Ua-Platform", "\"macOS\"");
            httpGet.setHeader("Sec-Fetch-Dest", "empty");
            httpGet.setHeader("Sec-Fetch-Mode", "cors");
            httpGet.setHeader("Sec-Fetch-Site", "same-origin");
            httpGet.setHeader("Sub-System", "ark");
            httpGet.setHeader("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");

            HttpResponse httpResponse = httpClient.execute(httpGet);
            HttpEntity responseEntity = httpResponse.getEntity();
            String body = EntityUtils.toString(responseEntity);

            JSONObject jsonObject = JSONObject.parseObject(body);
            if (jsonObject.getBooleanValue("success") && jsonObject.getInteger("statusCode") == 200) {
                return jsonObject.getString("data");
            }
        } catch (Exception e) {
            LOG.error("获取导出任务下载URL失败", e);
        } finally {
            if (httpGet != null) {
                httpGet.releaseConnection();
            }
            try {
                httpClient.close();
            } catch (Exception e) {
                LOG.error("关闭httpClient失败", e);
            }
        }
        return null;
    }


    /**
     * 根据下载URL下载文件到本地
     *
     * @param url      下载URL
     * @param date     日期，用于构建文件名
     * @param fileName 文件名，用于替换默认的文件名部分
     * @return 返回文件的完整路径
     */
    public static String downloadExcel(Cjzh cjzh,String url, String date, String fileName) {
        CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cjzh.getCookieStore()).build();
        HttpGet httpGet = new HttpGet(url);
        HttpResponse httpResponse = null;
        InputStream is = null;
        ByteArrayOutputStream output = null;
        FileOutputStream fos = null;
        try {
            RequestConfig.Builder requestConfig = RequestConfig.custom()
                    .setSocketTimeout(120000)
                    .setConnectTimeout(120000)
                    .setConnectionRequestTimeout(120000)
                    .setCookieSpec(CookieSpecs.NETSCAPE);
            httpGet.setConfig(requestConfig.build());
            HttpContext httpContext = new BasicHttpContext();
            httpResponse = httpClient.execute(httpGet, httpContext);
            HttpEntity httpEntity = httpResponse.getEntity();

            // 根据InputStream 下载文件
            is = httpEntity.getContent();
            String filePath = CommonConfig.getString("tempdir") + date + "_" + fileName + ".xlsx";
            output = new ByteArrayOutputStream();
            byte[] buffer = new byte[4096];
            int r;
            while ((r = is.read(buffer)) > 0) {
                output.write(buffer, 0, r);
            }
            fos = new FileOutputStream(filePath);
            output.writeTo(fos);
            output.flush();

            EntityUtils.consume(httpEntity);
            return filePath;
        } catch (Exception e) {
            LOG.error("下载文件失败", e);
        } finally {
            try {
                if (is != null) {
                    is.close();
                }
                if (output != null) {
                    output.close();
                }
                if (fos != null) {
                    fos.close();
                }
                if (httpResponse != null) {
                    EntityUtils.consume(httpResponse.getEntity());
                }
                httpClient.close();
            } catch (Exception e) {
                LOG.error("关闭资源失败", e);
            }
        }
        return null;
    }
}
