package com.abujlb.zdcj8.tsdao;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import javax.annotation.PostConstruct;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.abujlb.Config;
import com.abujlb.Result;
import com.abujlb.dao.TsDao;
import com.abujlb.zdcj8.bean.SjsSkusale;
import com.abujlb.zdcj8.bean.SkuSale;
import com.abujlb.zdcj8.util.StrUtil;
import com.alicloud.openservices.tablestore.SyncClient;
import com.alicloud.openservices.tablestore.model.Column;
import com.alicloud.openservices.tablestore.model.ColumnValue;
import com.alicloud.openservices.tablestore.model.GetRowRequest;
import com.alicloud.openservices.tablestore.model.GetRowResponse;
import com.alicloud.openservices.tablestore.model.PrimaryKey;
import com.alicloud.openservices.tablestore.model.PrimaryKeyBuilder;
import com.alicloud.openservices.tablestore.model.PrimaryKeyValue;
import com.alicloud.openservices.tablestore.model.PutRowRequest;
import com.alicloud.openservices.tablestore.model.Row;
import com.alicloud.openservices.tablestore.model.RowPutChange;
import com.alicloud.openservices.tablestore.model.RowUpdateChange;
import com.alicloud.openservices.tablestore.model.SingleRowQueryCriteria;
import com.alicloud.openservices.tablestore.model.UpdateRowRequest;
import com.alicloud.openservices.tablestore.model.search.SearchQuery;
import com.alicloud.openservices.tablestore.model.search.SearchRequest;
import com.alicloud.openservices.tablestore.model.search.SearchResponse;
import com.alicloud.openservices.tablestore.model.search.query.BoolQuery;
import com.alicloud.openservices.tablestore.model.search.query.Query;
import com.alicloud.openservices.tablestore.model.search.query.RangeQuery;
import com.alicloud.openservices.tablestore.model.search.query.TermQuery;
import com.alicloud.openservices.tablestore.model.search.sort.FieldSort;
import com.alicloud.openservices.tablestore.model.search.sort.Sort;
import com.alicloud.openservices.tablestore.model.search.sort.SortOrder;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

/**
 * 表格存储dao：数据蛇 -> 竞品sku销量
 * 
 * <AUTHOR>
 * @date 2020-09-24 17:07:02
 */
@Component
public class SjsSkusaleTsDao {

	private static Logger log = Logger.getLogger(SjsSkusaleTsDao.class);

	private static String TSINSTANCE_NAME = null; // 表格存储实例名

	private static final String TABLE_NAME = "sjs_skusale"; // 表名
	private static final String INDEX_NAME = "index_sjs_skusale"; // 索引名称

	private static final String COLUMN_ITEMID = "itemid"; // 宝贝id(分片键)
	private static final String COLUMN_RQ = "rq"; // 日期，例如：2020-09-22

	private static final String COLUMN_DATA = "data"; // 内容

	@Autowired
	private TsDao tsDao;
	@Autowired
	private Config config;

	@PostConstruct
	public void init() {
		try {
			TSINSTANCE_NAME = config.getString("tsdao_database3");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 插入数据
	 * 
	 * @param 主键字段和列
	 * @return
	 */
	public boolean putRow(String itemid, String rq, String data) {
		try {
			SyncClient client = tsDao.getClient(TSINSTANCE_NAME);
			// 构造主键
			PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
			primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_ITEMID, PrimaryKeyValue.fromString(itemid));
			primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_RQ, PrimaryKeyValue.fromString(rq));

			PrimaryKey primaryKey = primaryKeyBuilder.build();
			// 设置表名
			RowPutChange rowPutChange = new RowPutChange(TABLE_NAME, primaryKey);
			// 加入一些属性列，非主键
			rowPutChange.addColumn(new Column(COLUMN_DATA, ColumnValue.fromString(data)));
			// 插入
			client.putRow(new PutRowRequest(rowPutChange));
			return true;
		} catch (Throwable e) {
			log.error(e.getMessage(), e);
			e.printStackTrace();
			return false;
		}
	}

	/**
	 * 更新数据
	 * 
	 * @param 主键字段和列
	 * @return
	 */
	public boolean updateRow(String itemid, String rq, String data) {
		try {
			SyncClient client = tsDao.getClient(TSINSTANCE_NAME);
			// 构造主键
			PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
			primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_ITEMID, PrimaryKeyValue.fromString(itemid));
			primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_RQ, PrimaryKeyValue.fromString(rq));

			PrimaryKey primaryKey = primaryKeyBuilder.build();
			// 设置表名
			RowUpdateChange rowUpdateChange = new RowUpdateChange(TABLE_NAME, primaryKey);
			// 加入一些属性列，非主键
			rowUpdateChange.put(new Column(COLUMN_DATA, ColumnValue.fromString(data)));
			// 插入
			client.updateRow(new UpdateRowRequest(rowUpdateChange));
			return true;
		} catch (Throwable e) {
			log.error(e.getMessage(), e);
			e.printStackTrace();
			return false;
		}
	}

	/**
	 * 查询数据，处理数据
	 * 
	 * @param 主键字段和列
	 * @return
	 */
	public Result getRow(String itemid, String rq) {
		Result result = new Result(101, "默认提示内容!");
		try {
			PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
			primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_ITEMID, PrimaryKeyValue.fromString(itemid));
			primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_RQ, PrimaryKeyValue.fromString(rq));
			PrimaryKey primaryKey = primaryKeyBuilder.build();
			SingleRowQueryCriteria criteria = new SingleRowQueryCriteria(TABLE_NAME, primaryKey);
			criteria.setMaxVersions(1);
			criteria.addColumnsToGet(new String[] { COLUMN_DATA });
			GetRowResponse getRowResponse = tsDao.getClient(TSINSTANCE_NAME).getRow(new GetRowRequest(criteria));
			Row row1 = getRowResponse.getRow();
			if (row1 == null) {
				log.info("获取" + TABLE_NAME + "数据失败：itemid=" + itemid + "，rq=" + rq);
				result.setCodeContent(101, "获取" + TABLE_NAME + "数据失败：itemid=" + itemid + "，rq=" + rq);
				return result;
			}
			/******************************* 步骤二：处理数据 *******************************/
			String data = ""; // 数据
			if (row1.getLatestColumn(COLUMN_DATA) != null && row1.getLatestColumn(COLUMN_DATA).getValue() != null) {
				data = row1.getLatestColumn(COLUMN_DATA).getValue().asString();
			}
			result.putKey(COLUMN_DATA, data);
			result.setCodeContent(Result.SUCCESS, "");
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			result.setCodeContent(102, "表格存储访问错误！");
		}
		return result;
	}

	/**
	 * 全部查询：通过索引，多条匹配查询
	 * 
	 * @param wldsBjjl 查询条件，最多5个查询条件
	 * @return List
	 */
	public List<SkuSale> selectRowsByIndex(SjsSkusale sjsSkusale) {
		SyncClient client = null;
		try {
			client = tsDao.getClient(TSINSTANCE_NAME);
			// 查询条件：条件组合
			List<Query> queryList = new ArrayList<>();
			// 精准匹配：条件1
			if (!StrUtil.isNull(sjsSkusale.getItemid())) {
				TermQuery termQuery = new TermQuery();
				termQuery.setFieldName(COLUMN_ITEMID);
				termQuery.setTerm(ColumnValue.fromString(sjsSkusale.getItemid()));
				queryList.add(termQuery);
			}
			// 精准匹配：条件2
			if (!StrUtil.isNull(sjsSkusale.getRq())) {
				TermQuery termQuery = new TermQuery();
				termQuery.setFieldName(COLUMN_RQ);
				termQuery.setTerm(ColumnValue.fromString(sjsSkusale.getRq()));
				queryList.add(termQuery);
			}
			// 范围查询：条件3
			if (!StrUtil.isNull(sjsSkusale.getStartRq())) {
				RangeQuery rangeQuery = new RangeQuery(); // 设置查询类型为RangeQuery
				rangeQuery.setFieldName(COLUMN_RQ); // 设置针对哪个字段
				rangeQuery.greaterThanOrEqual(ColumnValue.fromString(sjsSkusale.getStartRq())); // 大于等于
				// rangeQuery.lessThan(ColumnValue.fromString()); // 小于
				queryList.add(rangeQuery);
			}
			// 范围查询：条件4
			if (!StrUtil.isNull(sjsSkusale.getEndRq())) {
				RangeQuery rangeQuery = new RangeQuery(); // 设置查询类型为RangeQuery
				rangeQuery.setFieldName(COLUMN_RQ); // 设置针对哪个字段
				rangeQuery.lessThanOrEqual(ColumnValue.fromString(sjsSkusale.getEndRq())); // 小于等于
				queryList.add(rangeQuery);
			}
			// MustQueries组合所有条件，and关系，满足所有条件
			BoolQuery boolQuery = new BoolQuery();
			boolQuery.setMustQueries(queryList);

			// 设置排序
			FieldSort fieldSort = new FieldSort(sjsSkusale.getOrder());
			if ("asc".equalsIgnoreCase(sjsSkusale.getOrderby().trim())) {
				fieldSort.setOrder(SortOrder.ASC);
			} else {
				fieldSort.setOrder(SortOrder.DESC);
			}
			SearchQuery searchQuery = new SearchQuery();
			// searchQuery.setLimit(wldsBjjl.getPagesize()); // 分页大小
			// searchQuery.setOffset(wldsBjjl.getBegin()); // 开始位置
			searchQuery.setSort(new Sort(Arrays.asList(fieldSort)));
			searchQuery.setQuery(boolQuery);
			// searchQuery.setGetTotalCount(true); // 获取总条数

			SearchRequest searchRequest = new SearchRequest(TABLE_NAME, INDEX_NAME, searchQuery);
			SearchRequest.ColumnsToGet columnsToGet = new SearchRequest.ColumnsToGet();
			columnsToGet.setReturnAll(true); // 设置返回所有列
			// columnsToGet.setColumns(Arrays.asList(COLUMN_DATA)); // 设置返回的列
			searchRequest.setColumnsToGet(columnsToGet);

			List<SkuSale> list = new ArrayList<SkuSale>();
			Gson gson = new Gson();
			// 循环读取
			while (true) {
				SearchResponse searchResponse = client.search(searchRequest);
				if (!searchResponse.isAllSuccess()) {
					throw new RuntimeException("not all success");
				}
				for (Row row : searchResponse.getRows()) {
					// String _itemid = row.getPrimaryKey().getPrimaryKeyColumn(COLUMN_ITEMID).getValue().asString();
					// String _rq = row.getPrimaryKey().getPrimaryKeyColumn(COLUMN_RQ).getValue().asString();

					String _data = "";
					if (row.getLatestColumn(COLUMN_DATA) != null && row.getLatestColumn(COLUMN_DATA).getValue() != null) {
						_data = row.getLatestColumn(COLUMN_DATA).getValue().asString();
					}
					if (!StrUtil.isNull(_data)) {
						Type type = new TypeToken<ArrayList<SkuSale>>() {
						}.getType();
						List<SkuSale> list2 = gson.fromJson(_data, type);
						if (list2 != null && list2.size() > 0) {
							list.addAll(list2);
						}
					}
				}
				// 可检查NextToken是否为空，若不为空，可通过NextToken继续读取。
				if (searchResponse.getNextToken() != null) {
					searchRequest.getSearchQuery().setToken(searchResponse.getNextToken()); // 把token设置到下一次请求中
				} else {
					break;
				}
			}
			if (list != null && list.size() > 0) {
				return list;
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			e.printStackTrace();
		}
		return null;
	}

	/**
	 * 分页查询：通过索引，多条匹配分页查询
	 * 
	 * @param wldsBjjl 查询条件，最多5个查询条件
	 * @return List
	 */
	public List<SkuSale> selectRowsByIndexWithPage(SjsSkusale sjsSkusale) {
		SyncClient client = null;
		try {
			client = tsDao.getClient(TSINSTANCE_NAME);
			// 查询条件：条件组合
			List<Query> queryList = new ArrayList<>();
			// 精准匹配：条件1
			if (!StrUtil.isNull(sjsSkusale.getItemid())) {
				TermQuery termQuery = new TermQuery();
				termQuery.setFieldName(COLUMN_ITEMID);
				termQuery.setTerm(ColumnValue.fromString(sjsSkusale.getItemid()));
				queryList.add(termQuery);
			}
			// 精准匹配：条件2
			if (!StrUtil.isNull(sjsSkusale.getRq())) {
				TermQuery termQuery = new TermQuery();
				termQuery.setFieldName(COLUMN_RQ);
				termQuery.setTerm(ColumnValue.fromString(sjsSkusale.getRq()));
				queryList.add(termQuery);
			}
			// 范围查询：条件3
			if (!StrUtil.isNull(sjsSkusale.getStartRq())) {
				RangeQuery rangeQuery = new RangeQuery(); // 设置查询类型为RangeQuery
				rangeQuery.setFieldName(COLUMN_RQ); // 设置针对哪个字段
				rangeQuery.greaterThanOrEqual(ColumnValue.fromString(sjsSkusale.getStartRq())); // 大于等于
				// rangeQuery.lessThan(ColumnValue.fromString()); // 小于
				queryList.add(rangeQuery);
			}
			// 范围查询：条件4
			if (!StrUtil.isNull(sjsSkusale.getEndRq())) {
				RangeQuery rangeQuery = new RangeQuery(); // 设置查询类型为RangeQuery
				rangeQuery.setFieldName(COLUMN_RQ); // 设置针对哪个字段
				rangeQuery.lessThanOrEqual(ColumnValue.fromString(sjsSkusale.getEndRq())); // 小于等于
				queryList.add(rangeQuery);
			}
			// MustQueries组合所有条件，and关系，满足所有条件
			BoolQuery boolQuery = new BoolQuery();
			boolQuery.setMustQueries(queryList);

			// 设置排序
			FieldSort fieldSort = new FieldSort(sjsSkusale.getOrder());
			if ("asc".equalsIgnoreCase(sjsSkusale.getOrderby().trim())) {
				fieldSort.setOrder(SortOrder.ASC);
			} else {
				fieldSort.setOrder(SortOrder.DESC);
			}

			SearchQuery searchQuery = new SearchQuery();
			searchQuery.setLimit(sjsSkusale.getPagesize()); // 分页大小
			searchQuery.setOffset(sjsSkusale.getBegin()); // 开始位置
			searchQuery.setSort(new Sort(Arrays.asList(fieldSort)));
			searchQuery.setQuery(boolQuery);
			searchQuery.setGetTotalCount(true); // 获取总条数

			SearchRequest searchRequest = new SearchRequest(TABLE_NAME, INDEX_NAME, searchQuery);
			SearchRequest.ColumnsToGet columnsToGet = new SearchRequest.ColumnsToGet();
			columnsToGet.setReturnAll(true); // 设置返回所有列
			// columnsToGet.setColumns(Arrays.asList(COLUMN_DATA)); // 设置返回的列
			searchRequest.setColumnsToGet(columnsToGet);

			List<SkuSale> list = new ArrayList<SkuSale>();
			Gson gson = new Gson();
			// 循环读取
			SearchResponse searchResponse = client.search(searchRequest);
			if (!searchResponse.isAllSuccess()) {
				throw new RuntimeException("not all success");
			}
			for (Row row : searchResponse.getRows()) {
				// String _itemid = row.getPrimaryKey().getPrimaryKeyColumn(COLUMN_ITEMID).getValue().asString();
				// String _rq = row.getPrimaryKey().getPrimaryKeyColumn(COLUMN_RQ).getValue().asString();

				String _data = "";
				if (row.getLatestColumn(COLUMN_DATA) != null && row.getLatestColumn(COLUMN_DATA).getValue() != null) {
					_data = row.getLatestColumn(COLUMN_DATA).getValue().asString();
				}
				if (!StrUtil.isNull(_data)) {
					Type type = new TypeToken<ArrayList<SkuSale>>() {
					}.getType();
					List<SkuSale> list2 = gson.fromJson(_data, type);
					if (list2 != null && list2.size() > 0) {
						list.addAll(list2);
					}
				}
			}
			sjsSkusale.setRecordcount((int) searchResponse.getTotalCount());
			// if (list != null && list.size() > 0) {
			// return list;
			// }
			return list;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			e.printStackTrace();
		}
		return null;
	}

}
