package com.abujlb.zdcj8.tsdao;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.abujlb.dao.TsDao;
import com.alicloud.openservices.tablestore.model.GetRowRequest;
import com.alicloud.openservices.tablestore.model.GetRowResponse;
import com.alicloud.openservices.tablestore.model.PrimaryKey;
import com.alicloud.openservices.tablestore.model.PrimaryKeyBuilder;
import com.alicloud.openservices.tablestore.model.PrimaryKeyValue;
import com.alicloud.openservices.tablestore.model.Row;
import com.alicloud.openservices.tablestore.model.SingleRowQueryCriteria;

/**
* <AUTHOR>
* @date 2022-3-10
*/
@Component
public class ScphSpAllTsDao {

	@Autowired
	private TsDao tsDao ;
	
	/**
	 * @param bbid
	 * @return 类目id
	 */
	public String getRowCateid(String bbid) {
		try {
			PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
			primaryKeyBuilder.addPrimaryKeyColumn("itemid", PrimaryKeyValue.fromString(bbid));
			PrimaryKey primaryKey = primaryKeyBuilder.build();
			SingleRowQueryCriteria criteria = new SingleRowQueryCriteria("scph_sp_all", primaryKey);
			criteria.setMaxVersions(1);
			criteria.addColumnsToGet(new String[] { "cateid" });
			GetRowResponse getRowResponse = tsDao.getClient("abujlb3").getRow(new GetRowRequest(criteria));
			Row row = getRowResponse.getRow();
			if (row == null || row.isEmpty()) {
				return null;
			}
			
			if (row.getLatestColumn("cateid") != null && row.getLatestColumn("cateid").getValue() != null) {
				return row.getLatestColumn("cateid").getValue().asString();
			}
		} catch (Exception e) {
		}
		return null;
	}
}
