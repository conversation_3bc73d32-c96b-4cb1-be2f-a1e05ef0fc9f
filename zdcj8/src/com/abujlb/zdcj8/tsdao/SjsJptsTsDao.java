package com.abujlb.zdcj8.tsdao;

import javax.annotation.PostConstruct;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.abujlb.Config;
import com.abujlb.Result;
import com.abujlb.dao.TsDao;
import com.abujlb.zdcj8.util.StrUtil;
import com.alicloud.openservices.tablestore.SyncClient;
import com.alicloud.openservices.tablestore.model.Column;
import com.alicloud.openservices.tablestore.model.ColumnValue;
import com.alicloud.openservices.tablestore.model.GetRowRequest;
import com.alicloud.openservices.tablestore.model.GetRowResponse;
import com.alicloud.openservices.tablestore.model.PrimaryKey;
import com.alicloud.openservices.tablestore.model.PrimaryKeyBuilder;
import com.alicloud.openservices.tablestore.model.PrimaryKeyValue;
import com.alicloud.openservices.tablestore.model.PutRowRequest;
import com.alicloud.openservices.tablestore.model.Row;
import com.alicloud.openservices.tablestore.model.RowPutChange;
import com.alicloud.openservices.tablestore.model.RowUpdateChange;
import com.alicloud.openservices.tablestore.model.SingleRowQueryCriteria;
import com.alicloud.openservices.tablestore.model.UpdateRowRequest;

/**
 * 表格存储dao：数据蛇 -> 超级竞品透视
 * 
 * <AUTHOR>
 * @date 2020-10-21 17:43:52
 */
@Component
public class SjsJptsTsDao {

	private static Logger log = Logger.getLogger(SjsJptsTsDao.class);

	private static String TSINSTANCE_NAME = null; // 表格存储实例名

	private static final String TABLE_NAME = "sjs_jpts"; // 表名
	private static final String TABLE_NAME2 = "sjs_jpts2"; // 表名，https://www.shujushe.com/Navigation?hkj=3

	private static final String COLUMN_ITEMID = "itemid"; // 宝贝id(分片键)
	private static final String COLUMN_RQ = "rq"; // 日期，例如：2020-09-22

	private static final String COLUMN_DATA = "data"; // (最近30天)宝贝综合数据
	private static final String COLUMN_GJC = "gjc"; // (最近30天)竞品关键词数据(无线端)
	private static final String COLUMN_QD = "qd"; // (最近30天)竞品渠道数据
	private static final String COLUMN_ZTC = "ztc"; // (最近30天)竞品直通车词数据(无线端)


	@Autowired
	private TsDao tsDao;
	@Autowired
	private Config config;

	@PostConstruct
	public void init() {
		try {
			TSINSTANCE_NAME = config.getString("tsdao_database3");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 插入数据
	 * 
	 * @param 主键字段和列
	 * @return
	 */
	public boolean putRow(String itemid, String rq, String data, String gjc, String qd, String ztc) {
		try {
			SyncClient client = tsDao.getClient(TSINSTANCE_NAME);
			// 构造主键
			PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
			primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_ITEMID, PrimaryKeyValue.fromString(itemid));
			primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_RQ, PrimaryKeyValue.fromString(rq));

			PrimaryKey primaryKey = primaryKeyBuilder.build();
			// 设置表名
			RowPutChange rowPutChange = new RowPutChange(TABLE_NAME, primaryKey);
			// 加入一些属性列，非主键
			rowPutChange.addColumn(new Column(COLUMN_DATA, ColumnValue.fromString(data)));
			rowPutChange.addColumn(new Column(COLUMN_GJC, ColumnValue.fromString(gjc)));
			rowPutChange.addColumn(new Column(COLUMN_QD, ColumnValue.fromString(qd)));
			rowPutChange.addColumn(new Column(COLUMN_ZTC, ColumnValue.fromString(ztc)));
			// 插入
			client.putRow(new PutRowRequest(rowPutChange));
			return true;
		} catch (Throwable e) {
			log.error(e.getMessage(), e);
			e.printStackTrace();
			return false;
		}
	}

	/**
	 * 更新数据
	 * 
	 * @param 主键字段和列
	 * @return
	 */
	public boolean updateRow(String itemid, String rq, String data, String gjc, String qd, String ztc) {
		try {
			SyncClient client = tsDao.getClient(TSINSTANCE_NAME);
			// 构造主键
			PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
			primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_ITEMID, PrimaryKeyValue.fromString(itemid));
			primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_RQ, PrimaryKeyValue.fromString(rq));

			PrimaryKey primaryKey = primaryKeyBuilder.build();
			// 设置表名
			RowUpdateChange rowUpdateChange = new RowUpdateChange(TABLE_NAME, primaryKey);
			// 加入一些属性列，非主键
			if (!StrUtil.isNull(data)) {
				rowUpdateChange.put(new Column(COLUMN_DATA, ColumnValue.fromString(data)));
			}
			if (!StrUtil.isNull(gjc)) {
				rowUpdateChange.put(new Column(COLUMN_GJC, ColumnValue.fromString(gjc)));
			}
			if (!StrUtil.isNull(qd)) {
				rowUpdateChange.put(new Column(COLUMN_QD, ColumnValue.fromString(qd)));
			}
			if (!StrUtil.isNull(ztc)) {
				rowUpdateChange.put(new Column(COLUMN_ZTC, ColumnValue.fromString(ztc)));
			}
			// 插入
			client.updateRow(new UpdateRowRequest(rowUpdateChange));
			return true;
		} catch (Throwable e) {
			log.error(e.getMessage(), e);
			e.printStackTrace();
			return false;
		}
	}

	/**
	 * 查询数据，处理数据
	 * 
	 * @param 主键字段和列
	 * @return
	 */
	public Result getRow(String itemid, String rq) {
		Result result = new Result(101, "默认提示内容!");
		try {
			PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
			primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_ITEMID, PrimaryKeyValue.fromString(itemid));
			primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_RQ, PrimaryKeyValue.fromString(rq));
			PrimaryKey primaryKey = primaryKeyBuilder.build();
			SingleRowQueryCriteria criteria = new SingleRowQueryCriteria(TABLE_NAME, primaryKey);
			criteria.setMaxVersions(1);
			criteria.addColumnsToGet(new String[] { COLUMN_DATA, COLUMN_GJC, COLUMN_QD, COLUMN_ZTC });
			GetRowResponse getRowResponse = tsDao.getClient(TSINSTANCE_NAME).getRow(new GetRowRequest(criteria));
			Row row1 = getRowResponse.getRow();
			if (row1 == null) {
				log.info("获取" + TABLE_NAME + "数据失败：itemid=" + itemid + "，rq=" + rq);
				result.setCodeContent(101, "获取" + TABLE_NAME + "数据失败：itemid=" + itemid + "，rq=" + rq);
				return result;
			}
			/******************************* 步骤二：处理数据 *******************************/
			String _data = ""; // 数据
			if (row1.getLatestColumn(COLUMN_DATA) != null && row1.getLatestColumn(COLUMN_DATA).getValue() != null) {
				_data = row1.getLatestColumn(COLUMN_DATA).getValue().asString();
			}
			String _gjc = ""; // 数据
			if (row1.getLatestColumn(COLUMN_GJC) != null && row1.getLatestColumn(COLUMN_GJC).getValue() != null) {
				_gjc = row1.getLatestColumn(COLUMN_GJC).getValue().asString();
			}
			String _qd = ""; // 数据
			if (row1.getLatestColumn(COLUMN_QD) != null && row1.getLatestColumn(COLUMN_QD).getValue() != null) {
				_qd = row1.getLatestColumn(COLUMN_QD).getValue().asString();
			}
			String _ztc = ""; // 数据
			if (row1.getLatestColumn(COLUMN_ZTC) != null && row1.getLatestColumn(COLUMN_ZTC).getValue() != null) {
				_ztc = row1.getLatestColumn(COLUMN_ZTC).getValue().asString();
			}
			result.putKey(COLUMN_DATA, _data);
			result.putKey(COLUMN_GJC, _gjc);
			result.putKey(COLUMN_QD, _qd);
			result.putKey(COLUMN_ZTC, _ztc);
			result.setCodeContent(Result.SUCCESS, "");
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			result.setCodeContent(102, "表格存储访问错误！");
		}
		return result;
	}
	
	/**
	 * 更新数据
	 * 
	 * @param 主键字段和列
	 * @return
	 */
	public boolean updateRow2(String itemid, String rq, String data) {
		if (StrUtil.isNull(data)) {
			return false;
		}
		try {
			SyncClient client = tsDao.getClient(TSINSTANCE_NAME);
			// 构造主键
			PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
			primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_ITEMID, PrimaryKeyValue.fromString(itemid));
			primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_RQ, PrimaryKeyValue.fromString(rq));
			
			PrimaryKey primaryKey = primaryKeyBuilder.build();
			// 设置表名
			RowUpdateChange rowUpdateChange = new RowUpdateChange(TABLE_NAME2, primaryKey);
			// 加入一些属性列，非主键
			rowUpdateChange.put(new Column(COLUMN_DATA, ColumnValue.fromString(data)));
			// 修改
			client.updateRow(new UpdateRowRequest(rowUpdateChange));
			return true;
		} catch (Throwable e) {
			log.error(e.getMessage(), e);
			e.printStackTrace();
			return false;
		}
	}
	
	/**
	 * 查询数据，处理数据
	 * 
	 * @param 主键字段和列
	 * @return
	 */
	public Result getRow2(String itemid, String rq) {
		Result result = new Result(101, "默认提示内容!");
		try {
			PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
			primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_ITEMID, PrimaryKeyValue.fromString(itemid));
			primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_RQ, PrimaryKeyValue.fromString(rq));
			PrimaryKey primaryKey = primaryKeyBuilder.build();
			SingleRowQueryCriteria criteria = new SingleRowQueryCriteria(TABLE_NAME2, primaryKey);
			criteria.setMaxVersions(1);
			criteria.addColumnsToGet(new String[] { COLUMN_DATA });
			GetRowResponse getRowResponse = tsDao.getClient(TSINSTANCE_NAME).getRow(new GetRowRequest(criteria));
			Row row1 = getRowResponse.getRow();
			if (row1 == null) {
				log.info("获取" + TABLE_NAME + "数据失败：itemid=" + itemid + "，rq=" + rq);
				result.setCodeContent(101, "获取" + TABLE_NAME + "数据失败：itemid=" + itemid + "，rq=" + rq);
				return result;
			}
			/******************************* 步骤二：处理数据 *******************************/
			String _data = ""; // 数据
			if (row1.getLatestColumn(COLUMN_DATA) != null && row1.getLatestColumn(COLUMN_DATA).getValue() != null) {
				_data = row1.getLatestColumn(COLUMN_DATA).getValue().asString();
			}
			result.putKey(COLUMN_DATA, _data);
			result.setCodeContent(Result.SUCCESS, "");
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			result.setCodeContent(102, "表格存储访问错误！");
		}
		return result;
	}

}
