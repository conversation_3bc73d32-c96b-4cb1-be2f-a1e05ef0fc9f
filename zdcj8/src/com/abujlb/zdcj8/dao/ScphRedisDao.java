package com.abujlb.zdcj8.dao;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.abujlb.dao.RedisDao;

import redis.clients.jedis.Jedis;
import redis.clients.jedis.exceptions.JedisException;

@Component
public class ScphRedisDao {
	private static Logger log = Logger.getLogger("ScphRedisDao");
	@Autowired
	private RedisDao redisDao;

	/**
	 * 
	 * @param cateid
	 *            类目id
	 * @param dateType
	 *            日期类型 recent30,recent7
	 * @param nDate
	 *            最后日期:
	 * @param type
	 *            shop/item
	 * @param actionCode
	 *            1：高交易，2：高流量
	 * @return data数据
	 */
	public String get(String cateid, String dateType, String nDate, String type, int actionCode) {
		String key = String.format("scph:%s:%s:%s:%s:%d", cateid, dateType, nDate, type, actionCode);
		Jedis jedis = null;
		try {
			jedis = redisDao.getJedis();
			String result = jedis.get(key);
			return result;
		} catch (JedisException je) {
			redisDao.returnBrokenResource(jedis);
			jedis = null;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		} finally {
			redisDao.returnResource(jedis);
		}
		return null;

	}

	/**
	 * 
	 * @param cateid
	 *            类目id
	 * @param dateType
	 * @param nDate
	 * @param type
	 * @param actionCode
	 * @param data
	 * @return
	 */
	public void save(String cateid, String dateType, String nDate, String type, int actionCode, String data) {
		String key = String.format("scph:%s:%s:%s:%s:%d", cateid, dateType, nDate, type, actionCode);
		Jedis jedis = null;
		try {
			jedis = redisDao.getJedis();
			jedis.set(key, data);
			// 保留36小时
			jedis.expire(key, 36 * 60 * 60);
		} catch (JedisException je) {
			redisDao.returnBrokenResource(jedis);
			jedis = null;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		} finally {
			redisDao.returnResource(jedis);
		}
	}
}
