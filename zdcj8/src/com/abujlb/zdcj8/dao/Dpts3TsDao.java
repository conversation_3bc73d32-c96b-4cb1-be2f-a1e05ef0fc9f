package com.abujlb.zdcj8.dao;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.abujlb.dao.RedisDao;
import com.abujlb.dao.TsDao;
import com.abujlb.zdcj8.bean.Dpts3Para;
import com.alicloud.openservices.tablestore.model.Column;
import com.alicloud.openservices.tablestore.model.ColumnValue;
import com.alicloud.openservices.tablestore.model.GetRowRequest;
import com.alicloud.openservices.tablestore.model.GetRowResponse;
import com.alicloud.openservices.tablestore.model.PrimaryKey;
import com.alicloud.openservices.tablestore.model.PrimaryKeyBuilder;
import com.alicloud.openservices.tablestore.model.PrimaryKeyValue;
import com.alicloud.openservices.tablestore.model.PutRowRequest;
import com.alicloud.openservices.tablestore.model.Row;
import com.alicloud.openservices.tablestore.model.RowPutChange;
import com.alicloud.openservices.tablestore.model.SingleRowQueryCriteria;

import redis.clients.jedis.Jedis;
import redis.clients.jedis.exceptions.JedisException;

@Component
public class Dpts3TsDao {
	private static Logger log = Logger.getLogger(Dpts3TsDao.class);

	private static final String TABLE_DPTS3 = "dpts3_dpts3";
	private static final String COLUMN_BBID_TYPE = "bbid_type";
	private static final String COLUMN_DEVICE = "device";
	private static final String COLUMN_DATETYPE = "datetype";
	private static final String COLUMN_DATE = "date";
	private static final String COLUMN_DATA = "data";

	private static final String DPTS3_REDISKEY = "dpts3_today_";

	@Autowired
	private TsDao tsDao;
	@Autowired
	private RedisDao redisDao;

	public String getDpts3Data(Dpts3Para p, String type) {
		if (p.getDateType().equalsIgnoreCase(Dpts3Para.TODAY) || p.getDateType().equalsIgnoreCase(Dpts3Para.RECENT7)
				|| p.getDateType().equalsIgnoreCase(Dpts3Para.RECENT30)) {
			Jedis jedis = null;
			try {
				jedis = redisDao.getJedis();
				String result = jedis
						.get(DPTS3_REDISKEY + p.getBbid() + "_" + type + "_" + p.getDateType() + "_" + p.getDevice());
				if (result != null) {
					return result;
				}
			} catch (JedisException je) {
				redisDao.returnBrokenResource(jedis);
				jedis = null;
			} catch (Exception e) {
				log.error(e.getMessage(), e);
			} finally {
				redisDao.returnResource(jedis);
			}
		}

		if (p.getDateType().equalsIgnoreCase(Dpts3Para.TODAY)) {
			// 是实时，则只从redis中获取

		} else {
			// 否则从表格存储中获取
			try {
				PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
				primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_BBID_TYPE, PrimaryKeyValue.fromString(p.getBbid() + type));
				primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_DEVICE, PrimaryKeyValue.fromString(p.getDevice()));
				primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_DATETYPE, PrimaryKeyValue.fromString(p.getDateType()));
				if (p.getDateType().equalsIgnoreCase(Dpts3Para.DAY)) {
					primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_DATE, PrimaryKeyValue.fromString(p.getDate()));
				} else {
					primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_DATE, PrimaryKeyValue.fromString(p.getUpdateNDay()));
				}

				PrimaryKey primaryKey = primaryKeyBuilder.build();

				SingleRowQueryCriteria criteria = null;
				criteria = new SingleRowQueryCriteria(TABLE_DPTS3, primaryKey);
				criteria.setMaxVersions(1);
				criteria.addColumnsToGet(COLUMN_DATA);
				GetRowResponse getRowResponse = tsDao.getClient("abujlb2").getRow(new GetRowRequest(criteria));
				Row row = getRowResponse.getRow();
				String json = null;

				if (row != null) {
					json = row.getLatestColumn(COLUMN_DATA).getValue().asString();
					return json;
				}

			} catch (Throwable e) {
				e.printStackTrace();
			}
		}

		return null;
	}

	public boolean saveDpts3Data(Dpts3Para p, String type, String data) {
		if (p.getDateType().equalsIgnoreCase(Dpts3Para.TODAY) || p.getDateType().equalsIgnoreCase(Dpts3Para.RECENT7)
				|| p.getDateType().equalsIgnoreCase(Dpts3Para.RECENT30)) {
			// 是实时，则写入redis中
			Jedis jedis = null;
			try {
				jedis = redisDao.getJedis();
				jedis.set(DPTS3_REDISKEY + p.getBbid() + "_" + type + "_" + p.getDateType() + "_" + p.getDevice(),
						data);
				jedis.expire(DPTS3_REDISKEY + p.getBbid() + "_" + type + "_" + p.getDateType() + "_" + p.getDevice(),
						5 * 60);
			} catch (JedisException je) {
				redisDao.returnBrokenResource(jedis);
				jedis = null;
			} catch (Exception e) {
				log.error(e.getMessage(), e);
			} finally {
				redisDao.returnResource(jedis);
			}
		}
		if (p.getDateType().equalsIgnoreCase(Dpts3Para.TODAY)) {
			// 实时，则只写入redis中
			return true;
		} else {
			// 否则写入表格存储中
			try {
				PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
				primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_BBID_TYPE, PrimaryKeyValue.fromString(p.getBbid() + type));
				primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_DEVICE, PrimaryKeyValue.fromString(p.getDevice()));
				primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_DATETYPE, PrimaryKeyValue.fromString(p.getDateType()));
				if (p.getDateType().equalsIgnoreCase(Dpts3Para.DAY)) {
					primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_DATE, PrimaryKeyValue.fromString(p.getDate()));
				} else {
					primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_DATE, PrimaryKeyValue.fromString(p.getUpdateNDay()));
				}
				PrimaryKey primaryKey = primaryKeyBuilder.build();
				RowPutChange rowPutChange = null;
				rowPutChange = new RowPutChange(TABLE_DPTS3, primaryKey);
				rowPutChange.addColumn(new Column(COLUMN_DATA, ColumnValue.fromString(data)));
				tsDao.getClient("abujlb2").putRow(new PutRowRequest(rowPutChange));
				return true;
			} catch (Throwable e) {
				e.printStackTrace();
				return false;
			}
		}

	}
}
