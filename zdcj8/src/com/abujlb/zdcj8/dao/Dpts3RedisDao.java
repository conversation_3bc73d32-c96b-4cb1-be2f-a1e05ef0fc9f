package com.abujlb.zdcj8.dao;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.abujlb.ckstore.TopItem;
import com.abujlb.dao.RedisDao;

import redis.clients.jedis.Jedis;
import redis.clients.jedis.exceptions.JedisException;

@Component
public class Dpts3RedisDao {
	private static Logger log = Logger.getLogger(TopItem.class);
	private static final String COMMONDATE_REDISKEY = "commondate_";

	@Autowired
	private RedisDao redisDao;

	public String getCommonDate(String type) {
		String result = null;
		Jedis jedis = null;
		try {
			jedis = redisDao.getJedis();
			result = jedis.get(COMMONDATE_REDISKEY + type);
		} catch (JedisException je) {
			redisDao.returnBrokenResource(jedis);
			jedis = null;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		} finally {
			redisDao.returnResource(jedis);
		}
		return result;
	}
}
