package com.abujlb.zdcj8.dao;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.abujlb.dao.TsDao;
import com.abujlb.zdcj8.bean.ShoptsData;
import com.alicloud.openservices.tablestore.model.Column;
import com.alicloud.openservices.tablestore.model.ColumnValue;
import com.alicloud.openservices.tablestore.model.GetRowRequest;
import com.alicloud.openservices.tablestore.model.GetRowResponse;
import com.alicloud.openservices.tablestore.model.PrimaryKey;
import com.alicloud.openservices.tablestore.model.PrimaryKeyBuilder;
import com.alicloud.openservices.tablestore.model.PrimaryKeyValue;
import com.alicloud.openservices.tablestore.model.PutRowRequest;
import com.alicloud.openservices.tablestore.model.Row;
import com.alicloud.openservices.tablestore.model.RowPutChange;
import com.alicloud.openservices.tablestore.model.SingleRowQueryCriteria;
import com.google.gson.Gson;

@Component("shoptsDao")
public class ShoptsDao {
	private static final String TABLE_DPTS_SHOP = "dpts_shop";

	private static final String COLUMN_USERID = "userid";
	private static final String COLUMN_CATEID = "cateid";
	private static final String COLUMN_DATE = "date";
	private static final String COLUMN_DATA = "data";

	@Autowired
	private TsDao tsDao;

	public ShoptsData getData(String userid, String cateid, String date) {
		try {
			PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
			primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_USERID, PrimaryKeyValue.fromString(userid));
			primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_CATEID, PrimaryKeyValue.fromString(cateid));
			primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_DATE, PrimaryKeyValue.fromString(date));
			PrimaryKey primaryKey = primaryKeyBuilder.build();

			SingleRowQueryCriteria criteria = null;
			criteria = new SingleRowQueryCriteria(TABLE_DPTS_SHOP, primaryKey);
			criteria.setMaxVersions(1);
			criteria.addColumnsToGet(COLUMN_DATA);
			GetRowResponse getRowResponse = tsDao.getClient().getRow(new GetRowRequest(criteria));
			Row row = getRowResponse.getRow();
			String json = null;

			if (row != null) {
				json = row.getLatestColumn(COLUMN_DATA).getValue().asString();
				Gson gson = new Gson();
				return gson.fromJson(json, ShoptsData.class);
			}

		} catch (Throwable e) {
			e.printStackTrace();
		}
		return null;
	}

	public boolean saveData(String userid, String date, String cateid, ShoptsData data) {
		try {
			Gson gson = new Gson();
			PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
			primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_USERID, PrimaryKeyValue.fromString(userid));
			primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_CATEID, PrimaryKeyValue.fromString(cateid));
			primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_DATE, PrimaryKeyValue.fromString(date));
			PrimaryKey primaryKey = primaryKeyBuilder.build();
			RowPutChange rowPutChange = null;
			rowPutChange = new RowPutChange(TABLE_DPTS_SHOP, primaryKey);
			rowPutChange.addColumn(new Column(COLUMN_DATA, ColumnValue.fromString(gson.toJson(data))));
			tsDao.getClient().putRow(new PutRowRequest(rowPutChange));
			return true;
		} catch (Throwable e) {
			e.printStackTrace();
			return false;
		}
	}
}
