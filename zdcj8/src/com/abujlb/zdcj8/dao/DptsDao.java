package com.abujlb.zdcj8.dao;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.abujlb.dao.TsDao;
import com.abujlb.zdcj8.bean.DptsGjcJob;
import com.abujlb.zdcj8.bean.DptsPara;
import com.abujlb.zdcj8.bean.SpuidPara;
import com.alicloud.openservices.tablestore.model.Column;
import com.alicloud.openservices.tablestore.model.ColumnValue;
import com.alicloud.openservices.tablestore.model.GetRowRequest;
import com.alicloud.openservices.tablestore.model.GetRowResponse;
import com.alicloud.openservices.tablestore.model.PrimaryKey;
import com.alicloud.openservices.tablestore.model.PrimaryKeyBuilder;
import com.alicloud.openservices.tablestore.model.PrimaryKeyValue;
import com.alicloud.openservices.tablestore.model.PutRowRequest;
import com.alicloud.openservices.tablestore.model.Row;
import com.alicloud.openservices.tablestore.model.RowPutChange;
import com.alicloud.openservices.tablestore.model.SingleRowQueryCriteria;

@Component("dptsDao")
public class DptsDao {
	private static final String TABLE_DPTS = "dpts_summary";
	private static final String TABLE_DPTS2 = "dpts2_summary";
	private static final String TABLE_DPTSSPU = "dpts2_spu";
	private static final String TABLE_DPTSLLJG = "dpts_lljg";

	private static final String COLUMN_SPUID = "spuid";
	private static final String COLUMN_BBID = "bbid";
	private static final String COLUMN_DEVICE = "device";
	private static final String COLUMN_DATETYPE = "datetype";
	private static final String COLUMN_DATE = "date";
	private static final String COLUMN_DAYS = "days";
	private static final String COLUMN_DATA = "data";

	@Autowired
	private TsDao tsDao;

	public String getDptsSpu(DptsPara p) {
		try {
			PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
			primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_SPUID, PrimaryKeyValue.fromString(p.getBbid()));
			primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_DEVICE, PrimaryKeyValue.fromString(p.getDevice()));
			primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_DATE, PrimaryKeyValue.fromString(p.getDate()));
			PrimaryKey primaryKey = primaryKeyBuilder.build();

			SingleRowQueryCriteria criteria = null;
			criteria = new SingleRowQueryCriteria(TABLE_DPTSSPU, primaryKey);
			criteria.setMaxVersions(1);
			criteria.addColumnsToGet(COLUMN_DATA);
			GetRowResponse getRowResponse = tsDao.getClient().getRow(new GetRowRequest(criteria));
			Row row = getRowResponse.getRow();
			String json = null;

			if (row != null) {
				json = row.getLatestColumn(COLUMN_DATA).getValue().asString();
				return json;
			}

		} catch (Throwable e) {
			e.printStackTrace();
		}
		return null;
	}

	public boolean saveDptsSpu(DptsPara p, String data) {
		try {
			PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
			primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_SPUID, PrimaryKeyValue.fromString(p.getBbid()));
			primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_DEVICE, PrimaryKeyValue.fromString(p.getDevice()));
			primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_DATE, PrimaryKeyValue.fromString(p.getDate()));
			PrimaryKey primaryKey = primaryKeyBuilder.build();
			RowPutChange rowPutChange = null;
			rowPutChange = new RowPutChange(TABLE_DPTSSPU, primaryKey);
			rowPutChange.addColumn(new Column(COLUMN_DATA, ColumnValue.fromString(data)));
			tsDao.getClient().putRow(new PutRowRequest(rowPutChange));
			return true;
		} catch (Throwable e) {
			e.printStackTrace();
			return false;
		}
	}

	public String getSummary(SpuidPara p) {
		try {
			PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
			primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_SPUID, PrimaryKeyValue.fromString(p.getSpuid()));
			primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_DEVICE, PrimaryKeyValue.fromString(p.getDevice()));
			primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_DATETYPE, PrimaryKeyValue.fromString(p.getDateType()));
			primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_DATE, PrimaryKeyValue.fromString(p.getDate()));
			PrimaryKey primaryKey = primaryKeyBuilder.build();

			SingleRowQueryCriteria criteria = null;
			criteria = new SingleRowQueryCriteria(TABLE_DPTS, primaryKey);
			criteria.setMaxVersions(1);
			criteria.addColumnsToGet(COLUMN_DATA);
			GetRowResponse getRowResponse = tsDao.getClient().getRow(new GetRowRequest(criteria));
			Row row = getRowResponse.getRow();
			String json = null;

			if (row != null) {
				json = row.getLatestColumn(COLUMN_DATA).getValue().asString();
				return json;
			}

		} catch (Throwable e) {
			e.printStackTrace();
		}
		return null;
	}

	public boolean saveSummary(SpuidPara p, String data) {
		try {
			PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
			primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_SPUID, PrimaryKeyValue.fromString(p.getSpuid()));
			primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_DEVICE, PrimaryKeyValue.fromString(p.getDevice()));
			primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_DATETYPE, PrimaryKeyValue.fromString(p.getDateType()));
			primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_DATE, PrimaryKeyValue.fromString(p.getDate()));
			PrimaryKey primaryKey = primaryKeyBuilder.build();
			RowPutChange rowPutChange = null;
			rowPutChange = new RowPutChange(TABLE_DPTS, primaryKey);
			rowPutChange.addColumn(new Column(COLUMN_DATA, ColumnValue.fromString(data)));
			tsDao.getClient().putRow(new PutRowRequest(rowPutChange));
			return true;
		} catch (Throwable e) {
			e.printStackTrace();
			return false;
		}
	}

	public String getSummary(DptsPara p) {
		try {
			PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
			primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_BBID, PrimaryKeyValue.fromString(p.getBbid()));
			primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_DEVICE, PrimaryKeyValue.fromString(p.getDevice()));
			primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_DATE, PrimaryKeyValue.fromString(p.getDate()));
			PrimaryKey primaryKey = primaryKeyBuilder.build();

			SingleRowQueryCriteria criteria = null;
			criteria = new SingleRowQueryCriteria(TABLE_DPTS2, primaryKey);
			criteria.setMaxVersions(1);
			criteria.addColumnsToGet(COLUMN_DATA);
			GetRowResponse getRowResponse = tsDao.getClient().getRow(new GetRowRequest(criteria));
			Row row = getRowResponse.getRow();
			String json = null;

			if (row != null) {
				json = row.getLatestColumn(COLUMN_DATA).getValue().asString();
				return json;
			}

		} catch (Throwable e) {
			e.printStackTrace();
		}
		return null;
	}

	public boolean saveSummary(DptsPara p, String data) {
		try {
			PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
			primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_BBID, PrimaryKeyValue.fromString(p.getBbid()));
			primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_DEVICE, PrimaryKeyValue.fromString(p.getDevice()));
			primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_DATE, PrimaryKeyValue.fromString(p.getDate()));
			PrimaryKey primaryKey = primaryKeyBuilder.build();
			RowPutChange rowPutChange = null;
			rowPutChange = new RowPutChange(TABLE_DPTS2, primaryKey);
			rowPutChange.addColumn(new Column(COLUMN_DATA, ColumnValue.fromString(data)));
			tsDao.getClient().putRow(new PutRowRequest(rowPutChange));
			return true;
		} catch (Throwable e) {
			e.printStackTrace();
			return false;
		}
	}

	public String getLljg(DptsGjcJob p) {
		try {
			PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
			primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_BBID, PrimaryKeyValue.fromString(p.getBbid()));
			primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_DEVICE, PrimaryKeyValue.fromString(p.getDevice()));
			primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_DATE, PrimaryKeyValue.fromString(p.getDate()));
			primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_DAYS, PrimaryKeyValue.fromLong(p.getDays()));
			PrimaryKey primaryKey = primaryKeyBuilder.build();

			SingleRowQueryCriteria criteria = null;
			criteria = new SingleRowQueryCriteria(TABLE_DPTSLLJG, primaryKey);
			criteria.setMaxVersions(1);
			criteria.addColumnsToGet(COLUMN_DATA);
			GetRowResponse getRowResponse = tsDao.getClient().getRow(new GetRowRequest(criteria));
			Row row = getRowResponse.getRow();
			String json = null;

			if (row != null) {
				json = row.getLatestColumn(COLUMN_DATA).getValue().asString();
				return json;
			}

		} catch (Throwable e) {
			e.printStackTrace();
		}
		return null;
	}

	public boolean saveLljg(DptsGjcJob p, String data) {
		try {
			PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
			primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_BBID, PrimaryKeyValue.fromString(p.getBbid()));
			primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_DEVICE, PrimaryKeyValue.fromString(p.getDevice()));
			primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_DATE, PrimaryKeyValue.fromString(p.getDate()));
			primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_DAYS, PrimaryKeyValue.fromLong(p.getDays()));
			PrimaryKey primaryKey = primaryKeyBuilder.build();
			RowPutChange rowPutChange = null;
			rowPutChange = new RowPutChange(TABLE_DPTSLLJG, primaryKey);
			rowPutChange.addColumn(new Column(COLUMN_DATA, ColumnValue.fromString(data)));
			tsDao.getClient().putRow(new PutRowRequest(rowPutChange));
			return true;
		} catch (Throwable e) {
			e.printStackTrace();
			return false;
		}
	}
}
