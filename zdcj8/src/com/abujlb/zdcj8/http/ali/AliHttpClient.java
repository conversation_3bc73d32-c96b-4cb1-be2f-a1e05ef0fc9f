package com.abujlb.zdcj8.http.ali;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.annotation.PostConstruct;

import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.CookieStore;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.config.RequestConfig.Builder;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.cookie.Cookie;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.cookie.BasicClientCookie;
import org.apache.http.util.EntityUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.abujlb.Result;
import com.abujlb.retry.Retry;
import com.abujlb.util.DateUtil;
import com.abujlb.zdcj8.bean.CookieVo;
import com.abujlb.zdcj8.bean.DpsxBb;
import com.abujlb.zdcj8.bean.DpsxPara;
import com.abujlb.zdcj8.constant.AliConst;
import com.abujlb.zdcj8.script.DpsxSignJSEngine;
import com.abujlb.zdcj8.util.StrUtil;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

/**
 * 阿里http请求
 * 
 * <AUTHOR>
 * @date 2021-03-16 10:12:15
 */
@Component
public class AliHttpClient {

	private static Logger log = Logger.getLogger(AliHttpClient.class);

	public static final String APPKEY = "********";
	/**
	 * 获取cna（可用），确认时间：2021-03-16 11:30
	 */
	public static final String CNA_URL = "https://log.mmstat.com/eg.js";
	/**
	 * 获取_m_h5_tk（可用），确认时间：2021-03-16 11:50
	 * 
	 * @说明：很多h5页面都有这个接口出现， {"api":"mtop.taobao.maserati.fansupgrade.rank","data":{"context":{},"ok":true,"result":{}},"ret":["SUCCESS::调用成功"],"v":"1.0"}
	 * @接口：https://h5api.m.taobao.com/h5/mtop.taobao.maserati.fansupgrade.rank/1.0/?jsv=2.4.2&appKey=********&t=*************&sign=942a6a112d5cb4ef18661762ed882679&api=mtop.taobao.maserati.fansupgrade.rank&v=1.0&AntiCreep=true&dataType=json&type=originaljson&data=%7B%22accountId%22%3A%************%22%7D
	 * @接口2：https://h5api.m.tmall.com/h5/mtop.taobao.baichuan.smb.get/1.0/?jsv=2.4.8&appKey=********&t=*************&sign=3b75a56b01564006c5988a2d94ae0730&api=mtop.taobao.baichuan.smb.get&v=1.0&type=originaljson&dataType=jsonp&timeout=10000
	 * @接口3：https://h5api.m.taobao.com/h5/mtop.tmall.kangaroo.core.service.route.aldlampservice/1.0/?jsv=2.5.1&appKey=********&t=*************&sign=5cca29d3555ff28ee9259074e24ac32d&api=mtop.tmall.kangaroo.core.service.route.AldLampService&v=1.0&type=jsonp&dataType=jsonp&timeout=20000&callback=mtopjsonp1&data=%7B%22appId%22%3A10760644%2C%22source%22%3A%22taobao_pc_focus_personal%22%2C%22count%22%3A2%2C%22bizId%22%3A20200922%7D
	 * @其中data参数示例：{"accountId":"**********"}
	 */
	public static final String H5_TK_URL = "https://h5api.m.taobao.com/h5/mtop.taobao.maserati.fansupgrade.rank/1.0/?jsv=2.4.2&appKey=%s&t=%s&sign=%s&api=mtop.taobao.maserati.fansupgrade.rank&v=1.0&AntiCreep=true&dataType=json&type=originaljson&data=";
	public static final String H5_TK_URL2 = "https://h5api.m.tmall.com/h5/mtop.taobao.baichuan.smb.get/1.0/?jsv=2.4.8&appKey=%s&t=%s&sign=%s&api=mtop.taobao.baichuan.smb.get&v=1.0&type=originaljson&dataType=jsonp&timeout=10000";
	/**
	 * 店铺上新（可用），确认时间：2021-03-16 11:20
	 * 
	 * @说明：手机淘宝 -> 店铺页面 -> 微淘 ，上新，https://market.m.taobao.com/app/tb-source-app/wz111/pages/weitao?pathInfo=shop/weitao&userId=**********&shopId=*********&_w&pageId=*********&alisite=true&sellerId=**********&shop_navi=dongtai&displayShopHeader=true
	 * @接口：https://h5api.m.taobao.com/h5/mtop.taobao.maserati.fansupgrade.gettabfeeds/1.0/?jsv=2.4.2&appKey=********&t=*************&sign=a8e642bc2a3acc408e494ac246cc5663&api=mtop.taobao.maserati.fansupgrade.getTabFeeds&v=1.0&type=originaljson&H5Request=true&timeout=3000&AntiCreep=true&dataType=json&data=%7B%22userId%22%3A**********%2C%22lastTime%22%3A0%2C%22tab%22%3A1%2C%22source%22%3Anull%7D
	 * @其中data参数示例：{"userId":**********,"lastTime":0,"tab":1,"source":null}
	 */
	public static final String DPSX_URL = "https://h5api.m.taobao.com/h5/mtop.taobao.maserati.fansupgrade.gettabfeeds/1.0/?jsv=2.4.2&appKey=%s&t=%s&sign=%s&api=mtop.taobao.maserati.fansupgrade.getTabFeeds&v=1.0&type=originaljson&H5Request=true&timeout=3000&AntiCreep=true&dataType=json&data="; //
	/**
	 * 宝贝详情、无线详情（可用），确认时间：2021-03-16 15:00
	 * 
	 * @说明：数据蛇 -> 竞品透视中发现，但是需要用到淘宝cookie，且成功率低，容易出现滑块
	 * @说明2：此接口就是我们一直使用第三方的商品详情接口
	 * @接口：https://h5api.m.taobao.com/h5/mtop.taobao.detail.getdetail/6.0/?ttid=2017%40taobao_h5_6.6.0&data=itemNumId%22%3A%22622385134292%22&callback=__jp0
	 * @接口：https://descnew.taobao.com/i6/620/380/622385134292/TB1xqkCMuL2gK0jSZFm8qw7iXla.desc%7Cvar%5Edesc%3Bsign%5E3c9fcc2de44b7a1b556b26ea53eba2f3%3Blang%5Egbk%3Bt%5E1615571149
	 * @其中data参数示例：itemNumId":"622385134292"
	 */
	public static final String BBXQ_URL = "https://h5api.m.taobao.com/h5/mtop.taobao.detail.getdetail/6.0/?ttid=2017%40taobao_h5_6.6.0&callback=__jp0&data=";

	/**
	 * 淘客订单查询（可用），确认时间：
	 * 
	 * @说明：没有源页面
	 * @接口：https://h5api.m.taobao.com/h5/mtop.alimama.union.rpt.single.order.detail/1.0/?jsv=2.5.1&appKey=********&t=1615788379366&sign=cf2a679fb33d0b5cdffdf3c576bec94c&api=mtop.alimama.union.rpt.single.order.detail&v=1.0&AntiCreep=true&AntiFlood=true&type=jsonp&ecode=0&dataType=jsonp&callback=mtopjsonp2&data=%7B%22tbTradeParentId%22%3A%221642791852157767720%22%7D
	 * @其中data参数示例：{"tbTradeParentId":"1642791852157767720"}
	 */
	// 手机淘客订单查询页面，仅真机手机淘宝可以打开：https://mos.m.taobao.com/union/query_cps_result?tbTradeParentId=1645099165362620829
	// 订单接口，应该是老版接口：https://api.m.taobao.com/gw/mtop.alimama.moon.provider.detail.orders.get/1.0/?data=%7B%22tbTradeParentId%22:%221645099165362620829%22%7D
	// 用户调用接口申请，：https://h5api.m.taobao.com/h5/mtop.user.getusersimple/1.0/?jsv=2.5.1&appKey=********&t=1615862574576&sign=b098dca8fd4df3fefe666fc50e674ea4&api=mtop.user.getUserSimple&v=1.0&ecode=1&sessionOption=AutoLoginOnly&jsonpIncPrefix=liblogin&type=jsonp&dataType=jsonp&callback=mtopjsonpliblogin1&data=%7B%7D
	public static final String TKDD_URL = "https://h5api.m.taobao.com/h5/mtop.alimama.union.rpt.single.order.detail/1.0/?jsv=2.5.1&appKey=%s&t=%s&sign=%s&api=mtop.alimama.union.rpt.single.order.detail&v=1.0&AntiCreep=true&AntiFlood=true&type=jsonp&ecode=0&dataType=jsonp&callback=mtopjsonp2&data=";

	/**
	 * 用户积分（可用），确认时间：2021-03-16
	 * 
	 * @说明：天猫首页(www.tmall.com)
	 * @接口：https://top-tmm.taobao.com/member/query_member_top.do?callback=_initMemberInfoCallback&is_new=true&t=1615886342688
	 * @说明2：淘宝首页(www.taobao.com) -> 鼠标放在页面上方，“购物车”触发接口
	 * @接口2：https://cart.taobao.com/trail_mini_cart.htm?callback=MiniCart.setData&t=1615891127937
	 * @其中data参数示例：{"tbTradeParentId":"1642791852157767720"}
	 */
	public static final String MEMBER_URL = "https://top-tmm.taobao.com/member/query_member_top.do?callback=_initMemberInfoCallback&is_new=true&t=%s";
	public static final String CART_URL = "https://cart.taobao.com/trail_mini_cart.htm?callback=MiniCart.setData&t=%s";

	// @Autowired
	// private Config config;
	// private static String temp_path = null;

	@Autowired
	private DpsxSignJSEngine signJSEngine;

	/**
	 * 初始化
	 * 
	 */
	@PostConstruct
	public void init() {
		try {
			// temp_path = config.getString("tempdir");
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		}
	}

	/**
	 * 获取cna
	 * 
	 * @return json
	 */
	@Retry
	public CookieStore getCna(CookieStore cookieStore) {
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cookieStore).build();
		HttpGet httpGet = new HttpGet(CNA_URL);
		httpGet.setHeader("accept", "*/*");
		httpGet.setHeader("accept-encoding", "gzip, deflate, br");
		httpGet.setHeader("accept-language", "zh-CN,zh;q=0.9");
		httpGet.setHeader("referer", "https://market.m.taobao.com/");
		httpGet.setHeader("user-agent", "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.75 Mobile Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(10000).setConnectTimeout(10000).setConnectionRequestTimeout(10000);
		httpGet.setConfig(requestConfig.build());
		HttpResponse httpresponse;
		try {
			httpresponse = httpClient.execute(httpGet);
			HttpEntity entity = httpresponse.getEntity(); // window.goldlog=(window.goldlog||{});goldlog.Etag="Ys7oF43vdwACASQFXoq65qDb";goldlog.stag=1;
			String body = EntityUtils.toString(entity); // 例如：window.goldlog=(window.goldlog||{});goldlog.Etag="nj8qGLIpVhICAXVAkun7PXTj";goldlog.stag=0;
			if (StrUtil.isNull(body)) {
				log.error("************************ getCna，返回结果是空！ ************************");
				return null;
			}
			String cna2 = body.substring(body.indexOf(".Etag=\"") + 7, body.indexOf("\";goldlog"));
			List<Cookie> list = cookieStore.getCookies();
			if (list == null || list.size() < 1) {
				log.error("************************ getCna，cookie回写失败！ ************************");
				return null;
			}
			String cna = null;
			for (Cookie cookie : list) {
				if (cookie.getName().equalsIgnoreCase("cna")) {
					cna = cookie.getValue();
				}
			}
			if (StrUtil.isNull(cna) && cookieStore != null) { // 新增cna
				BasicClientCookie cookie = new BasicClientCookie("cna", cna2);
				cookie.setDomain(".taobao.com");
				cookie.setPath("/");
				cookie.setCreationDate(new Date());
				cookie.setExpiryDate(DateUtil.afterMonth(new Date(), 36)); // 36个月之后
				cookie.setSecure(true);
				cookie.setVersion(1);
				cookieStore.addCookie(cookie);

				BasicClientCookie cookie2 = new BasicClientCookie("cna", cna2);
				cookie2.setDomain(".mmstat.com");
				cookie2.setPath("/");
				cookie2.setCreationDate(new Date());
				cookie2.setExpiryDate(DateUtil.afterMonth(new Date(), 36)); // 36个月之后
				cookie2.setSecure(true);
				cookie2.setVersion(1);
				cookieStore.addCookie(cookie2);
			}
			return cookieStore;
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		} finally {
			httpGet.releaseConnection();
		}
		return null;
	}

	/**
	 * 获取_m_h5_tk
	 * 
	 * @param userId 店铺用户id
	 * @return CookieStore
	 */
	@Retry
	public Result getH5TkForShop(CookieStore cookieStore, String userId) {
		Result result = new Result(2, "获取数据失败！");
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cookieStore).build();
		long timestamp = System.currentTimeMillis(); // 时间戳
		String data = "{\"accountId\":\"" + userId + "\"}";
		String url = String.format(H5_TK_URL, APPKEY, String.valueOf(timestamp), ""); // 注意：sign参数不给也能正常访问，获取cookie
		try {
			url = url + URLEncoder.encode(data, "UTF-8");
		} catch (UnsupportedEncodingException e1) {
			e1.printStackTrace();
			log.error(e1.getMessage(), e1);
		}
		HttpGet httpGet = new HttpGet(url);
		httpGet.setHeader("accept", "application/json");
		httpGet.setHeader("accept-encoding", "gzip, deflate, br");
		httpGet.setHeader("accept-language", "zh-CN,zh;q=0.9");
		httpGet.setHeader("content-type", "application/x-www-form-urlencoded");
		// httpGet.setHeader("cookie", "cna=nj8qGLIpVhICAXVAkun7PXTj");
		httpGet.setHeader("origin", "https://market.m.taobao.com");
		httpGet.setHeader("referer", "https://market.m.taobao.com/");
		httpGet.setHeader("user-agent", "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.75 Mobile Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(10000).setConnectTimeout(10000).setConnectionRequestTimeout(10000);
		httpGet.setConfig(requestConfig.build());
		HttpResponse httpresponse;
		try {
			httpresponse = httpClient.execute(httpGet);
			HttpEntity entity = httpresponse.getEntity();
			String body = EntityUtils.toString(entity);
			if (StrUtil.isNull(body)) {
				log.error("************************ getH5TkForShop，返回结果是空！ ************************");
				return null;
			}
			String _m_h5_tk = "";
			List<Cookie> list = cookieStore.getCookies();
			if (list == null || list.size() < 1) {
				log.error("************************ getH5TkForShop，cookie回写失败！ ************************");
				return null;
			}
			for (Cookie cookie : list) {
				if (cookie.getName().equalsIgnoreCase("_m_h5_tk")) {
					_m_h5_tk = cookie.getValue();
				}
			}
			if (StrUtil.isNull(_m_h5_tk)) {
				log.error("************************ getH5TkForShop，cookie回写失败2！ ************************");
				return null;
			}
			result.setCodeContent(Result.SUCCESS, "success");
			result.putKey("cookieStore", cookieStore);
			result.putKey("_m_h5_tk", _m_h5_tk);
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		} finally {
			httpGet.releaseConnection();
		}
		return null;
	}

	/**
	 * 获取_m_h5_tk
	 * 
	 * @return CookieStore
	 */
	@Retry
	public Result getH5TkForUser(CookieStore cookieStore) {
		Result result = new Result(2, "获取数据失败！");
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cookieStore).build();
		long timestamp = System.currentTimeMillis(); // 时间戳
		String url = String.format(H5_TK_URL2, APPKEY, String.valueOf(timestamp), ""); // 注意：sign参数不给也能正常访问，获取cookie
		HttpGet httpGet = new HttpGet(url);
		httpGet.setHeader("accept", "application/json");
		httpGet.setHeader("accept-encoding", "gzip, deflate, br");
		httpGet.setHeader("accept-language", "zh-CN,zh;q=0.9");
		httpGet.setHeader("content-type", "application/x-www-form-urlencoded");
		// httpGet.setHeader("cookie", "cna=nj8qGLIpVhICAXVAkun7PXTj");
		httpGet.setHeader("origin", "https://detail.m.tmall.com");
		httpGet.setHeader("referer", "https://detail.m.tmall.com/");
		httpGet.setHeader("user-agent", "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Mobile Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(10000).setConnectTimeout(10000).setConnectionRequestTimeout(10000);
		httpGet.setConfig(requestConfig.build());
		HttpResponse httpresponse;
		try {
			httpresponse = httpClient.execute(httpGet);
			HttpEntity entity = httpresponse.getEntity();
			String body = EntityUtils.toString(entity); // 例如：{"api":"mtop.taobao.baichuan.smb.get","data":{},"ret":["FAIL_SYS_TOKEN_EMPTY::令牌为空"],"v":"1.0"}
			if (StrUtil.isNull(body)) {
				log.error("************************ getH5TkForUser，返回结果是空！ ************************");
				return null;
			}
			String _m_h5_tk = "";
			List<Cookie> list = cookieStore.getCookies();
			if (list == null || list.size() < 1) {
				log.error("************************ getH5TkForUser，cookie回写失败！ ************************");
				return null;
			}
			for (Cookie cookie : list) {
				if (cookie.getName().equalsIgnoreCase("_m_h5_tk")) {
					_m_h5_tk = cookie.getValue();
				}
			}
			if (StrUtil.isNull(_m_h5_tk)) {
				log.error("************************ getH5TkForUser，cookie回写失败2！ ************************");
				return null;
			}
			result.setCodeContent(Result.SUCCESS, "success");
			result.putKey("cookieStore", cookieStore);
			result.putKey("_m_h5_tk", _m_h5_tk);
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		} finally {
			httpGet.releaseConnection();
		}
		return null;
	}

	/**
	 * 主程序：店铺上新
	 * 
	 * @return result
	 */
	public Result dpsx(DpsxPara dpsxPara, CookieStore cookieStore, String _m_h5_tk) {
		Result result = null;
		int time = 0; // 尝试次数
		do {
			result = this.dpsx2(dpsxPara, cookieStore, _m_h5_tk);
			if (result != null && result.isSuccess()) {
				break; // 结束循环
			}
			time++;
			try {
				Thread.sleep(((time >= AliConst.TRY_NUM) ? 0 : time) * 2000L);
			} catch (Exception e) {
				e.printStackTrace();
			}
		} while (time < AliConst.TRY_NUM);
		if (result == null || !result.isSuccess()) {
			result = new Result(9, "超过最大尝试");
			log.info("获取dpsx，超过最大尝试次数：" + time);
		}
		return result;
	}

	/**
	 * 店铺上新
	 * 
	 * @param dpsxPara 
	 * @return result，(其中code：0.成功、1.cookie过期、2.失败、9.超过最大尝试次数)
	 */
	public Result dpsx2(DpsxPara dpsxPara, CookieStore cookieStore, String _m_h5_tk) {
		Result result = new Result(2, "获取店铺上新数据失败");
		long timestamp = System.currentTimeMillis(); // 时间戳
		String data = "{\"userId\":" + dpsxPara.getUserId() + ",\"lastTime\":" + dpsxPara.getLastTime() + ",\"tab\":1,\"source\":null}";
		String token = _m_h5_tk.split(";")[0].split("_")[0];
		String sign = signJSEngine.getSign(String.valueOf(timestamp), APPKEY, data, token); //
		String url = String.format(DPSX_URL, APPKEY, String.valueOf(timestamp), sign);
		try {
			url = url + URLEncoder.encode(data, "UTF-8");
		} catch (UnsupportedEncodingException e1) {
			e1.printStackTrace();
			log.error(e1.getMessage(), e1);
		}
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cookieStore).build();
		HttpGet httpGet = new HttpGet(url);
		httpGet.setHeader("accept", "application/json");
		httpGet.setHeader("accept-encoding", "gzip, deflate, br");
		httpGet.setHeader("accept-language", "zh-CN,zh;q=0.9");
		httpGet.setHeader("content-type", "application/x-www-form-urlencoded");
		// httpGet.setHeader("cookie", "cna=nj8qGLIpVhICAXVAkun7PXTj; _m_h5_tk=039e5f0bc983053f6df5bed8c15898ae_1604570141633; _m_h5_tk_enc=c01ded27c376a2a3de1f6081e1ecab7d");
		httpGet.setHeader("origin", "https://market.m.taobao.com");
		httpGet.setHeader("referer", "https://market.m.taobao.com/");
		httpGet.setHeader("user-agent", "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Mobile Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(10000).setConnectTimeout(10000).setConnectionRequestTimeout(10000);
		httpGet.setConfig(requestConfig.build());
		HttpResponse httpresponse;
		try {
			httpresponse = httpClient.execute(httpGet);
			HttpEntity entity = httpresponse.getEntity();
			String body = EntityUtils.toString(entity);
			if (StrUtil.isNull(body)) { // 空
				log.error("************************ dpsx2，返回结果是空！ ************************");
				return null;
			}
			if (!StrUtil.isJson(body)) {
				log.error("获取dpsx2：返回的不是json");
				log.error(body);
				return null;
			}
			JSONObject jsonObj = JSONObject.fromObject(body);
			if (jsonObj.has("ret") && !"SUCCESS::调用成功".equalsIgnoreCase(jsonObj.getJSONArray("ret").get(0).toString()) 
					&& !"SUCCESS::接口调用成功".equalsIgnoreCase(jsonObj.getJSONArray("ret").get(0).toString())) { // code出现，例如：{"api":"mtop.taobao.maserati.fansupgrade.gettabfeeds","data":{},"ret":["FAIL_SYS_TOKEN_EXOIRED::令牌过期"],"v":"1.0"}或{"api":"mtop.taobao.maserati.fansupgrade.gettabfeeds","data":{},"ret":["FAIL_SYS_ILLEGAL_ACCESS::非法请求"],"v":"1.0"}
				log.error("获取dpsx2，请求错误：" + body);
				return null;
			}
			if (jsonObj.has("data") && jsonObj.getJSONObject("data").has("ok") && jsonObj.getJSONObject("data").getBoolean("ok")) {
				JSONObject dataObj = jsonObj.getJSONObject("data").getJSONObject("result").getJSONObject("data");
				long lastTime = 0L;
				if (dataObj.has("lastTime")) {
					lastTime = dataObj.getLong("lastTime"); // 用于下一次查询店铺上新使用。
				}
				boolean isLast = dataObj.getBoolean("isLast"); // 是否结束：false.未结束还可以根据lastTime查询店铺上新数据、true.结束
				List<DpsxBb> list = new ArrayList<DpsxBb>();

				int index_flag = 0; // 仅获取最新一次上新的宝贝信息
				JSONArray feedsWithDateArr = dataObj.getJSONArray("feedsWithDate");
				for (int i = 0; feedsWithDateArr != null && i < feedsWithDateArr.size(); i++) {
					JSONObject feedsObj = (JSONObject) feedsWithDateArr.get(i);
					if (!feedsObj.has("weitaoContent")) {
						continue;
					}
					// String editTime = feedsObj.getJSONObject("weitaoContent").getString("editTime");
					JSONArray elements = feedsObj.getJSONObject("weitaoContent").getJSONArray("elements");
					for (int j = 0; elements != null && j < elements.size(); j++) {
						JSONObject element = (JSONObject) elements.get(j);
						if (!element.has("type") && !"item".equalsIgnoreCase(element.getString("type"))) {
							continue;
						}
						DpsxBb dpsxBb = new DpsxBb(element);
						list.add(dpsxBb);
						index_flag = index_flag + 1;
					}
					if (index_flag > 0) { // 仅获取最新一次上新的宝贝信息
						break;
					}
				}
				result.setCodeContent(Result.SUCCESS, "success");
				result.putKey("list", list);
				result.putKey("lastTime", lastTime); // 时间戳
				result.putKey("isLast", isLast); // 是否结束
			}
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			return null;
		} finally {
			httpGet.releaseConnection();
		}
	}

	/**
	 * 主程序：宝贝详情
	 * 
	 * @param json
	 * @return result
	 */
	public Result bbxq(String itemid) {
		Result result = null;
		int time = 0; // 尝试次数
		do {
			try {
				result = this.bbxq2(itemid);
				if (result != null && result.isSuccess()) {
					break; // 结束循环
				}
				time++;
				Thread.sleep(((time >= AliConst.TRY_NUM) ? 0 : time) * 2000L);
			} catch (Exception e) {
				e.printStackTrace();
			}
		} while (time < AliConst.TRY_NUM);
		if (result == null || !result.isSuccess()) {
			result = new Result(9, "超过最大尝试");
			log.info("获取bbxq，超过最大尝试次数：" + time);
		}
		return result;
	}

	/**
	 * 宝贝详情
	 * 
	 * @param json
	 * @return result，(其中code：0.成功、1.cookie过期、2.失败、9.超过最大尝试次数)
	 */
	public Result bbxq2(String itemid) {
		Result result = new Result(2, "获取宝贝详情数据失败");
		String data = "itemNumId\":\"" + itemid + "\"";
		String url = "";
		try {
			url = BBXQ_URL + URLEncoder.encode(data, "UTF-8");
		} catch (UnsupportedEncodingException e1) {
			e1.printStackTrace();
			log.error(e1.getMessage(), e1);
		}
		CloseableHttpClient httpClient = HttpClients.custom().build();
		HttpGet httpGet = new HttpGet(url);
		httpGet.setHeader("accept", "application/json");
		httpGet.setHeader("accept-encoding", "gzip, deflate, br");
		httpGet.setHeader("accept-language", "zh-CN,zh;q=0.9");
		httpGet.setHeader("content-type", "application/x-www-form-urlencoded");
		// httpGet.setHeader("cookie", "cna=nj8qGLIpVhICAXVAkun7PXTj; _m_h5_tk=039e5f0bc983053f6df5bed8c15898ae_1604570141633; _m_h5_tk_enc=c01ded27c376a2a3de1f6081e1ecab7d");
		httpGet.setHeader("origin", "https://market.m.taobao.com");
		httpGet.setHeader("referer", "https://market.m.taobao.com/");
		httpGet.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(10000).setConnectTimeout(10000).setConnectionRequestTimeout(10000);
		httpGet.setConfig(requestConfig.build());
		HttpResponse httpresponse;
		try {
			httpresponse = httpClient.execute(httpGet);
			HttpEntity entity = httpresponse.getEntity();
			String body = EntityUtils.toString(entity);
			if (StrUtil.isNull(body)) { // 空
				log.error("************************ bbxq2，返回结果是空！ ************************");
				return null;
			}
			body = body.substring(body.indexOf("__jp0(") + 6); // __jp0()
			body = body.substring(0, body.length() - 1);
			if (!StrUtil.isJson(body)) {
				log.error("获取bbxq2：返回的不是json");
				log.error(body);
				return null;
			}
			// __jp0({"ret":["FAIL_SYS_USER_VALIDATE","RGV587_ERROR::SM::哎哟喂,被挤爆啦,请稍后重试"],"data":{"url":"https://h5api.m.taobao.com:443//h5/mtop.taobao.detail.getdetail/6.0/_____tmd_____/punish?x5secdata=...&x5step=2"}})
			JSONObject jsonObj = JSONObject.fromObject(body);
			if (jsonObj.has("ret") && !"SUCCESS::调用成功".equalsIgnoreCase(jsonObj.getJSONArray("ret").get(0).toString()) 
					&& !"SUCCESS::接口调用成功".equalsIgnoreCase(jsonObj.getJSONArray("ret").get(0).toString())) { // code出现，例如：{"api":"mtop.taobao.maserati.fansupgrade.gettabfeeds","data":{},"ret":["FAIL_SYS_TOKEN_EXOIRED::令牌过期"],"v":"1.0"}或{"api":"mtop.taobao.maserati.fansupgrade.gettabfeeds","data":{},"ret":["FAIL_SYS_ILLEGAL_ACCESS::非法请求"],"v":"1.0"}
				log.error("获取bbxq2，请求错误：" + body);
				return null;
			}
			if (jsonObj.has("data")) {
				result.setCodeContent(Result.SUCCESS, "成功");
				result.putKey("data", jsonObj.getString("data"));
			}
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			return null;
		} finally {
			httpGet.releaseConnection();
		}
	}

	/**
	 * 主程序：无线详情
	 * 
	 * @param json
	 * @return result
	 */
	public Result wxxq(String itemid) {
		Result result = null;
		int time = 0; // 尝试次数
		do {
			try {
				result = this.wxxq2(itemid);
				if (result != null && result.isSuccess()) {
					break; // 结束循环
				}
				time++;
				Thread.sleep(((time >= AliConst.TRY_NUM) ? 0 : time) * 2000L);
			} catch (Exception e) {
				e.printStackTrace();
			}
		} while (time < AliConst.TRY_NUM);
		if (result == null || !result.isSuccess()) {
			result = new Result(9, "超过最大尝试");
			log.info("获取wxxq，超过最大尝试次数：" + time);
		}
		return result;
	}

	/**
	 * 无线详情
	 * 
	 * @param json
	 * @return result，(其中code：0.成功、1.cookie过期、2.失败、9.超过最大尝试次数)
	 */
	public Result wxxq2(String itemid) {
		Result result = new Result(2, "获取无线详情数据失败");
		String data = "itemNumId\":\"" + itemid + "\"";
		String url = "";
		try {
			url = BBXQ_URL + URLEncoder.encode(data, "UTF-8");
		} catch (UnsupportedEncodingException e1) {
			e1.printStackTrace();
			log.error(e1.getMessage(), e1);
		}
		CloseableHttpClient httpClient = HttpClients.custom().build();
		HttpGet httpGet = new HttpGet(url);
		httpGet.setHeader("accept", "application/json");
		httpGet.setHeader("accept-encoding", "gzip, deflate, br");
		httpGet.setHeader("accept-language", "zh-CN,zh;q=0.9");
		httpGet.setHeader("content-type", "application/x-www-form-urlencoded");
		// httpGet.setHeader("cookie", "cna=nj8qGLIpVhICAXVAkun7PXTj; _m_h5_tk=039e5f0bc983053f6df5bed8c15898ae_1604570141633; _m_h5_tk_enc=c01ded27c376a2a3de1f6081e1ecab7d");
		httpGet.setHeader("origin", "https://market.m.taobao.com");
		httpGet.setHeader("referer", "https://market.m.taobao.com/");
		httpGet.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(10000).setConnectTimeout(10000).setConnectionRequestTimeout(10000);
		httpGet.setConfig(requestConfig.build());
		HttpResponse httpresponse;
		try {
			httpresponse = httpClient.execute(httpGet);
			HttpEntity entity = httpresponse.getEntity();
			String body = EntityUtils.toString(entity);
			if (StrUtil.isNull(body)) { // 空
				log.error("************************ wxxq2，返回结果是空！ ************************");
				return null;
			}
			body = body.substring(body.indexOf("__jp0(") + 6); // __jp0()
			body = body.substring(0, body.length() - 1);
			if (!StrUtil.isJson(body)) {
				log.error("获取wxxq2：返回的不是json");
				log.error(body);
				return null;
			}
			JSONObject jsonObj = JSONObject.fromObject(body);
			if (jsonObj.has("ret") && !"SUCCESS::调用成功".equalsIgnoreCase(jsonObj.getJSONArray("ret").get(0).toString()) 
					&& !"SUCCESS::接口调用成功".equalsIgnoreCase(jsonObj.getJSONArray("ret").get(0).toString())) { // code出现，例如：{"api":"mtop.taobao.maserati.fansupgrade.gettabfeeds","data":{},"ret":["FAIL_SYS_TOKEN_EXOIRED::令牌过期"],"v":"1.0"}或{"api":"mtop.taobao.maserati.fansupgrade.gettabfeeds","data":{},"ret":["FAIL_SYS_ILLEGAL_ACCESS::非法请求"],"v":"1.0"}
				log.error("获取wxxq2，请求错误：" + body);
				return null;
			}
			if (jsonObj.has("data")) {
				JSONArray images = jsonObj.getJSONObject("data").getJSONObject("wdescContent").optJSONArray("pages");
				List<String> list = new ArrayList<>(images.size());
				for (int i = 0; i < images.size(); i++) {
					String image = images.getString(i);
					if (image.indexOf("</img>") == -1 || image.indexOf(".gif") != -1) {
						continue;
					}
					image = image.replaceAll("</img>", "");
					image = image.substring(image.indexOf(">") + 1);
					list.add(image);
				}
				result.setCodeContent(Result.SUCCESS, "成功");
				result.putKey("list", JSONArray.fromObject(list));
			}
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			return null;
		} finally {
			httpGet.releaseConnection();
		}
	}

	/**
	 * 主程序：淘客订单查询
	 * 
	 * @return result
	 */
	public Result tkdd(CookieStore cookieStore, String _m_h5_tk, String orderid) {
		Result result = null;
		int time = 0; // 尝试次数
		do {
			result = this.tkdd2(cookieStore, _m_h5_tk, orderid);
			if (result != null && result.isSuccess()) {
				break; // 结束循环
			}
			time++;
			try {
				Thread.sleep(((time >= AliConst.TRY_NUM) ? 0 : time) * 2000L);
			} catch (Exception e) {
				e.printStackTrace();
			}
		} while (time < AliConst.TRY_NUM);
		if (result == null || !result.isSuccess()) {
			result = new Result(9, "超过最大尝试");
			log.info("获取tkdd，超过最大尝试次数：" + time);
		}
		return result;
	}

	/**
	 * 淘客订单查询
	 * 
	 * @返回，非淘客订单：{"api":"mtop.alimama.union.rpt.single.order.detail","data":{"hasNextPage":"1","orderQueryStateType":"ORDER_INVALID","orders":[]},"ret":["SUCCESS::接口调用成功"],"v":"1.0"}
	 * @返回，淘客订单： {"api":"mtop.alimama.union.rpt.single.order.detail","data":{"hasNextPage":"1","orderQueryStateType":"ORDER_NOTBELONG","orders":[]},"ret":["SUCCESS::接口调用成功"],"v":"1.0"}
	 * @return result，(其中code：0.成功、1.cookie过期、2.失败、9.超过最大尝试次数)
	 */
	public Result tkdd2(CookieStore cookieStore, String _m_h5_tk, String orderid) {
		Result result = new Result(2, "获取淘客订单数据失败");
		long timestamp = System.currentTimeMillis(); // 时间戳
		String data = "{\"tbTradeParentId\":\"" + orderid + "\"}";
		String token = _m_h5_tk.split(";")[0].split("_")[0];
		String sign = signJSEngine.getSign(String.valueOf(timestamp), APPKEY, data, token);
		String url = String.format(TKDD_URL, APPKEY, String.valueOf(timestamp), sign);
		try {
			url = url + URLEncoder.encode(data, "UTF-8");
		} catch (UnsupportedEncodingException e1) {
			e1.printStackTrace();
			log.error(e1.getMessage(), e1);
		}
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cookieStore).build();
		HttpGet httpGet = new HttpGet(url);
		httpGet.setHeader("accept", "application/json");
		httpGet.setHeader("accept-encoding", "gzip, deflate, br");
		httpGet.setHeader("accept-language", "zh-CN,zh;q=0.9");
		httpGet.setHeader("content-type", "application/x-www-form-urlencoded");
		// httpGet.setHeader("cookie", "cna=nj8qGLIpVhICAXVAkun7PXTj; _m_h5_tk=039e5f0bc983053f6df5bed8c15898ae_1604570141633; _m_h5_tk_enc=c01ded27c376a2a3de1f6081e1ecab7d");
		httpGet.setHeader("origin", "https://market.m.taobao.com");
		httpGet.setHeader("referer", "https://market.m.taobao.com/");
		httpGet.setHeader("user-agent", "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Mobile Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000).setConnectionRequestTimeout(20000);
		httpGet.setConfig(requestConfig.build());
		HttpResponse httpresponse;
		try {
			httpresponse = httpClient.execute(httpGet);
			HttpEntity entity = httpresponse.getEntity();
			String body = EntityUtils.toString(entity);
			if (StrUtil.isNull(body)) { // 空
				log.error("************************ tkdd2，返回结果是空！ ************************");
				return null;
			}
			body = body.substring(body.indexOf("mtopjsonp2(") + 11); // mtopjsonp2()
			body = body.substring(0, body.length() - 1);
			if (!StrUtil.isJson(body)) {
				log.error("获取tkdd2：返回的不是json");
				log.error(body);
				return null;
			}
			JSONObject jsonObj = JSONObject.fromObject(body);
			if (jsonObj.has("ret") && !"SUCCESS::调用成功".equalsIgnoreCase(jsonObj.getJSONArray("ret").get(0).toString()) 
					&& !"SUCCESS::接口调用成功".equalsIgnoreCase(jsonObj.getJSONArray("ret").get(0).toString())) { // code出现，例如：{"api":"mtop.taobao.maserati.fansupgrade.gettabfeeds","data":{},"ret":["FAIL_SYS_TOKEN_EXOIRED::令牌过期"],"v":"1.0"}或{"api":"mtop.taobao.maserati.fansupgrade.gettabfeeds","data":{},"ret":["FAIL_SYS_ILLEGAL_ACCESS::非法请求"],"v":"1.0"}
				log.error("获取tkdd2，请求错误：" + body);
				return null;
			}
			if (jsonObj.has("data")) {
				result.setCodeContent(Result.SUCCESS, "success");
				if (jsonObj.getJSONObject("data").has("orderQueryStateType") && AliConst.TKDD_TRUE.equalsIgnoreCase(jsonObj.getJSONObject("data").getString("orderQueryStateType"))) {
					result.putKey("data", "淘客订单");
				} else {
					result.putKey("data", "非淘客订单");
				}
			}
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			return null;
		} finally {
			httpGet.releaseConnection();
		}
	}

	/**
	 * 主程序：天猫积分，此接口会重定向
	 * 
	 * @return result
	 */
	public Result tmjf(CookieStore cookieStore) {
		Result result = null;
		int time = 0; // 尝试次数
		do {
			result = this.tmjf2(cookieStore);
			if (result != null && result.isSuccess()) {
				break; // 结束循环
			}
			time++;
			try {
				Thread.sleep(((time >= AliConst.TRY_NUM) ? 0 : time) * 2000L);
			} catch (Exception e) {
				e.printStackTrace();
			}
		} while (time < AliConst.TRY_NUM);
		if (result == null || !result.isSuccess()) {
			result = new Result(9, "超过最大尝试");
			log.info("获取tkdd，超过最大尝试次数：" + time);
		}
		return result;
	}

	/**
	 * 天猫积分
	 * 
	 * @return result，(其中code：0.成功、1.cookie过期、2.失败、9.超过最大尝试次数)
	 */
	public Result tmjf2(CookieStore cookieStore) {
		Result result = new Result(2, "获取数据失败！");
		long timestamp = System.currentTimeMillis(); // 时间戳
		String url = String.format(MEMBER_URL, String.valueOf(timestamp));
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cookieStore).build();
		HttpGet httpGet = new HttpGet(url);
		httpGet.setHeader("accept", "*/*");
		httpGet.setHeader("accept-encoding", "gzip, deflate, br");
		httpGet.setHeader("accept-language", "zh-CN,zh;q=0.9");
		// httpGet.setHeader("content-type", "application/x-www-form-urlencoded");
		// httpGet.setHeader("cookie", "cna=nj8qGLIpVhICAXVAkun7PXTj; _m_h5_tk=039e5f0bc983053f6df5bed8c15898ae_1604570141633; _m_h5_tk_enc=c01ded27c376a2a3de1f6081e1ecab7d");
		// httpGet.setHeader("origin", "https://www.tmall.com");
		httpGet.setHeader("referer", "https://www.tmall.com/");
		httpGet.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000).setConnectionRequestTimeout(20000);
		httpGet.setConfig(requestConfig.build());
		HttpResponse httpresponse;
		try {
			httpresponse = httpClient.execute(httpGet);
			HttpEntity entity = httpresponse.getEntity();
			String body = EntityUtils.toString(entity);
			if (StrUtil.isNull(body)) { // 空
				log.error("************************ tmjf2，返回结果是空！ ************************");
				return null;
			}
			// body = body.substring(body.indexOf("_initMemberInfoCallback(") + 24); // _initMemberInfoCallback()
			// body = body.substring(0, body.length() - 1);
			body = body.substring(body.indexOf("(") + 1); // 第一个“(”
			body = body.substring(0, body.lastIndexOf(")")); // 最后一个“)”
			if (!StrUtil.isJson(body)) {
				log.error("获取tmjf2：返回的不是json");
				log.error(body);
				return null;
			}
			JSONObject jsonObj = JSONObject.fromObject(body);
			result.putKey("data", body);
			if (!jsonObj.has("login") || !jsonObj.getBoolean("login")) {
				result.setCodeContent(Result.NOLOGIN, "登录超时！");
				return result;
			}
			result.setCodeContent(Result.SUCCESS, "success");
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			return null;
		} finally {
			httpGet.releaseConnection();
		}
	}

	/**
	 * 主程序：淘宝购物车
	 * 
	 * @return result
	 */
	public Result taobaoCart(CookieStore cookieStore) {
		Result result = null;
		int time = 0; // 尝试次数
		do {
			result = this.taobaoCart2(cookieStore);
			if (result != null && result.isSuccess()) {
				break; // 结束循环
			}
			time++;
			try {
				Thread.sleep(((time >= AliConst.TRY_NUM) ? 0 : time) * 2000L);
			} catch (Exception e) {
				e.printStackTrace();
			}
		} while (time < AliConst.TRY_NUM);
		if (result == null || !result.isSuccess()) {
			result = new Result(9, "超过最大尝试");
			log.info("获取tkdd，超过最大尝试次数：" + time);
		}
		return result;
	}

	/**
	 * 淘宝购物车
	 * 
	 * @return result，(其中code：0.成功、1.cookie过期、2.失败、9.超过最大尝试次数)
	 */
	public Result taobaoCart2(CookieStore cookieStore) {
		Result result = new Result(2, "获取数据失败！");
		long timestamp = System.currentTimeMillis(); // 时间戳
		String url = String.format(CART_URL, String.valueOf(timestamp));
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cookieStore).build();
		HttpGet httpGet = new HttpGet(url);
		httpGet.setHeader("accept", "*/*");
		httpGet.setHeader("accept-encoding", "gzip, deflate, br");
		httpGet.setHeader("accept-language", "zh-CN,zh;q=0.9");
		// httpGet.setHeader("content-type", "application/x-www-form-urlencoded");
		// httpGet.setHeader("cookie", "cna=nj8qGLIpVhICAXVAkun7PXTj; _m_h5_tk=039e5f0bc983053f6df5bed8c15898ae_1604570141633; _m_h5_tk_enc=c01ded27c376a2a3de1f6081e1ecab7d");
		// httpGet.setHeader("origin", "https://www.taobao.com");
		httpGet.setHeader("referer", "https://www.taobao.com/");
		httpGet.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000).setConnectionRequestTimeout(20000);
		httpGet.setConfig(requestConfig.build());
		HttpResponse httpresponse;
		try {
			httpresponse = httpClient.execute(httpGet);
			HttpEntity entity = httpresponse.getEntity();
			String body = EntityUtils.toString(entity);
			if (StrUtil.isNull(body)) { // 空
				log.error("************************ taobaoCart2，返回结果是空！ ************************");
				return null;
			}
			body = body.substring(body.indexOf("(") + 1); // 第一个“(”
			body = body.substring(0, body.lastIndexOf(")")); // 最后一个“)”
			if (!StrUtil.isJson(body)) {
				log.error("获取taobaoCart2：返回的不是json");
				log.error(body);
				return null;
			}
			JSONObject jsonObj = JSONObject.fromObject(body);
			result.putKey("data", body);
			if (!jsonObj.has("isLogin") || !jsonObj.getBoolean("isLogin")) {
				result.setCodeContent(Result.NOLOGIN, "登录超时！");
				return result;
			}
			result.setCodeContent(Result.SUCCESS, "success");
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			return null;
		} finally {
			httpGet.releaseConnection();
		}
	}

	/**
	 * 获取cookieStr
	 */
	public String getDocumentCookie(CookieStore cookieStore) {
		String result = "";
		Gson gson = new GsonBuilder().setDateFormat("yyyy-MM-dd HH:mm:ss").create();
		try {
			List<CookieVo> cookieVoList = new ArrayList<CookieVo>();
			if (cookieStore != null && cookieStore.getCookies().size() > 0) {
				List<Cookie> list = cookieStore.getCookies();
				for (int i = 0; i < list.size(); i++) {
					CookieVo cookieVo = new CookieVo(gson.toJson(list.get(i)));
					if (!cookieVo.getCookieDomain().equals(".taobao.com") && !cookieVo.getCookieDomain().equals(".mmstat.com") && !cookieVo.getCookieDomain().equals(".tanx.com")
							&& !cookieVo.getCookieDomain().equals("ald.taobao.com") && !cookieVo.getCookieDomain().equals(".m.taobao.com") && !cookieVo.getCookieDomain().equals(".tmall.com")
							&& !cookieVo.getCookieDomain().equals(".1688.com")) {
						continue;
					}
					if (cookieVo.getName().equalsIgnoreCase("isg")) {
						continue;
					}
					cookieVoList.add(cookieVo);
				}
			}
			for (int i = 0; cookieVoList != null && i < cookieVoList.size(); i++) {
				CookieVo cookie = cookieVoList.get(i);
				if (i == (cookieVoList.size() - 1)) { // 最后一个
					// result = result + cookie.getName() + "=" + URLEncoder.encode(cookie.getValue(), "UTF-8");
					result = result + cookie.getName() + "=" + cookie.getValue();
				} else {
					// result = result + cookie.getName() + "=" + URLEncoder.encode(cookie.getValue(), "UTF-8") + "; ";
					result = result + cookie.getName() + "=" + cookie.getValue() + "; ";
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		}
		return result;
	}

	/**
	 * 根据cookieStore获取_m_h5_tk
	 */
	public String getH5tkByCookieStore(CookieStore cookieStore, String domain) {
		String _m_h5_tk = "";
		Gson gson = new GsonBuilder().setDateFormat("yyyy-MM-dd HH:mm:ss").create();
		try {
			List<CookieVo> cookieVoList = new ArrayList<CookieVo>();
			if (cookieStore != null && cookieStore.getCookies().size() > 0) {
				List<Cookie> list = cookieStore.getCookies();
				for (int i = 0; i < list.size(); i++) {
					CookieVo cookieVo = new CookieVo(gson.toJson(list.get(i)));
					if (!cookieVo.getCookieDomain().equals(".taobao.com") && !cookieVo.getCookieDomain().equals("taobao.com") && !cookieVo.getCookieDomain().equals(".tmall.com")
							&& !cookieVo.getCookieDomain().equals("tmall.com") && !cookieVo.getCookieDomain().equals(".1688.com") && !cookieVo.getCookieDomain().equals("1688.com")) {
						continue;
					}
					cookieVoList.add(cookieVo);
				}
			}
			for (int i = 0; cookieVoList != null && i < cookieVoList.size(); i++) {
				CookieVo cookie = cookieVoList.get(i);
				if (cookie.getName().equalsIgnoreCase("_m_h5_tk") && domain.equalsIgnoreCase(cookie.getCookieDomain())) {
					_m_h5_tk = cookie.getValue();
					break;
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		}
		return _m_h5_tk;
	}

	// main方法
	public static void main(String[] args) {
		try {
			String data = "{\"userId\":**********,\"lastTime\":0,\"tab\":1,\"source\":null}";
			System.out.println(URLEncoder.encode(data, "UTF-8"));
			String body = "__jp0({\"api\":\"wdetail\",\"v\":\"6.0\"})";
			body = body.substring(body.indexOf("__jp0(") + 6);
			System.out.println(body);
			body = body.substring(0, body.length() - 1);
			System.out.println(body);

			System.out.println();
			body = "_initMemberInfoCallback({\"activeStatus\":1,\"availablePoints\":1531,\"cookies\":{\"uc1\":{\"value\":\"existShop=false&pas=0&cookie14=Uoe1hxHALa2b7A%3D%3D&cookie16=URm48syIJ1yk0MX2J7mAAEhTuw%3D%3D&cart_m=0&cookie21=WqG3DMC9Fb5mPLIQo9kR&cookie15=UIHiLt3xD8xYTw%3D%3D\"},\"t\":{\"value\":\"a51b284709def547b1a2dfe94e75c51e\"},\"unb\":{\"value\":\"918454580\"}},\"expiredPoints\":0,\"lastMessage\":\"\",\"lastMessageId\":-99,\"lastMessageType\":1,\"lastMessageUrl\":\"//vip.tmall.com/messagebox/index.htm?tab=shop&scm=1040.1.0.0\",\"login\":true,\"mallSeller\":false,\"messagePopup\":false,\"newMessage\":-99,\"newMsgList\":null,\"taskId\":\"\"});";
			body = body.substring(body.indexOf("(") + 1);
			System.out.println(body);
			body = body.substring(0, body.lastIndexOf(")"));
			System.out.println(body);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

}
