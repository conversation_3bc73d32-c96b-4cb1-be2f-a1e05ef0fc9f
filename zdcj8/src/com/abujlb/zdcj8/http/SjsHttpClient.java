package com.abujlb.zdcj8.http;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.annotation.PostConstruct;

import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.config.RequestConfig.Builder;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.abujlb.Config;
import com.abujlb.Result;
import com.abujlb.retry.Retry;
import com.abujlb.retry.Times;
import com.abujlb.util.DateUtil;
import com.abujlb.util.Md5;
import com.abujlb.zdcj8.bean.DpsxItem;
import com.abujlb.zdcj8.bean.JptsBbxx;
import com.abujlb.zdcj8.bean.JptsBbxx2;
import com.abujlb.zdcj8.bean.JptsBbxx3;
import com.abujlb.zdcj8.bean.JptsLljg;
import com.abujlb.zdcj8.bean.Tkdd;
import com.abujlb.zdcj8.bean.Wwxx;
import com.abujlb.zdcj8.bean.ZhSjs;
import com.abujlb.zdcj8.bean.sjs.JpggxsblInput;
import com.abujlb.zdcj8.bean.sjs.JpggxsblJl;
import com.abujlb.zdcj8.bean.sjs.JpggxsblJlData;
import com.abujlb.zdcj8.bean.sjs.Kllqd;
import com.abujlb.zdcj8.bean.sjs.KllqdInput;
import com.abujlb.zdcj8.bean.sjs.Mjx;
import com.abujlb.zdcj8.bean.sjs.MjxBaseInfo;
import com.abujlb.zdcj8.bean.sjs.MjxInput;
import com.abujlb.zdcj8.bean.sjs.MjxQdxzInput;
import com.abujlb.zdcj8.bean.sjs.MjxTp;
import com.abujlb.zdcj8.bean.sjs.Mjxyx;
import com.abujlb.zdcj8.bean.sjs.SsdbkspBdbInput;
import com.abujlb.zdcj8.bean.sjs.Ssksp;
import com.abujlb.zdcj8.bean.sjs.SskspInput;
import com.abujlb.zdcj8.bean.sjs.Ssmd;
import com.abujlb.zdcj8.bean.sjs.SsmdInput;
import com.abujlb.zdcj8.bean.sjs.Tkl;
import com.abujlb.zdcj8.constant.SjsConst;
import com.abujlb.zdcj8.script.Md5SjsJSEngine;
import com.abujlb.zdcj8.util.StrUtil;
import com.alibaba.fastjson.JSON;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

/**
 * 数据蛇http请求
 * 
 * <AUTHOR>
 * @date 2020-09-15 10:43:37
 */
@Component
public class SjsHttpClient {

	private static Logger log = Logger.getLogger(SjsHttpClient.class);

	private static String temp_path = null;

	@Autowired
	private Config config;
	@Autowired
	private Md5SjsJSEngine md5SjsJSEngine;

	/**
	 * 初始化
	 * 
	 */
	@PostConstruct
	public void init() {
		try {
			temp_path = config.getString("tempdir");
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		}
	}

	/**
	 * 获取淘宝
	 * 
	 * @param bbid 宝贝id
	 * @return json
	 */
	public String getTaobaoDetail(String bbid) {
		CloseableHttpClient httpClient = HttpClients.custom().build();
		String url = SjsConst.TAOBAO_ITEM_URL + bbid + "%22";
		HttpGet httpGet = new HttpGet(url);
		httpGet.setHeader("Accept", "*/*");
		httpGet.setHeader("Accept-Encoding", "gzip, deflate, br");
		httpGet.setHeader("Accept-Language", "zh-CN,zh;q=0.9");
		httpGet.setHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.116 Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(10000).setConnectTimeout(10000).setConnectionRequestTimeout(10000);
		httpGet.setConfig(requestConfig.build());

		HttpResponse httpresponse;
		try {
			httpresponse = httpClient.execute(httpGet);
			HttpEntity entity = httpresponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			if (!StrUtil.isJson(body)) {
				System.out.println("************************ getTaobaoDetail，返回结果不是json，cookie失效 ************************");
				return "";
			}
			JSONObject jsonObj = JSONObject.fromObject(body);
			if (jsonObj.has("api") && jsonObj.has("data") && jsonObj.getJSONObject("data") != null
					&& !jsonObj.getJSONObject("data").isNullObject() && !jsonObj.getJSONObject("data").isEmpty()) { // 状态为302，登录超时，删除该帐号
				return body;
			}
			return "";
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		} finally {
			httpGet.releaseConnection();
		}
		return "";
	}
	
	/**
	 * 获取淘宝
	 * 
	 * @param bbid 宝贝id
	 * @return json
	 */
	public String getTaobaoDetail2(String bbid) {
		CloseableHttpClient httpClient = HttpClients.custom().build();
		String url = "";
		try {
//			url = String.format(SjsConst.TAOBAO_ITEM_URL2, URLEncoder.encode("itemNumId\":\"" + bbid + "\"", "UTF-8")) + "&callback=__jp1";
			url = SjsConst.TAOBAO_ITEM_URL2 + URLEncoder.encode("itemNumId\":\"" + bbid + "\"", "UTF-8") + "&callback=__jp1";
		} catch (UnsupportedEncodingException e1) {
			e1.printStackTrace();
		}
		HttpGet httpGet = new HttpGet(url);
		httpGet.setHeader("accept", "*/*");
		httpGet.setHeader("accept-encoding", "gzip, deflate, br");
		httpGet.setHeader("accept-language", "zh-CN,zh;q=0.9");
		httpGet.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(10000).setConnectTimeout(10000).setConnectionRequestTimeout(10000);
		httpGet.setConfig(requestConfig.build());
		HttpResponse httpresponse;
		try {
			httpresponse = httpClient.execute(httpGet);
			HttpEntity entity = httpresponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			if (StrUtil.isNull(body)) {
				return "";
			}
			String json = body.substring(6, body.length() - 1);
			if (!StrUtil.isJson(json)) {
				return "";
			}
			return json;
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		} finally {
			httpGet.releaseConnection();
		}
		return "";
	}

	/**
	 * 竞品sku销量，生成数据链接，扣费链接，为避免连续扣费，不能尝试3次
	 * 
	 * @param zhSjs
	 * @param datatpye 日期类型：day.按天、recent7.最近7天、recent30.最近30天
	 * @param tjrq     统计开始日期
	 * @param tjrq2    统计结束日期
	 * @return result，(其中code：0.竞品SKU销量数据生成成功，但没有id、1.，但没有id、2.竞品SKU销量数据生成失败)
	 * @例如：https://gwhd.shujushe.com/shujushe/saleSku/save?userName=15205512621&datatpye=day&nid=611747292705&device=2&tjrq=2020-09-11&tjrq2=2020-09-17&starttime=2020-09-08&endtime=2020-09-17
	 */
	public Result jpsaleSkuSave(ZhSjs zhSjs, String bbid, String datatpye, String tjrq, String tjrq2) {
		Result result = new Result(2, "数据生成失败");
		long nowtime = System.currentTimeMillis();
		String endtime = DateUtil.formatDate(new Date(nowtime - 86400000L)); // 原js代码：endtime: this.timestampToTime((new Date).getTime() - 864e5),
		String starttime = DateUtil.formatDate(new Date(nowtime - 864000000L)); // 原js代码：starttime: this.timestampToTime((new Date).getTime() - 864e6),
		// if (RQLX_RECENT7.equals(datatpye)) {
		// tjrq2 = DateUtil.formatDate(new Date(nowtime - 24 * 60 * 60 * 1000L)); //
		// 昨天，86400000L = 24 * 60 * 60 * 1000L
		// tjrq = DateUtil.formatDate(new Date(nowtime - 7 * 24 * 60 * 60 * 1000L));
		// } else if (RQLX_RECENT30.equals(datatpye)) {
		// tjrq2 = DateUtil.formatDate(new Date(nowtime - 24 * 60 * 60 * 1000L)); // 昨天
		// tjrq = DateUtil.formatDate(new Date(nowtime - 30 * 24 * 60 * 60 * 1000L));
		// }
		String url = String.format(SjsConst.SALESKU_URL, zhSjs.getUsername(), datatpye, bbid, tjrq, tjrq2, starttime, endtime);
		String json = this.getTaobaoDetail(bbid);
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(zhSjs.getCookieStore()).build(); // 不需要cookie也可以
		HttpPost httpPost = new HttpPost(url);
		httpPost.setHeader("accept", "application/json, text/plain, */*");
		httpPost.setHeader("accept-encoding", "gzip, deflate, br");
		httpPost.setHeader("accept-language", "zh-CN,zh;q=0.9");
		httpPost.setHeader("content-type", "application/json;charset=UTF-8");
		httpPost.setHeader("origin", "https://www.shujushe.com");
		httpPost.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.116 Safari/537.36");

		Builder requestConfig = RequestConfig.custom().setSocketTimeout(60000).setConnectTimeout(60000).setConnectionRequestTimeout(60000);
		httpPost.setConfig(requestConfig.build());
		try {
			ArrayList<BasicNameValuePair> pairs = new ArrayList<BasicNameValuePair>(); // 用来存放post请求的参数，键值对
			pairs.add(new BasicNameValuePair("json", json));

			UrlEncodedFormEntity urlEncodedFormEntity = new UrlEncodedFormEntity(pairs, "utf-8");
			httpPost.setEntity(urlEncodedFormEntity);
			HttpResponse httpResponse = httpClient.execute(httpPost);
			HttpEntity entity = httpResponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");

			if (StrUtil.isNull(body)) { // 空
				log.error("数据蛇，获取jpsaleSkuSave：返回的是空字符串");
				return null;
			}
			if (!StrUtil.isJson(body)) {
				log.error("数据蛇，获取jpsaleSkuSave：返回的不是json");
				log.error(body);
				return null;
			}
			JSONObject jsonObj = JSONObject.fromObject(body);
			if (jsonObj.has("code")) { // code出现，例如：{"msg": "不支持' GET'请求","code": 500}
				log.error("数据蛇，获取jpsaleSkuSave，code错误：" + body);
				return result;
			}
			if (!jsonObj.has("errcode") || jsonObj.getInt("errcode") != 2000 || !jsonObj.has("result")
					|| jsonObj.getJSONObject("result").isNullObject()) { // 状态不为2000，说明接口返回失败
				log.info("数据蛇，获取jpsaleSkuSave，errcode错误：" + body);
				return result;
			}
			JSONObject resultObj = jsonObj.getJSONObject("result");
			result.setCodeContent(Result.SUCCESS, "success");
			result.putKey("id", resultObj.getLong("id"));
			result.putKey("image", resultObj.getString("image"));
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		} finally {
			httpPost.releaseConnection();
		}
		return null;
	}

	/**
	 * 主程序：竞品sku销量
	 * 
	 * @return result，(其中code：0.获取id，且status==2，可直接下载excel、1.获取id，且status!=2，需要等待、2.失败、9.超过最大尝试次数)
	 */
	public Result jpsaleSkulist(ZhSjs zhSjs, String bbid) {
		Result result = null;
		int time = 0; // 尝试次数
		int nulltime = 0; // 空次数
		do {
			result = this.jpsaleSkulist2(zhSjs, bbid);
			if (result == null) {
				nulltime++;
			}
			if (result != null && result.isSuccess()) {
				break; // 结束循环
			}
			time++;
			try {
				Thread.sleep(((time >= SjsConst.MAX_TRY_NUM) ? 0 : time) * 3000L);
			} catch (Exception e) {
				e.printStackTrace();
			}
		} while (time < SjsConst.MAX_TRY_NUM && nulltime < SjsConst.NULL_TRY_NUM);
		if (result == null) {
			result = new Result(2, "3次接口返回null。");
			log.info("获取jpsaleSkulist，尝试次数：" + time + "，返回null次数：" + nulltime);
		} else if (time >= SjsConst.MAX_TRY_NUM && result.getCode() == 1) {
			result.setCodeContent(9, "超过最大尝试");
			log.info("获取jpsaleSkulist，超过最大尝试次数：" + time + "，返回null次数：" + nulltime);
		}
		return result;
	}

	/**
	 * 竞品sku销量，获取所有sku数据
	 * 
	 * @param zhSjs 账号信息
	 * @param bbid  宝贝id
	 * @return result，(其中code：0.获取id，且status==2，可直接下载excel、1.获取id，且status!=2，需要等待、2.失败、9.超过最大尝试次数)
	 */
	public Result jpsaleSkulist2(ZhSjs zhSjs, String bbid) {
		Result result = new Result(2, "数据生成失败");
		String url = String.format(SjsConst.SALESKU_LIST_URL, zhSjs.getUsername(), "1");
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(zhSjs.getCookieStore()).build(); // 不需要cookie也可以
		HttpPost httpPost = new HttpPost(url);
		httpPost.setHeader("accept", "application/json, text/plain, */*");
		httpPost.setHeader("accept-encoding", "gzip, deflate, br");
		httpPost.setHeader("accept-language", "zh-CN,zh;q=0.9");
		httpPost.setHeader("content-type", "application/json;charset=UTF-8");
		httpPost.setHeader("origin", "https://www.shujushe.com");
		httpPost.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.116 Safari/537.36");

		Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000).setConnectionRequestTimeout(20000);
		httpPost.setConfig(requestConfig.build());
		try {
			// ArrayList<BasicNameValuePair> pairs = new ArrayList<BasicNameValuePair>();
			// pairs.add(new BasicNameValuePair("json", json));
			// UrlEncodedFormEntity urlEncodedFormEntity = new UrlEncodedFormEntity(pairs, "utf-8");
			// httpPost.setEntity(urlEncodedFormEntity);

			HttpResponse httpResponse = httpClient.execute(httpPost);
			HttpEntity entity = httpResponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			if (StrUtil.isNull(body)) { // 空
				log.error("数据蛇，获取jpsaleSkulist2：返回的是空字符串");
				return null;
			}
			if (!StrUtil.isJson(body)) {
				log.error("数据蛇，获取jpsaleSkulist2：返回的不是json");
				log.error(body);
				return null;
			}
			JSONObject jsonObj = JSONObject.fromObject(body);
			if (jsonObj.has("code")) { // code出现，例如：{"msg": "不支持' GET'请求","code": 500}
				log.error("数据蛇，获取jpsaleSkulist2，code错误：" + body);
				return null;
			}
			// if (!jsonObj.has("errcode") || jsonObj.getInt("errcode") != 2000 ||
			// !jsonObj.has("result") || jsonObj.getJSONArray("result").isEmpty()) { //
			// 状态不为2000，说明接口返回失败
			if (!jsonObj.has("errcode") || jsonObj.getInt("errcode") != 2000 || !jsonObj.has("result")) { // 状态不为2000，说明接口返回失败
				log.info("数据蛇，获取jpsaleSkulist2，errcode错误：" + body);
				return null;
			}
			JSONArray resultArr = jsonObj.getJSONArray("result");
			if (resultArr == null || resultArr.size() < 1) {
				return null;
			}
			for (int i = 0; i < resultArr.size(); i++) {
				JSONObject resultObj = (JSONObject) resultArr.get(i);
				if (!resultObj.has("nid") || StrUtil.isNull(resultObj.getString("nid"))) {
					continue;
				}
				String nid = resultObj.getString("nid");
				String status = resultObj.getString("status");
				if (nid.equals(bbid)) {
					if (!StrUtil.isNull(status) && "2".equals(status)) { // 数据已准备好，可以下载excel
						result.setCodeContent(Result.SUCCESS, "success");
						result.putKey("id", resultObj.getLong("id"));
						result.putKey("image", resultObj.getString("image"));
						result.putKey("datatpye", resultObj.getString("datatpye"));
						result.putKey("tjrq", resultObj.getString("tjrq"));
						result.putKey("tjrq2", resultObj.getString("tjrq2"));
						result.putKey("totalNum", resultObj.getInt("totalNum"));
						result.putKey("totalPage", resultObj.getInt("totalPage"));
						result.putKey("createTime", resultObj.getLong("createTime"));
					} else {
						result.setCodeContent(1, "wait a minute");
						result.putKey("id", resultObj.getLong("id"));
						result.putKey("status", status);
					}
					return result;
				}
			}
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			// recordError.record(zhSjs, e.getMessage());
			log.error(e.getMessage(), e);
			return null;
		} finally {
			httpPost.releaseConnection();
		}
	}

	/**
	 * 下载文件：excel
	 * 
	 * @param zhSjs 账户信息
	 * @param id    下载id
	 */
	@Retry
	public String jpsaleSkuDownload(long id) {
		String result = null;
		String url = String.format(SjsConst.SALESKU_EXPORT_URL, String.valueOf(id));
		CloseableHttpClient httpClient = HttpClients.custom().build();
		HttpGet httpGet = new HttpGet(url);
		httpGet.setHeader("accept", "*/*");
		httpGet.setHeader("accept-encoding", "gzip, deflate, br");
		httpGet.setHeader("accept-language", "zh-CN,zh;q=0.9");
		httpGet.setHeader("origin", "https://www.shujushe.com");
		httpGet.setHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.116 Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(30000).setConnectTimeout(30000).setConnectionRequestTimeout(30000);
		httpGet.setConfig(requestConfig.build());

		HttpResponse httpResponse;
		InputStream is = null;
		FileOutputStream os = null;
		try {
			httpResponse = httpClient.execute(httpGet);
			String filename = StrUtil.getFileName(".xls");
			File file = new File(temp_path);
			if (!file.exists()) {
				file.mkdirs();
			}
			result = temp_path + filename;
			HttpEntity entity = httpResponse.getEntity();
			is = entity.getContent();
			os = new FileOutputStream(result);
			byte[] b = new byte[4096];
			int count = 0;
			while ((count = is.read(b)) > 0) {
				os.write(b, 0, count);
			}
			os.flush();
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			return null;
		} finally {
			try {
				is.close();
			} catch (Exception e) {
			}
			try {
				os.close();
			} catch (Exception e) {
			}
			httpGet.releaseConnection();
		}
		return result;
	}

	/**
	 * 超级竞品透视：最近30天、最近7天，生成数据链接，扣费链接，为避免连续扣费，不能尝试3次
	 * 
	 * @param zhSjs
	 * @param datatpye 日期类型：day.按天、recent7.最近7天、recent30.最近30天
	 * @return result，(其中code：0.数据生成成功，但没有id、1.，但没有id、2.数据生成失败)
	 * @例如：最近7天：https://gwhd.shujushe.com/shujushe/cjpHigh/save?userName=18856038967&datatpye=recent7&nid=596798762216&device=2&tjrq=2020-10-14&tjrq2=2020-10-20&starttime=2020-09-21&endtime=2020-10-20 最近30天：https://gwhd.shujushe.com/shujushe/cjpHigh/save?userName=18856038967&datatpye=recent30&nid=600528317790&device=2&tjrq=2020-09-21&tjrq2=2020-10-20&starttime=2020-09-21&endtime=2020-10-20
	 */
	public Result superJptsSave(ZhSjs zhSjs, String bbid, String datatpye, long nowtime) {
		Result result = new Result(2, "数据生成失败");
		// long nowtime = System.currentTimeMillis();
		String starttime = DateUtil.formatDate(new Date(nowtime - 30 * 24 * 60 * 60 * 1000L)); // 30天前
		String endtime = DateUtil.formatDate(new Date(nowtime - 24 * 60 * 60 * 1000L)); // 昨天
		String tjrq = starttime;
		String tjrq2 = endtime;
		if (SjsConst.RQLX_RECENT7.equals(datatpye)) {
			tjrq = DateUtil.formatDate(new Date(nowtime - 7 * 24 * 60 * 60 * 1000L)); // 7天前
		} else if (SjsConst.RQLX_RECENT30.equals(datatpye)) {
			tjrq = starttime;
		}
		String url = String.format(SjsConst.SUPER_JPTS_URL, zhSjs.getUsername(), datatpye, bbid, tjrq, tjrq2, starttime, endtime);
		String json = this.getTaobaoDetail(bbid);
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(zhSjs.getCookieStore()).build(); // 不需要cookie也可以
		HttpPost httpPost = new HttpPost(url);
		httpPost.setHeader("accept", "application/json, text/plain, */*");
		httpPost.setHeader("accept-encoding", "gzip, deflate, br");
		httpPost.setHeader("accept-language", "zh-CN,zh;q=0.9");
		httpPost.setHeader("content-type", "application/json;charset=UTF-8");
		httpPost.setHeader("origin", "https://www.shujushe.com");
		httpPost.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.75 Safari/537.36");

		Builder requestConfig = RequestConfig.custom().setSocketTimeout(60000).setConnectTimeout(60000).setConnectionRequestTimeout(60000);
		httpPost.setConfig(requestConfig.build());
		try {
			ArrayList<BasicNameValuePair> pairs = new ArrayList<BasicNameValuePair>(); // 用来存放post请求的参数，键值对
			pairs.add(new BasicNameValuePair("json", json));

			UrlEncodedFormEntity urlEncodedFormEntity = new UrlEncodedFormEntity(pairs, "utf-8");
			httpPost.setEntity(urlEncodedFormEntity);
			HttpResponse httpResponse = httpClient.execute(httpPost);
			HttpEntity entity = httpResponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");

			if (StrUtil.isNull(body)) { // 空
				log.error("数据蛇，获取superJptsSave：返回的是空字符串");
				return null;
			}
			if (!StrUtil.isJson(body)) {
				log.error("数据蛇，获取superJptsSave：返回的不是json");
				log.error(body);
				return null;
			}
			JSONObject jsonObj = JSONObject.fromObject(body);
			if (jsonObj.has("code")) { // code出现，例如：{"msg": "不支持' GET'请求","code": 500}
				log.error("数据蛇，获取superJptsSave，code错误：" + body);
				return result;
			}
			if (!jsonObj.has("errcode") || jsonObj.getInt("errcode") != 2000 || !jsonObj.has("result")
					|| jsonObj.getJSONObject("result").isNullObject()) { // 状态不为2000，说明接口返回失败
				log.info("数据蛇，获取superJptsSave，errcode错误：" + body);
				return result;
			}
			JSONObject resultObj = jsonObj.getJSONObject("result");
			result.setCodeContent(Result.SUCCESS, "success");
			result.putKey("id", resultObj.getLong("id"));
			result.putKey("uuid", resultObj.getString("uuid"));
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		} finally {
			httpPost.releaseConnection();
		}
		return null;
	}

	/**
	 * 主程序：超级竞品透视，获取数据，以及竞品透视采集状态
	 * 
	 * @param uuid 唯一id，由超级竞品透视“扣费链接”得到
	 * @return result，(其中code：0.获取id，且status==2，可直接下载excel、1.获取id，且status!=2，需要等待、2.失败、9.超过最大尝试次数)
	 */
	public Result superJptsLljg(ZhSjs zhSjs, String uuid) {
		Result result = null;
		int time = 0; // 尝试次数
		int nulltime = 0; // 空次数
		do {
			result = this.superJptsLljg2(zhSjs, uuid);
			if (result == null) {
				nulltime++;
			}
			if (result != null && result.isSuccess()) {
				break; // 结束循环
			}
			time++;
			try {
				Thread.sleep(((time >= SjsConst.MAX_TRY_NUM) ? 0 : time) * 3000L);
			} catch (Exception e) {
				e.printStackTrace();
			}
		} while (time < SjsConst.MAX_TRY_NUM && nulltime < SjsConst.NULL_TRY_NUM);
		if (result == null) {
			result = new Result(2, "3次接口返回null。");
			log.info("获取superJptsLljg，尝试次数：" + time + "，返回null次数：" + nulltime);
		} else if (time >= SjsConst.MAX_TRY_NUM && result.getCode() == 1) {
			result.setCodeContent(9, "超过最大尝试");
			log.info("获取superJptsLljg，超过最大尝试次数：" + time + "，返回null次数：" + nulltime);
		}
		return result;
	}

	/**
	 * 超级竞品透视，获取数据，以及竞品透视采集状态
	 * 
	 * @param zhSjs 账号信息
	 * @param uuid  唯一id，由超级竞品透视“扣费链接”得到
	 * @return result，(其中code：0.获取id，且status==2，可直接下载excel、1.获取id，且status!=2，需要等待、2.失败、9.超过最大尝试次数)
	 */
	public Result superJptsLljg2(ZhSjs zhSjs, String uuid) {
		Result result = new Result(2, "失败");
		String url = String.format(SjsConst.SUPER_JPTS_LIST_DATA_URL, uuid);
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(zhSjs.getCookieStore()).build(); // 不需要cookie也可以
		HttpPost httpPost = new HttpPost(url);
		httpPost.setHeader("accept", "application/json, text/plain, */*");
		httpPost.setHeader("accept-encoding", "gzip, deflate, br");
		httpPost.setHeader("accept-language", "zh-CN,zh;q=0.9");
		httpPost.setHeader("content-type", "application/json;charset=UTF-8");
		httpPost.setHeader("origin", "https://www.shujushe.com");
		httpPost.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.75 Safari/537.36");

		Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000).setConnectionRequestTimeout(20000);
		httpPost.setConfig(requestConfig.build());
		try {
			// ArrayList<BasicNameValuePair> pairs = new ArrayList<BasicNameValuePair>();
			// pairs.add(new BasicNameValuePair("uuid", uuid));
			// pairs.add(new BasicNameValuePair("device", "2"));
			// UrlEncodedFormEntity urlEncodedFormEntity = new UrlEncodedFormEntity(pairs, "utf-8");
			// httpPost.setEntity(urlEncodedFormEntity);

			HttpResponse httpResponse = httpClient.execute(httpPost);
			HttpEntity entity = httpResponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			if (StrUtil.isNull(body)) { // 空
				log.error("数据蛇，获取superJptsLljg2：返回的是空字符串");
				return null;
			}
			if (!StrUtil.isJson(body)) {
				log.error("数据蛇，获取superJptsLljg2：返回的不是json");
				log.error(body);
				return null;
			}
			JSONObject jsonObj = JSONObject.fromObject(body);
			if (jsonObj.has("code")) { // code出现，例如：{"msg": "不支持' GET'请求","code": 500}
				log.error("数据蛇，获取superJptsLljg2，code错误：" + body);
				return null;
			}
			if (!jsonObj.has("errcode") || jsonObj.getInt("errcode") != 2000 || !jsonObj.has("result")
					|| jsonObj.getJSONObject("result").isNullObject()) { // 状态不为2000，说明接口返回失败
				log.info("数据蛇，获取superJptsLljg2，errcode错误：" + body);
				return null;
			}
			int status = jsonObj.getJSONObject("result").getInt("status");
			int statusCjTask = jsonObj.getJSONObject("result").getInt("statusCjTask");
			result.setCodeContent(Result.SUCCESS, "success");
			result.putKey("status", status);
			result.putKey("statusCjTask", statusCjTask);
			if (statusCjTask == 2) {
				JSONArray gjcArr = jsonObj.getJSONObject("result").getJSONArray("gjc");
				JSONArray qdArr = jsonObj.getJSONObject("result").getJSONArray("qd");
				JSONArray ztcArr = jsonObj.getJSONObject("result").getJSONArray("ztc");
				List<JptsLljg> gjcList = new ArrayList<JptsLljg>();
				List<JptsLljg> qdList = new ArrayList<JptsLljg>();
				List<JptsLljg> ztcList = new ArrayList<JptsLljg>();
				for (int i = 0; gjcArr != null && i < gjcArr.size(); i++) {
					JSONObject obj = (JSONObject) gjcArr.get(i);
					JptsLljg gjc = new JptsLljg(obj);
					gjcList.add(gjc);
				}
				for (int i = 0; qdArr != null && i < qdArr.size(); i++) {
					JSONObject obj = (JSONObject) qdArr.get(i);
					JptsLljg qd = new JptsLljg(obj);
					qdList.add(qd);
				}
				for (int i = 0; ztcArr != null && i < ztcArr.size(); i++) {
					JSONObject obj = (JSONObject) ztcArr.get(i);
					JptsLljg ztc = new JptsLljg(obj);
					ztcList.add(ztc);
				}
				result.putKey("gjcList", gjcList);
				result.putKey("qdList", qdList);
				result.putKey("ztcList", ztcList);
			}
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			// recordError.record(zhSjs, e.getMessage());
			log.error(e.getMessage(), e);
			return null;
		} finally {
			httpPost.releaseConnection();
		}
	}

	/**
	 * 超级竞品透视，宝贝综合信息
	 * 
	 * @param bbid 宝贝id
	 * @return result，(其中code：0.获取id，且status==2，可直接下载excel、1.获取id，且status!=2，需要等待、2.失败、9.超过最大尝试次数)
	 */
	public Result superJptsBbxx(ZhSjs zhSjs, String bbid) {
		Result result = null;
		int time = 0; // 尝试次数
		int nulltime = 0; // 空次数
		do {
			result = this.superJptsBbxx2(zhSjs, bbid);
			if (result == null) {
				nulltime++;
			}
			if (result != null && result.isSuccess()) {
				break; // 结束循环
			}
			time++;
			try {
				Thread.sleep(((time >= SjsConst.MAX_TRY_NUM) ? 0 : time) * 3000L);
			} catch (Exception e) {
				e.printStackTrace();
			}
		} while (time < SjsConst.MAX_TRY_NUM && nulltime < SjsConst.NULL_TRY_NUM);
		if (result == null) {
			result = new Result(2, "3次接口返回null。");
			log.info("获取superJptsBbxx，尝试次数：" + time + "，返回null次数：" + nulltime);
		} else if (time >= SjsConst.MAX_TRY_NUM && result.getCode() == 1) {
			result.setCodeContent(9, "超过最大尝试");
			log.info("获取superJptsBbxx，超过最大尝试次数：" + time + "，返回null次数：" + nulltime);
		}
		return result;
	}

	/**
	 * 超级竞品透视，宝贝综合信息
	 * 
	 * @param zhSjs 账号信息
	 * @param bbid  宝贝id
	 * @return result，(其中code：0.获取id，且status==2，可直接下载excel、1.获取id，且status!=2，需要等待、2.失败、9.超过最大尝试次数)
	 */
	public Result superJptsBbxx2(ZhSjs zhSjs, String bbid) {
		Result result = new Result(2, "失败");
		String url = String.format(SjsConst.SUPER_JPTS_DATA_URL, bbid);
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(zhSjs.getCookieStore()).build(); // 不需要cookie也可以
		HttpPost httpPost = new HttpPost(url);
		httpPost.setHeader("accept", "application/json, text/plain, */*");
		httpPost.setHeader("accept-encoding", "gzip, deflate, br");
		httpPost.setHeader("accept-language", "zh-CN,zh;q=0.9");
		httpPost.setHeader("content-type", "application/json;charset=UTF-8");
		httpPost.setHeader("origin", "https://www.shujushe.com");
		httpPost.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.75 Safari/537.36");

		Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000).setConnectionRequestTimeout(20000);
		httpPost.setConfig(requestConfig.build());
		try {
			// ArrayList<BasicNameValuePair> pairs = new ArrayList<BasicNameValuePair>();
			// pairs.add(new BasicNameValuePair("nid", bbid));
			// UrlEncodedFormEntity urlEncodedFormEntity = new UrlEncodedFormEntity(pairs, "utf-8");
			// httpPost.setEntity(urlEncodedFormEntity);

			HttpResponse httpResponse = httpClient.execute(httpPost);
			HttpEntity entity = httpResponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			if (StrUtil.isNull(body)) { // 空
				log.error("数据蛇，获取superJptsBbxx2：返回的是空字符串");
				return null;
			}
			if (!StrUtil.isJson(body)) {
				log.error("数据蛇，获取superJptsBbxx2：返回的不是json");
				log.error(body);
				return null;
			}
			JSONObject jsonObj = JSONObject.fromObject(body);
			if (jsonObj.has("code")) { // code出现，例如：{"msg": "不支持' GET'请求","code": 500}
				log.error("数据蛇，获取superJptsBbxx2，code错误：" + body);
				return null;
			}
			if (!jsonObj.has("errcode") || jsonObj.getInt("errcode") != 2000 || !jsonObj.has("result")) { // 状态不为2000，说明接口返回失败
				log.info("数据蛇，获取superJptsBbxx2，errcode错误：" + body);
				return null;
			}
			JSONArray resultArr = jsonObj.getJSONArray("result");
			if (resultArr == null || resultArr.size() < 1) {
				return null;
			}
			List<JptsBbxx> list = new ArrayList<JptsBbxx>();
			for (int i = 0; i < resultArr.size(); i++) {
				JSONObject obj = (JSONObject) resultArr.get(i);
				if (!obj.has("spid") || StrUtil.isNull(obj.getString("spid"))) {
					continue;
				}
				JptsBbxx jptsBbxx = new JptsBbxx(obj);
				list.add(jptsBbxx);
			}
			result.setCodeContent(Result.SUCCESS, "success");
			result.putKey("list", list);
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			// recordError.record(zhSjs, e.getMessage());
			log.error(e.getMessage(), e);
			return null;
		} finally {
			httpPost.releaseConnection();
		}
	}

	/**
	 * 超级竞品透视列表
	 * 
	 * @return result，(其中code：0.获取id，且status==2，可直接下载excel、1.获取id，且status!=2，需要等待、2.失败、9.超过最大尝试次数)
	 */
	public Result superJptsList(ZhSjs zhSjs, String bbid) {
		Result result = null;
		int time = 0; // 尝试次数
		int nulltime = 0; // 空次数
		do {
			result = this.superJptsList2(zhSjs, bbid);
			if (result == null) {
				nulltime++;
			}
			if (result != null && result.isSuccess()) {
				break; // 结束循环
			}
			time++;
			try {
				Thread.sleep(((time >= SjsConst.MAX_TRY_NUM) ? 0 : time) * 3000L);
			} catch (Exception e) {
				e.printStackTrace();
			}
		} while (time < SjsConst.MAX_TRY_NUM && nulltime < SjsConst.NULL_TRY_NUM);
		if (result == null) {
			result = new Result(2, "3次接口返回null。");
			log.info("获取superJptsList，尝试次数：" + time + "，返回null次数：" + nulltime);
		} else if (time >= SjsConst.MAX_TRY_NUM && result.getCode() == 1) {
			result.setCodeContent(9, "超过最大尝试");
			log.info("获取superJptsList，超过最大尝试次数：" + time + "，返回null次数：" + nulltime);
		}
		return result;
	}

	/**
	 * 超级竞品透视列表
	 * 
	 * @param zhSjs 账号信息
	 * @param bbid  宝贝id
	 * @return result，(其中code：0.获取id，且status==2，可直接下载excel、1.获取id，且status!=2，需要等待、2.失败、9.超过最大尝试次数)
	 */
	public Result superJptsList2(ZhSjs zhSjs, String bbid) {
		Result result = new Result(2, "失败");
		String url = String.format(SjsConst.SUPER_JPTS_LIST_URL, "1", zhSjs.getUsername()); // 第一页，pageSize = 40
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(zhSjs.getCookieStore()).build(); // 不需要cookie也可以
		HttpPost httpPost = new HttpPost(url);
		httpPost.setHeader("accept", "application/json, text/plain, */*");
		httpPost.setHeader("accept-encoding", "gzip, deflate, br");
		httpPost.setHeader("accept-language", "zh-CN,zh;q=0.9");
		httpPost.setHeader("content-type", "application/json;charset=UTF-8");
		httpPost.setHeader("origin", "https://www.shujushe.com");
		httpPost.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.116 Safari/537.36");

		Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000).setConnectionRequestTimeout(20000);
		httpPost.setConfig(requestConfig.build());
		try {
			ArrayList<BasicNameValuePair> pairs = new ArrayList<BasicNameValuePair>();
			pairs.add(new BasicNameValuePair("createBy", zhSjs.getUsername()));
			pairs.add(new BasicNameValuePair("pageNumber", "1"));
			pairs.add(new BasicNameValuePair("pageSize", "40"));
			UrlEncodedFormEntity urlEncodedFormEntity = new UrlEncodedFormEntity(pairs, "utf-8");
			httpPost.setEntity(urlEncodedFormEntity);

			HttpResponse httpResponse = httpClient.execute(httpPost);
			HttpEntity entity = httpResponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			if (StrUtil.isNull(body)) { // 空
				log.error("数据蛇，获取superJptsList2：返回的是空字符串");
				return null;
			}
			if (!StrUtil.isJson(body)) {
				log.error("数据蛇，获取superJptsList2：返回的不是json");
				log.error(body);
				return null;
			}
			JSONObject jsonObj = JSONObject.fromObject(body);
			if (jsonObj.has("code")) { // code出现，例如：{"msg": "不支持' GET'请求","code": 500}
				log.error("数据蛇，获取superJptsList2，code错误：" + body);
				return null;
			}
			if (!jsonObj.has("errcode") || jsonObj.getInt("errcode") != 2000 || !jsonObj.has("result")) { // 状态不为2000，说明接口返回失败
				log.info("数据蛇，获取superJptsList2，errcode错误：" + body);
				return null;
			}
			JSONArray resultArr = jsonObj.getJSONArray("result");
			if (resultArr == null || resultArr.size() < 1) {
				return null;
			}
			for (int i = 0; i < resultArr.size(); i++) {
				JSONObject resultObj = (JSONObject) resultArr.get(i);
				if (!resultObj.has("nid") || StrUtil.isNull(resultObj.getString("nid"))) {
					continue;
				}
				String nid = resultObj.getString("nid");
				String status = resultObj.getString("status");
				String statusCjTask = resultObj.getString("statusCjTask");
				if (nid.equals(bbid)) {
					if (!StrUtil.isNull(status) && "2".equals(status) && "2".equals(statusCjTask)) { // 数据已准备好，可以下载excel
						result.setCodeContent(Result.SUCCESS, "success");
						result.putKey("id", resultObj.getLong("id"));
						result.putKey("pictUrl", resultObj.getString("pictUrl"));
						result.putKey("datatpye", resultObj.getString("datatpye"));
						result.putKey("tjrq", resultObj.getString("tjrq"));
						result.putKey("tjrq2", resultObj.getString("tjrq2"));
						result.putKey("totalNum", resultObj.getInt("totalNum"));
						result.putKey("totalPage", resultObj.getInt("totalPage"));
						result.putKey("createTime", resultObj.getLong("createTime"));
					} else {
						// result.setCodeContent(1, "wait a minute");
						result.setCodeContent(Result.SUCCESS, "success");
						result.putKey("id", resultObj.getLong("id"));
					}
					result.putKey("status", status);
					result.putKey("statusCjTask", statusCjTask);
					result.putKey("uuid", resultObj.getString("uuid"));
					return result;
				}
			}
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			// recordError.record(zhSjs, e.getMessage());
			log.error(e.getMessage(), e);
			return null;
		} finally {
			httpPost.releaseConnection();
		}
	}

	/**
	 * 数据蛇旺旺查询扣费
	 * 
	 * @param
	 */
	@Retry
	public Result wwcxFree(ZhSjs zhSjs, String currentTime, String key) {
		Result result = new Result(2, "失败");
		String url = String.format(SjsConst.WWZYJ_URL, zhSjs.getUsername(), zhSjs.getToken().replace("=", "%3D"), zhSjs.getUserId(), currentTime, key);
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(zhSjs.getCookieStore()).build(); // 不需要cookie也可以
		HttpPost httpPost = new HttpPost(url);
		httpPost.setHeader("accept", "application/json, text/plain, */*");
		httpPost.setHeader("accept-encoding", "gzip, deflate, br");
		httpPost.setHeader("accept-language", "zh-CN,zh;q=0.9,en;q=0.8");
		httpPost.setHeader("content-type", "application/json;charset=UTF-8");
		httpPost.setHeader("origin", "https://www.shujushe.com");
		httpPost.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.198 Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(30000).setConnectTimeout(30000).setConnectionRequestTimeout(30000);
		httpPost.setConfig(requestConfig.build());
		try {
//			ArrayList<BasicNameValuePair> pairs = new ArrayList<BasicNameValuePair>();
//			pairs.add(new BasicNameValuePair("createBy", zhSjs.getUsername()));
//			UrlEncodedFormEntity urlEncodedFormEntity = new UrlEncodedFormEntity(pairs, "utf-8");
//			httpPost.setEntity(urlEncodedFormEntity);
			
			StringEntity stringEntity = new StringEntity("{\"createBy\": \"" + zhSjs.getUsername() + "\"}", "utf-8"); // 这里是关键,后面要跟上字符集格式
			stringEntity.setContentEncoding("UTF-8");
			stringEntity.setContentType("application/json"); // 发送json数据需要设置contentType
	        httpPost.setEntity(stringEntity);

			HttpResponse httpResponse = httpClient.execute(httpPost);
			HttpEntity entity = httpResponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			if (StrUtil.isNull(body)) { // 空
				log.error("数据蛇，获取wwcxFree：返回的是空字符串");
				return null;
			}
			if (!StrUtil.isJson(body)) {
				log.error("数据蛇，获取wwcxFree：返回的不是json");
				log.error(body);
				return null;
			}
			JSONObject jsonObj = JSONObject.fromObject(body);
			if (jsonObj.has("code")) { // code出现，例如：{"msg": "不支持' GET'请求","code": 500}
				log.error("数据蛇，获取wwcxFree，code错误：" + body);
				return null;
			}
			if (!jsonObj.has("errcode") || jsonObj.getInt("errcode") != 2000 || !jsonObj.has("result")) { // 状态不为2000，说明接口返回失败
				log.info("数据蛇，获取wwcxFree，errcode错误：" + body);
				return null;
			}
			boolean success = jsonObj.getBoolean("success");
			if (!success) {
				return null;
			}
			result.setCodeContent(Result.SUCCESS, "success");
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			return null;
		} finally {
			httpPost.releaseConnection();
		}
	}

	/**
	 * （数据蛇）旺旺照妖镜
	 * 
	 * @return result，(其中code：0.获取id，且status==2，可直接下载excel、1.获取id，且status!=2，需要等待、2.失败、9.超过最大尝试次数)
	 */
	public Result wwcx(ZhSjs zhSjs, String wwname, String currentTime, String key) {
		Result result = null;
		int time = 0; // 尝试次数
		int nulltime = 0; // 空次数
		do {
			result = this.wwcx2(zhSjs, wwname, currentTime, key);
			if (result == null) {
				nulltime++;
			}
			if (result != null && result.isSuccess()) {
				break; // 结束循环
			}
			time++;
			try {
				Thread.sleep(((time >= SjsConst.MAX_TRY_NUM) ? 0 : time) * 3000L);
			} catch (Exception e) {
				e.printStackTrace();
			}
		} while (time < SjsConst.MAX_TRY_NUM && nulltime < SjsConst.NULL_TRY_NUM);
		if (result == null) {
			result = new Result(2, "3次接口返回null。");
			log.info("获取wwcx，尝试次数：" + time + "，返回null次数：" + nulltime);
		} else if (time >= SjsConst.MAX_TRY_NUM && result.getCode() == 1) {
			result.setCodeContent(9, "超过最大尝试");
			log.info("获取wwcx，超过最大尝试次数：" + time + "，返回null次数：" + nulltime);
		}
		return result;
	}

	/**
	 * （数据蛇）旺旺照妖镜
	 * 
	 * @param zhSjs  账号信息
	 * @param wwname 旺旺号
	 * @return result，(其中code：0.获取id，且status==2，可直接下载excel、1.获取id，且status!=2，需要等待、2.失败、9.超过最大尝试次数)
	 */
	public Result wwcx2(ZhSjs zhSjs, String wwname, String currentTime, String key) {
		Result result = new Result(2, "失败");
		String url = String.format(SjsConst.WWZYJ_DATA_URL, zhSjs.getToken().replace("=", "%3D"), zhSjs.getUserId(), currentTime, key, wwname);
		try {
			url = String.format(SjsConst.WWZYJ_DATA_URL, zhSjs.getToken().replace("=", "%3D"), zhSjs.getUserId(), currentTime, key, URLEncoder.encode(wwname, "UTF-8"));
		} catch (UnsupportedEncodingException e1) {
			e1.printStackTrace();
			log.error(e1.getMessage(), e1);
		}
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(zhSjs.getCookieStore()).build(); // 不需要cookie也可以
		HttpGet httpGet = new HttpGet(url);
		httpGet.setHeader("accept", "application/json, text/plain, */*");
		httpGet.setHeader("accept-encoding", "gzip, deflate, br");
		httpGet.setHeader("accept-language", "zh-CN,zh;q=0.9,en;q=0.8");
		// httpGet.setHeader("content-type", "application/json;charset=UTF-8");
		httpGet.setHeader("origin", "https://www.shujushe.com");
		httpGet.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.198 Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000).setConnectionRequestTimeout(20000);
		httpGet.setConfig(requestConfig.build());
		try {
			HttpResponse httpResponse = httpClient.execute(httpGet);
			HttpEntity entity = httpResponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			if (StrUtil.isNull(body)) { // 空
				log.error("数据蛇，获取wwcx2：返回的是空字符串");
				return null;
			}
			if (!StrUtil.isJson(body)) {
				log.error("数据蛇，获取wwcx2：返回的不是json");
				log.error(body);
				return null;
			}
			JSONObject jsonObj = JSONObject.fromObject(body);
			if (jsonObj.has("code")) { // code出现，例如：{"msg": "不支持' GET'请求","code": 500}
				log.error("数据蛇，获取wwcx2，code错误：" + body);
				return null;
			}
			if (!jsonObj.has("errcode") || jsonObj.getInt("errcode") != 2000 || !jsonObj.has("result")) { // 状态不为2000，说明接口返回失败
				log.info("数据蛇，获取wwcx2，errcode错误：" + body);
				return null;
			}
			JSONObject obj = jsonObj.getJSONObject("result");
			if (obj == null) {
				return null;
			}
			Wwxx wwxx = new Wwxx(obj);
			result.setCodeContent(Result.SUCCESS, "success");
			result.putKey("data", wwxx);
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			return null;
		} finally {
			httpGet.releaseConnection();
		}
	}

	/**
	 * 数据蛇旺旺查询扣费
	 * 
	 * @param
	 */
	@Retry
	public Result wwvip(ZhSjs zhSjs, String wwname, String currentTime, String key) {
		Result result = new Result(2, "失败");
		String url = String.format(SjsConst.WWVIP_URL, wwname, zhSjs.getUsername(), zhSjs.getToken().replace("=", "%3D"), zhSjs.getUserId(), currentTime, key);
		try {
			url = String.format(SjsConst.WWVIP_URL, URLEncoder.encode(wwname, "UTF-8"), zhSjs.getUsername(), zhSjs.getToken().replace("=", "%3D"), zhSjs.getUserId(), currentTime, key);
		} catch (UnsupportedEncodingException e1) {
			e1.printStackTrace();
			log.error(e1.getMessage(), e1);
		}
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(zhSjs.getCookieStore()).build(); // 不需要cookie也可以
		HttpPost httpPost = new HttpPost(url);
		httpPost.setHeader("accept", "application/json, text/plain, */*");
		httpPost.setHeader("accept-encoding", "gzip, deflate, br");
		httpPost.setHeader("accept-language", "zh-CN,zh;q=0.9,en;q=0.8");
		httpPost.setHeader("content-type", "application/json;charset=UTF-8");
		httpPost.setHeader("origin", "https://www.shujushe.com");
		httpPost.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.198 Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(30000).setConnectTimeout(30000).setConnectionRequestTimeout(30000);
		httpPost.setConfig(requestConfig.build());
		try {
//			ArrayList<BasicNameValuePair> pairs = new ArrayList<BasicNameValuePair>();
//			pairs.add(new BasicNameValuePair("createBy", zhSjs.getUsername()));
//			pairs.add(new BasicNameValuePair("nick", wwname));
//			UrlEncodedFormEntity urlEncodedFormEntity = new UrlEncodedFormEntity(pairs, "utf-8");
//			httpPost.setEntity(urlEncodedFormEntity);
			
			StringEntity stringEntity = new StringEntity("{\"createBy\": \"" + zhSjs.getUsername() + "\", \"nick\": \"" + wwname + "\"}", "utf-8"); // 这里是关键,后面要跟上字符集格式
			stringEntity.setContentEncoding("UTF-8");
			stringEntity.setContentType("application/json"); // 发送json数据需要设置contentType
	        httpPost.setEntity(stringEntity);

			HttpResponse httpResponse = httpClient.execute(httpPost);
			HttpEntity entity = httpResponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			if (StrUtil.isNull(body)) { // 空
				log.error("数据蛇，获取wwvip：返回的是空字符串");
				return null;
			}
			if (!StrUtil.isJson(body)) {
				log.error("数据蛇，获取wwvip：返回的不是json");
				log.error(body);
				return null;
			}
			JSONObject jsonObj = JSONObject.fromObject(body);
			if (jsonObj.has("code")) { // code出现，例如：{"msg": "不支持' GET'请求","code": 500}
				log.error("数据蛇，获取wwvip，code错误：" + body);
				return null;
			}
			if (!jsonObj.has("errcode") || jsonObj.getInt("errcode") != 2000 || !jsonObj.has("result")) { // 状态不为2000，说明接口返回失败
				log.info("数据蛇，获取wwvip，errcode错误：" + body);
				return null;
			}
			String is88vip = jsonObj.getJSONObject("result").getString("is88vip");
			result.setCodeContent(Result.SUCCESS, "success");
			result.putKey("is88vip", is88vip);
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			return null;
		} finally {
			httpPost.releaseConnection();
		}
	}

	/**
	 * 数据蛇：淘客订单验证查询
	 * 
	 * @param zhSjs    数据蛇账号
	 * @param orderIds 订单ids
	 * @return uuid 返回uuid
	 */
	@Retry
	public Result tkddFree(ZhSjs zhSjs, String orderIds, String currentTime, String key) {
		Result result = new Result(2, "失败");
		String url = String.format(SjsConst.TKDD_URL, zhSjs.getUsername(), orderIds, zhSjs.getToken().replace("=", "%3D"), zhSjs.getUserId(), currentTime, key);
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(zhSjs.getCookieStore()).build(); // 不需要cookie也可以
		HttpPost httpPost = new HttpPost(url);
		httpPost.setHeader("accept", "application/json, text/plain, */*");
		httpPost.setHeader("accept-encoding", "gzip, deflate, br");
		httpPost.setHeader("accept-language", "zh-CN,zh;q=0.9,en;q=0.8");
		httpPost.setHeader("content-type", "application/json;charset=UTF-8");
		httpPost.setHeader("origin", "https://www.shujushe.com");
		httpPost.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.198 Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(10000).setConnectTimeout(10000).setConnectionRequestTimeout(10000);
		httpPost.setConfig(requestConfig.build());
		try {
//			ArrayList<BasicNameValuePair> pairs = new ArrayList<BasicNameValuePair>();
//			pairs.add(new BasicNameValuePair("createBy", zhSjs.getUsername()));
//			pairs.add(new BasicNameValuePair("orderId", orderIds));
//			UrlEncodedFormEntity urlEncodedFormEntity = new UrlEncodedFormEntity(pairs, "utf-8");
//			httpPost.setEntity(urlEncodedFormEntity);
			
			StringEntity stringEntity = new StringEntity("{\"createBy\": \"" + zhSjs.getUsername() + "\", \"orderId\": \"" + orderIds + "\"}", "utf-8"); // 这里是关键,后面要跟上字符集格式
			stringEntity.setContentEncoding("UTF-8");
			stringEntity.setContentType("application/json"); // 发送json数据需要设置contentType
	        httpPost.setEntity(stringEntity);

			HttpResponse httpResponse = httpClient.execute(httpPost);
			HttpEntity entity = httpResponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			if (StrUtil.isNull(body)) { // 空
				log.error("数据蛇，获取tkddFree：返回的是空字符串");
				return null;
			}
			if (!StrUtil.isJson(body)) {
				log.error("数据蛇，获取tkddFree：返回的不是json");
				log.error(body);
				return null;
			}
			JSONObject jsonObj = JSONObject.fromObject(body);
			if (jsonObj.has("code")) { // code出现，例如：{"msg": "不支持' GET'请求","code": 500}
				log.error("数据蛇，获取tkddFree，code错误：" + body);
				return null;
			}
			if (!jsonObj.has("errcode") || jsonObj.getInt("errcode") != 2000 || !jsonObj.has("result")) { // 状态不为2000，说明接口返回失败
				log.info("数据蛇，获取tkddFree，errcode错误：" + body);
				return null;
			}
			if (jsonObj.getJSONObject("result").has("uuid")
					&& !StrUtil.isNull(jsonObj.getJSONObject("result").getString("uuid"))) {
				String uuid = jsonObj.getJSONObject("result").getString("uuid");
				result.putKey("uuid", uuid);
				result.setCodeContent(Result.SUCCESS, "success");
				return result;
			}
			return null;
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			return null;
		} finally {
			httpPost.releaseConnection();
		}
	}

	/**
	 * （数据蛇）淘客订单验证
	 * 
	 * @return result，(其中code：0.获取id，且status==2，可直接下载excel、1.获取id，且status!=2，需要等待、2.失败、9.超过最大尝试次数)
	 */
	public Result tkdd(ZhSjs zhSjs, String uuid, String currentTime, String key) {
		Result result = null;
		int time = 0; // 尝试次数
		int nulltime = 0; // 空次数
		do {
			result = this.tkdd2(zhSjs, uuid, currentTime, key);
			if (result == null) {
				nulltime++;
			}
			if (result != null && result.isSuccess()) {
				break; // 结束循环
			}
			time++;
			try {
				Thread.sleep(((time >= SjsConst.MAX_TRY_NUM) ? 0 : time) * 3000L);
			} catch (Exception e) {
				e.printStackTrace();
			}
		} while (time < SjsConst.MAX_TRY_NUM && nulltime < SjsConst.NULL_TRY_NUM);
		if (result == null) {
			result = new Result(2, "3次接口返回null。");
			log.info("获取tkdd，尝试次数：" + time + "，返回null次数：" + nulltime);
		} else if (time >= SjsConst.MAX_TRY_NUM && result.getCode() == 1) {
			result.setCodeContent(9, "超过最大尝试");
			log.info("获取tkdd，超过最大尝试次数：" + time + "，返回null次数：" + nulltime);
		}
		return result;
	}

	/**
	 * （数据蛇）淘客订单验证
	 * 
	 * @param zhSjs 账号信息
	 * @param uuid
	 * @return result，(其中code：0.获取id，且status==2，可直接下载excel、1.获取id，且status!=2，需要等待、2.失败、9.超过最大尝试次数)
	 */
	public Result tkdd2(ZhSjs zhSjs, String uuid, String currentTime, String key) {
		Result result = new Result(2, "失败");
		String url = String.format(SjsConst.TKDD_DATA_URL, uuid, zhSjs.getToken().replace("=", "%3D"), zhSjs.getUserId(), currentTime, key);
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(zhSjs.getCookieStore()).build(); // 不需要cookie也可以
		HttpPost httpPost = new HttpPost(url);
		httpPost.setHeader("accept", "application/json, text/plain, */*");
		httpPost.setHeader("accept-encoding", "gzip, deflate, br");
		httpPost.setHeader("accept-language", "zh-CN,zh;q=0.9,en;q=0.8");
		httpPost.setHeader("content-type", "application/json;charset=UTF-8");
		httpPost.setHeader("origin", "https://www.shujushe.com");
		httpPost.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.198 Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000).setConnectionRequestTimeout(20000);
		httpPost.setConfig(requestConfig.build());
		try {
//			ArrayList<BasicNameValuePair> pairs = new ArrayList<BasicNameValuePair>();
//			pairs.add(new BasicNameValuePair("uuid", uuid));
//			UrlEncodedFormEntity urlEncodedFormEntity = new UrlEncodedFormEntity(pairs, "utf-8");
//			httpPost.setEntity(urlEncodedFormEntity);
			
			StringEntity stringEntity = new StringEntity("{\"uuid\": \"" + uuid + "\"}", "utf-8"); // 这里是关键,后面要跟上字符集格式
			stringEntity.setContentEncoding("UTF-8");
			stringEntity.setContentType("application/json"); // 发送json数据需要设置contentType
	        httpPost.setEntity(stringEntity);

			HttpResponse httpResponse = httpClient.execute(httpPost);
			HttpEntity entity = httpResponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			if (StrUtil.isNull(body)) { // 空
				log.error("数据蛇，获取tkdd2：返回的是空字符串");
				return null;
			}
			if (!StrUtil.isJson(body)) {
				log.error("数据蛇，获取tkdd2：返回的不是json");
				log.error(body);
				return null;
			}
			JSONObject jsonObj = JSONObject.fromObject(body);
			if (jsonObj.has("code")) { // code出现，例如：{"msg": "不支持' GET'请求","code": 500}
				log.error("数据蛇，获取tkdd2，code错误：" + body);
				return null;
			}
			if (!jsonObj.has("errcode") || jsonObj.getInt("errcode") != 2000 || !jsonObj.has("result")) { // 状态不为2000，说明接口返回失败
				log.info("数据蛇，获取tkdd2，errcode错误：" + body);
				return null;
			}
			JSONArray resultArr = jsonObj.getJSONArray("result");
			if (resultArr == null || resultArr.size() < 1) {
				return null;
			}
			List<Tkdd> list = new ArrayList<Tkdd>();
			int index_flag = 0;
			for (int i = 0; i < resultArr.size(); i++) {
				JSONObject obj = (JSONObject) resultArr.get(i);
				if (!obj.has("orderId") || StrUtil.isNull(obj.getString("orderId"))) {
					continue;
				}
				String status = obj.getString("status");
				if (!StrUtil.isNull(status) && "2".equals(status)) { // 数据已准备好
					Tkdd tkdd = new Tkdd(obj);
					list.add(tkdd);
				} else {
					index_flag = index_flag + 1;
				}
			}
			if (index_flag == 0) {
				result.putKey("list", list);
				result.setCodeContent(Result.SUCCESS, "success");
				return result;
			}
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			return null;
		} finally {
			httpPost.releaseConnection();
		}
	}

	/**
	 * 数据蛇：店铺上新查询
	 * 
	 * @param zhSjs 数据蛇账号
	 * @param nick  店铺旺旺号
	 */
	@Retry
	public Result dpsxFree(ZhSjs zhSjs, String nick, String currentTime, String key) {
		Result result = new Result(2, "失败");
		String url = String.format(SjsConst.DPSXCX_URL, nick, zhSjs.getUsername(), zhSjs.getToken().replace("=", "%3D"), zhSjs.getUserId(), currentTime, key);
		try {
			url = String.format(SjsConst.DPSXCX_URL, URLEncoder.encode(nick, "UTF-8"), zhSjs.getUsername(), zhSjs.getToken().replace("=", "%3D"), zhSjs.getUserId(), currentTime, key);
		} catch (UnsupportedEncodingException e1) {
			e1.printStackTrace();
			log.error(e1.getMessage(), e1);
		}
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(zhSjs.getCookieStore()).build(); // 不需要cookie也可以
		HttpPost httpPost = new HttpPost(url);
		httpPost.setHeader("accept", "application/json, text/plain, */*");
		httpPost.setHeader("accept-encoding", "gzip, deflate, br");
		httpPost.setHeader("accept-language", "zh-CN,zh;q=0.9,en;q=0.8");
		httpPost.setHeader("content-type", "application/json;charset=UTF-8");
		httpPost.setHeader("origin", "https://www.shujushe.com");
		httpPost.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.198 Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(60000).setConnectTimeout(60000).setConnectionRequestTimeout(60000);
		httpPost.setConfig(requestConfig.build());
		try {
//			ArrayList<BasicNameValuePair> pairs = new ArrayList<BasicNameValuePair>();
//			pairs.add(new BasicNameValuePair("nick", nick));
//			pairs.add(new BasicNameValuePair("userName", zhSjs.getUsername()));
//			UrlEncodedFormEntity urlEncodedFormEntity = new UrlEncodedFormEntity(pairs, "utf-8");
//			httpPost.setEntity(urlEncodedFormEntity);
			
			StringEntity stringEntity = new StringEntity("{\"nick\": \"" + nick + "\", \"userName\": \"" + zhSjs.getUsername() + "\"}", "utf-8"); // 这里是关键,后面要跟上字符集格式
			stringEntity.setContentEncoding("UTF-8");
			stringEntity.setContentType("application/json"); // 发送json数据需要设置contentType
	        httpPost.setEntity(stringEntity);

			HttpResponse httpResponse = httpClient.execute(httpPost);
			HttpEntity entity = httpResponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			if (StrUtil.isNull(body)) { // 空
				log.error("数据蛇，获取dpsxFree：返回的是空字符串");
				return null;
			}
			if (!StrUtil.isJson(body)) {
				log.error("数据蛇，获取dpsxFree：返回的不是json");
				log.error(body);
				return null;
			}
			JSONObject jsonObj = JSONObject.fromObject(body);
			if (jsonObj.has("code")) { // code出现，例如：{"msg": "不支持' GET'请求","code": 500}
				log.error("数据蛇，获取dpsxFree，code错误：" + body);
				return null;
			}
			if (!jsonObj.has("errcode") || jsonObj.getInt("errcode") != 2000 || !jsonObj.has("result")) { // 状态不为2000，说明接口返回失败
				log.info("数据蛇，获取dpsxFree，errcode错误：" + body);
				return null;
			}
			boolean success = jsonObj.getBoolean("success");
			if (!success) {
				return null;
			}
			result.setCodeContent(Result.SUCCESS, "success");
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			return null;
		} finally {
			httpPost.releaseConnection();
		}
	}

	/**
	 * （数据蛇）店铺上新状态查询
	 * 
	 * @return result，(其中code：0.获取id，且status==2，可直接下载excel、1.获取id，且status!=2，需要等待、2.失败、9.超过最大尝试次数)
	 */
	public Result dpsxStatus(ZhSjs zhSjs, String nick, String currentTime, String key) {
		Result result = null;
		int time = 0; // 尝试次数
		int nulltime = 0; // 空次数
		do {
			result = this.dpsxStatus2(zhSjs, nick, currentTime, key);
			if (result == null) {
				nulltime++;
			}
			if (result != null && result.isSuccess()) {
				break; // 结束循环
			}
			time++;
			try {
				Thread.sleep(((time >= SjsConst.MAX_TRY_NUM) ? 0 : time) * 3000L);
			} catch (Exception e) {
				e.printStackTrace();
			}
		} while (time < SjsConst.MAX_TRY_NUM && nulltime < SjsConst.NULL_TRY_NUM);
		if (result == null) {
			result = new Result(2, "3次接口返回null。");
			log.info("获取dpsxStatus，尝试次数：" + time + "，返回null次数：" + nulltime);
		} else if (time >= SjsConst.MAX_TRY_NUM && result.getCode() == 1) {
			result.setCodeContent(9, "超过最大尝试");
			log.info("获取dpsxStatus，超过最大尝试次数：" + time + "，返回null次数：" + nulltime);
		}
		return result;
	}

	/**
	 * （数据蛇）店铺上新状态查询
	 * 
	 * @param zhSjs 账号信息
	 * @param nick  店铺旺旺号
	 * @return result，(其中code：0.获取id，且status==2，可直接下载excel、1.获取id，且status!=2，需要等待、2.失败、9.超过最大尝试次数)
	 */
	public Result dpsxStatus2(ZhSjs zhSjs, String nick, String currentTime, String key) {
		Result result = new Result(2, "失败");
		String url = String.format(SjsConst.DPSXCX_DATA_URL, "1", "40", zhSjs.getUsername(), zhSjs.getToken().replace("=", "%3D"), zhSjs.getUserId(), currentTime, key);
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(zhSjs.getCookieStore()).build(); // 不需要cookie也可以
		HttpPost httpPost = new HttpPost(url);
		httpPost.setHeader("accept", "application/json, text/plain, */*");
		httpPost.setHeader("accept-encoding", "gzip, deflate, br");
		httpPost.setHeader("accept-language", "zh-CN,zh;q=0.9,en;q=0.8");
		httpPost.setHeader("content-type", "application/json;charset=UTF-8");
		httpPost.setHeader("origin", "https://www.shujushe.com");
		httpPost.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.198 Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000).setConnectionRequestTimeout(20000);
		httpPost.setConfig(requestConfig.build());
		try {
//			ArrayList<BasicNameValuePair> pairs = new ArrayList<BasicNameValuePair>();
//			pairs.add(new BasicNameValuePair("pageNumber", "1"));
//			pairs.add(new BasicNameValuePair("pageSize", "40"));
//			pairs.add(new BasicNameValuePair("userName", zhSjs.getUsername()));
//			UrlEncodedFormEntity urlEncodedFormEntity = new UrlEncodedFormEntity(pairs, "utf-8");
//			httpPost.setEntity(urlEncodedFormEntity);
			
			StringEntity stringEntity = new StringEntity("{\"pageNumber\": 1, \"pageSize\": 40, \"userName\": \"" + zhSjs.getUsername() + "\"}", "utf-8"); // 这里是关键,后面要跟上字符集格式
			stringEntity.setContentEncoding("UTF-8");
			stringEntity.setContentType("application/json"); // 发送json数据需要设置contentType
	        httpPost.setEntity(stringEntity);

			HttpResponse httpResponse = httpClient.execute(httpPost);
			HttpEntity entity = httpResponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			if (StrUtil.isNull(body)) { // 空
				log.error("数据蛇，获取dpsxStatus2：返回的是空字符串");
				return null;
			}
			if (!StrUtil.isJson(body)) {
				log.error("数据蛇，获取dpsxStatus2：返回的不是json");
				log.error(body);
				return null;
			}
			JSONObject jsonObj = JSONObject.fromObject(body);
			if (jsonObj.has("code")) { // code出现，例如：{"msg": "不支持' GET'请求","code": 500}
				log.error("数据蛇，获取dpsxStatus2，code错误：" + body);
				return null;
			}
			if (!jsonObj.has("errcode") || jsonObj.getInt("errcode") != 2000 || !jsonObj.has("result")) { // 状态不为2000，说明接口返回失败
				log.info("数据蛇，获取dpsxStatus2，errcode错误：" + body);
				return null;
			}
			JSONArray resultArr = jsonObj.getJSONArray("result");
			if (resultArr == null || resultArr.size() < 1) {
				return null;
			}
			for (int i = 0; i < resultArr.size(); i++) {
				JSONObject obj = (JSONObject) resultArr.get(i);
				if (!obj.has("uuid") || StrUtil.isNull(obj.getString("uuid"))) {
					continue;
				}
				if (!obj.has("nick") || StrUtil.isNull(obj.getString("nick")) || !nick.equals(obj.getString("nick"))) {
					continue;
				}
				String status = obj.getString("status");
				if (!StrUtil.isNull(status) && "2".equals(status)) { // 数据已准备好
					String uuid = obj.getString("uuid");
					result.putKey("uuid", uuid);
					result.setCodeContent(Result.SUCCESS, "success");
					return result;
				} else {
					result.setCodeContent(1, "数据还没准备好！");
					return result;
				}
			}
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			return null;
		} finally {
			httpPost.releaseConnection();
		}
	}

	/**
	 * （数据蛇）店铺上新List
	 * 
	 * @return result，(其中code：0.获取id，且status==2，可直接下载excel、1.获取id，且status!=2，需要等待、2.失败、9.超过最大尝试次数)
	 */
	public Result dpsx(ZhSjs zhSjs, String uuid, String currentTime, String key) {
		Result result = null;
		int time = 0; // 尝试次数
		int nulltime = 0; // 空次数
		do {
			result = this.dpsx2(zhSjs, uuid, currentTime, key);
			if (result == null) {
				nulltime++;
			}
			if (result != null && result.isSuccess()) {
				break; // 结束循环
			}
			time++;
			try {
				Thread.sleep(((time >= SjsConst.MAX_TRY_NUM) ? 0 : time) * 3000L);
			} catch (Exception e) {
				e.printStackTrace();
			}
		} while (time < SjsConst.MAX_TRY_NUM && nulltime < SjsConst.NULL_TRY_NUM);
		if (result == null) {
			result = new Result(2, "3次接口返回null。");
			log.info("获取dpsx，尝试次数：" + time + "，返回null次数：" + nulltime);
		} else if (time >= SjsConst.MAX_TRY_NUM && result.getCode() == 1) {
			result.setCodeContent(9, "超过最大尝试");
			log.info("获取dpsx，超过最大尝试次数：" + time + "，返回null次数：" + nulltime);
		}
		return result;
	}

	/**
	 * （数据蛇）店铺上新List
	 * 
	 * @param zhSjs 账号信息
	 * @param uuid
	 * @return result，(其中code：0.获取id，且status==2，可直接下载excel、1.获取id，且status!=2，需要等待、2.失败、9.超过最大尝试次数)
	 */
	public Result dpsx2(ZhSjs zhSjs, String uuid, String currentTime, String key) {
		Result result = new Result(2, "失败");
		String url = String.format(SjsConst.DPSXCX_LIST_URL, uuid, zhSjs.getToken().replace("=", "%3D"), zhSjs.getUserId(), currentTime, key);
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(zhSjs.getCookieStore()).build(); // 不需要cookie也可以
		HttpPost httpPost = new HttpPost(url);
		httpPost.setHeader("accept", "application/json, text/plain, */*");
		httpPost.setHeader("accept-encoding", "gzip, deflate, br");
		httpPost.setHeader("accept-language", "zh-CN,zh;q=0.9,en;q=0.8");
		httpPost.setHeader("content-type", "application/json;charset=UTF-8");
		httpPost.setHeader("origin", "https://www.shujushe.com");
		httpPost.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.198 Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(30000).setConnectTimeout(30000).setConnectionRequestTimeout(30000);
		httpPost.setConfig(requestConfig.build());
		try {
//			ArrayList<BasicNameValuePair> pairs = new ArrayList<BasicNameValuePair>();
//			pairs.add(new BasicNameValuePair("uuid", uuid));
//			UrlEncodedFormEntity urlEncodedFormEntity = new UrlEncodedFormEntity(pairs, "utf-8");
//			httpPost.setEntity(urlEncodedFormEntity);
			
			StringEntity stringEntity = new StringEntity("{\"uuid\": \"" + uuid + "\"}", "utf-8"); // 这里是关键,后面要跟上字符集格式
			stringEntity.setContentEncoding("UTF-8");
			stringEntity.setContentType("application/json"); // 发送json数据需要设置contentType
	        httpPost.setEntity(stringEntity);

			HttpResponse httpResponse = httpClient.execute(httpPost);
			HttpEntity entity = httpResponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			if (StrUtil.isNull(body)) { // 空
				log.error("数据蛇，获取dpsx2：返回的是空字符串");
				return null;
			}
			if (!StrUtil.isJson(body)) {
				log.error("数据蛇，获取dpsx2：返回的不是json");
				log.error(body);
				return null;
			}
			JSONObject jsonObj = JSONObject.fromObject(body);
			if (jsonObj.has("code")) { // code出现，例如：{"msg": "不支持' GET'请求","code": 500}
				log.error("数据蛇，获取dpsx2，code错误：" + body);
				return null;
			}
			if (!jsonObj.has("errcode") || jsonObj.getInt("errcode") != 2000 || !jsonObj.has("result")) { // 状态不为2000，说明接口返回失败
				log.info("数据蛇，获取dpsx2，errcode错误：" + body);
				return null;
			}
			List<DpsxItem> list = new ArrayList<DpsxItem>();
			JSONArray resultArr = jsonObj.getJSONArray("result");
			// if (resultArr == null || resultArr.size() < 1) {
			// return null;
			// }
			for (int i = 0; resultArr != null && i < resultArr.size(); i++) {
				JSONObject obj = (JSONObject) resultArr.get(i);
				if (!obj.has("nid") || StrUtil.isNull(obj.getString("nid"))) {
					continue;
				}
				DpsxItem dpsxItem = new DpsxItem(obj);
				list.add(dpsxItem);
			}
			result.putKey("list", list);
			result.setCodeContent(Result.SUCCESS, "success");
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			return null;
		} finally {
			httpPost.releaseConnection();
		}
	}

	/**
	 * （数据蛇） 淘口令生成
	 * 
	 * @param zhSjs 账号信息
	 * @param url   宝贝链接或者搜索页面链接地址
	 * @return
	 */
	public Result tklsc(ZhSjs zhSjs, String url) {
		Result result = new Result(2, "失败");
		String requestPostUrl = SjsConst.TKL_URL;
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(zhSjs.getCookieStore()).build(); // 不需要cookie也可以
		HttpPost httpPost = new HttpPost(requestPostUrl);
		httpPost.setHeader("accept", "application/json, text/plain, */*");
		httpPost.setHeader("accept-encoding", "gzip, deflate, br");
		httpPost.setHeader("accept-language", "zh-CN,zh;q=0.9");
		httpPost.setHeader("content-type", "application/json;charset=UTF-8");
		httpPost.setHeader("origin", "https://www.shujushe.com");
		httpPost.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.75 Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000).setConnectionRequestTimeout(20000);
		httpPost.setConfig(requestConfig.build());
		try {
			String json = "{\"createBy\":\"" + zhSjs.getUsername() + "\",\"url\":\"" + url + "\"}";
			httpPost.setEntity(new StringEntity(json, "UTF-8"));

			HttpResponse httpResponse = httpClient.execute(httpPost);
			HttpEntity entity = httpResponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			if (StrUtil.isNull(body)) { // 空
				log.error("数据蛇，获取tklsc：返回的是空字符串");
				return null;
			}
			if (!StrUtil.isJson(body)) {
				log.error("数据蛇，获取tklsc：返回的不是json");
				log.error(body);
				return null;
			}
			JSONObject jsonObj = JSONObject.fromObject(body);
			if (jsonObj.has("code")) { // code出现，例如：{"msg": "不支持' GET'请求","code": 500}
				log.error("数据蛇，获取tklsc，code错误：" + body);
				return null;
			}
			if (!jsonObj.has("errcode") || jsonObj.getInt("errcode") != 2000 || !jsonObj.has("result")) { // 状态不为2000，说明接口返回失败
				log.info("数据蛇，获取tklsc，errcode错误：" + body);
				return null;
			}
			JSONObject resultObj = jsonObj.optJSONObject("result");
			if (resultObj == null || resultObj.isNullObject()) {
				return null;
			}
			result.putKey("tkl", new Tkl(resultObj, url));
			result.setCodeContent(Result.SUCCESS, "success");
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			return null;
		} finally {
			httpPost.releaseConnection();
		}
	}

	/**
	 * （数据蛇）搜索秒单生成 扣费接口 不做重试
	 * 
	 * @param zhSjs 账号信息
	 * @param input 参数
	 * @return
	 */
	public Result ssmd(ZhSjs zhSjs, SsmdInput input) {
		Result result = new Result(2, "失败");
		String requestPostUrl = String.format(SjsConst.SSMD_URL, zhSjs.getUsername(), input.getGjcUrlEncode(), input.getWwUrlEncode(), input.getBbid());
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(zhSjs.getCookieStore()).build();
		HttpPost httpPost = new HttpPost(requestPostUrl);
		httpPost.setHeader("accept", "application/json, text/plain, */*");
		httpPost.setHeader("accept-encoding", "gzip, deflate, br");
		httpPost.setHeader("accept-language", "zh-CN,zh;q=0.9");
		httpPost.setHeader("content-type", "application/json;charset=UTF-8");
		httpPost.setHeader("origin", "https://www.shujushe.com");
		httpPost.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.75 Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000).setConnectionRequestTimeout(20000);
		httpPost.setConfig(requestConfig.build());
		try {
			input.setCreateBy(zhSjs.getUsername());
			String json = "{\"createBy\":\"" + zhSjs.getUsername() + "\",\"title\":\"" + input.getGjc()
					+ "\",\"nick\":\"" + input.getWw() + "\",\"nid\":\"" + input.getBbid() + "\"}";
			httpPost.setEntity(new StringEntity(json, "UTF-8"));

			HttpResponse httpResponse = httpClient.execute(httpPost);
			HttpEntity entity = httpResponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			if (StrUtil.isNull(body)) { // 空
				log.error("数据蛇，获取ssmd：返回的是空字符串");
				return null;
			}
			if (!StrUtil.isJson(body)) {
				log.error("数据蛇，获取ssmd：返回的不是json");
				log.error(body);
				return null;
			}
			JSONObject jsonObj = JSONObject.fromObject(body);
			if (jsonObj.has("code")) { // code出现，例如：{"msg": "不支持' GET'请求","code": 500}
				log.error("数据蛇，获取ssmd，code错误：" + body);
				return null;
			}
			if (!jsonObj.has("errcode") || jsonObj.getInt("errcode") != 2000 || !jsonObj.has("result")) { // 状态不为2000，说明接口返回失败
				log.info("数据蛇，获取ssmd，errcode错误：" + body);
				return null;
			}
			result.setCodeContent(Result.SUCCESS, "success");
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			return null;
		} finally {
			httpPost.releaseConnection();
		}
	}

	/**
	 * （数据蛇）查询搜索秒单生成记录（最新一条）
	 * 
	 * @param zhSjs 账号信息
	 * @return
	 */
	@Retry
	public Result ssmdjl(ZhSjs zhSjs) {
		Result result = new Result(2, "失败");
		String requestPostUrl = String.format(SjsConst.SSMD_JL_URL, zhSjs.getUsername(), 1, 1);
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(zhSjs.getCookieStore()).build();
		HttpPost httpPost = new HttpPost(requestPostUrl);
		httpPost.setHeader("accept", "application/json, text/plain, */*");
		httpPost.setHeader("accept-encoding", "gzip, deflate, br");
		httpPost.setHeader("accept-language", "zh-CN,zh;q=0.9");
		httpPost.setHeader("content-type", "application/json;charset=UTF-8");
		httpPost.setHeader("origin", "https://www.shujushe.com");
		httpPost.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.75 Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000).setConnectionRequestTimeout(20000);
		httpPost.setConfig(requestConfig.build());
		try {
			String json = "{\"createBy\":\"" + zhSjs.getUsername() + "\",\"PageSize\":\"" + 1 + "\",\"PageNumber\":\""
					+ 1 + "\"}";
			httpPost.setEntity(new StringEntity(json, "UTF-8"));

			HttpResponse httpResponse = httpClient.execute(httpPost);
			HttpEntity entity = httpResponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			if (StrUtil.isNull(body)) { // 空
				log.error("数据蛇，获取ssmdjl：返回的是空字符串");
				return null;
			}
			if (!StrUtil.isJson(body)) {
				log.error("数据蛇，获取ssmdjl：返回的不是json");
				log.error(body);
				return null;
			}
			JSONObject jsonObj = JSONObject.fromObject(body);
			if (jsonObj.has("code")) { // code出现，例如：{"msg": "不支持' GET'请求","code": 500}
				log.error("数据蛇，获取ssmdjl，code错误：" + body);
				return null;
			}
			if (!jsonObj.has("errcode") || jsonObj.getInt("errcode") != 2000 || !jsonObj.has("result")) { // 状态不为2000，说明接口返回失败
				log.info("数据蛇，获取ssmdjl，errcode错误：" + body);
				return null;
			}

			JSONArray jsonArray = jsonObj.getJSONArray("result");
			if (jsonArray == null || jsonArray.size() == 0 || jsonArray.isEmpty()) {
				return null;
			}

			Ssmd ssmd = new Ssmd(jsonArray);
			result.setCodeContent(Result.SUCCESS, ssmd.toString());
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			return null;
		} finally {
			httpPost.releaseConnection();
		}
	}

	/**
	 * 搜索卡首屏 扣费接口 不做重试
	 * 
	 * @param zhSjs 账户信息
	 * @param input 参数
	 * @return
	 */
	public Result ssksp(ZhSjs zhSjs, SskspInput input) {
		Result result = new Result(2, "失败");
		String requestPostUrl = String.format(SjsConst.SSKSP_URL, input.getGjcUrlEncode(), input.getBbid(), zhSjs.getUsername());
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(zhSjs.getCookieStore()).build();
		HttpPost httpPost = new HttpPost(requestPostUrl);
		httpPost.setHeader("accept", "application/json, text/plain, */*");
		httpPost.setHeader("accept-encoding", "gzip, deflate, br");
		httpPost.setHeader("accept-language", "zh-CN,zh;q=0.9");
		httpPost.setHeader("content-type", "application/json;charset=UTF-8");
		httpPost.setHeader("origin", "https://www.shujushe.com");
		httpPost.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.75 Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000).setConnectionRequestTimeout(20000);
		httpPost.setConfig(requestConfig.build());
		try {
			String json = "{\"createBy\":\"" + zhSjs.getUsername() + "\",\"gjc\":\"" + input.getGjc() + "\",\"nid\":\""
					+ input.getBbid() + "\"}";
			httpPost.setEntity(new StringEntity(json, "UTF-8"));

			HttpResponse httpResponse = httpClient.execute(httpPost);
			HttpEntity entity = httpResponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			if (StrUtil.isNull(body)) { // 空
				log.error("数据蛇，获取ssksp：返回的是空字符串");
				return null;
			}
			if (!StrUtil.isJson(body)) {
				log.error("数据蛇，获取ssksp：返回的不是json");
				log.error(body);
				return null;
			}
			JSONObject jsonObj = JSONObject.fromObject(body);
			if (jsonObj.has("code")) { // code出现，例如：{"msg": "不支持' GET'请求","code": 500}
				log.error("数据蛇，获取ssksp，code错误：" + body);
				return null;
			}
			if (!jsonObj.has("errcode") || jsonObj.getInt("errcode") != 2000 || !jsonObj.has("result")) { // 状态不为2000，说明接口返回失败
				log.info("数据蛇，获取ssksp，errcode错误：" + body);
				return null;
			}

			result.setCodeContent(Result.SUCCESS, "成功");
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			return null;
		} finally {
			httpPost.releaseConnection();
		}
	}

	/**
	 * 搜索卡首屏记录（最新一条）
	 * 
	 * @param zhSjs 账户信息
	 * @return
	 */
	@Retry
	public Result sskspjl(ZhSjs zhSjs) {
		Result result = new Result(2, "失败");
		String requestPostUrl = String.format(SjsConst.SSKSP_JL_URL, 1, 1, zhSjs.getUsername());
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(zhSjs.getCookieStore()).build();
		HttpPost httpPost = new HttpPost(requestPostUrl);
		httpPost.setHeader("accept", "application/json, text/plain, */*");
		httpPost.setHeader("accept-encoding", "gzip, deflate, br");
		httpPost.setHeader("accept-language", "zh-CN,zh;q=0.9");
		httpPost.setHeader("content-type", "application/json;charset=UTF-8");
		httpPost.setHeader("origin", "https://www.shujushe.com");
		httpPost.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.75 Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000).setConnectionRequestTimeout(20000);
		httpPost.setConfig(requestConfig.build());
		try {
			String json = "{\"createBy\":\"" + zhSjs.getUsername() + "\",\"pageSize\":\"" + 1 + "\",\"pageNumber\":\""
					+ 1 + "\"}";
			httpPost.setEntity(new StringEntity(json, "UTF-8"));

			HttpResponse httpResponse = httpClient.execute(httpPost);
			HttpEntity entity = httpResponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			if (StrUtil.isNull(body)) { // 空
				log.error("数据蛇，获取sskspjl：返回的是空字符串");
				return null;
			}
			if (!StrUtil.isJson(body)) {
				log.error("数据蛇，获取sskspjl：返回的不是json");
				log.error(body);
				return null;
			}
			JSONObject jsonObj = JSONObject.fromObject(body);
			if (jsonObj.has("code")) { // code出现，例如：{"msg": "不支持' GET'请求","code": 500}
				log.error("数据蛇，获取sskspjl，code错误：" + body);
				return null;
			}
			if (!jsonObj.has("errcode") || jsonObj.getInt("errcode") != 2000 || !jsonObj.has("result")) { // 状态不为2000，说明接口返回失败
				log.info("数据蛇，获取sskspjl，errcode错误：" + body);
				return null;
			}

			JSONArray jsonArray = jsonObj.getJSONArray("result");
			if (jsonArray == null || jsonArray.size() == 0 || jsonArray.isEmpty()) {
				return null;
			}

			Ssksp ssksp = new Ssksp(jsonArray.getJSONObject(0));
			result.setCodeContent(Result.SUCCESS, ssksp.toString());
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			return null;
		} finally {
			httpPost.releaseConnection();
		}
	}

	/**
	 * 搜索打标卡首屏 霸道版
	 * 
	 * @param zhSjs账户信息
	 * @param input参数
	 * @return
	 */
	public Result sskspbd(ZhSjs zhSjs, SsdbkspBdbInput input) {
		Result result = new Result(2, "失败");
		String requestPostUrl = String.format(SjsConst.SSDBKSP_BD_URL, input.getGjcUrlEncode(), input.getBbid(),
				SsdbkspBdbInput.getWwUrlEncode(input.getWw()), SjsConst.SSDBKSP_BD, zhSjs.getUserId());
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(zhSjs.getCookieStore()).build();
		HttpPost httpPost = new HttpPost(requestPostUrl);
		httpPost.setHeader("accept", "application/json, text/plain, */*");
		httpPost.setHeader("accept-encoding", "gzip, deflate, br");
		httpPost.setHeader("accept-language", "zh-CN,zh;q=0.9");
		httpPost.setHeader("content-type", "application/json;charset=UTF-8");
		httpPost.setHeader("origin", "https://www.shujushe.com");
		httpPost.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.75 Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000).setConnectionRequestTimeout(20000);
		httpPost.setConfig(requestConfig.build());
		try {
			String json = "[{\"title\":\"" + input.getGjc() + "\",\"type\":\"" + 1 + "\",\"nid\":\"" + input.getBbid()
					+ "\",\"userId\":\"" + zhSjs.getUserId() + "\",\"date\":" + "\"\"" + ",\"nick\":\"" + input.getWw()
					+ "\"}]";
			httpPost.setEntity(new StringEntity(json, "UTF-8"));

			HttpResponse httpResponse = httpClient.execute(httpPost);
			HttpEntity entity = httpResponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			if (StrUtil.isNull(body)) { // 空
				log.error("数据蛇，获取sskspbd：返回的是空字符串");
				return null;
			}
			if (!StrUtil.isJson(body)) {
				log.error("数据蛇，获取sskspbd：返回的不是json");
				log.error(body);
				return null;
			}
			JSONObject jsonObj = JSONObject.fromObject(body);
			if (jsonObj.has("code")) { // code出现，例如：{"msg": "不支持' GET'请求","code": 500}
				log.error("数据蛇，获取sskspbd，code错误：" + body);
				return null;
			}
			if (!jsonObj.has("errcode") || jsonObj.getInt("errcode") != 2000 || !jsonObj.has("result")) { // 状态不为2000，说明接口返回失败
				log.info("数据蛇，获取sskspbd，errcode错误：" + body);
				return null;
			}

			result.setCodeContent(Result.SUCCESS, "成功");
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			return null;
		} finally {
			httpPost.releaseConnection();
		}
	}

	/**
	 * 卡流量渠道
	 * 
	 * @param zhSjs
	 * @param input
	 * @return
	 */
	public Result kllqd(ZhSjs zhSjs, KllqdInput input) {
		Result result = new Result(2, "失败");
		String requestPostUrl = String.format(SjsConst.KLLQD_URL, input.getValue2(), zhSjs.getUserId(), input.getType(), input.getJpid());
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(zhSjs.getCookieStore()).build();
		HttpPost httpPost = new HttpPost(requestPostUrl);
		httpPost.setHeader("accept", "application/json, text/plain, */*");
		httpPost.setHeader("accept-encoding", "gzip, deflate, br");
		httpPost.setHeader("accept-language", "zh-CN,zh;q=0.9");
		httpPost.setHeader("content-type", "application/json;charset=UTF-8");
		httpPost.setHeader("origin", "https://www.shujushe.com");
		httpPost.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.75 Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000).setConnectionRequestTimeout(20000);
		httpPost.setConfig(requestConfig.build());
		try {
			String json = "{\"url\":\"" + input.getValue() + "\",\"userId\":\"" + zhSjs.getUserId() + "\",\"jpid\":\""
					+ input.getJpid() + "\",\"klltype\":[" + input.getType() + "]}";
			httpPost.setEntity(new StringEntity(json, "UTF-8"));

			HttpResponse httpResponse = httpClient.execute(httpPost);
			HttpEntity entity = httpResponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			if (StrUtil.isNull(body)) { // 空
				log.error("数据蛇，获取kllqd：返回的是空字符串");
				return null;
			}
			if (!StrUtil.isJson(body)) {
				log.error("数据蛇，获取kllqd：返回的不是json");
				log.error(body);
				return null;
			}
			JSONObject jsonObj = JSONObject.fromObject(body);
			if (jsonObj.has("code")) { // code出现，例如：{"msg": "不支持' GET'请求","code": 500}
				log.error("数据蛇，获取kllqd，code错误：" + body);
				return null;
			}
			if (!jsonObj.has("errcode") || jsonObj.getInt("errcode") != 2000 || !jsonObj.has("result")) { // 状态不为2000，说明接口返回失败
				log.info("数据蛇，获取kllqd，errcode错误：" + body);
				return null;
			}

			result.setCodeContent(Result.SUCCESS, "成功");
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			return null;
		} finally {
			httpPost.releaseConnection();
		}
	}

	/**
	 * 卡流量渠道记录
	 * 
	 * @param zhSjs
	 * @param input
	 * @return
	 */
	@Retry
	public Result kllqdjl(ZhSjs zhSjs, KllqdInput input) {
		Result result = new Result(2, "失败");
		long timestamp = System.currentTimeMillis();
		String key = Md5.md5(timestamp + zhSjs.getUserId());
		String requestPostUrl = String.format(SjsConst.KLLQD_JL_URL, zhSjs.getUsername(), 1,
				input.getType().split(",").length, zhSjs.getUserId(), zhSjs.getToken(), timestamp, key);
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(zhSjs.getCookieStore()).build();
		HttpPost httpPost = new HttpPost(requestPostUrl);
		httpPost.setHeader("accept", "application/json, text/plain, */*");
		httpPost.setHeader("accept-encoding", "gzip, deflate, br");
		httpPost.setHeader("accept-language", "zh-CN,zh;q=0.9");
		httpPost.setHeader("content-type", "application/json;charset=UTF-8");
		httpPost.setHeader("origin", "https://www.shujushe.com");
		httpPost.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.75 Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000).setConnectionRequestTimeout(20000);
		httpPost.setConfig(requestConfig.build());
		try {
			String json = "{\"createname\":\"" + zhSjs.getUsername() + "\",\"pageNumber\": 1,\"pageSize\":"
					+ input.getType().split(",").length + ",\"userId\":" + zhSjs.getUserId() + ",\"token\":\""
					+ zhSjs.getToken() + "\",\"tm\":" + timestamp + "\",\"key\":\"" + key + "\"}";
			httpPost.setEntity(new StringEntity(json, "UTF-8"));

			HttpResponse httpResponse = httpClient.execute(httpPost);
			HttpEntity entity = httpResponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			if (StrUtil.isNull(body)) { // 空
				log.error("数据蛇，获取kllqdjl：返回的是空字符串");
				return null;
			}
			if (!StrUtil.isJson(body)) {
				log.error("数据蛇，获取kllqdjl：返回的不是json");
				log.error(body);
				return null;
			}
			JSONObject jsonObj = JSONObject.fromObject(body);
			if (jsonObj.has("code")) { // code出现，例如：{"msg": "不支持' GET'请求","code": 500}
				log.error("数据蛇，获取kllqdjl，code错误：" + body);
				return null;
			}
			if (!jsonObj.has("errcode") || jsonObj.getInt("errcode") != 2000 || !jsonObj.has("result")) { // 状态不为2000，说明接口返回失败
				log.info("数据蛇，获取kllqdjl，errcode错误：" + body);
				return null;
			}

			List<Kllqd> list = new ArrayList<>();
			JSONArray jsonArray = jsonObj.getJSONArray("result");
			for (int i = 0; i < jsonArray.size(); i++) {
				Kllqd kllqd = new Kllqd(jsonArray.getJSONObject(i));
				list.add(kllqd);
			}
			result.setCodeContent(Result.SUCCESS, "成功");
			result.putKey("list", JSON.toJSONString(list));
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			return null;
		} finally {
			httpPost.releaseConnection();
		}
	}

	/**
	 * 竞品规格销售比例保存
	 * 
	 * @param zhSjs
	 * @param bbid
	 * @param num   页码
	 * @param pid   记录id
	 * @param tm    时间戳
	 * @param keyMd 加密密钥
	 * @return
	 */
	@Retry(3)
	public Result save_jpggxsbl(ZhSjs zhSjs, String bbid, int num, String pid, String tm, String keyMd) {
		Result result = new Result(2, "失败");
		String requestPostUrl = String.format(SjsConst.JPGGXSBL_URL, zhSjs.getUsername(), bbid, num, pid,
				zhSjs.getToken(), zhSjs.getUserId(), tm, keyMd);
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(zhSjs.getCookieStore()).build();
		HttpPost httpPost = new HttpPost(requestPostUrl);
		httpPost.setHeader("accept", "application/json, text/plain, */*");
		httpPost.setHeader("accept-encoding", "gzip, deflate, br");
		httpPost.setHeader("accept-language", "zh-CN,zh;q=0.9");
		httpPost.setHeader("content-type", "application/json;charset=UTF-8");
		httpPost.setHeader("origin", "https://www.shujushe.com");
		httpPost.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.75 Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000).setConnectionRequestTimeout(20000);
		httpPost.setConfig(requestConfig.build());
		try {
			String json = "{\"createBy\":\"" + zhSjs.getUsername() + "\",\"nid\":\"" + bbid + "\",\"num\":\"" + num
					+ "\",\"pid\":\"" + pid + "\"}";
			httpPost.setEntity(new StringEntity(json, "UTF-8"));
			HttpResponse httpResponse = httpClient.execute(httpPost);
			HttpEntity entity = httpResponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			if (StrUtil.isNull(body)) { // 空
				log.error("数据蛇，获取save_jpggxsbl：返回的是空字符串");
				return null;
			}
			if (!StrUtil.isJson(body)) {
				log.error("数据蛇，获取save_jpggxsbl：返回的不是json");
				log.error(body);
				return null;
			}
			JSONObject jsonObj = JSONObject.fromObject(body);
			if (jsonObj.has("code")) { // code出现，例如：{"msg": "不支持' GET'请求","code": 500}
				log.error("数据蛇，获取save_jpggxsbl，code错误：" + body);
				return null;
			}
			if (!jsonObj.has("errcode") || jsonObj.getInt("errcode") != 2000 || !jsonObj.has("result")) { // 状态不为2000，说明接口返回失败
				log.info("数据蛇，获取save_jpggxsbl，errcode错误：" + body);
				return null;
			}

			result.setCodeContent(Result.SUCCESS, "成功");
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			return null;
		} finally {
			httpPost.releaseConnection();
		}
	}

	/**
	 * 查询竞品规格销售比例记录
	 * 
	 * @param zhSjs
	 * @param tm    时间戳
	 * @param keyMd 加密密钥
	 * @return
	 */
	@Retry
	public Result list_jpggxsbl(ZhSjs zhSjs, String tm, String key) {
		Result result = new Result(2, "失败");
		String requestPostUrl = String.format(SjsConst.JPGGXSBL_JL_URL, 1, 1, zhSjs.getUsername(), zhSjs.getToken(),
				zhSjs.getUserId(), tm, key);
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(zhSjs.getCookieStore()).build();
		HttpPost httpPost = new HttpPost(requestPostUrl);
		httpPost.setHeader("accept", "application/json, text/plain, */*");
		httpPost.setHeader("accept-encoding", "gzip, deflate, br");
		httpPost.setHeader("accept-language", "zh-CN,zh;q=0.9");
		httpPost.setHeader("content-type", "application/json;charset=UTF-8");
		httpPost.setHeader("origin", "https://www.shujushe.com");
		httpPost.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.75 Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000).setConnectionRequestTimeout(20000);
		httpPost.setConfig(requestConfig.build());
		try {
			String json = "{\"createBy\":\"" + zhSjs.getUsername() + "\",\"pageNumber\":\"" + 1 + "\",\"pageSize\":\""
					+ 1 + "\"}";
			httpPost.setEntity(new StringEntity(json, "UTF-8"));

			HttpResponse httpResponse = httpClient.execute(httpPost);
			HttpEntity entity = httpResponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			if (StrUtil.isNull(body)) { // 空
				log.error("数据蛇，获取list_jpggxsbl：返回的是空字符串");
				return null;
			}
			if (!StrUtil.isJson(body)) {
				log.error("数据蛇，获取list_jpggxsbl：返回的不是json");
				log.error(body);
				return null;
			}
			JSONObject jsonObj = JSONObject.fromObject(body);
			if (jsonObj.has("code")) { // code出现，例如：{"msg": "不支持' GET'请求","code": 500}
				log.error("数据蛇，获取list_jpggxsbl，code错误：" + body);
				return null;
			}
			if (!jsonObj.has("errcode") || jsonObj.getInt("errcode") != 2000 || !jsonObj.has("result")) { // 状态不为2000，说明接口返回失败
				log.info("数据蛇，获取list_jpggxsbl，errcode错误：" + body);
				return null;
			}

			JSONObject record = jsonObj.getJSONArray("result").getJSONObject(0);
			JpggxsblJl jl = new JpggxsblJl(record);
			result.setCodeContent(Result.SUCCESS, "成功");
			result.putKey("record", jl.toString());
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			return null;
		} finally {
			httpPost.releaseConnection();
		}
	}

	/**
	 * 竞品规格销售比例详情查询
	 * 
	 * @param zhSjs
	 * @param input 参数
	 * @param tm    时间戳
	 * @param keyMd 加密密钥
	 * @return
	 */
	@Retry
	public Result xq_jpggxsbl(ZhSjs zhSjs, JpggxsblInput input, String tm, String keyMd) {
		Result result = new Result(2, "失败");
		String requestPostUrl = String.format(SjsConst.JPGGXSBL_DATA_URL, input.getUuid(), input.getNum(),
				input.getNid(), zhSjs.getUsername(), zhSjs.getToken(), zhSjs.getUserId(), tm, keyMd);
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(zhSjs.getCookieStore()).build();
		HttpPost httpPost = new HttpPost(requestPostUrl);
		httpPost.setHeader("accept", "application/json, text/plain, */*");
		httpPost.setHeader("accept-encoding", "gzip, deflate, br");
		httpPost.setHeader("accept-language", "zh-CN,zh;q=0.9");
		httpPost.setHeader("content-type", "application/json;charset=UTF-8");
		httpPost.setHeader("origin", "https://www.shujushe.com");
		httpPost.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.75 Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000).setConnectionRequestTimeout(20000);
		httpPost.setConfig(requestConfig.build());
		try {
			String json = "{\"createBy\":\"" + zhSjs.getUsername() + "\",\"num\":\"" + input.getNum() + "\",\"uuid\":\""
					+ input.getUuid() + "\",\"nid\":\"" + input.getNid() + "\"}";
			httpPost.setEntity(new StringEntity(json, "UTF-8"));

			HttpResponse httpResponse = httpClient.execute(httpPost);
			HttpEntity entity = httpResponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			if (StrUtil.isNull(body)) { // 空
				log.error("数据蛇，获取xq_jpggxsbl：返回的是空字符串");
				return null;
			}
			if (!StrUtil.isJson(body)) {
				log.error("数据蛇，获取xq_jpggxsbl：返回的不是json");
				log.error(body);
				return null;
			}
			JSONObject jsonObj = JSONObject.fromObject(body);
			if (jsonObj.has("code")) { // code出现，例如：{"msg": "不支持' GET'请求","code": 500}
				log.error("数据蛇，获取xq_jpggxsbl，code错误：" + body);
				return null;
			}
			if (!jsonObj.has("errcode") || jsonObj.getInt("errcode") != 2000 || !jsonObj.has("result")) { // 状态不为2000，说明接口返回失败
				log.info("数据蛇，获取xq_jpggxsbl，errcode错误：" + body);
				return null;
			}

			List<JpggxsblJlData> list = new ArrayList<>();
			JSONArray records = jsonObj.getJSONArray("result");
			for (int i = 0; i < records.size(); i++) {
				JpggxsblJlData data = new JpggxsblJlData(records.getJSONObject(i));
				list.add(data);
			}
			result.setCodeContent(Result.SUCCESS, "成功");
			result.putKey("records", JSONArray.fromObject(list));
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			return null;
		} finally {
			httpPost.releaseConnection();
		}
	}

	/**
	 * 买家秀印象
	 * 
	 * @param zhSjs
	 * @param bbid  宝贝id
	 * @param tm    时间戳
	 * @param keyMd 加密密钥
	 * @return
	 */
	public Result mjxyx(ZhSjs zhSjs, String bbid, String tm, String key) {
		Result result = new Result(2, "失败");
		String requestPostUrl = String.format(SjsConst.MJX_YX_URL, bbid, zhSjs.getToken(), zhSjs.getUserId(), tm, key);
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(zhSjs.getCookieStore()).build();
		HttpPost httpPost = new HttpPost(requestPostUrl);
		httpPost.setHeader("accept", "application/json, text/plain, */*");
		httpPost.setHeader("accept-encoding", "gzip, deflate, br");
		httpPost.setHeader("accept-language", "zh-CN,zh;q=0.9");
		httpPost.setHeader("content-type", "application/json;charset=UTF-8");
		httpPost.setHeader("origin", "https://www.shujushe.com");
		httpPost.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.75 Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000).setConnectionRequestTimeout(20000);
		httpPost.setConfig(requestConfig.build());
		try {
			String json = "{\"nid\":\"" + bbid + "\"}";
			httpPost.setEntity(new StringEntity(json, "UTF-8"));

			HttpResponse httpResponse = httpClient.execute(httpPost);
			HttpEntity entity = httpResponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			if (StrUtil.isNull(body)) { // 空
				log.error("数据蛇，获取mjxyx：返回的是空字符串");
				return null;
			}
			if (!StrUtil.isJson(body)) {
				log.error("数据蛇，获取mjxyx：返回的不是json");
				log.error(body);
				return null;
			}
			JSONObject jsonObj = JSONObject.fromObject(body);
			if (jsonObj.has("code")) { // code出现，例如：{"msg": "不支持' GET'请求","code": 500}
				log.error("数据蛇，获取mjxyx，code错误：" + body);
				return null;
			}
			if (!jsonObj.has("errcode") || jsonObj.getInt("errcode") != 2000 || !jsonObj.has("result")) { // 状态不为2000，说明接口返回失败
				log.info("数据蛇，获取mjxyx，errcode错误：" + body);
				return null;
			}

			List<Mjxyx> list1 = new ArrayList<>();
			List<Mjxyx> list2 = new ArrayList<>();
			jsonObj = jsonObj.getJSONObject("result");

			JSONArray rateArray = jsonObj.getJSONArray("rate");
			for (int i = 0; i < rateArray.size(); i++) {
				Mjxyx data = new Mjxyx(rateArray.getJSONObject(i), 1);
				list1.add(data);
			}

			JSONArray skuArray = jsonObj.getJSONArray("skuBase");
			for (int i = 0; i < skuArray.size(); i++) {
				Mjxyx data = new Mjxyx(skuArray.getJSONObject(i), 2);
				list2.add(data);
			}

			result.setCodeContent(Result.SUCCESS, "成功");
			result.putKey("word", JSONArray.fromObject(list1));
			result.putKey("sku", JSONArray.fromObject(list2));
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			return null;
		} finally {
			httpPost.releaseConnection();
		}
	}

	/**
	 * 买家秀
	 * 
	 * @param zhSjs
	 * @param input
	 * @param tm    时间戳
	 * @param keyMd 加密密钥
	 * @return
	 */
	public Result mjx(ZhSjs zhSjs, MjxInput input, String tm, String key) {
		Result result = new Result(2, "失败");
		String requestPostUrl = String.format(SjsConst.MJX_URL, zhSjs.getUsername(), input.getUrl(), input.getNum(),
				input.getSort(), input.getType(), input.getSku(), input.getExpre(), input.getPid(),
				input.getSkuCnUrlEncode(), input.getTypeCnUrlEncode(), input.getSortCnUrlEncode(),
				input.getExpreCnUrlEncode(), zhSjs.getToken(), zhSjs.getUserId(), tm, key);
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(zhSjs.getCookieStore()).build();
		HttpPost httpPost = new HttpPost(requestPostUrl);
		httpPost.setHeader("accept", "application/json, text/plain, */*");
		httpPost.setHeader("accept-encoding", "gzip, deflate, br");
		httpPost.setHeader("accept-language", "zh-CN,zh;q=0.9");
		httpPost.setHeader("content-type", "application/json;charset=UTF-8");
		httpPost.setHeader("origin", "https://www.shujushe.com");
		httpPost.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.75 Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000).setConnectionRequestTimeout(20000);
		httpPost.setConfig(requestConfig.build());
		try {
			String json = "{\"createBy\":\"" + zhSjs.getUsername() + "\",\"url\":\"" + input.getUrl() + "\",\"num\":\""
					+ input.getNum() + "\",\"sort\":\"" + input.getSort() + "\",\"type\":\"" + input.getType()
					+ "\",\"sku\":\"" + input.getSku() + "\",\"expre\":\"" + input.getExpre() + "\",\"skuCn\":\""
					+ input.getSkuCn() + "\",\"typeCn\":\"" + input.getTypeCn() + "\",\"sortCn\":\"" + input.getSortCn()
					+ "\",\"expreCn\":\"" + input.getExpreCn() + "\",\"pid\":\"" + input.getPid() + "\"}";

			httpPost.setEntity(new StringEntity(json, "UTF-8"));

			HttpResponse httpResponse = httpClient.execute(httpPost);
			HttpEntity entity = httpResponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			if (StrUtil.isNull(body)) { // 空
				log.error("数据蛇，获取mjx：返回的是空字符串");
				return null;
			}
			if (!StrUtil.isJson(body)) {
				log.error("数据蛇，获取mjx：返回的不是json");
				log.error(body);
				return null;
			}
			JSONObject jsonObj = JSONObject.fromObject(body);
			if (jsonObj.has("code")) { // code出现，例如：{"msg": "不支持' GET'请求","code": 500}
				log.error("数据蛇，获取mjx，code错误：" + body);
				return null;
			}
			if (!jsonObj.has("errcode") || jsonObj.getInt("errcode") != 2000 || !jsonObj.has("result")) { // 状态不为2000，说明接口返回失败
				log.info("数据蛇，获取mjx，errcode错误：" + body);
				return null;
			}
			JSONArray jsonArray = jsonObj.getJSONArray("result");
			if (jsonArray != null && jsonArray.size() > 0) {
				Mjx mjx = new Mjx();
				List<MjxTp> list = new ArrayList<>();
				for (int i = 0; i < jsonArray.size(); i++) {
					JSONObject jsonObject = jsonArray.getJSONObject(i);
					MjxTp tp = new MjxTp(jsonObject);
					list.add(tp);

					if (mjx.getBaseinfo() == null && StringUtils.isNotBlank(jsonObject.getString("feedAllCount"))) {
						MjxBaseInfo baseinfo = new MjxBaseInfo(jsonObject);
						mjx.setBaseinfo(baseinfo);
					}
				}

				if (mjx.getBaseinfo() == null) {
					mjx.setBaseinfo(new MjxBaseInfo());
				}
				mjx.setList(list);
				result.setCodeContent(Result.SUCCESS, "成功");
				result.putKey("result", mjx);
				return result;
			}
			result.setCodeContent(Result.SUCCESS, "成功");
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			return null;
		} finally {
			httpPost.releaseConnection();
		}
	}

	/**
	 * 买家秀记录
	 * 
	 * @param zhSjs
	 * @param input
	 * @param tm    时间戳
	 * @param keyMd 加密密钥
	 * @return
	 */
	public Result mjxJl(ZhSjs zhSjs, String tm, String key) {
		Result result = new Result(2, "失败");
		String requestPostUrl = String.format(SjsConst.MJX_JL_URL, zhSjs.getUsername(), 1, 1, zhSjs.getToken(), zhSjs.getUserId(), tm, key);
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(zhSjs.getCookieStore()).build();
		HttpPost httpPost = new HttpPost(requestPostUrl);
		httpPost.setHeader("accept", "application/json, text/plain, */*");
		httpPost.setHeader("accept-encoding", "gzip, deflate, br");
		httpPost.setHeader("accept-language", "zh-CN,zh;q=0.9");
		httpPost.setHeader("content-type", "application/json;charset=UTF-8");
		httpPost.setHeader("origin", "https://www.shujushe.com");
		httpPost.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.75 Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000).setConnectionRequestTimeout(20000);
		httpPost.setConfig(requestConfig.build());
		try {
			String json = "{\"userName\":\"" + zhSjs.getUsername() + "\",\"pageNumber\":\"" + 1 + "\",\"pageSize\":\""
					+ 1 + "\",\"ptype\":\"" + 2 + "\"}";

			httpPost.setEntity(new StringEntity(json, "UTF-8"));

			HttpResponse httpResponse = httpClient.execute(httpPost);
			HttpEntity entity = httpResponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			if (StrUtil.isNull(body)) { // 空
				log.error("数据蛇，获取mjxJl：返回的是空字符串");
				return null;
			}
			if (!StrUtil.isJson(body)) {
				log.error("数据蛇，获取mjxJl：返回的不是json");
				log.error(body);
				return null;
			}
			JSONObject jsonObj = JSONObject.fromObject(body);
			if (jsonObj.has("code")) { // code出现，例如：{"msg": "不支持' GET'请求","code": 500}
				log.error("数据蛇，获取mjxJl，code错误：" + body);
				return null;
			}
			if (!jsonObj.has("errcode") || jsonObj.getInt("errcode") != 2000 || !jsonObj.has("result")) { // 状态不为2000，说明接口返回失败
				log.info("数据蛇，获取mjxJl，errcode错误：" + body);
				return null;
			}
			result.setCodeContent(Result.SUCCESS, "成功");
			JSONArray jsonArray = jsonObj.getJSONArray("result");
			if (jsonArray != null && jsonArray.size() > 0) {
				JSONObject jsonObject = jsonArray.getJSONObject(0);
				String pid = jsonObject.getString("id");
				result.putKey("pid", pid);
				return result;
			}
			result.setCodeContent(Result.SUCCESS, "成功");
			result.putKey("pid", 0);
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			return null;
		} finally {
			httpPost.releaseConnection();
		}
	}

	/**
	 * 买家秀确定下载
	 * 
	 * @param zhSjs
	 * @param input
	 * @param tm    时间戳
	 * @param keyMd 加密密钥
	 * @return
	 */
	public Result mjxqdxz(ZhSjs zhSjs, MjxQdxzInput input, String tm, String keyMd) {
		Result result = new Result(2, "失败");
		String requestPostUrl = String.format(SjsConst.MJX_URL, zhSjs.getUsername(), input.getUrl(), 1, input.getSort(),
				input.getType(), input.getSku(), input.getExpre(), "", input.getSkuCnUrlEncode(),
				input.getTypeCnUrlEncode(), input.getSortCnUrlEncode(), input.getExpreCnUrlEncode(), zhSjs.getToken(),
				zhSjs.getUserId(), tm, keyMd);
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(zhSjs.getCookieStore()).build();
		HttpPost httpPost = new HttpPost(requestPostUrl);
		httpPost.setHeader("accept", "application/json, text/plain, */*");
		httpPost.setHeader("accept-encoding", "gzip, deflate, br");
		httpPost.setHeader("accept-language", "zh-CN,zh;q=0.9");
		httpPost.setHeader("content-type", "application/json;charset=UTF-8");
		httpPost.setHeader("origin", "https://www.shujushe.com");
		httpPost.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.75 Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000).setConnectionRequestTimeout(20000);
		httpPost.setConfig(requestConfig.build());
		try {
			String json = "{\"createBy\":\"" + zhSjs.getUsername() + "\",\"url\":\"" + input.getUrl() + "\",\"num\":\""
					+ 1 + "\",\"sort\":\"" + input.getSort() + "\",\"type\":\"" + input.getType() + "\",\"sku\":\""
					+ input.getSku() + "\",\"expre\":\"" + input.getExpre() + "\",\"skuCn\":\"" + input.getSkuCn()
					+ "\",\"typeCn\":\"" + input.getTypeCn() + "\",\"sortCn\":\"" + input.getSortCn()
					+ "\",\"expreCn\":\"" + input.getExpreCn() + "\",\"pid\":\"" + "" + "\"}";

			httpPost.setEntity(new StringEntity(json, "UTF-8"));

			HttpResponse httpResponse = httpClient.execute(httpPost);
			HttpEntity entity = httpResponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			if (StrUtil.isNull(body)) { // 空
				log.error("数据蛇，获取mjx：返回的是空字符串");
				return null;
			}
			if (!StrUtil.isJson(body)) {
				log.error("数据蛇，获取mjx：返回的不是json");
				log.error(body);
				return null;
			}
			JSONObject jsonObj = JSONObject.fromObject(body);
			if (jsonObj.has("code")) { // code出现，例如：{"msg": "不支持' GET'请求","code": 500}
				log.error("数据蛇，获取mjx，code错误：" + body);
				return null;
			}
			if (!jsonObj.has("errcode") || jsonObj.getInt("errcode") != 2000 || !jsonObj.has("result")) { // 状态不为2000，说明接口返回失败
				log.info("数据蛇，获取mjx，errcode错误：" + body);
				return null;
			}
			result.setCodeContent(Result.SUCCESS, "成功");
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			return null;
		} finally {
			httpPost.releaseConnection();
		}
	}
	
	/**
	 * 竞品透视：最近30天，生成数据链接，扣费链接，为避免连续扣费，不能尝试3次，https://www.shujushe.com/Navigation?hkj=3
	 * 
	 * @param zhSjs
	 * @return result，(其中code：0.数据生成成功，但没有id、1.，但没有id、2.数据生成失败)
	 * @forexample https://gwhd.shujushe.com/shujushe/dpsjVue/cjp1124?nid=600019477530&createBy=18856038967&type=1&datatpye=day&device=2&tjrq=2021-02-19&tjrq2=2021-02-19&starttime=2021-02-19&endtime=2021-02-19&token=2856609QC7ODU5NTE%3D23W&userId=85951&tm=1613792199000&key=1835bb69386c04f1b1379b3e97dc9567
	 */
	public Result jptsSave(ZhSjs zhSjs, String bbid, String currentTime, String key) {
		Result result = new Result(2, "数据生成失败");
		long nowtime = System.currentTimeMillis();
		String tjrq = DateUtil.formatDate(new Date(nowtime - 24 * 60 * 60 * 1000L)); // 昨天
		String url = String.format(SjsConst.JPTS_URL, bbid, zhSjs.getUsername(), tjrq, tjrq, tjrq, tjrq, zhSjs.getToken().replace("=", "%3D"), zhSjs.getUserId(), currentTime, key);
		
		// String json = this.getTaobaoDetail2(bbid);
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(zhSjs.getCookieStore()).build();
		HttpPost httpPost = new HttpPost(url);
		httpPost.setHeader("accept", "application/json, text/plain, */*");
		httpPost.setHeader("accept-encoding", "gzip, deflate, br");
		httpPost.setHeader("accept-language", "zh-CN,zh;q=0.9");
		httpPost.setHeader("content-type", "application/json;charset=UTF-8");
		httpPost.setHeader("origin", "https://www.shujushe.com");
		httpPost.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.75 Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(60000).setConnectTimeout(60000).setConnectionRequestTimeout(60000);
		httpPost.setConfig(requestConfig.build());
		try {
			StringEntity stringEntity = null;
//			if (StrUtil.isNull(json)) {
//				stringEntity = new StringEntity("{\"json\": \"\"}", "utf-8"); // 这里是关键,后面要跟上字符集格式
//			} else {
//				stringEntity = new StringEntity("{\"json\": " + json + "}", "utf-8"); // 这里是关键,后面要跟上字符集格式
//			}
			stringEntity = new StringEntity("{\"json\": \"\", \"jpdata\": \"\"}", "utf-8");
			
			stringEntity.setContentEncoding("UTF-8");
			stringEntity.setContentType("application/json"); // 发送json数据需要设置contentType
	        httpPost.setEntity(stringEntity);
			HttpResponse httpResponse = httpClient.execute(httpPost);
			HttpEntity entity = httpResponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			if (StrUtil.isNull(body)) { // 空
				log.error("数据蛇，获取jptsSave：返回的是空字符串");
				return null;
			}
			if (!StrUtil.isJson(body)) {
				log.error("数据蛇，获取jptsSave：返回的不是json");
				log.error(body);
				return null;
			}
			JSONObject jsonObj = JSONObject.fromObject(body);
			if (jsonObj.has("code")) { // code出现，例如：{"msg": "不支持' GET'请求","code": 500}
				log.error("数据蛇，获取jptsSave，code错误：" + body);
				return result;
			}
			if (!jsonObj.has("errcode") || jsonObj.getInt("errcode") != 2000 || !jsonObj.has("result") || jsonObj.getJSONObject("result").isNullObject()) { // 状态不为2000，说明接口返回失败
				log.info("数据蛇，获取jptsSave，errcode错误：" + body);
				return result;
			}
			JSONObject resultObj = jsonObj.getJSONObject("result");
			result.setCodeContent(Result.SUCCESS, "success");
			result.putKey("catename", resultObj.getString("leimu"));
			result.putKey("title", resultObj.getString("title"));
			result.putKey("images", resultObj.getString("images"));
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		} finally {
			httpPost.releaseConnection();
		}
		return null;
	}
	

	/**
	 * 竞品透视，最近30天数据，https://www.shujushe.com/Navigation?hkj=3
	 * 
	 * @param bbid 宝贝id
	 * @return result，(其中code：0.获取id，且status==2，可直接下载excel、1.获取id，且status!=2，需要等待、2.失败、9.超过最大尝试次数)
	 */
	public Result jptsList(ZhSjs zhSjs, String bbid, String currentTime, String key) {
		Result result = null;
		int time = 0; // 尝试次数
		int nulltime = 0; // 空次数
		
		String currentTime2 = String.valueOf(System.currentTimeMillis()); // 毫秒
		String key2 = Md5.md5(currentTime2 + zhSjs.getUserId());
		do {
			result = this.jptsList2(zhSjs, bbid, currentTime, key, currentTime2, key2);
			if (result == null) {
				nulltime++;
			}
			if (result != null && result.isSuccess()) {
				break; // 结束循环
			}
			time++;
			try {
				// Thread.sleep(((time >= SjsConst.MAX_TRY_NUM2) ? 0 : time) * 3000L); // 最多请求5次，等待4次，最多等待30秒（3 + 6 + 9 + 12）
				Thread.sleep(((time >= SjsConst.MAX_TRY_NUM2) ? 0 : 1) * 5000L); // 最多请求20次，等待19次，最多等待95秒（19 * 5）
			} catch (Exception e) {
				e.printStackTrace();
			}
		} while (time < SjsConst.MAX_TRY_NUM2 && nulltime < SjsConst.NULL_TRY_NUM);
		if (result == null) {
			result = new Result(2, "3次接口返回null。");
			log.info("获取superJptsBbxx，尝试次数：" + time + "，返回null次数：" + nulltime);
		} else if (time >= SjsConst.MAX_TRY_NUM2 && result.getCode() == 1) {
			result.setCodeContent(9, "超过最大尝试");
			log.info("获取superJptsBbxx，超过最大尝试次数：" + time + "，返回null次数：" + nulltime);
		}
		return result;
	}

	/**
	 * 竞品透视，最近30天数据，https://www.shujushe.com/Navigation?hkj=3
	 * 
	 * @param zhSjs 账号信息
	 * @param bbid  宝贝id
	 * @return result，(其中code：0.获取id，且status==2，可直接下载excel、1.获取id，且status!=2，需要等待、2.失败、9.超过最大尝试次数)
	 * @forexample https://gwhd.shujushe.com/shujushe/sycmNew/list?nid=600019477530&firstData=1&token=2856609QC7ODU5NTE%3D23W&userId=85951&tm=1613792199000&key=1835bb69386c04f1b1379b3e97dc9567
	 */
	public Result jptsList2(ZhSjs zhSjs, String bbid, String currentTime, String key, String currentTime2, String key2) {
		Result result = new Result(2, "失败");
		String url = String.format(SjsConst.JPTS_DATA_URL, bbid, zhSjs.getToken().replace("=", "%3D"), zhSjs.getUserId(), currentTime, key);
		
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(zhSjs.getCookieStore()).build(); // 不需要cookie也可以
		HttpPost httpPost = new HttpPost(url);
		httpPost.setHeader("accept", "application/json, text/plain, */*");
		httpPost.setHeader("accept-encoding", "gzip, deflate, br");
		httpPost.setHeader("accept-language", "zh-CN,zh;q=0.9");
		httpPost.setHeader("content-type", "application/json;charset=UTF-8");
		httpPost.setHeader("origin", "https://www.shujushe.com");
		httpPost.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000).setConnectionRequestTimeout(20000);
		httpPost.setConfig(requestConfig.build());
		try {
//			StringEntity stringEntity = new StringEntity("{\"nid\": \"" + bbid + "\", \"firstData\": 1, \"token\": \"" + zhSjs.getToken() + 
//					"\", \"userId\": \"" + zhSjs.getUserId() + "\", \"tm\": " + currentTime + ", \"key\": \"" + key + "\"}", "utf-8"); // 这里是关键,后面要跟上字符集格式
			StringEntity stringEntity = new StringEntity("{\"nid\": \"" + bbid + "\", \"firstData\": 1, \"token\": \"" + zhSjs.getToken() + 
					"\", \"userId\": \"" + zhSjs.getUserId() + "\", \"tm\": " + currentTime2 + ", \"key\": \"" + key2 + "\"}", "utf-8"); // tm和key变化了
			stringEntity.setContentEncoding("UTF-8");
			stringEntity.setContentType("application/json"); // 发送json数据需要设置contentType
	        httpPost.setEntity(stringEntity);

			HttpResponse httpResponse = httpClient.execute(httpPost);
			HttpEntity entity = httpResponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			if (StrUtil.isNull(body)) { // 空
				log.error("数据蛇，获取jptsList2：返回的是空字符串");
				return null;
			}
			if (!StrUtil.isJson(body)) {
				log.error("数据蛇，获取jptsList2：返回的不是json");
				log.error(body);
				return null;
			}
			JSONObject jsonObj = JSONObject.fromObject(body);
			if (jsonObj.has("code")) { // code出现，例如：{"msg": "不支持' GET'请求","code": 500}
				log.error("数据蛇，获取jptsList2，code错误：" + body);
				return null;
			}
			if (!jsonObj.has("errcode") || jsonObj.getInt("errcode") != 2000 || !jsonObj.has("result")) { // 状态不为2000，说明接口返回失败
				log.info("数据蛇，获取jptsList2，errcode错误：" + body);
				return null;
			}
			JSONArray resultArr = jsonObj.getJSONArray("result");
			if (resultArr == null || resultArr.size() < 1) {
				return null;
			}
			List<JptsBbxx2> list = new ArrayList<JptsBbxx2>();
			for (int i = 0; i < resultArr.size(); i++) {
				JSONObject obj = (JSONObject) resultArr.get(i);
//				if (!obj.has("nid") || StrUtil.isNull(obj.getString("nid"))) {
//					continue;
//				}
				JptsBbxx2 jptsBbxx2 = new JptsBbxx2(obj);
				list.add(jptsBbxx2);
			}
			result.setCodeContent(Result.SUCCESS, "success");
			result.putKey("list", list);
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			return null;
		} finally {
			httpPost.releaseConnection();
		}
	}
	
	/**
	 * History竞品透视，历史数据接口，https://www.shujushe.com/Navigation/3/
	 * 
	 * @param zhSjs 账号信息
	 * @forexample https://gwhd.shujushe.com/shujushe/dpsjVue/dpsjList?createBy=17318541425&pageNumber=1&pageSize=10&type=1&token=270207YB96MzQ5MDA%3D2AA&userId=34900&fromTo=www.shujushe.com&tm=1638512883000&key=535991d995f177b4a10ef2b23956c104
	 */
	@Retry
	public Result jptsHistoryList(ZhSjs zhSjs, @Times int time) {
		if (time > 1) {
			try {
				Thread.sleep(time * 1800L);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		Result result = new Result(2, "失败");
		String tm = String.valueOf(System.currentTimeMillis()); // 毫秒
		String key = md5SjsJSEngine.getKey(tm, zhSjs.getUserId());
		String key_payload = Md5.md5(tm + zhSjs.getUserId());
		int pageSize = 100;
		String url = String.format(SjsConst.JPTS_LSJL_URL, zhSjs.getUsername(), pageSize, zhSjs.getToken().replace("=", "%3D"), zhSjs.getUserId(), tm, key);
		
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(zhSjs.getCookieStore()).build(); // 不需要cookie也可以
		HttpPost httpPost = new HttpPost(url);
		httpPost.setHeader("accept", "application/json, text/plain, */*");
		httpPost.setHeader("accept-encoding", "gzip, deflate, br");
		httpPost.setHeader("accept-language", "zh-CN,zh;q=0.9");
		httpPost.setHeader("content-type", "application/json;charset=UTF-8");
		httpPost.setHeader("origin", "https://www.shujushe.com");
		httpPost.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000).setConnectionRequestTimeout(20000);
		httpPost.setConfig(requestConfig.build());
		try {
			StringEntity stringEntity = new StringEntity("{\"createBy\": \"" + zhSjs.getUsername() + "\", \"userId\": \"" + zhSjs.getUserId() + "\", \"token\": \"" + 
					zhSjs.getToken() + "\", \"type\": 1, \"tm\": " + tm + ", \"key\": \"" + key_payload + "\", \"pageNumber\": 1, \"pageSize\": " + pageSize + "}", "utf-8"); // tm和key变化了
			stringEntity.setContentEncoding("UTF-8");
			stringEntity.setContentType("application/json"); // 发送json数据需要设置contentType
			httpPost.setEntity(stringEntity);
			HttpResponse httpResponse = httpClient.execute(httpPost);
			HttpEntity entity = httpResponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			if (StrUtil.isNull(body)) { // 空
				log.error("数据蛇，获取jptsHistoryList：返回的是空字符串");
				return null;
			}
			if (!StrUtil.isJson(body)) {
				log.error("数据蛇，获取jptsHistoryList：返回的不是json");
				log.error(body);
				return null;
			}
			com.alibaba.fastjson.JSONObject jsonObj = com.alibaba.fastjson.JSONObject.parseObject(body);
			if (jsonObj.containsKey("code")) { // code出现，例如：{"msg": "不支持' GET'请求","code": 500}
				log.error("数据蛇，获取jptsHistoryList，code错误：" + body);
				return null;
			}
			if (!jsonObj.containsKey("errcode") || jsonObj.getIntValue("errcode") != 2000 || !jsonObj.containsKey("result")) { // 状态不为2000，说明接口返回失败
				log.info("数据蛇，获取jptsHistoryList，errcode错误：" + body);
				return null;
			}
			com.alibaba.fastjson.JSONArray resultArr = jsonObj.getJSONArray("result");
			if (resultArr == null || resultArr.size() < 1) {
				return result;
			}
			List<JptsBbxx3> list = new ArrayList<JptsBbxx3>();
			for (int i = 0; i < resultArr.size(); i++) {
				com.alibaba.fastjson.JSONObject obj = (com.alibaba.fastjson.JSONObject) resultArr.get(i);
				JptsBbxx3 jptsBbxx3 = new JptsBbxx3(obj);
				list.add(jptsBbxx3);
			}
			result.setCodeContent(Result.SUCCESS, "success");
			result.putKey("list", list);
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		} finally {
			httpPost.releaseConnection();
		}
		return null;
	}
	
	/**
	 * History竞品透视，确认历史数据是否存在接口，https://www.shujushe.com/Navigation/3/
	 * 
	 * @param zhSjs 账号信息
	 * @forexample https://gwhd.shujushe.com/shujushe/dpsjVue/loadData?uuid=093031deb2fb4885802f9b63ffd64764&createBy=17318541425&num=5&token=270207YB96MzQ5MDA%3D2AA&userId=34900&fromTo=www.shujushe.com&tm=1638514803000&key=65f749ff9c6df4ab0576425ed0334f80
	 */
	@Retry
	public Result jptsHistoryData(ZhSjs zhSjs, String uuid, @Times int time) {
		if (time > 1) {
			try {
				Thread.sleep(time * 1800L);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		Result result = new Result(2, "失败");
		String tm = String.valueOf(System.currentTimeMillis()); // 毫秒
		String key = md5SjsJSEngine.getKey(tm, zhSjs.getUserId());
		String url = String.format(SjsConst.JPTS_LOADDATA_URL, uuid, zhSjs.getUsername(), zhSjs.getToken().replace("=", "%3D"), zhSjs.getUserId(), tm, key);
		
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(zhSjs.getCookieStore()).build(); // 不需要cookie也可以
		HttpPost httpPost = new HttpPost(url);
		httpPost.setHeader("accept", "application/json, text/plain, */*");
		httpPost.setHeader("accept-encoding", "gzip, deflate, br");
		httpPost.setHeader("accept-language", "zh-CN,zh;q=0.9");
		httpPost.setHeader("content-type", "application/json;charset=UTF-8");
		httpPost.setHeader("origin", "https://www.shujushe.com");
		httpPost.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000).setConnectionRequestTimeout(20000);
		httpPost.setConfig(requestConfig.build());
		try {
			StringEntity stringEntity = new StringEntity("{\"uuid\": \"" + uuid + "\", \"createBy\": \"" + zhSjs.getUsername() + "\", \"num\": 5}", "utf-8"); // tm和key变化了
			stringEntity.setContentEncoding("UTF-8");
			stringEntity.setContentType("application/json"); // 发送json数据需要设置contentType
			httpPost.setEntity(stringEntity);
			HttpResponse httpResponse = httpClient.execute(httpPost);
			HttpEntity entity = httpResponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			if (StrUtil.isNull(body)) { // 空
				log.error("数据蛇，获取jptsHistoryData：返回的是空字符串");
				return null;
			}
			if (!StrUtil.isJson(body)) {
				log.error("数据蛇，获取jptsHistoryData：返回的不是json");
				log.error(body);
				return null;
			}
			com.alibaba.fastjson.JSONObject jsonObj = com.alibaba.fastjson.JSONObject.parseObject(body);
			if (jsonObj.containsKey("code")) { // code出现，例如：{"msg": "不支持' GET'请求","code": 500}
				log.error("数据蛇，获取jptsHistoryData，code错误：" + body);
				return null;
			}
			if (!jsonObj.containsKey("errcode") || jsonObj.getIntValue("errcode") != 2000 || !jsonObj.containsKey("result")) { // 状态不为2000，说明接口返回失败
				log.info("数据蛇，获取jptsHistoryData，errcode错误：" + body);
				return null;
			}
			result.setCodeContent(Result.SUCCESS, "success");
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		} finally {
			httpPost.releaseConnection();
		}
		return null;
	}
	
	/**
	 * wwdb旺旺打标，https://www.shujushe.com/Navigation/2
	 * 
	 * @param zhSjs 账号信息
	 * @param nid 宝贝id
	 * @param title 标题
	 * @param nick 旺旺名称s(可多个)，例如：读书因为爱你,ji8李龙
	 * @forexample https://gwhd.shujushe.com/shujushe/wwdbksp/save?
	 */
	@Retry
	public Result wwdb(ZhSjs zhSjs, String nid, String title, String nick, @Times int time) {
		if (time > 1) {
			try {
				Thread.sleep(time * 1800L);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		Result result = new Result(2, "失败");
		String tm = String.valueOf(System.currentTimeMillis()); // 毫秒
		String key = md5SjsJSEngine.getKey(tm, zhSjs.getUserId());
		String url = String.format(SjsConst.WWDB_URL, title, nid, nick, zhSjs.getToken().replace("=", "%3D"), zhSjs.getUserId(), tm, key);
		try {
			url = String.format(SjsConst.WWDB_URL, URLEncoder.encode(title, "UTF-8"), nid, URLEncoder.encode(nick, "UTF-8").replaceAll("%2C", ","), zhSjs.getToken().replace("=", "%3D"), zhSjs.getUserId(), tm, key);
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		}
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(zhSjs.getCookieStore()).build(); // 不需要cookie也可以
		HttpPost httpPost = new HttpPost(url);
		httpPost.setHeader("accept", "application/json, text/plain, */*");
		httpPost.setHeader("accept-encoding", "gzip, deflate, br");
		httpPost.setHeader("accept-language", "zh-CN,zh;q=0.9");
		httpPost.setHeader("content-type", "application/json;charset=UTF-8");
		httpPost.setHeader("origin", "https://www.shujushe.com");
		httpPost.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000).setConnectionRequestTimeout(20000);
		httpPost.setConfig(requestConfig.build());
		try {
			StringEntity stringEntity = new StringEntity("[]", "utf-8");
			stringEntity.setContentEncoding("UTF-8");
			stringEntity.setContentType("application/json"); // 发送json数据需要设置contentType
			httpPost.setEntity(stringEntity);
			HttpResponse httpResponse = httpClient.execute(httpPost);
			HttpEntity entity = httpResponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			if (StrUtil.isNull(body)) { // 空
				log.error("数据蛇，获取wwdb：返回的是空字符串");
				return null;
			}
			if (!StrUtil.isJson(body)) {
				log.error("数据蛇，获取wwdb：返回的不是json");
				log.error(body);
				return null;
			}
			com.alibaba.fastjson.JSONObject jsonObj = com.alibaba.fastjson.JSONObject.parseObject(body);
			if (jsonObj.containsKey("code")) { // code出现，例如：{"msg": "不支持' GET'请求","code": 500}
				log.error("数据蛇，获取wwdb，code错误：" + body);
				return null;
			}
			if (!jsonObj.containsKey("errcode") || jsonObj.getIntValue("errcode") != 2000 || !jsonObj.containsKey("result")) { // 状态不为2000，说明接口返回失败
				log.info("数据蛇，获取wwdb，errcode错误：" + body);
				return null;
			}
			result.setCodeContent(Result.SUCCESS, "success");
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		} finally {
			httpPost.releaseConnection();
		}
		return null;
	}
	
}
