package com.abujlb.zdcj8.bean;

public class DptsGjc implements java.lang.Comparable<DptsGjc> {
	private String gjc;
	private int uv;
	private int zfmjs;
	private double zfzhl;

	public DptsGjc(String gjc) {
		this.gjc = gjc;
	}

	public DptsGjc copy() {
		DptsGjc g = new DptsGjc(gjc);
		g.gjc = gjc;
		g.uv = uv;
		g.zfmjs = zfmjs;
		g.zfzhl = zfzhl;
		return g;
	}

	public void add(DptsGjc g) {
		this.uv = this.uv + g.uv;
		this.zfmjs = this.zfmjs + g.zfmjs;
		jszfzhl();
	}

	@Override
	public int compareTo(DptsGjc o) {
		return Integer.compare(o.uv, uv);
	}

	public String getGjc() {
		return gjc;
	}

	public void setGjc(String gjc) {
		this.gjc = gjc;
	}

	public int getUv() {
		return uv;
	}

	public void setUv(int uv) {
		this.uv = uv;
		jszfzhl();
	}

	public int getZfmjs() {
		return zfmjs;
	}

	public void setZfmjs(int zfmjs) {
		this.zfmjs = zfmjs;
		if (this.uv == 0) {
			this.uv = this.zfmjs;
		}
		jszfzhl();
	}

	public double getZfzhl() {
		return zfzhl;
	}

	public void jszfzhl() {
		if (this.uv < 0.1) {
			this.zfzhl = 0D;
		} else {
			this.zfzhl = (this.zfmjs * 1D) / this.uv;
		}
	}
}
