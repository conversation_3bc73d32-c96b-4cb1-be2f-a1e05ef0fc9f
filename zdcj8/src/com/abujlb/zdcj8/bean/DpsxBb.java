package com.abujlb.zdcj8.bean;

import com.abujlb.zdcj8.util.StrUtil;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

import net.sf.json.JSONObject;

/**
 * 店铺上新，宝贝信息
 * 
 * <AUTHOR>
 * @date 2020-11-05 19:56:12
 */
public class DpsxBb {

	private int height; // 图片：高
	private int width; // 图片：宽
	private String itemId; // 宝贝id
	// http://gw.alicdn.com/tfs/O1CN01KhvfAF1IOukvvzVQC_!!0-item_pic.jpg，直接访问原始图片
	private String path; // 图片，例如：O1CN01KhvfAF1IOukvvzVQC_!!0-item_pic.jpg，background-image: url("//gw.alicdn.com/tfs/O1CN01KhvfAF1IOukvvzVQC_!!0-item_pic.jpg_230x10000Q75.jpg_.webp")
	private long price; // 价格，例如21900代表219.00元
	private long starts; // 上新时间戳
	private String title; // 宝贝名称
	private String type; // 类型：item.宝贝、pic.图片、、
	private String videoId; // 视频id，例如：285741695317
	private String videoUrl; // 视频连接，例如：http://cloud.video.taobao.com/play/u/2616970884/p/2/e/6/t/1/285741695317.mp4?appKey=38829

	public DpsxBb() {

	}
	
	public DpsxBb(JSONObject obj) {
		try {
			this.height = 0; // 图片：高
			if (obj.has("height") && !StrUtil.isNull(obj.getString("height"))) {
				this.height = obj.getInt("height");
			}
			this.width = 0; // 图片：宽
			if (obj.has("width") && !StrUtil.isNull(obj.getString("width"))) {
				this.width = obj.getInt("width");
			}
			this.itemId = ""; // 宝贝id
			if (obj.has("itemId") && !StrUtil.isNull(obj.getString("itemId"))) {
				this.itemId = obj.getString("itemId");
			}
			this.path = ""; // 图片
			if (obj.has("path") && !StrUtil.isNull(obj.getString("path"))) {
				this.path = obj.getString("path");
			}
			this.price = 0; // 价格
			if (obj.has("price") && !StrUtil.isNull(obj.getString("price"))) {
				this.price = obj.getLong("price");
			}
			this.starts = 0; // 上新时间戳
			if (obj.has("starts") && !StrUtil.isNull(obj.getString("starts"))) {
				this.starts = obj.getLong("starts");
			}
			this.title = ""; // 宝贝名称
			if (obj.has("title") && !StrUtil.isNull(obj.getString("title"))) {
				this.title = obj.getString("title");
			}
			this.type = ""; // 类型：item.宝贝、pic.图片
			if (obj.has("type") && !StrUtil.isNull(obj.getString("type"))) {
				this.type = obj.getString("type");
			}
			
			this.videoId = ""; // 视频id
			if (obj.has("videoId") && !StrUtil.isNull(obj.getString("videoId"))) {
				this.videoId = obj.getString("videoId");
			}
			this.videoUrl = ""; // 视频连接
			if (obj.has("videoUrl") && !StrUtil.isNull(obj.getString("videoUrl"))) {
				this.videoUrl = obj.getString("videoUrl");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	public String toString() {
		Gson gson = new GsonBuilder().setDateFormat("yyyy-MM-dd HH:mm:ss").create();
		return gson.toJson(this);
	}

	public int getHeight() {
		return height;
	}

	public void setHeight(int height) {
		this.height = height;
	}

	public int getWidth() {
		return width;
	}

	public void setWidth(int width) {
		this.width = width;
	}

	public String getItemId() {
		return itemId;
	}

	public void setItemId(String itemId) {
		this.itemId = itemId;
	}

	public String getPath() {
		return path;
	}

	public void setPath(String path) {
		this.path = path;
	}

	public long getPrice() {
		return price;
	}

	public void setPrice(long price) {
		this.price = price;
	}

	public long getStarts() {
		return starts;
	}

	public void setStarts(long starts) {
		this.starts = starts;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getVideoId() {
		return videoId;
	}

	public void setVideoId(String videoId) {
		this.videoId = videoId;
	}

	public String getVideoUrl() {
		return videoUrl;
	}

	public void setVideoUrl(String videoUrl) {
		this.videoUrl = videoUrl;
	}
	
}
