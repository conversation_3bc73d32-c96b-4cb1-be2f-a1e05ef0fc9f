package com.abujlb.zdcj8.bean.ztc;

import org.springframework.format.annotation.DateTimeFormat;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 直通车行业人群榜单记录表
 * @date 2023/7/14 15:58
 **/
public class ZtcHyrqbdJl {
    private Long id;
    private Long jlbdpid;
    private Long userid;
    private String dpmc;
    private String zhuzh;
    private Long topid;
    private String msg;
    //记录时间
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date jlsj;
    //采集日期
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date rq;

    public ZtcHyrqbdJl() {
    }

    public ZtcHyrqbdJl(Long id, Long jlbdpid, Long userid, String dpmc,
                       String zhuzh, Long topid, String msg, Date jlsj, Date rq) {
        this.id = id;
        this.jlbdpid = jlbdpid;
        this.userid = userid;
        this.dpmc = dpmc;
        this.zhuzh = zhuzh;
        this.topid = topid;
        this.msg = msg;
        this.jlsj = jlsj;
        this.rq = rq;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getJlbdpid() {
        return jlbdpid;
    }

    public void setJlbdpid(Long jlbdpid) {
        this.jlbdpid = jlbdpid;
    }

    public Long getUserid() {
        return userid;
    }

    public void setUserid(Long userid) {
        this.userid = userid;
    }

    public String getDpmc() {
        return dpmc;
    }

    public void setDpmc(String dpmc) {
        this.dpmc = dpmc;
    }

    public String getZhuzh() {
        return zhuzh;
    }

    public void setZhuzh(String zhuzh) {
        this.zhuzh = zhuzh;
    }

    public Long getTopid() {
        return topid;
    }

    public void setTopid(Long topid) {
        this.topid = topid;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Date getJlsj() {
        return jlsj;
    }

    public void setJlsj(Date jlsj) {
        this.jlsj = jlsj;
    }

    public Date getRq() {
        return rq;
    }

    public void setRq(Date rq) {
        this.rq = rq;
    }
}
