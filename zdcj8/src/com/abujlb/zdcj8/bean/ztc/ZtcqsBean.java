package com.abujlb.zdcj8.bean.ztc;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

/**
 * bean 直通车趋势
 * 
 * <AUTHOR>
 * @date 2023-03-07 17:43:41
 */
public class ZtcqsBean {
	
	private String gjc;
	private String startDate;
	private String endDate;

	public ZtcqsBean() {

	}
	
	@Override
	public String toString() {
		Gson gson = new GsonBuilder().setDateFormat("yyyy-MM-dd HH:mm:ss").create();
		return gson.toJson(this);
	}

	public String getGjc() {
		return gjc;
	}

	public void setGjc(String gjc) {
		this.gjc = gjc;
	}

	public String getStartDate() {
		return startDate;
	}

	public void setStartDate(String startDate) {
		this.startDate = startDate;
	}

	public String getEndDate() {
		return endDate;
	}

	public void setEndDate(String endDate) {
		this.endDate = endDate;
	}
	
}
