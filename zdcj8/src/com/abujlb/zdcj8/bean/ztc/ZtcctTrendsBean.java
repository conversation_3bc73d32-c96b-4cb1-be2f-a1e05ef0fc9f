package com.abujlb.zdcj8.bean.ztc;

import com.abujlb.zdcj8.bean.ZtcctTrends;
import com.google.gson.Gson;

/**
 * bean直通车车图，行业关键词趋势
 * 
 * <AUTHOR>
 * @date 2023-03-07 22:50:44
 */
public class ZtcctTrendsBean {

	private String a; // 关键词
	private String b; // 类目id
	private String c; // 展现量，整数，例如：83916
	private String d; // 展现指数，浮点型，例如：66889.84101382896
	private String e; // 点击量（${t}在${i}${e}展示位上被点击的次数。注意，虚假点击会被${e}反作弊体系过滤，该数据为反作弊系统过滤后的数据。），整数，例如：4199
	private String f; // 点击指数，浮点型，例如：4502.730466193782
	private String g; // 点击转化率，小数，真实值，例如：0.014868329394186364，表示1.49%
	private String h; // 点击率，小数，真实值，例如：0.041377928764354976，表示4.14%
	private String i; // 花费（${t}在${i}${e}展示位上被用户点击所消耗的费用。），单位(分)，整数，例如：628887，表示6288.87元
	private String j; // 市场均价，单位(分)，浮点型，例如：48.403697642318214，表示0.48元
	private String k; // 竞争指数，整数，例如：349
	private String l; // 直接成交笔数（${t}在${i}${e}展示位被点击后，买家在所选转化周期内，直接在推广宝贝的详情页面拍下并通过支付宝交易的成交笔数。），整数，例如：93
	private String m; // 间接成交笔数（${t}在${i}${e}展示位被点击后，买家在所选转化周期内，通过推广宝贝的详情页面跳转至店铺内其他宝贝的详情页面拍下并通过支付宝交易的成交笔数。），整数，例如：67
	private String n; // 日期，例如：20230205000000
	
	// private double zxzs; // 展现指数
	// private double djlv; // 点击率
	
	public ZtcctTrendsBean() {
		
	}
	
	public ZtcctTrendsBean(ZtcctTrends ztcctTrends) {
		if (ztcctTrends != null) {
			this.a = ztcctTrends.getNormalWord(); // 关键词
			this.b = ztcctTrends.getCategoryId(); // 类目id
			this.c = ztcctTrends.getImpression(); // 展现量，整数，例如：83916
			this.d = ztcctTrends.getImpressionIndex(); // 展现指数，浮点型，例如：66889.84101382896
			this.e = ztcctTrends.getClick(); // 点击量（${t}在${i}${e}展示位上被点击的次数。注意，虚假点击会被${e}反作弊体系过滤，该数据为反作弊系统过滤后的数据。），整数，例如：4199
			this.f = ztcctTrends.getClickIndex(); // 点击指数，浮点型，例如：4502.730466193782
			this.g = ztcctTrends.getCvr(); // 点击转化率，小数，真实值，例如：0.014868329394186364，表示1.49%
			this.h = ztcctTrends.getCtr(); // 点击率，小数，真实值，例如：0.041377928764354976，表示4.14%
			this.i = ztcctTrends.getCost(); // 花费（${t}在${i}${e}展示位上被用户点击所消耗的费用。），单位(分)，整数，例如：628887，表示6288.87元
			this.j = ztcctTrends.getAvgPrice(); // 市场均价，单位(分)，浮点型，例如：48.403697642318214，表示0.48元
			this.k = ztcctTrends.getCompetition(); // 竞争指数，整数，例如：349
			this.l = ztcctTrends.getDirecttransactionshipping(); // 直接成交笔数（${t}在${i}${e}展示位被点击后，买家在所选转化周期内，直接在推广宝贝的详情页面拍下并通过支付宝交易的成交笔数。），整数，例如：93
			this.m = ztcctTrends.getIndirecttransactionshipping(); // 间接成交笔数（${t}在${i}${e}展示位被点击后，买家在所选转化周期内，通过推广宝贝的详情页面跳转至店铺内其他宝贝的详情页面拍下并通过支付宝交易的成交笔数。），整数，例如：67
			this.n = ztcctTrends.getTheDate(); // 日期，例如：20230205000000
		}
	}
	
	/*public void initData() {
		try {
			this.zxzs = 0D;
			if (!StringUtil.isNull2(this.c)) {
				this.zxzs = Double.valueOf(this.c);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		try {
			this.djlv = 0D;
			if (!StringUtil.isNull2(this.h)) {
				this.djlv = Double.valueOf(this.h);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}*/
	
	@Override
	public String toString() {
		Gson gson = new Gson();
		return gson.toJson(this);
	}

	public String getA() {
		return a;
	}

	public void setA(String a) {
		this.a = a;
	}

	public String getB() {
		return b;
	}

	public void setB(String b) {
		this.b = b;
	}

	public String getC() {
		return c;
	}

	public void setC(String c) {
		this.c = c;
	}

	public String getD() {
		return d;
	}

	public void setD(String d) {
		this.d = d;
	}

	public String getE() {
		return e;
	}

	public void setE(String e) {
		this.e = e;
	}

	public String getF() {
		return f;
	}

	public void setF(String f) {
		this.f = f;
	}

	public String getG() {
		return g;
	}

	public void setG(String g) {
		this.g = g;
	}

	public String getH() {
		return h;
	}

	public void setH(String h) {
		this.h = h;
	}

	public String getI() {
		return i;
	}

	public void setI(String i) {
		this.i = i;
	}

	public String getJ() {
		return j;
	}

	public void setJ(String j) {
		this.j = j;
	}

	public String getK() {
		return k;
	}

	public void setK(String k) {
		this.k = k;
	}

	public String getL() {
		return l;
	}

	public void setL(String l) {
		this.l = l;
	}

	public String getM() {
		return m;
	}

	public void setM(String m) {
		this.m = m;
	}

	public String getN() {
		return n;
	}

	public void setN(String n) {
		this.n = n;
	}

}
