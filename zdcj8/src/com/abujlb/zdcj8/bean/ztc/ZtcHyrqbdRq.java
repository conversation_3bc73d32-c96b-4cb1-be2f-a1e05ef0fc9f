package com.abujlb.zdcj8.bean.ztc;

import org.springframework.format.annotation.DateTimeFormat;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 直通车行业人群榜单日期表
 * @date 2023/7/14 16:36
 **/
public class ZtcHyrqbdRq {

    private Long topid;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date hyrqbdrq;

    public Long getTopid() {
        return topid;
    }

    public void setTopid(Long topid) {
        this.topid = topid;
    }

    public Date getHyrqbdrq() {
        return hyrqbdrq;
    }

    public void setHyrqbdrq(Date hyrqbdrq) {
        this.hyrqbdrq = hyrqbdrq;
    }
}
