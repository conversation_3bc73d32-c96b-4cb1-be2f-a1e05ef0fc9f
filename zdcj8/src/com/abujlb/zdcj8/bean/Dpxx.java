package com.abujlb.zdcj8.bean;

import java.util.List;

import com.abujlb.gson.SkipFieldExclusionStrategy;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

public class Dpxx {

	private int dpid; // 店铺id
	private String itemid; // 宝贝id
	private String jylmid; // 经营类目id
	private String detailUrl; // 淘宝地址
	private String picUrl; // 宝贝图片地址
	private String title; // 宝贝标题
	private String shopTitle; // 店铺名称
	private String shopUrl; // 店铺首页
	private String shopPictUrl; // 店铺图片地址
	private String userId; // 店铺主帐号id
	private int b2CShop; // 是否天猫 1/0
	private String province; // 店铺所在省
	private String city;// 店铺所在市
	private String content;// 详情页md5
	private String price;// 价格
	private String bbbt;// 标题
	private List<String> ztList;// 主图集合

	public int getDpid() {
		return dpid;
	}

	public void setDpid(int dpid) {
		this.dpid = dpid;
	}

	public String getItemid() {
		return itemid;
	}

	public void setItemid(String itemid) {
		this.itemid = itemid;
	}

	public String getJylmid() {
		return jylmid;
	}

	public void setJylmid(String jylmid) {
		this.jylmid = jylmid;
	}

	public String getDetailUrl() {
		return detailUrl;
	}

	public void setDetailUrl(String detailUrl) {
		this.detailUrl = detailUrl;
	}

	public String getPicUrl() {
		return picUrl;
	}

	public void setPicUrl(String picUrl) {
		this.picUrl = picUrl;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getShopTitle() {
		return shopTitle;
	}

	public void setShopTitle(String shopTitle) {
		this.shopTitle = shopTitle;
	}

	public String getShopUrl() {
		return shopUrl;
	}

	public void setShopUrl(String shopUrl) {
		this.shopUrl = shopUrl;
	}

	public String getShopPictUrl() {
		return shopPictUrl;
	}

	public void setShopPictUrl(String shopPictUrl) {
		this.shopPictUrl = shopPictUrl;
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public int getB2CShop() {
		return b2CShop;
	}

	public void setB2CShop(int b2cShop) {
		b2CShop = b2cShop;
	}

	public String getProvince() {
		return province;
	}

	public void setProvince(String province) {
		this.province = province;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public String getPrice() {
		return price;
	}

	public void setPrice(String price) {
		this.price = price;
	}

	public String getBbbt() {
		return bbbt;
	}

	public void setBbbt(String bbbt) {
		this.bbbt = bbbt;
	}

	public List<String> getZtList() {
		return ztList;
	}

	public void setZtList(List<String> ztList) {
		this.ztList = ztList;
	}

	public String toString() {
		Gson gson = new GsonBuilder()
				.setExclusionStrategies(new SkipFieldExclusionStrategy("itemid,jylmid,detailUrl,picUrl,title", true))
				.create();
		return gson.toJson(this);
	}

}
