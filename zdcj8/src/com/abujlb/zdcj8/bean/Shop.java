package com.abujlb.zdcj8.bean;

import com.abujlb.gson.SkipFieldExclusionStrategy;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

public class Shop {

	private String dpmc;// 店铺名称
	private String dpUrl;// 店铺地址
	private String dpPic;// 店铺图片
	private int dplx;// 店铺类型0c店1天猫店
	private int pm;// 行业排名
	private double ssrq;// 搜索人气
	private double jyzs;// 交易指数
	private double llzs;// 流量指数
	private double ssdjrs;// 搜索点击人数
	private double fks;// 访客数
	private double xse;// 成交金额
	private int jls;// 总记录条数
	private double uv;// uv价值
	private double zsuv;// 指数uv价值
	private double zzfzhl;
	
	public String getDpmc() {
		return dpmc;
	}

	public void setDpmc(String dpmc) {
		this.dpmc = dpmc;
	}

	public String getDpUrl() {
		return dpUrl;
	}

	public void setDpUrl(String dpUrl) {
		this.dpUrl = dpUrl;
	}

	public String getDpPic() {
		return dpPic;
	}

	public void setDpPic(String dpPic) {
		this.dpPic = dpPic;
	}

	public int getDplx() {
		return dplx;
	}

	public void setDplx(int dplx) {
		this.dplx = dplx;
	}

	public int getPm() {
		return pm;
	}

	public void setPm(int pm) {
		this.pm = pm;
	}

	public double getSsrq() {
		return ssrq;
	}

	public void setSsrq(double ssrq) {
		this.ssrq = ssrq;
	}

	public double getJyzs() {
		return jyzs;
	}

	public void setJyzs(double jyzs) {
		this.jyzs = jyzs;
	}

	public double getLlzs() {
		return llzs;
	}

	public void setLlzs(double llzs) {
		this.llzs = llzs;
	}

	public double getSsdjrs() {
		return ssdjrs;
	}

	public void setSsdjrs(double ssdjrs) {
		this.ssdjrs = ssdjrs;
	}

	public double getFks() {
		return fks;
	}

	public void setFks(double fks) {
		this.fks = fks;
	}

	public double getXse() {
		return xse;
	}

	public void setXse(double xse) {
		this.xse = xse;
	}

	public int getJls() {
		return jls;
	}

	public void setJls(int jls) {
		this.jls = jls;
	}

	public double getUv() {
		return uv;
	}

	public void setUv(double uv) {
		this.uv = uv;
	}

	public double getZsuv() {
		return zsuv;
	}

	public void setZsuv(double zsuv) {
		this.zsuv = zsuv;
	}
	
	public double getZzfzhl() {
		return zzfzhl;
	}

	public void setZzfzhl(double zzfzhl) {
		this.zzfzhl = zzfzhl;
	}

	public String toString() {
		Gson gson = new GsonBuilder().setExclusionStrategies(
				new SkipFieldExclusionStrategy("dpmc,dpUrl,dpPic,dplx,pm,ssrq,jyzs,llzs,ssdjrs,fks,xse,zsuv,uv,zzfzhl", true))
				.create();
		return gson.toJson(this);
	}
}
