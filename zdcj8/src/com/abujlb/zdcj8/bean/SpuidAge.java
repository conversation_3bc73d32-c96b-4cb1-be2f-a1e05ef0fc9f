package com.abujlb.zdcj8.bean;

import java.util.HashMap;
import java.util.Map;

import com.google.gson.Gson;

public class SpuidAge {
	public static final Map<String, String> NLD = new HashMap<String, String>();
	static {
		NLD.put("2", "18-25岁");
		NLD.put("3", "26-30岁");
		NLD.put("4", "31-35岁");
		NLD.put("5", "36-40岁");
		NLD.put("6", "41-50岁");
		NLD.put("7", "51岁以上");
		NLD.put("unknown", "蒙面侠");
	}
	private String ageBand;
	private String ageName;
	private double ageBandPer;

	public String getAgeBand() {
		return ageBand;
	}

	public double getAgeBandPer() {
		return ageBandPer;
	}

	public void setAgeBand(String ageBand) {
		this.ageBand = ageBand;
	}

	public void setAgeBandPer(double ageBandPer) {
		this.ageBandPer = ageBandPer;
	}

	public String getAgeName() {
		return ageName;
	}

	public void setAgeName(String ageName) {
		this.ageName = ageName;
	}

	public void clAgeName() {
		this.ageName = NLD.get(this.ageBand);
		if (this.ageName == null) {
			this.ageName = "未知";
		}
	}

	public String toString() {
		this.clAgeName();
		Gson gson = new Gson();
		return gson.toJson(this);
	}
}
