package com.abujlb.zdcj8.bean;

import com.abujlb.zdcj8.util.StrUtil;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

import net.sf.json.JSONObject;

/**
 * 淘客订单
 * 
 * <AUTHOR>
 * @date 2020-11-10 15:54:46
 */
public class Tkdd {

	private String orderId; // 订单号，例如：1347689055579458045
	private String content; // 内容，例如：是淘客订单、非淘客订单
	// private String id; // 主键id
	// private String status; // 状态：1.采集中、2.采集完成
	// private String uuid; // 唯一id

	public Tkdd() {

	}

	public Tkdd(JSONObject obj) {
		try {
			this.orderId = ""; // 注册时间
			if (obj.has("orderId") && !StrUtil.isNull(obj.getString("orderId"))) {
				this.orderId = obj.getString("orderId");
			}
			this.content = ""; // 性别
			if (obj.has("content") && !StrUtil.isNull(obj.getString("content"))) {
				this.content = obj.getString("content");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public String toString() {
		Gson gson = new GsonBuilder().setDateFormat("yyyy-MM-dd HH:mm:ss").create();
		return gson.toJson(this);
	}

	public String getOrderId() {
		return orderId;
	}

	public void setOrderId(String orderId) {
		this.orderId = orderId;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

}
