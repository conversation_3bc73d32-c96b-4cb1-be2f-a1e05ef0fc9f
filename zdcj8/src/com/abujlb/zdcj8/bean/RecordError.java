package com.abujlb.zdcj8.bean;

public class RecordError {
	private int id;
	private String zhuzh;// 主账号
	private String zzh;// 子账号
	private int jlbdpid;// 俱乐部店铺id
	private int errorcode;// 错误code
	private String errormsg;// 错误信息
	private String servername;// 服务器名称
	private String classmethod;// 类名方法
	private String ip;

	public RecordError(String zhuzh, String zzh, int jlbdpid, int errorcode, String errormsg, String servername,
			String classmethod) {
		this.zhuzh = zhuzh;
		this.zzh = zzh;
		this.jlbdpid = jlbdpid;
		this.errorcode = errorcode;
		this.errormsg = errormsg;
		this.servername = servername;
		this.classmethod = classmethod;
	}

	public RecordError(String zhuzh, String zzh, int jlbdpid, int errorcode, String errormsg, String servername,
			String classmethod, String ip) {
		this(zhuzh, zzh, jlbdpid, errorcode, errormsg, servername, classmethod);
		this.ip = ip;
	}

	public RecordError() {

	}

	public String getZhuzh() {
		return zhuzh;
	}

	public void setZhuzh(String zhuzh) {
		this.zhuzh = zhuzh;
	}

	public String getZzh() {
		return zzh;
	}

	public void setZzh(String zzh) {
		this.zzh = zzh;
	}

	public int getJlbdpid() {
		return jlbdpid;
	}

	public void setJlbdpid(int jlbdpid) {
		this.jlbdpid = jlbdpid;
	}

	public int getErrorcode() {
		return errorcode;
	}

	public void setErrorcode(int errorcode) {
		this.errorcode = errorcode;
	}

	public String getErrormsg() {
		return errormsg;
	}

	public void setErrormsg(String errormsg) {
		this.errormsg = errormsg;
	}

	public String getServername() {
		return servername;
	}

	public void setServername(String servername) {
		this.servername = servername;
	}

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public String getClassmethod() {
		return classmethod;
	}

	public void setClassmethod(String classmethod) {
		this.classmethod = classmethod;
	}

	public String getIp() {
		return ip;
	}

	public void setIp(String ip) {
		this.ip = ip;
	}
}
