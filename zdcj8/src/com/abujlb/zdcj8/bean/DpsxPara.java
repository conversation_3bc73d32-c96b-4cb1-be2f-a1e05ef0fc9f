package com.abujlb.zdcj8.bean;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

/**
 * 店铺上新
 * 
 * <AUTHOR>
 * @date 2020-11-05 15:07:19
 */
public class DpsxPara {

	private String userId; // 店铺用户id
	private long lastTime; // 查询时间，例如：0代表最近上新的宝贝信息
	// private long tab; // 类型：0.最新微淘、1.上新、2.直播、3.视频

	public DpsxPara() {

	}
	
	public DpsxPara(String userId, long lastTime) {
		this.userId = userId;
		this.lastTime = lastTime;
	}

	public String toString() {
		Gson gson = new GsonBuilder().setDateFormat("yyyy-MM-dd HH:mm:ss").create();
		return gson.toJson(this);
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public long getLastTime() {
		return lastTime;
	}

	public void setLastTime(long lastTime) {
		this.lastTime = lastTime;
	}

}
