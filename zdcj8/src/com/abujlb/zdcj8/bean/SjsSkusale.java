package com.abujlb.zdcj8.bean;

import com.abujlb.BaseBean;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

/**
 * 竞品sku销量 实体
 * 
 * <AUTHOR>
 * @date 2020-09-24 17:11:57
 */
public class SjsSkusale extends BaseBean {

	private String itemid; // 商品id
	private String rq; // 日期
	private String startRq; // 开始日期
	private String endRq; // 结束日期

	private String orderby; // 排序方式，ASC或DESC，配合order使用，例如order == rq，orderby == ASC;

	public SjsSkusale() {

	}

	public SjsSkusale(String itemid, String rq, String startRq, String endRq, String order, String orderby) {
		this.itemid = itemid; // 商品id
		this.rq = rq; // 日期
		this.startRq = startRq; // 开始日期
		this.endRq = endRq; // 结束日期
		this.setOrder(order); // 排序
		this.orderby = orderby; // 排序方式，ASC或DESC，配合order使用，例如order == xh，orderby == ASC;
	}

	public String toString() {
		Gson gson = new GsonBuilder().setDateFormat("yyyy-MM-dd HH:mm:ss").create();
		return gson.toJson(this);
	}

	public String getItemid() {
		return itemid;
	}

	public void setItemid(String itemid) {
		this.itemid = itemid;
	}

	public String getRq() {
		return rq;
	}

	public void setRq(String rq) {
		this.rq = rq;
	}

	public String getStartRq() {
		return startRq;
	}

	public void setStartRq(String startRq) {
		this.startRq = startRq;
	}

	public String getEndRq() {
		return endRq;
	}

	public void setEndRq(String endRq) {
		this.endRq = endRq;
	}

	public String getOrderby() {
		return orderby;
	}

	public void setOrderby(String orderby) {
		this.orderby = orderby;
	}

}
