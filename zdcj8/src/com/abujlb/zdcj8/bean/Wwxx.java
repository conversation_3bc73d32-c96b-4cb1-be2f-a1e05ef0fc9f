package com.abujlb.zdcj8.bean;

import com.abujlb.zdcj8.util.StrUtil;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

import net.sf.json.JSONObject;

/**
 * 旺旺号信息
 * 
 * <AUTHOR>
 * @date 2020-11-10 11:51:58
 */
public class Wwxx {

	private String reg_time; // 注册时间，例如：2013-04-14(约7.58年)
	private String last_logintime; // ，例如：
	private String sex; // 性别，例如：男号
	private String shop; // 是否开店，例如：无
	private String relaName; // 是否实名认证，例如：有认证s
	// private String relaNameImg; // 实名认证图片，例如：
	// private String buyer_total_point; // 注册天数，例如：2767天
	private String buyer_total_point; // 购买次数，例如：59单
	private String buyer_good_rate; // 好评率，例如：100%
	private String urlImg; // vip等级图片地址，例如：http://img.alicdn.com/newrank/b_red_3.gif
	private String vip; // vip等级，例如：3
	private String week_avg; // 每周平均购买，例如：0.63
	// private String is88vip; // 是否88vip，例如：
	private String jqNum; // 降权次数，例如：0
	// private String wwbqz; // 旺旺标签，例如：
	private String ep; // （标记）恶评，例如：0
	private String tk; // （标记）退款，例如：0
	private String qz; // （标记）敲诈，例如：0
	private String hcp; // （标记）换产品，例如：0
	private String cbs; // （标记）吃白食，例如：0
	private String qt; // （标记）抽检，例如：0
	private String dq; // （账号被查询次数）当前，例如：0
	private String zj3t; // （账号被查询次数）最近3天，例如：0
	private String zj7t; // （账号被查询次数）最近7天，例如：0
	// private String zj30t; // （账号被查询次数）最近30天，例如：0
	private String zj30t; // （账号被查询次数）上周，例如：0
	private String sdhj; // 刷单痕迹，例如：0
	private String active_point; // ，例如：2点1级
	private String totalDayNum; // 总查询次数，例如：7
	
	// private String buyer_total_pointImg; // 买家图片，例如：
	// private String delNum; // ，例如：0s
	// private String delNum9; // ，例如：0
	// private String falseTraNum; // ，例如：0
	// private String falseTraNum9; // ，例如：0
	// private String level; // ，例如：
	// private String params; // ，例如：
	// private String remark; // ，例如：
	// private String search30day; // ，例如：0
	// private String searchValue; // ，例如：

	public Wwxx() {

	}

	public Wwxx(JSONObject obj) {
		try {
			this.reg_time = ""; // 注册时间
			if (obj.has("reg_time") && !StrUtil.isNull(obj.getString("reg_time"))) {
				this.reg_time = obj.getString("reg_time");
			}
			this.sex = ""; // 性别
			if (obj.has("sex") && !StrUtil.isNull(obj.getString("sex"))) {
				this.sex = obj.getString("sex");
			}
			this.shop = ""; // 是否开店
			if (obj.has("shop") && !StrUtil.isNull(obj.getString("shop"))) {
				this.shop = obj.getString("shop");
			}
			this.relaName = ""; // 是否实名认证
			if (obj.has("relaName") && !StrUtil.isNull(obj.getString("relaName"))) {
				this.relaName = obj.getString("relaName");
			}
			this.buyer_total_point = ""; // 注册天数
			if (obj.has("buyer_total_point") && !StrUtil.isNull(obj.getString("buyer_total_point"))) {
				this.buyer_total_point = obj.getString("buyer_total_point");
			}
			this.buyer_good_rate = ""; // 好评率
			if (obj.has("buyer_good_rate") && !StrUtil.isNull(obj.getString("buyer_good_rate"))) {
				this.buyer_good_rate = obj.getString("buyer_good_rate");
			}
			this.vip = ""; // vip等级
			if (obj.has("vip") && !StrUtil.isNull(obj.getString("vip"))) {
				this.vip = obj.getString("vip");
			}
			this.week_avg = ""; // 每周平均购买
			if (obj.has("week_avg") && !StrUtil.isNull(obj.getString("week_avg"))) {
				this.week_avg = obj.getString("week_avg");
			}
			this.jqNum = ""; // 降权次数
			if (obj.has("jqNum") && !StrUtil.isNull(obj.getString("jqNum"))) {
				this.jqNum = obj.getString("jqNum");
			}
			this.ep = ""; // （标记）恶评
			if (obj.has("ep") && !StrUtil.isNull(obj.getString("ep"))) {
				this.ep = obj.getString("ep");
			}
			this.tk = ""; // （标记）退款
			if (obj.has("tk") && !StrUtil.isNull(obj.getString("tk"))) {
				this.tk = obj.getString("tk");
			}
			this.qz = ""; // （标记）敲诈
			if (obj.has("qz") && !StrUtil.isNull(obj.getString("qz"))) {
				this.qz = obj.getString("qz");
			}
			this.hcp = ""; // （标记）换产品
			if (obj.has("hcp") && !StrUtil.isNull(obj.getString("hcp"))) {
				this.hcp = obj.getString("hcp");
			}
			this.cbs = ""; // （标记）吃白食
			if (obj.has("cbs") && !StrUtil.isNull(obj.getString("cbs"))) {
				this.cbs = obj.getString("cbs");
			}
			this.qt = ""; // （标记）抽检
			if (obj.has("qt") && !StrUtil.isNull(obj.getString("qt"))) {
				this.qt = obj.getString("qt");
			}
			this.dq = ""; // （账号被查询次数）当前
			if (obj.has("dq") && !StrUtil.isNull(obj.getString("dq"))) {
				this.dq = obj.getString("dq");
			}
			this.zj3t = ""; // （账号被查询次数）最近3天
			if (obj.has("zj3t") && !StrUtil.isNull(obj.getString("zj3t"))) {
				this.zj3t = obj.getString("zj3t");
			}
			this.zj7t = ""; // （账号被查询次数）最近7天
			if (obj.has("zj7t") && !StrUtil.isNull(obj.getString("zj7t"))) {
				this.zj7t = obj.getString("zj7t");
			}
			this.zj30t = ""; // （账号被查询次数）最近30天
			if (obj.has("zj30t") && !StrUtil.isNull(obj.getString("zj30t"))) {
				this.zj30t = obj.getString("zj30t");
			}
			this.last_logintime = ""; // ，例如：
			if (obj.has("last_logintime") && !StrUtil.isNull(obj.getString("last_logintime"))) {
				this.last_logintime = obj.getString("last_logintime");
			}
			this.urlImg = ""; // vip等级图片地址，例如：http://img.alicdn.com/newrank/b_red_3.gif
			if (obj.has("urlImg") && !StrUtil.isNull(obj.getString("urlImg"))) {
				this.urlImg = obj.getString("urlImg");
			}
			this.sdhj = ""; // 刷单痕迹，例如：0
			if (obj.has("sdhj") && !StrUtil.isNull(obj.getString("sdhj"))) {
				this.sdhj = obj.getString("sdhj");
			}
			this.active_point = ""; // ，例如：2点1级
			if (obj.has("active_point") && !StrUtil.isNull(obj.getString("active_point"))) {
				this.active_point = obj.getString("active_point");
			}
			this.totalDayNum = ""; // 总查询次数，例如：7
			if (obj.has("totalDayNum") && !StrUtil.isNull(obj.getString("totalDayNum"))) {
				this.totalDayNum = obj.getString("totalDayNum");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public String toString() {
		Gson gson = new GsonBuilder().setDateFormat("yyyy-MM-dd HH:mm:ss").create();
		return gson.toJson(this);
	}

	public String getReg_time() {
		return reg_time;
	}

	public void setReg_time(String reg_time) {
		this.reg_time = reg_time;
	}

	public String getSex() {
		return sex;
	}

	public void setSex(String sex) {
		this.sex = sex;
	}

	public String getShop() {
		return shop;
	}

	public void setShop(String shop) {
		this.shop = shop;
	}

	public String getRelaName() {
		return relaName;
	}

	public void setRelaName(String relaName) {
		this.relaName = relaName;
	}

	public String getBuyer_total_point() {
		return buyer_total_point;
	}

	public void setBuyer_total_point(String buyer_total_point) {
		this.buyer_total_point = buyer_total_point;
	}

	public String getBuyer_good_rate() {
		return buyer_good_rate;
	}

	public void setBuyer_good_rate(String buyer_good_rate) {
		this.buyer_good_rate = buyer_good_rate;
	}

	public String getVip() {
		return vip;
	}

	public void setVip(String vip) {
		this.vip = vip;
	}

	public String getWeek_avg() {
		return week_avg;
	}

	public void setWeek_avg(String week_avg) {
		this.week_avg = week_avg;
	}

	public String getJqNum() {
		return jqNum;
	}

	public void setJqNum(String jqNum) {
		this.jqNum = jqNum;
	}

	public String getEp() {
		return ep;
	}

	public void setEp(String ep) {
		this.ep = ep;
	}

	public String getTk() {
		return tk;
	}

	public void setTk(String tk) {
		this.tk = tk;
	}

	public String getQz() {
		return qz;
	}

	public void setQz(String qz) {
		this.qz = qz;
	}

	public String getHcp() {
		return hcp;
	}

	public void setHcp(String hcp) {
		this.hcp = hcp;
	}

	public String getCbs() {
		return cbs;
	}

	public void setCbs(String cbs) {
		this.cbs = cbs;
	}

	public String getQt() {
		return qt;
	}

	public void setQt(String qt) {
		this.qt = qt;
	}

	public String getDq() {
		return dq;
	}

	public void setDq(String dq) {
		this.dq = dq;
	}

	public String getZj3t() {
		return zj3t;
	}

	public void setZj3t(String zj3t) {
		this.zj3t = zj3t;
	}

	public String getZj7t() {
		return zj7t;
	}

	public void setZj7t(String zj7t) {
		this.zj7t = zj7t;
	}

	public String getZj30t() {
		return zj30t;
	}

	public void setZj30t(String zj30t) {
		this.zj30t = zj30t;
	}

	public String getLast_logintime() {
		return last_logintime;
	}

	public void setLast_logintime(String last_logintime) {
		this.last_logintime = last_logintime;
	}

	public String getUrlImg() {
		return urlImg;
	}

	public void setUrlImg(String urlImg) {
		this.urlImg = urlImg;
	}

	public String getSdhj() {
		return sdhj;
	}

	public void setSdhj(String sdhj) {
		this.sdhj = sdhj;
	}

	public String getActive_point() {
		return active_point;
	}

	public void setActive_point(String active_point) {
		this.active_point = active_point;
	}

	public String getTotalDayNum() {
		return totalDayNum;
	}

	public void setTotalDayNum(String totalDayNum) {
		this.totalDayNum = totalDayNum;
	}

}
