package com.abujlb.zdcj8.bean;

public class RscHy {

	private int clickHits;// 点击人气
	private double clickRate;// 点击率
	private int hotSearchRank;// 热搜排名
	private int orderNum;//
	private double p4pRefPrice;//
	private int seIpvUvHits;// 搜索人气
	private String searchWord;// 搜索词
	private int soarRank;//
	private double tmClickRate;//
	private double payRate;// 支付转化率
	private int xl;// 销量

	public int getClickHits() {
		return clickHits;
	}

	public void setClickHits(int clickHits) {
		this.clickHits = clickHits;
	}

	public double getClickRate() {
		return clickRate;
	}

	public void setClickRate(double clickRate) {
		this.clickRate = clickRate;
	}

	public int getHotSearchRank() {
		return hotSearchRank;
	}

	public void setHotSearchRank(int hotSearchRank) {
		this.hotSearchRank = hotSearchRank;
	}

	public int getOrderNum() {
		return orderNum;
	}

	public void setOrderNum(int orderNum) {
		this.orderNum = orderNum;
	}

	public double getP4pRefPrice() {
		return p4pRefPrice;
	}

	public void setP4pRefPrice(double p4pRefPrice) {
		this.p4pRefPrice = p4pRefPrice;
	}

	public int getSeIpvUvHits() {
		return seIpvUvHits;
	}

	public void setSeIpvUvHits(int seIpvUvHits) {
		this.seIpvUvHits = seIpvUvHits;
	}

	public String getSearchWord() {
		return searchWord;
	}

	public void setSearchWord(String searchWord) {
		this.searchWord = searchWord;
	}

	public int getSoarRank() {
		return soarRank;
	}

	public void setSoarRank(int soarRank) {
		this.soarRank = soarRank;
	}

	public double getTmClickRate() {
		return tmClickRate;
	}

	public void setTmClickRate(double tmClickRate) {
		this.tmClickRate = tmClickRate;
	}

	public int getXl() {
		return xl;
	}

	public void setXl(int xl) {
		this.xl = xl;
	}

	public double getPayRate() {
		return payRate;
	}

	public void setPayRate(double payRate) {
		this.payRate = payRate;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj) {
			return true;
		}
		if (obj == null) {
			return false;
		}
		if (getClass() != obj.getClass()) {
			return false;
		}
		RscHy rscHy = (RscHy) obj;
		if (searchWord == null) {
			if (rscHy.searchWord != null) {
				return false;
			}
		} else if (!searchWord.equals(rscHy.searchWord)) {
			return false;
		}
		return true;

	}

}
