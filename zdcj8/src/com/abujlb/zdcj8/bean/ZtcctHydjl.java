package com.abujlb.zdcj8.bean;

import com.abujlb.gson.SkipFieldExclusionStrategy;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

/**
 * 直通车车图，行业点击率
 * 
 * <AUTHOR>
 * @date 2020-08-05 14:56:24
 */
public class ZtcctHydjl {

	private double djl; // 点击率
	
	private String cateId; // 类目id
	private String cateName; // 类目名称

	public void initDjl(Double djlv) {
		if (djlv != null) {
			this.djl = djlv.doubleValue();
		}
	}
	
	public String toString() {
		// Gson gson = gson = new GsonBuilder().setExclusionStrategies(new SkipFieldExclusionStrategy("id,djl")).create(); // 过滤
		Gson gson = new GsonBuilder().setExclusionStrategies(new SkipFieldExclusionStrategy("djl", true)).create(); // 只显示djl
		return gson.toJson(this);
	}

	public double getDjl() {
		return djl;
	}

	public void setDjl(double djl) {
		this.djl = djl;
	}

	public String getCateId() {
		return cateId;
	}

	public void setCateId(String cateId) {
		this.cateId = cateId;
	}

	public String getCateName() {
		return cateName;
	}

	public void setCateName(String cateName) {
		this.cateName = cateName;
	}

}
