package com.abujlb.zdcj8.bean;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;

import com.abujlb.util.DateUtil;

public class SpuidPara {
	private String spuid;
	private String device;
	private String dateType;
	private String date;
	private String dateRange;

	public SpuidPara(String spuid, String device, String dateType, String date) {
		super();
		this.spuid = spuid;
		this.device = device;
		this.dateType = dateType;
		this.date = date;
		this.jsDateRange();
	}

	public void jsDateRange() {
		DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
		Date dteEnd = null;
		Date dteStart = null;
		if (dateType == null) {
			dateType = "recent1";
		}
		try {
			dteEnd = df.parse(date);
		} catch (Exception e) {
		}
		if (dteEnd == null) {
			dteEnd = DateUtil.getLastDay();
		}
		if (dateType.equalsIgnoreCase("recent1")) {
			dteStart = dteEnd;
		} else if (dateType.equalsIgnoreCase("recent7")) {
			dteStart = DateUtil.getDateBefore(dteEnd, 6);
		} else if (dateType.equalsIgnoreCase("recent30")) {
			dteStart = DateUtil.getDateBefore(dteEnd, 29);
		} else { // day
			dteStart = dteEnd;
		}
		dateRange = df.format(dteStart) + "%7C" + df.format(dteEnd);
	}

	public String getSpuid() {
		return spuid;
	}

	public String getDevice() {
		return device;
	}

	public String getDateType() {
		return dateType;
	}

	public String getDate() {
		return date;
	}

	public String getDateRange() {
		return dateRange;
	}

	public void setSpuid(String spuid) {
		this.spuid = spuid;
	}

	public void setDevice(String device) {
		this.device = device;
	}

	public void setDateType(String dateType) {
		this.dateType = dateType;
	}

	public void setDate(String date) {
		this.date = date;
	}

	public void setDateRange(String dateRange) {
		this.dateRange = dateRange;
	}
	
	public static void main(String []args){
		SpuidPara p = new SpuidPara("0","0","day","2018-09-06");
		System.out.println(p.getDateRange());
	}
}
