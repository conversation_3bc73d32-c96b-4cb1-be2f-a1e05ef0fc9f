package com.abujlb.zdcj8.bean.sjs;

import com.aliyun.openservices.shade.com.alibaba.fastjson.JSON;

import net.sf.json.JSONObject;

/**
* <AUTHOR>
* @date 2020-11-16
*/
public class Ssksp {

	//宝贝id
	private String bbid ;
	//关键词
	private String gjc ;
	//淘口令
	private String tkl ;
	//淘宝二维码
	private String tbewm ;
	//微信二维码
	private String wxewm ;
	//生成时间
	private String scsj ;
	public Ssksp() {
		
	}
	public Ssksp(JSONObject jsonObject) {
		this.bbid = jsonObject.getString("url");
		this.gjc = jsonObject.getString("title");
		this.tkl = jsonObject.getString("tkl");
		this.tbewm = jsonObject.getString("tbQrcode");
		this.wxewm = jsonObject.getString("wxQrcode");
		this.scsj = jsonObject.getString("createTimeStr");
	}
	public String getBbid() {
		return bbid;
	}
	public void setBbid(String bbid) {
		this.bbid = bbid;
	}
	public String getGjc() {
		return gjc;
	}
	public void setGjc(String gjc) {
		this.gjc = gjc;
	}
	public String getTkl() {
		return tkl;
	}
	public void setTkl(String tkl) {
		this.tkl = tkl;
	}
	public String getTbewm() {
		return tbewm;
	}
	public void setTbewm(String tbewm) {
		this.tbewm = tbewm;
	}
	public String getWxewm() {
		return wxewm;
	}
	public void setWxewm(String wxewm) {
		this.wxewm = wxewm;
	}
	public String getScsj() {
		return scsj;
	}
	public void setScsj(String scsj) {
		this.scsj = scsj;
	}
	
	@Override
	public String toString() {
		return JSON.toJSONString(this) ;
	}
	
}
