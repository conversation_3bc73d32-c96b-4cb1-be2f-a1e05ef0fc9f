package com.abujlb.zdcj8.bean.sjs;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

import com.aliyun.openservices.shade.com.alibaba.fastjson.JSON;

/**
 * 搜索秒单接口参数
 * 
 * <AUTHOR>
 * @date 2020-11-13
 */
public class SsmdInput {

	// 宝贝id或者宝贝链接
	private String bbid;
	// 关键词
	private String gjc;
	// 旺旺
	private String ww;

	// 冗余
	private String createBy;

	public String getGjcUrlEncode() {
		try {
			String gjcUrlEncode = URLEncoder.encode(this.gjc, "utf-8");
			return gjcUrlEncode;
		} catch (UnsupportedEncodingException e) {
			return "";
		}
	}

	public String getWwUrlEncode() {
		try {
			String wwUrlEncode = URLEncoder.encode(this.ww, "utf-8");
			return wwUrlEncode;
		} catch (UnsupportedEncodingException e) {
			return "";
		}
	}

	public String getBbid() {
		return bbid;
	}

	public void setBbid(String bbid) {
		this.bbid = bbid;
	}

	public String getGjc() {
		return gjc;
	}

	public void setGjc(String gjc) {
		this.gjc = gjc;
	}

	public String getWw() {
		return ww;
	}

	public void setWw(String ww) {
		this.ww = ww;
	}

	public String getCreateBy() {
		return createBy;
	}

	public void setCreateBy(String createBy) {
		this.createBy = createBy;
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
	
}
