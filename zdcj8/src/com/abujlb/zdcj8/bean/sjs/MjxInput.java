package com.abujlb.zdcj8.bean.sjs;

import org.apache.commons.lang3.StringUtils;
import org.springframework.web.util.UriUtils;

import com.alibaba.fastjson.JSON;

/**
 * 查询买家秀参数
 * 
 * <AUTHOR>
 * @date 2020-11-20
 */
public class MjxInput {

	// 宝贝id
	private String url;
	// 排序 1：推荐排序 2：时间排序
	private String sort;
	// 类型 图片：picture 视频：video
	private String type;
	// 印象词id ==>买家秀印象接口返回
	private String expre;
	// skuid==>买家秀印象接口返回
	private String sku;
	// 当前页面
	private String num;
	// 确定下载接口返回的pid
	private String pid;
	// ==>买家秀印象接口返回
	private String skuCn;
	// ==>买家秀印象接口返回
	private String expreCn;
	// 以下参数不必传
	private String typeCn;
	private String sortCn;

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public String getSort() {
		return sort;
	}

	public void setSort(String sort) {
		this.sort = sort;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getExpre() {
		return StringUtils.isEmpty(expre) ? "" : expre;
	}

	public void setExpre(String expre) {
		this.expre = expre;
	}

	public String getSku() {
		return StringUtils.isEmpty(sku) ? "" : sku;
	}

	public void setSku(String sku) {
		this.sku = sku;
	}

	public String getPid() {
		return StringUtils.isEmpty(pid) ? "" : pid;
	}

	public void setPid(String pid) {
		this.pid = pid;
	}

	public String getSkuCn() {
		return StringUtils.isEmpty(skuCn) ? "" : skuCn;
	}

	public void setSkuCn(String skuCn) {
		this.skuCn = skuCn;
	}

	public String getSkuCnUrlEncode() {
		if (StringUtils.isEmpty(skuCn)) {
			return "";
		}
		try {
			return UriUtils.encodeQuery(this.skuCn, "utf-8");
		} catch (Exception e) {
			return "";
		}
	}

	public String getTypeCn() {
		return StringUtils.isEmpty(typeCn)?"":typeCn;
	}

	public String getTypeCnUrlEncode() {
		try {
			if ("picture".equals(type)) {
				// 图片
				return "%E5%9B%BE%E7%89%87";
			}
			if ("video".equals(type)) {
				// 视频
				return "%E8%A7%86%E9%A2%91";
			}
		} catch (Exception e) {
			return "";
		}
		return "";
	}

	public void setTypeCn(String typeCn) {
		this.typeCn = typeCn;
	}

	public String getSortCn() {
		return sortCn;
	}

	public String getSortCnUrlEncode() {
		try {
			if ("1".equals(sort)) {
				// 推荐排序
				return "%E6%8E%A8%E8%8D%90%E6%8E%92%E5%BA%8F";
			}
			if ("2".equals(sort)) {
				// 时间排序
				return "%E6%97%B6%E9%97%B4%E6%8E%92%E5%BA%8F";
			}
		} catch (Exception e) {
			return "";
		}
		return "";
	}

	public void setSortCn(String sortCn) {
		this.sortCn = sortCn;
	}

	public String getExpreCn() {
		return expreCn;
	}

	public String getExpreCnUrlEncode() {
		if (StringUtils.isEmpty(expreCn)) {
			return "";
		}
		try {
			return UriUtils.encodeQuery(this.expreCn, "utf-8");
		} catch (Exception e) {
			return "";
		}
	}

	public void setExpreCn(String expreCn) {
		this.expreCn = expreCn;
	}

	public String getNum() {
		return num ;
	}

	public void setNum(String num) {
		this.num = num;
	}

	public void check() {
		if (StringUtils.isEmpty(this.sort)) {
			this.sort = "1";
			this.sortCn = "推荐排序";
		}
		if (StringUtils.isEmpty(this.type)) {
			this.type = "picture";
			this.typeCn = "图片";
		}
	}
	
	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}
