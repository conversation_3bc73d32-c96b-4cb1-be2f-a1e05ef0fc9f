package com.abujlb.zdcj8.bean.sjs;

import java.io.UnsupportedEncodingException;

import org.springframework.web.util.UriUtils;

import com.alibaba.fastjson.JSON;

/**
 * <AUTHOR>
 * @date 2020-11-17
 */
public class KllqdInput {

	//当type！=1时 value表示宝贝id  否则value表示洋淘买家秀淘口令
	private String value;
	//入口类型 多个用英文逗号隔开
	//1：洋淘买家秀 2：相似宝贝 3：相似宝贝2  4：淘宝精选 5：点过赞的宝贝 6：手淘推荐 7：小黑盒 8：ifashion 
	//9：酷玩星球 10：首单优惠 11：品牌动态 12：躺平 13：极有家 14：单身狗联盟 15：亲宝贝 
	//16：U先试用 18：淘宝吃货 20：其他商品详情 21：时尚新品
	// 当选择入口为 ： 20其他商品详情 jpid不为空
	// 当选择入口为 ： 1洋淘买家秀 
	private String type;
	//竞品宝贝id 
	private String jpid ;
	
	public String getJpid() {
		return jpid==null?"":jpid;
	}

	public void setJpid(String jpid) {
		this.jpid = jpid;
	}

	public String getValue() {
		return value;
	}

	public String getValue2() {
		if(this.value!=null) {
			try {
				return UriUtils.encodeQuery(this.value, "utf-8") ;
			} catch (UnsupportedEncodingException e) {
				e.printStackTrace();
			}
		}
		return value;
	}
	
	public void setValue(String value) {
		this.value = value;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
	
}
