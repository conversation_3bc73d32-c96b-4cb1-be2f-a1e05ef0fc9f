package com.abujlb.zdcj8.bean.sjs;

import com.alibaba.fastjson.JSON;

import net.sf.json.JSONObject;

/**
* <AUTHOR>
* @date 2020-11-17
*/
public class Kllqd {

	private String gn ;
	private String tbewm ;
	private String qqwxdlj ;
	private String wxewm ;
	private String scsj ;
	private String url ;
	
	public Kllqd() {
		
	}
	
	public Kllqd(JSONObject jsonObject) {
		this.gn = jsonObject.getString("kltype");
		this.tbewm = jsonObject.getString("tbcode");
		this.qqwxdlj = jsonObject.getString("shorturl");
		this.wxewm = jsonObject.getString("qrcode");
		this.scsj = jsonObject.getString("createtimeStr");
		this.url = jsonObject.getString("url");
	}
	
	public String getGn() {
		return gn;
	}
	public void setGn(String gn) {
		this.gn = gn;
	}
	public String getTbewm() {
		return tbewm;
	}
	public void setTbewm(String tbewm) {
		this.tbewm = tbewm;
	}
	public String getQqwxdlj() {
		return qqwxdlj;
	}
	public void setQqwxdlj(String qqwxdlj) {
		this.qqwxdlj = qqwxdlj;
	}
	public String getWxewm() {
		return wxewm;
	}
	public void setWxewm(String wxewm) {
		this.wxewm = wxewm;
	}
	public String getScsj() {
		return scsj;
	}
	public void setScsj(String scsj) {
		this.scsj = scsj;
	}
	public String getUrl() {
		return url;
	}
	public void setUrl(String url) {
		this.url = url;
	}
	
	public String toString() {
		return JSON.toJSONString(this) ;
	}
}
