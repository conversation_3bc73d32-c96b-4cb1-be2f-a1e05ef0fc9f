package com.abujlb.zdcj8.bean.sjs;

import com.alibaba.fastjson.JSON;

import net.sf.json.JSONObject;

/**
 * 竞品规格销售比例 详情数据
 * 
 * <AUTHOR>
 * @date 2020-11-18
 */
public class JpggxsblJlData {

	private String key;
	private String value;

	public JpggxsblJlData() {
		
	}
	public JpggxsblJlData(JSONObject jsonObject) {
		this.key = jsonObject.getString("color");
		this.value = jsonObject.getString("colorValue");
	}

	public String getKey() {
		return key;
	}

	public void setKey(String key) {
		this.key = key;
	}

	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}
