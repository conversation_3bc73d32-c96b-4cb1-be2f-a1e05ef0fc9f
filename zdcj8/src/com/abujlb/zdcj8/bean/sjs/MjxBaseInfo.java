package com.abujlb.zdcj8.bean.sjs;


import com.alibaba.fastjson.JSON;

import net.sf.json.JSONObject;

/**
 * 买家秀基本信息
 * 
 * <AUTHOR>
 * @date 2020-11-19 9:20:24
 */
public class MjxBaseInfo {

	//总评价数feedAllCount
	private String total ;
	//折叠评价数foldCount
	private String fold ;
	//带图评价feedPicCount
	private String pic ;
	//视频评价feedVideoCount
	private String video ;
	//追评feedAppendCount
	private String append ;
	//好评数
	private String good;
	//中评数
	private String normal ;
	//差评数
	private String bad ;
	//总页数
	private String totalPage ;
	
	public MjxBaseInfo() {
		
	}
	
	public MjxBaseInfo(JSONObject jsonObject) {
		this.total = jsonObject.getString("feedAllCount");
		this.fold = jsonObject.getString("foldCount");
		this.pic = jsonObject.getString("feedPicCount");
		this.video = jsonObject.getString("feedVideoCount");
		this.append = jsonObject.getString("feedAppendCount");
		this.good = jsonObject.getString("feedGoodCount");
		this.normal = jsonObject.getString("feedNormalCount");
		this.bad = jsonObject.getString("feedBadCount");
		this.setTotalPage(jsonObject.getString("subtotalPage"));
	}
	public String getTotal() {
		return total;
	}


	public void setTotal(String total) {
		this.total = total;
	}


	public String getFold() {
		return fold;
	}


	public void setFold(String fold) {
		this.fold = fold;
	}


	public String getPic() {
		return pic;
	}


	public void setPic(String pic) {
		this.pic = pic;
	}


	public String getVideo() {
		return video;
	}


	public void setVideo(String video) {
		this.video = video;
	}


	public String getAppend() {
		return append;
	}


	public void setAppend(String append) {
		this.append = append;
	}


	public String getGood() {
		return good;
	}


	public void setGood(String good) {
		this.good = good;
	}


	public String getNormal() {
		return normal;
	}


	public void setNormal(String normal) {
		this.normal = normal;
	}


	public String getBad() {
		return bad;
	}


	public void setBad(String bad) {
		this.bad = bad;
	}


	public String getTotalPage() {
		return totalPage;
	}

	public void setTotalPage(String totalPage) {
		this.totalPage = totalPage;
	}
	
	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}
