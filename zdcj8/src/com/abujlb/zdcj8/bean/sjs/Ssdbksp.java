package com.abujlb.zdcj8.bean.sjs;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.abujlb.Result;
import com.abujlb.gson.Json2Object;
import com.abujlb.util.DateUtil;
import com.abujlb.zdcj8.bean.ZhSjs;
import com.abujlb.zdcj8.http.SjsHttpClient;
import com.abujlb.zdcj8.http.SjsUploader;
import com.aliyun.openservices.shade.com.alibaba.fastjson.JSON;

/**
 * <AUTHOR>
 * @date 2020-11-16
 */
@Component
public class Ssdbksp {

	@Autowired
	private SjsHttpClient sjsHttpClient;
	@Autowired
	private SjsUploader sjsUploader;

	/**
	 * 霸道版
	 * 
	 * @param zhSjs
	 * @param data
	 * @return
	 */
	public Result bd(ZhSjs zhSjs, String data) {
		Result result = new Result();
		SsdbkspBdbInput param = Json2Object.parseObj(data, SsdbkspBdbInput.class);
		String[] gjcArr = param.getGjc().split(",");
		String[] wwArr = param.getWw().split(",");
		String[] wwArrNew = wwArr.length > 9 ? new String[9] : new String[wwArr.length];
		for (int i = 0; i < wwArr.length; i++) {
			// 最多10个
			if (i > 9) {
				break;
			}
			wwArrNew[i] = wwArr[i];
		}
		String zxsj = DateUtil.formatDate(new Date(), "yyyy/MM/dd HH:mm:ss");
		String ww = StringUtils.join(wwArrNew, ",");
		List<SsdbkspBdb> list = new ArrayList<>();
		for (int i = 0; i < gjcArr.length; i++) {
			SsdbkspBdbInput input = new SsdbkspBdbInput(param.getBbid(), ww, gjcArr[i]);
			result = sjsHttpClient.sskspbd(zhSjs, input);
			if (result == null || !result.isSuccess()) {
				result = new Result(101, list.isEmpty() ? "扣费接口错误！" : JSON.toJSONString(list));
				sjsUploader.finish(zhSjs, false);
				return result;
			}

			for (int j = 0; j < wwArrNew.length; j++) {
				list.add(new SsdbkspBdb(param.getBbid(), wwArrNew[j], gjcArr[i], zxsj));
			}
		}

		result.setCode(Result.SUCCESS);
		result.setContent(JSON.toJSONString(list));
		return result;
	}
	
	/**
	 * 属性版
	 * 
	 * @param zhSjs
	 * @param data
	 * @return
	 */
	public Result sx(ZhSjs zhSjs, String data) {
//		Result result = new Result();
//		SsdbkspSxbInput param = Json2Object.parseObj(data, SsdbkspSxbInput.class);
//		String[] gjcArr = param.getGjc().split(",");
//		String[] wwArr = param.getWw().split(",");
//		String[] wwArrNew = wwArr.length > 9 ? new String[9] : new String[wwArr.length];
//		for (int i = 0; i < wwArr.length; i++) {
//			// 最多10个
//			if (i > 9) {
//				break;
//			}
//			wwArrNew[i] = wwArr[i];
//		}
//		String zxsj = DateUtil.formatDate(new Date(), "yyyy/MM/dd HH:mm:ss");
//		String ww = StringUtils.join(wwArrNew, ",");
//		List<SsdbkspBdb> list = new ArrayList<>();
//		for (int i = 0; i < gjcArr.length; i++) {
//			SsdbkspBdbInput input = new SsdbkspBdbInput(param.getBbid(), ww, gjcArr[i]);
//			result = sjsHttpClient.sskspbd(zhSjs, input);
//			if (result == null || !result.isSuccess()) {
//				result = new Result(101, list.isEmpty() ? "扣费接口错误！" : JSON.toJSONString(list));
//				sjsUploader.finish(zhSjs, false);
//				return result;
//			}
//
//			for (int j = 0; j < wwArrNew.length; j++) {
//				list.add(new SsdbkspBdb(param.getBbid(), wwArrNew[j], gjcArr[i], zxsj));
//			}
//		}
//
//		result.setCode(Result.SUCCESS);
//		result.setContent(JSON.toJSONString(list));
//		return result;
		return null;
	}
	
	
}
