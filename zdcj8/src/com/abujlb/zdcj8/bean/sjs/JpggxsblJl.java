package com.abujlb.zdcj8.bean.sjs;

import com.alibaba.fastjson.JSON;

import net.sf.json.JSONObject;

/**
 * 竞品规格销售比例 记录实体
 * 
 * <AUTHOR>
 * @date 2020-11-18
 */
public class JpggxsblJl {
	//id
	private int id;
	//创建时间
	private String cjsj;
	//宝贝id
	private String nid;
	//uuid
	private String uuid;
	//总页数
	private String totalPage;
	public JpggxsblJl() {
		
	}
	public JpggxsblJl(JSONObject jsonObject) {
		this.id = jsonObject.getInt("id");
		this.cjsj = jsonObject.getString("createTimeStr");
		this.nid = jsonObject.getString("nid");
		this.uuid = jsonObject.getString("uuid");
		this.totalPage = jsonObject.getString("subtotalPage") ;
	}
	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public String getCjsj() {
		return cjsj;
	}

	public void setCjsj(String cjsj) {
		this.cjsj = cjsj;
	}

	public String getNid() {
		return nid;
	}

	public void setNid(String nid) {
		this.nid = nid;
	}

	public String getUuid() {
		return uuid;
	}

	public void setUuid(String uuid) {
		this.uuid = uuid;
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
	public String getTotalPage() {
		return totalPage;
	}
	public void setTotalPage(String totalPage) {
		this.totalPage = totalPage;
	}
}
