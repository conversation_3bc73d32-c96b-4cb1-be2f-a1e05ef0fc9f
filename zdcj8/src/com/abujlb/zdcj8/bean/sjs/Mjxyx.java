package com.abujlb.zdcj8.bean.sjs;

import com.alibaba.fastjson.JSON;

import net.sf.json.JSONObject;

/**
 * 买家秀印象
 * 
 * <AUTHOR>
 * @date 2020-11-18
 */
public class Mjxyx {

	private String key;
	private String value;

	public Mjxyx() {
		
	}
	public Mjxyx(JSONObject jsonObject,int type) {
		if(type==1) {
			//印象词
			this.key = jsonObject.getString("word");
			this.value = jsonObject.getString("attribute");
		}
		if(type==2) {
			//规格
			this.key = jsonObject.getString("name");
			this.value = jsonObject.getString("vid");
		}
	}

	public String getKey() {
		return key;
	}

	public void setKey(String key) {
		this.key = key;
	}

	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}
