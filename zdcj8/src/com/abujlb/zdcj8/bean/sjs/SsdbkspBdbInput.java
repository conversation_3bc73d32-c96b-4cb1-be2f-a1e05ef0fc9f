package com.abujlb.zdcj8.bean.sjs;

import java.net.URLEncoder;

import com.alibaba.fastjson.JSON;

/**
 * 搜索打标卡首屏--霸道版入参
 * 
 * <AUTHOR>
 * @date 2020-11-16
 */
public class SsdbkspBdbInput {

	// 宝贝id
	private String bbid;
	// 旺旺，多个用逗号分割
	private String ww;
	// 关键词，多个用逗号分割
	private String gjc;

	public SsdbkspBdbInput() {
	}

	public SsdbkspBdbInput(String bbid, String ww, String gjc) {
		this.bbid = bbid;
		this.ww = ww;
		this.gjc = gjc;
	}

	public String getBbid() {
		return bbid;
	}

	public void setBbid(String bbid) {
		this.bbid = bbid;
	}

	public String getWw() {
		return ww;
	}

	public void setWw(String ww) {
		this.ww = ww;
	}

	public String getGjc() {
		return gjc;
	}

	public void setGjc(String gjc) {
		this.gjc = gjc;
	}

	public String getGjcUrlEncode() {
		try {
			return URLEncoder.encode(this.gjc, "utf-8");
		} catch (Exception e) {
			return "";
		}
	}

	public static  String getWwUrlEncode(String ww) {
		try {
			ww = ww.replaceAll(",", "____") ;
			String wwEncoded=URLEncoder.encode(ww, "utf-8") ;
			return wwEncoded.replaceAll("____", ",");
		} catch (Exception e) {
			return "";
		}
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
	
	public static void main(String[] args) {
		SsdbkspBdbInput input = new SsdbkspBdbInput ();
		input.setWw("happy丶mvp,yes_tb,哈哈");
		System.out.println(input.toString());
		
		
		SsdbkspBdbInput param =JSON.parseObject(input.toString(), SsdbkspBdbInput.class) ;
		System.out.println(param);
	}
}
