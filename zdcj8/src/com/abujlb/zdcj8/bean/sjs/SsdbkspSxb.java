package com.abujlb.zdcj8.bean.sjs;

import com.aliyun.openservices.shade.com.alibaba.fastjson.JSON;

/**
 * 搜索打标卡首屏--属性版
 * 
 * <AUTHOR>
 * @date 2020-11-16
 */
public class SsdbkspSxb {
	// 宝贝id
	private String tkl;
	// 关键词
	private String tbewm;
	// QQ微信短连接
	private String qqwxdlj;
	// 微信二维码
	private String wxewm;
	// 生成时间
	private String scsj;
	// 关键词
	private String gjc;
	// 宝贝链接
	private String bblj;

	public String getTkl() {
		return tkl;
	}

	public void setTkl(String tkl) {
		this.tkl = tkl;
	}

	public String getTbewm() {
		return tbewm;
	}

	public void setTbewm(String tbewm) {
		this.tbewm = tbewm;
	}

	public String getQqwxdlj() {
		return qqwxdlj;
	}

	public void setQqwxdlj(String qqwxdlj) {
		this.qqwxdlj = qqwxdlj;
	}

	public String getWxewm() {
		return wxewm;
	}

	public void setWxewm(String wxewm) {
		this.wxewm = wxewm;
	}

	public String getScsj() {
		return scsj;
	}

	public void setScsj(String scsj) {
		this.scsj = scsj;
	}

	public String getGjc() {
		return gjc;
	}

	public void setGjc(String gjc) {
		this.gjc = gjc;
	}

	public String getBblj() {
		return bblj;
	}

	public void setBblj(String bblj) {
		this.bblj = bblj;
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}
