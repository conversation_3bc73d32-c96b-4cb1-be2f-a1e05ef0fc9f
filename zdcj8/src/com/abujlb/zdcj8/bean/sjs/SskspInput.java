package com.abujlb.zdcj8.bean.sjs;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

import com.aliyun.openservices.shade.com.alibaba.fastjson.JSON;

/**
 * 搜索卡首屏 入参
* <AUTHOR>
* @date 2020-11-16
*/
public class SskspInput {

	//宝贝id
	private String bbid ;
	//关键词
	private String gjc ;
	
	public String getBbid() {
		return bbid;
	}
	public void setBbid(String bbid) {
		this.bbid = bbid;
	}
	public String getGjc() {
		return gjc;
	}
	public void setGjc(String gjc) {
		this.gjc = gjc;
	}
	public String getGjcUrlEncode() {
		try {
			return URLEncoder.encode(this.gjc, "utf-8");
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
			return "";
		}
	}
	@Override
	public String toString() {
		return JSON.toJSONString(this) ;
	}
	
	public static void main(String[] args) {
		SskspInput input = new SskspInput();
		input.setBbid("566829880906");
		input.setGjc("高配吃鸡");
		System.out.println(input.toString());
	}
}
