package com.abujlb.zdcj8.bean.sjs;

import java.util.List;

import com.alibaba.fastjson.JSON;

/**
 * 买家秀
 * 
 * <AUTHOR>
 * @date 2020-11-20
 */
public class Mjx {

	private MjxBaseInfo baseinfo;

	private List<MjxTp> list;

	public Mjx() {

	}

	public MjxBaseInfo getBaseinfo() {
		return baseinfo;
	}

	public void setBaseinfo(MjxBaseInfo baseinfo) {
		this.baseinfo = baseinfo;
	}

	public List<MjxTp> getList() {
		return list;
	}

	public void setList(List<MjxTp> list) {
		this.list = list;
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}

}
