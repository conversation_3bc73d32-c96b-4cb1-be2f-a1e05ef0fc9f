package com.abujlb.zdcj8.bean.sjs;

import com.alibaba.fastjson.JSON;

import net.sf.json.JSONObject;

/**
 * 买家秀图片视频
 * <AUTHOR>
 * @date 2020-11-20
 */
public class MjxTp {

	// 图片url
	private String url;
	// 内容
	private String content;

	public MjxTp() {

	}

	public MjxTp(JSONObject jsonObject) {
		this.url = jsonObject.getString("url");
		this.content = jsonObject.getString("content");
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}
