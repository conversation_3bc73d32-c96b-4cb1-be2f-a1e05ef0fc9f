package com.abujlb.zdcj8.bean.sjs;

import com.aliyun.openservices.shade.com.alibaba.fastjson.JSON;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

/**
 * 数据蛇 搜索秒单
 * 
 * <AUTHOR>
 * @date 2020-11-13
 */
public class Ssmd {

	// 宝贝id
	private String bbid;
	// 关键词
	private String gjc;
	// 旺旺
	private String ww;
	// 二维码
	private String ewm;
	// 生成时间
	private String scsj;

	public Ssmd() {

	}

	public Ssmd(JSONArray jsonArray) {
		JSONObject jsonObj = jsonArray.getJSONObject(0);
		this.bbid = jsonObj.getString("nid");
		this.gjc = jsonObj.getString("title");
		this.ww = jsonObj.getString("nick");
		this.ewm = jsonObj.getString("tkl");
		this.scsj = jsonObj.getString("createTimeStr");

	}

	public String getBbid() {
		return bbid;
	}

	public void setBbid(String bbid) {
		this.bbid = bbid;
	}

	public String getGjc() {
		return gjc;
	}

	public void setGjc(String gjc) {
		this.gjc = gjc;
	}

	public String getWw() {
		return ww;
	}

	public void setWw(String ww) {
		this.ww = ww;
	}

	public String getEwm() {
		return ewm;
	}

	public void setEwm(String ewm) {
		this.ewm = ewm;
	}

	public String getScsj() {
		return scsj;
	}

	public void setScsj(String scsj) {
		this.scsj = scsj;
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}
