package com.abujlb.zdcj8.bean.sjs;

import com.aliyun.openservices.shade.com.alibaba.fastjson.JSON;

/**
 * 搜索打标卡首屏--霸道版
 * <AUTHOR>
 * @date 2020-11-16
 */
public class SsdbkspBdb {

	// 宝贝id
	private String bbid;
	// 旺旺
	private String ww;
	// 关键词
	private String gjc;
	// 执行时间
	private String zxsj ;
	
	public SsdbkspBdb() {
		
	}
	public SsdbkspBdb(String bbid, String ww, String gjc, String zxsj) {
		this.bbid = bbid;
		this.ww = ww;
		this.gjc = gjc;
		this.zxsj = zxsj;
	}

	public String getBbid() {
		return bbid;
	}

	public void setBbid(String bbid) {
		this.bbid = bbid;
	}

	public String getWw() {
		return ww;
	}

	public void setWw(String ww) {
		this.ww = ww;
	}

	public String getGjc() {
		return gjc;
	}

	public void setGjc(String gjc) {
		this.gjc = gjc;
	}

	public String getZxsj() {
		return zxsj;
	}

	public void setZxsj(String zxsj) {
		this.zxsj = zxsj;
	}
	
	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}
