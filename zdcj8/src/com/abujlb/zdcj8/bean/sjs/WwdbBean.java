package com.abujlb.zdcj8.bean.sjs;

import com.aliyun.openservices.shade.com.alibaba.fastjson.JSON;

/**
 * Wwdb数据蛇 旺旺打标
 * 
 * <AUTHOR>
 * @date 2022-08-08 16:55:52
 */
public class WwdbBean {

	private String nid; // 宝贝id
	private String title; // 标题
	private String nick; // 旺旺名称

	public WwdbBean() {

	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}

	public String getNid() {
		return nid;
	}

	public void setNid(String nid) {
		this.nid = nid;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getNick() {
		return nick;
	}

	public void setNick(String nick) {
		this.nick = nick;
	}
	
}
