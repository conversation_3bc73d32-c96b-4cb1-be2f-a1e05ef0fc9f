package com.abujlb.zdcj8.bean.sjs;

import com.abujlb.util.DateUtil;
import com.aliyun.openservices.shade.com.alibaba.fastjson.JSON;

import net.sf.json.JSONObject;

/**
 * 数据蛇 淘口令生成
 * 
 * <AUTHOR>
 * @date 2020-11-13
 */
public class Tkl {

	// 淘口令
	private String tkl;
	// 短网址
	private String dwz;
	// 原始链接
	private String yslj;
	// 采集时间 格式yyyy/MM/dd HH:mm:ss
	private String cjsj;

	public Tkl() {

	}

	public Tkl(JSONObject jsonObject, String url) {
		if (jsonObject.has("tkl")) {
			this.dwz = jsonObject.getString("tblink");
		}
		if (jsonObject.has("tblink")) {
			this.dwz = jsonObject.getString("tblink");
		}
		this.yslj = url;
		this.cjsj = DateUtil.formatDate(DateUtil.getCurrentTime(), "yyyy/MM/dd HH:mm:ss");
	}

	public String getTkl() {
		return tkl;
	}

	public void setTkl(String tkl) {
		this.tkl = tkl;
	}

	public String getDwz() {
		return dwz;
	}

	public void setDwz(String dwz) {
		this.dwz = dwz;
	}

	public String getYslj() {
		return yslj;
	}

	public void setYslj(String yslj) {
		this.yslj = yslj;
	}

	public String getCjsj() {
		return cjsj;
	}

	public void setCjsj(String cjsj) {
		this.cjsj = cjsj;
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}
