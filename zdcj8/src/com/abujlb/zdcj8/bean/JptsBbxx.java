package com.abujlb.zdcj8.bean;

import com.abujlb.zdcj8.util.StrUtil;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

import net.sf.json.JSONObject;

/**
 * 超级竞品透视 宝贝综合数据
 * 
 * <AUTHOR>
 * @date 2020-10-22 11:22:04
 */
public class JptsBbxx {
	
	private String tjrq; // 统计日期
	private String itemid; // 商品id
	private String itemmc; // 商品名称
	
	private double fks; // fks 访客数，例如：25357.708，是小数，数据蛇展示时进行四舍五入取整
	private double zfzhl; // zfzhl 支付转化率，例如0.0245768，代表2.46%
	private double gmrs; // gmrs 购买人数，例如：623.2113179744，是小数，数据蛇展示时进行四舍五入取整
	private double zfjs; // zfjs 支付件数、购买件数，例如：639.195，是小数，数据蛇展示时进行四舍五入取整
	private double zfje; // zfje 支付金额、销售金额，例如：82611.46452
	private double kdj; // kdj 客单价，例如：132.95
	private double kdjJs; // kdjJs 客单价件数，例如：134.38183，比kdj稍大，数据蛇是按照kdjJs作为客单价展示
	private double uv; // uv UV价值，例如：3.25784
	private double spscmjs; // spscmjs 商品收藏买家数、收藏量，例如：1710.466
	private double sclv; // sclv 收藏率，例如：0.06745，代表6.75%
	private double jgjs; // jgjs 加购件数、加购量，例如：2385.337
	private double jglv; // jglv 加购率，0.0954，代表9.54%
	private double tklv; // 退款率，计算得来，用cgtkthje除以zfje得到，
	
	private double cgtkthje; // cgtkthje采购退款退货金额，例如：26726.03
	private int pcdbgl; // pcdbgl PC端曝光量，例如：8758
	private int pcddjcs; // pcddjcs PC端点击次数，例如：167
	private String pcdzfzhl; // pcdzfzhl PC端支付转化率，例如：1.43%
	private double pjtlsc; // pjtlsc 评价停留时长，估计单位为秒，例如：8.64
	private int wxdzfmjs; // wxdzfmjs 无线端支付买家数，例如：631
	private String wxdzfzhl; // wxdzfzhl 无线端支付转化率，例如：2.49%
	private int wxfks; // wxfks 无线访客数，例如：25385
	private double wxzfje; // wxzfje 无线支付金额，例如：82810.52
	private String xqytcl; // xqytcl 详情页跳出量，例如：58.82%
	
	public JptsBbxx() {
		
	}
	
	public JptsBbxx(String tjrq, String itemid, String itemmc) {
		this.tjrq = tjrq; // 统计日期
		this.itemid = itemid; // 商品id
		this.itemmc = itemmc; // 商品名称
	}
	
	public JptsBbxx(JSONObject obj) {
		try {
			this.tjrq = ""; // 统计日期
			if (obj.has("tjrq") && !StrUtil.isNull(obj.getString("tjrq"))) {
				this.tjrq = obj.getString("tjrq");
			}
			this.itemid = ""; // 商品id
			if (obj.has("spid") && !StrUtil.isNull(obj.getString("spid"))) {
				this.itemid = obj.getString("spid");
			}
			this.itemmc = ""; // 商品名称
			if (obj.has("spmc") && !StrUtil.isNull(obj.getString("spmc"))) {
				this.itemmc = obj.getString("spmc");
			}
			this.fks = 0D; // fks 访客数，例如：25357.708，是小数，数据蛇展示时进行四舍五入取整
			if (obj.has("fks") && !StrUtil.isNull(obj.getString("fks"))) {
				this.fks = obj.getDouble("fks");
			}
			this.zfzhl = 0D; // zfzhl 支付转化率，例如0.0245768，代表2.46%
			if (obj.has("zfzhl") && !StrUtil.isNull(obj.getString("zfzhl"))) {
				this.zfzhl = obj.getDouble("zfzhl");
			}
			this.gmrs = 0D; // gmrs 购买人数，例如：623.2113179744，是小数，数据蛇展示时进行四舍五入取整
			if (obj.has("gmrs") && !StrUtil.isNull(obj.getString("gmrs"))) {
				this.gmrs = obj.getDouble("gmrs");
			}
			this.zfjs = 0D; // zfjs 支付件数、购买件数，例如：639.195，是小数，数据蛇展示时进行四舍五入取整
			if (obj.has("zfjs") && !StrUtil.isNull(obj.getString("zfjs"))) {
				this.zfjs = obj.getDouble("zfjs");
			}
			this.zfje = 0D; // zfje 支付金额、销售金额，例如：82611.46452
			if (obj.has("zfje") && !StrUtil.isNull(obj.getString("zfje"))) {
				this.zfje = obj.getDouble("zfje");
			}
			this.kdj = 0D; // kdj 客单价，例如：132.95
			if (obj.has("kdj") && !StrUtil.isNull(obj.getString("kdj"))) {
				this.kdj = obj.getDouble("kdj");
			}
			this.kdjJs = 0D; // kdjJs 客单价件数，例如：134.38183，比kdj稍大，数据蛇是按照kdjJs作为客单价展示
			if (obj.has("kdjJs") && !StrUtil.isNull(obj.getString("kdjJs"))) {
				this.kdjJs = obj.getDouble("kdjJs");
			}
			this.uv = 0D; // uv UV价值，例如：3.25784
			if (obj.has("uv") && !StrUtil.isNull(obj.getString("uv"))) {
				this.uv = obj.getDouble("uv");
			}
			this.spscmjs = 0D; // spscmjs 商品收藏买家数、收藏量，例如：1710.466
			if (obj.has("spscmjs") && !StrUtil.isNull(obj.getString("spscmjs"))) {
				this.spscmjs = obj.getDouble("spscmjs");
			}
			this.sclv = 0D; // sclv 收藏率，例如：0.06745，代表6.75%
			if (obj.has("sclv") && !StrUtil.isNull(obj.getString("sclv"))) {
				this.sclv = obj.getDouble("sclv");
			}
			this.jgjs = 0D; // jgjs 加购件数、加购量，例如：2385.337
			if (obj.has("jgjs") && !StrUtil.isNull(obj.getString("jgjs"))) {
				this.jgjs = obj.getDouble("jgjs");
			}
			this.jglv = 0D; // jglv 加购率，0.0954，代表9.54%
			if (obj.has("jglv") && !StrUtil.isNull(obj.getString("jglv"))) {
				this.jglv = obj.getDouble("jglv");
			}
			
			this.cgtkthje = 0D; // cgtkthje采购退款退货金额，例如：26726.03
			if (obj.has("cgtkthje") && !StrUtil.isNull(obj.getString("cgtkthje"))) {
				this.cgtkthje = obj.getDouble("cgtkthje");
			}
			this.pcdbgl = 0; // pcdbgl PC端曝光量，例如：8758
			if (obj.has("pcdbgl") && !StrUtil.isNull(obj.getString("pcdbgl"))) {
				this.pcdbgl = obj.getInt("pcdbgl");
			}
			this.pcddjcs = 0; // pcddjcs PC端点击次数，例如：167
			if (obj.has("pcddjcs") && !StrUtil.isNull(obj.getString("pcddjcs"))) {
				this.pcddjcs = obj.getInt("pcddjcs");
			}
			this.pcdzfzhl = ""; // pcdzfzhl PC端支付转化率，例如：1.43%
			if (obj.has("pcdzfzhl") && !StrUtil.isNull(obj.getString("pcdzfzhl"))) {
				this.pcdzfzhl = obj.getString("pcdzfzhl");
			}
			this.pjtlsc = 0D; // pjtlsc 评价停留时长，估计单位为秒，例如：8.64
			if (obj.has("pjtlsc") && !StrUtil.isNull(obj.getString("pjtlsc"))) {
				this.pjtlsc = obj.getDouble("pjtlsc");
			}
			this.wxdzfmjs = 0; // wxdzfmjs 无线端支付买家数，例如：631
			if (obj.has("wxdzfmjs") && !StrUtil.isNull(obj.getString("wxdzfmjs"))) {
				this.wxdzfmjs = obj.getInt("wxdzfmjs");
			}
			this.wxdzfzhl = ""; // wxdzfzhl 无线端支付转化率，例如：2.49%
			if (obj.has("wxdzfzhl") && !StrUtil.isNull(obj.getString("wxdzfzhl"))) {
				this.wxdzfzhl = obj.getString("wxdzfzhl");
			}
			this.wxfks = 0; // wxfks 无线访客数，例如：25385
			if (obj.has("wxfks") && !StrUtil.isNull(obj.getString("wxfks"))) {
				this.wxfks = obj.getInt("wxfks");
			}
			this.wxzfje = 0D; // wxzfje 无线支付金额，例如：82810.52
			if (obj.has("wxzfje") && !StrUtil.isNull(obj.getString("wxzfje"))) {
				this.wxzfje = obj.getDouble("wxzfje");
			}
			this.xqytcl = ""; // xqytcl 详情页跳出量，例如：58.82%
			if (obj.has("xqytcl") && !StrUtil.isNull(obj.getString("xqytcl"))) {
				this.xqytcl = obj.getString("xqytcl");
			}
			this.tklv = 0D; // 退款率，计算得来，用cgtkthje除以zfje得到，
			if (this.zfje > 0) {
				this.tklv = this.cgtkthje / this.zfje;
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	public String toString() {
		Gson gson = new GsonBuilder().setDateFormat("yyyy-MM-dd HH:mm:ss").create();
		return gson.toJson(this);
	}

	public String getTjrq() {
		return tjrq;
	}

	public void setTjrq(String tjrq) {
		this.tjrq = tjrq;
	}

	public String getItemid() {
		return itemid;
	}

	public void setItemid(String itemid) {
		this.itemid = itemid;
	}

	public String getItemmc() {
		return itemmc;
	}

	public void setItemmc(String itemmc) {
		this.itemmc = itemmc;
	}

	public double getFks() {
		return fks;
	}

	public void setFks(double fks) {
		this.fks = fks;
	}

	public double getZfzhl() {
		return zfzhl;
	}

	public void setZfzhl(double zfzhl) {
		this.zfzhl = zfzhl;
	}

	public double getGmrs() {
		return gmrs;
	}

	public void setGmrs(double gmrs) {
		this.gmrs = gmrs;
	}

	public double getZfjs() {
		return zfjs;
	}

	public void setZfjs(double zfjs) {
		this.zfjs = zfjs;
	}

	public double getZfje() {
		return zfje;
	}

	public void setZfje(double zfje) {
		this.zfje = zfje;
	}

	public double getKdj() {
		return kdj;
	}

	public void setKdj(double kdj) {
		this.kdj = kdj;
	}

	public double getKdjJs() {
		return kdjJs;
	}

	public void setKdjJs(double kdjJs) {
		this.kdjJs = kdjJs;
	}

	public double getUv() {
		return uv;
	}

	public void setUv(double uv) {
		this.uv = uv;
	}

	public double getSpscmjs() {
		return spscmjs;
	}

	public void setSpscmjs(double spscmjs) {
		this.spscmjs = spscmjs;
	}

	public double getSclv() {
		return sclv;
	}

	public void setSclv(double sclv) {
		this.sclv = sclv;
	}

	public double getJgjs() {
		return jgjs;
	}

	public void setJgjs(double jgjs) {
		this.jgjs = jgjs;
	}

	public double getJglv() {
		return jglv;
	}

	public void setJglv(double jglv) {
		this.jglv = jglv;
	}

	public double getTklv() {
		return tklv;
	}

	public void setTklv(double tklv) {
		this.tklv = tklv;
	}

	public double getCgtkthje() {
		return cgtkthje;
	}

	public void setCgtkthje(double cgtkthje) {
		this.cgtkthje = cgtkthje;
	}

	public int getPcdbgl() {
		return pcdbgl;
	}

	public void setPcdbgl(int pcdbgl) {
		this.pcdbgl = pcdbgl;
	}

	public int getPcddjcs() {
		return pcddjcs;
	}

	public void setPcddjcs(int pcddjcs) {
		this.pcddjcs = pcddjcs;
	}

	public String getPcdzfzhl() {
		return pcdzfzhl;
	}

	public void setPcdzfzhl(String pcdzfzhl) {
		this.pcdzfzhl = pcdzfzhl;
	}

	public double getPjtlsc() {
		return pjtlsc;
	}

	public void setPjtlsc(double pjtlsc) {
		this.pjtlsc = pjtlsc;
	}

	public int getWxdzfmjs() {
		return wxdzfmjs;
	}

	public void setWxdzfmjs(int wxdzfmjs) {
		this.wxdzfmjs = wxdzfmjs;
	}

	public String getWxdzfzhl() {
		return wxdzfzhl;
	}

	public void setWxdzfzhl(String wxdzfzhl) {
		this.wxdzfzhl = wxdzfzhl;
	}

	public int getWxfks() {
		return wxfks;
	}

	public void setWxfks(int wxfks) {
		this.wxfks = wxfks;
	}

	public double getWxzfje() {
		return wxzfje;
	}

	public void setWxzfje(double wxzfje) {
		this.wxzfje = wxzfje;
	}

	public String getXqytcl() {
		return xqytcl;
	}

	public void setXqytcl(String xqytcl) {
		this.xqytcl = xqytcl;
	}
	
}
