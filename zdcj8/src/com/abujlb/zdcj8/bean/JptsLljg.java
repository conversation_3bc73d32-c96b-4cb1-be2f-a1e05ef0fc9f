package com.abujlb.zdcj8.bean;

import com.abujlb.zdcj8.util.StrUtil;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

import net.sf.json.JSONObject;

/**
 * 超级竞品透视 流量结构
 * 
 * <AUTHOR>
 * @date 2020-10-22 11:02:36
 */
public class JptsLljg {

	/************* 数据类型 *************/
	public static final String JPTS_GJC = "gjc"; // 竞品关键词数据(无线端)
	public static final String JPTS_QD = "qd"; // 竞品渠道数据
	public static final String JPTS_ZTC = "ztc"; // 竞品直通车词数据(无线端)

	private String name; // 关键词或名称，pageName
	private String zfzhl; // 支付转化率payRateIndex，例如：2.68%，有可能是空字符“”
	private String fks; // 访客数uvIndex，例如：15443，有可能是空字符“”
	private String zfmjs; // 支付买家数zfmjs，例如：413.8724、0.0，是小数，数据蛇展示时进行四舍五入取整

	// private long id; //
	// private String device; //
	// private String pid; // 即uuid
	// private String type; // 数据类型，例如：竞品关键词数据、竞品渠道数据、竞品直通车词数据

	public JptsLljg() {

	}

	public JptsLljg(String name, String zfzhl, String fks, String zfmjs) {
		this.name = name; // 关键词或名称
		this.zfzhl = zfzhl; // 支付转化率payRateIndex
		this.fks = fks; // 访客数uvIndex
		this.zfmjs = zfmjs; // 支付买家数zfmjs
	}
	
	public JptsLljg(JSONObject obj) {
		try {
			this.name = ""; // 关键词或名称
			if (obj.has("pageName") && !StrUtil.isNull(obj.getString("pageName"))) {
				this.name = obj.getString("pageName");
			}
			this.zfzhl = ""; // 支付转化率payRateIndex
			if (obj.has("payRateIndex") && !StrUtil.isNull(obj.getString("payRateIndex"))) {
				this.zfzhl = obj.getString("payRateIndex");
			}
			this.fks = ""; // 访客数uvIndex
			if (obj.has("uvIndex") && !StrUtil.isNull(obj.getString("uvIndex"))) {
				this.fks = obj.getString("uvIndex");
			}
			this.zfmjs = ""; // 支付买家数zfmjs
			if (obj.has("zfmjs") && !StrUtil.isNull(obj.getString("zfmjs"))) {
				this.zfmjs = obj.getString("zfmjs");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public String toString() {
		Gson gson = new GsonBuilder().setDateFormat("yyyy-MM-dd HH:mm:ss").create();
		return gson.toJson(this);
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getZfzhl() {
		return zfzhl;
	}

	public void setZfzhl(String zfzhl) {
		this.zfzhl = zfzhl;
	}

	public String getFks() {
		return fks;
	}

	public void setFks(String fks) {
		this.fks = fks;
	}

	public String getZfmjs() {
		return zfmjs;
	}

	public void setZfmjs(String zfmjs) {
		this.zfmjs = zfmjs;
	}

}
