package com.abujlb.zdcj8.bean;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.abujlb.BaseBean;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

import net.sf.json.JSONObject;

public class Cjzhxx extends BaseBean {
	private static Gson gson = new GsonBuilder().setDateFormat("yyyy-MM-dd HH:mm:ss").create();

	private String zhuzh;
	private String zzh;
	private int dpid;
	private String dpmc;
	private int jlbdpid;
	private Date logintime;
	private Date lastuse;
	private int usetimes;
	private transient String timesMap;
	private List<TimesMap> timesMaps;
	private int success;
	private int error;
	private String successlast;
	private String sfsx;
	private Date gxsj;
	private transient String dateRange1;
	private transient String dateRange2;

	public String getZhuzh() {
		return zhuzh;
	}

	public void setZhuzh(String zhuzh) {
		this.zhuzh = zhuzh;
	}

	public String getZzh() {
		return zzh;
	}

	public void setZzh(String zzh) {
		this.zzh = zzh;
	}

	public int getDpid() {
		return dpid;
	}

	public void setDpid(int dpid) {
		this.dpid = dpid;
	}

	public String getDpmc() {
		return dpmc;
	}

	public void setDpmc(String dpmc) {
		this.dpmc = dpmc;
	}

	public int getJlbdpid() {
		return jlbdpid;
	}

	public void setJlbdpid(int jlbdpid) {
		this.jlbdpid = jlbdpid;
	}

	public Date getLogintime() {
		return logintime;
	}

	public void setLogintime(Date logintime) {
		this.logintime = logintime;
	}

	public Date getLastuse() {
		return lastuse;
	}

	public void setLastuse(Date lastuse) {
		this.lastuse = lastuse;
	}

	public int getUsetimes() {
		return usetimes;
	}

	public void setUsetimes(int usetimes) {
		this.usetimes = usetimes;
	}

	public int getSuccess() {
		return success;
	}

	public void setSuccess(int success) {
		this.success = success;
	}

	public int getError() {
		return error;
	}

	public void setError(int error) {
		this.error = error;
	}

	public String getSuccesslast() {
		return successlast;
	}

	public void setSuccesslast(String successlast) {
		this.successlast = successlast;
	}

	public String getSfsx() {
		return sfsx;
	}

	public void setSfsx(String sfsx) {
		this.sfsx = sfsx;
	}

	public Date getGxsj() {
		return gxsj;
	}

	public void setGxsj(Date gxsj) {
		this.gxsj = gxsj;
	}

	public List<TimesMap> getTimesMaps() {

		return this.timesMaps;
	}

	public void setTimesMaps(List<TimesMap> timesMaps) {
		this.timesMaps = timesMaps;
	}

	public String getTimesMap() {
		return timesMap;
	}

	public void setTimesMap(String timesMap) {
		this.timesMaps = new ArrayList<TimesMap>();
		String[] zjs = new String[] { "rsc", "pp", "hydp", "uvjzfx", "btyh", "dpts", "dpph" };
		if (timesMap.contentEquals("")) {
			return;
		}
		JSONObject obj = null;
		try {

			obj = JSONObject.fromObject(timesMap);
			for (String key : zjs) {
				if (obj.has(key)) {
					JSONObject o = obj.getJSONObject(key);
					TimesMap tm = new TimesMap(key, o.getInt("success"), o.getInt("error"));
					this.timesMaps.add(tm);
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public String getDateRange1() {
		return dateRange1;
	}

	public void setDateRange1(String dateRange1) {
		this.dateRange1 = dateRange1;
	}

	public String getDateRange2() {
		return dateRange2;
	}

	public void setDateRange2(String dateRange2) {
		this.dateRange2 = dateRange2;
	}

	public String toString() {
		return gson.toJson(this);
	}

}
