package com.abujlb.zdcj8.bean;

import com.abujlb.util.DateUtil;
import com.abujlb.zdcj8.util.StrUtil;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

/**
 * 竞品sku销量 实体
 * 
 * <AUTHOR>
 * @date 2020-09-18 09:36:21
 */
public class SkuSale implements Comparable<SkuSale> {
	
	/************* 日期类型 *************/
	public static final String RQLX_DAY = "day"; // 日期类型：day.按天、recent7.最近7天、recent30.最近30天
	public static final String RQLX_RECENT7 = "recent7";
	public static final String RQLX_RECENT30 = "recent30";
	
	private String tjrq; // 统计日期
	private String itemid; // 商品id
	private String itemmc; // 商品名称
	private String skuid; //
	private String skumc; // sku名称
	private int jgjs; // 加购件数
	private int xdjs; // 下单件数
	private double xdje; // 下单金额
	private int xdmjs; // 下单买家数
	private int zfjs; // 支付件数
	private double zfje; // 支付金额
	private int zfmjs; // 支付买家数

	// private Date rq; // 采集日期，date，startDate，endDate都一样

	// public boolean after(SkuSale skuSale2) {
	// return skuSale2.getRq().after(rq);
	// }
	
	public SkuSale() {
		
	}
	
	public SkuSale(String tjrq, String itemid, String itemmc, String skuid, String skumc, int jgjs, int xdjs, double xdje, int xdmjs, int zfjs, double zfje, int zfmjs) {
		this.tjrq = tjrq; // 统计日期
		this.itemid = itemid; // 商品id
		this.itemmc = itemmc; // 商品名称
		this.skuid = skuid; //
		this.skumc = skumc; // sku名称
		this.jgjs = jgjs; // 加购件数
		this.xdjs = xdjs; // 下单件数
		this.xdje = xdje; // 下单金额
		this.xdmjs = xdmjs; // 下单买家数
		this.zfjs = zfjs; // 支付件数
		this.zfje = zfje; // 支付金额
		this.zfmjs = zfmjs; // 支付买家数
	}
	
	public String toString() {
		Gson gson = new GsonBuilder().setDateFormat("yyyy-MM-dd HH:mm:ss").create();
		return gson.toJson(this);
	}
	
	@Override // 重写Comparable接口的compareTo方法，
	public int compareTo(SkuSale o) {
		if (!StrUtil.isNull(this.tjrq) && !StrUtil.isNull(o.getTjrq())) {
			if (this.tjrq.equals(o.getTjrq())) {
				return 0;
			} else {
				return DateUtil.parseDate(this.tjrq).getTime() >= DateUtil.parseDate(o.getTjrq()).getTime()?1:-1; // 按日期正序排序，即日期较大的在后面
			}
		}
		return 0;
		// return this.zfjs - user.getZfjs(); // 根据支付件数升序排列，降序修改相减顺序即可
	}

	public String getTjrq() {
		return tjrq;
	}

	public void setTjrq(String tjrq) {
		this.tjrq = tjrq;
	}

	public String getItemid() {
		return itemid;
	}

	public void setItemid(String itemid) {
		this.itemid = itemid;
	}

	public String getItemmc() {
		return itemmc;
	}

	public void setItemmc(String itemmc) {
		this.itemmc = itemmc;
	}

	public String getSkuid() {
		return skuid;
	}

	public void setSkuid(String skuid) {
		this.skuid = skuid;
	}

	public String getSkumc() {
		return skumc;
	}

	public void setSkumc(String skumc) {
		this.skumc = skumc;
	}

	public int getJgjs() {
		return jgjs;
	}

	public void setJgjs(int jgjs) {
		this.jgjs = jgjs;
	}

	public int getXdjs() {
		return xdjs;
	}

	public void setXdjs(int xdjs) {
		this.xdjs = xdjs;
	}

	public double getXdje() {
		return xdje;
	}

	public void setXdje(double xdje) {
		this.xdje = xdje;
	}

	public int getXdmjs() {
		return xdmjs;
	}

	public void setXdmjs(int xdmjs) {
		this.xdmjs = xdmjs;
	}

	public int getZfjs() {
		return zfjs;
	}

	public void setZfjs(int zfjs) {
		this.zfjs = zfjs;
	}

	public double getZfje() {
		return zfje;
	}

	public void setZfje(double zfje) {
		this.zfje = zfje;
	}

	public int getZfmjs() {
		return zfmjs;
	}

	public void setZfmjs(int zfmjs) {
		this.zfmjs = zfmjs;
	}

}
