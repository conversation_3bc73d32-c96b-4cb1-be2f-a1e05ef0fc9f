package com.abujlb.zdcj8.bean;

import java.util.List;

public class DptsGjcData implements java.lang.Comparable<DptsGjcData> {
	private String date;
	private List<DptsGjc> gjcList;

	@Override
	public int compareTo(DptsGjcData o) {
		return date.compareTo(o.getDate());
	}

	public String getDate() {
		return date;
	}

	public void setDate(String date) {
		this.date = date;
	}

	public List<DptsGjc> getGjcList() {
		return gjcList;
	}

	public void setGjcList(List<DptsGjc> gjcList) {
		this.gjcList = gjcList;
	}

}
