package com.abujlb.zdcj8.bean;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import com.abujlb.util.DateUtil;

public class Dpts3Para extends CheckPara {
	public static final String RECENT30 = "recent30";
	public static final String RECENT7 = "recent7";
	public static final String TODAY = "today";
	public static final String DAY = "day";

	private static Map<String, Integer> DATEMAP = new HashMap<String, Integer>();
	static {
		DATEMAP.put(TODAY, 1);
		DATEMAP.put(DAY, 1);
		DATEMAP.put(RECENT7, 7);
		DATEMAP.put(RECENT30, 30);
	}

	private String bbid;
	private String device;
	private String dateType;
	private String date;
	private String updateNDay;

	public String getDateRange() {
		if (dateType == null || !DATEMAP.containsKey(dateType)) {
			// dateType无效，则自动设置为最近30天
			dateType = RECENT30;
		}
		int days = DATEMAP.get(dateType);
		Date date2 = null;
		if (dateType.equalsIgnoreCase(TODAY)) {
			// 实时
			date2 = DateUtil.getCurrentTime();
		} else if (dateType.equalsIgnoreCase(DAY)) {
			date2 = DateUtil.parseDate(date);
		} else {
			date2 = DateUtil.parseDate(updateNDay);
		}
		Date date1 = DateUtil.getDateBefore(date2, days - 1);
		return DateUtil.format(date1) + "%7C" + DateUtil.format(date2);
	}

	public String getBbid() {
		return bbid;
	}

	public String getDevice() {
		return device == null ? "" : device;
	}

	public String getDateType() {
		return dateType;
	}

	public String getUpdateNDay() {
		return updateNDay == null ? DateUtil.format(DateUtil.getLastDay()) : updateNDay;
	}

	public void setBbid(String bbid) {
		this.bbid = bbid;
	}

	public void setDevice(String device) {
		this.device = device;
	}

	public void setDateType(String dateType) {
		this.dateType = dateType;
	}

	public void setUpdateNDay(String updateNDay) {
		this.updateNDay = updateNDay;
	}

	public String getDate() {
		return date == null ? "" : date;
	}

	public void setDate(String date) {
		this.date = date;
	}

	@Override
	public String checkString() {
		return String.format("%s%s%s%s", bbid, this.getDate(), dateType, this.getDevice());
	}

	public static void main(String[] args) {
		Dpts3Para dpts3Para = new Dpts3Para();
		dpts3Para.setDateType(DAY);
		dpts3Para.setDate("2020-07-09");
		System.out.println(dpts3Para.getDateRange());
		// System.out.println(dpts3Para.checkString());
	}
}
