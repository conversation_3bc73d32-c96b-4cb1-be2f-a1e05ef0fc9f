package com.abujlb.zdcj8.bean;

import com.google.gson.Gson;

/**
 * 生意参谋接口，类目bean表
 * 
 * <AUTHOR>
 * @date 2020-09-28 15:37:18
 */
public class CateInfo {

	/**
	 * @说明
	 * 		一级类目，pid为0，且id == topid，name == topname，cateFlag == 1
	 * 		二级类目，pid == topid，cateFlag == 2
	 * 		二级类目，cateFlag == 0
	 */
	private long pid; // 一级类目，pid为0
	private long id;
	private String name;
	private int cateFlag; // 类目类型：1.一级类目、2.二级类目、0.三级类目
	private String version; // 版本：pro.专业版、vip.豪华版、std.标准版
	private String yz; // 是否叶子类目：Y.是叶子类目、N.不是叶子类目
	private long topid; // 一级类目id
	private String topname; // 一级类目名称

	public CateInfo() {

	}

	public CateInfo(long pid, long id, String name, int cateFlag, String version, String yz, long topid, String topname) {
		this.pid = pid;
		this.id = id;
		this.name = name;
		this.cateFlag = cateFlag;
		this.version = version;
		this.yz = yz;
		this.topid = topid;
		this.topname = topname;
	}

	public String toString() {
		Gson gson = new Gson();
		return gson.toJson(this);
	}
	
	public boolean isOk() {
		if ("pro".equalsIgnoreCase(this.version)) { // 只采集专业版
			return true;
		}
		return false;
	}

	public long getPid() {
		return pid;
	}

	public void setPid(long pid) {
		this.pid = pid;
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public int getCateFlag() {
		return cateFlag;
	}

	public void setCateFlag(int cateFlag) {
		this.cateFlag = cateFlag;
	}

	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	public String getYz() {
		return yz;
	}

	public void setYz(String yz) {
		this.yz = yz;
	}

	public long getTopid() {
		return topid;
	}

	public void setTopid(long topid) {
		this.topid = topid;
	}

	public String getTopname() {
		return topname;
	}

	public void setTopname(String topname) {
		this.topname = topname;
	}

}
