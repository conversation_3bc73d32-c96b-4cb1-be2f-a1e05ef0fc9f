package com.abujlb.zdcj8.bean;

import com.abujlb.gson.SkipFieldExclusionStrategy;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

public class DpInfo {

	private String dpmc;
	private String dpurl;
	private String dppic;
	private String dpszd;

	public String getDpmc() {
		return dpmc;
	}

	public void setDpmc(String dpmc) {
		this.dpmc = dpmc;
	}

	public String getDpurl() {
		return dpurl;
	}

	public void setDpurl(String dpurl) {
		this.dpurl = dpurl;
	}

	public String getDppic() {
		return dppic;
	}

	public void setDppic(String dppic) {
		this.dppic = dppic;
	}

	public String getDpszd() {
		return dpszd;
	}

	public void setDpszd(String dpszd) {
		this.dpszd = dpszd;
	}

	public String toString() {
		Gson gson = new GsonBuilder().setExclusionStrategies(new SkipFieldExclusionStrategy("dpmc,dpurl,dppic", true))
				.create();
		return gson.toJson(this);
	}

}
