package com.abujlb.zdcj8.bean;

import java.util.ArrayList;
import java.util.List;

import com.google.gson.Gson;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

public class DptsLljg {
	private String childPageType;
	private String pageId;
	private String pageName; // 渠道名
	private double payByrCntIndex; // 指数转换后是买家数
	private double payRateIndex; // 指数转换后是转化率
	private double tradeIndex; // 指数转换后是交易金额
	private double uv; // 访客数

	public static String toDptsLljg(String data) {
		List<DptsLljg> list = new ArrayList<DptsLljg>();
		JSONArray array = JSONArray.fromObject(data);
		for (int i = 0; i < array.size(); i++) {
			JSONObject obj = array.getJSONObject(i);
			DptsLljg lljg = new DptsLljg();
			lljg.setPageName(getStr(obj, "pageName"));
			lljg.setPageId(getStr(obj, "pageId"));
			lljg.setChildPageType(getStr(obj, "childPageType"));
			lljg.setPayByrCntIndex(getDbl(obj, "rivalItem1PayByrCntIndex"));
			lljg.setPayRateIndex(getDbl(obj, "rivalItem1PayRateIndex"));
			lljg.setTradeIndex(getDbl(obj, "rivalItem1TradeIndex"));
			lljg.setUv(getDbl(obj, "rivalItem1Uv"));
			list.add(lljg);
		}
		Gson gson = new Gson();
		return gson.toJson(list);
	}

	public static String getStr(JSONObject obj, String name) {
		if (obj.has(name) && obj.getJSONObject(name).has("value")) {
			return obj.getJSONObject(name).getString("value");
		}
		return null;
	}

	public static double getDbl(JSONObject obj, String name) {
		if (obj.has(name) && obj.getJSONObject(name).has("value")) {
			return obj.getJSONObject(name).getDouble("value");
		}
		return 0D;
	}

	public String getChildPageType() {
		return childPageType;
	}

	public void setChildPageType(String childPageType) {
		this.childPageType = childPageType;
	}

	public String getPageId() {
		return pageId;
	}

	public void setPageId(String pageId) {
		this.pageId = pageId;
	}

	public String getPageName() {
		return pageName;
	}

	public void setPageName(String pageName) {
		this.pageName = pageName;
	}

	public double getPayByrCntIndex() {
		return payByrCntIndex;
	}

	public void setPayByrCntIndex(double payByrCntIndex) {
		this.payByrCntIndex = payByrCntIndex;
	}

	public double getPayRateIndex() {
		return payRateIndex;
	}

	public void setPayRateIndex(double payRateIndex) {
		this.payRateIndex = payRateIndex;
	}

	public double getTradeIndex() {
		return tradeIndex;
	}

	public void setTradeIndex(double tradeIndex) {
		this.tradeIndex = tradeIndex;
	}

	public double getUv() {
		return uv;
	}

	public void setUv(double uv) {
		this.uv = uv;
	}

}
