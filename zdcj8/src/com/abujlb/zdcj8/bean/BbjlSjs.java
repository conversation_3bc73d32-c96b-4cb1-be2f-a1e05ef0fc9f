package com.abujlb.zdcj8.bean;

import java.util.Date;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

/**
 * 宝贝记录表 实体
 * 
 * <AUTHOR>
 * @date 2020-09-23 15:59:19
 */
public class BbjlSjs {
	
	/**
	 * lx类型
	 */
	public static final String SKU_SALE = "sku_sale"; // 竞品sku销量
	public static final String SUPER_JPTS = "super_jpts"; // 超级竞品透视
	public static final String JPTS = "jpts"; // 竞品透视（最近30天），https://www.shujushe.com/Navigation?hkj=3
	
	private String itemid; // 主键1：宝贝id
	private String lx; // 主键2：类型 例如：sku_sale(竞品sku销量)
	private Date rq; // 日期时间

	public BbjlSjs() {

	}
	
	public BbjlSjs(String itemid, String lx, Date rq) {
		this.itemid = itemid;
		this.lx = lx;
		this.rq = rq;
	}

	public String toString() {
		Gson gson = new GsonBuilder().setDateFormat("yyyy-MM-dd").create();
		return gson.toJson(this);
	}

	public String getItemid() {
		return itemid;
	}

	public void setItemid(String itemid) {
		this.itemid = itemid;
	}

	public String getLx() {
		return lx;
	}

	public void setLx(String lx) {
		this.lx = lx;
	}

	public Date getRq() {
		return rq;
	}

	public void setRq(Date rq) {
		this.rq = rq;
	}

}
