package com.abujlb.zdcj8.bean;

import java.text.SimpleDateFormat;

import com.abujlb.util.DateUtil;
import com.abujlb.util.StringUtil;
import com.abujlb.zdcj8.util.ObjAttr2Number;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

/**
 * class竞品透视 宝贝综合数据，https://www.shujushe.com/Navigation/3/
 * 
 * <AUTHOR>
 * @date 2021-12-03 15:10:59
 */
public class JptsBbxx3 {

	private String itemid; // 商品id
	private String title; // 商品名称
	private String images; // 商品图片
	private String uuid;
	private String createTimeStr;
	private long createTime;
	private long id;
	private int totalPage;
	private int totalNum;
	private String itemstatus;
	private String status;

	private String rq;

	public JptsBbxx3() {

	}

	public JptsBbxx3(JSONObject obj) {
		try {
			if (obj != null && !obj.isEmpty()) {
				this.itemid = ObjAttr2Number.toString(obj, "nid"); // 商品id
				this.title = ObjAttr2Number.toString(obj, "title"); // 商品名称
				this.images = ObjAttr2Number.toString(obj, "images"); // 商品图片
				this.uuid = ObjAttr2Number.toString(obj, "uuid");
				this.createTimeStr = ObjAttr2Number.toString(obj, "createTimeStr");
				this.createTime = ObjAttr2Number.toLong(obj, "createTime");
				this.id = ObjAttr2Number.toLong(obj, "id");
				this.totalPage = ObjAttr2Number.toInt(obj, "totalPage");
				this.totalNum = ObjAttr2Number.toInt(obj, "totalNum");
				this.itemstatus = ObjAttr2Number.toString(obj, "itemstatus");
				this.status = ObjAttr2Number.toString(obj, "status");
				
				if (!StringUtil.isNull2(this.createTimeStr)) {
					SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
					this.rq = DateUtil.format(sdf.parse(this.createTimeStr));
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public String toString() {
		Gson gson = new GsonBuilder().setDateFormat("yyyy-MM-dd HH:mm:ss").create();
		return gson.toJson(this);
	}

	public String getItemid() {
		return itemid;
	}

	public void setItemid(String itemid) {
		this.itemid = itemid;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getImages() {
		return images;
	}

	public void setImages(String images) {
		this.images = images;
	}

	public String getUuid() {
		return uuid;
	}

	public void setUuid(String uuid) {
		this.uuid = uuid;
	}

	public String getCreateTimeStr() {
		return createTimeStr;
	}

	public void setCreateTimeStr(String createTimeStr) {
		this.createTimeStr = createTimeStr;
	}

	public long getCreateTime() {
		return createTime;
	}

	public void setCreateTime(long createTime) {
		this.createTime = createTime;
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public int getTotalPage() {
		return totalPage;
	}

	public void setTotalPage(int totalPage) {
		this.totalPage = totalPage;
	}

	public int getTotalNum() {
		return totalNum;
	}

	public void setTotalNum(int totalNum) {
		this.totalNum = totalNum;
	}

	public String getItemstatus() {
		return itemstatus;
	}

	public void setItemstatus(String itemstatus) {
		this.itemstatus = itemstatus;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getRq() {
		return rq;
	}

	public void setRq(String rq) {
		this.rq = rq;
	}

}
