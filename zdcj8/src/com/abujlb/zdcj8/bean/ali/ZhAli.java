package com.abujlb.zdcj8.bean.ali;

import org.apache.http.client.CookieStore;

import com.abujlb.gson.DefSkipFieldExclusionStrategy;
import com.abujlb.util.CookieString;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

/**
 * 阿里账户表 实体
 * 
 * <AUTHOR>
 * @date 2021-03-19 14:41:15
 */
public class ZhAli {

	private long id; // 主键id 使用序列，不自增，索引唯一
	private String xm; // 姓名
	private String sjh; // 验证码手机号
	private String username; // 用户名
	private String password; // 密码
	private int lx; // 类型 0.(默认)
	private int zt; // 状态：1.可用、2.禁用、9.已失效

	private transient CookieStore cookieStore;
	private String cookieKey;
	private String cookieStr;
	private int cjzt; // 采集状态：0.未开始、1.进行中、2.已结束
	private long logintime; // 登录时间
	private long lastuse; // 最后使用时间
	private int usetimes; // 使用次数
	private int success; // 成功次数
	private int error; // 连续失败次数
	private boolean successlast; // 最后一次是否成功了
	private int sfsx; // 是否失效：0.未失效、1.已失效
//	private int sfqf; // 是否欠费：0.未欠费、1.已欠费（积分不足3）
	
	/************************************** 数据蛇登录之后，获取的参数 **************************************/
//	private String userId; // 当前登录的数据蛇用户id
//	private String token; // 当前登录的数据蛇用户token
//	private String userpass; // 用户密码
//	private String role; // 数据蛇用户角色，一般为2
//	private double jf; // 当前积分
	
	public void init() {
		if (this.cookieStr != null && !"".equals(cookieStr.trim())) {
			this.cookieStore = CookieString.strToCookies(cookieStr);
		}
//		this.cjzt = AliConst.CJZT_WKS;
//		this.logintime = System.currentTimeMillis();
//		this.lastuse = 0;
//		this.usetimes = 0;
//		this.success = 0;
//		this.error = 0;
//		this.successlast = true;
//		this.sfsx = AliConst.SFSX_WSX;
	}
	
	/**
	 * 开始使用，增加使用次数
	 */
	public void use() {
		this.usetimes++;
		this.lastuse = System.currentTimeMillis();
	}
	
	/**
	 * 申请执行完成后返回
	 * 
	 * @param success t:成功,f:失败
	 */
	public synchronized void back(boolean success) {
		if (success) {
			this.success++;
			this.error = 0;
			this.successlast = true;
		} else {
			this.error++;
			this.successlast = false;
		}
	}

	public ZhAli() {

	}

	public String toString() {
		Gson gson = new GsonBuilder().setExclusionStrategies(new DefSkipFieldExclusionStrategy("password,cookieStore")).setDateFormat("yyyy-MM-dd HH:mm:ss").create();
		return gson.toJson(this);
	}
	
	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getXm() {
		return xm;
	}

	public void setXm(String xm) {
		this.xm = xm;
	}

	public String getSjh() {
		return sjh;
	}

	public void setSjh(String sjh) {
		this.sjh = sjh;
	}

	public String getUsername() {
		return username;
	}

	public void setUsername(String username) {
		this.username = username;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public int getLx() {
		return lx;
	}

	public void setLx(int lx) {
		this.lx = lx;
	}

	public int getZt() {
		return zt;
	}

	public void setZt(int zt) {
		this.zt = zt;
	}

	public CookieStore getCookieStore() {
		return cookieStore;
	}

	public void setCookieStore(CookieStore cookieStore) {
		this.cookieStore = cookieStore;
	}

	public String getCookieKey() {
		return cookieKey;
	}

	public void setCookieKey(String cookieKey) {
		this.cookieKey = cookieKey;
	}

	public String getCookieStr() {
		return cookieStr;
	}

	public void setCookieStr(String cookieStr) {
		this.cookieStr = cookieStr;
	}

	public synchronized int getCjzt() { // 同步方法，不能同时被调用
		return cjzt;
	}

	public synchronized void setCjzt(int cjzt) { // 同步方法，不能同时被调用
		this.cjzt = cjzt;
	}

	public long getLogintime() {
		return logintime;
	}

	public void setLogintime(long logintime) {
		this.logintime = logintime;
	}

	public long getLastuse() {
		return lastuse;
	}

	public void setLastuse(long lastuse) {
		this.lastuse = lastuse;
	}

	public int getUsetimes() {
		return usetimes;
	}

	public void setUsetimes(int usetimes) {
		this.usetimes = usetimes;
	}

	public int getSuccess() {
		return success;
	}

	public void setSuccess(int success) {
		this.success = success;
	}

	public int getError() {
		return error;
	}

	public void setError(int error) {
		this.error = error;
	}

	public boolean isSuccesslast() {
		return successlast;
	}

	public void setSuccesslast(boolean successlast) {
		this.successlast = successlast;
	}

	public int getSfsx() {
		return sfsx;
	}

	public void setSfsx(int sfsx) {
		this.sfsx = sfsx;
	}

}
