package com.abujlb.zdcj8.bean;

public class RscFc {

	private int avgWordClickHits;// 相关词点击人气
	private int avgWordSeIpvUvHits;// 相关词搜索人气
	private int hotSearchRank;// 热搜排名
	private double p4pRefPrice;//
	private int relSeWordCnt;// 相关搜索词数
	private String searchWord;// 搜索词
	private int soarRank;//
	private double avgWordClickRate;//词均点击率
	private double avgWordPayRate;//词均支付转化率
	
	

	public int getAvgWordClickHits() {
		return avgWordClickHits;
	}

	public void setAvgWordClickHits(int avgWordClickHits) {
		this.avgWordClickHits = avgWordClickHits;
	}

	public int getAvgWordSeIpvUvHits() {
		return avgWordSeIpvUvHits;
	}

	public void setAvgWordSeIpvUvHits(int avgWordSeIpvUvHits) {
		this.avgWordSeIpvUvHits = avgWordSeIpvUvHits;
	}

	public int getHotSearchRank() {
		return hotSearchRank;
	}

	public void setHotSearchRank(int hotSearchRank) {
		this.hotSearchRank = hotSearchRank;
	}

	public double getP4pRefPrice() {
		return p4pRefPrice;
	}

	public void setP4pRefPrice(double p4pRefPrice) {
		this.p4pRefPrice = p4pRefPrice;
	}

	public int getRelSeWordCnt() {
		return relSeWordCnt;
	}

	public void setRelSeWordCnt(int relSeWordCnt) {
		this.relSeWordCnt = relSeWordCnt;
	}

	public String getSearchWord() {
		return searchWord;
	}

	public void setSearchWord(String searchWord) {
		this.searchWord = searchWord;
	}

	public int getSoarRank() {
		return soarRank;
	}

	public void setSoarRank(int soarRank) {
		this.soarRank = soarRank;
	}

	public double getAvgWordClickRate() {
		return avgWordClickRate;
	}

	public void setAvgWordClickRate(double avgWordClickRate) {
		this.avgWordClickRate = avgWordClickRate;
	}

	public double getAvgWordPayRate() {
		return avgWordPayRate;
	}

	public void setAvgWordPayRate(double avgWordPayRate) {
		this.avgWordPayRate = avgWordPayRate;
	}
	
	@Override
	public boolean equals(Object obj) {
		if (this == obj) {
			return true;
		}
		if (obj == null) {
			return false;
		}
		if (getClass() != obj.getClass()) {
			return false;
		}
		RscFc rscFc = (RscFc) obj;
		if (searchWord == null) {
			if (rscFc.searchWord != null) {
				return false;
			}
		} else if (!searchWord.equals(rscFc.searchWord)) {
			return false;
		}
		return true;

	}

}
