package com.abujlb.zdcj8.bean;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

import com.abujlb.util.DateUtil;
import com.abujlb.zdcj8.util.StrUtil;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

import net.sf.json.JSONObject;

/**
 * cookie 实体
 * 
 * <AUTHOR>
 * @date 2020-12-24 15:35:01
 */
public class CookieVo {
	private String name;
	private AttribsBean attribs; // cookie详细信息，包含cookie格林威治时间 (GMT)
	private String value; // 值
	private String cookieDomain; // 域名，例如：.taobao.com
	private Date cookieExpiryDate; // cookie到期时间，北京时间，此时间与格式化的attribs.expires相同
	private String cookiePath;
	private boolean isSecure;
	private int cookieVersion;
	
	// private String url; // 地址

	public CookieVo() {

	}
	
	public CookieVo(String json) {
		try {
			if (StrUtil.isNull(json) || !StrUtil.isJson(json)) {
				return;
			}
			JSONObject jsonOb = JSONObject.fromObject(json);
			if (jsonOb.has("name")) {
				this.name = jsonOb.getString("name");
			}
			if (jsonOb.has("attribs")) {
				this.attribs = new AttribsBean(jsonOb.getJSONObject("attribs"));
			}
			if (jsonOb.has("value")) {
				this.value = jsonOb.getString("value");
			}
			if (jsonOb.has("cookieDomain")) {
				this.cookieDomain = jsonOb.getString("cookieDomain");
			}
			if (jsonOb.has("cookieExpiryDate")) {
//				String format = "MMM dd, yyyy KK:mm:ss aa"; // 日期格式化：MMM代表月，KK代表时间
//				SimpleDateFormat dateFormat = new SimpleDateFormat(format, Locale.ENGLISH); // 这里只能是Locale.ENGLISH
//				// dateFormat.setTimeZone(TimeZone.getTimeZone("GMT")); // 设置时区
//				this.cookieExpiryDate = dateFormat.parse(jsonOb.getString("cookieExpiryDate"));
				
				SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
				this.cookieExpiryDate = null ;
		        try {
		        	this.cookieExpiryDate = simpleDateFormat.parse(jsonOb.getString("cookieExpiryDate")) ; // 此时间与格式化的attribs.expires相同
		        } catch (Exception e) {
		            e.printStackTrace();
		        }
			}
			if (jsonOb.has("cookiePath")) {
				this.cookiePath = jsonOb.getString("cookiePath");
			}
			if (jsonOb.has("isSecure")) {
				this.isSecure = jsonOb.getBoolean("isSecure");
			}
			if (jsonOb.has("cookieVersion")) {
				this.cookieVersion = jsonOb.getInt("cookieVersion");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	public String toString() {
		Gson gson = new GsonBuilder().setDateFormat("yyyy-MM-dd HH:mm:ss").create();
		return gson.toJson(this);
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public AttribsBean getAttribs() {
		return attribs;
	}

	public void setAttribs(AttribsBean attribs) {
		this.attribs = attribs;
	}

	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}

	public String getCookieDomain() {
		return cookieDomain;
	}

	public void setCookieDomain(String cookieDomain) {
		this.cookieDomain = cookieDomain;
	}

	public Date getCookieExpiryDate() {
		return cookieExpiryDate;
	}

	public void setCookieExpiryDate(Date cookieExpiryDate) {
		this.cookieExpiryDate = cookieExpiryDate;
	}

	public String getCookiePath() {
		return cookiePath;
	}

	public void setCookiePath(String cookiePath) {
		this.cookiePath = cookiePath;
	}

	public boolean isSecure() {
		return isSecure;
	}

	public void setSecure(boolean isSecure) {
		this.isSecure = isSecure;
	}

	public int getCookieVersion() {
		return cookieVersion;
	}

	public void setCookieVersion(int cookieVersion) {
		this.cookieVersion = cookieVersion;
	}
	
	// main方法
	public static void main(String[] args) {
		// String format = "MMM dd, yyyy HH:mm:ss 'PM'";
		String format = "MMM dd, yyyy KK:mm:ss aa";
		try {
			SimpleDateFormat dateFormat = new SimpleDateFormat(format, Locale.ENGLISH);
			
			// dateFormat.setTimeZone(TimeZone.getTimeZone("GMT"));
			System.out.println(dateFormat.format(new Date()));
			System.out.println();
			String dateStr = "Dec 23, 2021 8:38:03 PM";
			Date date = dateFormat.parse(dateStr);
			if (date != null) {
				System.out.println(date.toString());
				System.out.println(DateUtil.formatTime(date));
			}
			
			int oneHourInMilliseconds = 60 * 60 * 10 * 1000; // 一个小时是60*60*10*1000，一分钟60*10*1000
			long expirationTimeInMicroseconds = (System.currentTimeMillis() + oneHourInMilliseconds) * 1000; // 代表1个小时以后：（当前时间+一分钟）*1000毫秒
			System.out.println(expirationTimeInMicroseconds);
			// System.out.println(date.getTimezoneOffset());
			System.out.println(date.getTime());
			System.out.println(date.getTime() * 1000L);
			
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

}
