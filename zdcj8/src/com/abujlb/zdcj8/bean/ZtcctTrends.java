package com.abujlb.zdcj8.bean;

import com.abujlb.util.StringUtil;
import com.abujlb.zdcj8.bean.ztc.ZtcctTrendsBean;
import com.google.gson.Gson;

/**
 * 直通车车图，市场数据趋势
 * 
 * <AUTHOR>
 * @date 2020-08-07 17:05:58
 */
public class ZtcctTrends {

	private String normalWord; // 关键词
	private String categoryId; // 类目id
	private String impression; // 展现量，整数，例如：83916
	private String impressionIndex; // 展现指数，浮点型，例如：66889.84101382896
	private String click; // 点击量（${t}在${i}${e}展示位上被点击的次数。注意，虚假点击会被${e}反作弊体系过滤，该数据为反作弊系统过滤后的数据。），整数，例如：4199
	private String clickIndex; // 点击指数，浮点型，例如：4502.730466193782
	private String cvr; // 点击转化率，小数，真实值，例如：0.014868329394186364，表示1.49%
	private String ctr; // 点击率，小数，真实值，例如：0.041377928764354976，表示4.14%
	private String cost; // 花费（${t}在${i}${e}展示位上被用户点击所消耗的费用。），单位(分)，整数，例如：628887，表示6288.87元
	private String avgPrice; // 市场均价，单位(分)，浮点型，例如：48.403697642318214，表示0.48元
	private String competition; // 竞争指数，整数，例如：349
	private String directtransactionshipping; // 直接成交笔数（${t}在${i}${e}展示位被点击后，买家在所选转化周期内，直接在推广宝贝的详情页面拍下并通过支付宝交易的成交笔数。），整数，例如：93
	private String indirecttransactionshipping; // 间接成交笔数（${t}在${i}${e}展示位被点击后，买家在所选转化周期内，通过推广宝贝的详情页面跳转至店铺内其他宝贝的详情页面拍下并通过支付宝交易的成交笔数。），整数，例如：67
	private String theDate; // 日期，例如：20230205000000
	
	// private double zxzs; // 展现指数
	private double zxl; // 展现量
	private double djlv; // 点击率
	
	public ZtcctTrends() {
		
	}
	
	public ZtcctTrends(ZtcctTrendsBean ztcctTrendsBean) {
		if (ztcctTrendsBean != null) {
			this.normalWord = ztcctTrendsBean.getA(); // 关键词
			this.categoryId = ztcctTrendsBean.getB(); // 类目id
			this.impression = ztcctTrendsBean.getC(); // 展现量，整数，例如：83916
			this.impressionIndex = ztcctTrendsBean.getD(); // 展现指数，浮点型，例如：66889.84101382896
			this.click = ztcctTrendsBean.getE(); // 点击量（${t}在${i}${e}展示位上被点击的次数。注意，虚假点击会被${e}反作弊体系过滤，该数据为反作弊系统过滤后的数据。），整数，例如：4199
			this.clickIndex = ztcctTrendsBean.getF(); // 点击指数，浮点型，例如：4502.730466193782
			this.cvr = ztcctTrendsBean.getG(); // 点击转化率，小数，真实值，例如：0.014868329394186364，表示1.49%
			this.ctr = ztcctTrendsBean.getH(); // 点击率，小数，真实值，例如：0.041377928764354976，表示4.14%
			this.cost = ztcctTrendsBean.getI(); // 花费（${t}在${i}${e}展示位上被用户点击所消耗的费用。），单位(分)，整数，例如：628887，表示6288.87元
			this.avgPrice = ztcctTrendsBean.getJ(); // 市场均价，单位(分)，浮点型，例如：48.403697642318214，表示0.48元
			this.competition = ztcctTrendsBean.getK(); // 竞争指数，整数，例如：349
			this.directtransactionshipping = ztcctTrendsBean.getL(); // 直接成交笔数（${t}在${i}${e}展示位被点击后，买家在所选转化周期内，直接在推广宝贝的详情页面拍下并通过支付宝交易的成交笔数。），整数，例如：93
			this.indirecttransactionshipping = ztcctTrendsBean.getM(); // 间接成交笔数（${t}在${i}${e}展示位被点击后，买家在所选转化周期内，通过推广宝贝的详情页面跳转至店铺内其他宝贝的详情页面拍下并通过支付宝交易的成交笔数。），整数，例如：67
			this.theDate = ztcctTrendsBean.getN(); // 日期，例如：20230205000000
		}
	}
	
	public void initData() {
		try {
			/*this.zxzs = 0D;
			if (!StrUtil.isNull(this.impressionIndex)) {
				this.zxzs = Double.valueOf(this.impressionIndex);
			}*/
			this.zxl = 0D;
			if (!StringUtil.isNull2(this.impression)) {
				this.zxl = Double.valueOf(this.impression);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		try {
			this.djlv = 0D;
			if (!StringUtil.isNull2(this.ctr)) {
				this.djlv = Double.valueOf(this.ctr);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	public String toString() {
		Gson gson = new Gson();
		return gson.toJson(this);
	}

	public String getNormalWord() {
		return normalWord;
	}

	public void setNormalWord(String normalWord) {
		this.normalWord = normalWord;
	}

	public String getCategoryId() {
		return categoryId;
	}

	public void setCategoryId(String categoryId) {
		this.categoryId = categoryId;
	}

	public String getImpressionIndex() {
		return impressionIndex;
	}

	public void setImpressionIndex(String impressionIndex) {
		this.impressionIndex = impressionIndex;
	}

	public String getClickIndex() {
		return clickIndex;
	}

	public void setClickIndex(String clickIndex) {
		this.clickIndex = clickIndex;
	}

	public String getCtr() {
		return ctr;
	}

	public void setCtr(String ctr) {
		this.ctr = ctr;
	}

	public String getCvr() {
		return cvr;
	}

	public void setCvr(String cvr) {
		this.cvr = cvr;
	}

	public String getCompetition() {
		return competition;
	}

	public void setCompetition(String competition) {
		this.competition = competition;
	}

	public String getAvgPrice() {
		return avgPrice;
	}

	public void setAvgPrice(String avgPrice) {
		this.avgPrice = avgPrice;
	}

	public String getTheDate() {
		return theDate;
	}

	public void setTheDate(String theDate) {
		this.theDate = theDate;
	}

	public double getZxl() {
		return zxl;
	}

	public void setZxl(double zxl) {
		this.zxl = zxl;
	}

	public double getDjlv() {
		return djlv;
	}

	public void setDjlv(double djlv) {
		this.djlv = djlv;
	}

	public String getImpression() {
		return impression;
	}

	public void setImpression(String impression) {
		this.impression = impression;
	}

	public String getClick() {
		return click;
	}

	public void setClick(String click) {
		this.click = click;
	}

	public String getCost() {
		return cost;
	}

	public void setCost(String cost) {
		this.cost = cost;
	}

	public String getIndirecttransactionshipping() {
		return indirecttransactionshipping;
	}

	public void setIndirecttransactionshipping(String indirecttransactionshipping) {
		this.indirecttransactionshipping = indirecttransactionshipping;
	}

	public String getDirecttransactionshipping() {
		return directtransactionshipping;
	}

	public void setDirecttransactionshipping(String directtransactionshipping) {
		this.directtransactionshipping = directtransactionshipping;
	}

}
