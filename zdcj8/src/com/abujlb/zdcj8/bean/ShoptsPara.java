package com.abujlb.zdcj8.bean;

import com.google.gson.Gson;

public class ShoptsPara {
	private String userid;
	private String[] catelist;

	public String getUserid() {
		return userid;
	}

	public String[] getCatelist() {
		return catelist;
	}

	public void setUserid(String userid) {
		this.userid = userid;
	}

	public void setCatelist(String[] catelist) {
		this.catelist = catelist;
	}

	public static void main(String[] args) {
		Gson gson = new Gson();
		String str = "{\"userid\":2138095092,\"catelist\":[\"122952001\",\"122952002\"]}";
		ShoptsPara sp = gson.fromJson(str, ShoptsPara.class);
		System.out.println(gson.toJson(sp));

	}
}
