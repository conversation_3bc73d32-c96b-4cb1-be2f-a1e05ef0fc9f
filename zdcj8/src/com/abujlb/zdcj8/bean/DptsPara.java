package com.abujlb.zdcj8.bean;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;

import com.abujlb.util.DateUtil;

public class DptsPara {
	private String bbid;
	private String device;
	private String date;
	private String ndate;

	public DptsPara(String bbid, String device) {
		this.bbid = bbid;
		this.device = device;
	}

	public String getRecent30Date() {
		DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
		Date d1 = null;
		try {
			if (ndate != null) {
				d1 = df.parse(ndate);
			} else {
				d1 = df.parse(date);
			}
		} catch (Exception e) {
		}
		Date d2 = DateUtil.getDateBefore(d1, 29);
		return df.format(d2) + "%7C" + df.format(d1);
	}

	public String getBbid() {
		return bbid;
	}

	public String getDevice() {
		return device;
	}

	public void setBbid(String bbid) {
		this.bbid = bbid;
	}

	public void setDevice(String device) {
		this.device = device;
	}

	public String getDate() {
		return date;
	}

	public void setDate(String date) {
		this.date = date;
	}

	public String getNdate() {
		return ndate;
	}

	public void setNdate(String ndate) {
		this.ndate = ndate;
	}

	public static void main(String[] args) {
		DptsPara dp = new DptsPara("1", "0");
		dp.setNdate("2019-03-01");
		System.out.println(dp.getRecent30Date());
	}
}
