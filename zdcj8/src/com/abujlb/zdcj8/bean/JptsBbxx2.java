package com.abujlb.zdcj8.bean;

import com.abujlb.zdcj8.util.StrUtil;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

import net.sf.json.JSONObject;

/**
 * 竞品透视 宝贝综合数据，https://www.shujushe.com/Navigation?hkj=3
 * 
 * <AUTHOR>
 * @date 2021-02-20 10:51:46
 */
public class JptsBbxx2 {
	
	private String tjrq; // 统计日期
	private String itemid; // 商品id
	
	private String fks; // fks 访客数，例如："25665"，是字符串
//	private long id;
	private String je; // je 金额，例如："220104"，是字符串
	private String kdj; // kdj 客单价，例如："92"，是字符串
//	private String skuid; // skuid，例如：""，是字符串
//	private String spsku; // spsku，例如：""，是字符串
	private String vujz; // vujz UV价值，例如："5.64"，是字符串
	private String zfmjs; // zfmjs 支付买家数/购买人数，例如："2395"，是字符串
	private String zhl; // zhl 转化率，例如："5.4%"，是字符串
	
	public JptsBbxx2() {
		
	}
	
	public JptsBbxx2(String tjrq, String itemid) {
		this.tjrq = tjrq; // 统计日期
		this.itemid = itemid; // 商品id
	}
	
	public JptsBbxx2(JSONObject obj) {
		try {
			this.tjrq = ""; // 统计日期
			if (obj.has("tjrq") && !StrUtil.isNull(obj.getString("tjrq"))) {
				this.tjrq = obj.getString("tjrq");
			}
			this.itemid = ""; // 商品id
			if (obj.has("nid") && !StrUtil.isNull(obj.getString("nid"))) {
				this.itemid = obj.getString("nid");
			}
			this.fks = ""; // fks 访客数，例如："25665"，是字符串
			if (obj.has("fks") && !StrUtil.isNull(obj.getString("fks"))) {
				this.fks = obj.getString("fks");
			}
			this.je = ""; // je 金额，例如："220104"，是字符串
			if (obj.has("je") && !StrUtil.isNull(obj.getString("je"))) {
				this.je = obj.getString("je");
			}
			this.kdj = ""; // kdj 客单价，例如："92"，是字符串
			if (obj.has("kdj") && !StrUtil.isNull(obj.getString("kdj"))) {
				this.kdj = obj.getString("kdj");
			}
			this.vujz = ""; // vujz UV价值，例如："5.64"，是字符串
			if (obj.has("vujz") && !StrUtil.isNull(obj.getString("vujz"))) {
				this.vujz = obj.getString("vujz");
			}
			this.zfmjs = ""; // zfmjs 支付买家数/购买人数，例如："2395"，是字符串
			if (obj.has("zfmjs") && !StrUtil.isNull(obj.getString("zfmjs"))) {
				this.zfmjs = obj.getString("zfmjs");
			}
			this.zhl = ""; // zhl 转化率，例如："5.4%"，是字符串
			if (obj.has("zhl") && !StrUtil.isNull(obj.getString("zhl"))) {
				this.zhl = obj.getString("zhl");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	public String toString() {
		Gson gson = new GsonBuilder().setDateFormat("yyyy-MM-dd HH:mm:ss").create();
		return gson.toJson(this);
	}

	public String getTjrq() {
		return tjrq;
	}

	public void setTjrq(String tjrq) {
		this.tjrq = tjrq;
	}

	public String getItemid() {
		return itemid;
	}

	public void setItemid(String itemid) {
		this.itemid = itemid;
	}

	public String getFks() {
		return fks;
	}

	public void setFks(String fks) {
		this.fks = fks;
	}

	public String getJe() {
		return je;
	}

	public void setJe(String je) {
		this.je = je;
	}

	public String getKdj() {
		return kdj;
	}

	public void setKdj(String kdj) {
		this.kdj = kdj;
	}

	public String getVujz() {
		return vujz;
	}

	public void setVujz(String vujz) {
		this.vujz = vujz;
	}

	public String getZfmjs() {
		return zfmjs;
	}

	public void setZfmjs(String zfmjs) {
		this.zfmjs = zfmjs;
	}

	public String getZhl() {
		return zhl;
	}

	public void setZhl(String zhl) {
		this.zhl = zhl;
	}

}
