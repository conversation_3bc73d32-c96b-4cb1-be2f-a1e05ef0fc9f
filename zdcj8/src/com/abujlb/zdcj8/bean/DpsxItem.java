package com.abujlb.zdcj8.bean;

import com.abujlb.zdcj8.util.StrUtil;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

import net.sf.json.JSONObject;

/**
 * （数据蛇）店铺上新，宝贝信息
 * 
 * <AUTHOR>
 * @date 2020-11-10 16:56:42
 */
public class DpsxItem {

	private String itemId; // 宝贝id，例如：631600970256
	private String title; // 宝贝名称，例如：微喇叭牛仔裤女裤子秋冬季高腰显瘦修身百搭宽松直筒秋季烟管长裤
	private String pic; // 图片，例如：//img.alicdn.com/imgextra/i2/0/O1CN01CJYgi52J5yASlkSeb_!!0-item_pic.jpg
	private String price; // 价格，例如：69.80，单位：元
	private String date; // 上新日期时间，例如：2020-11-10 21:00:00
	
	public DpsxItem() {

	}
	
	public DpsxItem(JSONObject obj) {
		try {
			this.itemId = ""; // 宝贝id
			if (obj.has("nid") && !StrUtil.isNull(obj.getString("nid"))) {
				this.itemId = obj.getString("nid");
			}
			this.title = ""; // 宝贝名称
			if (obj.has("title") && !StrUtil.isNull(obj.getString("title"))) {
				this.title = obj.getString("title");
			}
			this.pic = ""; // 图片
			if (obj.has("pic") && !StrUtil.isNull(obj.getString("pic"))) {
				this.pic = obj.getString("pic");
			}
			this.price = ""; // 价格
			if (obj.has("price") && !StrUtil.isNull(obj.getString("price"))) {
				this.price = obj.getString("price");
			}
			this.date = ""; // 上新日期时间
			if (obj.has("date") && !StrUtil.isNull(obj.getString("date"))) {
				this.date = obj.getString("date");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	public String toString() {
		Gson gson = new GsonBuilder().setDateFormat("yyyy-MM-dd HH:mm:ss").create();
		return gson.toJson(this);
	}

	public String getItemId() {
		return itemId;
	}

	public void setItemId(String itemId) {
		this.itemId = itemId;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getPic() {
		return pic;
	}

	public void setPic(String pic) {
		this.pic = pic;
	}

	public String getPrice() {
		return price;
	}

	public void setPrice(String price) {
		this.price = price;
	}

	public String getDate() {
		return date;
	}

	public void setDate(String date) {
		this.date = date;
	}
	
}
