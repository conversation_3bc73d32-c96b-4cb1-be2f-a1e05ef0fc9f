package com.abujlb.zdcj8.bean;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import java.util.TimeZone;

import com.abujlb.util.DateUtil;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

import net.sf.json.JSONObject;

/**
 * cookie.attribs 实体
 * 
 * <AUTHOR>
 * @date 2020-12-25 09:40:35
 */
public class AttribsBean {
	
	private String path;
	private Date expires; // cookie到期时间：格林威治时间 (GMT)
	private String samesite;
	private String domain;

	public AttribsBean() {

	}
	
	public AttribsBean(JSONObject jsonOb) {
		try {
			if (jsonOb == null) {
				return;
			}
			if (jsonOb.has("path")) {
				this.path = jsonOb.getString("path");
			}
			if (jsonOb.has("expires")) {
				String format = "EEE, dd-MMM-yyyy HH:mm:ss 'GMT'"; // 日期格式化：EEE代表星期
				SimpleDateFormat dateFormat = new SimpleDateFormat(format, Locale.US); // 这里只能是Locale.US
				dateFormat.setTimeZone(TimeZone.getTimeZone("GMT")); // 格林威治时间 (GMT)
				this.expires = dateFormat.parse(jsonOb.getString("expires"));
			}
			if (jsonOb.has("samesite")) {
				this.samesite = jsonOb.getString("samesite");
			}
			if (jsonOb.has("domain")) {
				this.domain = jsonOb.getString("domain");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	public String toString() {
		Gson gson = new GsonBuilder().setDateFormat("yyyy-MM-dd HH:mm:ss").create();
		return gson.toJson(this);
	}

	public String getPath() {
		return path;
	}

	public void setPath(String path) {
		this.path = path;
	}

	public Date getExpires() {
		return expires;
	}

	public void setExpires(Date expires) {
		this.expires = expires;
	}

	public String getSamesite() {
		return samesite;
	}

	public void setSamesite(String samesite) {
		this.samesite = samesite;
	}

	public String getDomain() {
		return domain;
	}

	public void setDomain(String domain) {
		this.domain = domain;
	}
	
	// main方法
	public static void main(String[] args) {
		String format = "EEE, dd-MMM-yyyy HH:mm:ss 'GMT'";
		// String format = "EEE, dd MMM y HH:mm:ss 'GMT'";
		try {
			SimpleDateFormat dateFormat = new SimpleDateFormat(format, Locale.US);
	       
			
			dateFormat.setTimeZone(TimeZone.getTimeZone("GMT"));
			System.out.println(dateFormat.format(new Date()));
			System.out.println();
			
			String dateStr = "Fri, 24-Dec-2021 12:41:26 GMT";
			Date date = dateFormat.parse(dateStr);
			if (date != null) {
				System.out.println(date.toString());
				System.out.println(DateUtil.formatTime(date));
				
			}
			
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

}
