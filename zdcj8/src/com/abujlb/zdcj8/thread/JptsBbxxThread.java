package com.abujlb.zdcj8.thread;

import java.util.List;

import javax.annotation.PostConstruct;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.abujlb.AbujlbBeanFactory;
import com.abujlb.SpringBeanUtil;
import com.abujlb.zdcj8.bean.JptsBbxx;
import com.abujlb.zdcj8.tsdao.SjsJptsTsDao;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

/**
 * 数据蛇：超级竞品透视 宝贝综合信息 保存
 * 
 * <AUTHOR>
 * @date 2020-10-23 00:41:00
 */
@Component
@Scope("prototype") // 每次获得bean都会生成一个新的对象，不是单例的
public class JptsBbxxThread implements Runnable {

	private static Logger log = Logger.getLogger(JptsBbxxThread.class);

	private String itemid; // 宝贝id
	private String rq; // 日期
	private List<JptsBbxx> list;

	@Autowired
	private SjsJptsTsDao sjsJptsTsDao;
	@Autowired
	private AbujlbBeanFactory abujlbBeanFactory;

	/**
	 * 项目重启之后会先执行init 在初始化的时候执行
	 * 
	 * @doc 说明：因为没有保存在数据库或redis中所以重启之后不能初始化
	 */
	@PostConstruct
	public void init() {
		try {
			// Thread t = new Thread(this);
			// t.setName("process.JptsLljgThread.Starter");
			// t.start();
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		}
	}

	/**
	 * 初始化数据
	 */
	public void initParam(String itemid, String rq, List<JptsBbxx> list) {
		try {
			this.itemid = itemid;
			this.rq = rq;
			this.list = list;
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		}
		
	}

	/**
	 * 开始线程，不占用tomcat启动时间，防止启动超时
	 */
	@Override
	public void run() {
		try {
			SpringBeanUtil.setBeanFactory(abujlbBeanFactory); // 定时器，没有被拦截器拦截，spring上下文会丢失，加上这句即可
			Gson gson = new GsonBuilder().setDateFormat("yyyy-MM-dd HH:mm:ss").create();
			sjsJptsTsDao.updateRow(this.itemid, this.rq, gson.toJson(this.list), null, null, null);
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		}
	}

	public String getItemid() {
		return itemid;
	}

	public void setItemid(String itemid) {
		this.itemid = itemid;
	}

	public String getRq() {
		return rq;
	}

	public void setRq(String rq) {
		this.rq = rq;
	}

	public List<JptsBbxx> getList() {
		return list;
	}

	public void setList(List<JptsBbxx> list) {
		this.list = list;
	}

}
