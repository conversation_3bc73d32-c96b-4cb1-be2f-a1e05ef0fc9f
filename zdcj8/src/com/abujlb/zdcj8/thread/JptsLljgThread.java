package com.abujlb.zdcj8.thread;

import java.util.List;

import javax.annotation.PostConstruct;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.abujlb.AbujlbBeanFactory;
import com.abujlb.Result;
import com.abujlb.SpringBeanUtil;
import com.abujlb.zdcj8.bean.JptsLljg;
import com.abujlb.zdcj8.bean.ZhSjs;
import com.abujlb.zdcj8.http.SjsHttpClient;
import com.abujlb.zdcj8.tsdao.SjsJptsTsDao;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

/**
 * 数据蛇：超级竞品透视 流量结构 保存
 * 
 * <AUTHOR>
 * @date 2020-10-22 22:59:58
 */
@Component
@Scope("prototype") // 每次获得bean都会生成一个新的对象，不是单例的
public class JptsLljgThread implements Runnable {

	private static Logger log = Logger.getLogger(JptsLljgThread.class);

	private String itemid; // 宝贝id
	private String rq; // 日期
	private ZhSjs zhSjs; // 数据蛇账号
	private String uuid;
	private List<JptsLljg> gjcList;
	private List<JptsLljg> qdList;
	private List<JptsLljg> ztcList;
	private int flag; // 标识：0.数据未获取、1.数据已获取

	@Autowired
	private SjsHttpClient sjsHttpClient;
	@Autowired
	private SjsJptsTsDao sjsJptsTsDao;
	@Autowired
	private AbujlbBeanFactory abujlbBeanFactory;

	/**
	 * 项目重启之后会先执行init 在初始化的时候执行
	 * 
	 * @doc 说明：因为没有保存在数据库或redis中所以重启之后不能初始化
	 */
	@PostConstruct
	public void init() {
		try {
			// Thread t = new Thread(this);
			// t.setName("process.JptsLljgThread.Starter");
			// t.start();
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		}
	}

	/**
	 * 初始化数据
	 */
	public void initParam(String itemid, String rq, ZhSjs zhSjs, String uuid, List<JptsLljg> gjcList, List<JptsLljg> qdList, List<JptsLljg> ztcList, int flag) {
		try {
			this.itemid = itemid;
			this.rq = rq;
			this.zhSjs = zhSjs;
			this.uuid = uuid;
			this.gjcList = gjcList;
			this.qdList = qdList;
			this.ztcList = ztcList;
			this.flag = flag; // 标识：0.数据未获取、1.数据已获取
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		}
		
	}

	/**
	 * 开始线程，不占用tomcat启动时间，防止启动超时
	 */
	@SuppressWarnings("unchecked")
	@Override
	public void run() {
		try {
			SpringBeanUtil.setBeanFactory(abujlbBeanFactory); // 定时器，没有被拦截器拦截，spring上下文会丢失，加上这句即可
			Gson gson = new GsonBuilder().setDateFormat("yyyy-MM-dd HH:mm:ss").create();
			if (this.flag == 1) { // 数据已经获取，直接保存
				sjsJptsTsDao.updateRow(this.itemid, this.rq, null, gson.toJson(this.gjcList), gson.toJson(this.qdList), gson.toJson(this.ztcList));
			} else {
				int time = 0; // 尝试次数
				int try_num = 20; // 尝试
				Result resultTemp = null;
				do {
					resultTemp = sjsHttpClient.superJptsLljg(zhSjs, uuid);
					if (resultTemp == null || !resultTemp.isSuccess()) { // 接口不能正常访问
						break; // 结束循环
					}
					int statusCjTask = (int) resultTemp.getKey("statusCjTask");
					if (statusCjTask == 2) {
						List<JptsLljg> _gjcList = (List<JptsLljg>) resultTemp.getKey("gjcList");
						List<JptsLljg> _qdList = (List<JptsLljg>) resultTemp.getKey("qdList");
						List<JptsLljg> _ztcList = (List<JptsLljg>) resultTemp.getKey("ztcList");
						sjsJptsTsDao.updateRow(this.itemid, this.rq, null, gson.toJson(_gjcList), gson.toJson(_qdList), gson.toJson(_ztcList));
						break;
					}
					time++;
					try {
						Thread.sleep(((time >= try_num) ? 0 : time) * 1000L); // 最多等待190
						// Thread.sleep(2000L);
					} catch (Exception e) {
						e.printStackTrace();
					}
				} while (time < try_num);
			}
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		}
	}

	public String getItemid() {
		return itemid;
	}

	public void setItemid(String itemid) {
		this.itemid = itemid;
	}

	public String getRq() {
		return rq;
	}

	public void setRq(String rq) {
		this.rq = rq;
	}

	public ZhSjs getZhSjs() {
		return zhSjs;
	}

	public void setZhSjs(ZhSjs zhSjs) {
		this.zhSjs = zhSjs;
	}

	public String getUuid() {
		return uuid;
	}

	public void setUuid(String uuid) {
		this.uuid = uuid;
	}

	public List<JptsLljg> getGjcList() {
		return gjcList;
	}

	public void setGjcList(List<JptsLljg> gjcList) {
		this.gjcList = gjcList;
	}

	public List<JptsLljg> getQdList() {
		return qdList;
	}

	public void setQdList(List<JptsLljg> qdList) {
		this.qdList = qdList;
	}

	public List<JptsLljg> getZtcList() {
		return ztcList;
	}

	public void setZtcList(List<JptsLljg> ztcList) {
		this.ztcList = ztcList;
	}

	public int getFlag() {
		return flag;
	}

	public void setFlag(int flag) {
		this.flag = flag;
	}

}
