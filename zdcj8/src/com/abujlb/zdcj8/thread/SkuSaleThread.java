package com.abujlb.zdcj8.thread;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import javax.annotation.PostConstruct;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.abujlb.AbujlbBeanFactory;
import com.abujlb.SpringBeanUtil;
import com.abujlb.zdcj8.bean.SkuSale;
import com.abujlb.zdcj8.tsdao.SjsSkusaleTsDao;
import com.abujlb.zdcj8.util.StrUtil;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;

/**
 * 数据蛇：竞品sku销量list 保存
 * 
 * <AUTHOR>
 * @date 2020-09-23 19:21:15
 */
@Component
@Scope("prototype") // 每次获得bean都会生成一个新的对象，不是单例的
public class SkuSaleThread implements Runnable {

	private static Logger log = Logger.getLogger(SkuSaleThread.class);

	private List<SkuSale> list;
	private String itemid; // 宝贝id

	@Autowired
	private SjsSkusaleTsDao sjsSkusaleTsDao;
	@Autowired
	private AbujlbBeanFactory abujlbBeanFactory;

	/**
	 * 项目重启之后会先执行init 在初始化的时候执行
	 * 
	 * @doc 说明：因为没有保存在数据库或redis中所以重启之后不能初始化
	 */
	@PostConstruct
	public void init() {
		try {
			// Thread t = new Thread(this);
			// t.setName("process.SkuSaleThread.Starter");
			// t.start();
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		}
	}

	/**
	 * 初始化数据
	 */
	public void initParam(List<SkuSale> list2, String itemid) {
		try {
			if (list2 != null && list2.size() > 0) { // java深度复制
				Gson gson = new GsonBuilder().setDateFormat("yyyy-MM-dd HH:mm:ss").create();
				String data = gson.toJson(list2);
				Type type = new TypeToken<ArrayList<SkuSale>>() {
				}.getType();
				this.list = gson.fromJson(data, type);
			}
			this.itemid = itemid;
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		}
		
	}

	/**
	 * 开始线程，不占用tomcat启动时间，防止启动超时
	 */
	@Override
	public void run() {
		try {
			SpringBeanUtil.setBeanFactory(abujlbBeanFactory); // 定时器，没有被拦截器拦截，spring上下文会丢失，加上这句即可
			// for (int i = 0; this.list != null && i < this.list.size() ; i++) {
			// SkuSale skuSale = list.get(i);
			// if (!StrUtil.isNull(skuSale.getItemid()) && !StrUtil.isNull(skuSale.getTjrq()) && !StrUtil.isNull(skuSale.getSkuid())) {
			// sjsSkusaleTsDao.updateRow(skuSale.getItemid(), skuSale.getTjrq(), skuSale.getSkuid(), skuSale.toString());
			// }
			// }

			if (this.list == null || this.list.size() < 1) {
				return;
			}
			List<String> rqList = new ArrayList<String>();
			List<List<SkuSale>> allList = new ArrayList<List<SkuSale>>();
			
			Collections.sort(this.list); // 按日期正序排序，即日期较大的在后面
			for (int i = (this.list.size() - 1); i >= 0; i--) {
				if (StrUtil.isNull(this.list.get(i).getTjrq())) {
					this.list.remove(i);
				}
			}
			while (this.list != null && this.list.size() > 0) {
				String rq = this.list.get(0).getTjrq();
				
				List<SkuSale> list2 = new ArrayList<SkuSale>();
				for (int i = (this.list.size() - 1); i >= 0; i--) { // 倒序
					if (rq.equals(this.list.get(i).getTjrq())) {
						list2.add(this.list.get(i));
						this.list.remove(i);
					}
				}
				if (list2 != null && list2.size() > 0) {
					rqList.add(rq);
					allList.add(list2);
				}
			}
			Gson gson = new GsonBuilder().setDateFormat("yyyy-MM-dd HH:mm:ss").create();
			for (int i = 0; rqList != null && i < rqList.size() ; i++) {
				sjsSkusaleTsDao.updateRow(this.itemid, rqList.get(i), gson.toJson(allList.get(i)));
			}
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		}
	}

	public List<SkuSale> getList() {
		return list;
	}

	public void setList(List<SkuSale> list) {
		this.list = list;
	}

	public String getItemid() {
		return itemid;
	}

	public void setItemid(String itemid) {
		this.itemid = itemid;
	}

}
