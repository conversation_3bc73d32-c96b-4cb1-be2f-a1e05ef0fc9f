var HEX_CHARS = "0123456789abcdef".split(""), EXTRA = [128, 32768, 8388608, -2147483648],
    SHIFT = [0, 8, 16, 24],
    OUTPUT_TYPES = ["hex", "array", "digest", "buffer", "arrayBuffer", "base64"],
    BASE64_ENCODE_CHAR = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".split(""),
    blocks = [],
    buffer8;
var buffer = new ArrayBuffer(68);
var ARRAY_BUFFER = true;
buffer8 = new Uint8Array(buffer),
blocks = new Uint32Array(buffer);
 function Md5(e) {
     if (e)
         blocks[0] = blocks[16] = blocks[1] = blocks[2] = blocks[3] = blocks[4] = blocks[5] = blocks[6] = blocks[7] = blocks[8] = blocks[9] = blocks[10] = blocks[11] = blocks[12] = blocks[13] = blocks[14] = blocks[15] = 0,
         this.blocks = blocks,
         this.buffer8 = buffer8;
     else if (ARRAY_BUFFER) {
         var t = new ArrayBuffer(68);
         this.buffer8 = new Uint8Array(t),
             this.blocks = new Uint32Array(t)
     } else
         this.blocks = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];
     this.h0 = this.h1 = this.h2 = this.h3 = this.start = this.bytes = this.hBytes = 0,
         this.finalized = this.hashed = !1,
         this.first = !0
 }
 Md5.prototype.update = function (e) {
         if (!this.finalized) {
             var t, n = typeof e;
             if ("string" !== n) {
                 if ("object" !== n)
                     throw ERROR;
                 if (null === e)
                     throw ERROR;
                 if (ARRAY_BUFFER && e.constructor === ArrayBuffer)
                     e = new Uint8Array(e);
                 else if (!(Array.isArray(e) || ARRAY_BUFFER && ArrayBuffer.isView(e)))
                     throw ERROR;
                 t = !0
             }
             for (var i, r, o = 0, a = e.length, s = this.blocks, l = this.buffer8; o < a;) {
                 if (this.hashed && (this.hashed = !1,
                         s[0] = s[16],
                         s[16] = s[1] = s[2] = s[3] = s[4] = s[5] = s[6] = s[7] = s[8] = s[9] = s[10] = s[11] = s[12] = s[13] = s[14] = s[15] = 0),
                     t)
                     if (ARRAY_BUFFER)
                         for (r = this.start; o < a && r < 64; ++o)
                             l[r++] = e[o];
                     else
                         for (r = this.start; o < a && r < 64; ++o)
                             s[r >> 2] |= e[o] << SHIFT[3 & r++];
                 else if (ARRAY_BUFFER)
                     for (r = this.start; o < a && r < 64; ++o)
                         (i = e.charCodeAt(o)) < 128 ? l[r++] = i : i < 2048 ? (l[r++] = 192 | i >> 6,
                             l[r++] = 128 | 63 & i) : i < 55296 || i >= 57344 ? (l[r++] = 224 | i >> 12,
                             l[r++] = 128 | i >> 6 & 63,
                             l[r++] = 128 | 63 & i) : (i = 65536 + ((1023 & i) << 10 | 1023 & e.charCodeAt(++o)),
                             l[r++] = 240 | i >> 18,
                             l[r++] = 128 | i >> 12 & 63,
                             l[r++] = 128 | i >> 6 & 63,
                             l[r++] = 128 | 63 & i);
                 else
                     for (r = this.start; o < a && r < 64; ++o)
                         (i = e.charCodeAt(o)) < 128 ? s[r >> 2] |= i << SHIFT[3 & r++] : i < 2048 ? (s[r >> 2] |= (192 | i >> 6) << SHIFT[3 & r++],
                             s[r >> 2] |= (128 | 63 & i) << SHIFT[3 & r++]) : i < 55296 || i >= 57344 ? (s[r >> 2] |= (224 | i >> 12) << SHIFT[3 & r++],
                             s[r >> 2] |= (128 | i >> 6 & 63) << SHIFT[3 & r++],
                             s[r >> 2] |= (128 | 63 & i) << SHIFT[3 & r++]) : (i = 65536 + ((1023 & i) << 10 | 1023 & e.charCodeAt(++o)),
                             s[r >> 2] |= (240 | i >> 18) << SHIFT[3 & r++],
                             s[r >> 2] |= (128 | i >> 12 & 63) << SHIFT[3 & r++],
                             s[r >> 2] |= (128 | i >> 6 & 63) << SHIFT[3 & r++],
                             s[r >> 2] |= (128 | 63 & i) << SHIFT[3 & r++]);
                 this.lastByteIndex = r,
                     this.bytes += r - this.start,
                     r >= 64 ? (this.start = r - 64,
                         this.hash(),
                         this.hashed = !0) : this.start = r
             }
             return this.bytes > 4294967295 && (this.hBytes += this.bytes / 4294967296 << 0,
                     this.bytes = this.bytes % 4294967296),
                 this
         }
     },
     Md5.prototype.finalize = function () {
         if (!this.finalized) {
             this.finalized = !0;
             var e = this.blocks,
                 t = this.lastByteIndex;
             e[t >> 2] |= EXTRA[3 & t],
                 t >= 56 && (this.hashed || this.hash(),
                     e[0] = e[16],
                     e[16] = e[1] = e[2] = e[3] = e[4] = e[5] = e[6] = e[7] = e[8] = e[9] = e[10] = e[11] = e[12] = e[13] = e[14] = e[15] = 0),
                 e[14] = this.bytes << 3,
                 e[15] = this.hBytes << 3 | this.bytes >>> 29,
                 this.hash()
         }
     },
     Md5.prototype.hash = function () {
         var e, t, n, i, r, o, a = this.blocks;
         this.first ? t = ((t = ((e = ((e = a[0] - 680876937) << 7 | e >>> 25) - 271733879 << 0) ^ (n = ((n = (-271733879 ^ (i = ((i = (-1732584194 ^ 2004318071 & e) + a[1] - 117830708) << 12 | i >>> 20) + e << 0) & (-271733879 ^ e)) + a[2] - 1126478375) << 17 | n >>> 15) + i << 0) & (i ^ e)) + a[3] - 1316259209) << 22 | t >>> 10) + n << 0 : (e = this.h0,
                 t = this.h1,
                 n = this.h2,
                 t = ((t += ((e = ((e += ((i = this.h3) ^ t & (n ^ i)) + a[0] - 680876936) << 7 | e >>> 25) + t << 0) ^ (n = ((n += (t ^ (i = ((i += (n ^ e & (t ^ n)) + a[1] - 389564586) << 12 | i >>> 20) + e << 0) & (e ^ t)) + a[2] + 606105819) << 17 | n >>> 15) + i << 0) & (i ^ e)) + a[3] - 1044525330) << 22 | t >>> 10) + n << 0),
             t = ((t += ((e = ((e += (i ^ t & (n ^ i)) + a[4] - 176418897) << 7 | e >>> 25) + t << 0) ^ (n = ((n += (t ^ (i = ((i += (n ^ e & (t ^ n)) + a[5] + 1200080426) << 12 | i >>> 20) + e << 0) & (e ^ t)) + a[6] - 1473231341) << 17 | n >>> 15) + i << 0) & (i ^ e)) + a[7] - 45705983) << 22 | t >>> 10) + n << 0,
             t = ((t += ((e = ((e += (i ^ t & (n ^ i)) + a[8] + 1770035416) << 7 | e >>> 25) + t << 0) ^ (n = ((n += (t ^ (i = ((i += (n ^ e & (t ^ n)) + a[9] - 1958414417) << 12 | i >>> 20) + e << 0) & (e ^ t)) + a[10] - 42063) << 17 | n >>> 15) + i << 0) & (i ^ e)) + a[11] - 1990404162) << 22 | t >>> 10) + n << 0,
             t = ((t += ((e = ((e += (i ^ t & (n ^ i)) + a[12] + 1804603682) << 7 | e >>> 25) + t << 0) ^ (n = ((n += (t ^ (i = ((i += (n ^ e & (t ^ n)) + a[13] - 40341101) << 12 | i >>> 20) + e << 0) & (e ^ t)) + a[14] - 1502002290) << 17 | n >>> 15) + i << 0) & (i ^ e)) + a[15] + 1236535329) << 22 | t >>> 10) + n << 0,
             t = ((t += ((i = ((i += (t ^ n & ((e = ((e += (n ^ i & (t ^ n)) + a[1] - 165796510) << 5 | e >>> 27) + t << 0) ^ t)) + a[6] - 1069501632) << 9 | i >>> 23) + e << 0) ^ e & ((n = ((n += (e ^ t & (i ^ e)) + a[11] + 643717713) << 14 | n >>> 18) + i << 0) ^ i)) + a[0] - 373897302) << 20 | t >>> 12) + n << 0,
             t = ((t += ((i = ((i += (t ^ n & ((e = ((e += (n ^ i & (t ^ n)) + a[5] - 701558691) << 5 | e >>> 27) + t << 0) ^ t)) + a[10] + 38016083) << 9 | i >>> 23) + e << 0) ^ e & ((n = ((n += (e ^ t & (i ^ e)) + a[15] - 660478335) << 14 | n >>> 18) + i << 0) ^ i)) + a[4] - 405537848) << 20 | t >>> 12) + n << 0,
             t = ((t += ((i = ((i += (t ^ n & ((e = ((e += (n ^ i & (t ^ n)) + a[9] + 568446438) << 5 | e >>> 27) + t << 0) ^ t)) + a[14] - 1019803690) << 9 | i >>> 23) + e << 0) ^ e & ((n = ((n += (e ^ t & (i ^ e)) + a[3] - 187363961) << 14 | n >>> 18) + i << 0) ^ i)) + a[8] + 1163531501) << 20 | t >>> 12) + n << 0,
             t = ((t += ((i = ((i += (t ^ n & ((e = ((e += (n ^ i & (t ^ n)) + a[13] - 1444681467) << 5 | e >>> 27) + t << 0) ^ t)) + a[2] - 51403784) << 9 | i >>> 23) + e << 0) ^ e & ((n = ((n += (e ^ t & (i ^ e)) + a[7] + 1735328473) << 14 | n >>> 18) + i << 0) ^ i)) + a[12] - 1926607734) << 20 | t >>> 12) + n << 0,
             t = ((t += ((o = (i = ((i += ((r = t ^ n) ^ (e = ((e += (r ^ i) + a[5] - 378558) << 4 | e >>> 28) + t << 0)) + a[8] - 2022574463) << 11 | i >>> 21) + e << 0) ^ e) ^ (n = ((n += (o ^ t) + a[11] + 1839030562) << 16 | n >>> 16) + i << 0)) + a[14] - 35309556) << 23 | t >>> 9) + n << 0,
             t = ((t += ((o = (i = ((i += ((r = t ^ n) ^ (e = ((e += (r ^ i) + a[1] - 1530992060) << 4 | e >>> 28) + t << 0)) + a[4] + 1272893353) << 11 | i >>> 21) + e << 0) ^ e) ^ (n = ((n += (o ^ t) + a[7] - 155497632) << 16 | n >>> 16) + i << 0)) + a[10] - 1094730640) << 23 | t >>> 9) + n << 0,
             t = ((t += ((o = (i = ((i += ((r = t ^ n) ^ (e = ((e += (r ^ i) + a[13] + 681279174) << 4 | e >>> 28) + t << 0)) + a[0] - 358537222) << 11 | i >>> 21) + e << 0) ^ e) ^ (n = ((n += (o ^ t) + a[3] - 722521979) << 16 | n >>> 16) + i << 0)) + a[6] + 76029189) << 23 | t >>> 9) + n << 0,
             t = ((t += ((o = (i = ((i += ((r = t ^ n) ^ (e = ((e += (r ^ i) + a[9] - 640364487) << 4 | e >>> 28) + t << 0)) + a[12] - 421815835) << 11 | i >>> 21) + e << 0) ^ e) ^ (n = ((n += (o ^ t) + a[15] + 530742520) << 16 | n >>> 16) + i << 0)) + a[2] - 995338651) << 23 | t >>> 9) + n << 0,
             t = ((t += ((i = ((i += (t ^ ((e = ((e += (n ^ (t | ~i)) + a[0] - 198630844) << 6 | e >>> 26) + t << 0) | ~n)) + a[7] + 1126891415) << 10 | i >>> 22) + e << 0) ^ ((n = ((n += (e ^ (i | ~t)) + a[14] - 1416354905) << 15 | n >>> 17) + i << 0) | ~e)) + a[5] - 57434055) << 21 | t >>> 11) + n << 0,
             t = ((t += ((i = ((i += (t ^ ((e = ((e += (n ^ (t | ~i)) + a[12] + 1700485571) << 6 | e >>> 26) + t << 0) | ~n)) + a[3] - 1894986606) << 10 | i >>> 22) + e << 0) ^ ((n = ((n += (e ^ (i | ~t)) + a[10] - 1051523) << 15 | n >>> 17) + i << 0) | ~e)) + a[1] - 2054922799) << 21 | t >>> 11) + n << 0,
             t = ((t += ((i = ((i += (t ^ ((e = ((e += (n ^ (t | ~i)) + a[8] + 1873313359) << 6 | e >>> 26) + t << 0) | ~n)) + a[15] - 30611744) << 10 | i >>> 22) + e << 0) ^ ((n = ((n += (e ^ (i | ~t)) + a[6] - 1560198380) << 15 | n >>> 17) + i << 0) | ~e)) + a[13] + 1309151649) << 21 | t >>> 11) + n << 0,
             t = ((t += ((i = ((i += (t ^ ((e = ((e += (n ^ (t | ~i)) + a[4] - 145523070) << 6 | e >>> 26) + t << 0) | ~n)) + a[11] - 1120210379) << 10 | i >>> 22) + e << 0) ^ ((n = ((n += (e ^ (i | ~t)) + a[2] + 718787259) << 15 | n >>> 17) + i << 0) | ~e)) + a[9] - 343485551) << 21 | t >>> 11) + n << 0,
             this.first ? (this.h0 = e + 1732584193 << 0,
                 this.h1 = t - 271733879 << 0,
                 this.h2 = n - 1732584194 << 0,
                 this.h3 = i + 271733878 << 0,
                 this.first = !1) : (this.h0 = this.h0 + e << 0,
                 this.h1 = this.h1 + t << 0,
                 this.h2 = this.h2 + n << 0,
                 this.h3 = this.h3 + i << 0)
     },
     Md5.prototype.hex = function () {
         this.finalize();
         var e = this.h0,
             t = this.h1,
             n = this.h2,
             i = this.h3;
         return HEX_CHARS[e >> 4 & 15] + HEX_CHARS[15 & e] + HEX_CHARS[e >> 12 & 15] + HEX_CHARS[e >> 8 & 15] + HEX_CHARS[e >> 20 & 15] + HEX_CHARS[e >> 16 & 15] + HEX_CHARS[e >> 28 & 15] + HEX_CHARS[e >> 24 & 15] + HEX_CHARS[t >> 4 & 15] + HEX_CHARS[15 & t] + HEX_CHARS[t >> 12 & 15] + HEX_CHARS[t >> 8 & 15] + HEX_CHARS[t >> 20 & 15] + HEX_CHARS[t >> 16 & 15] + HEX_CHARS[t >> 28 & 15] + HEX_CHARS[t >> 24 & 15] + HEX_CHARS[n >> 4 & 15] + HEX_CHARS[15 & n] + HEX_CHARS[n >> 12 & 15] + HEX_CHARS[n >> 8 & 15] + HEX_CHARS[n >> 20 & 15] + HEX_CHARS[n >> 16 & 15] + HEX_CHARS[n >> 28 & 15] + HEX_CHARS[n >> 24 & 15] + HEX_CHARS[i >> 4 & 15] + HEX_CHARS[15 & i] + HEX_CHARS[i >> 12 & 15] + HEX_CHARS[i >> 8 & 15] + HEX_CHARS[i >> 20 & 15] + HEX_CHARS[i >> 16 & 15] + HEX_CHARS[i >> 28 & 15] + HEX_CHARS[i >> 24 & 15]
     },
     Md5.prototype.toString = Md5.prototype.hex,
     Md5.prototype.digest = function () {
         this.finalize();
         var e = this.h0,
             t = this.h1,
             n = this.h2,
             i = this.h3;
         return [255 & e, e >> 8 & 255, e >> 16 & 255, e >> 24 & 255, 255 & t, t >> 8 & 255, t >> 16 & 255, t >> 24 & 255, 255 & n, n >> 8 & 255, n >> 16 & 255, n >> 24 & 255, 255 & i, i >> 8 & 255, i >> 16 & 255, i >> 24 & 255]
     },
     Md5.prototype.array = Md5.prototype.digest,
     Md5.prototype.arrayBuffer = function () {
         this.finalize();
         var e = new ArrayBuffer(16),
             t = new Uint32Array(e);
         return t[0] = this.h0,
             t[1] = this.h1,
             t[2] = this.h2,
             t[3] = this.h3,
             e
     },
     Md5.prototype.buffer = Md5.prototype.arrayBuffer,
     Md5.prototype.base64 = function () {
         for (var e, t, n, i = "", r = this.array(), o = 0; o < 15;)
             e = r[o++],
             t = r[o++],
             n = r[o++],
             i += BASE64_ENCODE_CHAR[e >>> 2] + BASE64_ENCODE_CHAR[63 & (e << 4 | t >>> 4)] + BASE64_ENCODE_CHAR[63 & (t << 2 | n >>> 6)] + BASE64_ENCODE_CHAR[63 & n];
         return e = r[o],
             i += BASE64_ENCODE_CHAR[e >>> 2] + BASE64_ENCODE_CHAR[e << 4 & 63] + "=="
     };