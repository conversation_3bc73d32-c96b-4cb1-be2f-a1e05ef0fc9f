package com.abujlb.zdcj8.script;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.PostConstruct;
import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;

import org.apache.http.client.CookieStore;
import org.apache.http.cookie.Cookie;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;

import com.abujlb.zdcj8.bean.CookieVo;
import com.abujlb.zdcj8.util.FileToJson;
import com.abujlb.zdcj8.util.StrUtil;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

/**
 * js破解，生意参谋阿明工具，md5加密
 * 
 * <AUTHOR>
 * @date 2021-02-01 10:56:57
 */
@Component
public class SycmIsgJSEngine {

	private static Logger log = Logger.getLogger(SycmIsgJSEngine.class);

	private ScriptEngine engine;
	/**
	 * 初始化
	 * 
	 * @doc 
	 */
	@PostConstruct
	public void init() {
		try {
			ScriptEngineManager scriptEngineManager = new ScriptEngineManager();
			this.engine = scriptEngineManager.getEngineByName("JavaScript"); // this.engine = scriptEngineManager.getEngineByName("js"); 或者 this.engine = scriptEngineManager.getEngineByName("text/javascript");
			String javascript = FileToJson.readFile("/com/abujlb/zdcj8/script/sycm_isg.js"); // FileToJson.readFile(new File("src/com/abujlb/zdcjsjs/http/sycm_core.js"));
			this.engine.eval(javascript); // 准备执行js
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		}
	}
	
	/**
	 * 获取cookieStr
	 */
	public String getDocumentCookie(CookieStore cookieStore) {
		String result = "";
		Gson gson = new GsonBuilder().setDateFormat("yyyy-MM-dd HH:mm:ss").create();
		try {
			List<CookieVo> cookieVoList = new ArrayList<CookieVo>();
			if (cookieStore != null && cookieStore.getCookies().size() > 0) {
				List<Cookie> list = cookieStore.getCookies();
				for (int i = 0; i < list.size(); i ++) {
					CookieVo cookieVo = new CookieVo(gson.toJson(list.get(i)));
//					if (!cookieVo.getCookieDomain().endsWith("taobao.com")) {
//						continue;
//					}
					if (!cookieVo.getCookieDomain().equals(".taobao.com") && !cookieVo.getCookieDomain().equals("sycm.taobao.com") && 
							!cookieVo.getCookieDomain().equals(".sycm.taobao.com") && !cookieVo.getCookieDomain().equals("taobao.com") && 
							!cookieVo.getCookieDomain().equals("mmstat.com") && !cookieVo.getCookieDomain().equals(".mmstat.com")) {
						continue;
					}
					if (cookieVo.getName().equalsIgnoreCase("isg")) {
						continue;
					}
					cookieVoList.add(cookieVo);
				}
			}
			for (int i = 0; cookieVoList != null && i < cookieVoList.size(); i ++) {
				CookieVo cookie = cookieVoList.get(i);
				if (i == (cookieVoList.size() - 1)) { // 最后一个
					// result = result + cookie.getName() + "=" + URLEncoder.encode(cookie.getValue(), "UTF-8");
					result = result + cookie.getName() + "=" + cookie.getValue();
				} else {
					// result = result + cookie.getName() + "=" + URLEncoder.encode(cookie.getValue(), "UTF-8") + "; ";
					result = result + cookie.getName() + "=" + cookie.getValue() + "; ";
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		}
		return result;
	}
	
	/**
	 * 初始化cookie
	 */
	public void cookieInit(String cookie) {
		try {
			String js = " u.cookie = '" + cookie + "'";
			this.engine.eval(js);
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		}
	}
	
	/**
	 * 获取isg
	 * 
	 * @param str
	 */
	public String getIsg(String cookie) {
		try {
			String js = " u.cookie = '" + cookie + "'";
			this.engine.eval(js);
			Object res = this.engine.eval("L.sa(!0, !1)");
			return res.toString();
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		}
		return null;
	}
	
	/**
	 * 获取isg
	 * 
	 * @param str
	 */
	public String getIsg2(String cookie) {
		try {
			String js = " u.cookie = '" + cookie + "'";
			this.engine.eval(js);
			Object res = this.engine.eval("L.sa(!1, !1)");
			if (res != null && !StrUtil.isNull(res.toString())) {
				String isg = res.toString();
				this.engine.eval(" isg_init = '" + isg + "' ");
				js = " u.cookie = '" + cookie + "; isg=" + isg + "'";
				this.engine.eval(js);
			}
			res = engine.eval("L.sa(!0, !1)");
			if (res != null && !StrUtil.isNull(res.toString())) {
				String isg = res.toString();
				this.engine.eval(" isg_init = '" + isg + "' ");
				return isg;
			}
			return "";
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		}
		return null;
	}

	/**
	 * escape
	 * 
	 * @param str
	 */
	public String escape(String str) {
		try {
			Object res = engine.eval("escape('" + str + "')");
			return res.toString();
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		}
		return null;
	}

	/**
	 * escape
	 * 
	 * @param str
	 */
	public String unescape(String str) {
		try {
			Object res = engine.eval("unescape('" + str + "')");
			return res.toString();
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		}
		return null;
	}

}
