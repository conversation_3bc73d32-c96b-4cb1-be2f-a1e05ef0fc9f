package com.abujlb.zdcj8.script;

import javax.annotation.PostConstruct;
import javax.script.Invocable;
import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;

import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;

import com.abujlb.zdcj8.util.FileToJson;

/**
 * 店铺上新，js引擎
 * 
 * <AUTHOR>
 * @date 2020-11-05 10:03:32
 */
@Component
public class DpsxSignJSEngine {

	private static Logger log = Logger.getLogger(DpsxSignJSEngine.class);

	private ScriptEngine engine;

	/**
	 * 初始化
	 * 
	 * @doc 
	 */
	@PostConstruct
	public void init() {
		try {
			ScriptEngineManager scriptEngineManager = new ScriptEngineManager();
			this.engine = scriptEngineManager.getEngineByName("JavaScript"); // this.engine = scriptEngineManager.getEngineByName("js"); 或者 this.engine = scriptEngineManager.getEngineByName("text/javascript");
			String javascript = FileToJson.readFile("/com/abujlb/zdcj8/script/dpsx_sign.js"); // FileToJson.readFile(new File("src/com/abujlb/zdcjsjs/http/md5_sjs.js"));
			this.engine.eval(javascript); // 准备执行js
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		}
	}

	/**
	 * 执行js，获取key（Md5加密值）
	 * 
	 * @param s 时间戳
	 * @param appkey
	 * @param data对象，{"userId":2616970884,"lastTime":0,"tab":1}
	 * @param token 是cookie返回的_m_h5_tk
	 * @执行 getSign函数
	 */
	public String getSign(String timestamp, String appkey, String data, String token) {
		try {
			Invocable invocable = (Invocable) this.engine;
			Object res = invocable.invokeFunction("getSign", new Object[] { timestamp, appkey, data, token });
			return res.toString();
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		}
		return null;
	}

	/**
	 * escape
	 * 
	 * @param str
	 */
	public String escape(String str) {
		try {
			Object res = engine.eval("escape('" + str + "')");
			return res.toString();
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		}
		return null;
	}

	/**
	 * escape
	 * 
	 * @param str
	 */
	public String unescape(String str) {
		try {
			Object res = engine.eval("unescape('" + str + "')");
			return res.toString();
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		}
		return null;
	}

}
