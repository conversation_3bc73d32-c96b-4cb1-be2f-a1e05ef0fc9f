package com.abujlb.zdcj8.script;

import javax.annotation.PostConstruct;
import javax.script.Invocable;
import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;

import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;

import com.abujlb.zdcj8.util.FileToJson;

/**
 * 直通车，js加密签名
 * 
 * <AUTHOR>
 * @date 2020-11-11 16:02:34
 */
@Component
public class ZtcHashJSEngine {

	private static Logger log = Logger.getLogger(ZtcHashJSEngine.class);

	private ScriptEngine engine;

	/**
	 * 初始化
	 * 
	 * @doc 
	 */
	@PostConstruct
	public void init() {
		try {
			ScriptEngineManager scriptEngineManager = new ScriptEngineManager();
			this.engine = scriptEngineManager.getEngineByName("JavaScript"); // this.engine = scriptEngineManager.getEngineByName("js"); 或者 this.engine = scriptEngineManager.getEngineByName("text/javascript");
			String javascript = FileToJson.readFile("/com/abujlb/zdcj8/script/get-hash.js");
			this.engine.eval(javascript); // 准备执行js
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		}
	}

	/**
	 * 执行js，获取key（Md5加密值）
	 * 
	 * @param timestamp 时间戳
	 * @param ztcUser 直通车对象
	 * @执行 getSign函数
	 */
	public String getHash(String timestamp, String ztcUser) {
		try {
			Invocable invocable = (Invocable) this.engine;
			Object res = invocable.invokeFunction("default_2", new Object[] { timestamp, ztcUser});
			return res.toString();
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		}
		return null;
	}
	
	/**
	 * 执行js，获取SessionId（原js：https://g.alicdn.com/mm/subway-plus/20230302.183141.204/subway-plus/dataplus/dataplus/index.js）
	 */
	public String webOpSessionIdByJs() {
		try {
			Object res = engine.eval("Number(Math.random().toString().substr(2, 4) + Date.now()).toString(36)");
			return res.toString();
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		}
		return null;
	}
	
	/**
	 * java获取SessionId（十进制转36进制）
	 */
	public String webOpSessionId() {
		try {
			double d1 = Math.random();
			String timeStamp = String.valueOf(System.currentTimeMillis()); // 13位时间戳
			String str = String.valueOf(d1).substring(2, 6) + timeStamp;
			return Long.toString(Long.valueOf(str), 36); // 十进制转36进制
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		}
		return null;
	}

}
