package com.abujlb.zdcj8.script;

import java.io.File;
import java.io.FileReader;
import java.util.Map;
import java.util.TreeMap;

import javax.annotation.PostConstruct;
import javax.script.Invocable;
import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.SimpleBindings;

import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;

import com.abujlb.zdcj8.util.FileToJson;

import jdk.nashorn.api.scripting.ScriptObjectMirror;

/**
 * 数据蛇http请求
 * 
 * <AUTHOR>
 * @date 2020-09-15 18:00:41
 */
@Component
public class Md5SjsJSEngine {

	private static Logger log = Logger.getLogger(Md5SjsJSEngine.class);

	private ScriptEngine engine;
	// private ScriptObjectMirror md5;

	/**
	 * 初始化
	 * 
	 * @doc 
	 */
	@PostConstruct
	public void init() {
		try {
			ScriptEngineManager scriptEngineManager = new ScriptEngineManager();
			this.engine = scriptEngineManager.getEngineByName("JavaScript"); // this.engine = scriptEngineManager.getEngineByName("js"); 或者 this.engine = scriptEngineManager.getEngineByName("text/javascript");
			String javascript = FileToJson.readFile("/com/abujlb/zdcj8/script/md5_sjs.js"); // FileToJson.readFile(new File("src/com/abujlb/zdcj8/script/md5_sjs.js"));
			this.engine.eval(javascript); // 准备执行js
			// this.md5 = (ScriptObjectMirror) this.engine.eval("new Md5(true);"); // 执行初始化方法
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		}
	}

	/**
	 * 执行js，获取key（Md5加密值）
	 * 
	 * @param new String(this.tm) + new String(this.taguId);
	 * @执行 new Md5(true).update("160015934600085950")["hex"]()
	 */
	public String update(String t) {
		try {
			ScriptObjectMirror md5 = (ScriptObjectMirror) this.engine.eval("new Md5(true);"); // 执行初始化方法
			Invocable invocable = (Invocable) this.engine;
			// 执行md5对象的名为update的方法
			ScriptObjectMirror updateMap = (ScriptObjectMirror) invocable.invokeMethod(md5, "update", t);

			// ScriptObjectMirror hex = (ScriptObjectMirror) updateMap.get("hex");
			return updateMap.callMember("hex").toString();
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		}
		return null;
	}

	/**
	 * 执行js，获取key（Md5加密值）
	 * 
	 * @param new String(this.tm) + new String(this.taguId);
	 * @执行 new Md5(true).update("160015934600085950")["hex"]()
	 */
	public String update2(String t) {
		try {
			Object key = this.engine.eval("new Md5(true).update(\"" + t + "\")[\"hex\"]();"); // 执行初始化方法
			return key.toString();
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		}
		return null;
	}
	
	/**
	 * 执行js，获取key（Md5加密值）
	 * 
	 * @param new String(this.tm) + new String(this.taguId);
	 * @执行 new Md5(true).update("160015934600085950")["hex"]()
	 */
	public String getKey(String currentTime, String userId) {
		try {
			String t = "AUT$090" + currentTime + "y9svt!" + userId + "807FnJdg";
			return update(t);
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		}
		return null;
	}

	/**
	 * escape
	 * 
	 * @param str
	 */
	public String escape(String str) {
		try {
			Object res = engine.eval("escape('" + str + "')");
			return res.toString();
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		}
		return null;
	}

	/**
	 * escape
	 * 
	 * @param str
	 */
	public String unescape(String str) {
		try {
			Object res = engine.eval("unescape('" + str + "')");
			return res.toString();
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		}
		return null;
	}

	public void getValues() throws Exception {
		String js = "  var msg='hello';          " + "  var number = 123;         " + "  var array=['A','B','C'];  " + "  var json={                " + "      'name':'pd',          "
				+ "      'subjson':{           " + "           'subname':'spd'  " + "           ,'id':123        " + "           }                " + "      };                    ";
		this.engine.eval(js);
		js = "msg+=' world';number+=5";
		System.out.println(this.engine.get("msg"));
		System.out.println(this.engine.get("number"));
		// 获取数组
		ScriptObjectMirror array = (ScriptObjectMirror) this.engine.get("array");
		System.out.println(array.getSlot(0));

		ScriptObjectMirror json = (ScriptObjectMirror) this.engine.get("json");
		System.out.println(json.get("name"));

		// json嵌套
		ScriptObjectMirror subjson = (ScriptObjectMirror) json.get("subjson");
		System.out.println(subjson.get("subname"));
	}

	public void getObject() throws Exception {
		String js = "  var obj=new Object();     " + "  obj.info='hello world';   " + "  obj.getInfo=function(){   " + "        return this.info;   " + "  };                        ";
		this.engine.eval(js);
		ScriptObjectMirror obj = (ScriptObjectMirror) this.engine.get("obj");
		System.out.println(obj.get("info"));
		System.out.println(obj.get("getInfo"));
		js = "obj.getInfo()";
		System.out.println(this.engine.eval(js));
	}

	// 给js传递变量
	public void putValue() throws Exception {
		String js = "Math.pow(a,b)";
		Map<String, Object> input = new TreeMap<>();
		input.put("a", 2);
		input.put("b", 8);
		System.out.println(this.engine.eval(js, new SimpleBindings(input)));
	}

	// 调用js函数
	public void callJSFunction() throws Exception {
		String js = "function add (a, b) {return a+b; }";
		// 执行js脚本定义函数
		this.engine.eval(js);
		Invocable invocable = (Invocable) this.engine;
		Object res = invocable.invokeFunction("add", new Object[] { 2, 3 });
		System.out.println(res);

	}

	// 读取js文件，执行函数;易变业务使用脚本编写，这样即使修改脚本，也不需重新部署java程序
	public void callJSFunctionFromFile() throws Exception {
		// 执行js
		while (true) {
			// 模拟执行期间add.js被修改
			Thread.sleep(5000);
			this.engine.eval(new FileReader("e:\\add.js"));
			Invocable invocable = (Invocable) this.engine;
			Object res = invocable.invokeFunction("add", new Object[] { 2, 3 });
			System.out.println(res);
		}
	}

	public static void main(String[] args) throws Exception {
		File directory = new File("");// 参数为空
		String courseFile = directory.getCanonicalPath();
		System.out.println(courseFile);

		System.out.println(System.getProperty("java.class.path"));

		System.out.println(System.getProperty("user.dir"));
	}

}
