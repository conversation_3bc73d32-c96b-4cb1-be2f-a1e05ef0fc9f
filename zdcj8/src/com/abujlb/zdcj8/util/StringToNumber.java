package com.abujlb.zdcj8.util;

/**
 * 字符串转数字
 * 
 * <AUTHOR>
 * @date 2020-09-18 10:04:29
 */
public class StringToNumber {

	public static double transform(String s) {
		if (s == null || s.trim().length() == 0) {
			return 0D;
		}
		try {
			s = s.trim();
			if (s.matches("\\d+\\.\\d*%|[-+]{0,1}\\d*\\.\\d+%")) {
				String s1 = s.replaceAll("%", "");
				return Double.valueOf(s1) * 0.01;
			}
			if (s.matches("\\d+\\.\\d*|[-+]{0,1}\\d*\\.\\d+")) {
				String s1 = s.replaceAll("%", "");
				return Double.valueOf(s1);
			}
		} catch (Exception e) {
			return 0D;
		}
		return 0D;
	}

	public static int transformInt(String s) {
		double d = transform(s);
		return (int) d;
	}

	public static void main(String[] args) {
		int d = transformInt("3.9999999");
		System.out.println(d);
	}
}
