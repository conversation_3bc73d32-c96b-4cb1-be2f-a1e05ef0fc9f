package com.abujlb.zdcj8.util;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;

import com.abujlb.zdcj8.bean.SkuSale;
import com.google.gson.Gson;

/**
 * 读取Excel文件
 * 
 * <AUTHOR>
 * @date 2020-09-18 10:02:40
 */
public class SkuXLSReader {

	/**
	 * 获取数据蛇：竞品sku销量
	 * 
	 * @param filename 文件路径
	 * @param date 采集日期、创建日期
	 */
	public synchronized static List<SkuSale> parseXLS(String filename, Date date) {
		List<SkuSale> skuSaleList = new ArrayList<SkuSale>();
		InputStream is = null;
		try {
			is = new FileInputStream(filename);
			Workbook wb = new HSSFWorkbook(is);
			Sheet sheet = wb.getSheetAt(0);
			Iterator<Row> rows = sheet.rowIterator();
			boolean start = false;
			while (rows.hasNext()) {
				Row row = rows.next();
				String title = row.getCell(0).getStringCellValue();
				if (!start && title.equals("统计日期")) {
					start = true;
					continue;
				}
				if (start) {
					int cols = row.getPhysicalNumberOfCells();
					SkuSale skuSale = new SkuSale();
					skuSale.setTjrq(getRowStr(row, cols, 0)); // 统计日期
					skuSale.setItemid(getRowStr(row, cols, 1)); // 商品id
					skuSale.setItemmc(getRowStr(row, cols, 2)); // 商品名称
					skuSale.setSkuid(getRowStr(row, cols, 3)); //
					skuSale.setSkumc(getRowStr(row, cols, 4)); // sku名称
					skuSale.setJgjs(getRowInt(row, cols, 5)); // 加购件数
					skuSale.setXdjs(getRowInt(row, cols, 6)); // 下单件数
					skuSale.setXdje(getRowDouble(row, cols, 7)); // 下单金额
					skuSale.setXdmjs(getRowInt(row, cols, 8)); // 下单买家数
					skuSale.setZfjs(getRowInt(row, cols, 9)); // 支付件数
					skuSale.setZfje(getRowDouble(row, cols, 10)); // 支付金额
					skuSale.setZfmjs(getRowInt(row, cols, 11)); // 支付买家数
					// skuSale.setRq(date);
					skuSaleList.add(skuSale);
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			try {
				is.close();
			} catch (Exception e) {
			}
			new File(filename).delete();
		}
		return skuSaleList;
	}

	public synchronized static String getRowStr(Row row, int cols, int col) {
		if (col < 0 || col >= cols) {
			return null;
		}
		return row.getCell(col).getStringCellValue();
	}

	public synchronized static double getRowDouble(Row row, int cols, int col) {
		if (col < 0 || col >= cols) {
			return 0D;
		}
		Cell cell = row.getCell(col);
		if (cell.getCellType() == Cell.CELL_TYPE_NUMERIC) {
			return cell.getNumericCellValue();
		}
		String str = row.getCell(col).getStringCellValue();
		return StringToNumber.transform(str);
	}

	public synchronized static Date getRowDate(Row row, int cols, int col) {
		try {
			DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			String str = row.getCell(col).getStringCellValue();
			return df.parse(str);
		} catch (Exception e) {
			return null;
		}
	}

	public synchronized static int getRowInt(Row row, int cols, int col) {
		double val = getRowDouble(row, cols, col);
		return (int) val;
	}

	// main方法
	public static void main(String[] args) {
		String filename = "C:\\Users\\<USER>\\Desktop\\580442138828sku - 副本.xls";
		Date date = new Date();
		Gson gson = new Gson();
		List<SkuSale> list = parseXLS(filename, date);
		if (list != null) {
			System.out.println("总数：" + list.size());
			// System.out.println(gson.toJson(list));
			String2Oss.stringToJsonFile(gson.toJson(list), "C:\\Users\\<USER>\\Desktop\\最近30天.txt", false, false);
		}
	}
}