package com.abujlb.zdcj8.util;

import java.io.BufferedWriter;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

/**
 * 字符串转oss
 * 
 * <AUTHOR>
 * @date 2019-01-15 11:19:49
 */
public class String2Oss {

	private static Logger log = Logger.getLogger(String2Oss.class);

	/**
	 * 过滤emoji 或者 其他非文字类型的字符
	 * 
	 * @param source
	 * @return
	 */
	public static String filterEmoji(String source) {
		if (StrUtil.isNull(source)) {
			return source;
		}
		if (StringUtils.isBlank(source)) {
			return source;
		}
		StringBuilder buf = new StringBuilder(source.length());
		int len = source.length();
		for (int i = 0; i < len; i++) {
			char codePoint = source.charAt(i);
			if (isEmojiCharacter(codePoint)) {
				buf.append(codePoint);
			}
		}
		String str = buf.toString();
		str = str.replaceAll(" ", " ").replaceAll("✪", "").replaceAll("▽", "").
				replaceAll("&lsquo", "").replaceAll("&rsquo", "").replaceAll("&hellip", "").
				replaceAll("&acute", "").replaceAll("&epsilon", "").replaceAll("&amp", "").
				replaceAll("&mdash", "").replaceAll("�", "").replaceAll("๑", "").replaceAll("()", "");
		/*String regEx = "[`~!@#$%^&*()+=|{}':;',\\[\\].<>/?~！@#￥%……&*（）——+|{}【】‘；：”“’。，、？]";
		Pattern p = Pattern.compile(regEx);
		Matcher m = p.matcher(str);
		return m.replaceAll("").trim();*/
		return str;
	}
	
	/**
	 * 判断是否是表情字符
	 * 
	 * @param codePoint
	 * @return
	 */
	private static boolean isEmojiCharacter(char codePoint) {
		return (codePoint == 0x0) || (codePoint == 0x9) || (codePoint == 0xA) || (codePoint == 0xD) || ((codePoint >= 0x20) && (codePoint <= 0xD7FF))
				|| ((codePoint >= 0xE000) && (codePoint <= 0xFFFD)) || ((codePoint >= 0x10000) && (codePoint <= 0x10FFFF));
	}
	
	/**
	 * 过滤&nbsp;&nbsp 
	 * 
	 * @param source
	 * @return
	 */
	public static String replaceNbsp(String source) {
		if (StrUtil.isNull(source)) {
			return source;
		}
		return source.replaceAll("&nbsp;&nbsp", ";");
	}
	
	/**
	 * 根据url，获取callback
	 * 
	 * @param url
	 * @return
	 */
	public static String getCallbackNameByUrl(String url) {
		String callback_name = "";
		if (StrUtil.isNull(url)) {
			return callback_name;
		}
		callback_name = url.substring(url.indexOf("&callback=") + 10); // jsonp的返回函数
		if (callback_name.indexOf("&") > -1) {
			callback_name = callback_name.substring(0, callback_name.indexOf("&"));
		}
		return callback_name;
	}
	
	/**
	 * json转文件
	 * 
	 * @param datajson json内容
	 * @param path 生成本地文件路径
	 * @param delFile 生成的本地文件是否删除
	 * @param jsonp 生成的文件是否是jsonp格式
	 */
	public static boolean stringToJsonFile(String datajson, String path, boolean delFile, boolean jsonp) {
		boolean url = false;
		try {
			File file = new File(path);
			if (!file.getParentFile().exists()) {
				file.getParentFile().mkdirs();
			}
			BufferedWriter bw = null;
			try {
				bw = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(file), "utf-8"));
				if (jsonp) {
					bw.write("callback(");
					bw.write(datajson);
					bw.write(");");
				} else {
					bw.write(datajson);
				}
				url = true;
			} catch (Exception e) {
				log.error(e.getMessage(), e);
			} finally {
				bw.close();
			}
			if (delFile) {
				if (file.exists()) {
					file.delete();
				}
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		return url;
	}
	
	/** 
	 * 获得指定文件的byte数组 
	 */
	public static byte[] getBytes(String filePath) {
		byte[] buffer = null;
		try {
			File file = new File(filePath);
			FileInputStream fis = new FileInputStream(file);
			ByteArrayOutputStream bos = new ByteArrayOutputStream(1000);
			byte[] b = new byte[1000];
			int n;
			while ((n = fis.read(b)) != -1) {
				bos.write(b, 0, n);
			}
			fis.close();
			bos.close();
			buffer = bos.toByteArray();
		} catch (FileNotFoundException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		}
		return buffer;
	}
	
	// main方法
	public static void main(String[] args) {
		System.out.println(filterEmoji("喜欢&lsquo;棒棒的&rsquo;质量很好"));
		System.out.println(filterEmoji("质量好(✪▽✪)值得购买哦"));
		System.out.println(filterEmoji("好！棒！不错！Nice！Good！没词了&hellip;&hellip;"));
	}
}
