package com.abujlb.zdcj8.util;

import java.io.BufferedReader;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.Reader;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;

import com.abujlb.util.RegexPattern;
import com.abujlb.util.ResourceLoader;

/**
 * json文件转json
 * 
 * <AUTHOR>
 * @date 2018-12-27 09:45:35
 */
public class FileToJson {

	/**
	 * 读取oss文件转string
	 * 
	 * @param path
	 * @return str
	 */
	public static String ossToJson(String host, String path) {
		// String host = Config.getString("osshost2");
		ByteArrayOutputStream bos = null;
		InputStream is = null;
		URL url = null;
		try {
			bos = new ByteArrayOutputStream();
			url = new URL(host + path);

			HttpURLConnection conn = (HttpURLConnection) url.openConnection();
			int state = conn.getResponseCode();
			if (state != 200) { // 文件不存在
				return null;
			}

			is = url.openStream();
			byte[] b = new byte[4096];
			int len = 0;
			while ((len = is.read(b)) != -1) {
				bos.write(b, 0, len);
			}
			bos.flush();
			String s = bos.toString("utf-8");
			String json = RegexPattern.patternFind("(\\[.*\\])", s);
			return json;
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			try {
				if (is != null) {
					is.close();
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
			try {
				bos.close();
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		return null;
	}

	/**
	 * 文件转json
	 * 
	 * @param file
	 * @return json
	 */
	public static String toJson(File file) {
		if (!file.exists()) {
			return null;
		}
		String s = readFile(file);
		String json = RegexPattern.patternFind("(\\[.*\\])", s);
		return json;
	}

	/**
	 * 读取文件转string
	 * 
	 * @param file
	 * @return str
	 */
	public static String readFile(File f) {
		BufferedReader br = null;
		try {
			br = new BufferedReader(new InputStreamReader(new FileInputStream(f), "utf-8"));
			StringBuilder sb = new StringBuilder();
			String line = null;
			while ((line = br.readLine()) != null) {
				sb.append(line);
			}
			return sb.toString();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			try {
				br.close();
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		return null;
	}

	/**
	 * 根据文件路径转json
	 * 
	 * @param path
	 * @return json
	 */
	public static String toJson(String path) {
		return toJson(new File(path));
	}

	/**
	 * 获取远程的json文件数据
	 * 
	 * @param url
	 * @return json
	 */
	public static String loadJson(String url) {
		String json = "";
		try {
			URL urls = new URL(url);
			HttpURLConnection conn = (HttpURLConnection) urls.openConnection();
			conn.setRequestProperty("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
			conn.setDoOutput(true);
			conn.setDoInput(true);
			conn.setRequestMethod("GET");
			conn.setRequestProperty("contentType", "UTF-8");

			int state = conn.getResponseCode();
			if (state != 200) { // 文件不存在
				return null;
			}

			InputStream inputStream = conn.getInputStream();
			BufferedReader bf = new BufferedReader(new InputStreamReader(inputStream, "UTF-8"));
			String line = null;
			while ((line = bf.readLine()) != null) {// 一行一行的读
				json = json + line;
			}
			if (inputStream != null) {
				inputStream.close();
			}
			String[] strs = json.split("\\\\");
			String str = "";
			StringBuffer jsons = new StringBuffer("");
			for (int i = 0; i < strs.length; i++) {
				str = strs[i];
				jsons = jsons.append(str);
			}
			jsons.replace(0, 9, "");
			jsons.replace(jsons.length() - 2, jsons.length(), "");
			return jsons.toString();
		} catch (MalformedURLException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		}
		return json.toString();
	}
	
	/**
	 * 读取文件转string
	 * 
	 * @param path 包路径，例如：/com/abujlb/zdcjsjs/http/md5_sjs.js
	 * @return str
	 */
	public static String readFile(String path) {
		Reader r = null;
		try {
			StringBuilder sb = new StringBuilder();
			
			InputStream in = ResourceLoader.getResourceAsStream(path);
			r = new InputStreamReader(in, "utf-8");
			int length = 0;
            for (char[] c = new char[1024]; (length = r.read(c)) != -1;) {
                sb.append(c, 0, length);
            }
			return sb.toString();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			try {
				if (r != null) {
					r.close();
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		return null;
	}
	
	/**
	 * 读取oss文件转string
	 * 
	 * @param path
	 * @return str
	 */
	public static String ossToData(String host, String path) {
		// String host = Config.getString("osshost2");
		ByteArrayOutputStream bos = null;
		InputStream is = null;
		URL url = null;
		try {
			bos = new ByteArrayOutputStream();
			url = new URL(host + path);
			
			HttpURLConnection conn = (HttpURLConnection) url.openConnection();
			int state = conn.getResponseCode();
			if (state != 200) { // 文件不存在
				return null;
			}
			
			is = url.openStream();
			byte[] b = new byte[4096];
			int len = 0;
			while ((len = is.read(b)) != -1) {
				bos.write(b, 0, len);
			}
			bos.flush();
			String s = bos.toString("utf-8");
			// String json = RegexPattern.patternFind("(\\[.*\\])", s);
			return s;
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			try {
				if (is != null) {
					is.close();
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
			try {
				bos.close();
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		return null;
	}
	
	/**
	 * 获取远程的json文件数据
	 * 
	 * @param url
	 * @return json
	 */
	public static String ossToData2(String url) {
		String json = "";
		try {
			URL urls = new URL(url);
			HttpURLConnection conn = (HttpURLConnection) urls.openConnection();
			conn.setRequestProperty("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
			conn.setDoOutput(true);
			conn.setDoInput(true);
			conn.setRequestMethod("GET");
			conn.setRequestProperty("contentType", "UTF-8");

			int state = conn.getResponseCode();
			if (state != 200) { // 文件不存在
				return null;
			}

			InputStream inputStream = conn.getInputStream();
			BufferedReader bf = new BufferedReader(new InputStreamReader(inputStream, "UTF-8"));
			String line = null;
			while ((line = bf.readLine()) != null) {// 一行一行的读
				json = json + line;
			}
			if (inputStream != null) {
				inputStream.close();
			}
			return json;
		} catch (MalformedURLException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		}
		return json.toString();
	}

	// main方法
	public static void main(String[] args) {
		// File f = new File("E:\\rsc\\rscdata\\rsc\\11\\110201\\month\\201702.json");
		// System.out.println(FileToJson.toJson(f));

		// System.out.println(FileToJson.ossToJson("abujlbjson/rsc/122650005/121410006/day/20181224.json")); // 失败

		/*
		 * String curl = "https://json.abujlb.com/rsc/122650005/121410006/day/20181224.json"; // String curl =
		 * "http://abujlbjson.oss-cn-shanghai.aliyuncs.com/abujlbjson/rsc/122650005/121410006/day/20181224.json"; // String curl =
		 * "https://abujlb.cn-shanghai.ots.aliyuncs.com/abujlbjson/rsc/122650005/121410006/day/20181224.json"; // curl = curl.replace("https", "http"); String curl2 = CdnAuth.getAuth(curl);
		 * System.out.println(curl2); System.out.println(FileToJson.loadJson(curl2));
		 */

		/*
		 * // String curl = "https://json.abujlb.com/rsc/122650005/121410006/day/20181224.json"; // String curl =
		 * "https://abujlb.cn-shanghai.ots.aliyuncs.com/abujlbjson/rsc/122650005/121410006/day/20181224.json"; String curl =
		 * "http://abujlbjson.oss-cn-shanghai.aliyuncs.com/rsc/122650005/50012343/day/20170902.json"; // String curl2 = CdnAuth.getAuth(curl); // System.out.println(curl2); File f = new File(curl);
		 * System.out.println(FileToJson.toJson(f));
		 */

		String curl = "http://abujlbjson.oss-cn-shanghai.aliyuncs.com/rsc/122650005/50012343/day/20170902.json";
		System.out.println(FileToJson.loadJson(curl)); // 读取的json文件开头不带“callback(”，结尾不带“);”
		// System.out.println(FileToJson.ossToJson("rsc/122650005/50012343/day/20170902.json")); // 读取的json文件开头不带“callback(”，结尾不带“);”，仅有key current的值

		System.out.println(FileToJson.loadJson("http://abujlbjson.oss-cn-shanghai.aliyuncs.com/rsc/122650005/50012343/day/20181230.json")); //
		// System.out.println(FileToJson.ossToJson("http://abujlbjson.oss-cn-shanghai.aliyuncs.com/rsc/122650005/50012343/day/20181230.json")); //
	}
	
	public static String toJson2(File file) {
		if (!file.exists()) {
			return "";
		}
		try {
			return readFile(file);
		} catch (Exception e) {
			return "";
		} finally {
			file.delete();
		}
	}
}
