package com.abujlb.zdcj8.controller;

import java.lang.reflect.Type;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.abujlb.Result;
import com.abujlb.ckstore.OnOff;
import com.abujlb.util.Md5;
import com.abujlb.zdcj8.bean.ztc.ZtcqsBean;
import com.abujlb.zdcj8.service.ZtcService;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

/**
 * 直通车接口Controller
 * 
 * <AUTHOR>
 * @date 2020-08-05 09:57:32
 */
@RestController
@RequestMapping("/")
public class ZtcController {

	@Autowired
	private ZtcService ztcService;
	@Autowired
	private OnOff onOff;

	/**
	 * 车图数据中心，行业点击率接口
	 * 
	 * @param data
	 *            关键词数组
	 * @param stamp
	 *            时间戳
	 * @param password
	 *            加密密码
	 */
	@RequestMapping("ztcct_hydjlv.action")
	public String hydjlv(String data, String stamp, String password) {
		if (!onOff.isOn("ztchydjl")) {
			return Result.RESULT_BUSY.toString();
		}
		if (Md5.password(data + stamp).equalsIgnoreCase(password)) {
			Gson gson = new Gson();
			Type type = new TypeToken<String[]>() {
			}.getType();
			// data = URLDecoder.decode(data, "utf-8");
			String[] arr = gson.fromJson(data, type);
			return ztcService.getHydjl(arr).toString();
		} else {
			return "{\"code\":1,\"content\":\"password is wrong!\"}";
		}
	}

	/**
	 * ztc车图数据中心，关键词趋势分析
	 * 
	 * @param data 关键词，开始日期、结束日期
	 * @param stamp 时间戳
	 * @param password 加密密码
	 */
	@RequestMapping("ztcct_gjcqs.action")
	public String gjcqs(String data, String stamp, String password) {
		if (!onOff.isOn("ztcqs")) {
			return Result.RESULT_BUSY.toString();
		}
		if (Md5.password(data + stamp).equalsIgnoreCase(password)) {
			Gson gson = new Gson();
			ZtcqsBean ztcqsBean = gson.fromJson(data, ZtcqsBean.class);
			return ztcService.gjcqs(ztcqsBean).toString();
		} else {
			return "{\"code\":1,\"content\":\"password is wrong!\"}";
		}
	}

}
