package com.abujlb.zdcj8.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.abujlb.Result;
import com.abujlb.ckstore.OnOff;
import com.abujlb.util.Md5;
import com.abujlb.zdcj8.service.GetRscService;

import common.Logger;

@RestController
@RequestMapping("/")
public class GetRscController {
	private static final Logger LOG = Logger.getLogger(GetRscController.class);
	@Autowired
	private GetRscService getRscService;
	@Autowired
	private OnOff onOff;

	@RequestMapping("getRsc_forcateid.action")
	public String forcateid(String cateid, String topid, String stamp, String password) {
		if (!onOff.isOn("rsc_cate")) {
			return Result.RESULT_BUSY.toString();
		}
		Result result = new Result();
		if (Md5.password(cateid + topid + stamp).equalsIgnoreCase(password)) {
			// 调用getRscService获取数据
			return getRscService.cj(topid, cateid);
		} else {
			result.setCodeContent(1, "password is wrong!");
			return result.toString();
		}
	}

	@RequestMapping("getRsc_forCookies.action")
	public String forCookies(String cateid, String topid, String cookies, String stamp, String password) {
		if (!onOff.isOn("rsc_cookie")) {
			return Result.RESULT_BUSY.toString();
		}
		Result result = new Result();
		if (Md5.password(cateid + topid + cookies + stamp).equalsIgnoreCase(password)) {
			String json = getRscService.cj(topid, cateid, cookies);
			LOG.info("用户扫码获取行业数据：" + json);
			return json;
		} else {
			result.setCodeContent(1, "password is wrong!");
			return result.toString();
		}
	}

	@RequestMapping("getRsc_forzhuzh.action")
	public String forzhuzh(String zhuzh, String stamp, String password) {
		Result result = new Result();
		if (Md5.password(zhuzh + stamp).equalsIgnoreCase(password)) {
			// 调用getRscService获取数据
		} else {
			result.setCodeContent(1, "password is wrong!");
		}
		return result.toString();
	}
}
