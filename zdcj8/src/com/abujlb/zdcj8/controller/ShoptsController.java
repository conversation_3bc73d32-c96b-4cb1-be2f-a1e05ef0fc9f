package com.abujlb.zdcj8.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.abujlb.Result;
import com.abujlb.ckstore.OnOff;
import com.abujlb.util.Md5;
import com.abujlb.zdcj8.bean.ShoptsPara;
import com.abujlb.zdcj8.service.ShoptsService;
import com.google.gson.Gson;

@RestController
@RequestMapping("/")
public class ShoptsController {
	@Autowired
	private ShoptsService shoptsService;
	@Autowired
	private OnOff onOff;

	@RequestMapping("shopts_ts.action")
	public String ts(String data, String stamp, String password) {
		if (!onOff.isOn("shopts")) {
			return Result.RESULT_BUSY.toString();
		}
		if (Md5.password(data + stamp).equalsIgnoreCase(password)) {
			Gson gson = new Gson();
			ShoptsPara sp = gson.fromJson(data, ShoptsPara.class);
			return shoptsService.ts(sp);
		} else {
			return "{\"code\":1,\"content\":\"password is wrong!\"}";
		}
	}
}
