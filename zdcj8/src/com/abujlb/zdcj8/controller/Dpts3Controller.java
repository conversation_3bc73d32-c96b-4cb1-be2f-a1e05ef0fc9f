package com.abujlb.zdcj8.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.abujlb.Result;
import com.abujlb.ckstore.OnOff;
import com.abujlb.zdcj8.bean.Dpts3Para;
import com.abujlb.zdcj8.service.Dpts3Service;

@RestController
@RequestMapping("/")
public class Dpts3Controller {
	@Autowired
	private Dpts3Service service;
	@Autowired
	private OnOff onOff;

	@RequestMapping("dpts3_lljg")
	public String lljg(Dpts3Para para) {
		if (!onOff.isOn("lljg")) {
			return Result.RESULT_BUSY.toString();
		}
		if (para.check()) {
			return service.lljg(para).toString();
		} else {
			return "{\"code\":1,\"content\":\"password is wrong!\"}";
		}
	}

	@RequestMapping("dpts3_ssgjc")
	public String ssgjc(Dpts3Para para) {
		if (!onOff.isOn("ssgjc")) {
			return Result.RESULT_BUSY.toString();
		}
		if (para.check()) {
			return service.ssgjc(para).toString();
		} else {
			return "{\"code\":1,\"content\":\"password is wrong!\"}";
		}
	}

	@RequestMapping("dpts3_ztcgjc")
	public String ztcgjc(Dpts3Para para) {
		if (!onOff.isOn("ztcgjc")) {
			return Result.RESULT_BUSY.toString();
		}
		if (para.check()) {
			return service.ztcgjc(para).toString();
		} else {
			return "{\"code\":1,\"content\":\"password is wrong!\"}";
		}
	}
}
