package com.abujlb.zdcj8.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.abujlb.util.Md5;
import com.abujlb.zdcj8.bean.DpsxPara;
import com.abujlb.zdcj8.service.FreeService;
import com.google.gson.Gson;

/**
 * 达官电商，免费工具接口Controller
 * 
 * <AUTHOR>
 * @date 2020-11-05 09:47:07
 */
@RestController
@RequestMapping("/")
public class FreeController {
	
	@Autowired
	private FreeService freeService;

	/**
	 * 达官电商 -> 免费工具 -> 店铺上新
	 * 
	 * @param data 店铺的userid
	 * @param stamp 时间戳
	 * @param password 加密密码
	 */
	@RequestMapping("free_dpsx.action")
	public String dpsx(String data, String stamp, String password) {
		if (Md5.password(data + stamp).equalsIgnoreCase(password)) {
			Gson gson = new Gson();
			DpsxPara dpsxPara = gson.fromJson(data, DpsxPara.class);
			return freeService.dpsx(dpsxPara).toString();
		} else {
			return "{\"code\":1,\"content\":\"password is wrong!\"}";
		}
	}
	
	/**
	 * 达官电商 -> 免费工具 -> （数据蛇）店铺上新2
	 * 
	 * @param data 店铺旺旺号
	 * @param stamp 时间戳
	 * @param password 加密密码
	 */
	@RequestMapping("free_dpsx2.action")
	public String dpsx2(String data, String stamp, String password) {
		if (Md5.password(data + stamp).equalsIgnoreCase(password)) {
			return freeService.dpsx2(data).toString();
		} else {
			return "{\"code\":1,\"content\":\"password is wrong!\"}";
		}
	}
	
	/**
	 * 达官电商 -> 免费工具 -> （数据蛇）旺旺查询
	 * 
	 * @param data 旺旺名称
	 * @param stamp 时间戳
	 * @param password 加密密码
	 */
	@RequestMapping("free_wwcx.action")
	public String wwcx(String data, String stamp, String password) {
		if (Md5.password(data + stamp).equalsIgnoreCase(password)) {
			return freeService.wwcx(data).toString();
		} else {
			return "{\"code\":1,\"content\":\"password is wrong!\"}";
		}
	}
	
	/**
	 * 达官电商 -> 免费工具 -> （数据蛇）旺旺查询 -> 查询是否开通88会员
	 * 
	 * @param data 旺旺名称
	 * @param stamp 时间戳
	 * @param password 加密密码
	 */
	@RequestMapping("free_wwvip.action")
	public String wwvip(String data, String stamp, String password) {
		if (Md5.password(data + stamp).equalsIgnoreCase(password)) {
			return freeService.wwvip(data).toString();
		} else {
			return "{\"code\":1,\"content\":\"password is wrong!\"}";
		}
	}
	
	/**
	 * 达官电商 -> 免费工具 -> （数据蛇）淘客订单查询
	 * 
	 * @param data 订单号s
	 * @param stamp 时间戳
	 * @param password 加密密码
	 */
	@RequestMapping("free_tkdd.action")
	public String tkdd(String data, String stamp, String password) {
		if (Md5.password(data + stamp).equalsIgnoreCase(password)) {
			return freeService.tkdd(data).toString();
		} else {
			return "{\"code\":1,\"content\":\"password is wrong!\"}";
		}
	}
	
	/**
	 * 达官电商 -> 免费工具 -> （数据蛇）无线详情
	 * @param data 宝贝id
	 * @param stamp 时间戳
	 * @param password 加密密码
	 */
	@RequestMapping("free_wxxq.action")
	public String wxxq(String data, String stamp, String password) {
		if (Md5.password(data + stamp).equalsIgnoreCase(password)) {
			return freeService.wxxq(data).toString();
		} else {
			return "{\"code\":1,\"content\":\"password is wrong!\"}";
		}
	}
	
	/**
	 * 达官电商 -> 免费工具 -> （数据蛇）竞品规格销售比例
	 * @param data 宝贝id
	 * @param stamp 时间戳
	 * @param password 加密密码
	 */
	@RequestMapping("free_jpggxsbl.action")
	public String jpggxsbl(String data, String stamp, String password) {
		if (Md5.password(data + stamp).equalsIgnoreCase(password)) {
			return freeService.jpggxsbl(data).toString();
		} else {
			return "{\"code\":1,\"content\":\"password is wrong!\"}";
		}
	}
	
	/**
	 * 达官电商 -> 免费工具 -> （数据蛇）竞品规格销售比例->详情
	 * @param data 宝贝id
	 * @param stamp 时间戳
	 * @param password 加密密码
	 */
	@RequestMapping("free_jpggxsblxq.action")
	public String jpggxsblxq(String data, String stamp, String password) {
		if (Md5.password(data + stamp).equalsIgnoreCase(password)) {
			return freeService.jpggxsblxq(data).toString();
		} else {
			return "{\"code\":1,\"content\":\"password is wrong!\"}";
		}
	}
	
	/**
	 * 达官电商 -> 免费工具 -> （数据蛇）买家秀印象
	 * @param data 宝贝id
	 * @param stamp 时间戳
	 * @param password 加密密码
	 */
	@RequestMapping("free_mjxyx.action")
	public String mjxyz(String data, String stamp, String password) {
		if (Md5.password(data + stamp).equalsIgnoreCase(password)) {
			return freeService.mjxyx(data).toString();
		} else {
			return "{\"code\":1,\"content\":\"password is wrong!\"}";
		}
	}
	
	/**
	 * 达官电商 -> 免费工具 -> （数据蛇）买家秀下载->确定下载 
	 * @param data 宝贝id
	 * @param stamp 时间戳
	 * @param password 加密密码
	 */
	@RequestMapping("free_mjx_qdxz.action")
	public String mjx_qdxz(String data, String stamp, String password) {
		if (Md5.password(data + stamp).equalsIgnoreCase(password)) {
			return freeService.mjx_qdxz(data).toString();
		} else {
			return "{\"code\":1,\"content\":\"password is wrong!\"}";
		}
	}
	
	/**
	 * 达官电商 -> 免费工具 -> （数据蛇）买家秀下载->点击查看
	 * @param data 宝贝id
	 * @param stamp 时间戳
	 * @param password 加密密码
	 */
	@RequestMapping("free_mjx_ck.action")
	public String mjx_ck(String data, String stamp, String password) {
		if (Md5.password(data + stamp).equalsIgnoreCase(password)) {
			return freeService.mjx(data).toString();
		} else {
			return "{\"code\":1,\"content\":\"password is wrong!\"}";
		}
	}
	
}