package com.abujlb.zdcj8.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.abujlb.Result;
import com.abujlb.ckstore.OnOff;
import com.abujlb.util.Md5;
import com.abujlb.zdcj8.service.GetScphService;

@RestController
@RequestMapping("/")
public class GetScphController {
	@Autowired
	private GetScphService getScphService;
	@Autowired
	private OnOff onOff;
	
	@RequestMapping("getScph_kscj.action")
	public String kscj(String cateid, String topid, String type, String stamp, String password,@RequestParam(required=false,value="actionCode",defaultValue="2")int actionCode) {
		if (!onOff.isOn("scph")) {
			return Result.RESULT_BUSY.toString();
		}
		Result result = new Result();
		if (Md5.password(cateid + topid + type + stamp).equalsIgnoreCase(password)) {
			// 调用getScphService获取数据
			return getScphService.cj(cateid, topid, type,actionCode);
		} else {
			result.setCodeContent(1, "password is wrong!");
			return result.toString();
		}
	}

	@RequestMapping("getScph_forzhuzh.action")
	public String forzhuzh(String zhuzh, String stamp, String password) {
		Result result = new Result();
		if (Md5.password(zhuzh + stamp).equalsIgnoreCase(password)) {
			// 调用getScphService获取数据
		} else {
			result.setCodeContent(1, "password is wrong!");
		}
		return result.toString();
	}
}
