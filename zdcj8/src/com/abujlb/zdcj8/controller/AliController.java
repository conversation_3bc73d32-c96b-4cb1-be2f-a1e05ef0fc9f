package com.abujlb.zdcj8.controller;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.abujlb.annotation.CheckSpider;
import com.abujlb.util.Md5;
import com.abujlb.zdcj8.bean.DpsxPara;
import com.abujlb.zdcj8.service.AliService;
import com.google.gson.Gson;

/**
 * 阿里Controller（已废弃：2023-07-19）
 * 
 * <AUTHOR>
 * @date 2021-03-19 15:03:07
 */
@RestController
@RequestMapping("/")
public class AliController {
	
	@Autowired
	private AliService service;
	
	/**
	 * 接口：店铺上新
	 * 
	 * @param data 查询条件
	 * @return json
	 */
	@RequestMapping("ali_dpsx.action")
	@CheckSpider
	public String dpsx(String data, String stamp, String password) {
		if (Md5.password(data + stamp).equalsIgnoreCase(password)) {
			Gson gson = new Gson();
			DpsxPara dpsxPara = gson.fromJson(data, DpsxPara.class);
			return service.dpsx(dpsxPara).toString();
		} else {
			return "{\"code\":1,\"content\":\"password is wrong!\"}";
		}
	}
	
	/**
	 * 接口：淘客订单查询（单个）
	 * 
	 * @param data 订单号
	 * @return json
	 */
	@RequestMapping("ali_tkdd.action")
	@CheckSpider
	public String tkdd(String data, String stamp, String password) {
		if (Md5.password(data + stamp).equalsIgnoreCase(password)) {
			return service.tkdd(data).toString();
		} else {
			return "{\"code\":1,\"content\":\"password is wrong!\"}";
		}
	}

}
