package com.abujlb.zdcj8.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.abujlb.Result;
import com.abujlb.ckstore.OnOff;
import com.abujlb.util.Md5;
import com.abujlb.zdcj8.bean.DptsPara;
import com.abujlb.zdcj8.service.Dpts2Service;
import com.abujlb.zdcj8.service.DptsSpuService;

@RestController
@RequestMapping("/")
public class Dpts2Controller {
	@Autowired
	private Dpts2Service dpts2Service;
	@Autowired
	private DptsSpuService dptsSpuService;
	@Autowired
	private OnOff onOff;

	@RequestMapping("dpts2_ts.action")
	public String ts(String bbid, String device, String stamp, String password) {
		if (!onOff.isOn("dpts")) {
			return Result.RESULT_BUSY.toString();
		}
		if (Md5.password(bbid + device + stamp).equalsIgnoreCase(password)) {
			DptsPara p = new DptsPara(bbid, device);
			return dpts2Service.ts(p);
		} else {
			return "{\"code\":1,\"content\":\"password is wrong!\"}";
		}
	}

	@RequestMapping("dpts2_tsspu.action")
	public String tsspu(String spuid, String device, String stamp, String password) {
		if (!onOff.isOn("sputs")) {
			return Result.RESULT_BUSY.toString();
		}
		if (Md5.password(spuid + device + stamp).equalsIgnoreCase(password)) {
			DptsPara p = new DptsPara(spuid, device);
			return dptsSpuService.ts(p);
		} else {
			return "{\"code\":1,\"content\":\"password is wrong!\"}";
		}
	}
}
