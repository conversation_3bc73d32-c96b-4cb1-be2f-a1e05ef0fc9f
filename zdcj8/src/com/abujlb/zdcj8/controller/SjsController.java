package com.abujlb.zdcj8.controller;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.abujlb.util.Md5;
import com.abujlb.zdcj8.service.SjsService;

/**
 * 数据蛇Controller
 * 
 * <AUTHOR>
 * @date 2020-09-18 16:24:55
 */
@RestController
@RequestMapping("/")
public class SjsController {
	
	@Autowired
	private SjsService sjsService;

	/**
	 * 竞品sku销量：最近30天
	 * 
	 * @param data 商品id
	 * @param stamp 时间戳
	 * @param password 加密密码
	 */
	@RequestMapping("sjs_sku.action")
	public String sku(String data, String stamp, String password) {
		if (Md5.password(data + stamp).equalsIgnoreCase(password)) {
			return sjsService.sku(data).toString();
		} else {
			return "{\"code\":1,\"content\":\"password is wrong!\"}";
		}
	}
	
	/**
	 * 超级竞品透视：最近30天
	 * 
	 * @param data 商品id
	 * @param stamp 时间戳
	 * @param password 加密密码
	 */
	/*@RequestMapping("sjs_jpts.action")
	public String jpts(String data, String stamp, String password) {
		if (Md5.password(data + stamp).equalsIgnoreCase(password)) {
			return sjsService.jpts(data).toString();
		} else {
			return "{\"code\":1,\"content\":\"password is wrong!\"}";
		}
	}*/
	
	/**
	 * 竞品透视：最近30天，https://www.shujushe.com/Navigation?hkj=3
	 * 
	 * @param data 商品id
	 * @param stamp 时间戳
	 * @param password 加密密码
	 */
	@RequestMapping("sjs_jpts30.action")
	public String jpts30(String data, String stamp, String password) {
		if (Md5.password(data + stamp).equalsIgnoreCase(password)) {
			return sjsService.jpts30(data).toString();
		} else {
			return "{\"code\":1,\"content\":\"password is wrong!\"}";
		}
	}
	
	/**
	 * 生成淘口令
	 * 
	 * @param data 商品/搜索结果页url
	 * @param stamp 时间戳
	 * @param password 加密密码
	 */
	/*@RequestMapping("sjs_tkl.action")
	public String tkl(String data, String stamp, String password) {
		if (Md5.password(data + stamp).equalsIgnoreCase(password)) {
			return sjsService.tkl(data).toString();
		} else {
			return "{\"code\":1,\"content\":\"password is wrong!\"}";
		}
	}*/
	
	/**
	 * 搜索秒单
	 * 
	 * @param data 宝贝链接或者宝贝id + 关键词 +买家旺旺号
	 * @param stamp 时间戳
	 * @param password 加密密码
	 */
	/*@RequestMapping("sjs_ssmd.action")
	public String ssmd(String data, String stamp, String password) {
		if (Md5.password(data + stamp).equalsIgnoreCase(password)) {
			return sjsService.ssmd(data).toString();
		} else {
			return "{\"code\":1,\"content\":\"password is wrong!\"}";
		}
	}*/

	/**
	 * 卡流量渠道
	 * 
	 * @param data 宝贝链接或者宝贝id (其实为宝贝id)
	 * @param stamp 时间戳
	 * @param password 加密密码
	 */
	/*@RequestMapping("sjs_kllqd.action")
	public String kllqd(String data, String stamp, String password) {
		if (Md5.password(data + stamp).equalsIgnoreCase(password)) {
			return sjsService.kllqd(data).toString();
		} else {
			return "{\"code\":1,\"content\":\"password is wrong!\"}";
		}
	}*/
	
	
	/**
	 * 搜索卡首屏
	 * 
	 * @param data 宝贝链接或者宝贝id (其实为宝贝id) 、 要卡的关键词
	 * @param stamp 时间戳
	 * @param password 加密密码
	 */
	/*@RequestMapping("sjs_ssksp.action")
	public String ssksp(String data, String stamp, String password) {
		if (Md5.password(data + stamp).equalsIgnoreCase(password)) {
			return sjsService.ssksp(data).toString();
		} else {
			return "{\"code\":1,\"content\":\"password is wrong!\"}";
		}
	}*/
	
	
	/**
	 * 搜索打标卡首屏
	 * @param type 1霸道版 2至尊版 3属性版 4点击板
	 * @param data 根据type 对应不同的实体
	 * @param stamp 时间戳
	 * @param password 加密密码
	 */
	/*@RequestMapping("sjs_ssdbksp.action")
	public String ssdbksp(int type ,String data, String stamp, String password) {
		if (Md5.password(data + stamp).equalsIgnoreCase(password)) {
			return sjsService.ssdbksp(type,data).toString();
		} else {
			return "{\"code\":1,\"content\":\"password is wrong!\"}";
		}
	}*/
	
	/**
	 * wwdb旺旺打标：https://www.shujushe.com/Navigation/2
	 * 
	 * @param data json对象
	 * @param stamp 时间戳
	 * @param password 加密密码
	 */
	@RequestMapping("sjs_wwdb.action")
	public String wwdb(String data, String stamp, String password) {
		if (Md5.password(data + stamp).equalsIgnoreCase(password)) {
			return sjsService.wwdb(data).toString();
		} else {
			return "{\"code\":1,\"content\":\"password is wrong!\"}";
		}
	}
	
}
