package com.abujlb.zdcj8.consumer;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;

import javax.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.abujlb.Config;
import com.abujlb.Result;
import com.abujlb.service.ServiceClient;
import com.abujlb.zdcj8.consumer.pojo.Provider;
import com.google.gson.reflect.TypeToken;

@Component
@Lazy
public class RegClient {
	@Autowired
	private Config config;
	private ServiceClient client;

	@PostConstruct
	public void init() {
		client = new ServiceClient(config.getString("reg-appid"), config.getString("reg-appkey"),
				config.getString("reg-server"));
	}

	public boolean beat(Provider provider) {
		String json = client.exec("/regserver/reg", provider, 3000);
		return Result.isTrue(json);
	}

	public boolean down(Provider provider) {
		String json = client.exec("/regserver/down", provider, 3000);
		return Result.isTrue(json);
	}

	public List<Provider> list(String group) {
		String json = client.exec("/regserver/listProvider", group, 3000);
		Type type = new TypeToken<ArrayList<Provider>>() {
		}.getType();
		return Result.getValueFromJson(json, "list", type);
	}
}
