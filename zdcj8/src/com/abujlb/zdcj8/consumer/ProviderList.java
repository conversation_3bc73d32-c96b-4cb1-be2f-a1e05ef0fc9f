package com.abujlb.zdcj8.consumer;

import java.util.List;
import java.util.Random;

import javax.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.abujlb.Config;
import com.abujlb.zdcj8.consumer.pojo.Provider;

@Component
@Lazy
public class ProviderList implements Runnable {
	@Autowired
	private RegClient regClient;
	@Autowired
	private Config config;

	private volatile List<Provider> list;

	@PostConstruct
	public void init() {
		this.update();
		Thread t = new Thread(this);
		t.setName("ProviderList");
		t.start();
	}

	@Override
	public void run() {
		while (true) {
			try {
				Thread.sleep(5000L);
				this.update();
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
	}

	public void update() {
		List<Provider> list = regClient.list(config.getString("reg-group"));
		if (list != null) {
			this.list = list;
		}
	}

	public Provider nextProvider() {
		List<Provider> list = this.list;
		if (list == null || list.size() == 0) {
			return null;
		}
		Random random = new Random();
		int rd = random.nextInt(list.size());
		return list.get(rd);
	}
}
