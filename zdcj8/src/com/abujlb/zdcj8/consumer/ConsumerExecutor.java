package com.abujlb.zdcj8.consumer;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.abujlb.Config;
import com.abujlb.Result;
import com.abujlb.retry.DonotRetryException;
import com.abujlb.retry.Retry;
import com.abujlb.service.ServiceClient;
import com.abujlb.zdcj8.consumer.pojo.Provider;

@Component
@Lazy
public class ConsumerExecutor {
	@Autowired
	private ProviderList providerList;
	@Autowired
	private Config config;

	@Retry
	public String exec(String path, Object para) {
		Provider provider = providerList.nextProvider();
		if (provider == null) {
			throw new DonotRetryException(Result.RESULT_BUSY.toString());
		}
		String pathPrefix = "http://" + provider.getIp() + ":" + provider.getPort();
		ServiceClient client = new ServiceClient(config.getString("consumer-appid"),
				config.getString("consumer-appkey"), pathPrefix);
		return client.exec(path, para, 5000);
	}
}
