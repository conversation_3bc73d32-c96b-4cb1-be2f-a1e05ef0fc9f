package com.abujlb.zdcj8.consumer.pojo;

public class Provider {
	public static final long ALIVE_MILLES = 15000L;
	public static final String DEFAULT_GROUP = "default";

	private String group;
	private String ip;
	private int port;
	private long timestamp;

	/**
	 * 刷新最后活跃时间
	 */
	public void refresh() {
		this.timestamp = System.currentTimeMillis();
	}

	/**
	 * 根据ip+":"+port进行索引
	 * 
	 * @return
	 */
	public String getId() {
		return ip + ":" + port;
	}

	/**
	 * 是否是活着的
	 * 
	 * @return
	 */
	public boolean isAlive() {
		return System.currentTimeMillis() - timestamp < ALIVE_MILLES;
	}

	public String getIp() {
		return ip;
	}

	public int getPort() {
		return port;
	}

	public long getTimestamp() {
		return timestamp;
	}

	public void setIp(String ip) {
		this.ip = ip;
	}

	public void setPort(int port) {
		this.port = port;
	}

	public void setTimestamp(long timestamp) {
		this.timestamp = timestamp;
	}

	public String getGroup() {
		return group;
	}

	public void setGroup(String group) {
		this.group = group;
	}
}
