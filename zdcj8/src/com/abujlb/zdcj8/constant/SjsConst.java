package com.abujlb.zdcj8.constant;

/**
 * 常量
 * 
 * <AUTHOR>
 * @date 2020-11-10 10:13:10
 */
public interface SjsConst {
	
	/**
	 * 已失效
	 */
	public static final int SFSX_YSX = 1;
	/**
	 * 未失效
	 */
	public static final int SFSX_WSX = 0;
	
	public static final int CJZT_WKS = 0; // 采集状态：未开始
	public static final int CJZT_JXZ = 1; // 采集状态：进行中
	public static final int CJZT_YJS = 2; // 采集状态：已结束
	
	/**
	 * 是否欠费：0.未欠费、1.已欠费（积分不足3）
	 */
	public static final int SFQF_WQF = 0;
	public static final int SFQF_YQF = 1;
	
	/**
	 * 常量0
	 */
	public static final int ZERO = 0;
	
	/**
	 * @Retry重试注解，time参数
	 */
	public static final int RETRY_TIME = 100;
	
	/**
	 * 最小的毫秒数
	 */
	public static final long MIN_MILLISECOND = 1514736000000L;
	
	/**
	 * 尝试次数
	 */
	public static final int TRY_NUM = 3;
	public static final int NULL_TRY_NUM = 3;
	public static final int MAX_TRY_NUM = 5; // 接口尝试次数
	public static final int MAX_TRY_NUM2 = 20; // 接口尝试次数
	
	/**
	 * 数据蛇账号状态：1.可用、2.禁用、9.已失效
	 */
	public static final int ZHSJS_ZT_1 = 1;
	public static final int ZHSJS_ZT_2 = 2;
	public static final int ZHSJS_ZT_9 = 9;
	
	/************* 表格日期存储类型 *************/
	public static final String LX_1 = "1"; // 日期类型：1.最近1天、7.最近7天、14.最近14天、30.最近30天、day.日、week.周、month.月
	public static final String LX_7 = "7";
	public static final String LX_14 = "14";
	public static final String LX_30 = "30";
	public static final String LX_DAY = "day";
	public static final String LX_WEEK = "week";
	public static final String LX_MONTH = "month";
	
	/************* 月份 *************/
	public static final int MONTH_1 = 1;
	public static final int MONTH_2 = 2;
	public static final int MONTH_3 = 3;
	public static final int MONTH_4 = 4;
	public static final int MONTH_5 = 5;
	public static final int MONTH_6 = 6;
	public static final int MONTH_7 = 7;
	public static final int MONTH_8 = 8;
	public static final int MONTH_9 = 9;
	public static final int MONTH_10 = 10;
	public static final int MONTH_11 = 11;
	public static final int MONTH_12 = 12;
	
	/**
	 * 当天开始时间，每天8点钟开始采集
	 */
	public static final long TIME_START = 8 * 3600 * 1000L;
	/**
	 * 每天采集到晚上23点
	 */
	public static final long TIME_END = 23 * 3600 * 1000L;
	
	
	public static final String LOGIN_URL = "https://gwhd.shujushe.com/shujushe/userVue/login?u=%s&v=%s&validateCode=2&rememberMe=false"; // 登录
	/**
	 * getpersonInfo: function() 函数参数：{userId: this.taguId,token: this.token,tm: this.tm,shopType: this.shopType,shopCategory: this.shopCategory,key: this.keymd}
	 * 其中：this.tm = Date.parse(new Date);var t = new String(this.tm) + new String(this.taguId); this.keymd = this.$md5(t),
	 */
	public static final String UCENTER_URL = "https://gwhd.shujushe.com/shujushe/userVue/ucenter?userId=%s&token=%s&tm=%s&key=%s&shopType=undefined&shopCategory=undefined"; // 获取用户信息接口
	
	/************* 日期类型 *************/
	public static final String RQLX_DAY = "day"; // 日期类型：day.按天、recent7.最近7天、recent30.最近30天
	public static final String RQLX_RECENT7 = "recent7";
	public static final String RQLX_RECENT30 = "recent30";
	
	/**
	 * 淘宝接口，返回宝贝详情信息数据，几乎所有数据蛇扣费接口都会用到淘宝接口返回的数据
	 * 
	 */
	public static final String TAOBAO_ITEM_URL = "https://trade-acs.m.taobao.com/gw/mtop.taobao.detail.getdetail/6.0/?data=itemNumId%22%3A%22"; // 淘宝接口，需要用到
//	public static final String TAOBAO_ITEM_URL2 = "https://h5api.m.taobao.com/h5/mtop.taobao.detail.getdetail/6.0/?ttid=2017%40taobao_h5_6.6.0&data=%s&callback=__jp1"; // 淘宝接口，需要用到
	public static final String TAOBAO_ITEM_URL2 = "https://h5api.m.taobao.com/h5/mtop.taobao.detail.getdetail/6.0/?ttid=2017%40taobao_h5_6.6.0&data="; // 淘宝接口，需要用到
	
	/**
	 * 数据蛇：竞品sku销量
	 * 
	 */
	public static final String SALESKU_URL = "https://gwhd.shujushe.com/shujushe/saleSku/save?userName=%s&datatpye=%s&nid=%s&device=2&tjrq=%s&tjrq2=%s&starttime=%s&endtime=%s"; // 数据生成链接，扣费链接endtime，tjrq2都是前一天的日期，tjrq是最近30天，starttime最近10天
	public static final String SALESKU_LIST_URL = "https://gwhd.shujushe.com/shujushe/saleSku/list?userName=%s&pageNumber=%s&pageSize=40"; // 查询列表，需要对应nid（宝贝id），获取导出的id，pageNumber从1开始
	public static final String SALESKU_EXPORT_URL = "https://gwhd.shujushe.com/shujushe/dpsjExport/exportSku/%s"; // 下载excel
	
	/**
	 * 数据蛇：超级竞品透视：super_jpts
	 * 
	 */
	public static final String SUPER_JPTS_URL = "https://gwhd.shujushe.com/shujushe/cjpHigh/save?userName=%s&datatpye=%s&nid=%s&device=2&tjrq=%s&tjrq2=%s&starttime=%s&endtime=%s"; // 数据生成链接，扣费链接endtime，tjrq2都是前一天的日期，tjrq是最近30天或最近7天，starttime最近30天
	public static final String SUPER_JPTS_LIST_URL = "https://gwhd.shujushe.com/shujushe/cjpHigh/list?pageSize=40&pageNumber=%s&createBy=%s"; // 查询列表，需要对应nid（宝贝id），获取导出的id，pageNumber从1开始（此链接暂用不到）
	public static final String SUPER_JPTS_LIST_DATA_URL = "https://gwhd.shujushe.com/shujushe/cjpHigh/listData?uuid=%s&device=2"; // uuid来源于扣费链接
	public static final String SUPER_JPTS_DATA_URL = "https://gwhd.shujushe.com/shujushe/cjpHigh/listByCjpRj?nid=%s"; // nid是宝贝id
	
	/**
	 * 数据蛇：竞品透视：jpts，https://www.shujushe.com/Navigation?hkj=3
	 * 
	 */
	public static final String JPTS_URL = "https://gwhd.shujushe.com/shujushe/dpsjVue/cjp1124?nid=%s&createBy=%s&type=1&datatpye=day&device=2&tjrq=%s&tjrq2=%s&starttime=%s&endtime=%s&token=%s&userId=%s&fromTo=www.shujushe.com&tm=%s&key=%s"; // 发起竞品透视请求，扣费链接
	public static final String JPTS_LSJL_URL = "https://gwhd.shujushe.com/shujushe/dpsjVue/dpsjList?createBy=%s&pageNumber=1&pageSize=%d&type=1&token=%s&userId=%s&fromTo=www.shujushe.com&tm=%s&key=%s"; // 历史记录（uuid）
	public static final String JPTS_DATA_URL = "https://gwhd.shujushe.com/shujushe/sycmNew/list?nid=%s&firstData=1&token=%s&userId=%s&fromTo=www.shujushe.com&tm=%s&key=%s"; // 获取最近30天竞品透视数据
	public static final String JPTS_LOADDATA_URL = "https://gwhd.shujushe.com/shujushe/dpsjVue/loadData?uuid=%s&createBy=%s&num=5&token=%s&userId=%s&fromTo=www.shujushe.com&tm=%s&key=%s"; // 确认历史数据是否存在
	public static final String JPTS_TM_URL = "https://gwhd.shujushe.com/shujushe/moreFun/getDate?token=%s&userId=%s&fromTo=www.shujushe.com&tm=%s&key=%s"; // 
	
	/**
	 * 数据蛇：旺旺照妖镜，wwzyj，https://www.shujushe.com/Navigation?hkj=34
	 * 
	 */
	// public static final String WWZYJ_URL = "https://gwhd.shujushe.com/shujushe/userVue/chhIsFree?createBy=%s"; // (免费)相对于扣费链接
	public static final String WWZYJ_URL = "https://gwhd.shujushe.com/shujushe/userVue/chhIsFree?createBy=%s&token=%s&userId=%s&tm=%s&key=%s"; // (免费)相对于扣费链接
	// public static final String WWZYJ_DATA_URL = "https://gwhd.shujushe.com/shujushe/moreFun/getDataByWW?userId=%s&wwname=%s"; // 
	public static final String WWZYJ_DATA_URL = "https://gwhd.shujushe.com/shujushe/moreFun/getDataByWW?token=%s&userId=%s&tm=%s&key=%s&wwname=%s"; // 
	public static final String WWVIP_URL = "https://gwhd.shujushe.com/shujushe/moreFun/getDataBy88Vip?nick=%s&createBy=%s&token=%s&userId=%s&tm=%s&key=%s"; // 查询是否开通88会员
	
	/**
	 * 数据蛇：批量淘客订单验证，tkdd，https://www.shujushe.com/Navigation?hkj=28
	 * 
	 */
	// public static final String TKDD_URL = "https://gwhd.shujushe.com/shujushe/tkddcxFree/save?createBy=%s&orderId=%s"; // (免费)相对于扣费链接
	public static final String TKDD_URL = "https://gwhd.shujushe.com/shujushe/tkddcxFree/save?createBy=%s&orderId=%s&token=%s&userId=%s&tm=%s&key=%s"; // (免费)相对于扣费链接
	// public static final String TKDD_DATA_URL = "https://gwhd.shujushe.com/shujushe/tkddcxFree/getDataByUuid?uuid=%s"; // 
	public static final String TKDD_DATA_URL = "https://gwhd.shujushe.com/shujushe/tkddcxFree/getDataByUuid?uuid=%s&token=%s&userId=%s&tm=%s&key=%s"; // 
	
	/**
	 * 数据蛇：店铺上新查询，dpsxcx，https://www.shujushe.com/Navigation?hkj=39
	 * 
	 */
	public static final String GETDATE_URL = "https://gwhd.shujushe.com/shujushe/moreFun/getDate"; // 获取时间戳
	public static final String DPSXCX_URL = "https://gwhd.shujushe.com/shujushe/newstaobao/save?nick=%s&userName=%s&token=%s&userId=%s&tm=%s&key=%s"; // (免费)相对于扣费链接
	// public static final String DPSXCX_DATA_URL = "https://gwhd.shujushe.com/shujushe/newstaobao/list?pageNumber=%s&pageSize=%s&userName=%s"; // 
	public static final String DPSXCX_DATA_URL = "https://gwhd.shujushe.com/shujushe/newstaobao/list?pageNumber=%s&pageSize=%s&userName=%s&token=%s&userId=%s&tm=%s&key=%s"; // 
	// public static final String DPSXCX_LIST_URL = "https://gwhd.shujushe.com/shujushe/newstaobao/listByUuid?uuid=%s"; // 
	public static final String DPSXCX_LIST_URL = "https://gwhd.shujushe.com/shujushe/newstaobao/listByUuid?uuid=%s&token=%s&userId=%s&tm=%s&key=%s"; // 
	
	/**
	 * 数据蛇：淘口令生成 tkl，https://www.shujushe.com/Navigation?hkj=8
	 */
	public static final String TKL_URL="https://gwhd.shujushe.com/shujushe/moreFun/tklNew";
	public static final String TKL_JL_URL=" https://gwhd.shujushe.com/shujushe/moreFun/getTkl?createBy=%s&pageNumber=%s&pageSize=%s";
	
	/**
	 * 数据蛇：搜索秒单 ssmd，https://www.shujushe.com/Navigation?hkj=10
	 */
	public static final String SSMD_URL="https://gwhd.shujushe.com/shujushe/ssZero/save?createBy=%s&title=%s&nick=%s&nid=%s";
	public static final String SSMD_JL_URL="https://gwhd.shujushe.com/shujushe/ssZero/list?createBy=%s&PageSize=%s&PageNumber=%s";
	
	/**
	 * 数据蛇：搜索卡首屏ssksp，http://shujushe.com/Navigation?hkj=43
	 */
	public static final String SSKSP_URL="https://gwhd.shujushe.com/shujushe/zerosaleTask/save?gjc=%s&nid=%s&createBy=%s";
	public static final String SSKSP_JL_URL="https://gwhd.shujushe.com/shujushe/zerosaleTask/list?pageSize=%s&pageNumber=%s&createBy=%s";
	
	/**
	 * 数据蛇：搜索打标卡首屏ssdbksp，http://shujushe.com/Navigation?hkj=1
	 */
	public static final int SSDBKSP_BD = 1 ;
	public static final int SSDBKSP_ZZ = 2 ;
	public static final int SSDBKSP_SX = 3 ;
	public static final int SSDBKSP_DJ = 4 ;
	
	public static final String SSDBKSP_BD_URL= "https://gwhd.shujushe.com/shujushe/wwdbksp/bdsave?title=%s&nid=%s&nick=%s&type=%s&userId=%s&date=";
	public static final String SSDBKSP_BDJL_URL= "https://gwhd.shujushe.com/shujushe/wwdbksp/bdlist?userId=%s&pageNumber=%s&pageSize=%s";

	/**
	 * 数据蛇：卡流量渠道kllqd，https://www.shujushe.com/Navigation?hkj=6
	 */
	public static final String KLLQD_URL ="https://gwhd.shujushe.com/shujushe/kllVue/singleSave?url=%s&userId=%s&klltype=%s&jpid=%s";
	public static final String KLLQD_JL_URL ="https://gwhd.shujushe.com/shujushe/kllVue/singleList?createname=%s&pageNumber=%s&pageSize=%s&userId=%s&token=%s&tm=%s&key=%s";
	
	/**
	 * 数据蛇：宝贝类目bblm，https://www.shujushe.com/Navigation?hkj=27
	 */
	public static final String BBLM_URL ="https://gwhd.shujushe.com/shujushe/leimu/save?nid=%s";
	
	/**
	 * 数据蛇：竞品规格销售比例，
	 */
	public static final String JPGGXSBL_URL="https://gwhd.shujushe.com/shujushe/jpggxsTask/save?createBy=%s&nid=%s&num=%s&pid=%s&token=%s&userId=%s&fromTo=www.shujushe.com&tm=%s&key=%s";
	public static final String JPGGXSBL_JL_URL="https://gwhd.shujushe.com/shujushe/jpggxsTask/list?pageSize=%s&pageNumber=%s&createBy=%s&token=%s&userId=%s&fromTo=www.shujushe.com&tm=%s&key=%s";
	public static final String JPGGXSBL_DATA_URL ="https://gwhd.shujushe.com/shujushe/jpggxsTask/loadData?uuid=%s&num=%s&nid=%s&createBy=%s&token=%s&userId=%s&fromTo=www.shujushe.com&tm=%s&key=%s";

	/**
	 * 数据蛇：买家秀
	 */
	public static final String MJX_YX_URL="https://gwhd.shujushe.com/shujushe/gjmjxVue/getExpression?nid=%s&token=%s&userId=%s&tm=%s&key=%s";
	public static final String MJX_URL ="https://gwhd.shujushe.com/shujushe/gjmjxVue/gjpjspxz?createBy=%s&url=%s&num=%s&sort=%s&type=%s&sku=%s&expre=%s&pid=%s&skuCn=%s&typeCn=%s&sortCn=%s&expreCn=%s&token=%s&userId=%s&tm=%s&key=%s";
	public static final String MJX_JL_URL ="https://gwhd.shujushe.com/shujushe/moreFun/videoList?userName=%s&pageNumber=%s&pageSize=%s&ptype=2&token=%s&userId=%s&tm=%s&key=%s";
	
	/**
	 * 数据蛇：旺旺打标：wwdb，https://www.shujushe.com/Navigation/2
	 * 
	 */
	public static final String WWDB_URL = "https://gwhd.shujushe.com/shujushe/wwdbksp/save?&title=%s&nid=%s&nick=%s&date=&type=1&token=%s&userId=%s&fromTo=www.shujushe.com&tm=%s&key=%s"; // 发起旺旺打标请求，扣费链接
}
