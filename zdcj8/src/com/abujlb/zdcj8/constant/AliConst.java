package com.abujlb.zdcj8.constant;

/**
 * 常量
 * 
 * <AUTHOR>
 * @date 2021-03-16 09:58:04
 */
public interface AliConst {
	
	public static final String COOKIE_KEY = "ali_ck_";
	
	// public static final long MININTERVAL = 240000L; // 最小时间间隔，单位：ms，4分钟
	public static final long MININTERVAL = 4*60*60*1000L; // 最小时间间隔，单位：ms，4小时
	// public static final long MININTERVALSTD = 60000L; // 最小时间间隔，单位：ms，4分钟
	public static final long MAXTIMES = 30L; // 每次登录的最大使用次数，30次
	// public static final long MAXTIMES_DPPH = 30; // 单账号店铺排行的每天最大使用次数，每天最多使用30次
	public static final long MAXLOGINMINUTES = 24 * 60L; // 一次登录最多维持24小时登录状态
	public static final long MAXMINUTES = 23 * 60L + 30L; // 每次登录的最长使用时间，单位：min，10小时
	public static final int MAXERROR = 3; // 最大连续失败次数，达到该连续失败次数即丢弃该会话
	
	public static final long OVERTIMES = 8L; // 8分钟
	
	public static final int MAP_SIZE = 1000; // 多数map的初始化size，建议定义为每天需要采集的店铺帐号数量总和左右
	public static final int CATE_SIZE = 10000; // 建议同一时间采集的类目总数
	
	/**
	 * 已失效
	 */
	public static final int SFSX_YSX = 1;
	/**
	 * 未失效
	 */
	public static final int SFSX_WSX = 0;
	
	public static final int CJZT_WKS = 0; // 采集状态：未开始
	public static final int CJZT_JXZ = 1; // 采集状态：进行中
	public static final int CJZT_YJS = 2; // 采集状态：已结束
	
	/**
	 * 是否欠费：0.未欠费、1.已欠费（积分不足3）
	 */
	public static final int SFQF_WQF = 0;
	public static final int SFQF_YQF = 1;
	
	/**
	 * 常量0
	 */
	public static final int ZERO = 0;
	
	/**
	 * @Retry重试注解，time参数
	 */
	public static final int RETRY_TIME = 100;
	
	/**
	 * 最小的毫秒数
	 */
	public static final long MIN_MILLISECOND = 1514736000000L;
	
	/**
	 * 尝试次数
	 */
	public static final int TRY_NUM = 3;
	
	/**
	 * 账号状态：1.可用、2.禁用、9.已失效
	 */
	public static final int ZH_ZT_1 = 1;
	public static final int ZH_ZT_2 = 2;
	public static final int ZH_ZT_9 = 9;
	
	/************* 表格日期存储类型 *************/
	public static final String LX_1 = "1"; // 日期类型：1.最近1天、7.最近7天、14.最近14天、30.最近30天、day.日、week.周、month.月
	public static final String LX_7 = "7";
	public static final String LX_14 = "14";
	public static final String LX_30 = "30";
	public static final String LX_DAY = "day";
	public static final String LX_WEEK = "week";
	public static final String LX_MONTH = "month";
	
	/************* 日期类型 *************/
	public static final String RQLX_DAY = "day"; // 日期类型：day.按天、recent7.最近7天、recent30.最近30天
	public static final String RQLX_RECENT7 = "recent7";
	public static final String RQLX_RECENT30 = "recent30";
	
	/************* 月份 *************/
	public static final int MONTH_1 = 1;
	public static final int MONTH_2 = 2;
	public static final int MONTH_3 = 3;
	public static final int MONTH_4 = 4;
	public static final int MONTH_5 = 5;
	public static final int MONTH_6 = 6;
	public static final int MONTH_7 = 7;
	public static final int MONTH_8 = 8;
	public static final int MONTH_9 = 9;
	public static final int MONTH_10 = 10;
	public static final int MONTH_11 = 11;
	public static final int MONTH_12 = 12;
	
	/**
	 * 当天开始时间，每天8点钟开始采集
	 */
	public static final long TIME_START = 8 * 3600 * 1000L;
	/**
	 * 每天采集到晚上23点
	 */
	public static final long TIME_END = 23 * 3600 * 1000L;
	
	/**
	 * 淘宝接口，返回宝贝详情信息数据，几乎所有数据蛇扣费接口都会用到淘宝接口返回的数据
	 * 
	 */
	public static final String TAOBAO_ITEM_URL = "https://trade-acs.m.taobao.com/gw/mtop.taobao.detail.getdetail/6.0/?data=itemNumId%22%3A%22"; // 淘宝接口，需要用到
	
	/**
	 * 阿里cookie中所有的domain
	 */
	public static final String CK_DOMAIN_PRE = ".";
	
	public static final String CK_DOMAIN_TAOBAO_PC = "taobao.com";
	public static final String CK_DOMAIN_TAOBAO_PHONE = ".m.taobao.com";
	public static final String CK_DOMAIN_TAOBAO = "taobao.com";
	public static final String CK_DOMAIN_POINT_TAOBAO = ".taobao.com";
	public static final String CK_DOMAIN_TMALL = "tmall.com";
	public static final String CK_DOMAIN_1688 = "1688.com";
	public static final String CK_DOMAIN_ALIMAMA = "alimama.com";
	
	/**
	 * 淘客订单标识：
	 */
	public static final String TKDD_TRUE = "ORDER_NOTBELONG"; // 淘客订单
	public static final String TKDD_FALSE = "ORDER_INVALID"; // 非淘客订单
	
}
