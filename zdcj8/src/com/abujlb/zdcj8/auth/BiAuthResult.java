package com.abujlb.zdcj8.auth;

import com.google.gson.Gson;

/**
 * <AUTHOR>
 * @date 2021-10-28
 */
public class BiAuthResult {

    public static BiAuthResult AUTH_SUCCESS = new BiAuthResult(BiAuthResult.SUCCESS, "");

    //无权限
    public static final int NO_AUTH = 0;
    //未登录
    public static final int NOT_LOGIN = -1;
    //未开通
    public static final int NOT_OPEN = -2;
    //其他
    public static final int OTHER = -3;
    //exception
    public static final int EXCEPTION = -99;
    //成功
    public static final int SUCCESS = 1;

    private int code;
    private String msg;
    //最终记录的时间
    private String sj;

    public BiAuthResult() {
    }

    public BiAuthResult(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public boolean authSuccess() {
        return this.code == SUCCESS;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getSj() {
        return sj;
    }

    public void setSj(String sj) {
        this.sj = sj;
    }

    @Override
    public String toString() {
        return new Gson().toJson(this);
    }

}
