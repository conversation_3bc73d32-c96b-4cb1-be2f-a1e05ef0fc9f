package com.abujlb.zdcj8.auth;


import com.abujlb.ckstore.Cjzh;
import com.abujlb.jyrb.log.CjLog;
import net.sf.json.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.cookie.Cookie;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.apache.log4j.Logger;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2022/5/9 14:56
 */
public class BiAuth {

    private static final Logger log = Logger.getLogger(BiAuth.class);

    /**
     * 万相台无界
     *
     * @param cjzh
     * @return
     */
    public static BiAuthResult adbrainOne(Cjzh cjzh) {
        CjLog.addTime("adbrainone_http.auth");

        // https://sycm.taobao.com/oneauth/api/permission.json?p_url=http%3A%2F%2Fsycm.taobao.com%2Fcc%2Fitem_rank&_=1636010721759&token=1ced73e20
        // 返回code不为0 则说明生意参谋异常
        CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cjzh.getCookieStore()).build();
        HttpPost httpGet = new HttpPost("https://one.alimama.com/member/checkAccess.json?bizCode=universalBP&r=mx_13");
        httpGet.setHeader("accept", "application/json, text/javascript, */*; q=0.01");
        httpGet.setHeader("accept-encoding", "gzip, deflate, br");
        httpGet.setHeader("accept-language", "zh-CN,zh;q=0.9");
        httpGet.setHeader("bx-v", "2.5.1");
        httpGet.setHeader("content-type", "application/json");
        httpGet.setHeader("origin", "https://one.alimama.com");
        httpGet.setHeader("referer", "https://one.alimama.com/index.html?mxredirectUrl=https%3A%2F%2Fone.alimama.com%2Fredirect.action%3FredirectURL%3Dhttps%253A%252F%252Fone.alimama.com%252Findex.html");
        httpGet.setHeader("sec-ch-ua", "\"Google Chrome\";v=\"95\", \"Chromium\";v=\"95\", \";Not A Brand\";v=\"99\"");
        httpGet.setHeader("sec-ch-ua-mobile", "?0");
        httpGet.setHeader("sec-ch-ua-platform", "\"Windows\"");
        httpGet.setHeader("sec-fetch-dest", "empty");
        httpGet.setHeader("sec-fetch-mode", "cors");
        httpGet.setHeader("sec-fetch-site", "same-origin");
        httpGet.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");
        httpGet.setHeader("x-requested-with", "XMLHttpRequest");
        RequestConfig.Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000).setConnectionRequestTimeout(20000);
        httpGet.setConfig(requestConfig.build());
        HttpResponse httpResponse;
        try {
            StringEntity param = new StringEntity("{\"bizCode\":\"universalBP\"}");
            httpGet.setEntity(param);

            httpResponse = httpClient.execute(httpGet);
            HttpEntity entity = httpResponse.getEntity();
            String body = EntityUtils.toString(entity, "utf-8");
            JSONObject jsonObject = JSONObject.fromObject(body);

            if (jsonObject.containsKey("data")) {

                JSONObject data = jsonObject.getJSONObject("data");

                if (data.containsKey("accessPassed")) {
                    boolean accessPassed = data.getBoolean("accessPassed");
                    if (!accessPassed) {
                        return new BiAuthResult(BiAuthResult.NO_AUTH, body);
                    }
                }
                if (data.containsKey("errorInfo")) {
                    JSONObject errorInfo = data.getJSONObject("errorInfo");
                    if (errorInfo.getString("code").equals("USER_UNAUTHORIZED")) {
                        //无权限
                        return new BiAuthResult(BiAuthResult.NO_AUTH, body);
                    }
                    return new BiAuthResult(BiAuthResult.OTHER, body);
                }
                if (data.containsKey("accessInfo")) {
                    JSONObject accessInfo = data.getJSONObject("accessInfo");
                    if (StringUtils.isNotBlank(accessInfo.getString("csrfId"))) {
                        cjzh.putData("adbrain_one_csrfId", accessInfo.getString("csrfId"));
                        return new BiAuthResult(BiAuthResult.SUCCESS, "");
                    }
                }
            }

            return new BiAuthResult(BiAuthResult.OTHER, body);
        } catch (Exception e) {
            return new BiAuthResult(BiAuthResult.EXCEPTION, e.getMessage());
        } finally {
            httpGet.releaseConnection();
        }
    }


    public static String getH5Token(Cjzh cjzh) {
        List<Cookie> cookies = cjzh.getCookieStore().getCookies();
        if (cookies == null) {
            return "";
        }

        Map<Integer, String> map = new HashMap<>();
        for (Cookie cookie : cookies) {
            if (cookie.getName().equals("_m_h5_tk") && cookie.getDomain().contains("taobao.com")) {
                map.put(cookie.getVersion(), cookie.getValue().split("_")[0]);
            }
        }

        if (map.isEmpty()) {
            return "";
        }

        List<Integer> keys = new ArrayList<>(map.keySet());
        Collections.sort(keys);

        return map.get(keys.get(0));
    }
}
