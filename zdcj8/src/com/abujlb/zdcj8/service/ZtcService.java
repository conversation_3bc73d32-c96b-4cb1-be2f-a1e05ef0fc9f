package com.abujlb.zdcj8.service;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.abujlb.Result;
import com.abujlb.ckstore.AbujlbCookieStore;
import com.abujlb.ckstore.Cjzh;
import com.abujlb.util.DateUtil;
import com.abujlb.util.StringUtil;
import com.abujlb.zdcj8.bean.ZtcctHydjl;
import com.abujlb.zdcj8.bean.ZtcctTrends;
import com.abujlb.zdcj8.bean.ztc.ZtcctTrendsBean;
import com.abujlb.zdcj8.bean.ztc.ZtcqsBean;
import com.abujlb.zdcj8.http.ZtcHttpClient;
import com.abujlb.zdcj8.script.ZtcHashJSEngine;

/**
 * 直通车service
 * 
 * <AUTHOR>
 * @date 2020-08-05 14:40:02
 */
@Component("ztcService")
public class ZtcService {
	
	private static int LAST_DAYS = 7; // 获取过去n天的数据
	
	@Autowired
	private AbujlbCookieStore abujlbCookieStore;
	@Autowired
	private ZtcHttpClient ztcHttpClient;
	@Autowired
	private ZtcHashJSEngine ztcHashJSEngine;

	/**
	 * 获取：直通车车图行业点击率
	 * 
	 * @param gjcArr 关键词数据
	 * @return result结果
	 */
	public Result getHydjl(String[] gjcArr) {
		Result result = new Result(101, "参数错误！");
		if (gjcArr == null || gjcArr.length < 1) {
			return result;
		}
		for (int i = 0; i < gjcArr.length ;i++) {
			if (StringUtil.isNull2(gjcArr[i])) {
				return result;
			}
		}
		Cjzh cjzh = abujlbCookieStore.nextCjzhZtc();
		if (cjzh == null) {
			result.setCodeContent(101, "暂无可用的直通车账号！");
			return result;
		}
		Result resultTemp = ztcHttpClient.getToken(cjzh, 3);
		if (resultTemp == null || !resultTemp.isSuccess()) {
			result.setCodeContent(101, "直通车账号获取用户信息失败！");
			return result;
		}
		String ztcUser = (String) resultTemp.getKey("ztcUser");
		String token = (String) resultTemp.getKey("token");
		if (StringUtil.isNull2(token)) {
			result.setCodeContent(101, "直通车账号获取token失败！");
			return result;
		}
		// 获取webOpSessionId
		String webOpSessionId = ztcHashJSEngine.webOpSessionId();
		
		String startDate = DateUtil.formatDate(DateUtil.getDateBefore(DateUtil.getLastDay(), LAST_DAYS - 1)); // 开始日期：2020-07-31（7天前）
		String endDate = DateUtil.formatDate(DateUtil.getLastDay()); // 结束日期：2020-08-06（昨天）
		List<ZtcctHydjl> list = new ArrayList<ZtcctHydjl>();
		for (int i = 0; i < gjcArr.length ;i++) {
			ZtcctHydjl ztcctHydjl = new ZtcctHydjl();
			List<ZtcctHydjl> cateList = ztcHttpClient.getCategory(cjzh, token, ztcUser, gjcArr[i], webOpSessionId, 1);
			if (cateList == null || cateList.size() < 1 || StringUtil.isNull2(cateList.get(0).getCateId())) {
				
			} else {
				List<ZtcctTrendsBean> qsList = ztcHttpClient.getTrends(cjzh, token, ztcUser, gjcArr[i], cateList.get(0).getCateId(), startDate, endDate, webOpSessionId, 1);
				double djl = 0D; // 行业点击率
				double sum1 = 0D; // 点击量sum
				double sum2 = 0D; // 展现量sum
				for (int j = 0; qsList != null && j < qsList.size() ; j++) {
					ZtcctTrends ztcctTrends = new ZtcctTrends(qsList.get(j));
					ztcctTrends.initData();
					// sum1 = sum1 + ztcctTrends.getZxzs() * ztcctTrends.getDjlv(); // n天之和(展现指数 * 点击率)
					// sum2 = sum2 + ztcctTrends.getZxzs(); // n天之和(展现指数)
					sum1 = sum1 + ztcctTrends.getZxl() * ztcctTrends.getDjlv(); // n天之和(展现量 * 点击率 = 点击量)
					sum2 = sum2 + ztcctTrends.getZxl(); // n天之和(展现量)
				}
				if (sum2 != 0) {
					djl = sum1/sum2;
				}
				// ztcctHydjl.initDjl(djl);
				ztcctHydjl.setDjl(djl);
			}
			list.add(ztcctHydjl);
		}
		result.putKey("list", list);
		result.setCodeContent(Result.SUCCESS, "success");
		return result;
	}
	
	/**
	 * 获取：直通车车图行业趋势
	 * 
	 * @param gjcArr 关键词数据
	 * @return result
	 */
	public Result gjcqs(ZtcqsBean ztcqsBean) {
		Result result = new Result(101, "参数错误！");
		if (ztcqsBean == null || StringUtil.isNull2(ztcqsBean.getGjc()) || StringUtil.isNull2(ztcqsBean.getStartDate()) || StringUtil.isNull2(ztcqsBean.getEndDate())) {
			return result;
		}
		Cjzh cjzh = abujlbCookieStore.nextCjzhZtc();
		if (cjzh == null) {
			result.setCodeContent(101, "暂无可用的直通车账号！");
			return result;
		}
		Result resultTemp = ztcHttpClient.getToken(cjzh, 3);
		if (resultTemp == null || !resultTemp.isSuccess()) {
			result.setCodeContent(101, "直通车账号获取用户信息失败！");
			return result;
		}
		String ztcUser = (String) resultTemp.getKey("ztcUser");
		String token = (String) resultTemp.getKey("token");
		if (StringUtil.isNull2(token)) {
			result.setCodeContent(101, "直通车账号获取token失败！");
			return result;
		}
		// 获取webOpSessionId
		String webOpSessionId = ztcHashJSEngine.webOpSessionId();
		
		List<ZtcctHydjl> cateList = ztcHttpClient.getCategory(cjzh, token, ztcUser, ztcqsBean.getGjc(), webOpSessionId, 1);
		if (cateList == null || cateList.size() < 1 || StringUtil.isNull2(cateList.get(0).getCateId())) {
			result.setCodeContent(101, "直通车账号获取关键词类目失败！");
			return result;
		}
		List<ZtcctTrendsBean> list = ztcHttpClient.getTrends(cjzh, token, ztcUser, ztcqsBean.getGjc(), cateList.get(0).getCateId(), ztcqsBean.getStartDate(), ztcqsBean.getEndDate(), webOpSessionId, 1);
		result.putKey("list", list);
		result.setCodeContent(Result.SUCCESS, "success");
		return result;
	}

}
