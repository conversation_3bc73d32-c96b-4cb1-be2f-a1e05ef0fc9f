package com.abujlb.zdcj8.service;

import com.abujlb.ckstore.AbujlbCookieStore;
import com.abujlb.ckstore.Cjzh;
import com.abujlb.dao.OssDao;
import com.abujlb.jyrb.bean.ZtcMsg;
import com.abujlb.jyrb.http.ZtcHyrqbdHttpClient;
import com.abujlb.jyrb.http.ZtcStateHttpClient;
import com.abujlb.jyrb.log.CjLog;
import com.abujlb.jyrb.log.CjLogBean;
import com.abujlb.jyrb.service.DataUploader;
import com.abujlb.jyrb.tsdao.LogCjTsDao;
import com.abujlb.util.DateUtil;
import com.abujlb.zdcj8.bean.ztc.ZtcHyrqbdJl;
import com.abujlb.zdcj8.bean.ztc.ZtcHyrqbdRq;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Component
@Scope("prototype")
public class HyrqbdService {

    private static final Logger log = Logger.getLogger(HyrqbdService.class);
    @Autowired
    private AbujlbCookieStore cookieStore;
    @Autowired
    private DataUploader uploader;

    @Autowired
    private LogCjTsDao logCjTsDao;
    @Autowired
    private OssDao ossDao;

    @Autowired
    private DataUploader dataUploader;
    private ZtcMsg ztcMsg;

    public HyrqbdService() {
    }

    public HyrqbdService(ZtcMsg ztcMsg) {
        this.ztcMsg = ztcMsg;
    }

    /**
     * 消息处理入口
     *
     * @return
     */
    public boolean process() {
        try {
            //根据cateId获取token
            String cateId = ztcMsg.getCateId();
            if (StringUtils.isBlank(cateId)) {
                return true;
            }
            Boolean hyrqbdToken = dataUploader.getHyrqbdToken(Long.valueOf(cateId));
            if (!hyrqbdToken) {
                return true;
            }
            //采集标识
            boolean cjFlag = false;
            String cjdp = null;
            //从service2根据cateId获取已采集账号信息，执行数据采集,没获取到再执行for逻辑
            ZtcHyrqbdJl ztcHyrqbdJlByTopid = dataUploader.findZtcHyrqbdJlByTopid(Long.valueOf(cateId));
            String cookieKeyForZhuzh = null;
            Cjzh cjzh = null;
            if (ztcHyrqbdJlByTopid != null
                    && StringUtils.isNotBlank(cookieKeyForZhuzh = cookieStore.getCookieKeyForZhuzh(ztcHyrqbdJlByTopid.getZhuzh()))
                    && (cjzh = cookieStore.getCjzhForKey(cookieKeyForZhuzh)) != null
                    && ZtcStateHttpClient.ztc(cjzh)) {
                //执行数据采集
                try {
                    CjLog.newLog(cjzh);
                    cjFlag = cj(cateId, cjzh);
                    //采集成功直接return
                    if (cjFlag) {
                        return true;
                    } else {
                        cjdp = cjzh.getDpmc();
                    }
                } finally {
                    CjLogBean log = CjLog.getLogForSave();
                    logCjTsDao.save(log);
                }
            }
            String keysForCateZtc = cookieStore.getKeysForCateZtc(cateId);
            if (StringUtils.isBlank(keysForCateZtc)) {
                log.info("HyrqbdService.process获取cookieKey为空，cateId为：" + cateId);
                return true;
            }
            List<String> cookieKeyList = Arrays.stream(keysForCateZtc.split(",")).distinct().collect(Collectors.toList());
            //计数器,尝试3次不同的账号
            int count = 0;
            //执行for逻辑
            for (int i = 0; !cjFlag && count < 3 && i < cookieKeyList.size(); i++) {
                String cookieKey = cookieKeyList.get(i);
                cjzh = cookieStore.getCjzhForKey(cookieKey);
                if (cjzh == null || cjzh.getDpmc().equals(cjdp)) {
                    continue;
                }
                if (ZtcStateHttpClient.ztc(cjzh)) {
                    //执行数据采集
                    try {
                        CjLog.newLog(cjzh);
                        cjFlag = cj(cateId, cjzh);
                        if (cjFlag) {
                            return true;
                        }
                    } finally {
                        CjLogBean log = CjLog.getLogForSave();
                        logCjTsDao.save(log);
                    }
                }
                count++;
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return true;
    }

    private Boolean cj(String cateId, Cjzh cjzh) {
        try {
            JSONArray itemList = ZtcHyrqbdHttpClient.queryForRpt(cjzh);
            if (CollectionUtils.isEmpty(itemList)) {
                return false;
            }
            int count = 0;
            for (int i = 0; i < itemList.size(); i++) {
                JSONObject jsonObject = itemList.getJSONObject(i);
                String categoryIdList = jsonObject.getJSONObject("extraAttributes").getString("categoryId");
                if (StringUtils.isNotBlank(categoryIdList) && categoryIdList.contains(cateId)) {
                    TimeUnit.SECONDS.sleep(2);
                    //执行数据采集
                    String itemId = jsonObject.getString("itemId");
                    List<String> itemIdList = Arrays.asList(itemId);
                    JSONArray dyList = ZtcHyrqbdHttpClient.dyList(cjzh, itemIdList);
                    if (count >= 2) {
                        return false;
                    }
                    if (CollectionUtils.isEmpty(dyList)) {
                        log.info("HyrqbdService.cj获取单元列表为空，店铺名称为:" + cjzh.getDpmc() + "宝贝id为:" + itemId);
                        count++;
                        continue;
                    }
                    JSONObject dyJsonObject = dyList.getJSONObject(0);
                    String adgroupId = dyJsonObject.getString("adgroupId");
                    String campaignId = dyJsonObject.getString("campaignId");
                    if (StringUtils.isBlank(adgroupId) || StringUtils.isBlank(campaignId)) {
                        return false;
                    }
                    String rq = DateUtil.format(DateUtil.getLastDay());
                    TimeUnit.SECONDS.sleep(2);
                    JSONArray recSceneEffect = ZtcHyrqbdHttpClient.hyrqbd(cjzh, "recSceneEffect", adgroupId, campaignId, itemId);
                    if (recSceneEffect == null) {
                        return false;
                    }
                    ossDao.uploadauth(recSceneEffect.toJSONString(), "ztchyrq/" + rq + "/" + cateId + "/效果榜单.json");
                    TimeUnit.SECONDS.sleep(2);

                    JSONArray recSceneHeat = ZtcHyrqbdHttpClient.hyrqbd(cjzh, "recSceneHeat", adgroupId, campaignId, itemId);
                    if (recSceneHeat == null) {
                        return false;
                    }
                    ossDao.uploadauth(recSceneHeat.toJSONString(), "ztchyrq/" + rq + "/" + cateId + "/热度榜单.json");
                    TimeUnit.SECONDS.sleep(2);
                    JSONArray cjbd = ZtcHyrqbdHttpClient.cjbd(cjzh, adgroupId, campaignId, itemId);
                    if (cjbd == null) {
                        return false;
                    }
                    ossDao.uploadauth(cjbd.toJSONString(), "ztchyrq/" + rq + "/" + cateId + "/场景榜单.json");
                    //插入直通车行业人群榜单记录表
                    ZtcHyrqbdJl ztcHyrqbdJl = new ZtcHyrqbdJl();
                    ztcHyrqbdJl.setJlbdpid(cjzh.getJlbdpid());
                    ztcHyrqbdJl.setUserid(cjzh.getUserid());
                    ztcHyrqbdJl.setDpmc(cjzh.getDpmc());
                    ztcHyrqbdJl.setZhuzh(cjzh.getZhuzh());
                    ztcHyrqbdJl.setTopid(Long.valueOf(cateId));
                    ztcHyrqbdJl.setMsg("ztc_hyrqbd");
                    ztcHyrqbdJl.setJlsj(new Date());
                    ztcHyrqbdJl.setRq(DateUtil.getLastDay());
                    uploader.insertZtcHyrqbdJl(ztcHyrqbdJl);
                    //插入或者修改直通车行业人群榜单日期表
                    ZtcHyrqbdRq ztcHyrqbdRq = new ZtcHyrqbdRq();
                    ztcHyrqbdRq.setTopid(Long.valueOf(cateId));
                    ztcHyrqbdRq.setHyrqbdrq(DateUtil.getLastDay());
                    uploader.insertOrUpdateZtcHyrqbdRq(ztcHyrqbdRq);
                    return true;
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return false;
    }
}
