package com.abujlb.zdcj8.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.abujlb.Result;
import com.abujlb.zdcj8.bean.Dpts3Para;
import com.abujlb.zdcj8.dao.Dpts3TsDao;
import com.abujlb.zdcj8.http.Dpts3HttpClient;

@Component
public class Dpts3Service {
	private static final String TYPE_LLJG = "lljg";
	private static final String TYPE_SSGJC = "ssgjc";
	private static final String TYPE_ZTCGJC = "ztcgjc";

	@Autowired
	private Dpts3TsDao tsDao;
	@Autowired
	private Dpts3HttpClient httpClient;

	/**
	 * 获取流量结构数据
	 * 
	 * @param para
	 * @return
	 */
	public Result lljg(Dpts3Para para) {
		Result result = new Result();
		String data = tsDao.getDpts3Data(para, TYPE_LLJG);
		if (data != null) {
			result.setCodeContent(Result.SUCCESS, "success");
			result.putKey("data", new StringBuilder(data));
			return result;
		}
		data = httpClient.lljg(para, null);
		if (data != null) {
			tsDao.saveDpts3Data(para, TYPE_LLJG, data);
			result.setCodeContent(Result.SUCCESS, "success");
			result.putKey("data", new StringBuilder(data));
			return result;
		}
		result.setCodeContent(101, "获取数据失败");
		return result;
	}

	/**
	 * 获取搜索关键词数据
	 * 
	 * @param para
	 * @return
	 */
	public Result ssgjc(Dpts3Para para) {
		Result result = new Result();
		String data = tsDao.getDpts3Data(para, TYPE_SSGJC);
		if (data != null) {
			result.setCodeContent(Result.SUCCESS, "success");
			result.putKey("data", new StringBuilder(data));
			return result;
		}
		data = httpClient.ssgjc(para, null);
		if (data != null) {
			tsDao.saveDpts3Data(para, TYPE_SSGJC, data);
			result.setCodeContent(Result.SUCCESS, "success");
			result.putKey("data", new StringBuilder(data));
			return result;
		}
		result.setCodeContent(101, "获取数据失败");
		return result;
	}

	/**
	 * 获取直通车关键词数据
	 * 
	 * @param para
	 * @return
	 */
	public Result ztcgjc(Dpts3Para para) {
		Result result = new Result();
		String data = tsDao.getDpts3Data(para, TYPE_ZTCGJC);
		if (data != null) {
			result.setCodeContent(Result.SUCCESS, "success");
			result.putKey("data", new StringBuilder(data));
			return result;
		}
		data = httpClient.ztcgjc(para, null);
		if (data != null) {
			tsDao.saveDpts3Data(para, TYPE_ZTCGJC, data);
			result.setCodeContent(Result.SUCCESS, "success");
			result.putKey("data", new StringBuilder(data));
			return result;
		}
		result.setCodeContent(101, "获取数据失败");
		return result;
	}
}
