package com.abujlb.zdcj8.service;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.abujlb.AbujlbBeanFactory;
import com.abujlb.Result;
import com.abujlb.ThreadPool;
import com.abujlb.gson.Json2Object;
import com.abujlb.util.DateUtil;
import com.abujlb.util.StringUtil;
import com.abujlb.zdcj8.bean.BbjlSjs;
import com.abujlb.zdcj8.bean.JptsBbxx;
import com.abujlb.zdcj8.bean.JptsBbxx2;
import com.abujlb.zdcj8.bean.JptsBbxx3;
import com.abujlb.zdcj8.bean.JptsLljg;
import com.abujlb.zdcj8.bean.SjsSkusale;
import com.abujlb.zdcj8.bean.SkuSale;
import com.abujlb.zdcj8.bean.ZhSjs;
import com.abujlb.zdcj8.bean.sjs.KllqdInput;
import com.abujlb.zdcj8.bean.sjs.Ssdbksp;
import com.abujlb.zdcj8.bean.sjs.SsdbkspDjb;
import com.abujlb.zdcj8.bean.sjs.SsdbkspZzb;
import com.abujlb.zdcj8.bean.sjs.SskspInput;
import com.abujlb.zdcj8.bean.sjs.SsmdInput;
import com.abujlb.zdcj8.bean.sjs.Tkl;
import com.abujlb.zdcj8.bean.sjs.WwdbBean;
import com.abujlb.zdcj8.http.SjsHttpClient;
import com.abujlb.zdcj8.http.SjsUploader;
import com.abujlb.zdcj8.script.Md5SjsJSEngine;
import com.abujlb.zdcj8.thread.JptsBbxxThread;
import com.abujlb.zdcj8.thread.JptsLljgThread;
import com.abujlb.zdcj8.thread.SkuSaleThread;
import com.abujlb.zdcj8.tsdao.SjsJptsTsDao;
import com.abujlb.zdcj8.tsdao.SjsSkusaleTsDao;
import com.abujlb.zdcj8.util.SkuXLSReader;
import com.abujlb.zdcj8.util.StrUtil;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;

/**
 * 数据蛇service
 * 
 * <AUTHOR>
 * @date 2020-09-18 16:25:27
 */
@Component("sjsService")
public class SjsService {

	private static Logger log = Logger.getLogger(SjsService.class);

	@Autowired
	private SjsUploader sjsUploader;
	@Autowired
	private SjsHttpClient sjsHttpClient;
	@Autowired
	private SjsSkusaleTsDao sjsSkusaleTsDao;
	@Autowired
	private SjsJptsTsDao sjsJptsTsDao;
	@Autowired
	private AbujlbBeanFactory abujlbBeanFactory;
	@Autowired
	private ThreadPool threadPool; // 调用线程池，最多并行线程是5个
	@Autowired
	private Ssdbksp ssdbksp;
	@Autowired
	private Md5SjsJSEngine md5SjsJSEngine;

	/**
	 * 竞品sku销量：最近30天
	 * 
	 * @param bbid 宝贝id
	 * @return result结果
	 */
	public Result sku(String bbid) {
		Result result = new Result(101, "参数错误！");
		if (StrUtil.isNull(bbid)) {
			return result;
		}
		long nowtime = System.currentTimeMillis();
		/******************************
		 * 一、竞品sku销量数据是否已经存在
		 ******************************/
		BbjlSjs bbjlSjs = sjsUploader.getBbjl(bbid, BbjlSjs.SKU_SALE);

		String tjrq2 = DateUtil.formatDate(new Date(nowtime - 24 * 60 * 60 * 1000L)); // 昨天，1天前，86400000L=24*60*60*1000L
		String tjrq = DateUtil.formatDate(new Date(nowtime - 30 * 24 * 60 * 60 * 1000L)); // 30天前
		if (bbjlSjs == null) { // 数据不存在，直接请求数据
			this.skuSaleList(result, bbid, SkuSale.RQLX_RECENT30, tjrq, tjrq2); // 获取数据
			this.save(result, bbid, bbjlSjs); // 保存记录和历史数据
		} else {
			/****************************** 二、是否需要请求新的接口数据 ******************************/
			long days = (DateUtil.parseDate(tjrq2).getTime() - bbjlSjs.getRq().getTime()) / (1000 * 3600 * 24); // 日期相差天数
			if (days == 0) { // 不用请求
				SjsSkusale sjsSkusale = new SjsSkusale(bbid, null, tjrq, tjrq2, "rq", "asc"); // 按照日期正序查询
				List<SkuSale> list = sjsSkusaleTsDao.selectRowsByIndex(sjsSkusale);
				result.putKey("list", list);
				result.setCodeContent(Result.SUCCESS, "success");
				return result;
			} else if (days > 0 && days <= 7) { // 请求数据，消耗1个积分
				this.getListByDays(result, bbid, bbjlSjs, 7, nowtime, SkuSale.RQLX_RECENT7);
			} else if (days > 7 && days <= 14) { // 请求数据，消耗2个积分
				this.getListByDays(result, bbid, bbjlSjs, days, nowtime, SkuSale.RQLX_DAY);
			} else if (days > 14 && days <= 21) { // 请求数据，消耗2个积分
				this.getListByDays(result, bbid, bbjlSjs, days, nowtime, SkuSale.RQLX_DAY);
			} else { // 直接请求30天的数据，消耗3个积分
				this.skuSaleList(result, bbid, SkuSale.RQLX_RECENT30, tjrq, tjrq2); // 获取数据
				this.save(result, bbid, bbjlSjs); // 保存记录和历史数据
			}
		}
		return result;
	}

	/**
	 * 保存数据
	 * 
	 * @return
	 */
	@SuppressWarnings("unchecked")
	private Result getListByDays(Result result, String bbid, BbjlSjs bbjlSjs, long days, long nowtime, String rqlx) {
		String tjrq = DateUtil.formatDate(new Date(nowtime - days * 24 * 60 * 60 * 1000L)); // days天前
		String tjrq2 = DateUtil.formatDate(new Date(nowtime - 24 * 60 * 60 * 1000L)); // 昨天，1天前，86400000L=24*60*60*1000L

		this.skuSaleList(result, bbid, rqlx, tjrq, tjrq2); // 获取数据
		this.save(result, bbid, bbjlSjs); // 保存记录和历史数据
		List<SkuSale> list = new ArrayList<SkuSale>();
		if (result != null && result.isSuccess()) {
			list = (List<SkuSale>) result.getKey("list");
		}
		String endRq = DateUtil.formatDate(new Date(nowtime - (days + 1) * 24 * 60 * 60 * 1000L)); // days + 1天前，86400000L=24*60*60*1000L
		String startRq = DateUtil.formatDate(new Date(nowtime - 30 * 24 * 60 * 60 * 1000L)); // 30天前
		SjsSkusale sjsSkusale = new SjsSkusale(bbid, null, startRq, endRq, "rq", "asc"); // 按照日期正序查询
		List<SkuSale> list2 = sjsSkusaleTsDao.selectRowsByIndex(sjsSkusale);
		if (list2 != null && list2.size() > 0) {
			if (list == null) {
				list = list2;
			} else {
				list.addAll(list2);
			}
		}
		Collections.sort(list); // 按日期正序排序，即日期较大的在后面
		result.putKey("list", list);
		result.setCodeContent(Result.SUCCESS, "success");
		return result;
	}

	/**
	 * 保存数据
	 * 
	 * @return
	 */
	@SuppressWarnings("unchecked")
	private Result save(Result result, String bbid, BbjlSjs bbjlSjs) {
		if (result == null || !result.isSuccess()) {
			return result;
		}
		List<SkuSale> list = (List<SkuSale>) result.getKey("list");
		if (list == null || list.size() < 1) {
			return result;
		}
		Collections.sort(list); // 按日期正序排序，即日期较大的在后面
		String rq = list.get(list.size() - 1).getTjrq();
		if (StrUtil.isNull(rq)) {
			return result;
		}
		if (bbjlSjs == null) { // 新增
			bbjlSjs = new BbjlSjs(bbid, BbjlSjs.SKU_SALE, DateUtil.parseDate(rq));
		} else {
			bbjlSjs.setRq(DateUtil.parseDate(rq));
		}
		sjsUploader.saveBbjl(bbjlSjs);

		SkuSaleThread skuSaleThread = abujlbBeanFactory.getBean(SkuSaleThread.class); // 重新注入
		skuSaleThread.initParam(list, bbid);
		threadPool.exec(skuSaleThread); // 保存数据
		return result;
	}

	/**
	 * 获取竞品sku数据
	 * 
	 * @return
	 */
	private Result skuSaleList(Result result, String bbid, String rqlx, String startRq, String endRq) {
		ZhSjs zhSjs = sjsUploader.next();
		if (zhSjs == null) {
			result.setCodeContent(101, "暂无可用的数据蛇账号！");
			log.info(System.currentTimeMillis() + "，暂无可用的数据蛇账号！");
			return result;
		}
		Result resultTemp = sjsHttpClient.jpsaleSkuSave(zhSjs, bbid, rqlx, startRq, endRq);
		if (resultTemp == null || !resultTemp.isSuccess()) {
			result.setCodeContent(101, "扣费接口错误！");
			log.error(System.currentTimeMillis() + "，手机号：" + zhSjs.getSjh() + "，宝贝id：" + bbid + "，扣费接口错误！");
			sjsUploader.finish(zhSjs, false);
			return result;
		}
		Date date = new Date();
		long id = 0;
		String filename = "";
		resultTemp = sjsHttpClient.jpsaleSkulist(zhSjs, bbid);
		if (resultTemp == null || !resultTemp.isSuccess()) {
			result.setCodeContent(101, "sku查询接口错误！");
			log.error(System.currentTimeMillis() + "，手机号：" + zhSjs.getSjh() + "，宝贝id：" + bbid + "，sku查询接口错误！");
			sjsUploader.finish(zhSjs, false);
			return result;
		}
		if (resultTemp.isSuccess()) {
			sjsUploader.finish(zhSjs, true);

			id = (long) resultTemp.getKey("id");
			filename = sjsHttpClient.jpsaleSkuDownload(id); // 下载文件
			if (StrUtil.isNull(filename)) {
				result.setCodeContent(101, "下载文件错误！");
				log.error(System.currentTimeMillis() + "，手机号：" + zhSjs.getSjh() + "，宝贝id：" + bbid + "，下载文件错误！");
				return result;
			}
			List<SkuSale> list = SkuXLSReader.parseXLS(filename, date);
			result.putKey("list", list);
			result.setCodeContent(Result.SUCCESS, "success");
		}
		return result;
	}

	/**
	 * 超级竞品透视：最近30天（因为“竞品渠道数据”“竞品关键词数据(无线端)”“竞品直通车词数据(无线端)”不能按天拆分，不能按天保存，则不能通过最近7天的接口去补充数据，这个很重要）
	 * 
	 * @param bbid 宝贝id
	 * @return result结果
	 */
	public Result jpts(String bbid) {
		Result result = new Result(101, "参数错误！");
		if (StrUtil.isNull(bbid)) {
			return result;
		}
		long nowtime = System.currentTimeMillis();
		/****************************** 一、记录 ******************************/
		Gson gson = new Gson();
		BbjlSjs bbjlSjs = sjsUploader.getBbjl(bbid, BbjlSjs.SUPER_JPTS);

		String tjrq2 = DateUtil.formatDate(new Date(nowtime - 24 * 60 * 60 * 1000L)); // 昨天，1天前，86400000L=24*60*60*1000L
		// String tjrq = DateUtil.formatDate(new Date(nowtime - 30 * 24 * 60 * 60 * 1000L)); // 30天前
		if (bbjlSjs == null) { // 记录不存在，直接请求30天的数据，消耗1个积分
			this.getSjsSuperJpts(result, bbid, nowtime);
		} else {
			/****************************** 二、是否需要请求新的接口数据 ******************************/
			long days = (DateUtil.parseDate(tjrq2).getTime() - bbjlSjs.getRq().getTime()) / (1000 * 3600 * 24); // 日期相差天数
			if (days == 0) { // 不用请求
				List<JptsBbxx> list = new ArrayList<JptsBbxx>();
				Result resultTemp = sjsJptsTsDao.getRow(bbid, tjrq2);
				if (resultTemp != null && resultTemp.isSuccess()) {
					String _data = (String) resultTemp.getKey("data");
					Type type = new TypeToken<ArrayList<JptsBbxx>>() {
					}.getType();
					list = gson.fromJson(_data, type);
				}
				result.putKey("list", list);
				result.setCodeContent(Result.SUCCESS, "success");
				return result;
			} else { // 直接请求30天的数据，消耗1个积分
				this.getSjsSuperJpts(result, bbid, nowtime);
			}
		}
		return result;
	}

	/**
	 * 超级竞品透视，数据获取，并保存
	 * 
	 * @return
	 */
	@SuppressWarnings("unchecked")
	private Result getSjsSuperJpts(Result result, String bbid, long nowtime) {
		/*********************************************
		 * 一、获取数据蛇账号
		 *********************************************/
		ZhSjs zhSjs = sjsUploader.next();
		if (zhSjs == null) {
			result.setCodeContent(101, "暂无可用的数据蛇账号！");
			log.info(System.currentTimeMillis() + "，暂无可用的数据蛇账号！");
			return result;
		}
		/*********************************************
		 * 二、扣费
		 *********************************************/
		Result resultTemp = sjsHttpClient.superJptsSave(zhSjs, bbid, "recent30", nowtime); // 最近30天数据
		if (resultTemp == null || !resultTemp.isSuccess()) {
			result.setCodeContent(101, "扣费接口错误！");
			log.error(System.currentTimeMillis() + "，手机号：" + zhSjs.getSjh() + "，宝贝id：" + bbid + "，【超级竞品透视】扣费接口错误！");
			sjsUploader.finish(zhSjs, false);
			return result;
		}
		/*********************************************
		 * 三、监控数据采集状态
		 *********************************************/
		String rq = DateUtil.formatDate(new Date(nowtime - 24 * 60 * 60 * 1000L)); // 昨天
		String uuid = (String) resultTemp.getKey("uuid");
		int lljgType = 0; // 流量结构数据标识
		int status = 0;
		int statusCjTask = 0;

		int time = 0; // 尝试次数
		int try_num = 20; // 尝试
		do {
			resultTemp = sjsHttpClient.superJptsLljg(zhSjs, uuid);
			if (resultTemp == null || !resultTemp.isSuccess()) { // 接口不能正常访问
				// break; // 结束循环
				result.setCodeContent(101, "流量结构接口不能正常访问！");
				log.error(System.currentTimeMillis() + "，手机号：" + zhSjs.getSjh() + "，宝贝id：" + bbid + "，uuid：" + uuid + "，流量结构接口不能正常访问！");
				sjsUploader.finish(zhSjs, false);
				return result;
			}
			status = (int) resultTemp.getKey("status");
			statusCjTask = (int) resultTemp.getKey("statusCjTask");
			if (statusCjTask == 2 && lljgType == 0) {
				lljgType = lljgType + 1;
				List<JptsLljg> gjcList = (List<JptsLljg>) resultTemp.getKey("gjcList");
				List<JptsLljg> qdList = (List<JptsLljg>) resultTemp.getKey("qdList");
				List<JptsLljg> ztcList = (List<JptsLljg>) resultTemp.getKey("ztcList");
				JptsLljgThread jptsLljgThread = abujlbBeanFactory.getBean(JptsLljgThread.class); // 重新注入
				jptsLljgThread.initParam(bbid, rq, zhSjs, uuid, gjcList, qdList, ztcList, 1);
				threadPool.exec(jptsLljgThread); // 保存数据
			}
			if (status == 2) {
				break;
			}
			time++;
			try {
				Thread.sleep(((time >= try_num) ? 0 : time) * 1000L); // 最多等待190
				// Thread.sleep(2000L);
			} catch (Exception e) {
				e.printStackTrace();
			}
		} while (time < try_num);
		/*********************************************
		 * 四、宝贝综合数据
		 *********************************************/
		if (status != 2) { // 超时
			result.setCodeContent(101, "流量结构接口超时！");
			log.info(System.currentTimeMillis() + "，流量结构接口超时！");
			sjsUploader.finish(zhSjs, false);
			return result;
		}
		resultTemp = sjsHttpClient.superJptsBbxx(zhSjs, bbid);
		if (resultTemp == null || !resultTemp.isSuccess()) {
			result.setCodeContent(101, "宝贝综合数据接口错误！");
			log.error(System.currentTimeMillis() + "，手机号：" + zhSjs.getSjh() + "，宝贝id：" + bbid + "，宝贝综合数据接口错误！");
			sjsUploader.finish(zhSjs, false);
			return result;
		}
		if (lljgType == 0) { // 启动线程保存流量结构数据。
			JptsLljgThread jptsLljgThread = abujlbBeanFactory.getBean(JptsLljgThread.class); // 重新注入
			jptsLljgThread.initParam(bbid, rq, zhSjs, uuid, null, null, null, 0);
			threadPool.exec(jptsLljgThread); // 保存数据
		}
		sjsUploader.finish(zhSjs, true);
		List<JptsBbxx> list = (List<JptsBbxx>) resultTemp.getKey("list");
		result.putKey("list", list);
		result.setCodeContent(Result.SUCCESS, "success");

		JptsBbxxThread jptsBbxxThread = abujlbBeanFactory.getBean(JptsBbxxThread.class); // 重新注入
		jptsBbxxThread.initParam(bbid, rq, list);
		threadPool.exec(jptsBbxxThread); // 保存数据

		sjsUploader.saveBbjl(new BbjlSjs(bbid, BbjlSjs.SUPER_JPTS, DateUtil.parseDate(rq))); // 保存宝贝记录
		return result;
	}
	
	/**
	 * 竞品透视：最近30天，https://www.shujushe.com/Navigation/3/
	 * 
	 * @param bbid 宝贝id
	 * @return result结果
	 */
	public Result jpts30(String bbid) {
		Result result = new Result(101, "参数错误！");
		if (StrUtil.isNull(bbid)) {
			return result;
		}
		/****************************** 一、记录 ******************************/
		Gson gson = new Gson();
		BbjlSjs bbjlSjs = sjsUploader.getBbjl(bbid, BbjlSjs.JPTS);
		long nowtime = System.currentTimeMillis();
		String tjrq = DateUtil.formatDate(new Date(nowtime - 24 * 60 * 60 * 1000L)); // 昨天，1天前，86400000L=24*60*60*1000L
		if (bbjlSjs == null) { // 记录不存在，直接请求30天的数据，消耗0.5个积分
			this.getSjsJpts30(result, bbid, tjrq);
		} else {
			/****************************** 二、是否需要请求新的接口数据 ******************************/
			long days = (DateUtil.parseDate(tjrq).getTime() - bbjlSjs.getRq().getTime()) / (1000 * 3600 * 24); // 日期相差天数
			if (days == 0) { // 不用请求
				List<JptsBbxx2> list = new ArrayList<JptsBbxx2>();
				Result resultTemp = sjsJptsTsDao.getRow2(bbid, tjrq);
				if (resultTemp != null && resultTemp.isSuccess()) {
					String _data = (String) resultTemp.getKey("data");
					Type type = new TypeToken<ArrayList<JptsBbxx2>>() {
					}.getType();
					list = gson.fromJson(_data, type);
				}
				result.putKey("list", list);
				result.setCodeContent(Result.SUCCESS, "success");
				return result;
			} else { // 直接请求30天的数据，消耗0.5个积分
				this.getSjsJpts30(result, bbid, tjrq);
			}
		}
		return result;
	}
	
	/**
	 * 竞品透视，数据获取，并保存，https://www.shujushe.com/Navigation/3/
	 * 
	 * @return
	 */
	@SuppressWarnings("unchecked")
	private Result getSjsJpts30(Result result, String bbid, String rq) {
		ZhSjs zhSjs = sjsUploader.next();
		if (zhSjs == null) {
			result.setCodeContent(101, "暂无可用的数据蛇账号！");
			log.info(System.currentTimeMillis() + "，暂无可用的数据蛇账号！");
			return result;
		}
		// 先判断历史数据，是否需要
		JptsBbxx3 jptsBbxx3 = null;
		String today = DateUtil.formatDate(new Date());
		Result resultTemp = sjsHttpClient.jptsHistoryList(zhSjs, 1);
		if (resultTemp != null && resultTemp.isSuccess()) {
			List<JptsBbxx3> hisList = (List<JptsBbxx3>) resultTemp.getKey("list");
			for (JptsBbxx3 bbxx : hisList) {
				if (bbid.equals(bbxx.getItemid()) && today.equals(bbxx.getRq()) && !StringUtil.isNull2(bbxx.getUuid())) {
					jptsBbxx3 = bbxx;
					break;
				}
			}
		}
		if (jptsBbxx3 != null) {
			this.getSjsJpts30_his(result, bbid, rq, zhSjs, jptsBbxx3);
			return result;
		} else {
			this.getSjsJpts30_save(result, bbid, rq, zhSjs);
			return result;
		}
	}
	
	/**
	 * 竞品透视，数据获取，并保存，https://www.shujushe.com/Navigation/3/
	 * 
	 * @return
	 */
	@SuppressWarnings("unchecked")
	private Result getSjsJpts30_his(Result result, String bbid, String rq, ZhSjs zhSjs, JptsBbxx3 jptsBbxx3) {
		Result resultTemp = sjsHttpClient.jptsHistoryData(zhSjs, jptsBbxx3.getUuid(), 1);
		if (resultTemp == null || !resultTemp.isSuccess()) {
			this.getSjsJpts30_save(result, bbid, rq, zhSjs);
			return result;
		}
		
		String currentTime = String.valueOf(System.currentTimeMillis()); // 毫秒
		String key = md5SjsJSEngine.getKey(currentTime, zhSjs.getUserId());
		resultTemp = sjsHttpClient.jptsList(zhSjs, bbid, currentTime, key);
		if (resultTemp == null || !resultTemp.isSuccess()) {
			result.setCodeContent(101, "竞品访客数据（最近30天）数据接口错误！");
			log.error(System.currentTimeMillis() + "，手机号：" + zhSjs.getSjh() + "，宝贝id：" + bbid + "，竞品访客数据（最近30天）接口错误！");
			sjsUploader.finish(zhSjs, false);
			return result;
		}
		sjsUploader.finish(zhSjs, true);
		List<JptsBbxx2> list = (List<JptsBbxx2>) resultTemp.getKey("list");
		result.putKey("list", list);
		result.setCodeContent(Result.SUCCESS, "success");
		
		Gson gson = new GsonBuilder().setDateFormat("yyyy-MM-dd HH:mm:ss").create();
		sjsJptsTsDao.updateRow2(bbid, rq, gson.toJson(list));
		
		sjsUploader.saveBbjl(new BbjlSjs(bbid, BbjlSjs.JPTS, DateUtil.parseDate(rq))); // 保存宝贝记录
		return result;
	}
	
	/**
	 * 竞品透视，数据获取，并保存，https://www.shujushe.com/Navigation/3/
	 * 
	 * @return
	 */
	@SuppressWarnings("unchecked")
	private Result getSjsJpts30_save(Result result, String bbid, String rq, ZhSjs zhSjs) {
		String currentTime = String.valueOf(System.currentTimeMillis()); // 毫秒
		String key = md5SjsJSEngine.getKey(currentTime, zhSjs.getUserId());
		Result resultTemp = sjsHttpClient.jptsSave(zhSjs, bbid, currentTime, key); // 发起请求
		if (resultTemp == null || !resultTemp.isSuccess()) {
			result.setCodeContent(101, "扣费接口错误！");
			log.error(System.currentTimeMillis() + "，手机号：" + zhSjs.getSjh() + "，宝贝id：" + bbid + "，【竞品透视】扣费接口错误！");
			sjsUploader.finish(zhSjs, false);
			return result;
		}
		resultTemp = sjsHttpClient.jptsList(zhSjs, bbid, currentTime, key);
		if (resultTemp == null || !resultTemp.isSuccess()) {
			result.setCodeContent(101, "竞品访客数据（最近30天）数据接口错误！");
			log.error(System.currentTimeMillis() + "，手机号：" + zhSjs.getSjh() + "，宝贝id：" + bbid + "，竞品访客数据（最近30天）接口错误！");
			sjsUploader.finish(zhSjs, false);
			return result;
		}
		sjsUploader.finish(zhSjs, true);
		List<JptsBbxx2> list = (List<JptsBbxx2>) resultTemp.getKey("list");
		result.putKey("list", list);
		result.setCodeContent(Result.SUCCESS, "success");
		
		Gson gson = new GsonBuilder().setDateFormat("yyyy-MM-dd HH:mm:ss").create();
		sjsJptsTsDao.updateRow2(bbid, rq, gson.toJson(list));
		
		sjsUploader.saveBbjl(new BbjlSjs(bbid, BbjlSjs.JPTS, DateUtil.parseDate(rq))); // 保存宝贝记录
		return result;
	}

	/**
	 * 淘口令生成
	 * 
	 * @param url 宝贝链接/搜索页面链接
	 * @return
	 */
	public Result tkl(String url) {
		Result result = new Result(101, "参数错误！");
		/*********************************************
		 * 一、获取数据蛇账号
		 *********************************************/
		ZhSjs zhSjs = sjsUploader.next();
		if (zhSjs == null) {
			result.setCodeContent(101, "暂无可用的数据蛇账号！");
			log.info(System.currentTimeMillis() + "，暂无可用的数据蛇账号！");
			return result;
		}
		/*********************************************
		 * 二、扣费
		 *********************************************/
		Result resultTemp = sjsHttpClient.tklsc(zhSjs, url); // 最近30天数据
		if (resultTemp == null || !resultTemp.isSuccess()) {
			result.setCodeContent(101, "扣费接口错误！");
			log.error(System.currentTimeMillis() + "，手机号：" + zhSjs.getSjh() + "，链接地址：" + url + "，【淘口令生成】扣费接口错误！");
			sjsUploader.finish(zhSjs, false);
			return result;
		}

		sjsUploader.finish(zhSjs, true);
		result.setCodeContent(Result.SUCCESS, "淘口令生成成功！");
		Tkl tkl = (Tkl) resultTemp.getKey("tkl");
		result.putKey("tkl", tkl.toString());
		return result;
	}

	/**
	 * 搜索秒单
	 * 
	 * @param data
	 * @return
	 */
	public Result ssmd(String data) {
		Result result = new Result(101, "参数错误！");
		/*********************************************
		 * 一、获取数据蛇账号
		 *********************************************/
		ZhSjs zhSjs = sjsUploader.next();
		if (zhSjs == null) {
			result.setCodeContent(101, "暂无可用的数据蛇账号！");
			log.info(System.currentTimeMillis() + "，暂无可用的数据蛇账号！");
			return result;
		}
		/*********************************************
		 * 二、扣费
		 *********************************************/
		SsmdInput input = Json2Object.parseObj(data, SsmdInput.class);

		Result resultTemp = sjsHttpClient.ssmd(zhSjs, input);
		if (resultTemp == null || !resultTemp.isSuccess()) {
			result.setCodeContent(101, "扣费接口错误！");
			log.error(System.currentTimeMillis() + "，手机号：" + zhSjs.getSjh() + "，地址：" + input.getBbid() + ",关键词：" + input.getGjc() + "，旺旺：" + input.getWw() + "，【搜索秒单】扣费接口错误！");
			sjsUploader.finish(zhSjs, false);
			return result;
		}

		/*********************************************
		 * 三、返回最新记录
		 *********************************************/
		resultTemp = sjsHttpClient.ssmdjl(zhSjs);
		if (resultTemp == null || !resultTemp.isSuccess()) {
			result.setCodeContent(101, "搜索秒单记录查询错误！");
			log.error(System.currentTimeMillis() + "，手机号：" + zhSjs.getSjh() + "，地址：" + input.getBbid() + ",关键词：" + input.getGjc() + "，旺旺：" + input.getWw() + "，【搜索秒单记录查询】错误！");
			sjsUploader.finish(zhSjs, false);
			return result;
		}

		sjsUploader.finish(zhSjs, true);
		return resultTemp;
	}

	/**
	 * 卡流量渠道
	 * 
	 * @param bbid 宝宝id
	 * @return
	 */
	public Result kllqd(String data) {
		Result result = new Result(101, "参数错误！");
		/*********************************************
		 * 一、获取数据蛇账号
		 *********************************************/
		ZhSjs zhSjs = sjsUploader.next();
		if (zhSjs == null) {
			result.setCodeContent(101, "暂无可用的数据蛇账号！");
			log.info(System.currentTimeMillis() + "，暂无可用的数据蛇账号！");
			return result;
		}
		/*********************************************
		 * 二、扣费
		 *********************************************/
		KllqdInput input = Json2Object.parseObj(data, KllqdInput.class);
		Result resultTemp = sjsHttpClient.kllqd(zhSjs, input);
		if (resultTemp == null || !resultTemp.isSuccess()) {
			result.setCodeContent(101, "扣费接口错误！");
			sjsUploader.finish(zhSjs, false);
			return result;
		}
		/*********************************************
		 * 三、获取记录
		 *********************************************/
		resultTemp = sjsHttpClient.kllqdjl(zhSjs, input);
		if (resultTemp == null || !resultTemp.isSuccess()) {
			result.setCodeContent(101, "记录查询接口错误！");
			sjsUploader.finish(zhSjs, false);
			return result;
		}
		sjsUploader.finish(zhSjs, true);
		result.setCode(Result.SUCCESS);
		result.setContent("成功");
		result.putKey("records", resultTemp.getKey("list"));
		return result;
	}

	/**
	 * 搜素卡首屏
	 * 
	 * @param data
	 * @return
	 */
	public Result ssksp(String data) {
		Result result = new Result(101, "参数错误！");
		/*********************************************
		 * 一、获取数据蛇账号
		 *********************************************/
		ZhSjs zhSjs = sjsUploader.next();
		if (zhSjs == null) {
			result.setCodeContent(101, "暂无可用的数据蛇账号！");
			log.info(System.currentTimeMillis() + "，暂无可用的数据蛇账号！");
			return result;
		}
		/*********************************************
		 * 二、扣费
		 *********************************************/
		SskspInput input = Json2Object.parseObj(data, SskspInput.class);
		Result resultTemp = sjsHttpClient.ssksp(zhSjs, input);
		if (resultTemp == null || !resultTemp.isSuccess()) {
			result.setCodeContent(101, "扣费接口错误！");
			log.error(System.currentTimeMillis() + "，手机号：" + zhSjs.getSjh() + "，地址：" + input.getBbid() + ",关键词：" + input.getGjc() + "，【搜索卡首屏】扣费接口错误！");
			sjsUploader.finish(zhSjs, false);
			return result;
		}

		/*********************************************
		 * 三、返回最新记录
		 *********************************************/
		resultTemp = sjsHttpClient.sskspjl(zhSjs);
		if (resultTemp == null || !resultTemp.isSuccess()) {
			result.setCodeContent(101, "搜索卡首屏记录查询错误！");
			log.error(System.currentTimeMillis() + "，手机号：" + zhSjs.getSjh() + "，地址：" + input.getBbid() + ",关键词：" + input.getGjc() + "，【搜索卡首屏记录查询】错误！");
			sjsUploader.finish(zhSjs, false);
			return result;
		}

		sjsUploader.finish(zhSjs, true);
		return resultTemp;
	}

	/**
	 * 搜索打标卡首屏
	 * 
	 * @param type 1霸道版 2至尊版 3属性版 4点击板
	 * @param data 根据type 对应不同的实体
	 * @return
	 */
	// TODO 未完成
	@SuppressWarnings("unused")
	public Result ssdbksp(int type, String data) {
		Result result = new Result(101, "参数错误！");
		/*********************************************
		 * 一、获取数据蛇账号
		 *********************************************/
		ZhSjs zhSjs = sjsUploader.next();
		if (zhSjs == null) {
			result.setCodeContent(101, "暂无可用的数据蛇账号！");
			log.info(System.currentTimeMillis() + "，暂无可用的数据蛇账号！");
			return result;
		}

		/*********************************************
		 * 二、扣费
		 *********************************************/
		Result resultTemp = null;

		if (type == 1) {
			// 霸道版
			resultTemp = ssdbksp.bd(zhSjs, data);
			return resultTemp;
		}

		if (type == 2) {
			// 至尊版
			SsdbkspZzb zzb = Json2Object.parseObj(data, SsdbkspZzb.class);
		}

		if (type == 3) {
			// 属性版
			resultTemp = ssdbksp.sx(zhSjs, data);
			return resultTemp;
		}

		if (type == 4) {
			SsdbkspDjb djb = Json2Object.parseObj(data, SsdbkspDjb.class);
		}

		if (resultTemp == null || !resultTemp.isSuccess()) {
			result.setCodeContent(101, "扣费接口错误！");
			log.error(System.currentTimeMillis() + "，手机号：" + zhSjs.getSjh() + "，类型：" + type + "，参数：" + data + "，【搜索打标卡首屏】扣费接口错误！");
			sjsUploader.finish(zhSjs, false);
			return result;
		}
		return null;
	}
	
	/**
	 * wwdb旺旺打标：https://www.shujushe.com/Navigation/2
	 * 
	 * @param bbid 宝贝id
	 * @return result结果
	 */
	public Result wwdb(String data) {
		Result result = new Result(101, "参数错误！");
		if (!StrUtil.isJson2(data)) {
			return result;
		}
		Gson gson = new Gson();
		WwdbBean wwdbBean = gson.fromJson(data, WwdbBean.class);
		if (wwdbBean == null || StrUtil.isNull2(wwdbBean.getNid()) || StrUtil.isNull2(wwdbBean.getTitle()) || StrUtil.isNull2(wwdbBean.getNick())) {
			result.setCodeContent(101, "参数错误2！");
			return result;
		}
		ZhSjs zhSjs = sjsUploader.next();
		if (zhSjs == null) {
			result.setCodeContent(101, "暂无可用的数据蛇账号！");
			log.info(System.currentTimeMillis() + "，暂无可用的数据蛇账号！");
			return result;
		}
		Result resultTemp = sjsHttpClient.wwdb(zhSjs, wwdbBean.getNid(), wwdbBean.getTitle(), wwdbBean.getNick(), 1);
		if (resultTemp == null || !resultTemp.isSuccess()) {
			result.setCodeContent(101, "接口错误！");
			sjsUploader.finish(zhSjs, false);
			return result;
		}
		sjsUploader.finish(zhSjs, true);
		result.setCodeContent(Result.SUCCESS, "success");
		return result;
	}

}
