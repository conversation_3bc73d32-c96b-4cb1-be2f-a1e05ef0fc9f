package com.abujlb.zdcj8.service;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.config.RequestConfig.Builder;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.abujlb.Result;
import com.abujlb.ckstore.AbujlbCookieStore;
import com.abujlb.ckstore.Cjzh;
import com.abujlb.ckstore.ModuleName;
import com.abujlb.ckstore.RecordError;
import com.abujlb.ckstore.RecordZdcjLog;
import com.abujlb.util.DateUtil;
import com.abujlb.util.SycmDataTojson;
import com.abujlb.util.SycmTransitid;
import com.abujlb.zdcj8.bean.DptsPara;
import com.abujlb.zdcj8.dao.DptsDao;
import com.abujlb.zdcj8.util.IsgUtil;

import net.sf.json.JSONObject;

@Component("dptsSpuService")
public class DptsSpuService {
	private static Logger log = Logger.getLogger(Dpts2Service.class);
	@Autowired
	private AbujlbCookieStore abujlbCookieStore;
	@Autowired
	private RecordZdcjLog cjlog;

	@Autowired
	private DptsDao dptsDao;

	@Autowired
	private RecordError recordError;
	@Autowired
	private IsgUtil isgUtil;

	public String ts(DptsPara p) {
		Result result = new Result();
		String date = DateUtil.format(DateUtil.getLastDay());
		p.setDate(date);
		// 判断是否已经存在
		String json = dptsDao.getDptsSpu(p);
		// 如果已经存在则不会再次重新获取
		if (json != null) {
			return json;
		}

		// 随机获取一个采集帐号
		Cjzh cjzh = null;
		int sbcs = 0;
		boolean cjzhzc = false;
		while (!cjzhzc && sbcs < 3) {
			cjzh = abujlbCookieStore.nextCjzhSj();
			if (cjzh == null) {
				result.setCodeContent(101, "系统忙，请稍后重试");
				return result.toString();
			}
			Map<String, String> dateMap = this.getCommDate(cjzh);
			if (dateMap == null) {
				sbcs++;
				continue;
			}
			cjzhzc = true;
			date = dateMap.get("update1Day");
			p.setDate(date);
			p.setNdate(dateMap.get("updateNDay"));
		}
		if (!cjzhzc) {
			result.setCodeContent(101, "系统忙，请稍后重试");
			return result.toString();
		}

		String cateid = cjzh.getJylmlist().get(0).getCateid();
		String summary = this.getSummary(cjzh, cateid, p);
		if (summary != null && !summary.trim().equals("") && !summary.trim().equalsIgnoreCase("null")) {

			StringBuilder sb = new StringBuilder(summary);
			result.putKey("data", sb);
			result.putKey("date", date);
			result.putKey("ndata", p.getNdate());

			result.setCodeContent(Result.SUCCESS, "success");
			dptsDao.saveDptsSpu(p, result.toString());
			abujlbCookieStore.back(cjzh, true, ModuleName.SPU);
		} else {
			result.setCodeContent(102, "获取失败，请重试！");
			abujlbCookieStore.back(cjzh, false, ModuleName.SPU);
		}
		return result.toString();
	}

	public Map<String, String> getCommDate(Cjzh cjzh) {
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(abujlbCookieStore.getCookie(cjzh))
				.build();
		long t = System.currentTimeMillis();
		HttpGet httpget = new HttpGet(
				"https://sycm.taobao.com/portal/common/commDate.json?targetUrl=http%3A%2F%2Fsycm.taobao.com%2Fmc%2Fmq%2Fproduct_insight&_="
						+ t + "&token=");
		httpget.setHeader("accept", "*/*");
		httpget.setHeader("accept-encoding", "gzip, deflate, sdch, br");
		httpget.setHeader("accept-language", "zh-CN,zh;q=0.8");
		httpget.setHeader("referer", "https://sycm.taobao.com/mc/mq/product_insight");
		httpget.setHeader("user-agent",
				"Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/55.0.2883.75 Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000)
				.setConnectionRequestTimeout(20000);
		httpget.setConfig(requestConfig.build());

		HttpResponse httpresponse;
		try {
			httpresponse = httpClient.execute(httpget);
			HttpEntity entity = httpresponse.getEntity();
			String body = EntityUtils.toString(entity);
			JSONObject jsonObj = JSONObject.fromObject(body);
			log.info(body);
			if (jsonObj.has("hasError") && !jsonObj.getBoolean("hasError") && jsonObj.has("content")
					&& jsonObj.getJSONObject("content").has("data")) {
				JSONObject dataobj = jsonObj.getJSONObject("content").getJSONObject("data");
				Map<String, String> dateMap = new HashMap<String, String>();
				@SuppressWarnings("unchecked")
				Iterator<String> iter = dataobj.keys();
				while (iter.hasNext()) {
					String key = iter.next();
					String value = dataobj.getString(key);
					dateMap.put(key, value);
				}
				return dateMap;
			} else {
				log.error(cjzh.getCookieKey() + " " + body);
				return null;
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return null;
		} finally {
			httpget.releaseConnection();
		}
	}

	public String getSummary(Cjzh cjzh, String cateid, DptsPara p) {
		
		cjlog.log(cjzh, "spu", "0", "zdcj8.DptsSpuService.getSummary");
		
		isgUtil.getCnaAndCreateIsg(cjzh.getCookieStore());
		
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(abujlbCookieStore.getCookie(cjzh))
				.build();
		long t = System.currentTimeMillis();
		String url = "https://sycm.taobao.com/mc/mkt/product/trend.json?dateType=day&dateRange=" + p.getDate() + "%7C"
				+ p.getDate() + "&indexCode=&device=0&cateId=" + cateid + "&sellerType=-1&spuId=" + p.getBbid() + "&_="
				+ t + "&token=" + cjzh.getMicrodata("legalityToken");
		HttpGet httpget = new HttpGet(url);
		httpget.setHeader("accept", "*/*");
		httpget.setHeader("accept-encoding", "gzip, deflate, sdch, br");
		httpget.setHeader("accept-language", "zh-CN,zh;q=0.8");
		httpget.setHeader("referer", "https://sycm.taobao.com/mc/mq/product_insight");
		httpget.setHeader("transit-id", SycmTransitid.getTransitid());
		httpget.setHeader("user-agent",
				"Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/55.0.2883.75 Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000)
				.setConnectionRequestTimeout(20000);
		httpget.setConfig(requestConfig.build());

		HttpResponse httpresponse;
		try {
			httpresponse = httpClient.execute(httpget);
			HttpEntity entity = httpresponse.getEntity();
			String body = EntityUtils.toString(entity);
			JSONObject jsonObj = JSONObject.fromObject(body);
			// 记录错误
			if (jsonObj.has("code") && jsonObj.getInt("code") != 0) {
				String message = jsonObj.has("message") ? jsonObj.getString("message") : "";
				recordError.record(cjzh, jsonObj.getInt("code"), message);
			}
			if (jsonObj.has("code") && jsonObj.getInt("code") == 0) {
				String data = SycmDataTojson.toJson(jsonObj.getString("data"));
				return data;
			} else {
				log.error(body);
				cjlog.log(cjzh, "spu", "101", body);
				return null;
			}
		} catch (Exception e) {
			recordError.record(cjzh, e.getMessage());
			log.error(e.getMessage(), e);
			return null;
		} finally {
			httpget.releaseConnection();
		}
	}
}
