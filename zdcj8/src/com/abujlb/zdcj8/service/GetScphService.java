package com.abujlb.zdcj8.service;

import java.lang.reflect.Type;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.config.RequestConfig.Builder;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.abujlb.Result;
import com.abujlb.ckstore.AbujlbCookieStore;
import com.abujlb.ckstore.Cjzh;
import com.abujlb.ckstore.ModuleName;
import com.abujlb.ckstore.RecordError;
import com.abujlb.ckstore.RecordZdcjLog;
import com.abujlb.util.DateUtil;
import com.abujlb.util.SycmDataTojson;
import com.abujlb.util.SycmTransitid;
import com.abujlb.zdcj8.bean.Item;
import com.abujlb.zdcj8.bean.Shop;
import com.abujlb.zdcj8.dao.ScphRedisDao;
import com.abujlb.zdcj8.util.IsgUtil;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

@Component("getScphService")
public class GetScphService {
	private static Logger log = Logger.getLogger("GetScphService");
	@Autowired
	private AbujlbCookieStore abujlbCookieStore;
	@Autowired
	private ScphRedisDao scphRedisDao;
	@Autowired
	private RecordError recordError;
	@Autowired
	private IsgUtil isgUtil;
	@Autowired
	private RecordZdcjLog cjlog;

	public static final int ACTION_CODE_GJY = 1; // 高交易
	public static final int ACTION_CODE_GLL = 2; // 高流量

	public String cj(String cateid, String topid, String type, int actionCode) {
		Result result = new Result();
		// 根据类目id获取一个采集账号
		Cjzh cjzh = abujlbCookieStore.nextCjzh(topid);
		try {
			if (cjzh == null) {
				result.setCodeContent(101, "系统忙，请稍后重试");
				return result.toString();
			}
			// 获取采集时间并拼装
			Map<String, String> dateMap = getCommDate(cjzh);
			if (dateMap == null) {
				result.setCodeContent(101, "获取失败");
				return result.toString();
			}

			isgUtil.getCna(cjzh.getCookieStore());
			
			String dateType = "recent30";
			if (type.contentEquals("shop")) {

				List<Shop> shopList = this.cjShop(cjzh, cateid, dateMap.get("updateNDay"), dateType, actionCode);
				if (shopList != null && shopList.size() > 0) {
					result.setCodeContent(0, "获取成功");
					result.putKey("data", shopList);
					result.putKey("maxDate", dateMap.get("updateNDay"));
					abujlbCookieStore.back(cjzh, true, ModuleName.UVJZFX);
				} else {
					result.setCodeContent(101, "获取失败");
					abujlbCookieStore.back(cjzh, false, ModuleName.UVJZFX);
				}
			} else if (type.contentEquals("item")) {
				List<Item> itemList = this.cjItem(cjzh, cateid, dateMap.get("updateNDay"), dateType, actionCode);
				if (itemList != null && itemList.size() > 0) {
					result.setCodeContent(0, "获取成功");
					result.putKey("data", itemList);
					result.putKey("maxDate", dateMap.get("updateNDay"));
					abujlbCookieStore.back(cjzh, true, ModuleName.UVJZFX);
				} else {
					result.setCodeContent(101, "获取失败");
					abujlbCookieStore.back(cjzh, false, ModuleName.UVJZFX);
				}
			} else {
				result.setCodeContent(101, "获取失败");
			}

		} catch (Exception e) {
			result.setCodeContent(103, "系统忙，请稍后重试");
			log.error(e.getMessage(), e);
		}
		return result.toString();
	}

	/**
	 *
	 * <AUTHOR>
	 * 
	 * @Description 获取生意参谋市场排行高流量店铺
	 *
	 * @param cjzh
	 *            采集账号
	 * @param cateid
	 *            类目id
	 * @param dateRange
	 *            时间范围
	 * @param dateType
	 *            时间类型 recent30
	 * @return
	 */
	public List<Shop> cjShop(Cjzh cjzh, String cateid, String nDate, String dateType, int actionCode) {
		Gson gson = new Gson();
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		String dataTemp = scphRedisDao.get(cateid, dateType, nDate, "shop", actionCode);
		if (dataTemp != null) {
			Type type = new TypeToken<List<Shop>>() {
			}.getType();
			List<Shop> res = gson.fromJson(dataTemp, type);
			return res;
		}

		String dateRange = null;
		try {
			if (dateType.equalsIgnoreCase("recent30")) {
				// 最近30天
				dateRange = sdf.format(DateUtil.getDateBefore(sdf.parse(nDate), 29)) + "%7C" + nDate;
			} else {
				// 最近7天
				dateRange = sdf.format(DateUtil.getDateBefore(sdf.parse(nDate), 6)) + "%7C" + nDate;
			}
		} catch (Exception e) {
		}

		isgUtil.createIsg(cjzh.getCookieStore());
		
		cjlog.log(cjzh, "scph", "0", "zdcj8.GetScphService.cjShop");
		
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(abujlbCookieStore.getCookie(cjzh))
				.build();
		List<Shop> list = new ArrayList<Shop>();
		long t = System.currentTimeMillis();
		int recordCount = 3;
		// 淘宝取消了页数，直接返回全部，改为2只用跑一次
		for (int i = 1; i < 2; i++) {
			if (i > recordCount) {
				break;
			}
			String url = "";
			String onetrace = "";
			if (actionCode == ACTION_CODE_GLL) {
				url += "https://sycm.taobao.com/mc/mq/mkt/rank/shop/hotsearch.json?dateRange=" + dateRange
						+ "&dateType=" + dateType + "&pageSize=100&page=" + i + "&order=desc&orderBy=uvIndex&cateId="
						+ cateid
						+ "&device=0&sellerType=-1&indexCode=cateRankId%2CuvIndex%2CseIpvUvHits%2CtradeIndex&_=" + t
						+ "&token=";
				onetrace = "sycm-mc-mq-market-rank.sycm-mc-mq-shop-search";
			} else {
				url += "https://sycm.taobao.com/mc/mq/mkt/rank/shop/hotsale.json?dateRange=" + dateRange + "&dateType="
						+ dateType + "&pageSize=100&page=" + i + "&order=desc&orderBy=tradeIndex&cateId=" + cateid
						+ "&device=0&sellerType=-1&indexCode=tradeIndex%2CtradeGrowthRange%2CpayRateIndex&_=" + t
						+ "&token=";
				onetrace = "sycm-mc-mq-market-rank.sycm-mc-mq-shop-sale";
			}
			HttpGet httpGet = new HttpGet(url);
			Builder requestConfig = RequestConfig.custom().setSocketTimeout(5000).setConnectTimeout(5000)
					.setConnectionRequestTimeout(5000).setSocketTimeout(3000);
			httpGet.setConfig(requestConfig.build());
			httpGet.setHeader("accept", "*/*");
			httpGet.setHeader("accept-encoding", "gzip, deflate, br");
			httpGet.setHeader("accept-language", "zh-CN,zh;q=0.9");
			httpGet.setHeader("referer", "https://sycm.taobao.com/mc/mq/market_rank?activeKey=shop&cateFlag=2&cateId="
					+ cateid + "&dateRange=" + dateRange + "&dateType=" + dateType + "&device=0&sellerType=-1");
			httpGet.setHeader("transit-id", SycmTransitid.getTransitid());
			httpGet.setHeader("onetrace-card-id", onetrace);
			
			httpGet.setHeader("user-agent",
					"Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.132 Safari/537.36");
			String body = null;
			HttpResponse httpResponse;
			try {
				httpResponse = httpClient.execute(httpGet);
				HttpEntity entity = httpResponse.getEntity();
				body = EntityUtils.toString(entity);
				JSONObject object = JSONObject.fromObject(body);
				// 记录错误
				if (object.has("code") && object.getInt("code") != 0) {
					String message = object.has("message") ? object.getString("message") : "";
					recordError.record(cjzh, object.getInt("code"), message);
				}
				if (object.has("code") && object.has("data")) {
					String data = SycmDataTojson.toJson(object.getString("data"));
					JSONArray jArray = JSONArray.fromObject(data);
					// recordCount =
					// object.getJSONObject("data").getInt("recordCount") / 100;
					arrayToShopList(jArray, list);
					if (list.size() > 0) {
						scphRedisDao.save(cateid, dateType, nDate, "shop", actionCode, gson.toJson(list));
					}
				} else {
					log.error("市场排行高流量店铺获取失败：" + body);
				}
			} catch (Exception e) {
				recordError.record(cjzh, e.getMessage());
				log.error("市场排行高流量店铺获取失败：" + e.getMessage(), e);
			} finally {
				httpGet.releaseConnection();
			}
			// 每次暂停1.5秒
			try {
				Thread.sleep(1500);
			} catch (Exception e) {
				log.error(e.getMessage(), e);
			}
		}
		return list;
	}

	/**
	 *
	 * <AUTHOR>
	 * 
	 * @Description 获取生意参谋市场排行高流量商品
	 *
	 * @param cjzh
	 *            采集账号
	 * @param cateid
	 *            类目id
	 * @param dateRange
	 *            时间范围
	 * @param dateType
	 *            时间类型 recent30
	 * @return
	 */
	public List<Item> cjItem(Cjzh cjzh, String cateid, String nDate, String dateType, int actionCode) {
		Gson gson = new Gson();
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		String dataTemp = scphRedisDao.get(cateid, dateType, nDate, "item", actionCode);
		if (dataTemp != null) {
			Type type = new TypeToken<List<Item>>() {
			}.getType();
			List<Item> res = gson.fromJson(dataTemp, type);
			return res;
		}

		String dateRange = null;
		try {
			if (dateType.equalsIgnoreCase("recent30")) {
				// 最近30天
				dateRange = sdf.format(DateUtil.getDateBefore(sdf.parse(nDate), 29)) + "%7C" + nDate;
			} else {
				// 最近7天
				dateRange = sdf.format(DateUtil.getDateBefore(sdf.parse(nDate), 6)) + "%7C" + nDate;
			}
		} catch (Exception e) {
		}

		isgUtil.createIsg(cjzh.getCookieStore());
		
		cjlog.log(cjzh, "scph", "0", "zdcj8.GetScphService.cjItem");
		
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(abujlbCookieStore.getCookie(cjzh))
				.build();
		List<Item> list = new ArrayList<Item>();
		long t = System.currentTimeMillis();
		int recordCount = 3;
		// 淘宝取消了页数，直接返回全部，改为2只用跑一次
		for (int i = 1; i < 2; i++) {
			if (i > recordCount) {
				break;
			}
			String url = "";
			
			String onetrace= "sycm-mc-mq-market-rank.sycm-mc-mq-shop-search";
			
			if (actionCode == ACTION_CODE_GLL) {
				url += "https://sycm.taobao.com/mc/mq/mkt/rank/item/hotsearch.json?dateRange=" + dateRange
						+ "&dateType=" + dateType + "&pageSize=100&page=" + i + "&order=desc&orderBy=uvIndex&cateId="
						+ cateid
						+ "&device=0&sellerType=-1&indexCode=cateRankId%2CuvIndex%2CseIpvUvHits%2CtradeIndex&_=" + t
						+ "&token=";
				
				onetrace= "sycm-mc-mq-market-rank.sycm-mc-mq-item-search--"+cateid+"-0--1----recent30-"+dateRange;
			} else {
				url += "https://sycm.taobao.com/mc/mq/mkt/rank/item/hotsale.json?dateRange=" + dateRange + "&dateType="
						+ dateType + "&pageSize=100&page=" + i + "&order=desc&orderBy=tradeIndex&cateId=" + cateid
						+ "&device=0&sellerType=-1&indexCode=tradeIndex%2CtradeGrowthRange%2CpayRateIndex&_=" + t
						+ "&token=";
				onetrace= "sycm-mc-mq-market-rank.sycm-mc-mq-item-sale--"+cateid+"-0--1----recent30-"+dateRange;
			}

			HttpGet httpGet = new HttpGet(url);
			Builder requestConfig = RequestConfig.custom().setSocketTimeout(5000).setConnectTimeout(5000)
					.setConnectionRequestTimeout(5000);
			httpGet.setConfig(requestConfig.build());
			httpGet.setHeader("accept", "*/*");
			httpGet.setHeader("accept-encoding", "gzip, deflate, br");
			httpGet.setHeader("accept-language", "zh-CN,zh;q=0.9");
			httpGet.setHeader("referer", "https://sycm.taobao.com/mc/mq/market_rank?activeKey=item&cateFlag=2&cateId="
					+ cateid + "&dateRange=" + dateRange + "&dateType=" + dateType + "&device=0&sellerType=-1");
			httpGet.setHeader("transit-id", SycmTransitid.getTransitid());
			httpGet.setHeader("onetrace-card-id", onetrace);
			httpGet.setHeader("user-agent",
					"Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.132 Safari/537.36");
			String body = null;
			HttpResponse httpResponse;
			try {
				httpResponse = httpClient.execute(httpGet);
				HttpEntity entity = httpResponse.getEntity();
				body = EntityUtils.toString(entity);
				JSONObject object = JSONObject.fromObject(body);
				// 记录错误
				if (object.has("code") && object.getInt("code") != 0) {
					String message = object.has("message") ? object.getString("message") : "";
					recordError.record(cjzh, object.getInt("code"), message);
				}
				if (object.has("code") && object.has("data")) {
					String data = SycmDataTojson.toJson(object.getString("data"));
					JSONArray jArray = JSONArray.fromObject(data);
					// recordCount =
					// object.getJSONObject("data").getInt("recordCount") / 100;
					arrayToItemList(jArray, list);
					if (list.size() > 0) {
						scphRedisDao.save(cateid, dateType, nDate, "item", actionCode, gson.toJson(list));
					}
				} else {
					log.error("市场排行高流量商品获取失败：" + body);
				}
			} catch (Exception e) {
				recordError.record(cjzh, e.getMessage());
				log.error("市场排行高流量商品获取失败：" + e.getMessage(), e);
			} finally {
				httpGet.releaseConnection();
			}
		}
		return list;
	}

	/**
	 * 获取生意参谋时间范围
	 */
	public Map<String, String> getCommDate(Cjzh cjzh) {
		cjlog.log(cjzh, "commDate", "0", "zdcj8.GetScphService.getCommDate");
		
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(abujlbCookieStore.getCookie(cjzh))
				.build();
		long t = System.currentTimeMillis();
		HttpGet httpget = new HttpGet(
				"https://sycm.taobao.com/portal/common/commDate.json?targetUrl=http%3A%2F%2Fsycm.taobao.com%2Fmc%2Fmq%2Fproduct_insight&_="
						+ t + "&token=");
		httpget.setHeader("accept", "*/*");
		httpget.setHeader("accept-encoding", "gzip, deflate, sdch, br");
		httpget.setHeader("accept-language", "zh-CN,zh;q=0.8");
		httpget.setHeader("referer", "https://sycm.taobao.com/mc/mq/product_insight");
		httpget.setHeader("user-agent",
				"Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/55.0.2883.75 Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000)
				.setConnectionRequestTimeout(20000);
		httpget.setConfig(requestConfig.build());

		HttpResponse httpresponse;
		try {
			httpresponse = httpClient.execute(httpget);
			HttpEntity entity = httpresponse.getEntity();
			String body = EntityUtils.toString(entity);

			JSONObject jsonObj = JSONObject.fromObject(body);
			if (jsonObj.has("hasError") && !jsonObj.getBoolean("hasError") && jsonObj.has("content")
					&& jsonObj.getJSONObject("content").has("data")) {
				JSONObject dataobj = jsonObj.getJSONObject("content").getJSONObject("data");
				Map<String, String> dateMap = new HashMap<String, String>();
				@SuppressWarnings("unchecked")
				Iterator<String> iter = dataobj.keys();
				while (iter.hasNext()) {
					String key = iter.next();
					String value = dataobj.getString(key);
					dateMap.put(key, value);
				}
				return dateMap;
			} else {
				log.error(body);
				return null;
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return null;
		} finally {
			httpget.releaseConnection();
		}
	}

	public List<Shop> arrayToShopList(JSONArray jArray, List<Shop> list) {
		for (Object object2 : jArray) {
			JSONObject j = JSONObject.fromObject(object2);
			JSONObject shopJson = JSONObject.fromObject(j.get("shop"));
			Shop shop = new Shop();
			shop.setDplx(shopJson.getBoolean("b2CShop") ? 1 : 0);
			shop.setDpmc(shopJson.getString("title"));
			shop.setDpPic(shopJson.has("pictureUrl") ? shopJson.getString("pictureUrl") : "");
			shop.setDpUrl(shopJson.getString("shopUrl"));
			shop.setJyzs(j.has("tradeIndex") ? JSONObject.fromObject(j.get("tradeIndex")).getDouble("value") : 0);
			shop.setLlzs(j.has("uvIndex") ? JSONObject.fromObject(j.get("uvIndex")).getDouble("value") : 0);
			shop.setPm(j.has("cateRankId") ? JSONObject.fromObject(j.get("cateRankId")).getInt("value") : 0);
			shop.setSsrq(j.has("seIpvUvHits") ? JSONObject.fromObject(j.get("seIpvUvHits")).getDouble("value") : 0);
			shop.setZzfzhl(j.has("payRateIndex") ? JSONObject.fromObject(j.get("payRateIndex")).getDouble("value") : 0);
			list.add(shop);
		}
		return list;
	}

	public List<Item> arrayToItemList(JSONArray jArray, List<Item> list) {
		for (Object object2 : jArray) {
			JSONObject j = JSONObject.fromObject(object2);
			JSONObject shopJson = JSONObject.fromObject(j.get("shop"));
			JSONObject itemJson = JSONObject.fromObject(j.get("item"));
			Item item = new Item();
			item.setBbbt(itemJson.getString("title"));
			item.setBbid(itemJson.getString("itemId"));
			item.setBbPic(itemJson.getString("pictUrl"));
			item.setBbUrl(itemJson.getString("detailUrl"));
			item.setDpmc(shopJson.getString("title"));
			item.setDpPic(shopJson.has("pictureUrl") ? shopJson.getString("pictureUrl") : "");
			item.setDpUrl(shopJson.getString("shopUrl"));
			item.setJyzs(j.has("tradeIndex") ? JSONObject.fromObject(j.get("tradeIndex")).getDouble("value") : 0);
			item.setLlzs(j.has("uvIndex") ? JSONObject.fromObject(j.get("uvIndex")).getDouble("value") : 0);
			item.setPm(j.has("cateRankId") ? JSONObject.fromObject(j.get("cateRankId")).getInt("value") : 0);
			item.setSsrq(j.has("seIpvUvHits") ? JSONObject.fromObject(j.get("seIpvUvHits")).getDouble("value") : 0);
			item.setZzfzhl(j.has("payRateIndex") ? JSONObject.fromObject(j.get("payRateIndex")).getDouble("value") : 0);
			list.add(item);
		}
		return list;
	}

	public static void main(String[] args) {
		// String cookie = "hng=CN%7Czh-CN%7CCNY%7C156; thw=cn;
		// miid=915521124384441773; ali_ab=************.1531880172618.6;
		// UM_distinctid=16538c0667438e-0682db2d232993-5d4e211f-1fa400-16538c066754c4;
		// t=14e0e14beb44e7ed5cafd34943d2d812; tg=0;
		// cna=YfvUEzp2aF0CASQF4Ld8BXR3; l=AsbGqDKu9Qo8H1O0-do5db4PlrZIowrh;
		// _cc_=UIHiLt3xSw%3D%3D; mt=ci=0_0;
		// enc=klE4Vfa5yL4pEQlmCCkwH1vpx3sVqicQUmw%2FPU9S9OouO3eVfoWL1690WO44WzppO%2FLoWO%2Fx2Upz98jCpTmKdw%3D%3D;
		// uc3=id2=&nk2=&lg2=; tracknick=;
		// _m_h5_tk=89c51e6002b679c285f227229eb00152_1539087533926;
		// _m_h5_tk_enc=2f0e3ef80e57c6ce201efd839c69a7a1;
		// cookie2=1ff0df559cc4037924693749e17d6345; _tb_token_=56fee5b6b61e;
		// JSESSIONID=D940A7056E1CDD085CD3C6EE1B917DEE; x=3350780681;
		// uc1=cookie14=UoTfItFoDhiZNw%3D%3D&lng=zh_CN; skt=953d094664122058;
		// sn=%E5%BF%97%E9%AB%98%E5%AE%B9%E8%B4%B5%E4%B8%93%E5%8D%96%E5%BA%97%3A%E4%BF%B1%E4%B9%90%E9%83%A8%E8%80%81%E5%B8%88;
		// unb=4146102966; csg=661fa0c4; _euacm_ac_l_uid_=4146102966;
		// 4146102966_euacm_ac_c_uid_=3350780681;
		// 4146102966_euacm_ac_rs_uid_=3350780681; _euacm_ac_rs_sid_=333049435;
		// _portal_version_=new; v=0; mkt_gray=1;
		// isg=BPn5lWtVhlZdT1rQwdUnQpO4CGUTru3dansIyRsufiCfohk0Y1ITimv0IObxGoXw";
		// String cateid = "50012493";
		// // 获取采集时间并拼装
		// SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		// try {
		//
		// String dateRange = "2018-09-10%7C2018-10-09";
		// System.out.println(dateRange);
		// String dateType = "recent30";
		//// List<Shop> list =new GetScphService().cjShop(cookie, null, cateid,
		// dateRange, dateType);
		//// for (Shop shop : list) {
		//// System.out.println(shop.getDpmc());
		//// }
		// } catch (Exception e) {
		// e.printStackTrace();
		// }
	}
}
