package com.abujlb.zdcj8.service;

import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.config.RequestConfig.Builder;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.abujlb.Result;
import com.abujlb.ckstore.AbujlbCookieStore;
import com.abujlb.ckstore.Cjzh;
import com.abujlb.ckstore.Jylm;
import com.abujlb.ckstore.ModuleName;
import com.abujlb.ckstore.RecordError;
import com.abujlb.czs.Cate;
import com.abujlb.util.DateUtil;
import com.abujlb.util.SycmDataTojson;
import com.abujlb.util.SycmTransitid;
import com.abujlb.zdcj8.bean.DptsGjcJob;
import com.abujlb.zdcj8.bean.DptsPara;
import com.abujlb.zdcj8.bean.Item;
import com.abujlb.zdcj8.dao.DptsDao;
import com.abujlb.zdcj8.util.IsgUtil;

import net.sf.json.JSONObject;

@Component("dpts2Service")
public class Dpts2Service {
	private static Logger log = Logger.getLogger(Dpts2Service.class);
	@Autowired
	private AbujlbCookieStore abujlbCookieStore;

	@Autowired
	private CateBaobei bb;

	@Autowired
	private DptsDao dptsDao;

	@Autowired
	private RecordError recordError;
	@Autowired
	private GetScphService getScphService;

	@Autowired
	private IsgUtil isgUtil;

	public String ts(DptsPara p) {
		return this.ts(p, true);
	}

	/**
	 * 
	 * @param p
	 * @param xz
	 *            是否修正访客数
	 * @return
	 */
	public String ts(DptsPara p, boolean xz) {
		Result result = new Result();
		String date = DateUtil.format(DateUtil.getLastDay());
		p.setDate(date);
		// 判断是否已经存在
		String json = dptsDao.getSummary(p);
		// 如果已经存在则不会再次重新获取
		if (json != null) {
			return json;
		}

		// 随机获取一个采集帐号
		Cjzh cjzh = null;

		Cate cate = bb.getLm(p.getBbid());
		int sbcs = 0;
		boolean cjzhzc = false;
		while (!cjzhzc && sbcs < 3) {
			// 先取本类目的帐号，如果本类目没有帐号，则随意取一个类目帐号
			cjzh = abujlbCookieStore.nextCjzh(cate.getTopid());
			if (cjzh == null) {
				cjzh = abujlbCookieStore.nextCjzhStd(cate.getTopid());
			}
			if (cjzh == null) {
				result.setCodeContent(101, "系统忙，请稍后重试");
				return result.toString();
			}
			Map<String, String> dateMap = this.getCommDate(cjzh);
			if (dateMap == null) {
				sbcs++;
				continue;
			}
			cjzhzc = true;
			date = dateMap.get("update1Day");
			p.setDate(date);
			p.setNdate(dateMap.get("updateNDay"));
		}
		if (!cjzhzc) {
			result.setCodeContent(101, "系统忙，请稍后重试");
			return result.toString();
		}

		String summary = this.getSummary(cjzh, cate.getTopid(), p);
		if (summary != null) {
			StringBuilder sb = new StringBuilder(summary);
			result.putKey("data", sb);
			result.putKey("date", date);
			result.putKey("ndata", p.getNdate());

			// 获取修正访客数数据
			// 1.判断帐号的对应类目版本
			Jylm jylm = cjzh.getLmForId(cate.getTopid());
			if (jylm != null
					&& (jylm.getVersion().equalsIgnoreCase("pro") || jylm.getVersion().equalsIgnoreCase("vip"))) {
				// 帐号对应类目是专业版以上的
				List<Item> itemList30 = getScphService.cjItem(cjzh, cate.getCateid(), p.getNdate(), "recent30", 2);
				Item item30 = getItemFromList(itemList30, p.getBbid());
				if (item30 != null) {
					result.putKey("xzfks30", item30.getLlzs());
				}
				List<Item> itemList7 = getScphService.cjItem(cjzh, cate.getCateid(), p.getNdate(), "recent7", 2);
				Item item7 = getItemFromList(itemList7, p.getBbid());
				if (item7 != null) {
					result.putKey("xzfks7", item7.getLlzs());
				}
			}

			// ****为了适配旧版，依然返回流量结构数据，后续版本会将这里屏蔽掉
			// DptsGjcJob dptsGjc = new DptsGjcJob(p.getBbid(), p.getDevice(),
			// p.getNdate(), 30);
			// String lljg = this.getLljg(cjzh, dptsGjc);
			// result.putKey("lljg", new
			// StringBuilder(DptsLljg.toDptsLljg(lljg)));
			// ************************************************************

			result.setCodeContent(Result.SUCCESS, "success");
			dptsDao.saveSummary(p, result.toString());
			abujlbCookieStore.back(cjzh, true, ModuleName.DPTS);
		} else {
			result.setCodeContent(102, "获取失败，请重试！");
			abujlbCookieStore.back(cjzh, false, ModuleName.DPTS);
		}
		return result.toString();
	}

	/**
	 * 判断宝贝id是否在列表中，如果在就返回，如果不在则返回null
	 * 
	 * @param itemList
	 * @param bbid
	 * @return
	 */
	public Item getItemFromList(List<Item> itemList, String bbid) {
		for (int i = 0; itemList != null && i < itemList.size(); i++) {
			if (bbid.equalsIgnoreCase(itemList.get(i).getBbid())) {
				return itemList.get(i);
			}
		}
		return null;
	}

	/**
	 * 带帐号的ts，测试用
	 */
	public String ts2(DptsPara p, Cjzh cjzh) {
		Result result = new Result();
		String date = DateUtil.format(DateUtil.getLastDay());
		p.setDate(date);
		// 判断是否已经存在
		String json = dptsDao.getSummary(p);
		// 如果已经存在则不会再次重新获取
		if (json != null) {
			return json;
		}

		// 随机获取一个采集帐号
		if (cjzh == null) {
			result.setCodeContent(101, "系统忙，请稍后重试");
			return result.toString();
		}
		Map<String, String> dateMap = this.getCommDate(cjzh);
		date = dateMap.get("update1Day");
		p.setNdate(dateMap.get("updateNDay"));
		p.setDate(date);
		String cateid = cjzh.getJylmlist().get(0).getCateid();
		String summary = this.getSummary(cjzh, cateid, p);
		if (summary != null) {
			StringBuilder sb = new StringBuilder(summary);
			result.putKey("data", sb);
			result.putKey("date", date);
			result.setCodeContent(Result.SUCCESS, "success");
			dptsDao.saveSummary(p, result.toString());
		} else {
			result.setCodeContent(102, "获取失败，请重试！");
		}
		return result.toString();
	}

	public Map<String, String> getCommDate(Cjzh cjzh) {
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cjzh.getCookieStore()).build();
		long t = System.currentTimeMillis();
		HttpGet httpget = new HttpGet(
				"https://sycm.taobao.com/portal/common/commDate.json?targetUrl=http%3A%2F%2Fsycm.taobao.com%2Fmc%2Fmq%2Fproduct_insight&_="
						+ t + "&token=");
		httpget.setHeader("accept", "*/*");
		httpget.setHeader("accept-encoding", "gzip, deflate, sdch, br");
		httpget.setHeader("accept-language", "zh-CN,zh;q=0.8");
		httpget.setHeader("referer", "https://sycm.taobao.com/mc/mq/product_insight");
		httpget.setHeader("user-agent",
				"Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/55.0.2883.75 Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000)
				.setConnectionRequestTimeout(20000);
		httpget.setConfig(requestConfig.build());

		HttpResponse httpresponse;
		try {
			httpresponse = httpClient.execute(httpget);
			HttpEntity entity = httpresponse.getEntity();
			String body = EntityUtils.toString(entity);
			JSONObject jsonObj = JSONObject.fromObject(body);
			if (jsonObj.has("hasError") && !jsonObj.getBoolean("hasError") && jsonObj.has("content")
					&& jsonObj.getJSONObject("content").has("data")) {
				JSONObject dataobj = jsonObj.getJSONObject("content").getJSONObject("data");
				Map<String, String> dateMap = new HashMap<String, String>();
				@SuppressWarnings("unchecked")
				Iterator<String> iter = dataobj.keys();
				while (iter.hasNext()) {
					String key = iter.next();
					String value = dataobj.getString(key);
					dateMap.put(key, value);
				}
				return dateMap;
			} else {
				log.error(cjzh.getCookieKey() + " " + body);
				return null;
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return null;
		} finally {
			httpget.releaseConnection();
		}
	}

	public String getSummary(Cjzh cjzh, String cateid, DptsPara p) {

		isgUtil.getCnaAndCreateIsg(cjzh.getCookieStore());

		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(abujlbCookieStore.getCookie(cjzh))
				.build();
		long t = System.currentTimeMillis();

		// 如果当前帐号存在与商品对应的类目，则采用商品类目，否则采用帐号的类目
		boolean exist = false;
		List<Jylm> jylmList = cjzh.getJylmlist();
		for (int i = 0; i < jylmList.size(); i++) {
			if (jylmList.get(i).getCateid().equals(cateid)) {
				exist = true;
				break;
			}
		}
		if (!exist) {
			cateid = cjzh.getJylmlist().get(0).getCateid();
		}

		String url = "https://sycm.taobao.com/mc/ci/item/trend.json?dateType=day&dateRange=" + p.getDate() + "%7C"
				+ p.getDate() + "&cateId=" + cateid + "&itemId=" + p.getBbid() + "&device=" + p.getDevice()
				+ "&sellerType=-1&indexCode=uvIndex%2CpayRateIndex%2CtradeIndex%2CpayByrCntIndex&_=" + t + "&token="
				+ cjzh.getMicrodata("legalityToken");
		HttpGet httpget = new HttpGet(url);
		httpget.setHeader("accept", "*/*");
		httpget.setHeader("accept-encoding", "gzip, deflate, sdch, br");
		httpget.setHeader("accept-language", "zh-CN,zh;q=0.8");
		httpget.setHeader("referer", "https://sycm.taobao.com/mc/mq/product_insight");
		httpget.setHeader("transit-id", SycmTransitid.getTransitid());
		httpget.setHeader("onetrace-card-id",
				"sycm-mc-ci-config-rival.sycm-mc-ci-config-rival.sycm-mc-ci-config-rival-search-rival-item");
		httpget.setHeader("user-agent",
				"Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/55.0.2883.75 Safari/537.36");

		//httpget.setHeader("bx-umidtoken", "T2gAATuxbl9M0tLzKvfsGFGG_j7Qu0eBZCpJeHl5ktl6cfMYOAfZ3HAqUklbRmNyQuE=");
		
		httpget.setHeader("bx-umidtoken", "defaultToken3_init_callback_not_called@@https://sycm.taobao.com/mc/mq/product_insight?activeKey=analyse&brandId=0&brandName=&cateFlag=1&cateId=" 
				+ cateid + "&dateRange" + p.getDate() + "%7C" + p.getDate() + "&dateType=day&device=0&modelId=&modelName=&parentCateId=0&sellerType=-1@@" + System.currentTimeMillis());
		// httpget.setHeader("bx-umidtoken", "defaultToken2_load_failed with timeout@@https://sycm.taobao.com/mc/ci/shop/recognition?activeKey=drain&cateFlag=1&cateId=50008164&dateRange=2021-03-21%7C2021-03-21&dateType=day&device=0&parentCateId=0&sellerType=-1@@1616381535029");
		// httpget.setHeader("bx-ua", "undefined");
		httpget.setHeader("bx-ua", "defaultFY3_fyjs_not_initialized@@https://sycm.taobao.com/mc/mq/product_insight@@" + System.currentTimeMillis());
		
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000)
				.setConnectionRequestTimeout(20000);
		httpget.setConfig(requestConfig.build());

		HttpResponse httpresponse;
		try {
			httpresponse = httpClient.execute(httpget);
			HttpEntity entity = httpresponse.getEntity();
			String body = EntityUtils.toString(entity);
			JSONObject jsonObj = JSONObject.fromObject(body);
			// 记录错误
			if (jsonObj.has("code") && jsonObj.getInt("code") != 0) {
				String message = jsonObj.has("message") ? jsonObj.getString("message") : "";
				recordError.record(cjzh, jsonObj.getInt("code"), message);
			}
			if (jsonObj.has("code") && jsonObj.getInt("code") == 0) {
				String data = SycmDataTojson.toJson(jsonObj.getString("data"));
				return data;
			} else {
				log.error(body);
				return null;
			}
		} catch (Exception e) {
			recordError.record(cjzh, e.getMessage());
			log.error(e.getMessage(), e);
			return null;
		} finally {
			httpget.releaseConnection();
		}
	}

	public String tslljg(DptsGjcJob p) {
		Result result = new Result();
		// 判断是否已经存在
		String json = dptsDao.getLljg(p);
		// 如果已经存在则不会再次重新获取
		if (json != null) {
			return json;
		}

		// 随机获取一个采集帐号
		Cjzh cjzh = null;

		cjzh = abujlbCookieStore.nextCjzhSj();
		if (cjzh == null) {
			result.setCodeContent(101, "系统忙，请稍后重试");
			return result.toString();
		}

		String lljg = this.getLljg(cjzh, p);
		if (lljg != null) {
			result.putKey("lljg", new StringBuilder(lljg));
			dptsDao.saveLljg(p, result.toString());
			abujlbCookieStore.back(cjzh, true, ModuleName.DPTS);
		} else {
			result.setCodeContent(102, "获取失败，请重试！");
			abujlbCookieStore.back(cjzh, false, ModuleName.DPTS);
		}
		return result.toString();
	}

	/**
	 * 获取商品最近30天的流量结构
	 */
	public String getLljg(Cjzh cjzh, DptsGjcJob p) {
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(abujlbCookieStore.getCookie(cjzh))
				.build();
		long t = System.currentTimeMillis();

		String cateid = cjzh.getJylmlist().get(0).getCateid();

		String device = p.getDevice();
		if (device == null || device.trim().length() == 0 || device.equals("0")) {
			device = "2";
			p.setDevice(device);
		}
		HttpGet httpget = new HttpGet("https://sycm.taobao.com/mc/rivalItem/analysis/getFlowSource.json?device="
				+ device + p.getRecentPara() + "&cateId=" + cateid + "&selfItemId=" + p.getBbid()
				+ "&indexCode=uv&orderBy=uv&order=desc&_=" + t + "&token=" + cjzh.getMicrodata("legalityToken"));
		httpget.setHeader("accept", "*/*");
		httpget.setHeader("accept-encoding", "gzip, deflate, sdch, br");
		httpget.setHeader("accept-language", "zh-CN,zh;q=0.8");
		httpget.setHeader("referer", "https://sycm.taobao.com/mc/ci/item/analysis");
		httpget.setHeader("transit-id", SycmTransitid.getTransitid());
		httpget.setHeader("user-agent",
				"Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/55.0.2883.75 Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000)
				.setConnectionRequestTimeout(20000);
		httpget.setConfig(requestConfig.build());

		HttpResponse httpresponse;
		try {
			httpresponse = httpClient.execute(httpget);
			HttpEntity entity = httpresponse.getEntity();
			String body = EntityUtils.toString(entity);
			JSONObject jsonObj = JSONObject.fromObject(body);
			// 记录错误
			if (jsonObj.has("code") && jsonObj.getInt("code") != 0) {
				String message = jsonObj.has("message") ? jsonObj.getString("message") : "";
				recordError.record(cjzh, jsonObj.getInt("code"), message);
			}
			if (jsonObj.has("code") && jsonObj.getInt("code") == 0) {
				String data = SycmDataTojson.toJson(jsonObj.getString("data"));
				return data;
			} else {
				log.error(body);
				return null;
			}
		} catch (Exception e) {
			recordError.record(cjzh, e.getMessage());
			log.error(e.getMessage(), e);
			return null;
		} finally {
			httpget.releaseConnection();
		}
	}
}
