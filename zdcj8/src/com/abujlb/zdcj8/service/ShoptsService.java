package com.abujlb.zdcj8.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.config.RequestConfig.Builder;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.abujlb.Result;
import com.abujlb.ckstore.AbujlbCookieStore;
import com.abujlb.ckstore.Cjzh;
import com.abujlb.ckstore.ModuleName;
import com.abujlb.ckstore.RecordError;
import com.abujlb.ckstore.RecordZdcjLog;
import com.abujlb.util.DateUtil;
import com.abujlb.util.SycmDataTojson;
import com.abujlb.util.SycmTransitid;
import com.abujlb.zdcj8.bean.ShoptsData;
import com.abujlb.zdcj8.bean.ShoptsPara;
import com.abujlb.zdcj8.bean.ShoptsResult;
import com.abujlb.zdcj8.dao.ShoptsDao;
import com.abujlb.zdcj8.util.IsgUtil;
import com.google.gson.Gson;

import net.sf.json.JSONObject;

@Component("shoptsService")
public class ShoptsService {
	private static Logger log = Logger.getLogger(ShoptsService.class);
	@Autowired
	private AbujlbCookieStore abujlbCookieStore;
	@Autowired
	private ShoptsDao shoptsDao;

	@Autowired
	private RecordError recordError;
	@Autowired
	private IsgUtil isgUtil;
	@Autowired
	private RecordZdcjLog cjlog;

	/**
	 * 获取店铺透视数据
	 * 
	 * @param para
	 *            店铺透视参数
	 * @return 店铺透视结果
	 */
	public String ts(ShoptsPara para) {
		Result result = new Result();
		List<ShoptsResult> shoptsResult = new ArrayList<ShoptsResult>();
		String date = this.getCommDate();
		String userid = para.getUserid();
		for (int i = 0; para.getCatelist() != null && i < para.getCatelist().length; i++) {
			String cateid = para.getCatelist()[i];
			ShoptsResult tsResult = new ShoptsResult();
			tsResult.setCateid(cateid);
			ShoptsData tsdata = this.getData(userid, cateid, date);
			tsResult.setData(tsdata);
			shoptsResult.add(tsResult);
		}
		result.putKey("date", date);
		result.putKey("list", shoptsResult);
		result.setCodeContent(Result.SUCCESS, "success");
		return result.toString();
	}

	/**
	 * 根据店铺id、类目id、日期获取店铺透视数据
	 * 
	 * @param userid
	 *            店铺userid
	 * @param cateid
	 *            经营类目id
	 * @param date
	 *            日期
	 * @return
	 */
	public ShoptsData getData(String userid, String cateid, String date) {
		ShoptsData data = shoptsDao.getData(userid, cateid, date);
		// 判断表格存储中是否已经存在
		if (data == null) {
			// 如果表格存储中不存在，则从生意参谋获取，并将获取到的数据写入表格存储中
			data = this.getDataSycm(userid, cateid, date);
			if (data != null) {
				shoptsDao.saveData(userid, date, cateid, data);
			}
		}
		return data;
	}

	/**
	 * 获取数据的更新日期
	 */
	public String getCommDate() {
		int sbcs = 0;
		boolean cjzhzc = false;
		String date = null;
		while (!cjzhzc && sbcs < 3) {
			Map<String, String> dateMap = this.getCommDateMap();
			if (dateMap == null) {
				sbcs++;
				continue;
			}
			cjzhzc = true;
			date = dateMap.get("update1Day");
		}
		if (date == null) {
			date = DateUtil.format(DateUtil.getLastDay());
		}
		return date;
	}

	private Map<String, String> getCommDateMap() {
		Cjzh cjzh = abujlbCookieStore.nextCjzhBjcs();
		if (cjzh == null) {
			return null;
		}
		cjlog.log(cjzh, "shopts", "0", "zdcj8.ShoptsService.getCommDateMap");
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(abujlbCookieStore.getCookie(cjzh))
				.build();
		long t = System.currentTimeMillis();
		HttpGet httpget = new HttpGet(
				"https://sycm.taobao.com/portal/common/commDate.json?targetUrl=http%3A%2F%2Fsycm.taobao.com%2Fmc%2Fci%2Fconfig%2Frival&_="
						+ t + "&token=");
		httpget.setHeader("accept", "*/*");
		httpget.setHeader("accept-encoding", "gzip, deflate, sdch, br");
		httpget.setHeader("accept-language", "zh-CN,zh;q=0.8");
		httpget.setHeader("referer", "https://sycm.taobao.com/mc/ci/config/rival?activeKey=shop&cateFlag=1&cateId="
				+ cjzh.getJylmlist().get(0).getCateid() + "&parentCateId=0");
		httpget.setHeader("user-agent",
				"Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/55.0.2883.75 Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000)
				.setConnectionRequestTimeout(20000);
		httpget.setConfig(requestConfig.build());

		HttpResponse httpresponse;
		try {
			httpresponse = httpClient.execute(httpget);
			HttpEntity entity = httpresponse.getEntity();
			String body = EntityUtils.toString(entity);
			JSONObject jsonObj = JSONObject.fromObject(body);
			if (jsonObj.has("hasError") && !jsonObj.getBoolean("hasError") && jsonObj.has("content")
					&& jsonObj.getJSONObject("content").has("data")) {
				JSONObject dataobj = jsonObj.getJSONObject("content").getJSONObject("data");
				Map<String, String> dateMap = new HashMap<String, String>();
				@SuppressWarnings("unchecked")
				Iterator<String> iter = dataobj.keys();
				while (iter.hasNext()) {
					String key = iter.next();
					String value = dataobj.getString(key);
					dateMap.put(key, value);
				}
				return dateMap;
			} else {
				log.error(cjzh.getCookieKey() + " " + body);
				return null;
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return null;
		} finally {
			httpget.releaseConnection();
		}
	}

	/**
	 * 从生意参谋获取数据
	 */
	private ShoptsData getDataSycm(String userid, String cateid, String date) {
		int sbcs = 0;
		ShoptsData result = null;
		while (result == null && sbcs < 3) {
			result = this.getDataSycm2(userid, cateid, date);
			sbcs++;
		}
		return result;
	}

	private ShoptsData getDataSycm2(String userid, String cateid, String date) {
		Cjzh cjzh = abujlbCookieStore.nextCjzhStd(cateid);
		if (cjzh == null) {
			return null;
		}

		isgUtil.getCnaAndCreateIsg(cjzh.getCookieStore());

		cjlog.log(cjzh, "shopts", "0", "zdcj8.ShoptsService.getDataSycm2");
		
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(abujlbCookieStore.getCookie(cjzh))
				.build();
		long t = System.currentTimeMillis();
		HttpGet httpget = new HttpGet("https://sycm.taobao.com/mc/ci/shop/trend.json?dateType=day&dateRange=" + date
				+ "%7C" + date + "&cateId=" + cateid + "&userId=" + userid
				+ "&device=0&sellerType=-1&indexCode=uvIndex%2CpayRateIndex%2CtradeIndex%2CpayByrCntIndex&_=" + t
				+ "&token=");
		httpget.setHeader("accept", "*/*");
		httpget.setHeader("accept-encoding", "gzip, deflate, sdch, br");
		httpget.setHeader("accept-language", "zh-CN,zh;q=0.8");
		httpget.setHeader("referer", "https://sycm.taobao.com/mc/ci/config/rival?activeKey=shop&cateFlag=1&cateId="
				+ cateid + "&parentCateId=0");
		httpget.setHeader("transit-id", SycmTransitid.getTransitid());
		httpget.setHeader("onetrace-card-id",
				"sycm-mc-mq-market-rank.sycm-mc-mq-shop-sale.sycm-mc-mq-rival-item-" + userid);
		httpget.setHeader("user-agent",
				"Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/55.0.2883.75 Safari/537.36");

		httpget.setHeader("bx-umidtoken",
				"defaultToken2_load_failed with timeout@@https://sycm.taobao.com/mc/ci/config/rival?activeKey=shop&cateFlag=1&cateId="
						+ cateid + "&parentCateId=0@@" + System.currentTimeMillis());
		// httpget.setHeader("bx-umidtoken", "defaultToken2_load_failed with
		// timeout@@https://sycm.taobao.com/mc/ci/shop/recognition?activeKey=drain&cateFlag=1&cateId=50008164&dateRange=2021-03-21%7C2021-03-21&dateType=day&device=0&parentCateId=0&sellerType=-1@@1616381535029");
		httpget.setHeader("bx-ua", "undefined");

		Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000)
				.setConnectionRequestTimeout(20000);
		httpget.setConfig(requestConfig.build());

		HttpResponse httpresponse;
		try {
			httpresponse = httpClient.execute(httpget);
			HttpEntity entity = httpresponse.getEntity();
			String body = EntityUtils.toString(entity);

			JSONObject jsonObj = JSONObject.fromObject(body);
			// 记录错误
			if (jsonObj.has("code") && jsonObj.getInt("code") != 0) {
				String message = jsonObj.has("message") ? jsonObj.getString("message") : "";
				recordError.record(cjzh, jsonObj.getInt("code"), message);
			}
			if (jsonObj.has("code") && jsonObj.getInt("code") == 0) {
				String data = SycmDataTojson.toJson(jsonObj.getString("data"));
				abujlbCookieStore.back(cjzh, true, ModuleName.SHOPTS);
				Gson gson = new Gson();
				return gson.fromJson(data, ShoptsData.class);
			} else {
				abujlbCookieStore.back(cjzh, false, ModuleName.SHOPTS);
				log.error(body);
				cjlog.log(cjzh, "shopts", "101", body);
				return null;
			}
		} catch (Exception e) {
			recordError.record(cjzh, e.getMessage());
			cjlog.log(cjzh, "shopts", "101", e.getMessage());
			log.error(e.getMessage(), e);
			return null;
		} finally {
			httpget.releaseConnection();
		}
	}

}
