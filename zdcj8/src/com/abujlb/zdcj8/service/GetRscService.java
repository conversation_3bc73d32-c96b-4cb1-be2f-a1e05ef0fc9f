package com.abujlb.zdcj8.service;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStreamWriter;
import java.io.StringReader;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.CookieStore;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.config.RequestConfig.Builder;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.wltea.analyzer.core.IKSegmenter;
import org.wltea.analyzer.core.Lexeme;

import com.abujlb.Config;
import com.abujlb.Result;
import com.abujlb.ckstore.AbujlbCookieStore;
import com.abujlb.ckstore.Cjzh;
import com.abujlb.ckstore.ModuleName;
import com.abujlb.ckstore.RecordError;
import com.abujlb.ckstore.RecordZdcjLog;
import com.abujlb.dao.OssDao;
import com.abujlb.util.CookieString;
import com.abujlb.util.DateUtil;
import com.abujlb.util.SycmDataTojson;
import com.abujlb.util.SycmTransitid;
import com.abujlb.zdcj8.bean.RscFc;
import com.abujlb.zdcj8.bean.RscHy;
import com.abujlb.zdcj8.util.IsgUtil;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

@Component("getRscService")
public class GetRscService {
	private static Logger log = Logger.getLogger(GetRscService.class);

	@Autowired
	private RecordZdcjLog cjlog;

	/**
	 * 搜索词是否做为分词
	 */
	private static final boolean FC_SSC = true;

	@Autowired
	private AbujlbCookieStore abujlbCookieStore;
	@Autowired
	private RecordError recordError;
	@Autowired
	private Config config;
	@Autowired
	private OssDao ossDao;

	@Autowired
	private IsgUtil isgUtil;

	public String cj(String topid, String cateid) {
		Result result = new Result();
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		// 根据类目id获取一个采集账号
		Cjzh cjzh = abujlbCookieStore.nextCjzh(topid);
		try {
			if (cjzh == null) {
				result.setCodeContent(101, "系统忙，请稍后重试");
				return result.toString();
			}

			isgUtil.getCna(cjzh.getCookieStore());

			// 获取采集时间并拼装
			Map<String, String> dateMap = getCommDate(cjzh);
			String dateRange = sdf.format(DateUtil.getDateBefore(sdf.parse(dateMap.get("updateNDay")), 6)) + "%7C"
					+ dateMap.get("updateNDay");
			// 采集

			isgUtil.createIsg(cjzh.getCookieStore());
			List<RscHy> ssc = cjSsc(cjzh, cateid, dateRange, result);
			Thread.sleep(1500);
			isgUtil.createIsg(cjzh.getCookieStore());
			List<RscHy> cwc = cjCwc(cjzh, cateid, dateRange);
			Thread.sleep(1500);
			isgUtil.createIsg(cjzh.getCookieStore());
			List<RscFc> hxc = cjHxc(cjzh, cateid, dateRange);
			Thread.sleep(1500);
			isgUtil.createIsg(cjzh.getCookieStore());
			List<RscFc> xsc = cjXsc(cjzh, cateid, dateRange);

			if ((ssc.size() > 0 || cwc.size() > 0) && (hxc.size() > 0 || xsc.size() > 0)) {
				// 上传oss
				String[] paths = uploadOss(ssc, cwc, hxc, xsc, cateid);
				result.putKey("hyPath", paths[0]);
				result.putKey("fcPath", paths[1]);
				result.setCodeContent(0, "获取成功");
				// 返回账号状态
				abujlbCookieStore.back(cjzh, true, ModuleName.BTYH);
			} else {
				abujlbCookieStore.back(cjzh, false, ModuleName.BTYH);
				result.setCodeContent(102, "系统忙，请稍后重试");
				return result.toString();
			}
		} catch (Exception e) {
			result.setCodeContent(103, "系统忙，请稍后重试");
			log.error(e.getMessage(), e);
		}
		return result.toString();
	}

	public String cj(String topid, String cateid, String cookies) {
		Result result = new Result();
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		// 根据类目id获取一个采集账号
		CookieStore cookieStore = CookieString.strToCookies(cookies);
		Cjzh cjzh = new Cjzh(cookieStore);
		try {
			// 获取采集时间并拼装
			Map<String, String> dateMap = getCommDate(cjzh);
			String dateRange = sdf.format(DateUtil.getDateBefore(sdf.parse(dateMap.get("updateNDay")), 6)) + "%7C"
					+ dateMap.get("updateNDay");
			// 采集
			List<RscHy> ssc = cjSsc(cjzh, cateid, dateRange, result);
			if (result.getKey("returnCode") != null && result.getKey("returnCode").toString().contentEquals("10013")) {
				result.setCodeContent(10013, "类目无权限");
				return result.toString();
			}

			isgUtil.getCna(cjzh.getCookieStore());

			Thread.sleep(1500);

			isgUtil.createIsg(cjzh.getCookieStore());
			List<RscHy> cwc = cjCwc(cjzh, cateid, dateRange);
			Thread.sleep(1500);
			isgUtil.createIsg(cjzh.getCookieStore());
			List<RscFc> hxc = cjHxc(cjzh, cateid, dateRange);
			Thread.sleep(1500);
			isgUtil.createIsg(cjzh.getCookieStore());
			List<RscFc> xsc = cjXsc(cjzh, cateid, dateRange);

			if ((ssc.size() > 0 || cwc.size() > 0) && (hxc.size() > 0 || xsc.size() > 0)) {
				// 上传oss
				String[] paths = uploadOss(ssc, cwc, hxc, xsc, cateid);
				result.putKey("hyPath", paths[0]);
				result.putKey("fcPath", paths[1]);
				result.setCodeContent(0, "获取成功");
			} else {
				result.setCodeContent(102, "系统忙，请稍后重试");
				return result.toString();
			}
		} catch (Exception e) {
			result.setCodeContent(103, "系统忙，请稍后重试");
			log.error(e.getMessage(), e);
		}
		return result.toString();
	}

	/**
	 * 搜索词
	 */
	public List<RscHy> cjSsc(Cjzh cjzh, String cateid, String dateRange, Result result) {

		cjlog.log(cjzh, "ssc", "0", "zdcj8.GetRscService.cjSsc,cateid:" + cateid + ",dateRange:" + dateRange);

		List<RscHy> list = new ArrayList<RscHy>();
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(abujlbCookieStore.getCookie(cjzh))
				.build();
		long t = System.currentTimeMillis();
		String url = "https://sycm.taobao.com/mc/industry/searchWord.json?dateRange=" + dateRange
				+ "&dateType=recent7&pageSize=10&page=1&order=desc&orderBy=seIpvUvHits&cateId=" + cateid
				+ "&device=0&indexCode=hotSearchRank%2CseIpvUvHits%2CclickHits%2CclickRate%2CpayRate&_=" + t
				+ "&token=";
		HttpGet httpGet = new HttpGet(url);
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(5000).setConnectTimeout(5000)
				.setConnectionRequestTimeout(5000);
		httpGet.setConfig(requestConfig.build());
		httpGet.setHeader("accept", "*/*");
		httpGet.setHeader("accept-encoding", "gzip, deflate, br");
		httpGet.setHeader("accept-language", "zh-CN,zh;q=0.9");
		httpGet.setHeader("transit-id", SycmTransitid.getTransitid());
		httpGet.setHeader("referer", "https://sycm.taobao.com/mc/mq/search_rank?activeKey=searchWord&cateFlag=0&cateId="
				+ cateid + "&dateRange=" + dateRange + "&dateType=recent7&device=0");
		httpGet.setHeader("user-agent",
				"Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.132 Safari/537.36");
		String body = null;
		HttpResponse httpResponse;
		try {
			httpResponse = httpClient.execute(httpGet);
			HttpEntity entity = httpResponse.getEntity();
			body = EntityUtils.toString(entity);
			JSONObject object = JSONObject.fromObject(body);
			// 记录错误
			if (object.has("code") && object.getInt("code") != 0) {
				String message = object.has("message") ? object.getString("message") : "";
				result.putKey("returnCode", object.getInt("code"));
				recordError.record(cjzh, object.getInt("code"), message);
			}
			if (object.has("code") && object.has("data")) {
				String data = SycmDataTojson.toJson(object.getString("data"));
				JSONObject o = JSONObject.fromObject(data);
				JSONArray jArray = o.getJSONArray("hotList");
				list = arrayToHyList(jArray);
			} else {
				log.error("标题优化获取搜索词失败：" + body);
			}
		} catch (Exception e) {
			recordError.record(cjzh, e.getMessage());
			log.error("标题优化获取搜索词失败：" + e.getMessage(), e);
		} finally {
			httpGet.releaseConnection();
		}
		return list;
	}

	/**
	 * 长尾词
	 */
	public List<RscHy> cjCwc(Cjzh cjzh, String cateid, String dateRange) {
		cjlog.log(cjzh, "cwc", "0", "zdcj8.GetRscService.cjCwc,cateid:" + cateid + ",dateRange:" + dateRange);
		
		List<RscHy> list = new ArrayList<RscHy>();
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(abujlbCookieStore.getCookie(cjzh))
				.build();
		long t = System.currentTimeMillis();
		String url = "https://sycm.taobao.com/mc/industry/tailWord.json?dateRange=" + dateRange
				+ "&dateType=recent7&pageSize=10&page=1&order=desc&orderBy=seIpvUvHits&cateId=" + cateid
				+ "&device=0&indexCode=hotSearchRank%2CseIpvUvHits%2CclickHits%2CclickRate%2CpayRate&_=" + t
				+ "&token=";
		HttpGet httpGet = new HttpGet(url);
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(5000).setConnectTimeout(5000)
				.setConnectionRequestTimeout(5000);
		httpGet.setConfig(requestConfig.build());
		httpGet.setHeader("accept", "*/*");
		httpGet.setHeader("accept-encoding", "gzip, deflate, br");
		httpGet.setHeader("accept-language", "zh-CN,zh;q=0.9");
		httpGet.setHeader("transit-id", SycmTransitid.getTransitid());
		httpGet.setHeader("referer", "https://sycm.taobao.com/mc/mq/search_rank?activeKey=tailWord&cateFlag=0&cateId="
				+ cateid + "&dateRange=" + dateRange + "&dateType=recent7&device=0");
		httpGet.setHeader("user-agent",
				"Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.132 Safari/537.36");
		String body = null;
		HttpResponse httpResponse;
		try {
			httpResponse = httpClient.execute(httpGet);
			HttpEntity entity = httpResponse.getEntity();
			body = EntityUtils.toString(entity);
			JSONObject object = JSONObject.fromObject(body);
			// 记录错误
			if (object.has("code") && object.getInt("code") != 0) {
				String message = object.has("message") ? object.getString("message") : "";
				recordError.record(cjzh, object.getInt("code"), message);
			}
			if (object.has("code") && object.has("data")) {
				String data = SycmDataTojson.toJson(object.getString("data"));
				JSONObject o = JSONObject.fromObject(data);
				JSONArray jArray = o.getJSONArray("hotList");
				list = arrayToHyList(jArray);
			} else {
				// 采集失败
				log.error("标题优化获取长尾词失败：" + body);
			}
		} catch (Exception e) {
			recordError.record(cjzh, e.getMessage());
			log.error("标题优化获取长尾词失败：" + e.getMessage(), e);
		} finally {
			httpGet.releaseConnection();
		}
		return list;
	}

	/**
	 * 核心词
	 */
	public List<RscFc> cjHxc(Cjzh cjzh, String cateid, String dateRange) {
		cjlog.log(cjzh, "hxc", "0", "zdcj8.GetRscService.cjHxc,cateid:" + cateid + ",dateRange:" + dateRange);
		
		List<RscFc> list = new ArrayList<RscFc>();
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(abujlbCookieStore.getCookie(cjzh))
				.build();
		long t = System.currentTimeMillis();
		String url = "https://sycm.taobao.com/mc/industry/coreWord.json?dateRange=" + dateRange
				+ "&dateType=recent7&pageSize=10&page=1&order=desc&orderBy=seIpvUvHits&cateId=" + cateid
				+ "&device=0&indexCode=hotSearchRank%2CseIpvUvHits%2CclickHits%2CclickRate%2CpayRate&_=" + t
				+ "&token=";
		HttpGet httpGet = new HttpGet(url);
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(5000).setConnectTimeout(5000)
				.setConnectionRequestTimeout(5000);
		httpGet.setConfig(requestConfig.build());
		httpGet.setHeader("accept", "*/*");
		httpGet.setHeader("accept-encoding", "gzip, deflate, br");
		httpGet.setHeader("accept-language", "zh-CN,zh;q=0.9");
		httpGet.setHeader("transit-id", SycmTransitid.getTransitid());
		httpGet.setHeader("referer", "https://sycm.taobao.com/mc/mq/search_rank?activeKey=coreWord&cateFlag=0&cateId="
				+ cateid + "&dateRange=" + dateRange + "&dateType=recent7&device=0");
		httpGet.setHeader("user-agent",
				"Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.132 Safari/537.36");
		String body = null;
		HttpResponse httpResponse;
		try {
			httpResponse = httpClient.execute(httpGet);
			HttpEntity entity = httpResponse.getEntity();
			body = EntityUtils.toString(entity);
			JSONObject object = JSONObject.fromObject(body);
			// 记录错误
			if (object.has("code") && object.getInt("code") != 0) {
				String message = object.has("message") ? object.getString("message") : "";
				recordError.record(cjzh, object.getInt("code"), message);
			}
			if (object.has("code") && object.has("data")) {
				String data = SycmDataTojson.toJson(object.getString("data"));
				JSONObject o = JSONObject.fromObject(data);
				JSONArray jArray = o.getJSONArray("hotList");
				list = arrayToFcList(jArray);
			} else {
				// 采集失败
				log.error("标题优化获取核心词失败：" + body);
			}
		} catch (Exception e) {
			recordError.record(cjzh, e.getMessage());
			log.error("标题优化获取核心词失败：" + e.getMessage(), e);
		} finally {
			httpGet.releaseConnection();
		}
		return list;
	}

	/**
	 * 修饰词
	 */
	public List<RscFc> cjXsc(Cjzh cjzh, String cateid, String dateRange) {
		cjlog.log(cjzh, "xsc", "0", "zdcj8.GetRscService.cjXsc,cateid:" + cateid + ",dateRange:" + dateRange);
		
		List<RscFc> list = new ArrayList<RscFc>();
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(abujlbCookieStore.getCookie(cjzh))
				.build();
		long t = System.currentTimeMillis();
		String url = "https://sycm.taobao.com/mc/industry/attrWord.json?dateRange=" + dateRange
				+ "&dateType=recent7&pageSize=10&page=1&order=desc&orderBy=seIpvUvHits&cateId=" + cateid
				+ "&device=0&indexCode=hotSearchRank%2CseIpvUvHits%2CclickHits%2CclickRate%2CpayRate&_=" + t
				+ "&token=";
		HttpGet httpGet = new HttpGet(url);
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(5000).setConnectTimeout(5000)
				.setConnectionRequestTimeout(5000);
		httpGet.setConfig(requestConfig.build());
		httpGet.setHeader("accept", "*/*");
		httpGet.setHeader("accept-encoding", "gzip, deflate, br");
		httpGet.setHeader("accept-language", "zh-CN,zh;q=0.9");
		httpGet.setHeader("transit-id", SycmTransitid.getTransitid());
		httpGet.setHeader("referer", "https://sycm.taobao.com/mc/mq/search_rank?activeKey=attrWord&cateFlag=0&cateId="
				+ cateid + "&dateRange=" + dateRange + "&dateType=recent7&device=0");
		httpGet.setHeader("user-agent",
				"Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.132 Safari/537.36");
		String body = null;
		HttpResponse httpResponse;
		try {
			httpResponse = httpClient.execute(httpGet);
			HttpEntity entity = httpResponse.getEntity();
			body = EntityUtils.toString(entity);
			JSONObject object = JSONObject.fromObject(body);
			// 记录错误
			if (object.has("code") && object.getInt("code") != 0) {
				String message = object.has("message") ? object.getString("message") : "";
				recordError.record(cjzh, object.getInt("code"), message);
			}
			if (object.has("code") && object.has("data")) {
				String data = SycmDataTojson.toJson(object.getString("data"));
				JSONObject o = JSONObject.fromObject(data);
				JSONArray jArray = o.getJSONArray("hotList");
				list = arrayToFcList(jArray);
			} else {
				// 采集失败
				log.error("标题优化获取修饰词失败：" + body);
			}
		} catch (Exception e) {
			recordError.record(cjzh, e.getMessage());
			log.error("标题优化获取修饰词失败：" + e.getMessage(), e);
		} finally {
			httpGet.releaseConnection();
		}
		return list;
	}

	public List<RscHy> arrayToHyList(JSONArray jArray) {
		List<RscHy> list = new ArrayList<RscHy>();
		for (Object object2 : jArray) {
			JSONObject j = JSONObject.fromObject(object2);
			RscHy rscHy = new RscHy();
			rscHy.setClickHits(j.has("clickHits") == true ? j.getInt("clickHits") * 7 : 0);
			rscHy.setClickRate(j.has("clickRate") == true ? formartDouble(j.getDouble("clickRate") * 100) : 0);
			rscHy.setHotSearchRank(j.has("hotSearchRank") == true ? j.getInt("hotSearchRank") : 0);
			rscHy.setOrderNum(j.has("orderNum") == true ? j.getInt("orderNum") : 0);
			rscHy.setP4pRefPrice(j.has("p4pRefPrice") == true ? j.getDouble("p4pRefPrice") : 0);
			rscHy.setSearchWord(j.has("searchWord") == true ? j.getString("searchWord") : "");
			rscHy.setSeIpvUvHits(j.has("seIpvUvHits") == true ? j.getInt("seIpvUvHits") * 7 : 0);
			rscHy.setSoarRank(j.has("soarRank") == true ? j.getInt("soarRank") : 0);
			rscHy.setTmClickRate(j.has("tmClickRate") == true ? formartDouble(j.getDouble("tmClickRate") * 100) : 0);
			rscHy.setPayRate(j.has("payRate") == true ? formartDouble(j.getDouble("payRate") * 100) : 0);
			rscHy.setXl(Integer.parseInt(Math.round((rscHy.getClickHits() * rscHy.getPayRate() / 100)) + ""));// 销量
			list.add(rscHy);
		}
		return list;
	}

	public List<RscFc> arrayToFcList(JSONArray jArray) {
		List<RscFc> list = new ArrayList<RscFc>();
		for (Object object2 : jArray) {
			JSONObject j = JSONObject.fromObject(object2);
			RscFc rscFc = new RscFc();
			rscFc.setAvgWordClickHits(j.has("avgWordClickHits") == true ? j.getInt("avgWordClickHits") * 7 : 0);
			rscFc.setAvgWordSeIpvUvHits(j.has("avgWordSeIpvUvHits") == true ? j.getInt("avgWordSeIpvUvHits") * 7 : 0);
			rscFc.setAvgWordClickRate(
					j.has("avgWordClickRate") == true ? formartDouble(j.getDouble("avgWordClickRate") * 100) : 0);
			rscFc.setAvgWordPayRate(
					j.has("avgWordPayRate") == true ? formartDouble(j.getDouble("avgWordPayRate") * 100) : 0);
			rscFc.setHotSearchRank(j.has("hotSearchRank") == true ? j.getInt("hotSearchRank") : 0);
			rscFc.setP4pRefPrice(j.has("p4pRefPrice") == true ? j.getDouble("p4pRefPrice") : 0);
			rscFc.setRelSeWordCnt(j.has("relSeWordCnt") == true ? j.getInt("relSeWordCnt") : 0);
			rscFc.setSearchWord(j.has("searchWord") == true ? j.getString("searchWord") : "");
			rscFc.setSoarRank(j.has("soarRank") == true ? j.getInt("soarRank") : 0);
			list.add(rscFc);
		}
		return list;
	}

	public String[] uploadOss(List<RscHy> ssc, List<RscHy> cwc, List<RscFc> hxc, List<RscFc> xsc, String cateid) {
		// 搜索词和长尾词合并
		ssc.addAll(cwc);
		// 核心词和修饰词合并
		hxc.addAll(xsc);
		// 行业数据去重
		List<RscHy> newList = new ArrayList<RscHy>();
		for (RscHy rscHy : ssc) {
			if (!newList.contains(rscHy)) {
				newList.add(rscHy);
			}
		}
		// 分词数据去重
		List<RscFc> newListFc = new ArrayList<RscFc>();
		for (RscFc rscFc : hxc) {
			if (!newListFc.contains(rscFc)) {
				newListFc.add(rscFc);
			}
		}
		// 将搜索词加入分词
		if (FC_SSC) {
			// 将所有搜索词拼接
			StringBuilder sb = new StringBuilder();
			for (int i = 0; i < ssc.size(); i++) {
				sb.append(ssc.get(i).getSearchWord());
			}
			// 将拼接的搜索词进行分词
			StringReader reader = new StringReader(sb.toString());
			IKSegmenter ik = new IKSegmenter(reader, true);
			Lexeme lexeme = null;
			try {
				while ((lexeme = ik.next()) != null) {
					RscFc rscFc = new RscFc();
					rscFc.setSearchWord(lexeme.getLexemeText());
					if (!newListFc.contains(rscFc)) {
						newListFc.add(rscFc);
					}
				}
			} catch (Exception e) {
				e.printStackTrace();
			}

		}
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
		SimpleDateFormat sdf2 = new SimpleDateFormat("dd");
		String[] paths = new String[2];
		paths[0] = uploadToOss(getOssPath("hysj", cateid, sdf.format(new Date()), sdf2.format(new Date())),
				toHysjJson(newList));// 上传oss行业数据
		paths[1] = uploadToOss(getOssPath("fcsj", cateid, sdf.format(new Date()), sdf2.format(new Date())),
				toFcsjJson(newListFc));// 上传oss分词数据
		return paths;
	}

	public String toHysjJson(List<RscHy> hysjList) {
		StringBuilder sb = new StringBuilder();
		sb.append("callback({\"data\":[");
		if (hysjList != null && hysjList.size() > 0) {
			for (RscHy hysj : hysjList) {
				sb.append("{\"pm\":");
				sb.append(hysj.getHotSearchRank() + ",");
				sb.append("\"ssc\":");
				sb.append("\"" + hysj.getSearchWord() + "\",");
				sb.append("\"ssrq\":");
				sb.append(hysj.getSeIpvUvHits() + ",");
				sb.append("\"scdjzb\":");
				sb.append(0 + ",");
				sb.append("\"djl\":");
				sb.append(hysj.getClickRate() + ",");
				sb.append("\"djrq\":");
				sb.append(hysj.getClickHits() + ",");
				sb.append("\"cfzhl\":");
				sb.append(hysj.getPayRate() + ",");
				sb.append("\"ztcckj\":");
				sb.append(0 + ",");
				sb.append("\"xl\":");
				sb.append(hysj.getXl() + "},");
			}
			return sb.substring(0, sb.length() - 1) + "]});";
		} else {
			return sb.append("]});").toString();
		}
	}

	public String toFcsjJson(List<RscFc> fcList) {
		StringBuilder sb = new StringBuilder();
		sb.append("callback({\"data\":[");
		if (fcList != null && fcList.size() > 0) {
			for (RscFc fcsj : fcList) {
				sb.append("{\"pm\":");
				sb.append(fcsj.getHotSearchRank() + ",");
				sb.append("\"ssc\":");
				sb.append("\"" + fcsj.getSearchWord() + "\",");
				sb.append("\"ssrq\":");
				sb.append(fcsj.getAvgWordSeIpvUvHits() + ",");
				sb.append("\"xgssc\":");
				sb.append(fcsj.getRelSeWordCnt() + ",");
				sb.append("\"cjdjl\":");
				sb.append(fcsj.getAvgWordClickRate() + ",");
				sb.append("\"djrq\":");
				sb.append(fcsj.getAvgWordClickHits() + ",");
				sb.append("\"cjcfzhl\":");
				sb.append(fcsj.getAvgWordPayRate() + ",");
				sb.append("\"ztcckj\":");
				sb.append(0 + "},");
			}
			return sb.substring(0, sb.length() - 1) + "]});";
		} else {
			return sb.append("]});").toString();
		}
	}

	public String uploadToOss(String fileName, String text) {
		String path = "";
		try {
			File f = new File(config.getString("tempdir") + fileName);
			if (!f.getParentFile().exists()) {
				f.getParentFile().mkdirs();
			}
			BufferedWriter bw = null;
			try {
				bw = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(f), "utf-8"));
				bw.write(text);
				bw.flush();
			} catch (Exception e) {
				e.printStackTrace();
			} finally {
				try {
					bw.close();
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
			path = ossDao.uploadauth(f, fileName);
			f.delete();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return path;
	}

	public String getOssPath(String lx, String cateid, String yearAndMonth, String day) {
		String uuid = UUID.randomUUID().toString().replace("-", "").toLowerCase();
		return String.format("btyh/%s/%s/%s/%s/%s.json", lx, cateid, yearAndMonth, day, uuid);
	}

	/**
	 * 获取生意参谋时间范围
	 */
	public Map<String, String> getCommDate(Cjzh cjzh) {
		cjlog.log(cjzh, "commDate", "0", "zdcj8.GetRscService.getCommDate");
		
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cjzh.getCookieStore()).build();
		long t = System.currentTimeMillis();
		HttpGet httpget = new HttpGet(
				"https://sycm.taobao.com/portal/common/commDate.json?targetUrl=http%3A%2F%2Fsycm.taobao.com%2Fmc%2Fmq%2Fproduct_insight&_="
						+ t + "&token=");
		httpget.setHeader("accept", "*/*");
		httpget.setHeader("accept-encoding", "gzip, deflate, sdch, br");
		httpget.setHeader("accept-language", "zh-CN,zh;q=0.8");
		httpget.setHeader("referer", "https://sycm.taobao.com/mc/mq/product_insight");
		httpget.setHeader("user-agent",
				"Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/55.0.2883.75 Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000)
				.setConnectionRequestTimeout(20000);
		httpget.setConfig(requestConfig.build());

		HttpResponse httpresponse;
		try {
			httpresponse = httpClient.execute(httpget);
			HttpEntity entity = httpresponse.getEntity();
			String body = EntityUtils.toString(entity);

			JSONObject jsonObj = JSONObject.fromObject(body);
			if (jsonObj.has("hasError") && !jsonObj.getBoolean("hasError") && jsonObj.has("content")
					&& jsonObj.getJSONObject("content").has("data")) {
				JSONObject dataobj = jsonObj.getJSONObject("content").getJSONObject("data");
				Map<String, String> dateMap = new HashMap<String, String>();
				@SuppressWarnings("unchecked")
				Iterator<String> iter = dataobj.keys();
				while (iter.hasNext()) {
					String key = iter.next();
					String value = dataobj.getString(key);
					dateMap.put(key, value);
				}
				return dateMap;
			} else {
				log.error(body);
				cjlog.log(cjzh, "commDate", "101", body);
				return null;
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			cjlog.log(cjzh, "commDate", "101", e.getMessage());
			return null;
		} finally {
			httpget.releaseConnection();
		}
	}

	public static double formartDouble(double d) {
		DecimalFormat df = new DecimalFormat("#.00");
		return Double.parseDouble(df.format(d));
	}

	public static void main(String[] args) {
		// GetRscService rscService = new GetRscService();
		List<RscFc> newListFc = new ArrayList<RscFc>();
		String str = "盲盒收纳展示架盲盒收纳泡泡玛特展示盒盲盒展示盒手办展示架盲盒收纳盒手办展示盒盲盒收纳别墅泡泡玛特收纳盒盲盒收纳展示盒盲盒展示架亚克力展示架手办盒手办收纳盒泡泡玛特旋转盲盒展示柜公仔收纳柜popmart 展示盒盲盒架子收纳goto收纳盒泡泡马特展示盒手办收纳盲盒展示盲盒架盲盒柜泡泡玛特展示柜盲盒架子popmart收纳盒盲盒收纳盒单个亚克力盲盒展示架摩天轮泡泡玛特收纳泡泡玛特别墅盲盒收纳柜娃柜泡泡玛特挂墙盲盒墙盲盒收纳挂墙盲盒防尘罩盲盒收纳架盲盒架子收纳架popmart展示盒盲盒收纳罐盲盒收纳展示手办收纳展示柜手办展示盲盒相框盲盒收纳展示架单个茉莉的一天盲盒收纳展示柜娃娃收纳盒盲盒收纳展示架 挂墙式泡泡玛特架子 展示架防尘盒泡泡玛特展示泡泡玛特收纳架盲盒收纳展示架挂墙烧脑拼图防尘罩透明展示盒娃娃展示柜molly的一天系列泡泡玛特展示架亚加丽加盲盒 收纳盲盒盒子收纳盲盒收纳展示架防尘盒盲盒柜子泡泡玛特架子泡泡玛特盒子盲盒整理盒冰鹏旗舰店卓越塑胶制品厂模型收纳盒盲盒置物架亚克力收纳盒手办盲盒收纳展示架场景盲盒收纳展示架 壁挂式娃盒盲盒展示盒单个亚克力手办展示盒盲盒展示罐盲盒盒子泡泡玛特展示盒 单个盲盒亚克力展示架展示柜盲盒盲盒柜展示泡泡玛特收纳柜盲盒收纳盒别墅popmart泡泡玛特 展示盒娃娃展示架盲盒收纳展示架 可旋转盲盒别墅收纳盒盲盒盲盒收纳场景齐天大圣手办盲盒收纳展示架柜子泡泡玛特盒子收纳盒盲盒收纳单个盲盒架子挂墙泡泡玛特亚克力展示盒亚克力展示盲盒亚克力盲盒娃娃收纳盒收纳展示盒盲盒防尘盲盒收纳墙molly收纳盲盒收纳展示架 木质盲盒 展示盒盲盒展示场景收纳盲盒盲盒 展示娃娃展示盒盲盒别墅展示公仔展示盒放盲盒的架子乐高陈列架盲盒防尘盒手办收纳架公仔收纳盒亚克力盲盒盲盒收纳展示架带灯公仔柜盲盒收纳袋无匠旗舰店公仔收纳盲盒收纳盒 透明手办展示架 亚克力泡泡玛特收纳盒单个盲盒摆放架娃娃收纳架泡泡玛特防尘罩盲盒收藏盒展示盒盲盒展示盒 手办盲盒单个收纳唯兮旗舰店泡泡玛特收纳展示柜goto盲盒泡泡玛特展示盒场景不倒胶盲盒柜子挂墙手办收纳盒展示盒盲盒展示房子盲盒不倒胶蚂蚁动漫店盲盒收纳 单个盲盒收纳旋转放盲盒的架子 收纳亚克力架子泡泡玛特隐藏款娃娃收纳柜娃娃展示娃娃盒盲盒收纳展示盒单个盲盒收纳展示架 防尘摆件盒子透明 展示盒冰鹏盲盒旋转展示泡泡玛特展示盒 挂墙单个盲盒收纳娃收纳乐高收纳柜 展示盒盲盒阶梯放盲盒的盒子泡泡玛特盒装盲盒的盒子 收纳盒盲盒收纳展示架 置物盲盒收纳瓶goto洗护旗舰店泡泡玛特柜子公仔展示架kaman家居泡泡马特收纳盒玩偶收纳盒模型盒子盲盒防尘罩单个手办不倒胶盲盒陈列架泡泡玛特 收纳娃娃不倒胶泡泡玛特展示包泡泡玛特收纳罐手办 收纳盲盒展示墙火影手办盲盒盲盒粘胶公仔盒透明展示架肓盒收纳展示架娃娃收纳盒透明盲盒收纳屋娃娃架子盲盒背景纸盲盒收藏柜亚克力盲盒收纳盒乐高收纳架亚克力 展示盒固得呀盲盒透明展示盒手办 展示手办盲盒收纳唯兮泡泡玛特亚克力收纳盒盲盒脚贴亚克力 展示架亚克力小盒子盲盒透视马特手办展示罩扭蛋收纳盒泡泡马特展示架goto展示盒泡泡马特官方旗舰店娃架泡泡玛特房间泡泡玛特展示墙尖彩旗舰店盲盒娃娃展示盒扭蛋展示盒盲盒展示台popmart展示架盒柜盲盒 泡泡玛特透明摆设展示盒展示架盲盒盲盒收纳亚克力盲盒挂墙盲盒展示罩盲盒收纳阶梯泡泡马克手办展示柜分层架公仔展示柜木质展示盒泡泡玛特玻璃罩亚加力加收纳展示玩偶收纳架展示盒木质盲盒 亚克力鹿口福旗舰店盲盒亚克力收纳盒冒盒桌面娃娃盲盒 展示架泡泡玛特置物架放盲盒展示柜泡泡玛特收纳盒挂墙玩偶柜盲盒箱子泡泡玛特展示罐亚克力 展示泡泡玛特晒娃包展示架亚克力创意电视娃娃屋泡泡玛特娃包亚克力手办展示架盲盒展示屋亚加丽加收纳盒盲盒收纳柜展示柜公仔展示装盲盒的空盒子冰鹏亚克力泡泡玛特收纳盒盲盒展示架透明手办防尘盒展示柜可叠加泡泡玛特隐藏盲盒阶梯架molly展示架盲盒收藏架娃娃盒子收纳玻璃收纳盒 有盖盲盒展柜展示盒透明玻璃罩盲盒防尘展示柜泡泡玛特盲盒收纳展示架娃娃收纳架展示盲盒收纳箱高透明亚克力展示盒盲盒收纳手办盒子防尘罩积木带灯乐高收纳盒80012收纳盒娃娃盒子透明泡泡玛特防尘盒盲盒架子阶梯亚克力手办展示柜娃娃盲盒泡泡玛特茉莉高达手办展示墙手办收纳盒单个盲盒展示盒挂墙盲盒展示包茫盒盲盒 盒子展示箱 透明盲盒收纳展示架墙壁盲盒罩子 透明手办防尘盒放娃娃的盒子装盲盒的玻璃盒泡泡马特展示柜手办展示箱盲盒摆台防尘罩盲盒可叠加亚克力泡泡玛特收纳盒盲盒收纳展示架透明手办防尘盒展示柜盲盒收纳展示架盲盒收纳泡泡玛特展示盒盲盒展示盒盲盒收纳盒盲盒收纳别墅泡泡玛特收纳盒盲盒收纳展示盒盲盒展示架泡泡玛特旋转盲盒展示柜盲盒架子收纳泡泡马特展示盒盲盒展示盲盒架盲盒柜泡泡玛特展示柜盲盒架子盲盒收纳盒单个亚克力盲盒展示架泡泡玛特收纳泡泡玛特别墅盲盒收纳柜泡泡玛特挂墙盲盒墙盲盒收纳挂墙盲盒防尘罩盲盒收纳架盲盒架子收纳架盲盒收纳罐盲盒收纳展示手办收纳展示柜盲盒相框盲盒收纳展示架单个茉莉的一天盲盒收纳展示柜盲盒收纳展示架 挂墙式泡泡玛特架子 展示架泡泡玛特展示泡泡玛特收纳架盲盒收纳展示架挂墙烧脑拼图防尘罩透明展示盒molly的一天系列泡泡玛特展示架盲盒 收纳盲盒收纳展示架防尘盒盲盒盒子收纳盲盒柜子泡泡玛特盒子泡泡玛特架子盲盒整理盒卓越塑胶制品厂冰鹏旗舰店盲盒置物架亚克力收纳盒手办盲盒收纳展示架场景盲盒收纳展示架 壁挂式盲盒展示盒单个亚克力手办展示盒盲盒盒子盲盒展示罐泡泡玛特展示盒 单个盲盒亚克力展示架展示柜盲盒盲盒柜展示泡泡玛特收纳柜盲盒收纳盒别墅popmart泡泡玛特 展示盒盲盒收纳展示架 可旋转盲盒别墅收纳盒盲盒盲盒收纳场景盲盒收纳展示架柜子泡泡玛特盒子收纳盒盲盒收纳单个盲盒架子挂墙泡泡玛特亚克力展示盒盲盒亚克力盲盒娃娃收纳盒盲盒防尘盲盒收纳墙盲盒收纳展示架 木质盲盒 展示盒盲盒展示场景收纳盲盒盲盒 展示盲盒别墅展示放盲盒的架子盲盒防尘盒盲盒收纳展示架带灯亚克力盲盒无匠旗舰店盲盒收纳袋盲盒收纳盒 透明手办展示架 亚克力泡泡玛特收纳盒单个盲盒摆放架泡泡玛特防尘罩盲盒收藏盒展示盒盲盒盲盒单个收纳唯兮旗舰店泡泡玛特收纳展示柜泡泡玛特展示盒场景goto盲盒不倒胶盲盒柜子挂墙手办收纳盒展示盒盲盒不倒胶盲盒展示房子盲盒收纳 单个盲盒收纳旋转放盲盒的架子 收纳泡泡玛特隐藏款盲盒收纳展示架 防尘盲盒收纳展示盒单个摆件盒子透明 展示盒盲盒旋转展示泡泡玛特展示盒 挂墙单个盲盒收纳盲盒阶梯放盲盒的盒子乐高收纳柜 展示盒盲盒收纳展示架 置物泡泡玛特盒装盲盒的盒子 收纳盒盲盒收纳瓶goto洗护旗舰店泡泡玛特柜子泡泡马特收纳盒盲盒防尘罩单个手办不倒胶盲盒陈列架泡泡玛特 收纳泡泡玛特展示包娃娃不倒胶泡泡玛特收纳罐盲盒展示墙火影手办盲盒盲盒粘胶肓盒收纳展示架盲盒收纳屋娃娃收纳盒透明盲盒背景纸亚克力盲盒收纳盒盲盒收藏柜盲盒透明展示盒泡泡玛特亚克力收纳盒手办盲盒收纳盲盒脚贴亚克力小盒子盲盒透视泡泡马特展示架泡泡马特官方旗舰店尖彩旗舰店泡泡玛特房间泡泡玛特展示墙盲盒娃娃展示盒盲盒展示台展示架盲盒盲盒 泡泡玛特透明摆设展示盒盲盒收纳亚克力盲盒挂墙盲盒展示罩手办展示柜分层架盲盒收纳阶梯泡泡玛特玻璃罩盲盒 亚克力鹿口福旗舰店盲盒亚克力收纳盒放盲盒展示柜泡泡玛特收纳盒挂墙泡泡玛特置物架盲盒 展示架盲盒箱子泡泡玛特展示罐泡泡玛特晒娃包亚克力手办展示架创意电视娃娃屋泡泡玛特娃包泡泡玛特隐藏盲盒收纳柜展示柜盲盒展示屋装盲盒的空盒子冰鹏亚克力泡泡玛特收纳盒盲盒展示架透明手办防尘盒展示柜可叠加盲盒阶梯架盲盒防尘展示柜盲盒收藏架泡泡玛特盲盒收纳展示架娃娃收纳架展示玻璃收纳盒 有盖盲盒展柜展示盒娃娃盒子收纳盲盒收纳箱高透明亚克力展示盒盲盒收纳手办盒子防尘罩积木带灯乐高收纳盒娃娃盲盒泡泡玛特盲盒架子阶梯手办收纳盒单个亚克力手办展示柜娃娃盒子透明泡泡玛特防尘盒盲盒展示盒挂墙盲盒 盒子盲盒展示包盲盒收纳展示架墙壁盲盒罩子 透明可叠加亚克力泡泡玛特收纳盒盲盒收纳展示架透明手办防尘盒展示柜放娃娃的盒子泡泡马特展示柜防尘罩盲盒装盲盒的玻璃盒盲盒摆台泡泡玛特旗舰店展示架盲盒柜收纳泡泡玛特盒子收纳朵芮旗舰店若来盲盒收纳展示架泡泡玛特收纳盒透明展示盒盲盒收纳展示架亚克力手办展示盒 开门式芭比娃娃收纳架展示手伴收纳盒手办收纳盒 大号芷初生活展示柜 盲盒盲盒 架泡泡玛特展示盒亚克力毕奇收纳盒亚克力桌面收纳盲盒收纳大号泡泡玛特展示盒单个娃娃公仔收纳柜泡泡玛特收纳袋手办展示罐毛公仔收纳展示架盲盒陈列goto盲盒收纳pe膜盲盒收纳蛮盒收纳盒手办展示架 收纳盒乐高架子收纳桌面手办架盲盒盒子收纳单个盲盒展示盒透明泡泡玛特亚克力盲盒收纳展示架 单个亚克力展示盒挂墙goto手办收纳盒盲盒单个展示盒手办收纳盒透明盲盒收纳盒大收纳盒 盲盒盲盒收纳展示架子手办泡泡玛特娃娃公仔防尘透明亚克力popmart盒亚克力盲盒收纳盲盒摆架装手办的盒子 透明哈利波特盲盒收纳展示架毛绒娃娃收纳压克力透明板展示盒1泡泡玛特手办 还有$9poocthlcki亚克力收纳盒盲盒手办架子桌面肓盒泡泡玛特放娃娃的架子泡泡玛特墙盲盒架子收纳挂墙手办展示盒 亚克力盲盒收纳展示架公仔防尘 亚克力单个娃娃popmart泡泡玛特收纳别墅超大泡泡玛特装盲盒的盒子玻璃礼盒 透明高端盲盒支架盲盒箱装芭比娃娃的收纳盒泡泡玛特明盒优袋旗舰店防尘柜子手办盲盒展示梯盲盒收纳展示架 异形肓盒收纳盒娃娃收纳盒20cm 透明盲盒改造透明盲盒泡泡玛特展示架挂墙泡泡玛特背景展示盒悟空小侠收纳毛绒公仔收纳盒嘟嘴娃娃molly盲盒展柜盲盒展示盒亚克力旋转盲盒收纳盒立牌收纳盒防尘盲盒装饰场景布置泡泡玛特亚克力展示盒手办泡泡玛特溜娃包亚克力阶梯展示盒网红亡盒育盒手办";
		StringReader reader = new StringReader(str);
		Lexeme lexeme = null;
		IKSegmenter ik = new IKSegmenter(reader, true);
		try {
			while ((lexeme = ik.next()) != null) {
				RscFc rscFc = new RscFc();
				rscFc.setSearchWord(lexeme.getLexemeText());
				if (!newListFc.contains(rscFc)) {
					System.out.println(rscFc.getSearchWord());
					newListFc.add(rscFc);
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
}
