package com.abujlb.zdcj8.service;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.abujlb.czs.Cate;
import com.abujlb.czs.Czs;
import com.abujlb.tyjk.GetItemInfo;
import com.abujlb.tyjk.bean.ItemInfo;
import com.abujlb.util.StringUtil;
import com.abujlb.zdcj8.tsdao.ScphSpAllTsDao;

@Component
public class CateBaobei {
	private static Logger log = Logger.getLogger(CateBaobei.class);
	@Autowired
    private GetItemInfo getItemInfo;
	@Autowired
	private Czs czs;
	@Autowired
	private ScphSpAllTsDao scphSpAllTsDao;

	public Cate getLm(String bbid) {
		try {
			Cate cate = null;
			String lmid = null;
			ItemInfo itemInfo = getItemInfo.getDetailInfo(bbid);
			if (itemInfo == null || StringUtil.isNull2(itemInfo.getCateid())) {
				lmid = scphSpAllTsDao.getRowCateid(bbid); // 表格存储abujlb3.scph_sp_all 拿cateid
			} else {
				lmid = itemInfo.getCateid();
			}
			if (lmid == null) {
				return null;
			}
			// 2：调用查指数 获取类目信息
			cate = czs.getCate(lmid);
			if (cate == null) {
				return null;
			}
			return cate;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return null;
		}
	}

}
