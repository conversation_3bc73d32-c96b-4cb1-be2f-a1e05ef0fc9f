package com.abujlb.zdcj8.service;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.abujlb.Result;
import com.abujlb.zdcj8.bean.DpsxPara;
import com.abujlb.zdcj8.bean.ali.ZhAli;
import com.abujlb.zdcj8.constant.AliConst;
import com.abujlb.zdcj8.http.ali.AliHttpClient;
import com.abujlb.zdcj8.http.ali.AliUploader;
import com.abujlb.zdcj8.util.StrUtil;

/**
 * 阿里service
 * 
 * <AUTHOR>
 * @date 2021-03-19 15:02:50
 */
@Component
public class AliService {

	private static Logger log = Logger.getLogger(AliService.class);

	@Autowired
	private AliUploader uploader;
	@Autowired
	private AliHttpClient httpClient;

	/**
	 * 接口：店铺上新
	 * 
	 * @return json
	 */
	public String dpsx(DpsxPara dpsxPara) {
		Result result = new Result(2, "未知错误!");
		ZhAli zhAli = uploader.next();
		if (zhAli == null) {
			result.setCodeContent(Result.DBERROR, "暂无账号可用！");
			log.info(System.currentTimeMillis() + "，暂无账号可用！");
			return result.toString();
		}
		String _m_h5_tk = "";
		httpClient.getH5TkForUser(zhAli.getCookieStore());
		_m_h5_tk = httpClient.getH5tkByCookieStore(zhAli.getCookieStore(), AliConst.CK_DOMAIN_TAOBAO);
		if (StrUtil.isNull(_m_h5_tk)) {
			_m_h5_tk = httpClient.getH5tkByCookieStore(zhAli.getCookieStore(), AliConst.CK_DOMAIN_PRE + AliConst.CK_DOMAIN_TAOBAO);
		}
		if (StrUtil.isNull(_m_h5_tk)) {
			result.setCodeContent(Result.DBERROR, "获取_m_h5_tk失败！");
		} else {
			result = httpClient.dpsx(dpsxPara, zhAli.getCookieStore(), _m_h5_tk);
		}
		// uploader.finish(zhAli, true);
		return result.toString();
	}
	
	/**
	 * 接口：宝贝详情
	 * 
	 * @说明：这个接口因为没有对应的网页，研究不清，不知如何提高准确率
	 * @return json
	 */
	public String bbxq(String itemid) {
		Result result = new Result(2, "未知错误!");
		ZhAli zhAli = uploader.next();
		if (zhAli == null) {
			result.setCodeContent(Result.DBERROR, "暂无账号可用！");
			log.info(System.currentTimeMillis() + "，暂无账号可用！");
			return result.toString();
		}
		result = httpClient.bbxq(itemid);
		// uploader.finish(zhAli, true);
		return result.toString();
	}
	
	/**
	 * 接口：淘客订单查询
	 * 
	 * @return json
	 */
	public String tkdd(String orderid) {
		Result result = new Result(2, "未知错误!");
		ZhAli zhAli = uploader.next();
		if (zhAli == null) {
			result.setCodeContent(Result.DBERROR, "暂无账号可用！");
			log.info(System.currentTimeMillis() + "，暂无账号可用！");
			return result.toString();
		}
		String 	_m_h5_tk = httpClient.getH5tkByCookieStore(zhAli.getCookieStore(), AliConst.CK_DOMAIN_TAOBAO); // domain是taobao.com的_m_h5_tk非常重要
		if (StrUtil.isNull(_m_h5_tk)) {
			result.setCodeContent(Result.DBERROR, "获取_m_h5_tk失败！");
			// uploader.finish(zhAli, false);
		} else {
			result = httpClient.tkdd(zhAli.getCookieStore(), _m_h5_tk, orderid);
			if (result != null && result.isSuccess()) {
				// uploader.finish(zhAli, true);
			} else {
				// uploader.finish(zhAli, false);
			}
		}
		return result.toString();
	}
	
}
