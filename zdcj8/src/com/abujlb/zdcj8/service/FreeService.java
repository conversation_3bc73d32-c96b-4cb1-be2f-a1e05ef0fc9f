package com.abujlb.zdcj8.service;

import org.apache.http.client.CookieStore;
import org.apache.http.impl.client.BasicCookieStore;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.abujlb.Result;
import com.abujlb.gson.Json2Object;
import com.abujlb.zdcj8.bean.DpsxPara;
import com.abujlb.zdcj8.bean.ZhSjs;
import com.abujlb.zdcj8.bean.sjs.JpggxsblInput;
import com.abujlb.zdcj8.bean.sjs.Mjx;
import com.abujlb.zdcj8.bean.sjs.MjxInput;
import com.abujlb.zdcj8.bean.sjs.MjxQdxzInput;
import com.abujlb.zdcj8.http.FreeHttpClient;
import com.abujlb.zdcj8.http.SjsHttpClient;
import com.abujlb.zdcj8.http.SjsUploader;
import com.abujlb.zdcj8.script.Md5SjsJSEngine;
import com.abujlb.zdcj8.util.StrUtil;

/**
 * 达官电商，免费工具service
 * 
 * <AUTHOR>
 * @date 2020-11-05 09:50:16
 */
@Component("freeService")
public class FreeService {

	private static Logger log = Logger.getLogger(FreeService.class);

	@Autowired
	private FreeHttpClient freeHttpClient;
	@Autowired
	private SjsUploader sjsUploader;
	@Autowired
	private SjsHttpClient sjsHttpClient;
	@Autowired
	private Md5SjsJSEngine md5SjsJSEngine;

	/**
	 * 获取：店铺上新
	 * 
	 * @param userid 店铺的userid
	 * @return result结果
	 */
	public Result dpsx(DpsxPara dpsxPara) {
		Result result = new Result(101, "参数错误！");
		if (StrUtil.isNull(dpsxPara.getUserId())) {
			return result;
		}
		CookieStore cookieStore = new BasicCookieStore();
		Result resultTemp = freeHttpClient.getH5Tk(cookieStore, dpsxPara.getUserId());
		if (resultTemp == null || !resultTemp.isSuccess()) {
			result.setCodeContent(2, "获取h5_tk失败！");
			return result;
		}
		String _m_h5_tk = (String) resultTemp.getKey("_m_h5_tk");
		result = freeHttpClient.dpsx(dpsxPara, cookieStore, _m_h5_tk);
		return result;
	}

	/**
	 * 获取：（数据蛇）店铺上新2
	 * 
	 * @param nick 店铺旺旺号
	 * @return result结果
	 */
	public Result dpsx2(String nick) {
		Result result = new Result(101, "参数错误！");
		if (StrUtil.isNull(nick)) {
			return result;
		}
		ZhSjs zhSjs = sjsUploader.next();
		if (zhSjs == null) {
			result.setCodeContent(101, "暂无可用的数据蛇账号！");
			log.info(System.currentTimeMillis() + "，暂无可用的数据蛇账号！");
			return result;
		}
		/*********************************************
		 * 扣费
		 *********************************************/
		String currentTime = String.valueOf(System.currentTimeMillis()); // 毫秒
		String key = md5SjsJSEngine.getKey(currentTime, zhSjs.getUserId());
		Result resultTemp = sjsHttpClient.dpsxFree(zhSjs, nick, currentTime, key);
		if (resultTemp == null || !resultTemp.isSuccess()) {
			sjsUploader.finish(zhSjs, false);
			result.setCodeContent(2, "店铺上新扣费接口失败！");
			return result;
		}
		/*********************************************
		 * 监控数据
		 *********************************************/
		resultTemp = sjsHttpClient.dpsxStatus(zhSjs, nick, currentTime, key);
		if (resultTemp == null || !resultTemp.isSuccess()) {
			sjsUploader.finish(zhSjs, false);
			result.setCodeContent(2, "店铺上新状态查询接口失败！");
			return result;
		}
		/*********************************************
		 * 获取数据
		 *********************************************/
		String uuid = (String) resultTemp.getKey("uuid");
		result = sjsHttpClient.dpsx(zhSjs, uuid, currentTime, key);
		if (result != null && result.isSuccess()) {
			sjsUploader.finish(zhSjs, true);
		} else {
			sjsUploader.finish(zhSjs, false);
		}
		return result;
	}

	/**
	 * 获取：（数据蛇）旺旺查询
	 * 
	 * @param wwname 旺旺名称
	 * @return result结果
	 */
	public Result wwcx(String wwname) {
		Result result = new Result(101, "参数错误！");
		if (StrUtil.isNull(wwname)) {
			return result;
		}
		ZhSjs zhSjs = sjsUploader.next();
		if (zhSjs == null) {
			result.setCodeContent(101, "暂无可用的数据蛇账号！");
			log.info(System.currentTimeMillis() + "，暂无可用的数据蛇账号！");
			return result;
		}
		/*********************************************
		 * 扣费
		 *********************************************/
		String currentTime = String.valueOf(System.currentTimeMillis()); // 毫秒
		String key = md5SjsJSEngine.getKey(currentTime, zhSjs.getUserId());
		Result resultTemp = sjsHttpClient.wwcxFree(zhSjs, currentTime, key);
		if (resultTemp == null || !resultTemp.isSuccess()) {
			sjsUploader.finish(zhSjs, false);
			result.setCodeContent(2, "旺旺查询扣费接口失败！");
			return result;
		}
		/*********************************************
		 * 获取数据
		 *********************************************/
		result = sjsHttpClient.wwcx(zhSjs, wwname, currentTime, key);
		if (result != null && result.isSuccess()) {
			sjsUploader.finish(zhSjs, true);
		} else {
			sjsUploader.finish(zhSjs, false);
		}
		return result;
	}
	
	/**
	 * 获取：（数据蛇）旺旺会员查询
	 * 
	 * @param wwname 旺旺名称
	 * @return result结果
	 */
	public Result wwvip(String wwname) {
		Result result = new Result(101, "参数错误！");
		if (StrUtil.isNull(wwname)) {
			return result;
		}
		ZhSjs zhSjs = sjsUploader.next();
		if (zhSjs == null) {
			result.setCodeContent(101, "暂无可用的数据蛇账号！");
			log.info(System.currentTimeMillis() + "，暂无可用的数据蛇账号！");
			return result;
		}
		String currentTime = String.valueOf(System.currentTimeMillis()); // 毫秒
		String key = md5SjsJSEngine.getKey(currentTime, zhSjs.getUserId());
		/********************************************** 获取数据 *********************************************/
		result = sjsHttpClient.wwvip(zhSjs, wwname, currentTime, key);
		if (result != null && result.isSuccess()) {
			sjsUploader.finish(zhSjs, true);
		} else {
			sjsUploader.finish(zhSjs, false);
		}
		if (result == null) {
			result = new Result(101, "未知错误！");
		}
		return result;
	}

	/**
	 * 获取：（数据蛇）淘客订单查询
	 * 
	 * @param orderIds 订单号s
	 * @return result结果
	 */
	public Result tkdd(String orderIds) {
		Result result = new Result(101, "参数错误！");
		if (StrUtil.isNull(orderIds)) {
			return result;
		}
		ZhSjs zhSjs = sjsUploader.next();
		if (zhSjs == null) {
			result.setCodeContent(101, "暂无可用的数据蛇账号！");
			log.info(System.currentTimeMillis() + "，暂无可用的数据蛇账号！");
			return result;
		}
		/*********************************************
		 * 扣费
		 *********************************************/
		String currentTime = String.valueOf(System.currentTimeMillis()); // 毫秒
		String key = md5SjsJSEngine.getKey(currentTime, zhSjs.getUserId());
		Result resultTemp = sjsHttpClient.tkddFree(zhSjs, orderIds, currentTime, key);
		if (resultTemp == null || !resultTemp.isSuccess()) {
			sjsUploader.finish(zhSjs, false);
			result.setCodeContent(2, "淘客订单扣费接口失败！");
			return result;
		}
		String uuid = (String) resultTemp.getKey("uuid");
		/*********************************************
		 * 获取数据
		 *********************************************/
		result = sjsHttpClient.tkdd(zhSjs, uuid, currentTime, key);
		if (result != null && result.isSuccess()) {
			sjsUploader.finish(zhSjs, true);
		} else {
			sjsUploader.finish(zhSjs, false);
		}
		return result;
	}

	/**
	 * 无线详情
	 * 
	 * @param bbid
	 * @return
	 */
	public Result wxxq(String bbid) {
		Result result = new Result(101, "参数错误！");
		String format = "{\"id\":\"%s\",\"type\":\"0\",\"f\":\"\"}";
		String json = String.format(format, bbid);
		Result resultTemp = freeHttpClient.wxxq(json);
		if (resultTemp == null || !resultTemp.isSuccess()) {
			result.setCodeContent(101, "接口错误！");
			return result;
		}
		result.setCodeContent(Result.SUCCESS, "成功");
		result.putKey("records", resultTemp.getKey("list"));
		return result;
	}

	/**
	 * 竞品规格销售比例
	 * 
	 * @param bbid
	 * @return
	 */
	public Result jpggxsbl(String bbid) {
		Result result = new Result(101, "参数错误！");
		/*********************************************
		 * 一、获取数据蛇账号
		 *********************************************/
		ZhSjs zhSjs = sjsUploader.next();
		if (zhSjs == null) {
			result.setCodeContent(101, "暂无可用的数据蛇账号！");
			log.info(System.currentTimeMillis() + "，暂无可用的数据蛇账号！");
			return result;
		}

		/*********************************************
		 * 二、保存竞品规格销售比例接口
		 *********************************************/
		String timestamp = System.currentTimeMillis() + "";
		String keyMd = md5SjsJSEngine.getKey(timestamp, zhSjs.getUserId());
		Result resultTemp = sjsHttpClient.save_jpggxsbl(zhSjs, bbid, 1, "", timestamp, keyMd);
		if (resultTemp == null || !resultTemp.isSuccess()) {
			result.setCodeContent(101, "接口错误！");
			sjsUploader.finish(zhSjs, false);
			return result;
		}

		/*********************************************
		 * 三、查询竞品规格销售比例接口
		 *********************************************/
		resultTemp = sjsHttpClient.list_jpggxsbl(zhSjs, timestamp, keyMd);
		if (resultTemp == null || !resultTemp.isSuccess()) {
			result.setCodeContent(101, "接口错误！");
			sjsUploader.finish(zhSjs, false);
			return result;
		}
		sjsUploader.finish(zhSjs, true);
		result.setCodeContent(Result.SUCCESS, "成功!");
		result.putKey("record", resultTemp.getKey("record"));
		return result;
	}

	/**
	 * 竞品规格销售比例详情
	 * 
	 * @param data
	 * @return
	 */
	public Result jpggxsblxq(String data) {
		Result result = new Result(101, "参数错误！");
		/*********************************************
		 * 一、获取数据蛇账号
		 *********************************************/
		ZhSjs zhSjs = sjsUploader.next();
		if (zhSjs == null) {
			result.setCodeContent(101, "暂无可用的数据蛇账号！");
			log.info(System.currentTimeMillis() + "，暂无可用的数据蛇账号！");
			return result;
		}

		JpggxsblInput input = Json2Object.parseObj(data, JpggxsblInput.class);
		// 如果大于5页，则置为5
		if (input.getNum() > 5) {
			input.setNum(5);
		}

		/*********************************************
		 * 二、保存竞品规格销售比例接口
		 *********************************************/
		String timestamp = System.currentTimeMillis() + "";
		String keyMd = md5SjsJSEngine.getKey(timestamp, zhSjs.getUserId());
		if (input.getNum() != 1) {
			Result resultTemp = sjsHttpClient.save_jpggxsbl(zhSjs, input.getNid(), input.getNum(), input.getId(),
					timestamp, keyMd);
			if (resultTemp == null || !resultTemp.isSuccess()) {
				result.setCodeContent(101, "接口错误！");
				sjsUploader.finish(zhSjs, false);
				return result;
			}
		}

		/*********************************************
		 * 三、查询竞品规格销售比例详情数据接口
		 *********************************************/
		Result resultTemp = sjsHttpClient.xq_jpggxsbl(zhSjs, input, timestamp, keyMd);
		if (resultTemp == null || !resultTemp.isSuccess()) {
			result.setCodeContent(101, "接口错误！");
			sjsUploader.finish(zhSjs, false);
			return result;
		}

		sjsUploader.finish(zhSjs, true);
		result.setCodeContent(Result.SUCCESS, "成功!");
		result.putKey("records", resultTemp.getKey("records"));
		return result;
	}
	
	/**
	 * 买家秀印象
	 * 
	 * @param bbid
	 * @return
	 */
	public Result mjxyx(String bbid) {
		Result result = new Result(101, "参数错误！");
		/*********************************************
		 * 一、获取数据蛇账号
		 *********************************************/
		ZhSjs zhSjs = sjsUploader.next();
		if (zhSjs == null) {
			result.setCodeContent(101, "暂无可用的数据蛇账号！");
			log.info(System.currentTimeMillis() + "，暂无可用的数据蛇账号！");
			return result;
		}

		/*********************************************
		 * 二、查询宝贝买家秀印象
		 *********************************************/
		String timestamp = System.currentTimeMillis() + "";
		String keyMd = md5SjsJSEngine.getKey(timestamp, zhSjs.getUserId());
		Result resultTemp = sjsHttpClient.mjxyx(zhSjs, bbid,timestamp,keyMd);
		if (resultTemp == null || !resultTemp.isSuccess()) {
			result.setCodeContent(101, "接口错误！");
			sjsUploader.finish(zhSjs, false);
			return result;
		}
		sjsUploader.finish(zhSjs, true);
		return resultTemp;
	}

	/**
	 * 买家秀点击查看
	 * 
	 * @param data
	 * @return
	 */
	public Result mjx(String data) {
		Result result = new Result(101, "参数错误！");
		/*********************************************
		 * 一、获取数据蛇账号
		 *********************************************/
		ZhSjs zhSjs = sjsUploader.next();
		if (zhSjs == null) {
			result.setCodeContent(101, "暂无可用的数据蛇账号！");
			log.info(System.currentTimeMillis() + "，暂无可用的数据蛇账号！");
			return result;
		}

		MjxInput input = Json2Object.parseObj(data, MjxInput.class);
		input.check();
		/*********************************************
		 * 二、查询宝贝买家秀
		 *********************************************/
		String timestamp = System.currentTimeMillis() + "";
		String keyMd = md5SjsJSEngine.getKey(timestamp, zhSjs.getUserId());
		Result resultTemp = sjsHttpClient.mjx(zhSjs, input,timestamp,keyMd);
		if (resultTemp == null || !resultTemp.isSuccess()) {
			result.setCodeContent(101, "接口错误！");
			sjsUploader.finish(zhSjs, false);
			return result;
		}

		sjsUploader.finish(zhSjs, true);
		Mjx mjx = (Mjx) resultTemp.getKey("result");
		result.setCodeContent(Result.SUCCESS, "成功");
		if (mjx == null) {
			return result;
		}
		result.putKey("record", mjx.toString());
		return result;
	}


	/**
	 * 买家秀确定下载
	 * 
	 * @param data
	 * @return
	 */
	public Result mjx_qdxz(String data) {
		Result result = new Result(101, "参数错误！");
		/*********************************************
		 * 一、获取数据蛇账号
		 *********************************************/
		ZhSjs zhSjs = sjsUploader.next();
		if (zhSjs == null) {
			result.setCodeContent(101, "暂无可用的数据蛇账号！");
			log.info(System.currentTimeMillis() + "，暂无可用的数据蛇账号！");
			return result;
		}

		MjxQdxzInput input = Json2Object.parseObj(data, MjxQdxzInput.class);
		input.check();
		/*********************************************
		 * 二、确定下载
		 *********************************************/
		String timestamp = System.currentTimeMillis() + "";
		String keyMd = md5SjsJSEngine.getKey(timestamp, zhSjs.getUserId());
		Result resultTemp = sjsHttpClient.mjxqdxz(zhSjs, input,timestamp,keyMd);
		if (resultTemp == null || !resultTemp.isSuccess()) {
			result.setCodeContent(101, "接口错误！");
			sjsUploader.finish(zhSjs, false);
			return result;
		}
		/*********************************************
		 * 三、查询宝贝买家秀记录
		 *********************************************/
		resultTemp = sjsHttpClient.mjxJl(zhSjs,timestamp,keyMd);
		if (resultTemp == null || !resultTemp.isSuccess()) {
			result.setCodeContent(101, "接口错误！");
			sjsUploader.finish(zhSjs, false);
			return result;
		}
		sjsUploader.finish(zhSjs, true);
		result.setCodeContent(Result.SUCCESS, "成功");
		result.putKey("pid", resultTemp.getKey("pid"));
		return result;
	}
}
