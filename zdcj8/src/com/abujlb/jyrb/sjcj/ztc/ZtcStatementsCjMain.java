package com.abujlb.jyrb.sjcj.ztc;

import com.abujlb.ckstore.Cjzh;
import com.abujlb.dao.OssDao;
import com.abujlb.jyrb.bean.Dp;
import com.abujlb.jyrb.bean.JysjRq;
import com.abujlb.jyrb.http.ZtcStateHttpClient;
import com.abujlb.jyrb.service.DataUploader;
import com.abujlb.jyrb.sjcj.ztc.bean.ReportResult;
import com.abujlb.jyrb.sjcj.ztc.bean.ZtcReport;
import com.abujlb.jyrb.util.BiDateUtil;
import com.abujlb.jyrb.util.JysjCjsjd;
import com.abujlb.jyrb.util.ZipUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 直通车数据采集<p/>
 *
 * <AUTHOR>
 * @date 2023/5/26
 */
@Component
public class ZtcStatementsCjMain {
    private static final String[] tgfx_file_suffix = {"计划", "单元", "人群", "关键词", "创意"};
    final Logger LOG = Logger.getLogger(this.getClass());
    @Autowired
    private DataUploader dataUploader;
    @Autowired
    private OssDao ossDao;

    /**
     * <ul>
     * <li>采集页面地址：https://subway.simba.taobao.com/#!/report/bpreport/index</li>
     * <li>采集页面描述：直通车-->报表-->账户报表-->按钮：下载报表</li>
     * <li>采集方式：下载报表</li>
     * <li>采集选项：
     *  <ul>
     *          <li>报表名称：bi_ztc_xxx</li>
     *          <li>时间选择：填入开始时间和结束时间</li>
     *          <li>数据维度：分天</li>
     *          <li>数据内容：单元</li>
     *          <li>转化周期：15天累计</li>
     *  </ul>
     *  </li>
     * <li>采集单天和月</li>
     * </ul>
     *
     * @return
     */
    public void cj(Cjzh cjzh, Dp dp) {
        try {
            if (!JysjCjsjd.inCjsjd()) {
                // 不在有效采集时间段
                return;
            }

            boolean result = ZtcStateHttpClient.ztc(cjzh);
            if (!result) {
                return;
            }
            // 调用服务器端接口，判断最后采集成功日期是否是当天
            List<String> rqList = getAllDate(dp);
            if (CollectionUtils.isEmpty(rqList)) {
                return;
            }
            for (int i = 0; i < rqList.size(); i++) {
                String rq = rqList.get(i);
                //最后一天处理逻辑，采集最近7天和30天
                if (i == rqList.size() - 1) {
                    LocalDate date = LocalDate.parse(rq);
                    // 获取最近1天的日期
                    LocalDate endDate = date.minusDays(1);
                    // 获取最近7天的起始日期
                    LocalDate recent7DaysStart = date.minusDays(7);
                    // 获取最近30天的起始日期
                    LocalDate recent30DaysStart = date.minusDays(30);

                    //判断文件是否都存在  都存在就不采集了
                    boolean downloadedRecent7 = checkDownloaded(cjzh, "recent7", rq);
                    if (!downloadedRecent7) {
                        boolean ztcRecent7 = cj(cjzh, rq, "1", recent7DaysStart.toString(), endDate.toString(), "ztc_recent7");
                        if (!ztcRecent7) {
                            break;
                        }
                    }

                    boolean downloadedRecent30 = checkDownloaded(cjzh, "recent30", rq);
                    if (!downloadedRecent30) {
                        boolean ztcRecent30 = cj(cjzh, rq, "1", recent30DaysStart.toString(), endDate.toString(), "ztc_recent30");
                        if (!ztcRecent30) {
                            break;
                        }
                    }
                }
                if (cj(cjzh, rq, "2", null, null, null)) {
                    //更新日期
                    udpateRq(cjzh, dp, rq);
                    //每次下载完成 等待30s
                    TimeUnit.SECONDS.sleep(30);
                } else {
                    break;
                }
            }
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
    }

    private boolean checkDownloaded(Cjzh cjzh, String rqlx, String rq) {
        boolean downloaded = true;
        String fileNamePrefix = "ztc_" + rqlx;
        for (String tgfxFileSuffix : tgfx_file_suffix) {
            String key = "ztcreports/" + cjzh.getUserid() + "/" + rq + "/" + fileNamePrefix + "_" + tgfxFileSuffix + ".csv";
            if (!ossDao.existAuth(key)) {
                downloaded = false;
                break;
            }
        }
        return downloaded;
    }

    private boolean cj(Cjzh cjzh, String rq, String aggregationMode, String ksrq, String jsrq, String reportName) throws InterruptedException {
        List<ZtcReport> ztcReportList = new ArrayList<>(tgfx_file_suffix.length);
        List<ReportResult> list = null;
        try {
            ZtcReport ztcReport;
            if ("1".equals(aggregationMode)) {
                ztcReport = new ZtcReport(cjzh);
                ztcReport.setKsrq(ksrq);
                ztcReport.setJzrq(jsrq);
                ztcReport.setReportName(reportName);
            } else {
                ztcReport = new ZtcReport(cjzh, rq);
            }
            if (!ZtcStateHttpClient.ztctgfxbb(cjzh, ztcReport, aggregationMode)) {
                return false;
            }

            //等待40S 让报表创建完成
            TimeUnit.SECONDS.sleep(40);

            //下载、解压报表
            list = ZtcStateHttpClient.getTgfxReportList(cjzh, ztcReport);
            for (String suffix : tgfx_file_suffix) {
                for (ReportResult rr : list) {
                    if (rr.getFileName().equalsIgnoreCase(ztcReport.getReportName() + "_" + suffix)) {
                        ZtcReport temp = new ZtcReport(cjzh);
                        temp.setReportResult(rr);
                        temp.setReportId(rr.getId());
                        temp.setReportName(rr.getFileName());
                        ztcReportList.add(temp);
                        break;
                    }
                }
            }

            for (ZtcReport temp : ztcReportList) {
                boolean result = ZtcStateHttpClient.tgfxDownload(cjzh, temp);
                if (!result) {
                    return false;
                }

                File file = new File(temp.getReportFile());
                ZipUtils.unPackZip(file, temp.getFilePath() + temp.getReportId() + "/", 1);

                File zipdFile = new File(temp.getFilePath() + temp.getReportId() + "/" + temp.getReportName() + ".csv");
                String key = "ztcreports/" + cjzh.getUserid() + "/" + rq + "/" + zipdFile.getName();
                ossDao.uploadauth(zipdFile, key);

                //删除临时文件
                if (file.exists()) {
                    file.delete();
                }

                if (zipdFile.exists()) {
                    zipdFile.delete();
                }

                File zipdFileFolder = new File(temp.getFilePath() + temp.getReportId());
                zipdFileFolder.delete();
            }

            return true;
        } finally {
            Optional<List<ReportResult>> optional = Optional.ofNullable(list);

            String deleteIds = optional.orElseGet(Collections::emptyList).stream().filter(reportResult -> {
                return reportResult.getFileName().startsWith("ztc_");
            }).map(ReportResult::getId).collect(Collectors.joining(","));

            //删除直通车报表下载记录
            ZtcStateHttpClient.delTgfxReport(cjzh, deleteIds);
        }

    }

    private List<String> getAllDate(Dp dp) {
        return dataUploader.hqztcstaterq(dp);
    }

    private void udpateRq(Cjzh cjzh, Dp dp, String rq) {
        JysjRq jysjrq = new JysjRq();
        jysjrq.setDp(dp);
        jysjrq.setJlbdpid(dp.getJlbdpid());
        jysjrq.setZtcstaterq(BiDateUtil.strToDate(rq));
        dataUploader.updateZtcstaterq(jysjrq);
    }
}