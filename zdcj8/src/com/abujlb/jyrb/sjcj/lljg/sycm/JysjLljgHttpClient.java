package com.abujlb.jyrb.sjcj.lljg.sycm;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.config.RequestConfig.Builder;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.abujlb.ckstore.AbujlbCookieStore;
import com.abujlb.ckstore.Cjzh;
import com.abujlb.jyrb.sjcj.lljg.bean.DpTopItem;
import com.abujlb.jyrb.sjcj.lljg.bean.ZhIpLog;
import com.abujlb.jyrb.sjcj.lljg.constant.SycmConst;
import com.abujlb.jyrb.sjcj.lljg.tsdao.ZdcjZhIplogTsDao;
import com.abujlb.retry.Retry;
import com.abujlb.util.DateUtil;
import com.abujlb.web.IpRemote;
import com.abujlb.zdcj8.util.StrUtil;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

/**
 * 生意参谋接口采集：生意参谋 -> 品类 -> 商品360 -> 宝贝信息，经营数据流量结构
 * 
 * <AUTHOR>
 * @date 2020-11-13 18:37:01
 */
@Component
public class JysjLljgHttpClient {

	private static Logger log = Logger.getLogger(JysjLljgHttpClient.class);

	// private static final String LX = "jysjlljg";
	private static final String LX = "zdcj8";

	@Autowired
	private AbujlbCookieStore abujlbCookieStore;
//	@Autowired
//	private ZdcjZhIpLogDao dao;
	@Autowired
	private ZdcjZhIplogTsDao zdcjZhIplogTsDao;
	@Autowired
	private IpRemote ipRemote;

	/**
	 * 保存采集日志，到表格存储
	 * 
	 * @param cjzh 采集账号
	 * @param errcode 错误码
	 * @param data 报错内容
	 */
	public void saveLog(Cjzh cjzh, String errcode, String data) {
		ZhIpLog log = new ZhIpLog();
		log.setZhuzh(cjzh.getZhuzh());
		log.setLx(LX);
		log.setErrcode(errcode);
		log.setData(data);
		log.setIp(ipRemote.getIp());
		if (StrUtil.isNull(log.getIp())) {
			log.setIp("127.0.0.1");
		}
		log.setSj(DateUtil.formatTime(DateUtil.getCurrentTime()));
		// dao.save(log);
		zdcjZhIplogTsDao.saveLog(log);
	}

	/**
	 * 生意参谋 -> 品类 -> 商品360 -> 宝贝list，按照销售额获取店铺的top宝贝
	 * 
	 * @param cjzh 采集账号
	 * @return list 宝贝列表
	 */
	@Retry
	public List<DpTopItem> getTopList(Cjzh cjzh) {
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(abujlbCookieStore.getCookie(cjzh)).build();
		long timestamp = System.currentTimeMillis(); // 时间戳
		String indexCode = "itmUv,payAmt,payRate"; // 商品访客数，支付金额，支付转化率
		String url = String.format(SycmConst.TOP_ITEMLIST_URL, SycmConst.DEFAULT_PAGESIZE20, SycmConst.DEFAULT_PAGE, indexCode, String.valueOf(timestamp), cjzh.getMicrodata("legalityToken"));
		try {
			url = String.format(SycmConst.TOP_ITEMLIST_URL, SycmConst.DEFAULT_PAGESIZE20, SycmConst.DEFAULT_PAGE, URLEncoder.encode(indexCode, "UTF-8"), String.valueOf(timestamp),
					cjzh.getMicrodata("legalityToken"));
		} catch (UnsupportedEncodingException e1) {
			e1.printStackTrace();
			log.error(e1.getMessage(), e1);
		}
		HttpGet httpGet = new HttpGet(url);
		httpGet.setHeader("accept", "*/*");
		httpGet.setHeader("accept-encoding", "gzip, deflate, br");
		httpGet.setHeader("accept-language", "zh-CN,zh;q=0.9");
		httpGet.setHeader("referer", "https://sycm.taobao.com/cc/item_archives");
		httpGet.setHeader("sycm-referer", "/cc/item_archives");
		httpGet.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.75 Safari/537.36");

		Builder requestConfig = RequestConfig.custom().setSocketTimeout(30000).setConnectTimeout(30000).setConnectionRequestTimeout(30000);
		httpGet.setConfig(requestConfig.build());
		HttpResponse httpresponse;
		String name = "getTopList";
		String body = "";
		try {
			httpresponse = httpClient.execute(httpGet);
			HttpEntity entity = httpresponse.getEntity();
			body = EntityUtils.toString(entity, "utf-8");
			String errorjson = "{\"name\":\"" + name + "\",\"body\":\"" + body + "\"}";
			if (StrUtil.isNull(body)) { // 空
				log.error("生意参谋，获取" + name + "：返回的是空字符串");
				this.saveLog(cjzh, "returnnull", errorjson);
				return null;
			}
			if (!StrUtil.isJson(body)) {
				log.error("生意参谋，获取" + name + "：返回的不是json");
				log.error(body);
				this.saveLog(cjzh, "notjson", errorjson);
				return null;
			}
			JSONObject jsonObj = JSONObject.fromObject(body);
			// 出现验证码
			if (jsonObj.has("rgv587_flag") && jsonObj.getString("rgv587_flag").equalsIgnoreCase("sm")) {
				log.error("生意参谋，获取" + name + "：出现滑块！");
				this.saveLog(cjzh, "rgv587_flag", errorjson);
				return null; // 返回" "，不会触发@Retry，且不会保存到表格存储
			}
			if (!jsonObj.has("code") || jsonObj.getInt("code") != 0) { // 状态不为0，说明接口返回失败
				log.info("生意参谋，获取" + name + "，code错误：" + body);
				this.saveLog(cjzh, "errorcode", errorjson);
				return null;
			}
			if (!jsonObj.has("data") || !jsonObj.getJSONArray("data").isArray()) { // 有可能code == 0，但是没有data
				log.info("生意参谋，获取" + name + "，data错误：" + body);
				this.saveLog(cjzh, "errordata", errorjson);
				return null;
			}
			JSONArray dataArr = jsonObj.getJSONArray("data");
			List<DpTopItem> list = new ArrayList<DpTopItem>();
			for (int i = 0; dataArr != null && i < dataArr.size(); i++) {
				JSONObject data = dataArr.getJSONObject(i);
				DpTopItem item = new DpTopItem(data);
				if (!StrUtil.isNull(item.getItemId())) {
					list.add(item);
				}
			}
			return list;
		} catch (Exception e) {
			log.info("生意参谋，获取" + name + "，未知错误：" + body);
			log.error(e.getMessage(), e);
			e.printStackTrace();
			this.saveLog(cjzh, "unknownerror", e.getMessage());
		} finally {
			httpGet.releaseConnection();
		}
		return null;
	}

	/**
	 * 生意参谋 -> 品类 -> 商品360 -> 宝贝详情 -> 单品诊断（单品表现，(选择人群)整体，(选择比较范围)较日常平均）
	 * 
	 * @param cjzh 采集账号
	 * @param itemId 宝贝id
	 * @param rq 采集日期
	 * @return json原始数据
	 */
	@Retry
	public String topItemDpzdDpbx(Cjzh cjzh, String itemId, String rq) {
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(abujlbCookieStore.getCookie(cjzh)).build();
		long timestamp = System.currentTimeMillis(); // 时间戳
		String dateRange = rq + "%7C" + rq;
		String url = String.format(SycmConst.TOP_ITEM_DPZD_DPBX_URL, dateRange, itemId, String.valueOf(timestamp), cjzh.getMicrodata("legalityToken"));
		HttpGet httpGet = new HttpGet(url);
		httpGet.setHeader("accept", "*/*");
		httpGet.setHeader("accept-encoding", "gzip, deflate, br");
		httpGet.setHeader("accept-language", "zh-CN,zh;q=0.9");
		httpGet.setHeader("referer", "https://sycm.taobao.com/cc/item_archives?activeKey=diagnosis&dateRange=" + dateRange + "&dateType=day&itemId=" + itemId);
		httpGet.setHeader("sycm-referer", "/cc/item_archives");
		httpGet.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.75 Safari/537.36");

		Builder requestConfig = RequestConfig.custom().setSocketTimeout(30000).setConnectTimeout(30000).setConnectionRequestTimeout(30000);
		httpGet.setConfig(requestConfig.build());
		HttpResponse httpresponse;
		String name = "topItemDpzdDpbx";
		String body = "";
		try {
			httpresponse = httpClient.execute(httpGet);
			HttpEntity entity = httpresponse.getEntity();
			body = EntityUtils.toString(entity, "utf-8");
			return this.commonAllReturn(cjzh, name, body);
		} catch (Exception e) {
			log.info("生意参谋，获取" + name + "，未知错误：" + body);
			log.error(e.getMessage(), e);
			e.printStackTrace();
			this.saveLog(cjzh, "unknownerror", e.getMessage());
		} finally {
			httpGet.releaseConnection();
		}
		return null;
	}

	/**
	 * 生意参谋 -> 品类 -> 商品360 -> 宝贝详情 -> 销售分析，SKU 销售详情（选择：单日、所有终端）
	 * 
	 * @param cjzh 采集账号
	 * @param itemId 宝贝id
	 * @param rq 采集日期
	 * @return json原始数据
	 */
	@Retry
	public String topItemXsfxSku(Cjzh cjzh, String itemId, String rq) {
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(abujlbCookieStore.getCookie(cjzh)).build();
		long timestamp = System.currentTimeMillis(); // 时间戳
		// String yesterday = DateUtil.formatDate(new Date(timestamp - 24 * 60 * 60 * 1000L)); // 昨天，86400000L = 24 * 60 * 60 * 1000L
		String dateRange = rq + "%7C" + rq;
		String indexCode = "cartCnt,payAmt,payByrCnt"; // 加购件数，支付金额，支付买家数
		String url = String.format(SycmConst.TOP_ITEM_XSFX_SKU_URL, dateRange, SycmConst.DEFAULT_PAGESIZE, SycmConst.DEFAULT_PAGE, itemId, indexCode, String.valueOf(timestamp),
				cjzh.getMicrodata("legalityToken"));
		try {
			url = String.format(SycmConst.TOP_ITEM_XSFX_SKU_URL, dateRange, SycmConst.DEFAULT_PAGESIZE, SycmConst.DEFAULT_PAGE, itemId, URLEncoder.encode(indexCode, "UTF-8"),
					String.valueOf(timestamp), cjzh.getMicrodata("legalityToken"));
		} catch (UnsupportedEncodingException e1) {
			e1.printStackTrace();
			log.error(e1.getMessage(), e1);
		}
		HttpGet httpGet = new HttpGet(url);
		httpGet.setHeader("accept", "*/*");
		httpGet.setHeader("accept-encoding", "gzip, deflate, br");
		httpGet.setHeader("accept-language", "zh-CN,zh;q=0.9");
		httpGet.setHeader("referer", "https://sycm.taobao.com/cc/item_archives?activeKey=sale&dateRange=" + dateRange + "&dateType=day&itemId=" + itemId);
		httpGet.setHeader("sycm-referer", "/cc/item_archives");
		httpGet.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.75 Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(30000).setConnectTimeout(30000).setConnectionRequestTimeout(30000);
		httpGet.setConfig(requestConfig.build());
		HttpResponse httpresponse;
		String name = "topItemXsfxSku";
		String body = "";
		try {
			httpresponse = httpClient.execute(httpGet);
			HttpEntity entity = httpresponse.getEntity();
			body = EntityUtils.toString(entity, "utf-8");
			return this.commonAllReturn(cjzh, name, body);
		} catch (Exception e) {
			log.info("生意参谋，获取" + name + "，未知错误：" + body);
			log.error(e.getMessage(), e);
			e.printStackTrace();
			this.saveLog(cjzh, "unknownerror", e.getMessage());
		} finally {
			httpGet.releaseConnection();
		}
		return null;
	}

	/**
	 * 生意参谋 -> 品类 -> 商品360 -> 宝贝详情 -> 销售分析，颜色分类
	 * 
	 * @param cjzh 采集账号
	 * @param itemId 宝贝id
	 * @param rq 采集日期
	 * @return json原始数据
	 */
	@Retry
	public String topItemXsfxYsfl(Cjzh cjzh, String itemId, String rq) {
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(abujlbCookieStore.getCookie(cjzh)).build();
		long timestamp = System.currentTimeMillis(); // 时间戳
		// String yesterday = DateUtil.formatDate(new Date(timestamp - 24 * 60 * 60 * 1000L)); // 昨天，86400000L = 24 * 60 * 60 * 1000L
		String dateRange = rq + "%7C" + rq;
		String url = String.format(SycmConst.TOP_ITEM_XSFX_YSFL_URL, itemId, String.valueOf(timestamp), cjzh.getMicrodata("legalityToken"));
		HttpGet httpGet = new HttpGet(url);
		httpGet.setHeader("accept", "*/*");
		httpGet.setHeader("accept-encoding", "gzip, deflate, br");
		httpGet.setHeader("accept-language", "zh-CN,zh;q=0.9");
		httpGet.setHeader("referer", "https://sycm.taobao.com/cc/item_archives?activeKey=sale&dateRange=" + dateRange + "&dateType=today&itemId=" + itemId);
		httpGet.setHeader("sycm-referer", "/cc/item_archives");
		httpGet.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.75 Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(30000).setConnectTimeout(30000).setConnectionRequestTimeout(30000);
		httpGet.setConfig(requestConfig.build());
		HttpResponse httpresponse;
		String name = "topItemXsfxYsfl";
		String body = "";
		try {
			httpresponse = httpClient.execute(httpGet);
			HttpEntity entity = httpresponse.getEntity();
			body = EntityUtils.toString(entity, "utf-8");
			String errorjson = "{\"name\":\"" + name + "\",\"body\":\"" + body + "\"}";
			if (StrUtil.isNull(body)) { // 空
				log.error("生意参谋，获取" + name + "：返回的是空字符串");
				this.saveLog(cjzh, "returnnull", errorjson);
				return null;
			}
			if (!StrUtil.isJson(body)) {
				log.error("生意参谋，获取" + name + "：返回的不是json");
				log.error(body);
				this.saveLog(cjzh, "notjson", errorjson);
				return null;
			}
			JSONObject jsonObj = JSONObject.fromObject(body);
			// 出现验证码
			if (jsonObj.has("rgv587_flag") && jsonObj.getString("rgv587_flag").equalsIgnoreCase("sm")) {
				log.error("生意参谋，获取" + name + "：出现滑块！");
				this.saveLog(cjzh, "rgv587_flag", errorjson);
				return " "; // 返回" "，不会触发@Retry，且不会保存到表格存储
			}
			if (!jsonObj.has("code") || jsonObj.getInt("code") != 0) { // 状态不为0，说明接口返回失败
				log.info("生意参谋，获取" + name + "，code错误：" + body);
				this.saveLog(cjzh, "errorcode", errorjson);
				return " "; // 返回" "，不会触发@Retry，且不会保存到表格存储
			}
			if (!jsonObj.has("data") || !jsonObj.getJSONArray("data").isArray()) { // 有可能code == 0，但是没有data
				log.info("生意参谋，获取" + name + "，data错误：" + body);
				this.saveLog(cjzh, "errordata", errorjson);
				return null;
			}
			JSONArray dataArr = jsonObj.getJSONArray("data");
			if (dataArr != null && dataArr.size() > 0) {
				return dataArr.getString(0);
			} else {
				log.info("生意参谋，获取" + name + "：itemId = " + itemId + "，rq = " + rq + "，颜色分类，查询失败：" + body);
				this.saveLog(cjzh, "errordata", errorjson);
				return "";
			}
		} catch (Exception e) {
			log.info("生意参谋，获取" + name + "，未知错误：" + body);
			log.error(e.getMessage(), e);
			e.printStackTrace();
			this.saveLog(cjzh, "unknownerror", e.getMessage());
		} finally {
			httpGet.releaseConnection();
		}
		return null;
	}

	/**
	 * 生意参谋 -> 品类 -> 商品360 -> 宝贝详情 -> 销售分析，属性分析（选择：单日、所有终端）
	 * 
	 * @param cjzh 采集账号
	 * @param itemId 宝贝id
	 * @param rq 采集日期
	 * @param attrName 分类名称，例如：颜色分类
	 * @return json原始数据
	 */
	@Retry
	public String topItemXsfxSxfx(Cjzh cjzh, String itemId, String rq, String attrName) {
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(abujlbCookieStore.getCookie(cjzh)).build();
		long timestamp = System.currentTimeMillis(); // 时间戳
		// String yesterday = DateUtil.formatDate(new Date(timestamp - 24 * 60 * 60 * 1000L)); // 昨天，86400000L = 24 * 60 * 60 * 1000L
		String dateRange = rq + "%7C" + rq;
		String indexCode = "payAmt,payAmtRatio,payItmCnt,payByrCnt,payByrCntRatio"; // 支付金额，支付金额占比，支付件数，支付买家数，支付买家数占比
		String url = String.format(SycmConst.TOP_ITEM_XSFX_SXFX_URL, dateRange, SycmConst.DEFAULT_PAGESIZE, SycmConst.DEFAULT_PAGE, attrName, itemId, indexCode, String.valueOf(timestamp),
				cjzh.getMicrodata("legalityToken"));
		try {
			url = String.format(SycmConst.TOP_ITEM_XSFX_SXFX_URL, dateRange, SycmConst.DEFAULT_PAGESIZE, SycmConst.DEFAULT_PAGE, attrName, itemId, URLEncoder.encode(indexCode, "UTF-8"),
					String.valueOf(timestamp), cjzh.getMicrodata("legalityToken"));
		} catch (UnsupportedEncodingException e1) {
			e1.printStackTrace();
			log.error(e1.getMessage(), e1);
		}
		HttpGet httpGet = new HttpGet(url);
		httpGet.setHeader("accept", "*/*");
		httpGet.setHeader("accept-encoding", "gzip, deflate, br");
		httpGet.setHeader("accept-language", "zh-CN,zh;q=0.9");
		httpGet.setHeader("referer", "https://sycm.taobao.com/cc/item_archives?activeKey=sale&dateRange=" + dateRange + "&dateType=day&itemId=" + itemId);
		httpGet.setHeader("sycm-referer", "/cc/item_archives");
		httpGet.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.75 Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(30000).setConnectTimeout(30000).setConnectionRequestTimeout(30000);
		httpGet.setConfig(requestConfig.build());
		HttpResponse httpresponse;
		String name = "topItemXsfxSxfx";
		String body = "";
		try {
			httpresponse = httpClient.execute(httpGet);
			HttpEntity entity = httpresponse.getEntity();
			body = EntityUtils.toString(entity, "utf-8");
			return this.commonAllReturn(cjzh, name, body);
		} catch (Exception e) {
			log.info("生意参谋，获取" + name + "，未知错误：" + body);
			log.error(e.getMessage(), e);
			e.printStackTrace();
			this.saveLog(cjzh, "unknownerror", e.getMessage());
		} finally {
			httpGet.releaseConnection();
		}
		return null;
	}

	/**
	 * 生意参谋 -> 品类 -> 商品360 -> 宝贝详情 -> 流量来源（选择：单日、无线端、每一次访问来源），会出现滑块验证
	 * 
	 * @param cjzh 采集账号
	 * @param itemId 宝贝id
	 * @param rq 采集日期
	 * @return json原始数据
	 */
	@Retry
	public String topItemLlly(Cjzh cjzh, String itemId, String rq) {
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(abujlbCookieStore.getCookie(cjzh)).build();
		long timestamp = System.currentTimeMillis(); // 时间戳
		String dateRange = rq + "%7C" + rq;
		String url = String.format(SycmConst.TOP_ITEM_LLLY_URL, dateRange, itemId, String.valueOf(timestamp), cjzh.getMicrodata("legalityToken"));
		HttpGet httpGet = new HttpGet(url);
		httpGet.setHeader("accept", "*/*");
		httpGet.setHeader("accept-encoding", "gzip, deflate, br");
		httpGet.setHeader("accept-language", "zh-CN,zh;q=0.9");
		httpGet.setHeader("referer", "https://sycm.taobao.com/cc/item_archives?activeKey=flow&dateRange=" + dateRange + "&dateType=day&itemId=" + itemId);
		httpGet.setHeader("sycm-referer", "/cc/item_archives");
		httpGet.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.75 Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(30000).setConnectTimeout(30000).setConnectionRequestTimeout(30000);
		httpGet.setConfig(requestConfig.build());
		HttpResponse httpresponse;
		String name = "topItemLlly";
		String body = "";
		try {
			httpresponse = httpClient.execute(httpGet);
			HttpEntity entity = httpresponse.getEntity();
			body = EntityUtils.toString(entity, "utf-8");
			return this.commonAllReturn(cjzh, name, body);
		} catch (Exception e) {
			log.info("生意参谋，获取" + name + "，未知错误：" + body);
			log.error(e.getMessage(), e);
			e.printStackTrace();
			this.saveLog(cjzh, "unknownerror", e.getMessage());
		} finally {
			httpGet.releaseConnection();
		}
		return null;
	}

	/**
	 * 生意参谋 -> 品类 -> 商品360 -> 宝贝详情 -> 流量来源 -> 详情：手淘搜索、直通车（选择：单日、无线端、每一次访问来源）
	 * 
	 * @param cjzh 采集账号
	 * @param itemId 宝贝id
	 * @param rq 采集日期
	 * @return json原始数据
	 */
	@Retry
	public String topItemLllyDetail(Cjzh cjzh, String itemId, String rq, String pageId, String pPageId, String pageLevel, String childPageType) {
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(abujlbCookieStore.getCookie(cjzh)).build();
		long timestamp = System.currentTimeMillis(); // 时间戳
		String dateRange = rq + "%7C" + rq;
		String url = String.format(SycmConst.TOP_ITEM_LLLY_DETAIL_URL, dateRange, itemId, pageId, pPageId, pageLevel, childPageType, String.valueOf(timestamp), cjzh.getMicrodata("legalityToken"));
		HttpGet httpGet = new HttpGet(url);
		httpGet.setHeader("accept", "*/*");
		httpGet.setHeader("accept-encoding", "gzip, deflate, br");
		httpGet.setHeader("accept-language", "zh-CN,zh;q=0.9");
		httpGet.setHeader("referer", "https://sycm.taobao.com/cc/item_archives?activeKey=flow&dateRange=" + dateRange + "&dateType=day&itemId=" + itemId);
		httpGet.setHeader("sycm-referer", "/cc/item_archives");
		httpGet.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.75 Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(30000).setConnectTimeout(30000).setConnectionRequestTimeout(30000);
		httpGet.setConfig(requestConfig.build());
		HttpResponse httpresponse;
		String name = "topItemLllyDetail";
		String body = "";
		try {
			httpresponse = httpClient.execute(httpGet);
			HttpEntity entity = httpresponse.getEntity();
			body = EntityUtils.toString(entity, "utf-8");
			return this.commonAllReturn(cjzh, name, body);
		} catch (Exception e) {
			log.info("生意参谋，获取" + name + "，未知错误：" + body);
			log.error(e.getMessage(), e);
			e.printStackTrace();
			this.saveLog(cjzh, "unknownerror", e.getMessage());
		} finally {
			httpGet.releaseConnection();
		}
		return null;
	}

	/**
	 * 生意参谋 -> 品类 -> 商品360 -> 宝贝详情 -> 标题优化，标题分析（选择：单日、所有终端）
	 * 
	 * @param cjzh 采集账号
	 * @param itemId 宝贝id
	 * @param rq 采集日期
	 * @return json原始数据
	 */
	@Retry
	public String topItemBtyhBtfx(Cjzh cjzh, String itemId, String rq) {
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(abujlbCookieStore.getCookie(cjzh)).build();
		long timestamp = System.currentTimeMillis(); // 时间戳
		String dateRange = rq + "%7C" + rq;
		String url = String.format(SycmConst.TOP_ITEM_BTYH_BTFX_URL, itemId, dateRange, String.valueOf(timestamp), cjzh.getMicrodata("legalityToken"));
		HttpGet httpGet = new HttpGet(url);
		httpGet.setHeader("accept", "*/*");
		httpGet.setHeader("accept-encoding", "gzip, deflate, br");
		httpGet.setHeader("accept-language", "zh-CN,zh;q=0.9");
		httpGet.setHeader("referer", "https://sycm.taobao.com/cc/item_archives?activeKey=title&dateRange=" + dateRange + "&dateType=day&itemId=" + itemId);
		httpGet.setHeader("sycm-referer", "/cc/item_archives");
		httpGet.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.75 Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(30000).setConnectTimeout(30000).setConnectionRequestTimeout(30000);
		httpGet.setConfig(requestConfig.build());
		HttpResponse httpresponse;
		String name = "topItemBtyhBtfx";
		String body = "";
		try {
			httpresponse = httpClient.execute(httpGet);
			HttpEntity entity = httpresponse.getEntity();
			body = EntityUtils.toString(entity, "utf-8");
			return this.commonAllReturn(cjzh, name, body);
		} catch (Exception e) {
			log.info("生意参谋，获取" + name + "，未知错误：" + body);
			log.error(e.getMessage(), e);
			e.printStackTrace();
			this.saveLog(cjzh, "unknownerror", e.getMessage());
		} finally {
			httpGet.releaseConnection();
		}
		return null;
	}

	/**
	 * 生意参谋 -> 品类 -> 商品360 -> 宝贝详情 -> 标题优化 -> 综合诊断（选择：单日、所有终端）
	 * 
	 * @param cjzh 采集账号
	 * @param itemId 宝贝id
	 * @param rq 采集日期
	 * @return json原始数据
	 */
	@Retry
	public String topItemBtyhZhzdqs(Cjzh cjzh, String itemId, String rq) {
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(abujlbCookieStore.getCookie(cjzh)).build();
		long timestamp = System.currentTimeMillis(); // 时间戳
		String dateRange = rq + "%7C" + rq;
		String url = String.format(SycmConst.TOP_ITEM_BTYH_ZHZDQS_URL, dateRange, itemId, String.valueOf(timestamp), cjzh.getMicrodata("legalityToken"));
		HttpGet httpGet = new HttpGet(url);
		httpGet.setHeader("accept", "*/*");
		httpGet.setHeader("accept-encoding", "gzip, deflate, br");
		httpGet.setHeader("accept-language", "zh-CN,zh;q=0.9");
		httpGet.setHeader("referer", "https://sycm.taobao.com/cc/item_archives?activeKey=title&dateRange=" + dateRange + "&dateType=day&itemId=" + itemId);
		httpGet.setHeader("sycm-referer", "/cc/item_archives");
		httpGet.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.75 Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(30000).setConnectTimeout(30000).setConnectionRequestTimeout(30000);
		httpGet.setConfig(requestConfig.build());
		HttpResponse httpresponse;
		String name = "topItemBtyhZhzdqs";
		String body = "";
		try {
			httpresponse = httpClient.execute(httpGet);
			HttpEntity entity = httpresponse.getEntity();
			body = EntityUtils.toString(entity, "utf-8");
			return this.commonAllReturn(cjzh, name, body);
		} catch (Exception e) {
			log.info("生意参谋，获取" + name + "，未知错误：" + body);
			log.error(e.getMessage(), e);
			e.printStackTrace();
			this.saveLog(cjzh, "unknownerror", e.getMessage());
		} finally {
			httpGet.releaseConnection();
		}
		return null;
	}

	/**
	 * 生意参谋 -> 品类 -> 商品360 -> 宝贝详情 -> 标题优化 -> 本品（选择：单日、所有终端）
	 * 
	 * @param cjzh 采集账号
	 * @param itemId 宝贝id
	 * @param rq 采集日期
	 * @param index 类型：0.搜索词、1.品类词、2.修饰词、3.长尾词
	 * @return json原始数据
	 */
	@Retry
	public String topItemBtyhBp(Cjzh cjzh, String itemId, String rq, int index) {
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(abujlbCookieStore.getCookie(cjzh)).build();
		String[] kwTypes = new String[] { "se_keyword", "core_keyword", "prop_keyword", "lgt_keyword" }; // 搜索词、品类词、修饰词、长尾词
		long timestamp = System.currentTimeMillis(); // 时间戳
		String dateRange = rq + "%7C" + rq;
		String indexCode = "uv,payOrderByrCnt,payConveRate"; // 访客数，支付买家数，支付转化率
		String url = String.format(SycmConst.TOP_ITEM_BTYH_BP_URL, dateRange, SycmConst.DEFAULT_PAGESIZE, SycmConst.DEFAULT_PAGE, itemId, kwTypes[index], indexCode, String.valueOf(timestamp),
				cjzh.getMicrodata("legalityToken"));
		try {
			url = String.format(SycmConst.TOP_ITEM_BTYH_BP_URL, dateRange, SycmConst.DEFAULT_PAGESIZE, SycmConst.DEFAULT_PAGE, itemId, kwTypes[index], URLEncoder.encode(indexCode, "UTF-8"),
					String.valueOf(timestamp), cjzh.getMicrodata("legalityToken"));
		} catch (UnsupportedEncodingException e1) {
			e1.printStackTrace();
			log.error(e1.getMessage(), e1);
		}
		HttpGet httpGet = new HttpGet(url);
		httpGet.setHeader("accept", "*/*");
		httpGet.setHeader("accept-encoding", "gzip, deflate, br");
		httpGet.setHeader("accept-language", "zh-CN,zh;q=0.9");
		httpGet.setHeader("referer", "https://sycm.taobao.com/cc/item_archives?activeKey=title&dateRange=" + dateRange + "&dateType=day&itemId=" + itemId);
		httpGet.setHeader("sycm-referer", "/cc/item_archives");
		httpGet.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.75 Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(30000).setConnectTimeout(30000).setConnectionRequestTimeout(30000);
		httpGet.setConfig(requestConfig.build());
		HttpResponse httpresponse;
		String name = "topItemBtyhBp";
		String body = "";
		try {
			httpresponse = httpClient.execute(httpGet);
			HttpEntity entity = httpresponse.getEntity();
			body = EntityUtils.toString(entity, "utf-8");
			return this.commonAllReturn(cjzh, name, body);
		} catch (Exception e) {
			log.info("生意参谋，获取" + name + "，未知错误：" + body);
			log.error(e.getMessage(), e);
			e.printStackTrace();
			this.saveLog(cjzh, "unknownerror", e.getMessage());
		} finally {
			httpGet.releaseConnection();
		}
		return null;
	}

	/**
	 * 生意参谋 -> 品类 -> 商品360 -> 宝贝详情 -> 标题优化 -> 行业（选择：单日、所有终端）
	 * 
	 * @param cjzh 采集账号
	 * @param itemId 宝贝id
	 * @param rq 采集日期
	 * @param index 类型：0.搜索词、1.品类词、2.修饰词、3.长尾词
	 * @return json原始数据
	 */
	@Retry
	public String topItemBtyhHy(Cjzh cjzh, String itemId, String rq, int index) {
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(abujlbCookieStore.getCookie(cjzh)).build();
		String[] kwTypes = new String[] { "se_keyword", "core_keyword", "prop_keyword", "lgt_keyword" }; // 搜索词、品类词、修饰词、长尾词
		long timestamp = System.currentTimeMillis(); // 时间戳
		String dateRange = rq + "%7C" + rq;
		String indexCode = "sePvIndex,clickRate,payRate"; // 搜索热度，点击率，支付转化率
		String url = String.format(SycmConst.TOP_ITEM_BTYH_HY_URL, dateRange, SycmConst.DEFAULT_PAGESIZE, SycmConst.DEFAULT_PAGE, itemId, kwTypes[index], indexCode, String.valueOf(timestamp),
				cjzh.getMicrodata("legalityToken"));
		try {
			url = String.format(SycmConst.TOP_ITEM_BTYH_HY_URL, dateRange, SycmConst.DEFAULT_PAGESIZE, SycmConst.DEFAULT_PAGE, itemId, kwTypes[index], URLEncoder.encode(indexCode, "UTF-8"),
					String.valueOf(timestamp), cjzh.getMicrodata("legalityToken"));
		} catch (UnsupportedEncodingException e1) {
			e1.printStackTrace();
			log.error(e1.getMessage(), e1);
		}
		HttpGet httpGet = new HttpGet(url);
		httpGet.setHeader("accept", "*/*");
		httpGet.setHeader("accept-encoding", "gzip, deflate, br");
		httpGet.setHeader("accept-language", "zh-CN,zh;q=0.9");
		httpGet.setHeader("referer", "https://sycm.taobao.com/cc/item_archives?activeKey=title&dateRange=" + dateRange + "&dateType=day&itemId=" + itemId);
		httpGet.setHeader("sycm-referer", "/cc/item_archives");
		httpGet.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.75 Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(30000).setConnectTimeout(30000).setConnectionRequestTimeout(30000);
		httpGet.setConfig(requestConfig.build());
		HttpResponse httpresponse;
		String name = "topItemBtyhHy";
		String body = "";
		try {
			httpresponse = httpClient.execute(httpGet);
			HttpEntity entity = httpresponse.getEntity();
			body = EntityUtils.toString(entity, "utf-8");
			return this.commonAllReturn(cjzh, name, body);
		} catch (Exception e) {
			log.info("生意参谋，获取" + name + "，未知错误：" + body);
			log.error(e.getMessage(), e);
			e.printStackTrace();
			this.saveLog(cjzh, "unknownerror", e.getMessage());
		} finally {
			httpGet.releaseConnection();
		}
		return null;
	}

	/**
	 * 生意参谋 -> 品类 -> 商品360 -> 宝贝详情 -> 内容分析 -> TOP3渠道（选择：最近30天）
	 * 
	 * @param cjzh 采集账号
	 * @param itemId 宝贝id
	 * @param rq 采集日期
	 * @return json原始数据
	 */
	@Retry
	public String topItemNrfxTop3(Cjzh cjzh, String itemId, String rq) {
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(abujlbCookieStore.getCookie(cjzh)).build();
		long timestamp = System.currentTimeMillis(); // 时间戳
		Date rqDate = DateUtil.parseDate(rq);
		String rq1 = DateUtil.formatDate(new Date(rqDate.getTime() - 24 * 60 * 60 * 1000L)); // 昨天，86400000L = 24 * 60 * 60 * 1000L
		String rq2 = DateUtil.formatDate(new Date(rqDate.getTime() - 30 * 24 * 60 * 60 * 1000L)); //
		String dateRange = rq2 + "%7C" + rq1;
		String url = String.format(SycmConst.TOP_ITEM_NRFX_TOP3_URL, dateRange, SycmConst.DEFAULT_PAGESIZE, SycmConst.DEFAULT_PAGE, itemId, String.valueOf(timestamp),
				cjzh.getMicrodata("legalityToken"));
		HttpGet httpGet = new HttpGet(url);
		httpGet.setHeader("accept", "*/*");
		httpGet.setHeader("accept-encoding", "gzip, deflate, br");
		httpGet.setHeader("accept-language", "zh-CN,zh;q=0.9");
		httpGet.setHeader("referer", "https://sycm.taobao.com/cc/item_archives?activeKey=content&dateRange=" + dateRange + "&dateType=recent30&itemId=" + itemId);
		httpGet.setHeader("sycm-referer", "/cc/item_archives");
		httpGet.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.75 Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(30000).setConnectTimeout(30000).setConnectionRequestTimeout(30000);
		httpGet.setConfig(requestConfig.build());
		HttpResponse httpresponse;
		String name = "topItemNrfxTop3";
		String body = "";
		try {
			httpresponse = httpClient.execute(httpGet);
			HttpEntity entity = httpresponse.getEntity();
			body = EntityUtils.toString(entity, "utf-8");
			String errorjson = "{\"name\":\"" + name + "\",\"body\":\"" + body + "\"}";
			if (StrUtil.isNull(body)) { // 空
				log.error("生意参谋，获取" + name + "：返回的是空字符串");
				this.saveLog(cjzh, "returnnull", errorjson);
				return null;
			}
			if (!StrUtil.isJson(body)) {
				log.error("生意参谋，获取" + name + "：返回的不是json");
				log.error(body);
				this.saveLog(cjzh, "notjson", errorjson);
				return null;
			}
			JSONObject jsonObj = JSONObject.fromObject(body);
			// 出现验证码
			if (jsonObj.has("rgv587_flag") && jsonObj.getString("rgv587_flag").equalsIgnoreCase("sm")) {
				log.error("生意参谋，获取" + name + "：出现滑块！");
				this.saveLog(cjzh, "rgv587_flag", errorjson);
				return " "; // 返回" "，不会触发@Retry，且不会保存到表格存储
			}
			if (!jsonObj.has("hasError") || jsonObj.getBoolean("hasError")) { // 状态不为0，说明接口返回失败
				log.info("生意参谋，获取" + name + "，hasError错误：" + body);
				this.saveLog(cjzh, "hasError", errorjson);
				return " "; // 返回" "，不会触发@Retry，且不会保存到表格存储
			}
			if (!jsonObj.has("content") || jsonObj.getJSONObject("content").isNullObject() || !jsonObj.getJSONObject("content").has("code") 
					|| jsonObj.getJSONObject("content").getInt("code") != 0) { // 有可能code == 0，但是没有data
				log.info("生意参谋，获取" + name + "，code错误：" + body);
				this.saveLog(cjzh, "errordata", errorjson);
				return " ";
			}
			JSONArray dataArr = jsonObj.getJSONObject("content").getJSONArray("data");
			return dataArr.toString();
		} catch (Exception e) {
			log.info("生意参谋，获取" + name + "，未知错误：" + body);
			log.error(e.getMessage(), e);
			e.printStackTrace();
			this.saveLog(cjzh, "unknownerror", e.getMessage());
		} finally {
			httpGet.releaseConnection();
		}
		return null;
	}

	/**
	 * 生意参谋 -> 品类 -> 商品360 -> 宝贝详情 -> 内容分析 -> TOP3渠道（选择：最近30天）
	 * 
	 * @param cjzh 采集账号
	 * @param itemId 宝贝id
	 * @param rq 采集日期
	 * @return json原始数据
	 */
	@Retry
	public String topItemNrfxTop10(Cjzh cjzh, String itemId, String rq) {
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(abujlbCookieStore.getCookie(cjzh)).build();
		long timestamp = System.currentTimeMillis(); // 时间戳
		Date rqDate = DateUtil.parseDate(rq);
		String rq1 = DateUtil.formatDate(new Date(rqDate.getTime() - 24 * 60 * 60 * 1000L)); // 昨天，86400000L = 24 * 60 * 60 * 1000L
		String rq2 = DateUtil.formatDate(new Date(rqDate.getTime() - 30 * 24 * 60 * 60 * 1000L)); //
		String dateRange = rq2 + "%7C" + rq1;
		String url = String.format(SycmConst.TOP_ITEM_NRFX_TOP10_URL, dateRange, SycmConst.DEFAULT_PAGESIZE, SycmConst.DEFAULT_PAGE, itemId, String.valueOf(timestamp),
				cjzh.getMicrodata("legalityToken"));
		HttpGet httpGet = new HttpGet(url);
		httpGet.setHeader("accept", "*/*");
		httpGet.setHeader("accept-encoding", "gzip, deflate, br");
		httpGet.setHeader("accept-language", "zh-CN,zh;q=0.9");
		httpGet.setHeader("referer", "https://sycm.taobao.com/cc/item_archives?activeKey=content&dateRange=" + dateRange + "&dateType=recent30&itemId=" + itemId);
		httpGet.setHeader("sycm-referer", "/cc/item_archives");
		httpGet.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.75 Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(30000).setConnectTimeout(30000).setConnectionRequestTimeout(30000);
		httpGet.setConfig(requestConfig.build());
		HttpResponse httpresponse;
		String name = "topItemNrfxTop10";
		String body = "";
		try {
			httpresponse = httpClient.execute(httpGet);
			HttpEntity entity = httpresponse.getEntity();
			body = EntityUtils.toString(entity, "utf-8");
			String errorjson = "{\"name\":\"" + name + "\",\"body\":\"" + body + "\"}";
			if (StrUtil.isNull(body)) { // 空
				log.error("生意参谋，获取" + name + "：返回的是空字符串");
				this.saveLog(cjzh, "returnnull", errorjson);
				return null;
			}
			if (!StrUtil.isJson(body)) {
				log.error("生意参谋，获取" + name + "：返回的不是json");
				log.error(body);
				this.saveLog(cjzh, "notjson", errorjson);
				return null;
			}
			JSONObject jsonObj = JSONObject.fromObject(body);
			// 出现验证码
			if (jsonObj.has("rgv587_flag") && jsonObj.getString("rgv587_flag").equalsIgnoreCase("sm")) {
				log.error("生意参谋，获取" + name + "：出现滑块！");
				this.saveLog(cjzh, "rgv587_flag", errorjson);
				return " "; // 返回" "，不会触发@Retry，且不会保存到表格存储
			}
			if (!jsonObj.has("hasError") || jsonObj.getBoolean("hasError")) { // 状态不为0，说明接口返回失败
				log.info("生意参谋，获取" + name + "，hasError错误：" + body);
				this.saveLog(cjzh, "hasError", errorjson);
				return " "; // 返回" "，不会触发@Retry，且不会保存到表格存储
			}
			if (!jsonObj.has("content") || jsonObj.getJSONObject("content").isNullObject() || !jsonObj.getJSONObject("content").has("code") 
					|| jsonObj.getJSONObject("content").getInt("code") != 0) { // 有可能code == 0，但是没有data
				log.info("生意参谋，获取" + name + "，code错误：" + body);
				this.saveLog(cjzh, "errordata", errorjson);
				return null;
			}
			JSONArray dataArr = jsonObj.getJSONObject("content").getJSONArray("data");
			return dataArr.toString();
		} catch (Exception e) {
			log.info("生意参谋，获取" + name + "，未知错误：" + body);
			log.error(e.getMessage(), e);
			e.printStackTrace();
			this.saveLog(cjzh, "unknownerror", e.getMessage());
		} finally {
			httpGet.releaseConnection();
		}
		return null;
	}

	/**
	 * 生意参谋 -> 品类 -> 商品360 -> 宝贝详情 -> 客群洞察，判断（选择：单日）
	 * 
	 * @param cjzh 采集账号
	 * @param itemId 宝贝id
	 * @param rq 采集日期
	 * @param index 类型：0.访问人群、1.搜索人群、2.支付人群
	 * @return json原始数据
	 */
	@Retry
	public String topItemKqdcCheck(Cjzh cjzh, String itemId, String rq, int index) {
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(abujlbCookieStore.getCookie(cjzh)).build();
		String[] crowdsTypes = new String[] { "itmUv", "appSearchUv", "payByrCnt" }; // 访问人群、搜索人群、支付人群
		long timestamp = System.currentTimeMillis(); // 时间戳
		String dateRange = rq + "%7C" + rq;
		String url = String.format(SycmConst.TOP_ITEM_KQDC_CHECK_URL, dateRange, crowdsTypes[index], itemId, String.valueOf(timestamp), cjzh.getMicrodata("legalityToken"));
		HttpGet httpGet = new HttpGet(url);
		httpGet.setHeader("accept", "*/*");
		httpGet.setHeader("accept-encoding", "gzip, deflate, br");
		httpGet.setHeader("accept-language", "zh-CN,zh;q=0.9");
		httpGet.setHeader("referer", "https://sycm.taobao.com/cc/item_archives?activeKey=customer&dateRange=" + dateRange + "&dateType=day&itemId=" + itemId);
		httpGet.setHeader("sycm-referer", "/cc/item_archives");
		httpGet.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.75 Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(30000).setConnectTimeout(30000).setConnectionRequestTimeout(30000);
		httpGet.setConfig(requestConfig.build());
		HttpResponse httpresponse;
		String name = "topItemKqdcCheck";
		String body = "";
		try {
			httpresponse = httpClient.execute(httpGet);
			HttpEntity entity = httpresponse.getEntity();
			body = EntityUtils.toString(entity, "utf-8");
			String errorjson = "{\"name\":\"" + name + "\",\"body\":\"" + body + "\"}";
			if (StrUtil.isNull(body)) { // 空
				log.error("生意参谋，获取" + name + "：返回的是空字符串");
				this.saveLog(cjzh, "returnnull", errorjson);
				return null;
			}
			if (!StrUtil.isJson(body)) {
				log.error("生意参谋，获取" + name + "：返回的不是json");
				log.error(body);
				this.saveLog(cjzh, "notjson", errorjson);
				return null;
			}
			JSONObject jsonObj = JSONObject.fromObject(body);
			// 出现验证码
			if (jsonObj.has("rgv587_flag") && jsonObj.getString("rgv587_flag").equalsIgnoreCase("sm")) {
				log.error("生意参谋，获取" + name + "：出现滑块！");
				this.saveLog(cjzh, "rgv587_flag", errorjson);
				return " "; // 返回" "，不会触发@Retry，且不会保存到表格存储
			}
			if (!jsonObj.has("code") || jsonObj.getInt("code") != 0) { // 状态不为0，说明接口返回失败
				log.info("生意参谋，获取" + name + "，code错误：" + body);
				this.saveLog(cjzh, "errorcode", errorjson);
				return " "; // 返回" "，不会触发@Retry，且不会保存到表格存储
			}
			if (!jsonObj.has("data")) { // 有可能code == 0，但是没有data
				log.info("生意参谋，获取" + name + "，data错误：" + body);
				this.saveLog(cjzh, "errordata", errorjson);
				return " "; // 返回" "，不会触发@Retry，且不会保存到表格存储
			}
			// boolean data = jsonObj.getBoolean("data");
			return jsonObj.getString("data");
		} catch (Exception e) {
			log.info("生意参谋，获取" + name + "，未知错误：" + body);
			log.error(e.getMessage(), e);
			e.printStackTrace();
			this.saveLog(cjzh, "unknownerror", e.getMessage());
		} finally {
			httpGet.releaseConnection();
		}
		return null;
	}

	/**
	 * 生意参谋 -> 品类 -> 商品360 -> 宝贝详情 -> 客群洞察（选择：单日）
	 * 
	 * @param cjzh 采集账号
	 * @param itemId 宝贝id
	 * @param rq 采集日期
	 * @param index1 类型：0.(基础特征4个)新老占比、1.年龄分布、2.性别占比、3.兴趣爱好、4.(地域2个)地域（市）、5.地域（省）、6.(偏好特征2个)浏览类目偏好、7.浏览品牌偏好、8.(消费特征2个)消费层级、9.淘气值分布
	 * @param index2 类型：0.访问人群、1.搜索人群、2.支付人群
	 * @return json原始数据
	 */
	@Retry
	public String topItemKqdcAll(Cjzh cjzh, String itemId, String rq, int index1, int index2) {
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(abujlbCookieStore.getCookie(cjzh)).build();
		// (基础特征4个)新老占比、年龄分布、性别占比、兴趣爱好、(地域2个)地域（市）、地域（省）、(偏好特征2个)浏览类目偏好、浏览品牌偏好、(消费特征2个)消费层级、淘气值分布
		String[] profileTypes = new String[] { "new_old", "age", "gender", "crowd", "city", "province", "cate_prefer", "brand_prefer", "purchase_level", "tq" };
		String[] crowdsTypes = new String[] { "itmUv", "appSearchUv", "payByrCnt" }; // 访问人群、搜索人群、支付人群

		long timestamp = System.currentTimeMillis(); // 时间戳
		String dateRange = rq + "%7C" + rq;
		String url = String.format(SycmConst.TOP_ITEM_KQDC_ALL_URL, itemId, profileTypes[index1], crowdsTypes[index2], dateRange, String.valueOf(timestamp), cjzh.getMicrodata("legalityToken"));
		HttpGet httpGet = new HttpGet(url);
		httpGet.setHeader("accept", "*/*");
		httpGet.setHeader("accept-encoding", "gzip, deflate, br");
		httpGet.setHeader("accept-language", "zh-CN,zh;q=0.9");
		httpGet.setHeader("referer", "https://sycm.taobao.com/cc/item_archives?activeKey=customer&dateRange=" + dateRange + "&dateType=day&itemId=" + itemId);
		httpGet.setHeader("sycm-referer", "/cc/item_archives");
		httpGet.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.75 Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(30000).setConnectTimeout(30000).setConnectionRequestTimeout(30000);
		httpGet.setConfig(requestConfig.build());
		HttpResponse httpresponse;
		String name = "topItemKqdcAll";
		String body = "";
		try {
			httpresponse = httpClient.execute(httpGet);
			HttpEntity entity = httpresponse.getEntity();
			body = EntityUtils.toString(entity, "utf-8");
			return this.commonAllReturn(cjzh, name, body);
		} catch (Exception e) {
			log.info("生意参谋，获取" + name + "，未知错误：" + body);
			log.error(e.getMessage(), e);
			e.printStackTrace();
			this.saveLog(cjzh, "unknownerror", e.getMessage());
		} finally {
			httpGet.releaseConnection();
		}
		return null;
	}

	/**
	 * 生意参谋 -> 品类 -> 商品360 -> 宝贝详情 -> 管理搭配 -> 掌柜推荐-引导详情（选择：未来7天）
	 * 
	 * @param cjzh 采集账号
	 * @param itemId 宝贝id
	 * @param rq 采集日期
	 * @return json原始数据
	 */
	@Retry
	public String topItemGldpZgtj(Cjzh cjzh, String itemId, String rq) {
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(abujlbCookieStore.getCookie(cjzh)).build();
		long timestamp = System.currentTimeMillis(); // 时间戳
		Date rqDate = DateUtil.parseDate(rq);
		String rq1 = DateUtil.formatDate(new Date(rqDate.getTime() - 24 * 60 * 60 * 1000L)); // 昨天，86400000L = 24 * 60 * 60 * 1000L
		String rq2 = DateUtil.formatDate(new Date(rqDate.getTime() - 30 * 24 * 60 * 60 * 1000L)); //
		String dateRange = rq2 + "%7C" + rq1;
		String url = String.format(SycmConst.TOP_ITEM_GLDP_ZGTJ_URL, SycmConst.DEFAULT_PAGESIZE, SycmConst.DEFAULT_PAGE, itemId, String.valueOf(timestamp), cjzh.getMicrodata("legalityToken"));
		HttpGet httpGet = new HttpGet(url);
		httpGet.setHeader("accept", "*/*");
		httpGet.setHeader("accept-encoding", "gzip, deflate, br");
		httpGet.setHeader("accept-language", "zh-CN,zh;q=0.9");
		httpGet.setHeader("referer", "https://sycm.taobao.com/cc/item_archives?activeKey=related&dateRange=" + dateRange + "&dateType=recent30&itemId=" + itemId);
		httpGet.setHeader("sycm-referer", "/cc/item_archives");
		httpGet.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.75 Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(30000).setConnectTimeout(30000).setConnectionRequestTimeout(30000);
		httpGet.setConfig(requestConfig.build());
		HttpResponse httpresponse;
		String name = "topItemGldpZgtj";
		String body = "";
		try {
			httpresponse = httpClient.execute(httpGet);
			HttpEntity entity = httpresponse.getEntity();
			body = EntityUtils.toString(entity, "utf-8");
			return this.commonAllReturn(cjzh, name, body);
		} catch (Exception e) {
			log.info("生意参谋，获取" + name + "，未知错误：" + body);
			log.error(e.getMessage(), e);
			e.printStackTrace();
			this.saveLog(cjzh, "unknownerror", e.getMessage());
		} finally {
			httpGet.releaseConnection();
		}
		return null;
	}

	/**
	 * 生意参谋 -> 品类 -> 商品360 -> 宝贝详情 -> 管理搭配 -> 连带商品推荐（选择：最近30天(隐含)）
	 * 
	 * @param cjzh 采集账号
	 * @param itemId 宝贝id
	 * @param rq 采集日期
	 * @return json原始数据
	 */
	@Retry
	public String topItemGldpLdsp(Cjzh cjzh, String itemId, String rq) {
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(abujlbCookieStore.getCookie(cjzh)).build();
		long timestamp = System.currentTimeMillis(); // 时间戳
		Date rqDate = DateUtil.parseDate(rq);
		String rq1 = DateUtil.formatDate(new Date(rqDate.getTime() - 24 * 60 * 60 * 1000L)); // 昨天，86400000L = 24 * 60 * 60 * 1000L
		String rq2 = DateUtil.formatDate(new Date(rqDate.getTime() - 30 * 24 * 60 * 60 * 1000L)); //
		String dateRange = rq2 + "%7C" + rq1;
		String url = String.format(SycmConst.TOP_ITEM_GLDP_LDSP_URL, dateRange, SycmConst.DEFAULT_PAGESIZE, SycmConst.DEFAULT_PAGE, itemId, String.valueOf(timestamp),
				cjzh.getMicrodata("legalityToken"));
		HttpGet httpGet = new HttpGet(url);
		httpGet.setHeader("accept", "*/*");
		httpGet.setHeader("accept-encoding", "gzip, deflate, br");
		httpGet.setHeader("accept-language", "zh-CN,zh;q=0.9");
		httpGet.setHeader("referer", "https://sycm.taobao.com/cc/item_archives?activeKey=related&dateRange=" + dateRange + "&dateType=recent30&itemId=" + itemId);
		httpGet.setHeader("sycm-referer", "/cc/item_archives");
		httpGet.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.75 Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(30000).setConnectTimeout(30000).setConnectionRequestTimeout(30000);
		httpGet.setConfig(requestConfig.build());
		HttpResponse httpresponse;
		String name = "topItemGldpLdsp";
		String body = "";
		try {
			httpresponse = httpClient.execute(httpGet);
			HttpEntity entity = httpresponse.getEntity();
			body = EntityUtils.toString(entity, "utf-8");
			return this.commonAllReturn(cjzh, name, body);
		} catch (Exception e) {
			log.info("生意参谋，获取" + name + "，未知错误：" + body);
			log.error(e.getMessage(), e);
			e.printStackTrace();
			this.saveLog(cjzh, "unknownerror", e.getMessage());
		} finally {
			httpGet.releaseConnection();
		}
		return null;
	}

	/**
	 * 生意参谋 -> 品类 -> 商品360 -> 宝贝详情 -> 服务分析 -> 重点服务指标总览（选择：最近30天）
	 * 
	 * @param cjzh 采集账号
	 * @param itemId 宝贝id
	 * @param rq 采集日期
	 * @return json原始数据
	 */
	@Retry
	public String topItemFwfxZl(Cjzh cjzh, String itemId, String rq) {
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(abujlbCookieStore.getCookie(cjzh)).build();
		long timestamp = System.currentTimeMillis(); // 时间戳
		Date rqDate = DateUtil.parseDate(rq);
		String rq2 = DateUtil.formatDate(new Date(rqDate.getTime() - 29 * 24 * 60 * 60 * 1000L)); //
		String dateRange = rq2 + "%7C" + rq;
		String url = String.format(SycmConst.TOP_ITEM_FWFX_ZL_URL, dateRange, itemId, String.valueOf(timestamp), cjzh.getMicrodata("legalityToken"));
		HttpGet httpGet = new HttpGet(url);
		httpGet.setHeader("accept", "*/*");
		httpGet.setHeader("accept-encoding", "gzip, deflate, br");
		httpGet.setHeader("accept-language", "zh-CN,zh;q=0.9");
		httpGet.setHeader("referer", "https://sycm.taobao.com/cc/item_archives?activeKey=service&dateRange=" + dateRange + "&dateType=recent30&itemId=" + itemId);
		httpGet.setHeader("sycm-referer", "/cc/item_archives");
		httpGet.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.75 Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(30000).setConnectTimeout(30000).setConnectionRequestTimeout(30000);
		httpGet.setConfig(requestConfig.build());
		HttpResponse httpresponse;
		String name = "topItemFwfxZl";
		String body = "";
		try {
			httpresponse = httpClient.execute(httpGet);
			HttpEntity entity = httpresponse.getEntity();
			body = EntityUtils.toString(entity, "utf-8");
			return this.commonAllReturn(cjzh, name, body);
		} catch (Exception e) {
			log.info("生意参谋，获取" + name + "，未知错误：" + body);
			log.error(e.getMessage(), e);
			e.printStackTrace();
			this.saveLog(cjzh, "unknownerror", e.getMessage());
		} finally {
			httpGet.releaseConnection();
		}
		return null;
	}

	/**
	 * 生意参谋 -> 品类 -> 商品360 -> 宝贝详情 -> 服务分析 -> 服务指标趋势（选择：单日）
	 * 
	 * @param cjzh 采集账号
	 * @param itemId 宝贝id
	 * @param rq 采集日期
	 * @return json原始数据
	 */
	@Retry
	public String topItemFwfxQs(Cjzh cjzh, String itemId, String rq) {
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(abujlbCookieStore.getCookie(cjzh)).build();
		// "维权指标": ["rfdSucCnt", "rfdSucAmt", "rfdSucQaCnt", "rfdDsptCnt", "rfdShippedAmt", "rfdUnShippedAmt", "rfdSucGoodsAmt"],
		// "评价指标": ["clkRatePc", "clkRateApp", "pictureReviewCnt", "reviewCnt", "activeReviewCnt", "badReviewCnt", "goodReviewCnt", "oldBuyerBadReviewCnt", "oldBuyerGoodReviewCnt"],
		// "交易指标": ["payRatePc", "payRateApp", "payOrdCntPc", "payOrdCntApp", "payOrdAmtPc", "payOrdAmtWl"]
		long timestamp = System.currentTimeMillis(); // 时间戳
		String dateRange = rq + "%7C" + rq;
		String indexCode = "rfdSucCnt,rfdSucAmt,pictureReviewCnt,payOrdCntPc,payOrdAmtWl"; // （最多5个指标）
		String url = String.format(SycmConst.TOP_ITEM_FWFX_QS_URL, dateRange, indexCode, itemId, String.valueOf(timestamp), cjzh.getMicrodata("legalityToken"));
		try {
			url = String.format(SycmConst.TOP_ITEM_FWFX_QS_URL, dateRange, URLEncoder.encode(indexCode, "UTF-8"), itemId, String.valueOf(timestamp), cjzh.getMicrodata("legalityToken"));
		} catch (UnsupportedEncodingException e1) {
			e1.printStackTrace();
			log.error(e1.getMessage(), e1);
		}
		HttpGet httpGet = new HttpGet(url);
		httpGet.setHeader("accept", "*/*");
		httpGet.setHeader("accept-encoding", "gzip, deflate, br");
		httpGet.setHeader("accept-language", "zh-CN,zh;q=0.9");
		httpGet.setHeader("referer", "https://sycm.taobao.com/cc/item_archives?activeKey=service&dateRange=" + dateRange + "&dateType=day&itemId=" + itemId);
		httpGet.setHeader("sycm-referer", "/cc/item_archives");
		httpGet.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.75 Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(30000).setConnectTimeout(30000).setConnectionRequestTimeout(30000);
		httpGet.setConfig(requestConfig.build());
		HttpResponse httpresponse;
		String name = "topItemFwfxQs";
		String body = "";
		try {
			httpresponse = httpClient.execute(httpGet);
			HttpEntity entity = httpresponse.getEntity();
			body = EntityUtils.toString(entity, "utf-8");
			return this.commonAllReturn(cjzh, name, body);
		} catch (Exception e) {
			log.info("生意参谋，获取" + name + "，未知错误：" + body);
			log.error(e.getMessage(), e);
			e.printStackTrace();
			this.saveLog(cjzh, "unknownerror", e.getMessage());
		} finally {
			httpGet.releaseConnection();
		}
		return null;
	}

	/**
	 * 生意参谋 -> 品类 -> 商品360 -> 宝贝详情 -> 服务分析 -> 退款原因分析（选择：单日、全部订单状态、全部维权类型）
	 * 
	 * @param cjzh 采集账号
	 * @param itemId 宝贝id
	 * @param rq 采集日期
	 * @return json原始数据
	 */
	@Retry
	public String topItemFwfxTkyy(Cjzh cjzh, String itemId, String rq) {
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(abujlbCookieStore.getCookie(cjzh)).build();
		long timestamp = System.currentTimeMillis(); // 时间戳
		String dateRange = rq + "%7C" + rq;
		String url = String.format(SycmConst.TOP_ITEM_FWFX_TKYY_URL, dateRange, itemId, String.valueOf(timestamp), cjzh.getMicrodata("legalityToken"));
		HttpGet httpGet = new HttpGet(url);
		httpGet.setHeader("accept", "*/*");
		httpGet.setHeader("accept-encoding", "gzip, deflate, br");
		httpGet.setHeader("accept-language", "zh-CN,zh;q=0.9");
		httpGet.setHeader("referer", "https://sycm.taobao.com/cc/item_archives?activeKey=service&dateRange=" + dateRange + "&dateType=day&itemId=" + itemId);
		httpGet.setHeader("sycm-referer", "/cc/item_archives");
		httpGet.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.75 Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(30000).setConnectTimeout(30000).setConnectionRequestTimeout(30000);
		httpGet.setConfig(requestConfig.build());
		HttpResponse httpresponse;
		String name = "topItemFwfxTkyy";
		String body = "";
		try {
			httpresponse = httpClient.execute(httpGet);
			HttpEntity entity = httpresponse.getEntity();
			body = EntityUtils.toString(entity, "utf-8");
			return this.commonAllReturn(cjzh, name, body);
		} catch (Exception e) {
			log.info("生意参谋，获取" + name + "，未知错误：" + body);
			log.error(e.getMessage(), e);
			e.printStackTrace();
			this.saveLog(cjzh, "unknownerror", e.getMessage());
		} finally {
			httpGet.releaseConnection();
		}
		return null;
	}

	/**
	 * 生意参谋 -> 品类 -> 商品360 -> 宝贝详情 -> 服务分析 -> 商品评分分析（选择：单日）
	 * 
	 * @param cjzh 采集账号
	 * @param itemId 宝贝id
	 * @param rq 采集日期
	 * @return json原始数据
	 */
	@Retry
	public String topItemFwfxPjfx(Cjzh cjzh, String itemId, String rq) {
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(abujlbCookieStore.getCookie(cjzh)).build();
		long timestamp = System.currentTimeMillis(); // 时间戳
		String dateRange = rq + "%7C" + rq;
		String url = String.format(SycmConst.TOP_ITEM_FWFX_PJFX_URL, dateRange, itemId, String.valueOf(timestamp), cjzh.getMicrodata("legalityToken"));
		HttpGet httpGet = new HttpGet(url);
		httpGet.setHeader("accept", "*/*");
		httpGet.setHeader("accept-encoding", "gzip, deflate, br");
		httpGet.setHeader("accept-language", "zh-CN,zh;q=0.9");
		httpGet.setHeader("referer", "https://sycm.taobao.com/cc/item_archives?activeKey=service&dateRange=" + dateRange + "&dateType=day&itemId=" + itemId);
		httpGet.setHeader("sycm-referer", "/cc/item_archives");
		httpGet.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.75 Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(30000).setConnectTimeout(30000).setConnectionRequestTimeout(30000);
		httpGet.setConfig(requestConfig.build());
		HttpResponse httpresponse;
		String name = "topItemFwfxPjfx";
		String body = "";
		try {
			httpresponse = httpClient.execute(httpGet);
			HttpEntity entity = httpresponse.getEntity();
			body = EntityUtils.toString(entity, "utf-8");
			return this.commonAllReturn(cjzh, name, body);
		} catch (Exception e) {
			log.info("生意参谋，获取" + name + "，未知错误：" + body);
			log.error(e.getMessage(), e);
			e.printStackTrace();
			this.saveLog(cjzh, "unknownerror", e.getMessage());
		} finally {
			httpGet.releaseConnection();
		}
		return null;
	}

	/**
	 * 生意参谋 -> 品类 -> 商品360 -> 宝贝详情 -> 服务分析 -> 评价内容分析（选择：单日）
	 * 
	 * @param cjzh 采集账号
	 * @param itemId 宝贝id
	 * @param rq 采集日期
	 * @return json原始数据
	 */
	@Retry
	public String topItemFwfxPjnrfx(Cjzh cjzh, String itemId, String rq) {
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(abujlbCookieStore.getCookie(cjzh)).build();
		long timestamp = System.currentTimeMillis(); // 时间戳
		String dateRange = rq + "%7C" + rq;
		String url = String.format(SycmConst.TOP_ITEM_FWFX_PJNRFX_URL, dateRange, itemId, String.valueOf(timestamp), cjzh.getMicrodata("legalityToken"));
		HttpGet httpGet = new HttpGet(url);
		httpGet.setHeader("accept", "*/*");
		httpGet.setHeader("accept-encoding", "gzip, deflate, br");
		httpGet.setHeader("accept-language", "zh-CN,zh;q=0.9");
		httpGet.setHeader("referer", "https://sycm.taobao.com/cc/item_archives?activeKey=service&dateRange=" + dateRange + "&dateType=day&itemId=" + itemId);
		httpGet.setHeader("sycm-referer", "/cc/item_archives");
		httpGet.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.75 Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(30000).setConnectTimeout(30000).setConnectionRequestTimeout(30000);
		httpGet.setConfig(requestConfig.build());
		HttpResponse httpresponse;
		String name = "topItemFwfxPjnrfx";
		String body = "";
		try {
			httpresponse = httpClient.execute(httpGet);
			HttpEntity entity = httpresponse.getEntity();
			body = EntityUtils.toString(entity, "utf-8");
			return this.commonAllReturn(cjzh, name, body);
		} catch (Exception e) {
			log.info("生意参谋，获取" + name + "，未知错误：" + body);
			log.error(e.getMessage(), e);
			e.printStackTrace();
			this.saveLog(cjzh, "unknownerror", e.getMessage());
		} finally {
			httpGet.releaseConnection();
		}
		return null;
	}

	/**
	 * 生意参谋 -> 品类 -> 商品360 -> 宝贝详情 -> 服务分析 -> 评价内容分析 -> 评价内容（选择：单日）
	 * 
	 * @param cjzh 采集账号
	 * @param itemId 宝贝id
	 * @param rq 采集日期
	 * @param skuId 
	 * @return json原始数据
	 */
	@Retry
	public String topItemFwfxPjnrfxNr(Cjzh cjzh, String itemId, String rq, String skuId) {
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(abujlbCookieStore.getCookie(cjzh)).build();
		long timestamp = System.currentTimeMillis(); // 时间戳
		String dateRange = rq + "%7C" + rq;
		String url = String.format(SycmConst.TOP_ITEM_FWFX_PJNRFX_NR_URL, dateRange, itemId, skuId, SycmConst.DEFAULT_PAGE, String.valueOf(timestamp), cjzh.getMicrodata("legalityToken"));
		HttpGet httpGet = new HttpGet(url);
		httpGet.setHeader("accept", "*/*");
		httpGet.setHeader("accept-encoding", "gzip, deflate, br");
		httpGet.setHeader("accept-language", "zh-CN,zh;q=0.9");
		httpGet.setHeader("referer", "https://sycm.taobao.com/cc/item_archives?activeKey=service&dateRange=" + dateRange + "&dateType=day&itemId=" + itemId);
		httpGet.setHeader("sycm-referer", "/cc/item_archives");
		httpGet.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.75 Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(30000).setConnectTimeout(30000).setConnectionRequestTimeout(30000);
		httpGet.setConfig(requestConfig.build());
		HttpResponse httpresponse;
		String name = "topItemFwfxPjnrfxNr";
		String body = "";
		try {
			httpresponse = httpClient.execute(httpGet);
			HttpEntity entity = httpresponse.getEntity();
			body = EntityUtils.toString(entity, "utf-8");
			return this.commonAllReturn(cjzh, name, body);
		} catch (Exception e) {
			log.info("生意参谋，获取" + name + "，未知错误：" + body);
			log.error(e.getMessage(), e);
			e.printStackTrace();
			this.saveLog(cjzh, "unknownerror", e.getMessage());
		} finally {
			httpGet.releaseConnection();
		}
		return null;
	}

	/**
	 * 公共返回内容
	 * 
	 * @param name 名称
	 * @param body josn内容
	 */
	@SuppressWarnings("unused")
	private String commonReturn(Cjzh cjzh, String name, String body) {
		try {
			String errorjson = "{\"name\":\"" + name + "\",\"body\":\"" + body + "\"}";
			if (StrUtil.isNull(body)) { // 空
				log.error("生意参谋，获取" + name + "：返回的是空字符串");
				this.saveLog(cjzh, "returnnull", errorjson);
				return null;
			}
			if (!StrUtil.isJson(body)) {
				log.error("生意参谋，获取" + name + "：返回的不是json");
				log.error(body);
				this.saveLog(cjzh, "notjson", errorjson);
				return null;
			}
			JSONObject jsonObj = JSONObject.fromObject(body);
			// 出现验证码
			if (jsonObj.has("rgv587_flag") && jsonObj.getString("rgv587_flag").equalsIgnoreCase("sm")) {
				log.error("生意参谋，获取" + name + "：出现滑块！");
				this.saveLog(cjzh, "rgv587_flag", errorjson);
				return " "; // 返回" "，不会触发@Retry，且不会保存到表格存储
			}
			if (!jsonObj.has("code") || jsonObj.getInt("code") != 0) { // 状态不为0，说明接口返回失败
				log.info("生意参谋，获取" + name + "，code错误：" + body);
				this.saveLog(cjzh, "errorcode", errorjson);
				return " "; // 返回" "，不会触发@Retry，且不会保存到表格存储
			}
			if (!jsonObj.has("data")) { // 有可能code == 0，但是没有data
				log.info("生意参谋，获取" + name + "，data错误：" + body);
				this.saveLog(cjzh, "errordata", errorjson);
				return " "; // 返回" "，不会触发@Retry，且不会保存到表格存储
			}
			try { // data原本是个JSONObject，但也有可能是JSONArray
				if (jsonObj.getJSONObject("data").isNullObject()) {
					log.info("生意参谋，获取" + name + "，data错误2：" + body);
					this.saveLog(cjzh, "errordata", errorjson);
					return " "; // 返回" "，不会触发@Retry，且不会保存到表格存储
				}
				JSONObject dataObj = jsonObj.getJSONObject("data");
				return dataObj.toString();
			} catch (Exception e) {
				log.info("生意参谋，获取" + name + "，未知错误2：" + body);
				log.error(e.getMessage(), e);
				this.saveLog(cjzh, "unknownerror", e.getMessage());
				return jsonObj.getString("data");
			}
			// if (!jsonObj.has("code") || jsonObj.getInt("code") != 0 || !jsonObj.has("data") || jsonObj.getJSONObject("data").isNullObject()) { // 状态不为0，说明接口返回失败
			// log.info("生意参谋，获取" + name + "，code错误：" + body);
			// return null;
			// }
			// JSONObject dataObj = jsonObj.getJSONObject("data");
			// return dataObj.toString();
		} catch (Exception e) {
			log.info("生意参谋，获取" + name + "，未知错误：" + body);
			log.error(e.getMessage(), e);
			e.printStackTrace();
			this.saveLog(cjzh, "unknownerror", e.getMessage());
		}
		return null;
	}

	/**
	 * 公共返回内容
	 * 
	 * @param name 名称
	 * @param body josn内容
	 */
	@SuppressWarnings("unused")
	private String commonArrayReturn(Cjzh cjzh, String name, String body) {
		try {
			String errorjson = "{\"name\":\"" + name + "\",\"body\":\"" + body + "\"}";
			if (StrUtil.isNull(body)) { // 空
				log.error("生意参谋，获取" + name + "：返回的是空字符串");
				this.saveLog(cjzh, "returnnull", errorjson);
				return null;
			}
			if (!StrUtil.isJson(body)) {
				log.error("生意参谋，获取" + name + "：返回的不是json");
				log.error(body);
				this.saveLog(cjzh, "notjson", errorjson);
				return null;
			}
			JSONObject jsonObj = JSONObject.fromObject(body);
			// 出现验证码
			if (jsonObj.has("rgv587_flag") && jsonObj.getString("rgv587_flag").equalsIgnoreCase("sm")) {
				log.error("生意参谋，获取" + name + "：出现滑块！");
				this.saveLog(cjzh, "rgv587_flag", errorjson);
				return " "; // 返回" "，不会触发@Retry，且不会保存到表格存储
			}
			if (!jsonObj.has("code") || jsonObj.getInt("code") != 0) { // 状态不为0，说明接口返回失败
				log.info("生意参谋，获取" + name + "，code错误：" + body);
				this.saveLog(cjzh, "errorcode", errorjson);
				return " "; // 返回" "，不会触发@Retry，且不会保存到表格存储
			}
			if (!jsonObj.has("data")) { // 有可能code == 0，但是没有data
				log.info("生意参谋，获取" + name + "，data错误：" + body);
				this.saveLog(cjzh, "errordata", errorjson);
				return " "; // 返回" "，不会触发@Retry，且不会保存到表格存储
			}
			try { // data原本是个JSONArray，但也有可能是JSONObject
				if (!jsonObj.getJSONArray("data").isArray()) {
					log.info("生意参谋，获取" + name + "，data错误2：" + body);
					this.saveLog(cjzh, "errordata", errorjson);
					return " "; // 返回" "，不会触发@Retry，且不会保存到表格存储
				}
				JSONArray dataArr = jsonObj.getJSONArray("data");
				return dataArr.toString();
			} catch (Exception e) {
				log.info("生意参谋，获取" + name + "，未知错误2：" + body);
				log.error(e.getMessage(), e);
				this.saveLog(cjzh, "unknownerror", e.getMessage());
				return jsonObj.getString("data");
			}
			// // if (!jsonObj.has("code") || jsonObj.getInt("code") != 0 || !jsonObj.has("data") || jsonObj.getJSONArray("data").isEmpty()) { // 状态不为0，说明接口返回失败
			// if (!jsonObj.has("code") || jsonObj.getInt("code") != 0 || !jsonObj.has("data") || !jsonObj.getJSONArray("data").isArray()) { // 状态不为0，说明接口返回失败
			// log.info("生意参谋，获取" + name + "，code错误：" + body);
			// return null;
			// }
			// JSONArray dataArr = jsonObj.getJSONArray("data");
			// return dataArr.toString();
		} catch (Exception e) {
			log.info("生意参谋，获取" + name + "，未知错误：" + body);
			log.error(e.getMessage(), e);
			e.printStackTrace();
			this.saveLog(cjzh, "unknownerror", e.getMessage());
		}
		return null;
	}

	/**
	 * 公共返回内容
	 * 
	 * @param name 名称
	 * @param body josn内容
	 */
	private String commonAllReturn(Cjzh cjzh, String name, String body) {
		try {
			String errorjson = "{\"name\":\"" + name + "\",\"body\":\"" + body + "\"}";
			if (StrUtil.isNull(body)) { // 空
				log.error("生意参谋，获取" + name + "：返回的是空字符串");
				this.saveLog(cjzh, "returnnull", errorjson);
				return null;
			}
			if (!StrUtil.isJson(body)) {
				log.error("生意参谋，获取" + name + "：返回的不是json");
				log.error(body);
				this.saveLog(cjzh, "notjson", errorjson);
				return null;
			}
			JSONObject jsonObj = JSONObject.fromObject(body);
			// 出现验证码
			if (jsonObj.has("rgv587_flag") && jsonObj.getString("rgv587_flag").equalsIgnoreCase("sm")) {
				log.error("生意参谋，获取" + name + "：出现滑块！");
				this.saveLog(cjzh, "rgv587_flag", errorjson);
				return " "; // 返回" "，不会触发@Retry，且不会保存到表格存储
			}
			if (!jsonObj.has("code") || jsonObj.getInt("code") != 0) { // 状态不为0，说明接口返回失败
				log.info("生意参谋，获取" + name + "，code错误：" + body);
				this.saveLog(cjzh, "errorcode", errorjson);
				return " "; // 返回" "，不会触发@Retry，且不会保存到表格存储
			}
			if (!jsonObj.has("data")) { // 有可能code == 0，但是没有data
				log.info("生意参谋，获取" + name + "，data错误：" + body);
				this.saveLog(cjzh, "errordata", errorjson);
				return " "; // 返回" "，不会触发@Retry，且不会保存到表格存储
			}
			return jsonObj.getString("data");
		} catch (Exception e) {
			log.info("生意参谋，获取" + name + "，未知错误：" + body);
			log.error(e.getMessage(), e);
			e.printStackTrace();
			this.saveLog(cjzh, "unknownerror", e.getMessage());
		}
		return null;
	}

	// mian方法
	public static void main(String[] args) {
		try {
			long timestamp = System.currentTimeMillis(); // 时间戳
			String url = String.format(SycmConst.TOP_ITEMLIST_URL, 100, 1, "itmUv,payAmt,payRate", String.valueOf(timestamp), "aaaadddd");
			System.out.println(url);
			url = String.format(SycmConst.TOP_ITEMLIST_URL, 100, 1, URLEncoder.encode("itmUv,payAmt,payRate", "UTF-8"), String.valueOf(timestamp), "aaaadddd");
			System.out.println(url);

			String rq = "2020-11-16";
			Date rqDate = DateUtil.parseDate(rq);
			String rq1 = DateUtil.formatDate(new Date(rqDate.getTime() - 24 * 60 * 60 * 1000L)); // 昨天，86400000L = 24 * 60 * 60 * 1000L
			String rq2 = DateUtil.formatDate(new Date(rqDate.getTime() - 30 * 24 * 60 * 60 * 1000L)); //
			System.out.println(rq1);
			System.out.println(rq2);

			String aaa = "{\"traceId\":\"0b105ae416056672164924009e59c3\",\"code\":0,\"data\":[],\"message\":\"操作成功\"}";
			JSONObject jsonObj = JSONObject.fromObject(aaa);
			System.out.println(jsonObj.has("data"));
			System.out.println(jsonObj.getJSONArray("data").isEmpty());
			System.out.println(jsonObj.getJSONArray("data").isArray());
			JSONArray dataArr = jsonObj.getJSONArray("data");
			System.out.println(dataArr.toString());
			System.out.println();

			String bbb = "{\"traceId\":\"0b105ae416056672164924009e59c3\",\"code\":0,\"data\":{},\"message\":\"操作成功\"}";
			JSONObject jsonObj2 = JSONObject.fromObject(bbb);
			System.out.println(jsonObj2.has("data"));
			System.out.println(jsonObj2.getJSONObject("data").isNullObject());
			System.out.println(jsonObj2.getJSONObject("data").isEmpty());
			JSONObject dataObj = jsonObj2.getJSONObject("data");
			System.out.println(dataObj.toString());
			System.out.println();

			String ccc = "{\"traceId\":\"0b11686e16056706609438257e3011\",\"code\":0,\"data\":[],\"message\":\"操作成功\"}";
			JSONObject jsonObjc = JSONObject.fromObject(ccc);
			System.out.println(jsonObjc.has("data"));
			// System.out.println(jsonObjc.getJSONObject("data"));
			System.out.println(jsonObjc.getJSONArray("data"));
			// Object dataObjc = jsonObjc.get("data");

		} catch (UnsupportedEncodingException e1) {
			e1.printStackTrace();
			log.error(e1.getMessage(), e1);
		}
	}

}
