package com.abujlb.jyrb;

import com.abujlb.AbujlbBeanFactory;
import com.abujlb.annotation.MqProcessor;
import com.abujlb.jyrb.bean.ZtcMsg;
import com.abujlb.jyrb.util.Zdcj8JysjDateUtil;
import com.abujlb.mq.JobMessage;
import com.abujlb.mq.MqProcessorInterface;
import com.abujlb.zdcj8.service.HyrqbdService;
import com.google.gson.Gson;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;

@MqProcessor(type = "zdcj8hyrqbdprocesser")
public class HyrqbdProcessor implements MqProcessorInterface {
    final Gson gson = new Gson();
    private static final Logger log = Logger.getLogger(HyrqbdProcessor.class);
    @Autowired
    private AbujlbBeanFactory beanFactory;

    @Override
    public boolean process(JobMessage msg) {
        log.info("=============>>>>>zdcj8hyrqbd");
        log.info("当前时间：" + Zdcj8JysjDateUtil.getCurrtime());
        log.info("zdcj8hyrqbd-->消息体：" + msg.getMsgBody());

        ZtcMsg ztcMsg = null;
        try {
            ztcMsg = gson.fromJson(msg.getMsgBody(), ZtcMsg.class);
        } catch (Exception e) {
            return true;
        }
        try {
            HyrqbdService service = beanFactory.getBean(HyrqbdService.class, ztcMsg);
            return service.process();
        } catch (Throwable e) {
            log.error(e.getMessage(), e);
            return true;
        }
    }
}
