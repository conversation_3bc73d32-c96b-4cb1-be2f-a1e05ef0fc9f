package com.abujlb.jzts2.tsdao;

import com.abujlb.dao.TsDao;
import com.abujlb.jzts2.pojo.ScphUserid;
import com.abujlb.util.StringUtil;
import com.alicloud.openservices.tablestore.SyncClient;
import com.alicloud.openservices.tablestore.model.*;
import com.alicloud.openservices.tablestore.model.search.SearchQuery;
import com.alicloud.openservices.tablestore.model.search.SearchRequest;
import com.alicloud.openservices.tablestore.model.search.SearchResponse;
import com.alicloud.openservices.tablestore.model.search.query.BoolQuery;
import com.alicloud.openservices.tablestore.model.search.query.Query;
import com.alicloud.openservices.tablestore.model.search.query.TermQuery;
import com.alicloud.openservices.tablestore.model.search.sort.FieldSort;
import com.alicloud.openservices.tablestore.model.search.sort.Sort;
import com.alicloud.openservices.tablestore.model.search.sort.SortOrder;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * abujlb4.scph_userid 表格存储
 *
 * <AUTHOR>
 * @date 2023-04-13 19:22:50
 */
@Component
public class ScphUseridTsDao {

    private static final String TABLE_NAME = "scph_userid";
    private static final String INDEX_NAME = "scph_userid_index"; // 索引
    /***************** 主键 **************************/
    private static final String COLUMN_newuserid = "newuserid"; // 主键：加密的userid string类型
    private static final String COLUMN_dpmc = "dpmc"; // 店铺名称
    private static final String COLUMN_userid = "userid"; // userid
    private static final String COLUMN_b2c = "b2c"; //
    private static final String COLUMN_dpurl = "dpurl"; // 店铺链接
    private static final String COLUMN_type = "type"; // 类型
    private static final String COLUMN_updatetime = "updatetime"; // 更新时间
    private static final String COLUMN_logo = "logo"; // 店铺logo
    private static final String COLUMN_shopid = "shopid"; // 店铺id
    private static Logger log = Logger.getLogger(ScphUseridTsDao.class);
    private static String TSINSTANCE_NAME = "abujlb4"; // 表格存储实例名
    @Autowired
    private TsDao tsDao;

    /**
     * index多元索引 -> 精确查询
     *
     * @param 精准匹配：条件 userid
     * @return String
     * @参考 https://help.aliyun.com/document_detail/100416.html
     */
    public String termQuery(String userid) {
        SyncClient client = null;
        try {
            if (StringUtils.isBlank(userid)) {
                return null;
            }
            client = tsDao.getClient(TSINSTANCE_NAME);
            // 精准匹配：条件1
            TermQuery termQuery1 = new TermQuery();
            termQuery1.setFieldName(COLUMN_userid);
            termQuery1.setTerm(ColumnValue.fromString(userid));

            SearchQuery searchQuery = new SearchQuery();
            searchQuery.setQuery(termQuery1);
            // searchQuery.setGetTotalCount(true); // 返回总数

            SearchRequest searchRequest = new SearchRequest(TABLE_NAME, INDEX_NAME, searchQuery);
            SearchRequest.ColumnsToGet columnsToGet = new SearchRequest.ColumnsToGet();
            // columnsToGet.setReturnAll(true); // 设置返回所有列
            columnsToGet.setColumns(Arrays.asList(COLUMN_newuserid, COLUMN_userid, COLUMN_dpmc)); // 设置为返回指定列。
            searchRequest.setColumnsToGet(columnsToGet);
            // 循环读取
            while (true) {
                SearchResponse searchResponse = client.search(searchRequest);
                if (!searchResponse.isAllSuccess()) {
                    throw new RuntimeException("not all success");
                }
                for (Row row : searchResponse.getRows()) {
                    if (row == null || row.isEmpty()) {
                        continue;
                    }
                    String _newuserid = row.getPrimaryKey().getPrimaryKeyColumn(COLUMN_newuserid).getValue().asString();
                    if (!StringUtil.isNull2(_newuserid)) {
                        return _newuserid;
                    }
                }
                // 可检查NextToken是否为空，若不为空，可通过NextToken继续读取。
                if (searchResponse.getNextToken() != null) {
                    searchRequest.getSearchQuery().setToken(searchResponse.getNextToken()); // 把token设置到下一次请求中
                } else {
                    break;
                }
            }
        } catch (Exception e) {
            // e.printStackTrace();
            log.error(e.getMessage(), e);
        }
        return null;
    }

    /**
     * index多元索引 -> 多条件组合查询
     *
     * @param 精准匹配：条件1 userid
     * @return List<String>
     * @参考 https://help.aliyun.com/document_detail/100422.html
     */
    public String getIndexRows(String userid) {
        SyncClient client = null;
        try {
            client = tsDao.getClient(TSINSTANCE_NAME);
            // 精准匹配：条件1
            TermQuery termQuery1 = new TermQuery();
            termQuery1.setFieldName(COLUMN_userid);
            termQuery1.setTerm(ColumnValue.fromString(userid));

            SearchQuery searchQuery = new SearchQuery();
            // 条件组合
            List<Query> queryList = new ArrayList<>();
            queryList.add(termQuery1);
            // MustQueries组合所有条件，and关系，满足所有条件
            BoolQuery boolQuery = new BoolQuery();
            boolQuery.setMustQueries(queryList);

            searchQuery.setQuery(boolQuery);
            // searchQuery.setGetTotalCount(true); // 返回总数

            // 设置排序：按照xx正序
            FieldSort fieldSort = new FieldSort(COLUMN_newuserid);
            fieldSort.setOrder(SortOrder.ASC); // 正序
            searchQuery.setSort(new Sort(Arrays.asList(fieldSort)));

            SearchRequest searchRequest = new SearchRequest(TABLE_NAME, INDEX_NAME, searchQuery);

            SearchRequest.ColumnsToGet columnsToGet = new SearchRequest.ColumnsToGet();
            // columnsToGet.setReturnAll(true); // 设置返回所有列
            columnsToGet.setColumns(Arrays.asList(COLUMN_newuserid, COLUMN_userid, COLUMN_dpmc)); // 设置为返回指定列。
            searchRequest.setColumnsToGet(columnsToGet);

            // 循环读取
            while (true) {
                SearchResponse searchResponse = client.search(searchRequest);
                if (!searchResponse.isAllSuccess()) {
                    throw new RuntimeException("not all success");
                }
                for (Row row : searchResponse.getRows()) {
                    if (row == null || row.isEmpty()) {
                        continue;
                    }
                    String _newuserid = row.getPrimaryKey().getPrimaryKeyColumn(COLUMN_newuserid).getValue().asString();
                    /*
                     * String _userid = ""; if (row.getLatestColumn(COLUMN_userid) != null && row.getLatestColumn(COLUMN_userid).getValue() != null) { _userid =
                     * row.getLatestColumn(COLUMN_userid).getValue().asString(); }
                     */
                    if (!StringUtil.isNull2(_newuserid)) {
                        return _newuserid;
                    }
                }
                // 可检查NextToken是否为空，若不为空，可通过NextToken继续读取。
                if (searchResponse.getNextToken() != null) {
                    searchRequest.getSearchQuery().setToken(searchResponse.getNextToken()); // 把token设置到下一次请求中
                } else {
                    break;
                }
            }
        } catch (Exception e) {
            // e.printStackTrace();
            log.error(e.getMessage(), e);
        }
        return null;
    }

    /**
     * 更新数据
     *
     * @param 主键字段和列
     * @return
     */
    public boolean updateRow(ScphUserid scphUserid) {
        if (scphUserid == null || StringUtil.isNull2(scphUserid.getNewuserid()) || StringUtil.isNull2(scphUserid.getUserid())) {
            return false;
        }
        try {
            SyncClient client = tsDao.getClient(TSINSTANCE_NAME);
            // 构造主键
            PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
            primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_newuserid, PrimaryKeyValue.fromString(scphUserid.getNewuserid()));

            PrimaryKey primaryKey = primaryKeyBuilder.build();
            // 设置表名
            RowUpdateChange rowUpdateChange = new RowUpdateChange(TABLE_NAME, primaryKey);
            // 加入一些属性列，非主键
            rowUpdateChange.put(new Column(COLUMN_dpmc, ColumnValue.fromString(scphUserid.getDpmc())));
            rowUpdateChange.put(new Column(COLUMN_userid, ColumnValue.fromString(scphUserid.getUserid())));
            rowUpdateChange.put(new Column(COLUMN_b2c, ColumnValue.fromBoolean(scphUserid.isB2c())));
            rowUpdateChange.put(new Column(COLUMN_type, ColumnValue.fromLong(scphUserid.getType())));
            rowUpdateChange.put(new Column(COLUMN_updatetime, ColumnValue.fromString(scphUserid.getUpdatetime())));
            if (StringUtils.isNotBlank(scphUserid.getDpurl())) {
                rowUpdateChange.put(new Column(COLUMN_dpurl, ColumnValue.fromString(scphUserid.getDpurl())));
            }
            if (StringUtils.isNotBlank(scphUserid.getLogo())) {
                rowUpdateChange.put(new Column(COLUMN_logo, ColumnValue.fromString(scphUserid.getLogo())));
            }
            if (StringUtils.isNotBlank(scphUserid.getShopid())) {
                rowUpdateChange.put(new Column(COLUMN_shopid, ColumnValue.fromString(scphUserid.getShopid())));
            }
            // 插入
            client.updateRow(new UpdateRowRequest(rowUpdateChange));
            return true;
        } catch (Throwable e) {
            log.error(e.getMessage(), e);
            e.printStackTrace();
            return false;
        }
    }

}
