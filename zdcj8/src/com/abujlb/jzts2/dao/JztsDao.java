package com.abujlb.jzts2.dao;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.abujlb.Result;
import com.abujlb.dao.RedisDao;
import com.abujlb.dao.TsDao;
import com.abujlb.jzts2.http.CommDate;
import com.abujlb.jzts2.pojo.FinishResult;
import com.abujlb.jzts2.pojo.JztsMessage;
import com.abujlb.jzts2.pojo.JztsPara;
import com.abujlb.jzts2.pojo.JztsRw;
import com.abujlb.jzts2.pojo.ShoptsData;
import com.abujlb.jzts2.pojo.ShoptsResult;
import com.abujlb.util.DateUtil;
import com.abujlb.util.StringUtil;
import com.alicloud.openservices.tablestore.model.GetRowRequest;
import com.alicloud.openservices.tablestore.model.GetRowResponse;
import com.alicloud.openservices.tablestore.model.PrimaryKey;
import com.alicloud.openservices.tablestore.model.PrimaryKeyBuilder;
import com.alicloud.openservices.tablestore.model.PrimaryKeyValue;
import com.alicloud.openservices.tablestore.model.Row;
import com.alicloud.openservices.tablestore.model.SingleRowQueryCriteria;
import com.google.gson.Gson;

import redis.clients.jedis.Jedis;
import redis.clients.jedis.exceptions.JedisException;

@Component
public class JztsDao {
	private static Logger log = Logger.getLogger("JztsDao");

	private static final String JZTS_REDISKEY = "jzts2_";
	private static final String COMMDATE_REDISKEY = "_commdate";
	private static final String CURRRUNNING_REDISKEY = "_currrunning";
	private static final String LTLY_REDISKEY = "_ltly";

	private static final String COLUMN_TSLX = "tslx";
	private static final String COLUMN_CATEID = "cateid";
	private static final String COLUMN_DEVICE = "device";
	private static final String COLUMN_RQ = "rq";
	private static final String COLUMN_DATA = "data";
	private static final String TABLE_JZTS2 = "dpsp_ts";
	
	private final String SUB_TASK_SUFFIX = "_sub_msg"; // 子任务后缀（2023-04-20李龙）
	private final String JZTS2_DP_ERROR = "jzts2_dp_error_"; // 不可透视的店铺userid_类目id（2023-04-20李龙）

	@Autowired
	private RedisDao redisDao;
	@Autowired
	private TsDao tsDao;
	@Autowired
	private CommDate commDate;

	public void setDpUpdate(JztsPara para) {
		Jedis jedis = null;
		try {
			jedis = redisDao.getJedis();

			String key = JZTS_REDISKEY + para.getUserid() + LTLY_REDISKEY + COMMDATE_REDISKEY;
			String result = jedis.get(key);
			if (result == null) {
				jedis.set(key, para.getUpdateDate());
			} else {
				Date dold = DateUtil.parseDate(result);
				Date dnew = DateUtil.parseDate(para.getUpdateDate());
				if (dnew.after(dold)) {
					jedis.setex(key, 10 * 24 * 3600, para.getUpdateDate());
				}
			}
		} catch (JedisException je) {
			redisDao.returnBrokenResource(jedis);
			jedis = null;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		} finally {
			redisDao.returnResource(jedis);
		}
	}

	/**
	 * 获取生意参谋数据的最后更新日期
	 * 
	 * @param para
	 * @return
	 */
	public String getUpdateDate(JztsPara para) {
		// 读取缓存，在redis中缓存10分钟
		Jedis jedis = null;
		try {
			jedis = redisDao.getJedis();

			if (para.getLx().equalsIgnoreCase(JztsPara.LX_DP) && para.isLtly()) {
				// 六天六夜店铺详情
				String result = jedis.get(JZTS_REDISKEY + para.getUserid() + LTLY_REDISKEY + COMMDATE_REDISKEY);
				if (result != null) {
					Date d = DateUtil.parseDate(result);
					Date d7 = DateUtil.getDateBefore(DateUtil.getCurrentDate(), 7);
					if (!d.before(d7)) {
						// 如果缓存的日期在最近7天内，则直接返回缓存数据
						return result;
					}
				}

			}

			String result = jedis.get(JZTS_REDISKEY + para.getLx() + COMMDATE_REDISKEY);
			if (result != null && result.equals(DateUtil.format(DateUtil.getLastDay()))) {
				// 如果redis中已经存在了则直接返回
				return result;
			}
			// redis中不存在，调用生意参谋获取
			result = commDate.getUpdateDate2(para); // 改版2021-09-18，result =
													// commDate.getUpdateNDate(para);
			if (StringUtil.isNull(result)) {
				result = commDate.getUpdate1Date(para);
			}
			if (result != null) {
				// 将获取到的内容存入
				jedis.set(JZTS_REDISKEY + para.getLx() + COMMDATE_REDISKEY, result);
				// 如果日期已经是昨天了，则缓存到下一日的零点，否则缓存10分钟
				if (result.equals(DateUtil.format(DateUtil.getLastDay()))) {
					jedis.expireAt(JZTS_REDISKEY + para.getLx() + COMMDATE_REDISKEY,
							(DateUtil.getNextDay().getTime()) / 1000);
				} else {
					jedis.expire(JZTS_REDISKEY + para.getLx() + COMMDATE_REDISKEY, 10 * 60);
				}
				return result;
			}
		} catch (JedisException je) {
			redisDao.returnBrokenResource(jedis);
			jedis = null;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		} finally {
			redisDao.returnResource(jedis);
		}
		return null;
	}

	/**
	 * 获取数据
	 * 
	 * @param para
	 * @param type 类型：0.直接取数据、1.判断任务完成后取数据
	 * @return
	 */
	public Result getData(JztsPara para, int type) {
		Result result = new Result();
		Gson gson = new Gson();
		switch (para.getLx()) {
			case JztsPara.LX_DP: {
				List<ShoptsResult> shoptsResult = new ArrayList<ShoptsResult>();
				String[] cateList = para.getCatelist();
				List<String> wwcCate = new ArrayList<String>(); // 未完成类目
				Map<String, String> ywcCate = new HashMap<String, String>(); // 已完成类目
				
				List<String> errCate = new ArrayList<String>(); // 不能透视类目
				for (int i = 0; i < cateList.length; i++) {
					String json = this.getDataFromTs("dp_" + para.getUserid(), cateList[i], para.getUpdateDate());
					if (json != null) {
						ShoptsData shoptsData = null;
						shoptsData = gson.fromJson(json, ShoptsData.class);
						ShoptsResult shoptsRes = new ShoptsResult();
						shoptsRes.setCateid(cateList[i]);
						shoptsRes.setData(shoptsData);
						shoptsResult.add(shoptsRes);
						ywcCate.put(cateList[i], cateList[i]);
					} else {
						// wwcCate.add(cateList[i]);
						String useridCateid = this.getDptsStop(para.getUserid(), cateList[i]);
						if (StringUtil.isNull2(useridCateid)) {
							wwcCate.add(cateList[i]); // 未完成类目
						} else {
							errCate.add(cateList[i]); // 不能透视类目
						}
					}
				}
				if (shoptsResult.size() > 0) { // 有数据，直接返回（店铺有些类目没有数据，也没关系）
					result.setCodeContent(Result.SUCCESS, "success");
					result.putKey("list", shoptsResult);
					result.putKey("date", para.getUpdateDate());
					result.putKey("finished", JztsRw.FINISHED_YES);
					result.putKey("progress", 100);
					result.putKey("ywclm", ywcCate);
					if (errCate.size() > 0) {
						result.putKey("errCate", errCate); // 不能透视类目
					}
					if (wwcCate.size() > 0) {
						result.putKey("wwccate", wwcCate); // 未完成类目
					} else {
						if (errCate.size() == 0) {
							// 所有类目都是全的，重新写入新的日期缓存
							setDpUpdate(para);
						}
					}
				} else {
					if (errCate.size() > 0 && type == 0 && cateList.length == errCate.size()) { // 店铺的所有类目都不能透视，返回结果
						result.setCodeContent(Result.SUCCESS, "success");
						result.putKey("list", shoptsResult);
						result.putKey("date", para.getUpdateDate());
						result.putKey("finished", JztsRw.FINISHED_YES);
						result.putKey("progress", 100);
						result.putKey("ywclm", ywcCate);
						// result.putKey("wwccate", errCate); // 实际没有未完成类目，上层判断：不会发透视任务
						result.putKey("errCate", errCate); // 不能透视类目
					} else if (errCate.size() > 0 && type == 1) {
						result.setCodeContent(Result.SUCCESS, "success");
						result.putKey("list", shoptsResult);
						result.putKey("date", para.getUpdateDate());
						result.putKey("finished", JztsRw.FINISHED_YES);
						result.putKey("progress", 100);
						result.putKey("ywclm", ywcCate);
						// result.putKey("wwccate", errCate); // 实际没有未完成类目
						result.putKey("errCate", errCate); // 不能透视类目
					} else {
						result.setCodeContent(101, "no data");
					}
				}
				break;
			}
			case JztsPara.LX_SP: {
				String json = this.getDataFromTs("sp_" + para.getItemid(), para.getCateid(), para.getUpdateDate());
				if (json != null) {
					result.setCodeContent(Result.SUCCESS, "success");
					result.putKey("data", new StringBuilder(json));
					result.putKey("date", para.getUpdateDate());
					result.putKey("ndata", para.getUpdateDate());
					result.putKey("finished", JztsRw.FINISHED_YES);
					result.putKey("progress", 100);
				} else {
					result.setCodeContent(101, "no data");
				}
				break;
			}
			case JztsPara.LX_SCPH_DP: {
				String json1 = this.getDataFromTs("scph_dp_1_" + para.getCateid(), para.getCateid(),
						para.getUpdateDate());
				String json2 = this.getDataFromTs("scph_dp_2_" + para.getCateid(), para.getCateid(),
						para.getUpdateDate());
				if (json1 != null && json2 != null) {
					result.putKey("data1", new StringBuilder(gson.toJson(json1)));
					result.putKey("data2", new StringBuilder(gson.toJson(json2)));
					result.putKey("maxDate", para.getUpdateDate());
					result.putKey("finished", JztsRw.FINISHED_YES);
					result.putKey("progress", 100);
				} else {
					result.setCodeContent(101, "no data");
				}
				break;
			}
			case JztsPara.LX_SCPH_SP: {
				String json1 = this.getDataFromTs("scph_sp_1_" + para.getCateid(), para.getCateid(),
						para.getUpdateDate());
				String json2 = this.getDataFromTs("scph_sp_2_" + para.getCateid(), para.getCateid(),
						para.getUpdateDate());
				String json3 = this.getDataFromTs("scph_sp_3_" + para.getCateid(), para.getCateid(),
						para.getUpdateDate());
				if (json1 != null && json2 != null) {
					result.putKey("data1", new StringBuilder(gson.toJson(json1)));
					result.putKey("data2", new StringBuilder(gson.toJson(json2)));
					result.putKey("data3", new StringBuilder(gson.toJson(json3)));
					result.putKey("maxDate", para.getUpdateDate());
					result.putKey("finished", JztsRw.FINISHED_YES);
					result.putKey("progress", 100);
				} else {
					result.setCodeContent(101, "no data");
				}
				break;
			}
			case JztsPara.LX_SCPH_PP: {
				String json1 = this.getDataFromTs("scph_pp_1_" + para.getCateid(), para.getCateid(),
						para.getUpdateDate());
				String json2 = this.getDataFromTs("scph_pp_2_" + para.getCateid(), para.getCateid(),
						para.getUpdateDate());
				if (json1 != null && json2 != null) {
					result.putKey("data1", new StringBuilder(gson.toJson(json1)));
					result.putKey("data2", new StringBuilder(gson.toJson(json2)));
					result.putKey("maxDate", para.getUpdateDate());
					result.putKey("finished", JztsRw.FINISHED_YES);
					result.putKey("progress", 100);
				} else {
					result.setCodeContent(101, "no data");
				}
				break;
			}
		}
		return result;
	}

	private String getDataFromTs(String tslx, String cateid, String rq) {
		try {
			PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
			primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_TSLX, PrimaryKeyValue.fromString(tslx));
			primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_CATEID, PrimaryKeyValue.fromString(cateid));
			primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_DEVICE, PrimaryKeyValue.fromString("0"));
			primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_RQ, PrimaryKeyValue.fromString(rq));
			PrimaryKey primaryKey = primaryKeyBuilder.build();
			SingleRowQueryCriteria criteria = null;
			criteria = new SingleRowQueryCriteria(TABLE_JZTS2, primaryKey);
			criteria.setMaxVersions(1);
			criteria.addColumnsToGet(COLUMN_DATA);
			GetRowResponse getRowResponse = tsDao.getClient("abujlb4").getRow(new GetRowRequest(criteria));
			Row row = getRowResponse.getRow();
			String json = null;

			if (row != null) {
				json = row.getLatestColumn(COLUMN_DATA).getValue().asString();
				return json;
			}
		} catch (Throwable e) {
			e.printStackTrace();
		}
		return null;
	}

	/**
	 * 是否已经有正在运行中的任务
	 * 
	 * @param para
	 * @return
	 */
	public String hasRunning(JztsPara para) {
		String running = null;
		Jedis jedis = null;
		try {
			jedis = redisDao.getJedis();
			running = jedis.get(JZTS_REDISKEY + para.code() + CURRRUNNING_REDISKEY);
		} catch (JedisException je) {
			redisDao.returnBrokenResource(jedis);
			jedis = null;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		} finally {
			redisDao.returnResource(jedis);
		}
		return running;
	}

	/**
	 * 开始启动任务
	 * 
	 * @param rw
	 */
	public void startRw(JztsRw rw) {
		Jedis jedis = null;
		try {
			jedis = redisDao.getJedis();
			Gson gson = new Gson();
			List<JztsMessage> msgList = rw.getMsgList();
			// 将子任务写入redis
			for (int i = 0; i < msgList.size(); i++) {
				jedis.setex(msgList.get(i).getKey(), JztsPara.TIME_EXPIRE, String.valueOf(JztsRw.FINISHED_NO));
				
				// 保存子任务对象：finish通知时用到
				jedis.setex(msgList.get(i).getKey() + this.SUB_TASK_SUFFIX, JztsPara.TIME_EXPIRE * 6, gson.toJson(msgList.get(i))); // 有效期30分钟
			}
			// 将主任务写入redis
			jedis.setex(rw.getKey(), JztsPara.TIME_EXPIRE, gson.toJson(rw));
			// 将当前任务标记为正在执行的任务
			jedis.setex(JZTS_REDISKEY + rw.getPara().code() + CURRRUNNING_REDISKEY, JztsPara.TIME_EXPIRE, rw.getKey());
		} catch (JedisException je) {
			redisDao.returnBrokenResource(jedis);
			jedis = null;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		} finally {
			redisDao.returnResource(jedis);
		}
	}

	public Result progress(String key) {
		Result result = new Result();
		Jedis jedis = null;
		try {
			jedis = redisDao.getJedis();
			Gson gson = new Gson();
			String rwjson = jedis.get(key);
			if (StringUtil.isNull2(rwjson)) {
				return null;
			}
			JztsRw rw = gson.fromJson(rwjson, JztsRw.class);
			if (rw == null) {
				return null;
			}
			List<JztsMessage> msgList = rw.getMsgList();
			boolean finished = true;
			// 判断是否所有子任务都完成了
			for (int i = 0; i < msgList.size(); i++) {
				String val = jedis.get(msgList.get(i).getKey());
				if (val != null && val.equals(String.valueOf(JztsRw.FINISHED_NO))) {
					finished = false;
				}
			}
			if (finished) {
				return this.getData(rw.getPara(), 1);
			} else {
				result.putKey("finished", JztsRw.FINISHED_NO);
				long currTime = System.currentTimeMillis();
				int progress = 0;
				if (currTime - rw.getStartTime() > JztsPara.TIME_SECONDS * 1000) {
					progress = 90;
				} else {
					progress = (int) (90D * ((currTime - rw.getStartTime()) / (JztsPara.TIME_SECONDS * 1000D)));
				}
				result.putKey("progress", progress);
			}
			return result;
		} catch (JedisException je) {
			redisDao.returnBrokenResource(jedis);
			jedis = null;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		} finally {
			redisDao.returnResource(jedis);
		}
		return null;
	}

	/**
	 * 子任务完成
	 * 
	 * @param key
	 */
	public void finished(FinishResult data) {
		Jedis jedis = null;
		try {
			jedis = redisDao.getJedis();
			// 如果成功写入1，失败写入2
			jedis.setex(data.getKey(), JztsPara.TIME_EXPIRE,
					String.valueOf(data.isSuccess() ? JztsRw.FINISHED_YES : JztsRw.FINISHED_ERROR));
			
			if (!data.isSuccess() && !StringUtil.isNull2(data.getErrorCode())) { // 失败且存在错误码
				Gson gson = new Gson();
				// 查询任务详情
				String subJson = jedis.get(data.getKey() + this.SUB_TASK_SUFFIX); // 子任务json
				if (!StringUtil.isJson2(subJson)) {
					return;
				}
				JztsMessage jztsMessage = gson.fromJson(subJson, JztsMessage.class); // 竞争透视子任务msg
				if (jztsMessage == null) {
					return;
				}
				int errorCode = Integer.valueOf(data.getErrorCode());
				if (JztsPara.LX_DP.equalsIgnoreCase(jztsMessage.getLx()) && (errorCode == 19019 || errorCode == 19006)) {
					// 此时是不可抗力，通知采集完成（没有数据）
					jedis.setex(data.getKey(), JztsPara.TIME_EXPIRE, String.valueOf(JztsRw.FINISHED_YES));
					// 记录店铺userid_类目id，7天内不能发布任务
					jedis.setex(this.JZTS2_DP_ERROR + jztsMessage.getUserid() + "_" + jztsMessage.getCateid(), 60 * 60 * 24 * 7, jztsMessage.getUserid() + "_" + jztsMessage.getCateid());  // 有效期7天
				}
			}
		} catch (JedisException je) {
			redisDao.returnBrokenResource(jedis);
			jedis = null;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		} finally {
			redisDao.returnResource(jedis);
		}
	}
	
	/**
	 * dp店铺透视是否被禁止
	 * 
	 * @return
	 */
	public String getDptsStop(String userid, String cateid) {
		String result = null;
		Jedis jedis = null;
		try {
			jedis = redisDao.getJedis();
			result = jedis.get(this.JZTS2_DP_ERROR + userid + "_" + cateid);
		} catch (JedisException je) {
			redisDao.returnBrokenResource(jedis);
			jedis = null;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		} finally {
			redisDao.returnResource(jedis);
		}
		return result;
	}
	
}
