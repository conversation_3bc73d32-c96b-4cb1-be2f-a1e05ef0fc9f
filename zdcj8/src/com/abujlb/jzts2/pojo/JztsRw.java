package com.abujlb.jzts2.pojo;

import java.util.List;

public class JztsRw {
	/**
	 * 未完成
	 */
	public static final int FINISHED_NO = 0;
	/**
	 * 已完成
	 */
	public static final int FINISHED_YES = 1;
	/**
	 * 出错已结束
	 */
	public static final int FINISHED_ERROR = 2;

	private String key;
	private long startTime;
	private int finished;
	private int progress;
	private JztsPara para;
	/**
	 * 预计完成时间
	 */
	private int yjwcsj;
	private List<JztsMessage> msgList;

	public String getKey() {
		return key;
	}

	public long getStartTime() {
		return startTime;
	}

	public int getFinished() {
		return finished;
	}

	public int getProgress() {
		return progress;
	}

	public JztsPara getPara() {
		return para;
	}

	public List<JztsMessage> getMsgList() {
		return msgList;
	}

	public void setKey(String key) {
		this.key = key;
	}

	public void setStartTime(long startTime) {
		this.startTime = startTime;
	}

	public void setFinished(int finished) {
		this.finished = finished;
	}

	public void setProgress(int progress) {
		this.progress = progress;
	}

	public void setPara(JztsPara para) {
		this.para = para;
	}

	public void setMsgList(List<JztsMessage> msgList) {
		this.msgList = msgList;
	}

	public int getYjwcsj() {
		return yjwcsj;
	}

	public void setYjwcsj(int yjwcsj) {
		this.yjwcsj = yjwcsj;
	}
}
