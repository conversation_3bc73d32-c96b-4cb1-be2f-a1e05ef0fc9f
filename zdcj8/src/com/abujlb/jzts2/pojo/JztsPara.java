package com.abujlb.jzts2.pojo;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import com.abujlb.ckstore.AbujlbCookieStore;
import com.abujlb.ckstore.ZzhZxsl;
import com.abujlb.zdcj8.util.StrUtil;

public class JztsPara {
	/**
	 * 预估完成时间
	 */
	public static final int TIME_SECONDS = 30;

	/**
	 * 超时时间
	 */
	public static final int TIME_EXPIRE = 300;

	/**
	 * 店铺
	 */
	public static final String LX_DP = "dp";
	/**
	 * 商品
	 */
	public static final String LX_SP = "sp";
	/**
	 * 市场排行 店铺
	 */
	public static final String LX_SCPH_DP = "scph_dp";
	/**
	 * 市场排行 商品
	 */
	public static final String LX_SCPH_SP = "scph_sp";
	/**
	 * 市场排行品牌
	 */
	public static final String LX_SCPH_PP = "scph_pp";

	/**
	 * 热搜词
	 */
	public static final String LX_RSC = "rsc";

	/**
	 * 透视类型
	 */
	private String lx;
	/**
	 * 是否vip
	 */
	private boolean vip;

	/**
	 * 宝贝id，当类型为商品时必填
	 */
	private String itemid;

	/**
	 * 店铺userid，当类型为店铺时必填
	 */
	private String userid;
	/**
	 * 主张号名称，当类型为店铺时必填
	 */
	private String zhuzh;
	/**
	 * 经营类目列表，当类型为店铺时必填
	 */
	private String[] catelist;

	/**
	 * 是否六天六夜
	 */
	private boolean ltly;

	/**
	 * 类目，当类型为市场排行/热搜词时必填
	 */
	private String cateid;
	/**
	 * 顶级类目id
	 */
	private String topid;

	/**
	 * 数据的最后更新时间
	 */
	private String updateDate;

	private Map<String, String> ywclm;
	private Map<String, String> errlm;

	public String getLx() {
		return lx;
	}

	public String getItemid() {
		return itemid;
	}

	public String getUserid() {
		return userid;
	}

	public String[] getCatelist() {
		return catelist;
	}

	public String getCateid() {
		return cateid;
	}

	public void setLx(String lx) {
		this.lx = lx;
	}

	public void setItemid(String itemid) {
		this.itemid = itemid;
	}

	public void setUserid(String userid) {
		this.userid = userid;
	}

	public void setCatelist(String[] catelist) {
		this.catelist = catelist;
	}

	public void setCateid(String cateid) {
		this.cateid = cateid;
	}

	public String getZhuzh() {
		return zhuzh;
	}

	public void setZhuzh(String zhuzh) {
		this.zhuzh = zhuzh;
	}

	public String getUpdateDate() {
		return updateDate;
	}

	public void setUpdateDate(String updateDate) {
		this.updateDate = updateDate;
	}

	public String getTopid() {
		return topid;
	}

	public void setTopid(String topid) {
		this.topid = topid;
	}

	public boolean isVip() {
		return vip;
	}

	public void setVip(boolean vip) {
		this.vip = vip;
	}

	public boolean isLtly() {
		return ltly;
	}

	public void setLtly(boolean ltly) {
		this.ltly = ltly;
	}

	public Map<String, String> getYwclm() {
		return ywclm;
	}

	public void setYwclm(Map<String, String> ywclm) {
		this.ywclm = ywclm;
	}

	public Map<String, String> getErrlm() {
		return errlm;
	}

	public void setErrlm(Map<String, String> errlm) {
		this.errlm = errlm;
	}

	/**
	 * 任务的唯一识别标志
	 * 
	 * @return
	 */
	public String code() {
		switch (lx) {
			case LX_DP:
				return lx + "_" + userid;
			case LX_SP:
				return lx + "_" + itemid;
			default:
				return lx + "_" + cateid;
		}
	}

	private String createKey() {
		return UUID.randomUUID().toString().replaceAll("-", "");
	}

	public JztsRw createRw(AbujlbCookieStore cookieStore, String newuserid) {
		JztsRw jztsRw = new JztsRw();
		jztsRw.setKey(createKey());
		jztsRw.setStartTime(System.currentTimeMillis());
		jztsRw.setFinished(0);
		jztsRw.setPara(this);
		ZzhZxsl zxzhs = null;
		List<JztsMessage> list = new ArrayList<JztsMessage>();
		switch (lx) {
			case LX_DP: {
				jztsRw.setYjwcsj(TIME_SECONDS);
				for (int i = 0; i < catelist.length; i++) {
					zxzhs = cookieStore.zxzhs(catelist[i]);
					if (!zxzhs.canUse(this.lx)) {
						continue;
					}
					// 如果这个类目已经完成了
					if (this.ywclm != null && this.ywclm.containsKey(catelist[i])) {
						continue;
					}
					// 如果这个店铺userid_类目id不能透视，跳过
					if (this.errlm != null && this.errlm.containsKey(catelist[i])) {
						continue;
					}
					JztsMessage msg = new JztsMessage();
					msg.setKey(this.createKey());
					msg.setLx(this.lx);
					msg.setUserid(this.userid);
					msg.setZhuzh(this.zhuzh);
					msg.setCateid(catelist[i]);
					try {
						// 2024-05-06调整，newuserid可能是个url，例如：//store.taobao.com/shop/view_shop.htm?RAzN8HWVLQB9RrE1TSVG7r1mzdmk8KxwoH2keqpup8ZvAAETgqw
						if (!StrUtil.isNull2(newuserid)) {
							msg.setNewuserid(newuserid);
							
							if (newuserid.indexOf("?") > -1) {
								msg.setNewuserid(newuserid.split("\\?")[1]);
							}
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
					list.add(msg);
				}
				// 所有类目都不可用
				if (list.size() == 0) {
					return null;
				}
				break;
			}
			case LX_SP: {
				zxzhs = cookieStore.zxzhs(this.getCateid());
				if (!zxzhs.canUse(this.lx)) {
					return null;
				}
				jztsRw.setYjwcsj(TIME_SECONDS);
				JztsMessage msg = new JztsMessage();
				msg.setKey(this.createKey());
				msg.setLx(this.lx);
				msg.setItemid(itemid);
				msg.setCateid(cateid);
				list.add(msg);
				break;
			}
			default: {
				zxzhs = cookieStore.zxzhs(this.getTopid());
				if (!zxzhs.canUse(this.lx)) {
					return null;
				}
				jztsRw.setYjwcsj(TIME_SECONDS);
				JztsMessage msg = new JztsMessage();
				msg.setKey(this.createKey());
				msg.setLx(this.lx);
				msg.setCateid(cateid);
				list.add(msg);
				break;
			}
		}
		jztsRw.setMsgList(list);
		return jztsRw;
	}
}
