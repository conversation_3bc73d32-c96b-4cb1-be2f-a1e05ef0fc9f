package com.abujlb.jzts2.pojo;

import java.text.SimpleDateFormat;
import java.util.Date;

import com.abujlb.util.StringUtil;
import com.abujlb.zdcj8.util.ObjAttr2Number;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

/**
 * abujlb4.scph_userid封装类
 * 
 * <AUTHOR>
 * @date 2023-04-13 21:32:09
 */
public class ScphUserid {

	private String newuserid; // 加密userid
	private boolean b2c; //
	private String dpurl; // 店铺链接
	private int type; // 类型
	private String updatetime; // 更新时间
	private String dpmc; // 店铺名称
	private String userid; // userid
	private String logo; // 店铺logo
	private String shopid; // 店铺id

	public ScphUserid() {

	}

	public ScphUserid(String userid, String json) {
		try {
			this.userid = userid; // userid
			if (!StringUtil.isJson(json)) {
				return;
			}
			SimpleDateFormat tf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			JSONObject obj = JSONObject.parseObject(json);
			if (obj != null && obj.containsKey("data")) {
				JSONObject dataObj = obj.getJSONObject("data");
				this.newuserid = ObjAttr2Number.toString(dataObj, "userId"); // 加密userid
				this.b2c = ObjAttr2Number.toBoolean(dataObj, "b2CShop"); //
				this.dpurl = ObjAttr2Number.toString(dataObj, "linkUrl"); // 店铺链接
				this.type = 1; // 类型（数据已完整）
				this.updatetime = tf.format(new Date()); // 更新时间
				this.dpmc = ObjAttr2Number.toString(dataObj, "name"); // 店铺名称
				this.logo = ObjAttr2Number.toString(dataObj, "picUrl"); // 店铺logo
				this.shopid = ObjAttr2Number.toString(dataObj, "shopId"); // 店铺id
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	@Override
	public String toString() {
		Gson gson = new GsonBuilder().setDateFormat("yyyy-MM-dd HH:mm:ss").create();
		return gson.toJson(this);
	}

	public String getNewuserid() {
		return newuserid;
	}

	public void setNewuserid(String newuserid) {
		this.newuserid = newuserid;
	}

	public boolean isB2c() {
		return b2c;
	}

	public void setB2c(boolean b2c) {
		this.b2c = b2c;
	}

	public String getDpurl() {
		return dpurl;
	}

	public void setDpurl(String dpurl) {
		this.dpurl = dpurl;
	}

	public int getType() {
		return type;
	}

	public void setType(int type) {
		this.type = type;
	}

	public String getUpdatetime() {
		return updatetime;
	}

	public void setUpdatetime(String updatetime) {
		this.updatetime = updatetime;
	}

	public String getDpmc() {
		return dpmc;
	}

	public void setDpmc(String dpmc) {
		this.dpmc = dpmc;
	}

	public String getUserid() {
		return userid;
	}

	public void setUserid(String userid) {
		this.userid = userid;
	}

	public String getLogo() {
		return logo;
	}

	public void setLogo(String logo) {
		this.logo = logo;
	}

	public String getShopid() {
		return shopid;
	}

	public void setShopid(String shopid) {
		this.shopid = shopid;
	}

}
