package com.abujlb.jzts2.pojo;

public class JztsMessage {
	private String key;
	/**
	 * 是否vip
	 */
	private boolean vip;
	/**
	 * 透视类型
	 */
	private String lx;

	/**
	 * 宝贝id，当类型为商品时必填
	 */
	private String itemid;

	/**
	 * 店铺userid，当类型为店铺时必填
	 */
	private String userid;
	/**
	 * 主张号名称，当类型为店铺时必填
	 */
	private String zhuzh;
	/**
	 * 类目，当类型为市场排行/热搜词时必填
	 */
	private String cateid;
	/**
	 * 店铺加密userid，当类型为店铺时需要查询获得，查询不到则需要采集时调接口
	 */
	private String newuserid;

	public String getKey() {
		return key;
	}

	public String getLx() {
		return lx;
	}

	public String getItemid() {
		return itemid;
	}

	public String getUserid() {
		return userid;
	}

	public String getZhuzh() {
		return zhuzh;
	}

	public String getCateid() {
		return cateid;
	}

	public void setKey(String key) {
		this.key = key;
	}

	public void setLx(String lx) {
		this.lx = lx;
	}

	public void setItemid(String itemid) {
		this.itemid = itemid;
	}

	public void setUserid(String userid) {
		this.userid = userid;
	}

	public void setZhuzh(String zhuzh) {
		this.zhuzh = zhuzh;
	}

	public void setCateid(String cateid) {
		this.cateid = cateid;
	}

	public boolean isVip() {
		return vip;
	}

	public void setVip(boolean vip) {
		this.vip = vip;
	}

	public String getNewuserid() {
		return newuserid;
	}

	public void setNewuserid(String newuserid) {
		this.newuserid = newuserid;
	}

}
