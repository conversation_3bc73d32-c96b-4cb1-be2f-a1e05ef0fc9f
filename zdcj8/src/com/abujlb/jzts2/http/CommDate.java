package com.abujlb.jzts2.http;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.config.RequestConfig.Builder;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.abujlb.ckstore.AbujlbCookieStore;
import com.abujlb.ckstore.Cjzh;
import com.abujlb.jzts2.pojo.JztsPara;
import com.abujlb.retry.Retry;
import com.abujlb.retry.Times;
import com.abujlb.util.StringUtil;

import net.sf.json.JSONObject;

@Component
@Lazy
public class CommDate {
	private static Logger log = Logger.getLogger(CommDate.class);
	@Autowired
	private AbujlbCookieStore cookieStore;

	@Retry
	public String getUpdateNDate(JztsPara para) {
		Map<String, String> map = this.getUpdateDateMap(para);
		if (map != null) {
			return map.get("updateNDay");
		}
		return null;
	}
	
	/**
	 * 获取updateDay日期
	 * 
	 */
	@Retry
	public String getUpdate1Date(JztsPara para) {
		Map<String, String> map = this.getUpdateDateMap(para);
		if (map != null) {
			return map.get("update1Day");
		}
		return null;
	}

	/**
	 * 获取日期（旧版）
	 * 
	 * @param para
	 * @return {"hasError":false,"content":{"traceId":"212aa56e16319486310166813e12a4","code":0,"data":{"updateWeek":"2021-09-12","updateMonth":"2021-08-31","updateNDay":"2021-09-17","update1Day":"2021-09-17"},"message":"操作成功"}}
	 */
	@Retry
	public Map<String, String> getUpdateDateMap(JztsPara para) {
		Cjzh cjzh = cookieStore.nextCjzhBjcs();
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cjzh.getCookieStore()).build();
		long t = System.currentTimeMillis();
		HttpGet httpget = new HttpGet(
				"https://sycm.taobao.com/portal/common/commDate.json?targetUrl=http%3A%2F%2Fsycm.taobao.com%2Fmc%2Fmq%2Fproduct_insight&_="
						+ t + "&token=");
		httpget.setHeader("accept", "*/*");
		httpget.setHeader("accept-encoding", "gzip, deflate, sdch, br");
		httpget.setHeader("accept-language", "zh-CN,zh;q=0.8");
		httpget.setHeader("referer", "https://sycm.taobao.com/mc/mq/product_insight");
		httpget.setHeader("user-agent",
				"Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/55.0.2883.75 Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000)
				.setConnectionRequestTimeout(20000);
		httpget.setConfig(requestConfig.build());

		HttpResponse httpresponse;
		try {
			httpresponse = httpClient.execute(httpget);
			HttpEntity entity = httpresponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			JSONObject jsonObj = JSONObject.fromObject(body);
			if (jsonObj.has("hasError") && !jsonObj.getBoolean("hasError") && jsonObj.has("content")
					&& jsonObj.getJSONObject("content").has("data")) {
				JSONObject dataobj = jsonObj.getJSONObject("content").getJSONObject("data");
				Map<String, String> dateMap = new HashMap<String, String>();
				@SuppressWarnings("unchecked")
				Iterator<String> iter = dataobj.keys();
				while (iter.hasNext()) {
					String key = iter.next();
					String value = dataobj.getString(key);
					dateMap.put(key, value);
				}
				return dateMap;
			} else {
				log.error(cjzh.getCookieKey() + " " + body);
				return null;
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return null;
		} finally {
			httpget.releaseConnection();
		}
	}
	
	/**
	 * 获取updateDay日期
	 * 
	 */
	@Retry
	public String getUpdateDate2(JztsPara para) {
		Map<String, String> map = this.getUpdateDate2(para, 1);
		if (map != null) {
			return map.get("updateDay");
		}
		return null;
	}
	
	/**
	 * 获取日期（新版：2021-09-18）
	 * 
	 * @param para
	 * @return {"code":0,"data":{"updateWeek":"2021-09-12","updateDay":"2021-09-17","updateMonth":"2021-08-31","updateNDay":"2021-09-16"},"message":"操作成功"}
	 */
	@Retry
	public Map<String, String> getUpdateDate2(JztsPara para, @Times int time) {
		if (time > 1) {
			try {
				Thread.sleep(time * 800L);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		Cjzh cjzh = cookieStore.nextCjzhBjcs();
		if (cjzh == null) {
			return null;
		}
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cjzh.getCookieStore()).build();
		long t = System.currentTimeMillis();
		// HttpGet httpGet = new HttpGet("https://sycm.taobao.com/oneauth/api/commDate/onetrace.json?targetUrl=http%3A%2F%2Fsycm.taobao.com%2Fmc%2Fmq%2Fproduct_insight&_=" + t + "&token=" + cjzh.getMicrodata("legalityToken"));
		HttpGet httpGet = new HttpGet("https://sycm.taobao.com/oneauth/api/commDate/onetrace.json?targetUrl=http%3A%2F%2Fsycm.taobao.com%2Fmc%2Fci%2Fconfig%2Frival&_=" + t + "&token=" + cjzh.getMicrodata("legalityToken"));
		httpGet.setHeader("accept", "*/*");
		httpGet.setHeader("accept-encoding", "gzip, deflate, br");
		httpGet.setHeader("accept-language", "zh-CN,zh;q=0.9");
		// httpGet.setHeader("referer", "https://sycm.taobao.com/mc/mq/product_insight");
		httpGet.setHeader("referer", "https://sycm.taobao.com/mc/ci/config/rival?activeKey=shop&cateFlag=1&cateId=" + para.getTopid());
		httpGet.setHeader("sec-ch-ua", "\"Chromium\";v=\"92\", \" Not A;Brand\";v=\"99\", \"Google Chrome\";v=\"92\"");
		httpGet.setHeader("sec-ch-ua-mobile", "?0");
		httpGet.setHeader("sec-fetch-dest", "empty");
		httpGet.setHeader("sec-fetch-mode", "cors");
		httpGet.setHeader("sec-fetch-site", "same-origin");
		// httpGet.setHeader("sycm-referer", "/mc/mq/product_insight");
		httpGet.setHeader("sycm-referer", "/mc/ci/config/rival");
		// httpGet.setHeader("user-agent", "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/55.0.2883.75 Safari/537.36");
		httpGet.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000).setConnectionRequestTimeout(20000);
		httpGet.setConfig(requestConfig.build());
		HttpResponse httpresponse;
		try {
			httpresponse = httpClient.execute(httpGet);
			HttpEntity entity = httpresponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			if (!StringUtil.isJson(body)) {
				return null;
			}
			JSONObject jsonObj = JSONObject.fromObject(body);
			if (jsonObj != null && jsonObj.has("code") && jsonObj.getInt("code") == 0 && jsonObj.has("data") && jsonObj.getJSONObject("data").has("updateDay")) {
				JSONObject dataObj = jsonObj.getJSONObject("data");
				Map<String, String> dateMap = new HashMap<String, String>();
				@SuppressWarnings("unchecked")
				Iterator<String> iter = dataObj.keys();
				while (iter.hasNext()) {
					String key = iter.next();
					String value = dataObj.getString(key);
					dateMap.put(key, value);
				}
				return dateMap;
			} else {
				log.error(cjzh.getCookieKey() + " " + body);
				return null;
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return null;
		} finally {
			httpGet.releaseConnection();
		}
	}
	
}
