package com.abujlb.jzts2.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.abujlb.Result;
import com.abujlb.annotation.ServiceData;
import com.abujlb.jzts2.pojo.FinishResult;
import com.abujlb.jzts2.pojo.JztsPara;
import com.abujlb.jzts2.service.JztsService;

@RestController
@RequestMapping("/jzts2")
public class Jzts2Controller {
	@Autowired
	private JztsService jztsService;

	@RequestMapping("/start")
	public String start(@ServiceData JztsPara data) {
		Result result = jztsService.start(data);
		if (result != null && result.isSuccess()) {
			result.removeKey("ywclm");
		}
		return result.toString();
	}

	@RequestMapping("/progress")
	public String progress(@ServiceData String data) {
		Result result = jztsService.progress(data);
		if (result != null && result.isSuccess()) {
			result.removeKey("ywclm");
		}
		return result.toString();
	}
	
	@RequestMapping("/finished")
	public String finished(@ServiceData FinishResult data) {
		jztsService.finished(data);
		return Result.RESULT_SUCCESS.toString();
	}
	
	@RequestMapping("/setUpdateDate")
	public String setUpdateDate(@ServiceData JztsPara data){
		return jztsService.setUpdateDate(data).toString();
	}
}
