package com.abujlb.jzts2.service;

import org.springframework.stereotype.Component;

import com.abujlb.Result;
import com.abujlb.jzts2.pojo.JztsPara;
import com.abujlb.util.DateUtil;
import com.abujlb.util.ResourceLoader;

@Component
public class JztsServiceTest {
	public Result start(JztsPara data) {
		Result result = new Result();
		result.setCodeContent(Result.SUCCESS, "success");
		result.putKey("key", createKey(data));
		return result;
	}

	public Result progress(String key) {
		Result result = new Result();
		try {
			result.setCodeContent(Result.SUCCESS, "success");

			String lx = key.substring(0, key.indexOf("_"));
			long time = Long.parseLong(key.substring(key.indexOf("_") + 1));
			long second = (System.currentTimeMillis() - time) / 1000;
			if (second < JztsPara.TIME_SECONDS) {
				// 任务从创建到当前时间还不到30秒
				result.putKey("finished", 0);
				int progress = (int) (second * 100D / JztsPara.TIME_SECONDS);
				result.putKey("progress", progress);
			} else {
				// 已经达到或超过30秒
				result.putKey("finished", 1);
				result.putKey("progress", 100);
				String date = DateUtil.format(DateUtil.getLastDay());
				if (lx.equalsIgnoreCase(JztsPara.LX_DP)) {
					result.putKey("date", date);
					result.putKey("list",
							new StringBuilder(ResourceLoader.loadSource("/com/abujlb/jzts2/bean/dp.test.json")));
				} else if (lx.equalsIgnoreCase(JztsPara.LX_SP)) {
					result.putKey("date", date);
					result.putKey("ndata", date);
					result.putKey("data",
							new StringBuilder(ResourceLoader.loadSource("/com/abujlb/jzts2/bean/sp.test.json")));
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.setCodeContent(101, "未知错误");
		}

		return result;
	}

	private String createKey(JztsPara data) {
		String key = data.getLx() + "_" + System.currentTimeMillis();
		return key;
	}
}
