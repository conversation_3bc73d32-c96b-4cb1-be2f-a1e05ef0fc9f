package com.abujlb.jzts2.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.abujlb.Result;
import com.abujlb.ckstore.AbujlbCookieStore;
import com.abujlb.ckstore.OnOff;
import com.abujlb.czs.Cate;
import com.abujlb.czs.Czs;
import com.abujlb.jzts2.dao.JztsDao;
import com.abujlb.jzts2.pojo.FinishResult;
import com.abujlb.jzts2.pojo.JztsMessage;
import com.abujlb.jzts2.pojo.JztsPara;
import com.abujlb.jzts2.pojo.JztsRw;
import com.abujlb.jzts2.tsdao.ScphUseridTsDao;
import com.abujlb.mq.TxyunMq;
import com.abujlb.zdcj8.service.CateBaobei;

@Component
public class JztsService {
	private static final String QUEUE_NAME = "jzts2";

	@Autowired
	private JztsDao jztsDao;
	@Autowired
	private TxyunMq txyunMq;
	@Autowired
	private AbujlbCookieStore cookieStore;
	@Autowired
	private CateBaobei bb;
	@Autowired
	private Czs czs;
	@Autowired
	private OnOff onOff;
	@Autowired
	private ScphUseridTsDao scphUseridTsDao;

	public Result setUpdateDate(JztsPara data) {
		jztsDao.setDpUpdate(data);
		return Result.RESULT_SUCCESS;
	}

	@SuppressWarnings("unchecked")
	public Result start(JztsPara data) {

		Result result = null;
		String updateDate = jztsDao.getUpdateDate(data);
		if (updateDate == null) {
			result = new Result();
			result.setCodeContent(101, "数据获取错误");
			return result;
		}
		data.setUpdateDate(updateDate);

		// 是商品，则需要获取一下商品对应的顶级类目
		if (data.getLx().equalsIgnoreCase(JztsPara.LX_SP)) {
			Cate cate = bb.getLm(data.getItemid());
			if (cate != null) {
				data.setCateid(cate.getTopid());
			} else {
				result = new Result();
				result.setCodeContent(101, "数据获取错误");
				return result;
			}
		}
		// 是否已经存在数据了，如果已经存在数据了，则直接返回数据
		result = jztsDao.getData(data, 0);
		if (result.isSuccess()) {
			if (data.getLx().equalsIgnoreCase(JztsPara.LX_DP)) {
				// 是店铺透视
				List<String> wwcCate = (List<String>) result.getKey("wwccate");
				if (data.isLtly() || wwcCate == null || wwcCate.size() == 0) {
					return result;
				}
				Map<String, String> ywclm = (HashMap<String, String>) result.getKey("ywclm");
				data.setYwclm(ywclm);
				// 不能透视的类目
				List<String> errCate = (List<String>) result.getKey("errCate");
				if (errCate != null && errCate.size() > 0) {
					Map<String, String> errlm = new HashMap<String, String>();
					for (String cateidd : errCate) {
						errlm.put(cateidd, cateidd);
					}
					data.setErrlm(errlm);
				}
			} else {
				return result;
			}
		}

		if (data.getLx().equalsIgnoreCase(JztsPara.LX_DP) && !onOff.isOn("shopts")) {
			result = new Result();
			result.setCodeContent(101, "系统关闭");
			return result;
		}
		if (data.getLx().equalsIgnoreCase(JztsPara.LX_SP) && !onOff.isOn("dpts")) {
			result = new Result();
			result.setCodeContent(101, "系统关闭");
			return result;
		}
		if (data.getLx().equalsIgnoreCase(JztsPara.LX_RSC) && !onOff.isOn("rsc_cate")) {
			result = new Result();
			result.setCodeContent(101, "系统关闭");
			return result;
		}
		if ((data.getLx().equalsIgnoreCase(JztsPara.LX_SCPH_DP) || data.getLx().equalsIgnoreCase(JztsPara.LX_SCPH_SP)
				|| data.getLx().equalsIgnoreCase(JztsPara.LX_SCPH_PP)) && !onOff.isOn("scph")) {
			result = new Result();
			result.setCodeContent(101, "系统关闭");
			return result;
		}

		// 是否存在正在执行中的任务
		String running = jztsDao.hasRunning(data);
		if (running != null) {
			result = new Result();
			result.setCodeContent(Result.SUCCESS, "success");
			result.putKey("key", running);
			return result;
		}

		// 启动任务
		if (data.getCateid() != null) {
			Cate cate = czs.getCate(data.getCateid());
			if (cate != null) {
				data.setTopid(cate.getTopid());
			}
		}
		// 读取表格存储abujlb4.scph_userid：根据userid获取加密的userid
		String newuserid = scphUseridTsDao.termQuery(data.getUserid());
		JztsRw rw = data.createRw(cookieStore, newuserid);
		if (rw == null) { // 所有类目都不可用，无任务
			result = jztsDao.getData(data, 1);
			return result;
		}

		result = new Result();
		result.putKey("key", rw.getKey());
		this.startRw(rw);
		return result;
	}

	/**
	 * 发送消息开启任务
	 * 
	 * @param rw
	 */
	private void startRw(JztsRw rw) {
		// 1.在redis中初始化任务
		jztsDao.startRw(rw);
		// 2.发送消息
		List<JztsMessage> list = rw.getMsgList();
		for (int i = 0; i < list.size(); i++) {
			txyunMq.sendMessage2(QUEUE_NAME, list.get(i).getLx(), list.get(i));
		}
	}

	public Result progress(String key) {
		Result result = jztsDao.progress(key);
		if (result != null && result.isSuccess()) {
			return result;
		} else {
			// 错误
			return Result.RESULT_UNKNOWN;
		}
	}

	public void finished(FinishResult data) {
		jztsDao.finished(data);
	}
}
