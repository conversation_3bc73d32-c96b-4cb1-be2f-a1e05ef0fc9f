package com.abujlb.ckstore;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.abujlb.dao.RedisDao;

import redis.clients.jedis.Jedis;
import redis.clients.jedis.exceptions.JedisException;

/**
 * 获取当前店铺帐号的top商品，用于在获取竞品数据的时候搭配一个获取本店的数据
 * 
 * <AUTHOR>
 * @date 2020-07-08
 *
 */
@Component
public class TopItem {
	private static Logger log = Logger.getLogger(TopItem.class);
	private static final String TOPITEM_REDISKEY = "topitem_";

	@Autowired
	private RedisDao redisDao;

	public String getTopItem(Cjzh cjzh) {
		String result = null;
		Jedis jedis = null;
		try {
			jedis = redisDao.getJedis();
			result = jedis.get(TOPITEM_REDISKEY + cjzh.getZhuzh());
		} catch (JedisException je) {
			redisDao.returnBrokenResource(jedis);
			jedis = null;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		} finally {
			redisDao.returnResource(jedis);
		}
		return result;
	}

	public void saveTopItem(Cjzh cjzh, String topitem) {
		Jedis jedis = null;
		try {
			jedis = redisDao.getJedis();
			jedis.set(TOPITEM_REDISKEY + cjzh.getZhuzh(), topitem);
		} catch (JedisException je) {
			redisDao.returnBrokenResource(jedis);
			jedis = null;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		} finally {
			redisDao.returnResource(jedis);
		}
	}
}
