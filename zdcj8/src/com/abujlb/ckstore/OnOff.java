package com.abujlb.ckstore;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import javax.annotation.PostConstruct;
import javax.net.ssl.SSLContext;

import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.util.EntityUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.abujlb.Config;
import com.abujlb.Result;
import com.abujlb.util.AbujlbTrustStrategy;
import com.abujlb.util.Md5;
import com.google.gson.reflect.TypeToken;

@Component
public class OnOff {
	private static Logger log = Logger.getLogger(AbujlbCookieStore.class);

	private String SERVER_COOKIE;
	@Autowired
	private Config config;
	private Map<String, OnOffBean> map = null;

	@PostConstruct
	public void init() {
		SERVER_COOKIE = config.getString("cookieserver");
		map = new ConcurrentHashMap<String, OnOffBean>();
		this.update();
	}

	@Scheduled(cron = "0/10 * * * * ?")
	public void update() {
		String url = SERVER_COOKIE + "onoff/onofflist";
		SSLContext sslContext;
		HttpPost httppost = null;
		try {
			String stamp = String.valueOf(System.currentTimeMillis());
			String password = Md5.password(stamp);
			sslContext = new SSLContextBuilder().loadTrustMaterial(null, new AbujlbTrustStrategy()).build();
			SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslContext);
			CloseableHttpClient httpClient = HttpClients.custom().setSSLSocketFactory(sslsf).build();
			httppost = new HttpPost(url);
			ArrayList<BasicNameValuePair> pairs = new ArrayList<BasicNameValuePair>();
			pairs.add(new BasicNameValuePair("stamp", stamp));
			pairs.add(new BasicNameValuePair("password", password));

			HttpResponse httpresponse;

			UrlEncodedFormEntity urlEncodedFormEntity = new UrlEncodedFormEntity(pairs, "utf-8");
			httppost.setEntity(urlEncodedFormEntity);
			httpresponse = httpClient.execute(httppost);
			HttpEntity entity = httpresponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			Type type = new TypeToken<List<OnOffBean>>() {
			}.getType();
			List<OnOffBean> list = Result.getValueFromJson(body, "list", type);
			if (list != null) {
				list.forEach(c -> {
					map.put(c.getCode(), c);
				});
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		} finally {
			if (httppost != null) {
				httppost.releaseConnection();
			}
		}
	}

	public boolean isOn(String code) {
		if (code == null || code.trim().length() == 0) {
			return false;
		}
		OnOffBean bean = map.get(code);
		if (bean != null) {
			return bean.isOn();
		}
		return false;
	}
}
