package com.abujlb.ckstore;

import java.util.Date;
import java.util.Map;

import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.CookieStore;
import org.apache.http.client.config.CookieSpecs;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.config.RequestConfig.Builder;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;

import com.abujlb.util.DateUtil;
import com.abujlb.zdcj.util.String2Util;
import com.alibaba.fastjson.JSONObject;

@Component
public class LoginKeepThread {
	private static Logger log = Logger.getLogger(LoginKeepThread.class);

	public boolean keep(Cjzh cjzh, CookieStore cookieStore) {
		for (int i = 0; i < CookieStoreConst.BHSBCS_MAX; i++) {
			boolean res = this.keep2(cjzh, cookieStore);
			if (res) {
				return true;
			}
		}
		return false;
	}

	public boolean keep2(Cjzh cjzh, CookieStore cookieStore) {
		// 如果登录时间超过23.5小时，则丢弃掉，不必等到24小时超时
		if (System.currentTimeMillis() - cjzh.getLogintime() > (23 * 3600 + 3500) * 1000L) {
			return false;
		}
		if (cjzh.getDp() != null && cjzh.getDp().getBi() == 1 && cjzh.getLoginlx() == 1) { // 淘系登录方式：0.千牛或淘宝、1.淘宝客
			// 维持阿里妈妈淘宝联盟（淘宝客）的帐号cookie
			try {
				/*
				 * String alimamaToken = this.getAlimamaToken(cjzh,
				 * cookieStore); if (!String2Util.isNull(alimamaToken)) {
				 * Map<String, String> microdata = cjzh.getMicrodata();
				 * microdata.put("alimamaToken", alimamaToken);
				 * cjzh.setMicrodata(microdata); }
				 */
				// 2024-01-22 调整：使用新接口保活
				if (String2Util.isNull(cjzh.getMicrodata("alimamaToken"))) {
					String alimamaToken = this.getAlimamaToken(cjzh, cookieStore);
					if (!String2Util.isNull(alimamaToken)) {
						Map<String, String> microdata = cjzh.getMicrodata();
						microdata.put("alimamaToken", alimamaToken);
						cjzh.setMicrodata(microdata);
					}
				}
				this.tbkOverview(cjzh, cookieStore);

				// 万相台无界保活（取消：采集时在重新激活万相台无界cookie）
				// if (cjzh.getAiztOne() == 1) {
				// cjzhXxcj.loginAlimamaAizt(cjzh);
				// }

				// 生意参谋保活
				// cjzhXxcj.loadSycmIndex(cjzh);

			} catch (Exception e) {
				log.error(e.getMessage(), e);
			}
		}
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cookieStore).build();
		HttpGet httpget = new HttpGet("https://sycm.taobao.com/portal/live/new/index/overview.json?dateType=today&_="
				+ System.currentTimeMillis() + "&token=" + cjzh.getMicrodata("legalityToken"));
		httpget.setHeader("accept", "*/*");
		httpget.setHeader("accept-encoding", "gzip, deflate, sdch, br");
		httpget.setHeader("accept-language", "zh-CN,zh;q=0.8");
		httpget.setHeader("referer", "https://sycm.taobao.com/portal/home.htm");
		httpget.setHeader("user-agent",
				"Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/55.0.2883.75 Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(5000).setConnectTimeout(5000)
				.setConnectionRequestTimeout(5000);
		httpget.setConfig(requestConfig.build());

		HttpResponse httpresponse;
		try {
			httpresponse = httpClient.execute(httpget);
			HttpEntity entity = httpresponse.getEntity();
			String body = EntityUtils.toString(entity);

			if (!String2Util.isJson2(body)) { // 返回可能不是json，避免报错，按照原来的逻辑，cookie是不删除的
				log.info("保活接口返回不是json，jlbdpid：" + cjzh.getJlbdpid() + "，dpmc：" + cjzh.getDpmc() + "，子账号："
						+ cjzh.getZzh() + "，ck" + cjzh.getCookieKey());
				return true;
			}
			JSONObject jsonObj = JSONObject.parseObject(body);
			if (jsonObj.containsKey("code") && jsonObj.getInteger("code") == 5810) {
				// 登录超时了，删除该帐号
				return false;
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		} finally {
			httpget.releaseConnection();
		}
		return true;
	}

	

	/**
	 * 获取阿里妈妈token
	 * 
	 * @param cjzh
	 *            采集账号
	 * @param cookieStore
	 *            账号cookie
	 */
	public boolean alimamaKeep(Cjzh cjzh, CookieStore cookieStore) {
		long time = System.currentTimeMillis();
		String rq = DateUtil.format(new Date());
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cookieStore).build();
		HttpGet httpGet = new HttpGet(
				"https://ad.alimama.com/openapi/param2/1/gateway.unionadv/mkt.rpt.lens.data.account_effect.json?t="
						+ time + "&_tb_token_=" + cjzh.getMicrodata("alimamaToken") + "&startDate=" + rq + "&endDate="
						+ rq + "&type=cps&split=2&period=1d");
		httpGet.setHeader("accept", "*/*");
		httpGet.setHeader("accept-encoding", "gzip, deflate, br");
		httpGet.setHeader("accept-language", "zh-CN,zh;q=0.9");
		httpGet.setHeader("content-type", "application/x-www-form-urlencoded; charset=UTF-8");
		httpGet.setHeader("referer", "https://ad.alimama.com/dashboard.htm");
		httpGet.setHeader("user-agent",
				"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");
		httpGet.setHeader("x-requested-with", "XMLHttpRequest");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(10000).setConnectTimeout(10000)
				.setConnectionRequestTimeout(10000);
		httpGet.setConfig(requestConfig.build());

		HttpResponse httpresponse;
		try {
			httpresponse = httpClient.execute(httpGet);
			HttpEntity entity = httpresponse.getEntity();
			String body = EntityUtils.toString(entity);
			if (!String2Util.isJson2(body)) {
				return false;
			}
			JSONObject jsonObj = JSONObject.parseObject(body);
			if (jsonObj.containsKey("resultCode") && jsonObj.getInteger("resultCode") == 200
					&& jsonObj.containsKey("success") && jsonObj.getBoolean("success")) {
				return true;
			}
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		} finally {
			httpGet.releaseConnection();
		}
		return false;
	}

	/**
	 * getAlimamaToken 保持阿里妈妈cookie活性
	 * 
	 * @param cjzh
	 *            采集账号
	 * @param cookieStore
	 *            账号cookie
	 */
	public String getAlimamaToken(Cjzh cjzh, CookieStore cookieStore) {
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cookieStore).build();
		HttpGet httpGet = new HttpGet("https://ad.alimama.com/cps/shopkeeper/loginMessage.json?"); // 未登录时接口不包含“?”
		httpGet.setHeader("accept", "*/*");
		httpGet.setHeader("accept-encoding", "gzip, deflate, br");
		httpGet.setHeader("accept-language", "zh-CN,zh;q=0.9");
		httpGet.setHeader("bx-v", "2.2.3");
		httpGet.setHeader("content-type", "application/x-www-form-urlencoded; charset=UTF-8");
		httpGet.setHeader("referer", "https://ad.alimama.com/portal/v2/dashboard.htm"); // httpGet.setHeader("referer",
																						// "https://ad.alimama.com/dashboard.htm");
		httpGet.setHeader("user-agent",
				"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");
		httpGet.setHeader("x-requested-with", "XMLHttpRequest");
		Builder requestConfig = RequestConfig.custom().setCookieSpec(CookieSpecs.NETSCAPE).setSocketTimeout(10000)
				.setConnectTimeout(10000).setConnectionRequestTimeout(10000);
		httpGet.setConfig(requestConfig.build());

		HttpResponse httpresponse;
		try {
			httpresponse = httpClient.execute(httpGet);
			HttpEntity entity = httpresponse.getEntity();
			String body = EntityUtils.toString(entity);
			if (!String2Util.isJson2(body)) {
				return null;
			}
			// 未登录：{"info":{"ok":true,"message":null},"data":{"noLogin":true,"loginUrlPrefix":"//www.alimama.com/member/login.htm?forward=","env":"product"}}
			// 已登录：{"info":{"ok":true,"message":null},"data":{"isBigShopKeeper":false,"isEarnedSeller":true,"infoCompleteUrl":null,"isJoinedCps":true,"newItemEventCount":0,"logname":"<EMAIL>","frozen":0,"env":"product","isTodayJoinedCps":false,"isTmallHKSeller":false,"isB2C":false,"isShopKeeperOwningMoney":false,"hasDingTalkProfile":false,"isJoinedSelfSupport":null,"nickname":"虎骑士旗舰店","isInCPADebtWhiteList":false,"isInCPAWhiteList":false,"isEarnedSellerEvaluatePermitted":true,"shopKeeperId":30427866,"hasRecommendation":true,"rytFlag":1,"_tb_token_":"jdC88fnxMightkPDex0J","isEarnedSellerShopPermitted":false,"currentJoinSelfSupport":false}}
			// 已登录：{"data":{"isBigShopKeeper":false,"isEarnedSeller":true,"infoCompleteUrl":null,"isJoinedCps":true,"newItemEventCount":0,"logname":"<EMAIL>","frozen":0,"env":"product","isTodayJoinedCps":false,"isTmallHKSeller":false,"isB2C":false,"isShopKeeperOwningMoney":true,"hasDingTalkProfile":true,"isJoinedSelfSupport":null,"nickname":"尔沫旗舰店","isInCPADebtWhiteList":false,"isInCPAWhiteList":false,"isEarnedSellerEvaluatePermitted":true,"shopKeeperId":29953083,"hasRecommendation":true,"rytFlag":1,"_tb_token_":"5b4b14eee1b8e","isEarnedSellerShopPermitted":false,"currentJoinSelfSupport":false},"info":{"ok":true,"message":null}}
			JSONObject jsonObj = JSONObject.parseObject(body);
			if (jsonObj.containsKey("data") && jsonObj.getJSONObject("data") != null
					&& jsonObj.getJSONObject("data").containsKey("_tb_token_")) {
				String alimamaToken = jsonObj.getJSONObject("data").getString("_tb_token_");
				return alimamaToken;
			}
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		} finally {
			httpGet.releaseConnection();
		}
		return null;
	}

	/**
	 * tbkOverview 保持阿里妈妈cookie活性
	 * 
	 * @说明1 淘宝客首页：https://ad.alimama.com/portal/v2/dashboard.htm
	 * @说明2 淘宝客首页 -> 数据概览 -> 今日实时
	 * @说明3 此接口可能会重定向（此时返回的是html）：https://www.alimama.com/member/login.htm?forward=http%253A%252F%252Fadv.alimama.com……
	 * @说明4 此接口不用_tb_token_也能正常请求
	 * @param cookieStore
	 *            账号cookie
	 */
	public boolean tbkOverview(Cjzh cjzh, CookieStore cookieStore) {
		HttpGet httpGet = null;
		HttpResponse httpResponse = null;
		try {
			long t = System.currentTimeMillis();
			String rq = DateUtil.format(new Date());
			String _tb_token_ = "";
			if (cjzh.getMicrodata() != null && cjzh.getMicrodata().containsKey("alimamaToken")) {
				_tb_token_ = cjzh.getMicrodata().get("alimamaToken");
			}

			CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cookieStore).build();
			httpGet = new HttpGet("https://ad.alimama.com/openapi/param2/1/gateway.unionadv/data.home.overview.json?t="
					+ t + "&_tb_token_=" + _tb_token_ + "&startDate=" + rq + "&endDate=" + rq
					+ "&type=cps&split=2&period=1d");
			httpGet.setHeader("accept", "*/*");
			httpGet.setHeader("accept-encoding", "gzip, deflate, br");
			httpGet.setHeader("accept-language", "zh-CN,zh;q=0.9");
			// httpGet.setHeader("bx-v", "2.2.3");
			// httpGet.setHeader("cache-control", "no-cache");
			// httpGet.setHeader("connection", "keep-alive");
			httpGet.setHeader("content-type", "application/x-www-form-urlencoded; charset=UTF-8");
			// httpGet.setHeader("cookie", "");
			// // httpGet.setHeader("host", "ad.alimama.com");
			// httpGet.setHeader("pragma", "no-cache");
			httpGet.setHeader("referer", "https://ad.alimama.com/portal/v2/dashboard.htm");
			httpGet.setHeader("sec-fetch-dest", "empty");
			httpGet.setHeader("sec-fetch-mode", "cors");
			httpGet.setHeader("sec-fetch-site", "same-origin");
			httpGet.setHeader("user-agent",
					"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"); // Mozilla/5.0
																																			// (Windows
																																			// NT
																																			// 10.0;
																																			// Win64;
																																			// x64)
																																			// AppleWebKit/537.36
																																			// (KHTML,
																																			// like
																																			// Gecko)
																																			// Chrome/120.0.0.0
																																			// Safari/537.36
			httpGet.setHeader("x-requested-with", "XMLHttpRequest");
			Builder requestConfig = RequestConfig.custom().setCookieSpec(CookieSpecs.NETSCAPE).setSocketTimeout(10000)
					.setConnectTimeout(10000).setConnectionRequestTimeout(10000);
			httpGet.setConfig(requestConfig.build());

			httpResponse = httpClient.execute(httpGet);
			HttpEntity entity = httpResponse.getEntity();
			String body = EntityUtils.toString(entity);
			if (!String2Util.isJson2(body)) { // cookie失活，会跳转到https://www.alimama.com/member/login.htm?forward=http%253A%252F%252Fadv.alimama.com%252Fparam2%252F1%252Fgateway.unionadv%252Fdata.home.overview.json%253Ft%253D1705934692017%2526_tb_token_%253De3ae0d33737ee%2526startDate%253D2024-01-22%2526endDate%253D2024-01-22%2526type%253Dcps%2526split%253D2%2526period%253D1d
				return false;
			}
			// 成功：{"data":{"result":[{……}],"lastUpdateTime":"2024年01月22日
			// 22:44:51"},"resultCode":200,"success":true,"bizErrorCode":0}
			// 未登录：{"analyseTraceId":null,"info":{"ok":false,"message":"noLogin"},"result":null,"hostName":"halley-solar033043002057.center.na610","code":601}
			JSONObject jsonObj = JSONObject.parseObject(body);
			if (jsonObj.containsKey("resultCode") && jsonObj.getInteger("resultCode") == 200) {
				return true;
			}
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		} finally {
			if (httpGet != null) {
				httpGet.releaseConnection();
			}
		}
		return false;
	}

}
