package com.abujlb.ckstore;

import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.abujlb.util.DateUtil;
import com.abujlb.web.IpRemote;

@Component
public class RecordZdcjLog {
	@Autowired
	private IpRemote ipRemote;
	@Autowired
	private ZdcjZhIpLogDao logDao;

	public void log(Cjzh cjzh, String lx, String code, String msg) {
		try{
			ZhIpLog log = new ZhIpLog();
			log.setZhuzh(cjzh.getZhuzh());
			log.setErrcode(code);
			log.setIp(ipRemote.getIp());
			log.setData(msg);
			log.setLx(lx);
			log.setSj(DateUtil.formatTime(new Date()));
			logDao.save(log);
		}catch(Exception e){
		}
	}
}
