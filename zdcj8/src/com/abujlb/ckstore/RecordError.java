package com.abujlb.ckstore;

import java.util.ArrayList;

import javax.annotation.PostConstruct;
import javax.net.ssl.SSLContext;

import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.util.EntityUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.abujlb.Config;
import com.abujlb.util.AbujlbTrustStrategy;
import com.abujlb.util.Md5;

import net.sf.json.JSONObject;

@Component
public class RecordError {
	private static Logger log = Logger.getLogger(RecordError.class);

	public static final int RETRY = 10;// 失败重试次数
	public static final int UNKNOWN = 9999;// 未知错误

	private String SERVER_COOKIE; // cookie服务器地址
	private String SERVERNAME;// 服务器编号

	@Autowired
	private Config config;

	@PostConstruct
	public void init() {
		SERVER_COOKIE = config.getString("cookieserver");
		SERVERNAME = config.getString("servername");// 服务器编号
	}

	public boolean record(String zhuzh, String zzh, int jlbdpid, int errorcode, String errormsg, String classmethod) {
		if (errorcode == 0) {
			// 不记录code为0的错误
			return true;
		}
		boolean result = false;
		int sbcs = 0;
		while (!result && sbcs < RETRY) {
			result = recordError2(zhuzh, zzh, jlbdpid, errorcode, errormsg, classmethod);
			sbcs++;
		}
		return result;
	}

	public boolean record(Cjzh cjzh, String msg) {
		StackTraceElement[] stacks = Thread.currentThread().getStackTrace();
		StackTraceElement stack = (StackTraceElement) stacks[2];
		String classmethod = stack.getClassName() + "." + stack.getMethodName();
		return record(cjzh.getZhuzh(), cjzh.getZzh(), Integer.parseInt(cjzh.getJlbdpid() + ""), UNKNOWN, msg,
				classmethod);
	}

	public boolean record(Cjzh cjzh, int errorcode, String body) {
		StackTraceElement[] stacks = Thread.currentThread().getStackTrace();
		StackTraceElement stack = (StackTraceElement) stacks[2];
		String classmethod = stack.getClassName() + "." + stack.getMethodName();
		return record(cjzh.getZhuzh(), cjzh.getZzh(), Integer.parseInt(cjzh.getJlbdpid() + ""), errorcode, body,
				classmethod);
	}

	public boolean recordError2(String zhuzh, String zzh, int jlbdpid, int errorcode, String errormsg,
			String classmethod) {
		return recordError2(zhuzh, zzh, jlbdpid, errorcode, errormsg, classmethod, SERVERNAME);
	}

	public boolean recordError2(String zhuzh, String zzh, int jlbdpid, int errorcode, String errormsg,
			String classmethod, String lx) {
		String url = SERVER_COOKIE + "recordError_recordError.action";
		boolean result = false;
		SSLContext sslContext;
		HttpPost httppost = null;
		try {
			String stamp = String.valueOf(System.currentTimeMillis());
			String password = Md5.password(zhuzh + zzh + lx + stamp);

			sslContext = new SSLContextBuilder().loadTrustMaterial(null, new AbujlbTrustStrategy()).build();

			SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslContext);
			CloseableHttpClient httpClient = HttpClients.custom().setSSLSocketFactory(sslsf).build();

			httppost = new HttpPost(url);
			ArrayList<BasicNameValuePair> pairs = new ArrayList<BasicNameValuePair>();
			pairs.add(new BasicNameValuePair("zhuzh", zhuzh));
			pairs.add(new BasicNameValuePair("zzh", zzh));
			pairs.add(new BasicNameValuePair("jlbdpid", Integer.toString(jlbdpid)));
			pairs.add(new BasicNameValuePair("errorcode", Integer.toString(errorcode)));
			pairs.add(new BasicNameValuePair("errormsg", errormsg));
			pairs.add(new BasicNameValuePair("servername", lx));
			pairs.add(new BasicNameValuePair("classmethod", classmethod));
			pairs.add(new BasicNameValuePair("stamp", stamp));
			pairs.add(new BasicNameValuePair("password", password));
			HttpResponse httpresponse;

			UrlEncodedFormEntity urlEncodedFormEntity = new UrlEncodedFormEntity(pairs, "utf-8");
			httppost.setEntity(urlEncodedFormEntity);
			httpresponse = httpClient.execute(httppost);
			HttpEntity entity = httpresponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			JSONObject jsonObj = JSONObject.fromObject(body);
			int code = jsonObj.getInt("code");
			if (code == 0) {
				result = true;
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		} finally {
			if (httppost != null) {
				httppost.releaseConnection();
			}
		}
		return result;
	}
}
