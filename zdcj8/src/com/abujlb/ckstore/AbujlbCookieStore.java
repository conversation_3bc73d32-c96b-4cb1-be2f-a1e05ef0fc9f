package com.abujlb.ckstore;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.PostConstruct;
import javax.net.ssl.SSLContext;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.CookieStore;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.util.EntityUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.abujlb.Config;
import com.abujlb.dao.RedisDao;
import com.abujlb.util.AbujlbTrustStrategy;
import com.abujlb.util.CookieString;
import com.abujlb.util.Md5;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;

import redis.clients.jedis.Jedis;
import redis.clients.jedis.exceptions.JedisException;

@Component("abujlbCookieStore")
public class AbujlbCookieStore {
	private static Logger log = Logger.getLogger(AbujlbCookieStore.class);
	private String SERVER_COOKIE;
	@Autowired
	private Config config;
	@Autowired
	private RedisDao redisDao;

	@PostConstruct
	public void init() {
		SERVER_COOKIE = config.getString("cookieserver");
	}

	/**
	 * 获取当前在线帐号数量
	 * 
	 * @param cateid
	 * @return
	 */
	public ZzhZxsl zxzhs(String cateid) {
		String url = SERVER_COOKIE + "hqzh_zxzhs.action";
		SSLContext sslContext;
		HttpPost httppost = null;
		try {
			Gson gson = new Gson();
			String stamp = String.valueOf(System.currentTimeMillis());
			String password = Md5.password(cateid + stamp);

			sslContext = new SSLContextBuilder().loadTrustMaterial(null, new AbujlbTrustStrategy()).build();

			SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslContext);
			CloseableHttpClient httpClient = HttpClients.custom().setSSLSocketFactory(sslsf).build();

			httppost = new HttpPost(url);
			ArrayList<BasicNameValuePair> pairs = new ArrayList<BasicNameValuePair>();
			pairs.add(new BasicNameValuePair("cateid", cateid));
			pairs.add(new BasicNameValuePair("stamp", stamp));
			pairs.add(new BasicNameValuePair("password", password));

			HttpResponse httpresponse;

			UrlEncodedFormEntity urlEncodedFormEntity = new UrlEncodedFormEntity(pairs, "utf-8");
			httppost.setEntity(urlEncodedFormEntity);
			httpresponse = httpClient.execute(httppost);
			HttpEntity entity = httpresponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			JSONObject jsonObj = JSONObject.parseObject(body);
			int code = jsonObj.getIntValue("code");
			if (code == 0 && jsonObj.containsKey("zxzhs")) {
				String cjzhJson = jsonObj.getString("zxzhs");
				ZzhZxsl result = gson.fromJson(cjzhJson, ZzhZxsl.class);
				if (result != null) {
					return result;
				}
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		} finally {
			if (httppost != null) {
				httppost.releaseConnection();
			}
		}
		return null;
	}

	/**
	 * 随机获取一个任意类目的帐号
	 * 
	 * @return
	 */
	public Cjzh nextCjzhSj() {
		return this.nextCjzh("");
	}

	/**
	 * 随机获取一个指定类目的帐号
	 * 
	 * @param cateid
	 * @return
	 */
	public Cjzh nextCjzh(String cateid) {
		Cjzh result = null;
		String url = SERVER_COOKIE + "hqzh_next.action";
		SSLContext sslContext;
		HttpPost httppost = null;
		try {
			Gson gson = new Gson();
			String stamp = String.valueOf(System.currentTimeMillis());
			String password = Md5.password(cateid + stamp);

			sslContext = new SSLContextBuilder().loadTrustMaterial(null, new AbujlbTrustStrategy()).build();

			SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslContext);
			CloseableHttpClient httpClient = HttpClients.custom().setSSLSocketFactory(sslsf).build();

			httppost = new HttpPost(url);
			ArrayList<BasicNameValuePair> pairs = new ArrayList<BasicNameValuePair>();
			pairs.add(new BasicNameValuePair("cateid", cateid));
			pairs.add(new BasicNameValuePair("stamp", stamp));
			pairs.add(new BasicNameValuePair("password", password));

			HttpResponse httpresponse;

			UrlEncodedFormEntity urlEncodedFormEntity = new UrlEncodedFormEntity(pairs, "utf-8");
			httppost.setEntity(urlEncodedFormEntity);
			httpresponse = httpClient.execute(httppost);
			HttpEntity entity = httpresponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			JSONObject jsonObj = JSONObject.parseObject(body);
			int code = jsonObj.getIntValue("code");
			if (code == 0 && jsonObj.containsKey("cjzh")) {
				String cjzhJson = jsonObj.getString("cjzh");
				String cookie = jsonObj.getString("cookie");
				result = gson.fromJson(cjzhJson, Cjzh.class);
				if (result != null) {
					CookieStore ck = CookieString.strToCookies(cookie);
					result.setCookieStore(ck);
				}
			}
			return result;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		} finally {
			if (httppost != null) {
				httppost.releaseConnection();
			}
		}
		return result;
	}

	/**
	 * 随机获取一个帐号
	 * 
	 * @return
	 */
	public Cjzh nextCjzhBjcs() {
		Cjzh result = null;
		String url = SERVER_COOKIE + "hqzh_nextbjcs.action";
		SSLContext sslContext;
		HttpPost httppost = null;
		try {
			Gson gson = new Gson();
			String stamp = String.valueOf(System.currentTimeMillis());
			String password = Md5.password(stamp);

			sslContext = new SSLContextBuilder().loadTrustMaterial(null, new AbujlbTrustStrategy()).build();

			SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslContext);
			CloseableHttpClient httpClient = HttpClients.custom().setSSLSocketFactory(sslsf).build();

			httppost = new HttpPost(url);
			ArrayList<BasicNameValuePair> pairs = new ArrayList<BasicNameValuePair>();
			pairs.add(new BasicNameValuePair("stamp", stamp));
			pairs.add(new BasicNameValuePair("password", password));

			HttpResponse httpresponse;

			UrlEncodedFormEntity urlEncodedFormEntity = new UrlEncodedFormEntity(pairs, "utf-8");
			httppost.setEntity(urlEncodedFormEntity);
			httpresponse = httpClient.execute(httppost);
			HttpEntity entity = httpresponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			JSONObject jsonObj = JSONObject.parseObject(body);
			int code = jsonObj.getIntValue("code");
			if (code == 0 && jsonObj.containsKey("cjzh")) {
				String cjzhJson = jsonObj.getString("cjzh");
				String cookie = jsonObj.getString("cookie");
				result = gson.fromJson(cjzhJson, Cjzh.class);
				if (result != null) {
					CookieStore ck = CookieString.strToCookies(cookie);
					result.setCookieStore(ck);
				}
			}
			return result;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		} finally {
			if (httppost != null) {
				httppost.releaseConnection();
			}
		}
		return result;
	}

	/**
	 * 获取一个直通车可用的帐号
	 * 
	 * @return
	 */
	public Cjzh nextCjzhZtc() {
		Cjzh result = null;
		String url = SERVER_COOKIE + "hqzh_nextztc.action";
		SSLContext sslContext;
		HttpPost httppost = null;
		try {
			Gson gson = new Gson();
			String stamp = String.valueOf(System.currentTimeMillis());
			String password = Md5.password(stamp);

			sslContext = new SSLContextBuilder().loadTrustMaterial(null, new AbujlbTrustStrategy()).build();

			SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslContext);
			CloseableHttpClient httpClient = HttpClients.custom().setSSLSocketFactory(sslsf).build();

			httppost = new HttpPost(url);
			ArrayList<BasicNameValuePair> pairs = new ArrayList<BasicNameValuePair>();
			pairs.add(new BasicNameValuePair("stamp", stamp));
			pairs.add(new BasicNameValuePair("password", password));

			HttpResponse httpresponse;

			UrlEncodedFormEntity urlEncodedFormEntity = new UrlEncodedFormEntity(pairs, "utf-8");
			httppost.setEntity(urlEncodedFormEntity);
			httpresponse = httpClient.execute(httppost);
			HttpEntity entity = httpresponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			JSONObject jsonObj = JSONObject.parseObject(body);
			int code = jsonObj.getIntValue("code");
			if (code == 0 && jsonObj.containsKey("cjzh")) {
				String cjzhJson = jsonObj.getString("cjzh");
				String cookie = jsonObj.getString("cookie");
				result = gson.fromJson(cjzhJson, Cjzh.class);
				if (result != null) {
					CookieStore ck = CookieString.strToCookies(cookie);
					result.setCookieStore(ck);
				}
			}
			return result;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		} finally {
			if (httppost != null) {
				httppost.releaseConnection();
			}
		}
		return result;
	}

	/**
	 * 获取指定类目的下一个帐号，标准版优先
	 * 
	 * @param cateid
	 * @return
	 */
	public Cjzh nextCjzhStd(String cateid) {
		Cjzh result = null;
		String url = SERVER_COOKIE + "hqzh_nextstd.action";
		SSLContext sslContext;
		HttpPost httppost = null;
		try {
			Gson gson = new Gson();
			String stamp = String.valueOf(System.currentTimeMillis());
			String password = Md5.password(cateid + stamp);

			sslContext = new SSLContextBuilder().loadTrustMaterial(null, new AbujlbTrustStrategy()).build();

			SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslContext);
			CloseableHttpClient httpClient = HttpClients.custom().setSSLSocketFactory(sslsf).build();

			httppost = new HttpPost(url);
			ArrayList<BasicNameValuePair> pairs = new ArrayList<BasicNameValuePair>();
			pairs.add(new BasicNameValuePair("cateid", cateid));
			pairs.add(new BasicNameValuePair("stamp", stamp));
			pairs.add(new BasicNameValuePair("password", password));

			HttpResponse httpresponse;

			UrlEncodedFormEntity urlEncodedFormEntity = new UrlEncodedFormEntity(pairs, "utf-8");
			httppost.setEntity(urlEncodedFormEntity);
			httpresponse = httpClient.execute(httppost);
			HttpEntity entity = httpresponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			JSONObject jsonObj = JSONObject.parseObject(body);
			int code = jsonObj.getIntValue("code");
			if (code == 0 && jsonObj.containsKey("cjzh")) {
				String cjzhJson = jsonObj.getString("cjzh");
				String cookie = jsonObj.getString("cookie");
				result = gson.fromJson(cjzhJson, Cjzh.class);
				if (result != null) {
					CookieStore ck = CookieString.strToCookies(cookie);
					result.setCookieStore(ck);
				}
			}
			return result;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		} finally {
			if (httppost != null) {
				httppost.releaseConnection();
			}
		}
		return result;
	}

	/**
	 * 帐号使用后返回
	 * 
	 * @param cjzh
	 * @param flag
	 * @return
	 */
	public boolean back(Cjzh cjzh, boolean flag, String module) {
		// 刷新cookie
		this.refreshCookie(cjzh);

		String url = SERVER_COOKIE + "hqzh_back.action";
		SSLContext sslContext;
		HttpPost httppost = null;
		try {
			String stamp = String.valueOf(System.currentTimeMillis());
			String password = Md5.password(cjzh.getCookieKey() + String.valueOf(flag) + module + stamp);

			sslContext = new SSLContextBuilder().loadTrustMaterial(null, new AbujlbTrustStrategy()).build();

			SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslContext);
			CloseableHttpClient httpClient = HttpClients.custom().setSSLSocketFactory(sslsf).build();

			httppost = new HttpPost(url);
			ArrayList<BasicNameValuePair> pairs = new ArrayList<BasicNameValuePair>();
			pairs.add(new BasicNameValuePair("cookieKey", cjzh.getCookieKey()));
			pairs.add(new BasicNameValuePair("flag", String.valueOf(flag)));
			pairs.add(new BasicNameValuePair("module", module));
			pairs.add(new BasicNameValuePair("stamp", stamp));
			pairs.add(new BasicNameValuePair("password", password));

			HttpResponse httpresponse;

			UrlEncodedFormEntity urlEncodedFormEntity = new UrlEncodedFormEntity(pairs, "utf-8");
			httppost.setEntity(urlEncodedFormEntity);
			httpresponse = httpClient.execute(httppost);
			HttpEntity entity = httpresponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			JSONObject jsonObj = JSONObject.parseObject(body);
			int code = jsonObj.getIntValue("code");
			if (code == 0) {
				return true;
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		} finally {
			if (httppost != null) {
				httppost.releaseConnection();
			}
		}
		return false;
	}

	/**
	 * 根据cookieKey获取采集帐号
	 * 
	 * @param cookieKey
	 * @return
	 */
	public Cjzh getCjzhForKey(String cookieKey) {
		Cjzh result = null;
		String url = SERVER_COOKIE + "hqzh_getCjzhByKey.action";
		SSLContext sslContext;
		HttpPost httppost = null;
		try {
			Gson gson = new Gson();
			String stamp = String.valueOf(System.currentTimeMillis());
			String password = Md5.password(cookieKey + stamp);

			sslContext = new SSLContextBuilder().loadTrustMaterial(null, new AbujlbTrustStrategy()).build();

			SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslContext);
			CloseableHttpClient httpClient = HttpClients.custom().setSSLSocketFactory(sslsf).build();

			httppost = new HttpPost(url);
			ArrayList<BasicNameValuePair> pairs = new ArrayList<BasicNameValuePair>();
			pairs.add(new BasicNameValuePair("cookieKey", cookieKey));
			pairs.add(new BasicNameValuePair("stamp", stamp));
			pairs.add(new BasicNameValuePair("password", password));

			HttpResponse httpresponse;

			UrlEncodedFormEntity urlEncodedFormEntity = new UrlEncodedFormEntity(pairs, "utf-8");
			httppost.setEntity(urlEncodedFormEntity);
			httpresponse = httpClient.execute(httppost);
			HttpEntity entity = httpresponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			JSONObject jsonObj = JSONObject.parseObject(body);
			int code = jsonObj.getIntValue("code");
			if (code == 0 && jsonObj.containsKey("cjzh")) {
				String cjzhJson = jsonObj.getString("cjzh");
				String cookie = jsonObj.getString("cookie");
				result = gson.fromJson(cjzhJson, Cjzh.class);
				if (result != null) {
					CookieStore ck = CookieString.strToCookies(cookie);
					result.setCookieStore(ck);
				}
			}
			return result;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		} finally {
			if (httppost != null) {
				httppost.releaseConnection();
			}
		}
		return result;
	}

	/**
	 * 根据主张号名称获取cookieKey
	 * 
	 * @param zhuzh
	 * @return
	 */
	public String getCookieKeyForZhuzh(String zhuzh) {
		String url = SERVER_COOKIE + "hqzh_forzhuzh.action";
		SSLContext sslContext;
		HttpPost httppost = null;
		try {
			String stamp = String.valueOf(System.currentTimeMillis());
			String password = Md5.password(zhuzh + stamp);

			sslContext = new SSLContextBuilder().loadTrustMaterial(null, new AbujlbTrustStrategy()).build();

			SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslContext);
			CloseableHttpClient httpClient = HttpClients.custom().setSSLSocketFactory(sslsf).build();

			httppost = new HttpPost(url);
			ArrayList<BasicNameValuePair> pairs = new ArrayList<BasicNameValuePair>();
			pairs.add(new BasicNameValuePair("zhuzh", zhuzh));
			pairs.add(new BasicNameValuePair("stamp", stamp));
			pairs.add(new BasicNameValuePair("password", password));

			HttpResponse httpresponse;

			UrlEncodedFormEntity urlEncodedFormEntity = new UrlEncodedFormEntity(pairs, "utf-8");
			httppost.setEntity(urlEncodedFormEntity);
			httpresponse = httpClient.execute(httppost);
			HttpEntity entity = httpresponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			JSONObject jsonObj = JSONObject.parseObject(body);
			int code = jsonObj.getIntValue("code");
			if (code == 0 && jsonObj.containsKey("key")) {
				String key = jsonObj.getString("key");
				return key;
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		} finally {
			if (httppost != null) {
				httppost.releaseConnection();
			}
		}
		return null;
	}

	/**
	 * 获取具有某个模块权限的帐号
	 * 
	 * @param module
	 * @return
	 */
	public Cjzh getCjzhForModule(String module) {
		String url = SERVER_COOKIE + "hqzh_next_module.action";
		SSLContext sslContext;
		HttpPost httppost = null;
		try {
			String stamp = String.valueOf(System.currentTimeMillis());
			String password = Md5.password(module + stamp);

			sslContext = new SSLContextBuilder().loadTrustMaterial(null, new AbujlbTrustStrategy()).build();

			SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslContext);
			CloseableHttpClient httpClient = HttpClients.custom().setSSLSocketFactory(sslsf).build();

			httppost = new HttpPost(url);
			ArrayList<BasicNameValuePair> pairs = new ArrayList<BasicNameValuePair>();
			pairs.add(new BasicNameValuePair("module", module));
			pairs.add(new BasicNameValuePair("stamp", stamp));
			pairs.add(new BasicNameValuePair("password", password));

			HttpResponse httpresponse;

			UrlEncodedFormEntity urlEncodedFormEntity = new UrlEncodedFormEntity(pairs, "utf-8");
			httppost.setEntity(urlEncodedFormEntity);
			httpresponse = httpClient.execute(httppost);
			HttpEntity entity = httpresponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			JSONObject jsonObj = JSONObject.parseObject(body);
			int code = jsonObj.getIntValue("code");
			if (code == 0 && jsonObj.containsKey("cjzh")) {
				Gson gson = new Gson();
				String cjzhJson = jsonObj.getString("cjzh");
				String cookie = jsonObj.getString("cookie");
				Cjzh result = gson.fromJson(cjzhJson, Cjzh.class);
				if (result != null) {
					CookieStore ck = CookieString.strToCookies(cookie);
					result.setCookieStore(ck);
					return result;
				}
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		} finally {
			if (httppost != null) {
				httppost.releaseConnection();
			}
		}
		return null;
	}

	public CookieStore getCookie(Cjzh cjzh) {
		return cjzh.getCookieStore();
	}

	/**
	 * 更新cookie
	 * 
	 * @param cjzh
	 */
	public boolean refreshCookie(Cjzh cjzh) {
		String url = SERVER_COOKIE + "task_cookieBack.action";
		SSLContext sslContext;
		HttpPost httppost = null;
		try {
			String cookieKey = cjzh.getCookieKey();
			String cookieStr = CookieString.cookiesToStr(cjzh.getCookieStore());
			String stamp = String.valueOf(System.currentTimeMillis());
			String password = Md5.password(cookieKey + cookieStr + stamp);

			sslContext = new SSLContextBuilder().loadTrustMaterial(null, new AbujlbTrustStrategy()).build();

			SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslContext);
			CloseableHttpClient httpClient = HttpClients.custom().setSSLSocketFactory(sslsf).build();

			httppost = new HttpPost(url);
			ArrayList<BasicNameValuePair> pairs = new ArrayList<BasicNameValuePair>();
			pairs.add(new BasicNameValuePair("cookieKey", cookieKey));
			pairs.add(new BasicNameValuePair("cookieStr", cookieStr));
			pairs.add(new BasicNameValuePair("stamp", stamp));
			pairs.add(new BasicNameValuePair("password", password));

			HttpResponse httpresponse;

			UrlEncodedFormEntity urlEncodedFormEntity = new UrlEncodedFormEntity(pairs, "utf-8");
			httppost.setEntity(urlEncodedFormEntity);
			httpresponse = httpClient.execute(httppost);
			HttpEntity entity = httpresponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			JSONObject jsonObj = JSONObject.parseObject(body);
			int code = jsonObj.getIntValue("code");
			if (code == 0) {
				return true;
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		} finally {
			if (httppost != null) {
				httppost.releaseConnection();
			}
		}
		return false;
	}

	/**
	 * 帐号退出登录，BI帐号当天数据采集完成了之后，将不再保持登录状态，主动退出帐号
	 * 
	 * @param cjzh
	 */
	public boolean logout(Cjzh cjzh) {
		String url = SERVER_COOKIE + "logoutByKey.action";
		SSLContext sslContext;
		HttpPost httppost = null;
		try {
			String stamp = String.valueOf(System.currentTimeMillis());
			String password = Md5.password(cjzh.getCookieKey() + stamp);

			sslContext = new SSLContextBuilder().loadTrustMaterial(null, new AbujlbTrustStrategy()).build();

			SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslContext);
			CloseableHttpClient httpClient = HttpClients.custom().setSSLSocketFactory(sslsf).build();

			httppost = new HttpPost(url);
			ArrayList<BasicNameValuePair> pairs = new ArrayList<BasicNameValuePair>();
			pairs.add(new BasicNameValuePair("cookieKey", cjzh.getCookieKey()));
			pairs.add(new BasicNameValuePair("stamp", stamp));
			pairs.add(new BasicNameValuePair("password", password));

			HttpResponse httpresponse;

			UrlEncodedFormEntity urlEncodedFormEntity = new UrlEncodedFormEntity(pairs, "utf-8");
			httppost.setEntity(urlEncodedFormEntity);
			httpresponse = httpClient.execute(httppost);
			HttpEntity entity = httpresponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			JSONObject jsonObj = JSONObject.parseObject(body);
			int code = jsonObj.getIntValue("code");
			if (code == 0) {
				return true;
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		} finally {
			if (httppost != null) {
				httppost.releaseConnection();
			}
		}
		return false;
	}

	/**
	 * 获取指定类目的下一个直通车帐号
	 *
	 * @param cateid
	 * @return
	 */
	public Cjzh getCjzhForCateZtc(String cateid) {
		Cjzh result = null;
		String url = SERVER_COOKIE + "getCjzhForCateZtc.action";
		SSLContext sslContext;
		HttpPost httppost = null;
		try {
			Gson gson = new Gson();
			String stamp = String.valueOf(System.currentTimeMillis());
			String password = Md5.password(cateid + stamp);

			sslContext = new SSLContextBuilder().loadTrustMaterial(null, new AbujlbTrustStrategy()).build();

			SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslContext);
			CloseableHttpClient httpClient = HttpClients.custom().setSSLSocketFactory(sslsf).build();

			httppost = new HttpPost(url);
			ArrayList<BasicNameValuePair> pairs = new ArrayList<BasicNameValuePair>();
			pairs.add(new BasicNameValuePair("cateid", cateid));
			pairs.add(new BasicNameValuePair("stamp", stamp));
			pairs.add(new BasicNameValuePair("password", password));

			HttpResponse httpresponse;

			UrlEncodedFormEntity urlEncodedFormEntity = new UrlEncodedFormEntity(pairs, "utf-8");
			httppost.setEntity(urlEncodedFormEntity);
			httpresponse = httpClient.execute(httppost);
			HttpEntity entity = httpresponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			JSONObject jsonObj = JSONObject.parseObject(body);
			int code = jsonObj.getIntValue("code");
			if (code == 0 && jsonObj.containsKey("cjzh")) {
				String cjzhJson = jsonObj.getString("cjzh");
				String cookie = jsonObj.getString("cookie");
				result = gson.fromJson(cjzhJson, Cjzh.class);
				if (result != null) {
					CookieStore ck = CookieString.strToCookies(cookie);
					result.setCookieStore(ck);
				}
			}
			return result;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		} finally {
			if (httppost != null) {
				httppost.releaseConnection();
			}
		}
		return result;
	}

	/**
	 * 根据cateid获取cookieKey
	 *
	 * @param cateid
	 * @return
	 */
	public String getKeysForCateZtc(String cateid) {
		String url = SERVER_COOKIE + "getKeysForCateZtc.action";
		SSLContext sslContext;
		HttpPost httppost = null;
		try {
			String stamp = String.valueOf(System.currentTimeMillis());
			String password = Md5.password(cateid + stamp);
			sslContext = new SSLContextBuilder().loadTrustMaterial(null, new AbujlbTrustStrategy()).build();
			SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslContext);
			CloseableHttpClient httpClient = HttpClients.custom().setSSLSocketFactory(sslsf).build();

			httppost = new HttpPost(url);
			ArrayList<BasicNameValuePair> pairs = new ArrayList<BasicNameValuePair>();
			pairs.add(new BasicNameValuePair("cateid", cateid));
			pairs.add(new BasicNameValuePair("stamp", stamp));
			pairs.add(new BasicNameValuePair("password", password));

			HttpResponse httpresponse;
			UrlEncodedFormEntity urlEncodedFormEntity = new UrlEncodedFormEntity(pairs, "utf-8");
			httppost.setEntity(urlEncodedFormEntity);
			httpresponse = httpClient.execute(httppost);
			HttpEntity entity = httpresponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			JSONObject jsonObj = JSONObject.parseObject(body);
			int code = jsonObj.getIntValue("code");
			if (code == 0 && jsonObj.containsKey("data")) {
				String key = jsonObj.getString("data");
				return key;
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		} finally {
			if (httppost != null) {
				httppost.releaseConnection();
			}
		}
		return null;
	}

	/**
	 * 获取需要保活的key
	 * 
	 * @param size
	 * @return
	 */
	public KeepPara nextKeyKeepLive(int size) {
		KeepPara kp = new KeepPara();
		List<KeepResult> result = new ArrayList<KeepResult>();
		String url = SERVER_COOKIE + "hqzh_keepActiveList.action";
		SSLContext sslContext;
		HttpPost httppost = null;
		try {
			String stamp = String.valueOf(System.currentTimeMillis());
			String password = Md5.password(size + stamp);

			sslContext = new SSLContextBuilder().loadTrustMaterial(null, new AbujlbTrustStrategy()).build();

			SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslContext);
			CloseableHttpClient httpClient = HttpClients.custom().setSSLSocketFactory(sslsf).build();

			httppost = new HttpPost(url);
			ArrayList<BasicNameValuePair> pairs = new ArrayList<BasicNameValuePair>();
			pairs.add(new BasicNameValuePair("size", String.valueOf(size)));
			pairs.add(new BasicNameValuePair("stamp", stamp));
			pairs.add(new BasicNameValuePair("password", password));

			HttpResponse httpresponse;

			UrlEncodedFormEntity urlEncodedFormEntity = new UrlEncodedFormEntity(pairs, "utf-8");
			httppost.setEntity(urlEncodedFormEntity);
			httpresponse = httpClient.execute(httppost);
			HttpEntity entity = httpresponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			JSONObject jsonObj = JSONObject.parseObject(body);
			int code = jsonObj.getIntValue("code");
			if (code == 0 && jsonObj.containsKey("list")) {
				if (jsonObj.containsKey("list")) {
					JSONArray cjzhJson = jsonObj.getJSONArray("list");
					for (int i = 0; i < cjzhJson.size(); i++) {
						KeepResult kr = new KeepResult();
						kr.setKey(cjzhJson.getString(i));
						result.add(kr);
					}
				}
				if (jsonObj.containsKey("interval")) {
					kp.setInterval(jsonObj.getLong("interval"));
				}
			}
			kp.setKr(result);
			return kp;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		} finally {
			if (httppost != null) {
				httppost.releaseConnection();
			}
		}
		return kp;
	}

	/**
	 * 返回保活结果
	 * 
	 * @param size
	 * @return
	 */
	public boolean keepActiveBack(List<KeepResult> res) {
		if (CollectionUtils.isEmpty(res)) {
			return true;
		}
		String url = SERVER_COOKIE + "keepActiveBack.action";
		Gson gson = new Gson();
		SSLContext sslContext;
		HttpPost httppost = null;
		String resultStr = gson.toJson(res);
		try {
			String stamp = String.valueOf(System.currentTimeMillis());
			String password = Md5.password(resultStr + stamp);

			sslContext = new SSLContextBuilder().loadTrustMaterial(null, new AbujlbTrustStrategy()).build();

			SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslContext);
			CloseableHttpClient httpClient = HttpClients.custom().setSSLSocketFactory(sslsf).build();

			httppost = new HttpPost(url);
			ArrayList<BasicNameValuePair> pairs = new ArrayList<BasicNameValuePair>();
			pairs.add(new BasicNameValuePair("resultStr", resultStr));
			pairs.add(new BasicNameValuePair("stamp", stamp));
			pairs.add(new BasicNameValuePair("password", password));

			HttpResponse httpresponse;

			UrlEncodedFormEntity urlEncodedFormEntity = new UrlEncodedFormEntity(pairs, "utf-8");
			httppost.setEntity(urlEncodedFormEntity);
			httpresponse = httpClient.execute(httppost);
			HttpEntity entity = httpresponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			JSONObject jsonObj = JSONObject.parseObject(body);
			int code = jsonObj.getIntValue("code");
			if (code == 0) {
				return true;
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		} finally {
			if (httppost != null) {
				httppost.releaseConnection();
			}
		}
		return false;
	}

	public Cjzh getCjzhFromRedis(String cookieKey) {
		Gson gson = new Gson();
		Jedis jedis = null;
		try {
			jedis = redisDao.getJedis();
			String cjzhJson = jedis.get(CookieStoreConst.COOKIE_ZH + cookieKey);
			String cookieValue = jedis.get(cookieKey);
			if (StringUtils.isNotBlank(cjzhJson) && StringUtils.isNotBlank(cookieValue)) {
				Cjzh cjzh = null;
				try {
					cjzh = gson.fromJson(cjzhJson, Cjzh.class);
					CookieStore cks = CookieString.strToCookies(cookieValue);
					cjzh.setCookieStore(cks);
					return cjzh;
				} catch (Exception e) {
					log.error(e.getMessage(), e);
				}
			}
		} catch (JedisException je) {
			redisDao.returnBrokenResource(jedis);
			jedis = null;
			log.error(je.getMessage(), je);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		} finally {
			redisDao.returnResource(jedis);
		}
		return null;
	}
}
