/**
 * 经营类目
 */
package com.abujlb.ckstore;

public class Jylm {

	/**
	 * 所有类目
	 */
	public static final int CLFS_ALL = 1;
	/**
	 * 顶级类目
	 */
	public static final int CLFS_TOP = 2;
	/**
	 * 所有叶子类目
	 */
	public static final int CLFS_LEAF = 3;
	/**
	 * 所有叶子类目和顶级类目
	 */
	public static final int CLFS_LEAFANDTOP = 4;

	/**
	 * 所有非叶子类目
	 */
	public static final int CLFS_NOTLEAF = 5;

	private String cateid;
	private String name;
	private String version;
	private int clfs;

	public String getCateid() {
		return cateid;
	}

	public String getName() {
		return name;
	}

	public void setCateid(String cateid) {
		this.cateid = cateid;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	public int getClfs() {
		return clfs;
	}

	public void setClfs(int clfs) {
		this.clfs = clfs;
	}
}
