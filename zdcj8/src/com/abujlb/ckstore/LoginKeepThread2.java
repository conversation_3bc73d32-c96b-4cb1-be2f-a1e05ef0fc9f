package com.abujlb.ckstore;

import java.util.List;

import javax.annotation.PostConstruct;

import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.abujlb.Config;

@Component
public class LoginKeepThread2 implements Runnable {
	private static Logger log = Logger.getLogger(LoginKeepThread2.class);
	@Autowired
	private AbujlbCookieStore cookieStore;
	@Autowired
	private LoginKeepThread keepThread;
	@Autowired
	private Config config;

	@PostConstruct
	public void init() {
		if (!config.getBoolean("debug")) {
			new Thread(this).start();
		}
	}

	public void run() {
		while (true) {
			try {
				// 通过远程获取需要保活的key
				KeepPara kp = cookieStore.nextKeyKeepLive(16);
				if (kp != null) {
					long interval = kp.getInterval();
					if (interval <= 0) {
						interval = 100L;
					}
					List<KeepResult> krs = kp.getKr();
					if (CollectionUtils.isNotEmpty(krs)) {
						for (KeepResult kr : krs) {
							this.keepAlive(kr);
							Thread.sleep(interval);
						}
						boolean succ = false;
						for (int i = 0; i < 3 && !succ; i++) {
							succ = cookieStore.keepActiveBack(krs);
						}
						continue;
					}
				}
				try {
					Thread.sleep(5000L);
				} catch (Exception e) {
				}
			} catch (Exception e) {
				log.error(e.getMessage(), e);
			}
		}
	}

	public void keepAlive(KeepResult kr) {
		// 从redis中获取cookie和帐号信息
		Cjzh cjzh = cookieStore.getCjzhFromRedis(kr.getKey());
		if (cjzh != null && cjzh.getCookieStore() != null) {
			boolean res = keepThread.keep(cjzh, cjzh.getCookieStore());
			kr.setOnline(res);
		}
	}
}
