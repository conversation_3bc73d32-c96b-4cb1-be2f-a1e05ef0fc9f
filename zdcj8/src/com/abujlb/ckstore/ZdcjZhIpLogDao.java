package com.abujlb.ckstore;

import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;

import javax.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.abujlb.dao.TsDao;
import com.alicloud.openservices.tablestore.model.Column;
import com.alicloud.openservices.tablestore.model.ColumnValue;
import com.alicloud.openservices.tablestore.model.PrimaryKey;
import com.alicloud.openservices.tablestore.model.PrimaryKeyBuilder;
import com.alicloud.openservices.tablestore.model.PrimaryKeyValue;
import com.alicloud.openservices.tablestore.model.PutRowRequest;
import com.alicloud.openservices.tablestore.model.RowPutChange;

@Component
public class ZdcjZhIpLogDao implements Runnable {
	private static final String TABLE_LOG = "zdcj_zhiplog";
	private static final String COLUMN_ZHUZH = "zhuzh";
	private static final String COLUMN_IP = "ip";
	private static final String COLUMN_SJ = "sj";
	private static final String COLUMN_LX = "lx";
	private static final String COLUMN_ERRORCODE = "errorcode";
	private static final String COLUMN_DATA = "data";
	@Autowired
	private TsDao tsDao;

	private BlockingQueue<ZhIpLog> queue = null;

	@PostConstruct
	public void init() {
		queue = new LinkedBlockingQueue<>();
		Thread t = new Thread(this);
		t.setName("ZdcjZhIpLogDao");
		t.start();
	}

	@Override
	public void run() {
		while (true) {
			try {
				ZhIpLog log = queue.take();
				this.saveLog(log);
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
	}

	public void save(ZhIpLog log) {
		queue.offer(log);
	}

	private boolean saveLog(ZhIpLog log) {
		try {
			PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
			primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_ZHUZH, PrimaryKeyValue.fromString(log.getZhuzh()));
			primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_IP, PrimaryKeyValue.fromString(log.getIp()));
			primaryKeyBuilder.addPrimaryKeyColumn(COLUMN_SJ, PrimaryKeyValue.fromString(log.getSj()));
			PrimaryKey primaryKey = primaryKeyBuilder.build();
			RowPutChange rowPutChange = null;
			rowPutChange = new RowPutChange(TABLE_LOG, primaryKey);
			if (log.getLx() == null) {
				log.setLx("unknown");
			}
			rowPutChange.addColumn(new Column(COLUMN_LX, ColumnValue.fromString(log.getLx())));
			if (log.getErrcode() != null) {
				rowPutChange.addColumn(new Column(COLUMN_ERRORCODE, ColumnValue.fromString(log.getErrcode())));
			}
			rowPutChange.addColumn(new Column(COLUMN_DATA, ColumnValue.fromString(log.getData())));
			tsDao.getClient("abujlb3").putRow(new PutRowRequest(rowPutChange));
			return true;
		} catch (Throwable e) {
			e.printStackTrace();
			return false;
		}
	}

}
