/**
 * 采集帐号
 */
package com.abujlb.ckstore;

import com.abujlb.jyrb.sjcj.fblm.Fblm;
import com.abujlb.util.StringUtil;
import com.abujlb.zdcj8.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import org.apache.http.client.CookieStore;
import org.apache.http.cookie.Cookie;
import org.apache.http.impl.cookie.BasicClientCookie;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class Cjzh {
	private String zzh; // 子帐号
	private transient CookieStore cookieStore;
	private String cookieKey;
	private String zhuzh; // 主帐号
	private long dpid;
	private long userid;
	private String dpmc;
	private long jlbdpid;
	private List<Jylm> jylmlist;
	private Map<String, String> microdata;
	private Map<String, String> otherdata;
	private boolean ztc;
	private Map<String, String> moduleMap;
	private String topitemid;
	private String cookieString;
	private List<Fblm> fblmList;
	private long yhid;
	private int jysjMsgType;

	private int sycmfree; // 是否新版生意参谋：0.否、1.是、2.无法判断
	private JSONObject shopInfo ; //卖家中心店铺信息
	private boolean b2c ; // 店铺类型（海外天猫店也是天猫店）：true是天猫店、false是淘宝店
	//是否海外店铺
    private boolean overseas;
    
    private long logintime; // 登录时间
    private int loginlx; // 淘系登录方式：0.千牛或淘宝、1.淘宝客
    private Dp dp;

	public Cjzh() {
	}

	public Cjzh(CookieStore cookieStore) {
		this();
		this.cookieStore = cookieStore;
	}

	public Jylm getLmForId(String cateid) {
		for (int i = 0; jylmlist != null && i < jylmlist.size(); i++) {
			if (cateid.equalsIgnoreCase(jylmlist.get(i).getCateid())) {
				return jylmlist.get(i);
			}
		}
		return null;
	}

	public String getCookieKey() {
		return cookieKey;
	}

	public void setCookieKey(String cookieKey) {
		this.cookieKey = cookieKey;
	}

	public void setMicrodata(Map<String, String> microdata) {
		this.microdata = microdata;
	}

	public String getMicrodata(String key, String defaultval) {
		return microdata.getOrDefault(key, defaultval);
	}

	public String getMicrodata(String key) {
		return this.getMicrodata(key, "");
	}

	public String getZzh() {
		return zzh;
	}

	public CookieStore getCookieStore() {
		return cookieStore;
	}

	public List<Jylm> getJylmlist() {
		return jylmlist;
	}

	public void setZzh(String zzh) {
		this.zzh = zzh;
	}

	public void setCookieStore(CookieStore cookieStore) {
		this.cookieStore = cookieStore;
	}

	public void setJylmlist(List<Jylm> jylmlist) {
		this.jylmlist = jylmlist;
	}

	public String getZhuzh() {
		return zhuzh;
	}

	public long getDpid() {
		return dpid;
	}

	public String getDpmc() {
		return dpmc;
	}

	public void setZhuzh(String zhuzh) {
		this.zhuzh = zhuzh;
	}

	public void setDpid(long dpid) {
		this.dpid = dpid;
	}

	public void setDpmc(String dpmc) {
		this.dpmc = dpmc;
	}

	public long getJlbdpid() {
		return jlbdpid;
	}

	public void setJlbdpid(long jlbdpid) {
		this.jlbdpid = jlbdpid;
	}

	public Map<String, String> getMicrodata() {
		return microdata;
	}

	public boolean isZtc() {
		return ztc;
	}

	public void setZtc(boolean ztc) {
		this.ztc = ztc;
	}

	public Map<String, String> getModuleMap() {
		return moduleMap;
	}

	public void setModuleMap(Map<String, String> moduleMap) {
		this.moduleMap = moduleMap;
	}

	public String getTopitemid() {
		return topitemid;
	}

	public void setTopitemid(String topitemid) {
		this.topitemid = topitemid;
	}

	public long getUserid() {
		return userid;
	}

	public void setUserid(long userid) {
		this.userid = userid;
	}

	public Map<String, String> getOtherdata() {
		if (this.otherdata == null) {
			this.otherdata = new HashMap<String, String>();
		}
		return otherdata;
	}

	public void setOtherdata(Map<String, String> otherdata) {
		this.otherdata = otherdata;
	}

	public void putData(String key, String value) {
		if (this.otherdata == null) {
			this.otherdata = new HashMap<String, String>();
		}
		this.otherdata.put(key, value);
	}

	public String getData(String key) {
		return this.getOtherdata().get(key);
	}

	public String toString() {
		Gson gson = new Gson();
		return gson.toJson(this);
	}

	/**
	 * 回写cookie
	 */
	public synchronized boolean backCookie(CookieStore newCookieStore) {
		if (this.cookieStore == null || this.cookieStore.getCookies() == null || this.cookieStore.getCookies().size() < 1) {
			return false;
		}
		if (newCookieStore == null || newCookieStore.getCookies() == null || newCookieStore.getCookies().size() < 1) {
			return false;
		}
		List<Cookie> newList = newCookieStore.getCookies();
		for (int i = 0; newList != null && i < newList.size(); i++) {
			Cookie newCookie = newList.get(i);
			if (StringUtil.isNull(newCookie.getName()) || StringUtil.isNull(newCookie.getDomain())) {
				continue;
			}
			this.cookieStore.addCookie(newCookie);
		}
		return true;
	}

	public String getCookieString() {
		return cookieString;
	}

	public void setCookieString(String cookieString) {
		this.cookieString = cookieString;
	}

	public List<Fblm> getFblmList() {
		return fblmList;
	}

	public void setFblmList(List<Fblm> fblmList) {
		this.fblmList = fblmList;
	}

	public long getYhid() {
		return yhid;
	}

	public void setYhid(long yhid) {
		this.yhid = yhid;
	}

	public int getJysjMsgType() {
		return jysjMsgType;
	}

	public void setJysjMsgType(int jysjMsgType) {
		this.jysjMsgType = jysjMsgType;
	}

	public int getSycmfree() {
		return sycmfree;
	}

	public void setSycmfree(int sycmfree) {
		this.sycmfree = sycmfree;
	}

	public JSONObject getShopInfo() {
		return shopInfo;
	}

	public void setShopInfo(JSONObject shopInfo) {
		this.shopInfo = shopInfo;
	}

	public boolean isB2c() {
		return b2c;
	}

	public void setB2c(boolean b2c) {
		this.b2c = b2c;
	}
	
	/**
	 * initShopInfo，初始化
	 */
	public void initShopInfo(String json) {
		try {
			if (!StrUtil.isJson2(json)) {
				System.out.println("------------- 采集账号，initShopInfo，不是json：" + json);
				return;
			}
			JSONObject jsonObject = JSONObject.parseObject(json);
			JSONObject result = jsonObject.getJSONObject("data").getJSONObject("result");
			this.shopInfo = result;
			this.b2c = result.getBooleanValue("tmallSeller");
		} catch (Exception e) {
			System.out.println("------------- 采集账号，initShopInfo，初始化失败：" + json);
			e.printStackTrace();
		}
	}

	public String getCookieStr() {
		try {
			List<Cookie> list = this.getCookieStore().getCookies();
			String result = "";
			for (Cookie cookie : list) {
				if (cookie.getDomain().startsWith(".taobao.com") || cookie.getDomain().startsWith("adbrain.taobao.com")) {
					result = result + cookie.getName() + "=" + cookie.getValue() + "; ";
				}
			}
			if (result.length() > 2) {
				result = result.substring(0, result.length() - 2);
			}
			return result;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	public String getSycmCookieStr() {
		try {
			List<Cookie> list = this.getCookieStore().getCookies();
			String result = "";
			for (Cookie cookie : list) {
				if (cookie.getDomain().startsWith(".taobao.com") || cookie.getDomain().startsWith(".sycm.taobao.com") || cookie.getDomain().startsWith("sycm.taobao.com")) {
					result = result + cookie.getName() + "=" + cookie.getValue() + "; ";
				}
			}
			if (result.length() > 2) {
				result = result.substring(0, result.length() - 2);
			}
			return result;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	/**
	 * updateH5tk 如果不成功，说明_m_h5_tk已过期，替换掉旧的_m_h5_tk、_m_h5_tk_enc
	 */
	public void updateH5tk() {
		if (this.cookieStore == null || this.cookieStore.getCookies() == null || this.cookieStore.getCookies().size() < 1) {
			return;
		}
		try {
			for (int i = 0; this.cookieStore != null && this.cookieStore.getCookies() != null && i < this.cookieStore.getCookies().size(); i++) {
				Cookie newCookie = this.cookieStore.getCookies().get(i);
				if (StringUtil.isNull(newCookie.getName()) || StringUtil.isNull(newCookie.getDomain())) {
					continue;
				}
				if (!"taobao.com".equalsIgnoreCase(newCookie.getDomain())) {
					continue;
				}
				if ("_m_h5_tk".equalsIgnoreCase(newCookie.getName()) || "_m_h5_tk_enc".equalsIgnoreCase(newCookie.getName())) {
					BasicClientCookie c2 = new BasicClientCookie(newCookie.getName(), newCookie.getValue());
					c2.setDomain(".taobao.com"); // c2.setDomain(newCookie.getDomain());
					c2.setPath(newCookie.getPath());
					c2.setCreationDate(new Date());
					c2.setExpiryDate(newCookie.getExpiryDate()); // 此时newCookie.getExpiryDate()是null
					c2.setSecure(newCookie.isSecure());
					// c2.setHttpOnly(newCookie.isHttpOnly());
					c2.setVersion(1);
					this.cookieStore.addCookie(c2);
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * getH5Token 获取cookie里面的_m_h5_tk
	 * 
	 */
	public String getH5Token(String domain, boolean contains) {
		List<Cookie> cookies = this.getCookieStore().getCookies();
		if (cookies == null) {
			return "";
		}

		Map<Integer, String> map = new HashMap<>();
		for (Cookie cookie : cookies) {
			if (cookie.getName().equals("_m_h5_tk") && (contains ? cookie.getDomain().contains(domain) : cookie.getDomain().equals(domain))) {
				map.put(cookie.getVersion(), cookie.getValue().split("_")[0]);
			}
		}

		if (map.isEmpty()) {
			return "";
		}

		List<Integer> keys = new ArrayList<>(map.keySet());
		Collections.sort(keys);

		return map.get(keys.get(0));
	}
	
	public boolean isOverseas() {
        return overseas;
    }

    public void setOverseas(boolean overseas) {
        this.overseas = overseas;
    }

	public long getLogintime() {
		return logintime;
	}

	public Dp getDp() {
		return dp;
	}

	public void setLogintime(long logintime) {
		this.logintime = logintime;
	}

	public void setDp(Dp dp) {
		this.dp = dp;
	}

	public int getLoginlx() {
		return loginlx;
	}

	public void setLoginlx(int loginlx) {
		this.loginlx = loginlx;
	}
}
