package com.abujlb.ckstore;

public class Dp {
	/**
	 * 俱乐部店铺
	 */
	public static final int TYPE_JLB = 1;
	/**
	 * 多店铺管理的店铺
	 */
	public static final int TYPE_DDPGL = 3;
	/**
	 * 华南俱乐部店铺
	 */
	public static final int TYPE_HN = 2;

	/**
	 * 竞争分析
	 */
	public static final int TYPE_JZFX = 4;

	/**
	 * 其它帐号，不采集经营日报
	 */
	public static final int TYPE_OTHER = 99;

	private long dpid;
	private long userid;
	private String zhuzh;
	private String zizh;
	private String dpmc;
	private int jlbdpid;
	private int lx;
	private int bi;
	private int biqydps;  //BI企业店铺数
	private int fixed;
	private int zt;

	public long getDpid() {
		return dpid;
	}

	public String getZhuzh() {
		return zhuzh;
	}

	public String getZizh() {
		return zizh;
	}

	public String getDpmc() {
		return dpmc;
	}

	public void setDpid(long dpid) {
		this.dpid = dpid;
	}

	public void setZhuzh(String zhuzh) {
		this.zhuzh = zhuzh;
	}

	public void setZizh(String zizh) {
		this.zizh = zizh;
	}

	public void setDpmc(String dpmc) {
		this.dpmc = dpmc;
	}

	public int getJlbdpid() {
		return jlbdpid;
	}

	public int getLx() {
		return lx;
	}

	public void setJlbdpid(int jlbdpid) {
		this.jlbdpid = jlbdpid;
	}

	public void setLx(int lx) {
		this.lx = lx;
	}

	public int getZt() {
		return zt;
	}

	public void setZt(int zt) {
		this.zt = zt;
	}

	public int getFixed() {
		return fixed;
	}

	public void setFixed(int fixed) {
		this.fixed = fixed;
	}

	public long getUserid() {
		return userid;
	}

	public void setUserid(long userid) {
		this.userid = userid;
	}

	public int getBi() {
		return bi;
	}

	public void setBi(int bi) {
		this.bi = bi;
	}

	public int getBiqydps() {
		return biqydps;
	}

	public void setBiqydps(int biqydps) {
		this.biqydps = biqydps;
	}
}
