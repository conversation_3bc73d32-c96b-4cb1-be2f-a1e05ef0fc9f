package com.abujlb.ckstore;

public class OnOffBean implements Comparable<OnOffBean> {
	private String code;
	private String name;
	private boolean on;

	@Override
	public int compareTo(OnOffBean o) {
		return this.code.compareTo(o.code);
	}

	public String getCode() {
		return code;
	}

	public String getName() {
		return name;
	}

	public boolean isOn() {
		return on;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public void setName(String name) {
		this.name = name;
	}

	public void setOn(boolean on) {
		this.on = on;
	}
}
