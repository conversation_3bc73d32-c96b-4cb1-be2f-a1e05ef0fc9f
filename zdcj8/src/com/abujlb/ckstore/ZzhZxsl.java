package com.abujlb.ckstore;

import com.abujlb.jzts2.pojo.JztsPara;

/**
 * 在线帐号数量
 * 
 * <AUTHOR>
 * @date 2021-04-08
 *
 */
public class ZzhZxsl {
	private String cateid;
	private int pro;
	private int std;
	private int all;

	public String getCateid() {
		return cateid;
	}

	public int getPro() {
		return pro;
	}

	public int getStd() {
		return std;
	}

	public void setCateid(String cateid) {
		this.cateid = cateid;
	}

	public void setPro(int pro) {
		this.pro = pro;
	}

	public void setStd(int std) {
		this.std = std;
	}

	public int getAll() {
		return all;
	}

	public void setAll(int all) {
		this.all = all;
	}

	public boolean canUse(String lx) {
		switch (lx) {
			case JztsPara.LX_DP:
			case JztsPara.LX_SP:
				if (this.std > 0 || this.pro > 0) {
					return true;
				} else {
					return false;
				}
			case JztsPara.LX_RSC:
			case JztsPara.LX_SCPH_DP:
			case JztsPara.LX_SCPH_SP:
			case JztsPara.LX_SCPH_PP:
				if (this.pro > 0) {
					return true;
				} else {
					return false;
				}
		}
		return false;
	}

}
