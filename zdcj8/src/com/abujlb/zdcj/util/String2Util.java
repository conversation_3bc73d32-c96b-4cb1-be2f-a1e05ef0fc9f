package com.abujlb.zdcj.util;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

/**
 * 字符串 工具类（zdcj2项目专属，因为服务器common.jar不敢更新，不知道StringUtil是否能正常调用）
 * 
 * <AUTHOR>
 * @date 2023-08-14 21:56:42
 */
public class String2Util {

	public static String getRandomString(int length) { // length表示生成字符串的长度
		String base = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
		Random random = new Random();
		StringBuffer sb = new StringBuffer(length);
		for (int i = 0; i < length; i++) {
			int number = random.nextInt(base.length());
			sb.append(base.charAt(number));
		}
		return sb.toString();
	}

	public static String getRandomNum(int length) { // length表示生成字符串的长度
		String base = "0123456789";
		Random random = new Random();
		StringBuffer sb = new StringBuffer(length);
		for (int i = 0; i < length; i++) {
			int number = random.nextInt(base.length());
			sb.append(base.charAt(number));
		}
		return sb.toString();
	}

	public static boolean isMobileNO(String mobiles) {
		Pattern p = Pattern.compile("^((13[0-9])|(15[^4,\\D])|(18[0-9])|(147))\\d{8}$");
		Matcher m = p.matcher(mobiles);
		return m.matches();
	}

	public static List<String> splitForLength(String src, int length) {
		List<String> list = new ArrayList<String>();
		for (int i = 0; src != null && i < src.length(); i = i + length) {
			if (i + length < src.length()) {
				list.add(src.substring(i, i + length));
			} else {
				list.add(src.substring(i));
			}
		}
		return list;
	}

	/**
	 * 判断字符串是否为空(null或者空字符串)
	 * 
	 * @param str 目标字符串
	 * @return true 空 false 非空
	 */
	public static boolean isNull(String str) {
		if (str == null || "".equals(str.trim())) {
			return true;
		}
		return false;
	}

	/**
	 * 判断字符串是否为空(null或者空字符串)
	 * 
	 * @param str 目标字符串
	 * @return true 空 false 非空
	 */
	public static boolean isNull2(String str) {
		if (str == null || "".equals(str.trim()) || "null".equalsIgnoreCase(str.trim())) {
			return true;
		}
		return false;
	}

	/**
	 * 判断字符串是否为json格式
	 * 
	 * @param str 目标字符串
	 */
	public static boolean isJson(String str) {
		if (isNull(str)) {
			return false;
		}
		try {
			// JSONObject jsonObj = JSONObject.fromObject(str);
			// JSONObject.fromObject(str);
			JSONObject.parseObject(str);
			return true;
		} catch (Exception e) {
			return false;
		}
	}

	/**
	 * 判断字符串是否为json格式
	 * 
	 * @param str 目标字符串
	 */
	public static boolean isJson2(String str) {
		if (isNull2(str)) {
			return false;
		}
		try {
			// JSONObject jsonObj = JSONObject.fromObject(str);
			// JSONObject.fromObject(str);
			JSONObject.parseObject(str);
			return true;
		} catch (Exception e) {
			return false;
		}
	}

	/**
	 * 判断字符串是否为jsonArray格式
	 * 
	 * @param str 目标字符串
	 */
	public static boolean isJsonArray(String str) {
		if (isNull(str)) {
			return false;
		}
		try {
			// JSONArray.fromObject(str);
			JSONArray.parseArray(str);
			return true;
		} catch (Exception e) {
			return false;
		}
	}

	/**
	 * 判断字符串是否为jsonArray格式
	 * 
	 * @param str 目标字符串
	 */
	public static boolean isJsonArray2(String str) {
		if (isNull2(str)) {
			return false;
		}
		try {
			// JSONArray.fromObject(str);
			JSONArray.parseArray(str);
			return true;
		} catch (Exception e) {
			return false;
		}
	}

	public static String convertNull(String str) {
		if (str == null) {
			return "";
		}
		return str;
	}

	public static boolean convertNull(Boolean bool) {
		if (bool == null) {
			return false;
		}
		return bool.booleanValue();
	}

	/**
	 * 获取唯一文件名
	 * 
	 * @param ext 后缀名
	 */
	public static String getFileName(String ext) {
		return UUID.randomUUID().toString().replaceAll("-", "") + ext;
	}

}
