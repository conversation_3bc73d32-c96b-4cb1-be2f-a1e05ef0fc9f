package com.abujlb.zdcj8.test.msg;

import java.util.ArrayList;
import java.util.List;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.abujlb.BaseTest;
import com.abujlb.Config;
import com.abujlb.jyrb.bean.Dp;
import com.abujlb.jyrb.bean.JysjclMsg;
import com.abujlb.jyrb.service.DataUploader;
import com.abujlb.mq.TxyunMq;

public class TestSendMsg extends BaseTest {

	@Autowired
	private TxyunMq mq;
	@Autowired
	private Config config;
	@Autowired
	private DataUploader du;

	private void send(Dp dp) {
		String[] rqs = { "2019-11-02", "2019-11-09", "2019-11-16", "2019-11-23", "2019-11-30", "2019-12-07",
				"2019-12-14", "2019-12-21", "2019-12-28", "2019-12-31", "2020-01-04", "2020-01-11", "2020-01-18",
				"2020-01-25", "2020-01-31", "2020-02-01", "2020-02-08", "2020-02-15", "2020-02-22" };
		List<JysjclMsg> msgList = new ArrayList<JysjclMsg>();
		for (int i = 0; i < rqs.length; i++) {
			JysjclMsg jysjclMsg = new JysjclMsg();
			jysjclMsg.setDp(dp);
			jysjclMsg.setRq(rqs[i]);
			jysjclMsg.setRqlist(new String[] { rqs[i] });
			msgList.add(jysjclMsg);
			if (msgList.size() > 10) {
				mq.batchSendMessage2(config.getString("jysjclQueue"), "jysjcl", msgList);
				msgList.clear();
			}
		}
		if (msgList.size() > 0) {
			mq.batchSendMessage2(config.getString("jysjclQueue"), "jysjcl", msgList);
			msgList.clear();
		}
	}

	@Test
	public void test() {
		int[] dpids = { 1913, 1916, 1917, 1922, 1931, 1942, 1948, 1953, 1994, 1997, 1999, 2006, 2015, 2018, 2026, 2038,
				2048, 2051, 2056, 2059, 2060, 2099, 2107, 2111, 2115, 2121, 2139, 2140, 2141, 2154, 2155, 2166, 2167,
				2168, 2169, 2174, 2180, 2181, 2188, 2204, 2208, 2232, 2234, 2237, 2241, 2249, 2261, 2266, 2268, 2278,
				2280, 2281, 2590, 2602, 2610, 2617, 2638, 2648, 2652, 2658, 2664, 2669, 2670, 2673, 2690, 2694, 2695,
				2696, 2700, 2701, 2702, 2713, 2717, 2719, 2728, 2729, 2730, 2731, 2749, 2750, 2751, 2753, 2758, 2759,
				2768, 2771, 2785, 2788, 2835, 2841, 2843, 2848, 2851, 2854, 2856, 2858, 2868, 2871, 2884, 2885, 2890,
				2891, 2894, 2895, 2896, 2897, 2899, 2904, 2915, 2919, 2923, 2925, 2926, 2929, 2930, 2931, 2934, 2935,
				2939, 2949, 2950, 2951, 2952, 2953, 2960, 2961, 2966, 2971, 2975, 2976, 2978, 2981, 2988, 4243, 4244,
				4250, 4251, 4256, 4266, 4270, 4271, 4277, 4281, 4303, 4391, 4397, 4398, 4405, 4416, 4417, 4420, 4428,
				4442, 4444, 4461, 4483, 4521, 4541, 4542, 4545, 4547, 4552, 4553, 4556, 4557, 4558, 4563, 4564, 4565,
				4568, 4569, 4570, 4572, 4574, 4576, 4579, 4581, 4584, 4585, 4642, 4645, 4648, 4649, 4662, 4667, 4669,
				4678, 4684, 4686, 4701, 4723, 4761, 4822, 4823, 4824, 4825, 4828, 4833, 4840, 4841, 4844, 4846, 4853,
				4854, 4857, 4901, 4904, 5021, 5048, 5051, 5052, 5053, 5055, 5058, 5086, 5088, 5102, 5103, 5105, 5122,
				5129, 5130, 5132, 5134, 5136, 5202, 5221, 5242, 5244, 5246, 5254, 5256, 5257, 5260, 5282, 5284, 5285,
				5286, 5288, 5295, 5302, 5306, 5307, 5308, 5309, 5311, 5312, 5361, 5461, 5521, 5523, 5524, 5525, 5526,
				5529, 5530, 5531, 5532, 5533, 5534, 5536, 5539, 5540, 5541, 5543, 5545, 5546, 5548, 5551, 5555, 5556,
				5558, 5559, 5601, 5621, 5661, 5662, 5695, 5696, 5701, 5767, 5768, 5771, 5773, 5775, 5778, 5780, 5782,
				5783, 5786, 5790, 5791, 5794, 5795, 5796, 5798, 5800, 5801, 5802, 5805, 5806, 5807, 5808, 5810, 5811,
				5813, 5814, 5823, 5824, 5825, 5828, 5829, 5831, 5834, 5835, 5836, 5851, 5857, 5861, 5921, 5944, 5947,
				5951, 5952, 5953, 5954, 5955, 5956, 5960, 5962, 5964, 5966, 5967, 5968, 5969, 5970, 5971, 5973, 5974,
				5975, 5976, 5977, 5978, 5985, 5986, 5989, 5990, 6003, 6041, 6062, 6141, 6143, 6182, 6222, 6241, 6261,
				6262, 6263, 6265, 6267, 6272, 6273, 6275, 6276, 6277, 6283, 6284, 6285, 6286, 6287, 6290, 6293, 6294,
				6299, 6300, 6304, 6305, 6306, 6308, 6311, 6312, 6341, 6382, 6401, 6402, 6441, 6442, 6444, 6445, 6446,
				6452, 6453, 6457, 6459, 6460, 6461, 6462, 6469, 6471, 6473, 6476, 6477, 6478, 6479, 6481, 6482, 6483,
				6486, 6489, 6491, 6492, 6497, 6498, 6522, 6523, 6541, 6561, 6581, 6639, 6658, 6659, 6683, 6702, 6742,
				6743, 6744, 6745, 6746, 6747, 6749, 6751, 6752, 6754, 6756, 6758, 6759, 6760, 6761, 6762, 6763, 6765,
				6766, 6767, 6768, 6769, 6772, 6774, 6776, 6781, 6782, 6801, 6821, 6881, 6901, 6941, 6961, 6982, 7041,
				7061, 7062, 7063, 7064, 7065, 7067, 7068, 7070, 7071, 7072, 7073, 7074, 7075, 7076, 7077, 7078, 7079,
				7080, 7081, 7082, 7083, 7084, 7085, 7086, 7087, 7088, 7089, 7090, 7092, 7093, 7094, 7097, 7098, 7099,
				7100, 7101, 7107, 7108, 7109, 7110, 7111, 7141, 7161, 7162, 7201, 7222, 7241, 7281, 7302, 7303, 7305,
				7306, 7308, 7309, 7310, 7311, 7312, 7313, 7314, 7315, 7316, 7317, 7318, 7319, 7320, 7321, 7322, 7324,
				7327, 7328, 7329, 7330, 7331, 7333, 7361, 7381, 7422, 7482, 7483, 7501, 7541, 7561, 7563, 7565, 7566,
				7567, 7572, 7573, 7574, 7575, 7576, 7577, 7578, 7579, 7583, 7584, 7585, 7587, 7588, 7589, 7590, 7592,
				7593, 7594, 7595, 7596, 7597, 7599, 7600, 7601, 7602, 7603, 7604, 7606, 7607, 7641, 7681, 7682, 7701,
				7741, 7782, 7801, 7802, 7803, 7804, 7805, 7807, 7808, 7809, 7810, 7811, 7812, 7813, 7814, 7815, 7816,
				7817, 7818, 7820, 7821, 7822, 7823, 7824, 7825, 7827, 7828, 7829, 7830, 7831, 7832, 7833, 7834, 7835,
				7836, 7838, 7841, 7843, 7861, 7862, 7863, 7864, 7865, 7866, 7867, 7881, 7901, 7921, 7942, 7943, 7944,
				7961, 8041, 8042, 8043, 8044, 8081, 8101, 8102, 8141, 8161, 8181, 8182, 8183, 8201, 8221, 8222, 8223,
				8224, 8225, 8241, 8242, 8243, 8244, 8245, 8246, 8248, 8250, 8251, 8252, 8253, 8254, 8255, 8256, 8257,
				8258, 8259, 8260, 8261, 8263, 8264, 8265, 8269, 8270, 8271, 8272, 8273, 8274, 8275, 8276, 8277, 8278,
				8279, 8280, 8282, 8284, 8285, 8287, 8301, 8302, 8303, 8304, 8305, 8306, 8321, 8322, 8323, 8325, 8326,
				8328, 8361, 8382, 8383, 8401, 8421, 8442, 8444, 8461, 8501, 8521, 8522, 8541, 8542, 8544, 8545, 8546,
				8561, 8562, 8581, 8601, 8602, 8603, 8605, 8606, 8607, 8608, 8609, 8610, 8611, 8612, 8613, 8614, 8615,
				8616, 8617, 8618, 8619, 8620, 8621, 8623, 8624, 8628, 8661, 8662, 8781, 8801, 8821, 8822, 8824, 8841,
				8861, 8862, 8863, 8921, 8941, 8961, 8981, 8982, 8983, 8984, 8985, 8987, 8990, 8991, 8992, 8993, 8994,
				8995, 8996, 8997, 8998, 8999, 9000, 9001, 9002, 9003, 9004, 9006, 9007, 9008, 9009, 9010, 9012, 9013,
				9014, 9015, 9016, 9017, 9018, 9019, 9021, 9041, 9042, 9061, 9081, 9101, 9121, 9141, 9142, 9143, 9146,
				9161, 9162, 9163, 9164, 9165, 9167, 9168, 9169, 9181, 9201, 9221, 9241, 9242, 9261, 9281, 9302, 9321,
				9322, 9323, 9341, 9361, 9362, 9381, 9382, 9401, 9402, 9404, 9405, 9407, 9409, 9411, 9412, 9413, 9414,
				9415, 9416, 9418, 9419, 9420, 9421, 9422, 9423, 9424, 9425, 9427, 9429, 9430, 9431, 9433, 9435, 9436,
				9437, 9439, 9440, 9441, 9442, 9444, 9445, 9446, 9448, 9450, 9463, 9465, 9481, 9501, 9521, 9561, 9581,
				9582, 9602, 9621, 9622, 9623, 9641, 9661, 9662, 9663, 9667, 9681, 9701, 9702, 9721, 9741, 9742, 9743,
				9745, 9749, 9750, 9752, 9754, 9755, 9761, 9781, 9801, 9802, 9821, 9822, 9841, 9842, 9843, 9844, 9862,
				9863, 9864, 9865, 9866, 9867, 9868, 9869, 9870, 9872, 9873, 9874, 9875, 9876, 9877, 9878, 9879, 9880,
				9881, 9883, 9884, 9885, 9887, 9888, 9889, 9891, 9892, 9893, 9894, 9896, 9897, 9898, 9901, 9904, 9905,
				9906, 9941, 9961, 9962, 9963, 9964, 9965, 9966, 9981, 9982, 9983, 9984, 9985, 9987, 9988, 10001, 10002,
				10003, 10021, 10041, 10061, 10062, 10063, 10083, 10084, 10085, 10086, 10087, 10088, 10090, 10091, 10092,
				10094, 10095, 10098, 10099, 10100, 10101, 10102, 10103, 10104, 10105, 10106, 10108, 10109, 10111, 10114,
				10116, 10118, 10120, 10142, 10143, 10144, 10161, 10181, 10182, 10183, 10201, 10202, 10203, 10204, 10205,
				10221, 10222, 10223, 10224, 10225, 10241, 10242, 10243, 10244, 10245, 10261, 10262, 10263, 10281, 10282,
				10283, 10284, 10301, 10302, 10303, 10304, 10305, 10307, 10308, 10309, 10310, 10313, 10314, 10318, 10321,
				10322, 10341, 10342, 10343, 10381, 10401, 10402, 10403, 10404, 10405, 10406, 10408, 10409, 10410, 10411,
				10421, 10422, 10426, 10427, 10441, 10442, 10443, 10444, 10461, 10462, 10463, 10481, 10501, 10521, 10522,
				10541, 10542, 10581, 10582, 10584, 10586, 10587, 10588, 10589, 10590, 10591, 10592, 10593, 10594, 10595,
				10596, 10598, 10600, 10601, 10602, 10603, 10604, 10605, 10606, 10607, 10608, 10609, 10611, 10612, 10613,
				10614, 10615, 10616, 10618, 10619, 10620, 10621, 10623, 10624, 10625, 10626, 10627, 10628, 10641, 10642,
				10643, 10661, 10663, 10664, 10666, 10668, 10669, 10671, 10672, 10673, 10674, 10675, 10676, 10681, 10701,
				10702, 10703, 10705, 10706, 10721, 10741, 10761, 10762, 10781, 10802, 10803, 10821, 10823, 10824, 10825,
				10826, 10827, 10828, 10830, 10831, 10832, 10833, 10834, 10835, 10836, 10837, 10838, 10839, 10840, 10841,
				10842, 10843, 10844, 10845, 10846, 10847, 10848, 10849, 10850, 10851, 10852, 10853, 10854, 10855, 10856,
				10857, 10858, 10861, 10882, 10901, 10921, 10941, 10942, 10945, 10946, 10947, 10949, 10950, 10951, 10952,
				10953, 10954, 10955, 10956, 10957, 10958, 10959, 10960, 10962, 10963, 10964, 10965, 10966, 10969, 10970,
				10971, 10972, 10981, 10982, 11001, 11021, 11022, 11023, 11024, 11025, 11041, 11042, 11043, 11044, 11045,
				11046, 11062, 11063, 11065, 11066, 11067, 11081, 11101, 11121, 11141, 11142, 11145, 11148, 11149, 11150,
				11151, 11161, 11163, 11164, 11181, 11201, 11202, 11203, 11241, 11242, 11262, 11272, 11273, 11286, 11288,
				11289, 11290, 11291, 11292, 11293, 11301, 11302, 11321, 11322, 11341, 11342, 11343, 11344, 11345, 11346,
				11347, 11348, 11349, 11350, 11351, 11352, 11354, 11355, 11356, 11357, 11358, 11359, 11361, 11362, 11363,
				11364, 11365, 11366, 11367, 11368, 11369, 11370, 11371, 11373, 11374, 11375, 11378, 11379, 11380, 11381,
				11382, 11383, 11384, 11385, 11386, 11401, 11402, 11403, 11404, 11405, 11406, 11407, 11421, 11422, 11423,
				11424, 11441, 11442, 11462, 11464, 11465, 11481, 11482, 11483, 11501, 11521, 11541, 11543, 11544, 11563,
				11565, 11566, 11581, 11601, 11623, 11687, 11688, 11729, 11730, 11732, 11735, 11736, 11740, 11744, 11747,
				11748, 11749, 11750, 11751, 11752, 11753, 11755, 11762, 11765, 11770, 11772, 11775, 11844, 11845, 11846,
				11847, 11861, 11862, 11863, 11864, 11865, 11866, 11867, 11868, 11869, 11870, 11871, 11872, 11873, 11874,
				11875, 11877, 11878, 11879, 11881, 11882, 11883, 11884, 11885, 11886, 11887, 11888, 11889, 11890, 11891,
				11892, 11893, 11894, 11895, 11896, 11897, 11898, 11899, 11900, 11901, 11902, 11903, 11904, 11908, 11909,
				11910, 11911, 11913, 11914, 11915, 11922, 11925, 11926, 11928, 11941, 11942, 11943, 11944, 11945, 11946,
				11947, 11948, 11949, 11950, 11951, 11952, 11953, 11954, 11955, 11956, 11958, 11959, 11960, 11961, 11962,
				11963, 11964, 11965, 11966, 11967, 11968, 11969, 11970, 11972, 11973, 11975, 11976, 11977, 11978, 11979,
				11980, 11981, 11982, 11984, 11986, 11987, 12001, 12023, 12044, 12047, 12064, 12069, 1000001, 1000002,
				1000003, 1000121, 1000122, 1000123, 1000124, 1000125, 1000126, 1000127, 1000128, 1000129, 1000130,
				1000131, 1000132, 1000141, 1000143, 1000144, 1000145, 1000146, 1000147, 1000148, 1000149, 1000150,
				1000151, 1000152, 1000161, 1000204, 1000281, 1000282, 1000283, 1000284, 1000285, 1000286, 1000287,
				1000301, 1000302, 1000303, 1000304, 1000305, 1000306, 1000307, 1000308, 1000321 };

		for (int i = 0; i < dpids.length; i++) {
			int dpid = dpids[i];
			Dp dp = du.hqdpForId(dpid);
			if (dp != null) {
				this.send(dp);
			}
			System.out.printf("%d/%d\n", i + 1, dpids.length);
		}
	}
}
