package com.abujlb.zdcj8.test;

import com.abujlb.AbujlbBeanFactory;
import com.abujlb.BaseTest;
import com.abujlb.ckstore.AbujlbCookieStore;
import com.abujlb.ckstore.Cjzh;
import com.abujlb.jyrb.bean.JysjMsg;
import com.abujlb.jyrb.http.SycmHttpClient;
import com.abujlb.jyrb.service.JysjcjService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class TestJysjcj extends BaseTest {
	
	@Autowired
	private AbujlbBeanFactory beanFactory;
	@Autowired
    private AbujlbCookieStore cookieStore;


	@Test
	public void test() {
		JysjMsg msg = new JysjMsg();
		msg.setCookieKey("ck_784ef13ca1c140e49064b80e1ae1303c");
		msg.setType(JysjMsg.TYPE_LOGIN);
		JysjcjService service = beanFactory.getBean(JysjcjService.class, msg);
		boolean result = service.process();
		System.out.println(result);
	}
	
	@Test
	public void test2() {
		// Cjzh cjzh = cookieStore.getCjzhForKey("ck_9441fe4dfc854dbd90f65f9e61b7436d");
		Cjzh cjzh = cookieStore.getCjzhForKey("ck_57327f1af9114312bdccba39f5b9ad7c");
		SycmHttpClient.download(cjzh, "2022-11-24", "0");
	}


}
