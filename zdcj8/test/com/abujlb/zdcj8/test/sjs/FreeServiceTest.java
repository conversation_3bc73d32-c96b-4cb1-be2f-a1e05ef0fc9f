package com.abujlb.zdcj8.test.sjs;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.abujlb.BaseTest;
import com.abujlb.Result;
import com.abujlb.util.Md5;
import com.abujlb.zdcj8.bean.sjs.JpggxsblInput;
import com.abujlb.zdcj8.bean.sjs.MjxInput;
import com.abujlb.zdcj8.script.Md5SjsJSEngine;
import com.abujlb.zdcj8.service.FreeService;

/**
 * <AUTHOR>
 * @date 2020-11-17
 */
public class FreeServiceTest extends BaseTest {

	@Autowired
	private FreeService freeService;


	@Autowired
	private Md5SjsJSEngine md5 ;
	
	@Test
	public void testKey() {
		String userId ="34900";
		String tm = "1638512883000";
		String key = Md5.md5(tm + userId);
		System.out.println(key);
		String keyMd = md5.getKey(tm, userId);
		System.out.println("a47e7eb23ea9933051acd5cf27cb2f52");
		System.out.println(keyMd);
		
		System.out.println();
		System.out.println(md5.getKey("1638514803000", userId));
		System.out.println("65f749ff9c6df4ab0576425ed0334f80");
		
		System.out.println();
		System.out.println(md5.getKey("1659944766000", userId));
		System.out.println("01235a27a1f08c7896b5cca1f08bf361");
		
		try {
			System.out.println(URLEncoder.encode("读书因为爱你,ji8李龙", "UTF-8").replaceAll("%2C", ","));
			System.out.println("%E8%AF%BB%E4%B9%A6%E5%9B%A0%E4%B8%BA%E7%88%B1%E4%BD%A0,ji8%E6%9D%8E%E9%BE%99");
			System.out.println(URLEncoder.encode("电瓶车挡风被冬季", "UTF-8"));
			System.out.println("%E7%94%B5%E7%93%B6%E8%BD%A6%E6%8C%A1%E9%A3%8E%E8%A2%AB%E5%86%AC%E5%AD%A3");
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
		}
		
		System.out.println();
		System.out.println();
		System.out.println(md5.update("feSJ293j0sIj9u3lkj16412849541010aeJztWV9v28gRf9Dh0Dsc8nA4BMXhcBAI6JAUosxd_ndRoLYs2U7sxImcODkjCFbkUtqI4jL8I8kJ3A_Tx6KfqQ_6JJ1dUpQsymkL3EML1A_Ucua3szOzO7Mz9LLxICHzI5KRZeOvR58VkvuMK_sKwkZHM3TDNrFtIkuzDaWt5AQ45_wTC0OyZ3a05qMrFvl8njafXTaR1tH-2Lx6fmUZj5sHcRzSKzp8yrI9U7c7utV89PTk8vys3QzZhDaPqTfhj5vdccKndM-xOlrHwIbWQa7THJCAJKycBqvytBvnsDAMQxKNcjKi8PZprHafAcnjIU-OaJyNlX1stBWfzphHz-mUJzfKvtNWUi-hNHpJUx7mGeORsn-NTUtrI8PQ3rUVMiMsJMOQDu7FaQI3Jok_Jwnt8sjLk4RGHohHuK1kbEo_8Yg-D4KUZsq-ajjamgqaHqSM7A3GoPqYMNA4pWkK8gcZT6QpWZJTMI17JNyigXPpgvpHwxWBxzQSezUkaQXy4rwbkjSFlSKeNSt7YKU4JFnAkymwYKN0LEn5iEUAvr5WdEsb8CA7HyUXkgrsOglwBDaTeUQ4ZQ8AKQCmoyRezfHDUHn37l1bCmz1zNZBr-W4YuDA2JGDo9bBoWTpYoAl7LAviW7L0Vpuv9WzW67bOpSww6OWaxbq_KYCt4yZFkdZjeKPbMz5x5x5k5AXVpUmHVyE5IYmsMKBl7EZfQOjMU8zFo1gVPlgDTs9Pf0iGAawJfDss4QGfFFTaqFm4xw2PlFJLIWKEPAWK31CNhg8P-OFpGr9KL7LuGf3opiELIUTXhq5MWktPyY3IGFA4ZizTAzhyGcJD2GkF3PuQ-ywRaxHPS9LQrVS9g9fWOuC3ExplAmZISsGg5xlm-ueRhlNIipYJ5SE2fhfKcAAPJbIL-iQrnXwVsIU6S_qi_W3BFd0ZVvQnA6n3K-L8b14lxRJXgmR-ZDlU-GIo_7GPioXPMlEVAPtiHt56aM-BDepK7dQR5yPQqp6Mr-qsR-I0IfnPcu8ZnQuD1pN1PbUc-YlXGSAVheLuMRaeQXA6Bw8QqTSZSyUR3zTjihWp-l8WnfFAuiqZIi9aW_5KV1U9BnzKS_gJA3u7Gi7JrKIIHzPZCDDsw3S2zX2fArc-bRkyGtxxSCSQ3awiFATnrvEzeSs2Q7WTM6ayVnSyc-IyB5VDOzal4UaES-UJ6dmdVyxpLQXL1pdveVi8Tw0xBPyo6BYYg8PsdxJU45tyS3GfYlx5Lgr5_ZA5m8pbMukjHpgrPpRXJvTPGXeem-TLC2NGYy5N5kT6Z0-XHtj4NZo8IvdjgY_ibbDc-kKrwalhHQe1PwY5Fme0DReQeLwv0eFSyg_yuQ4eF5EXHHRyw3C2gtxk63ughMaxiK4a3dBtoCrYH1O3uTRGWUw4VUqo7eKWWCEmwyoqFgM2YiFZc6WwPaMJqKu-ZMwHeo53NF3rLkIcxDyvtrZcm12meRpd8wikT_ky8XlQftNL4IEGrZZ-TueC-qrp_TmNEozEobp7iSzEvBLKUBcw3LqaSloS8T6Qu7zRWUHAiPAjHpKzEiHQe2VikEJ7qCdGQg2SazXWf1uomtgKbOakGT_zpzxvNJGDndj5v6E3nRYYbB82Y2jMdSRWNPQClsR_r9T_3s7FcUvXlyMecaPoMPrLeTle4cirmzu56JVqKXiqMjFH2MB9wFOF3hXPpaljefzu8VoQdtdfkrGqmmAxsoj0YxAQ_IZWixIc_uKaWLPo44emGbgWp7lBBh5HtiGHeTYga_cwrXtH0LDNFH2AxKm0ARlPPfGgzyOoUyC3kZrF3T5hDUCKMVEg-QhEwVoiIYBsuEn0OHNRxSBcPhD0H068OIighwN68h0oPejCDga8jDSHEdDDhpq6z6qWrLoxeIE_Ollgxw6NgWE6jDTBjSdxtlNb0bCMxqNRKeq60BMEp70-5UNMwqFVAITj2Xx1jyNvI5ocGUNV3V7nE8YTXuRKAb9FRVqzlG44UKMiI0DPDQt17Ut39Fs30UGCXzbMgKKDOFCmOMnUGYkOxpH4vuHdExmTOhTqifXeC11PIj8l1S0KHRL3b8cPDs-6zUfiRI9fPTycfPVyVHzOCHxmHlp09K15hGErpfpRwg1Z-l7873WjOXPY1ECyUryIAwH7BOV1sBWiva7jXTNhG2cr3py8YB3HwCuCb_A0MAkKPL9ELrr7ph6k9ei7gMhG3bWMDv4LPL4KGIZB97t7bLxQDbmpzGMNJlmtGXj65z5y8YvcCBsGzuOrlquo-uOZTvLxs8JFSfiEvp_CMwpzPsRWQbCjuGaBpwjkFPmiWXjOyxuSSD9rv_0-Mmy0ZyPSaZS4Kssg2oyXTa-j8mIvve4T2Es2sIAuhlY_NsUAnSL9jXEGigwFcVtAD2SKtu8ZeMhXP-iOiAQ1MvG5TXYoBku_NmuYxmOZrq63RZEU0SBDqcFG5blmO9gmRxMBQVPsK8FPvYN1XRNrBqGhdUhIr5qDrFOsK4j37dKOAZ44GHX0gKq2tpwqBqUENVxYOIwgJ3EJCBwIMH-4jQvG38OJ-5HEl0-f4XP3g6jq8GzrJ_wp-dvz2dvX1y9PDhdNn5gqWz8IIjAX-LYg2enHPLwlM9o8QHr799cf1YWyj5Gtt1WbmAAmQHivtoA2CPjtl1iHEtizBrGrjAYIYlBVg3kOBXIKATp2jbI1cwK5OgSZKAaCOMVSEdGoZJeA-lWBTI0CbLry5lrSWbhAKfmAdeqFNetQnG3DnKMNaiQhLRtR0FgajUU3tbK0JBdQ8F520bpG7o7BUr8bqGMtfK6W6Ccuqz1JuuokCU3cgvlrLXXcIHCbg3lWrcQBw9F2qBJl3hjOHcnpkN0DwWGSkxbV42hjlTHdl3Vd4bYCDAx4PDDpOLTYz8hU5ikXBd5qw37p8oEBrG6EZkiY_yH0fndh_SEEoj5H5DmOq4G2R0y0PcJ_ZhD8jmFlPBr9-TiQ28UW_Mk7S0WZ84seoJGpmUu-scLdvLmQlpqg7qmptvLxk8p6HqYQD9Pk0syAgHuzfmb_pPe5BXNnhx90GfH6ScNHb-6RAef7NH015stAT-HnPgXm_nmH998hlsriiDtDzIi7spqigGToFyAe2YkC4Q637DFl1Hi9-ByzHqRv8G0YH8g9fPpGfBZNNqch8HNivyiI77XiqXX0oET0AyqhZ3KgDzxLQlWE2JpsXANamI4MKIz41FKa1w4wWvuXaUNXXPkIjKXEfmV8K7gQgfCojPIjXm8NV3qmFBf3qJrg8odr5skAg66t90eNLDIRhvsuiUCIF0yjUOa0bvu31J1t0PvFQ4C0D3-3lKz9HZhtORp7dWRqvsH7uwf_fITGayZwRF8yNbehvjwoAidFDfGV9cQRg9YcJWPKdyU38paBwSw6INwsGx00xL2EwtEJK96bUDLj-rLxu-DBEx4SQNREC0br8dZFqf7e3twEXdiqGlyKAp5x-PTvfTGm-6NOPfT9zQIYAWIYSjZur63XvshDYEBErs0WDb-9tXnqgJUxK0nKkIawEsuv8xNWcSGIYsmFaUMJ1lJFpSVwIoQ--A0Ra4n_qsTTSI-75aLfFaSPBL_vdhADBMS-ekGoaxgNihQRc0YnT8jdybOJ5tv8t9Hq5f3UCt7NE23SFAob1IKl690U_hQbItye_tPOzRoPw"));
		System.out.println("b1dca509a7001feb02607190beb764e364ccd7fa");
	}
	/**
	 * 测试：无线详情 
	 * 第一遍测试：√
	 * 第二遍测试：√
	 */
	@Test
	public void wxxq() {
		String bbid = "************";
		Result result = freeService.wxxq(bbid);
		System.out.println(result);
	}
	
	/**
	 * 测试：竞品规格销售比例
	 * 第一遍测试：√
	 * 第二遍测试：√
	 */
	@Test
	public void jpggxsbl() {
		String bbid ="************";
		Result result = freeService.jpggxsbl(bbid);
		System.out.println(result);
	}
	
	/**
	 * 测试：竞品规格销售比例详情
	 * 第一遍测试：√
	 * 第二遍测试：√
	 * 
	 * @remark 这里数据蛇有bug、  A账号创建的记录 B也可以查到
	 */
	@Test
	public void jpggxsblxq() {
		//第一遍测试：查询者与创建者同一个账号
		JpggxsblInput input  = new JpggxsblInput();
		input.setId("7862");
		input.setNid("************");
		input.setNum(3);
		input.setUuid("7e44fb7974bf491d9e4f7d0999197a7c");
		Result result =freeService.jpggxsblxq(input.toString());
		System.out.println(result);
		
		//第二遍测试：查询者与创建者不同一个账号
//		JpggxsblInput input2  = new JpggxsblInput();
//		input2.setId("7650");
//		input2.setNid("************");
//		input2.setNum(1);
//		input2.setUuid("8c7f10e23a994e3b8f5a1c3b4aaa6035");
//		Result result2 =freeService.jpggxsblxq(input2.toString());
//		System.out.println(result2);
	}
	/**
	 * 测试：买家秀印象
	 * 第一遍测试：√
	 * 第二遍测试：√
	 */
	@Test
	public void mjxyx() {
		String bbid ="************";
		Result result = freeService.mjxyx(bbid) ;
		System.out.println(result);
	}
	
	/**
	 * 测试：买家秀
	 */
	@Test
	public void mjx() {
		// 第一遍测试：确定下载按钮
//		MjxInput input = new MjxInput();
//		input.setUrl("************");
//		input.setNum("1");
//		Result result = freeService.mjx_qdxz(input.toString());
//		System.out.println(result);

		// 第二遍测试：点击查看按钮
		MjxInput input2 = new MjxInput();
		input2.setPid("ffd8d2b6a7a44582b7950805abbef5c4");
		input2.setNum("1");
		input2.setUrl("************");
		Result result2 = freeService.mjx(input2.toString());
		System.out.println(result2);

		// 第三遍测试：点击查看按钮2 下一页
//		MjxInput input3 = new MjxInput();
//		input3.setPid("6e9d71232dae42bd925e3fd070902d23");
//		input3.setUrl("616460321632");
//		input3.setNum("2");
//		Result result3 = freeService.mjx(input3.toString());
//		System.out.println(result3);

	}
}
