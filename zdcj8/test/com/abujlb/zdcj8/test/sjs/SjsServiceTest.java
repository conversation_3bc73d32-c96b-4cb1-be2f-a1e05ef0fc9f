package com.abujlb.zdcj8.test.sjs;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.abujlb.BaseTest;
import com.abujlb.Result;
import com.abujlb.zdcj8.bean.sjs.KllqdInput;
import com.abujlb.zdcj8.bean.sjs.SsdbkspBdbInput;
import com.abujlb.zdcj8.bean.sjs.SskspInput;
import com.abujlb.zdcj8.bean.sjs.SsmdInput;
import com.abujlb.zdcj8.service.SjsService;

/**
 * <AUTHOR>
 * @date 2020-11-13
 */
public class SjsServiceTest extends BaseTest {

	@Autowired
	private SjsService sjsService;

	/**
	 * 测试：淘口令生成
	 * 第一遍测试 √
	 * 第二遍测试  未开始
	 */
	@Test
	public void tkl() {
		String url = "https://detail.tmall.com/item.htm?spm=a230r.1.14.6.61a23a46M68EIt&id=626912788096&cm_id=140105335569ed55e27b&abbucket=15";
		Result result = sjsService.tkl(url);
		System.out.println(result.toString());
	}

	@Test
	public void ssmd() {
		SsmdInput input = new SsmdInput();
		input.setBbid("578245716002");
		input.setGjc("吃鸡神奇");
		input.setWw("happy丶mvp");
		Result result = sjsService.ssmd(input.toString());
		System.out.println(result.toString());
	}

	@Test
	public void ssksp() {
		SskspInput input = new SskspInput();
		input.setBbid("566829880906");
		input.setGjc("高配吃鸡");
		Result result = sjsService.ssksp(input.toString());
		System.out.println(result.toString());
	}

	@Test
	public void ssdbksp() {
		SsdbkspBdbInput input = new SsdbkspBdbInput();
		input.setBbid("520923281738");
		input.setGjc("一体机,四核");
		input.setWw("happy丶mvp,fate,jackey储");
		Result result  = sjsService.ssdbksp(1, input.toString());
		System.out.println(result);
	}
	
	
	/**
	 * 测试：卡流量渠道 
	 * 第一遍测试：√
	 * 第二遍测试：√
	 * 第三遍测试：√
	 */
	@Test
	public void kllqd() {
		// 第一遍测试 测试type！=1 && type！=20 的情况
		KllqdInput input = new KllqdInput();
		input.setValue("626845322899");
		input.setType("4");
		Result result = sjsService.kllqd(input.toString());
		System.out.println(result);
		//第二遍测试 测试type==20 的情况
		KllqdInput input2 = new KllqdInput();
		input2.setValue("626845322899");
		input2.setType("20");
		input2.setJpid("629679418936");
		Result result2 = sjsService.kllqd(input2.toString());
		System.out.println(result2);
		//第三遍测试 测试type==1 的情况
		KllqdInput input3 = new KllqdInput();
		input3.setValue(
				"1.0复zんι文本🔐pHd2cOL20IL😺打кǎIt~bao或點̸击̸链节 https://m.tb.cn/h.4eXSl0T?sm=49f272 至流蓝琪【图文选自：MMMINNNI- WOW！！！这也太好闻了吧！！！！！！！ 我的眼前瞬间浮现出一座玫】");
		input3.setType("1");
		Result result3 = sjsService.kllqd(input3.toString());
		System.out.println(result3);
	}
}
