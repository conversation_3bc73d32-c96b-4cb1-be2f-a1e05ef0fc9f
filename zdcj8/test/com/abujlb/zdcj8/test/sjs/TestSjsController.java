package com.abujlb.zdcj8.test.sjs;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;

import javax.net.ssl.SSLContext;

import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.config.RequestConfig.Builder;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.util.EntityUtils;
import org.apache.log4j.Logger;
import org.junit.Test;

import com.abujlb.BaseTest;
import com.abujlb.Result;
import com.abujlb.util.AbujlbTrustStrategy;
import com.abujlb.util.Md5;
import com.abujlb.zdcj8.bean.JptsBbxx;
import com.abujlb.zdcj8.bean.SkuSale;
import com.abujlb.zdcj8.bean.sjs.SsdbkspBdbInput;
import com.abujlb.zdcj8.constant.SjsConst;
import com.abujlb.zdcj8.util.StrUtil;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import net.sf.json.JSONObject;

/**
 * 单元测试：测试服务器
 * 
 * <AUTHOR>
 * @date 2020-09-24 11:03:56
 */
@SuppressWarnings("unused")
public class TestSjsController extends BaseTest {

	private static Logger log = Logger.getLogger(TestSjsController.class);

	// private static final String server_url = "http://localhost/zdcj8/";
	private static final String server_url = "http://*************/"; // 2024-07-02调整：腾讯云服务器：************，调整IP：*************

	/**
	 * 竞品sku销量：最近30天
	 * 
	 */
	private static boolean getJpSkuSale(String bbid) {
		String url = server_url + "sjs_sku.action";
		SSLContext sslContext;
		HttpPost httpPost = null;
		HttpResponse httpResponse;
		try {
			Gson gson = new Gson();
			String data = bbid;
			String stamp = String.valueOf(System.currentTimeMillis());
			String password = Md5.password(data + stamp);

			sslContext = new SSLContextBuilder().loadTrustMaterial(null, new AbujlbTrustStrategy()).build();
			SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslContext);
			CloseableHttpClient httpClient = HttpClients.custom().setSSLSocketFactory(sslsf).build();

			httpPost = new HttpPost(url);
			ArrayList<BasicNameValuePair> pairs = new ArrayList<BasicNameValuePair>();
			pairs.add(new BasicNameValuePair("data", data));
			pairs.add(new BasicNameValuePair("stamp", stamp));
			pairs.add(new BasicNameValuePair("password", password));

			UrlEncodedFormEntity urlEncodedFormEntity = new UrlEncodedFormEntity(pairs, "utf-8");
			httpPost.setEntity(urlEncodedFormEntity);
			httpResponse = httpClient.execute(httpPost);
			HttpEntity entity = httpResponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");

			JSONObject jsonObj = JSONObject.fromObject(body);
			int code = jsonObj.getInt("code");
			if (code == 0 && jsonObj.has("list")) {
				Type type = new TypeToken<ArrayList<SkuSale>>() {
				}.getType();
				List<SkuSale> list = gson.fromJson(jsonObj.getString("list"), type);
				if (list != null) {
					System.out.println("总数：" + list.size());
					for (int i = 0; i < 100 && i < list.size(); i++) {
						System.out.println(list.get(i).toString());
					}
				}
			} else {
				System.out.println(body);
			}
			return true;
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		} finally {
			if (httpPost != null) {
				httpPost.releaseConnection();
			}
		}
		return false;
	}

	/**
	 * 超级竞品透视：最近30天
	 * 
	 */
	private static boolean getSuperJpts(String bbid) {
		String url = server_url + "sjs_jpts.action";
		SSLContext sslContext;
		HttpPost httpPost = null;
		HttpResponse httpResponse;
		try {
			Gson gson = new Gson();
			String data = bbid;
			String stamp = String.valueOf(System.currentTimeMillis());
			String password = Md5.password(data + stamp);

			sslContext = new SSLContextBuilder().loadTrustMaterial(null, new AbujlbTrustStrategy()).build();
			SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslContext);
			CloseableHttpClient httpClient = HttpClients.custom().setSSLSocketFactory(sslsf).build();

			httpPost = new HttpPost(url);
			ArrayList<BasicNameValuePair> pairs = new ArrayList<BasicNameValuePair>();
			pairs.add(new BasicNameValuePair("data", data));
			pairs.add(new BasicNameValuePair("stamp", stamp));
			pairs.add(new BasicNameValuePair("password", password));

			UrlEncodedFormEntity urlEncodedFormEntity = new UrlEncodedFormEntity(pairs, "utf-8");
			httpPost.setEntity(urlEncodedFormEntity);
			httpResponse = httpClient.execute(httpPost);
			HttpEntity entity = httpResponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");

			JSONObject jsonObj = JSONObject.fromObject(body);
			int code = jsonObj.getInt("code");
			if (code == 0 && jsonObj.has("list")) {
				Type type = new TypeToken<ArrayList<JptsBbxx>>() {
				}.getType();
				List<JptsBbxx> list = gson.fromJson(jsonObj.getString("list"), type);
				if (list != null) {
					System.out.println("总数：" + list.size());
					for (int i = 0; i < 100 && i < list.size(); i++) {
						System.out.println(list.get(i).toString());
					}
				}
			} else {
				System.out.println(body);
			}
			return true;
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		} finally {
			if (httpPost != null) {
				httpPost.releaseConnection();
			}
		}
		return false;
	}

	// main方法
	public static void main(String[] args) {
		// String bbid = "591405432536"; // 天猫宝贝
		// String bbid = "611747292705"; // 淘宝宝贝
		// String bbid = "551362556697"; // 天猫宝贝，丝袜
		// String bbid = "624762593503"; // 淘宝宝贝，打底裤
		// String bbid = "625412286238"; // 天猫宝贝，连衣裙

		// String bbid = "40878263535"; // 天猫宝贝，正装女
		// System.out.println(getJpSkuSale(bbid)); // 竞品sku销量：最近30天

		String bbid = "525857779545"; // 天猫宝贝，正装女
		System.out.println(getSuperJpts(bbid)); // 超级竞品透视：最近30天
	}

	@Test
	public void test() {
		Result result = new Result(2, "失败");
		String requestPostUrl = "http://localhost:8081/zdcj8/test.action";
		CloseableHttpClient httpClient = HttpClients.custom().build();
		HttpPost httpPost = new HttpPost(requestPostUrl);
		httpPost.setHeader("accept", "application/json, text/plain, */*");
		httpPost.setHeader("accept-encoding", "gzip, deflate, br");
		httpPost.setHeader("accept-language", "zh-CN,zh;q=0.9");
		httpPost.setHeader("content-type", "application/json;charset=UTF-8");
		httpPost.setHeader("origin", "https://www.shujushe.com");
		httpPost.setHeader("user-agent",
				"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.75 Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000)
				.setConnectionRequestTimeout(20000);
		httpPost.setConfig(requestConfig.build());
		try {
			String json = "{\"title\":\"" + "aaa" + "\",\"type\":\"" + 1 + "\",\"nid\":\"" + "2378" + "\",\"userId\":\""
					+ "33756" + "\",\"date\":" + "\"\"" + "\",\"nick\":\"" + "彭立文" + "\"}";
			httpPost.setEntity(new StringEntity(json, "UTF-8"));

			HttpResponse httpResponse = httpClient.execute(httpPost);
			HttpEntity entity = httpResponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			System.out.println(body);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
}
