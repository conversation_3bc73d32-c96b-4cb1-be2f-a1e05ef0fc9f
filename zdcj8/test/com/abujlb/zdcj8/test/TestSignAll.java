package com.abujlb.zdcj8.test;

import org.apache.log4j.Logger;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.abujlb.BaseTest;
import com.abujlb.jyrb.script.JsvmJSEngine;
import com.abujlb.util.Md5;
import com.abujlb.zdcj8.script.DpsxSignJSEngine;

/**
 * 单元测试：测试签名get-hash.js
 * 
 * <AUTHOR>
 * @date 2020-11-11 17:12:26
 */
public class TestSignAll extends BaseTest {

	private static Logger log = Logger.getLogger(TestSignAll.class);
	
	@Autowired
	private DpsxSignJSEngine dpsxSignJSEngine;
	
	/**
	 * test1测试：会计师sign破解
	 */
	@Test
	public void test1() {
		try {
			String _m_h5_tk = "26a2f5e0add0fc25eba3649f88f5e023_1645690937572";
			long stamp = 1645684782712L;
			String appKey = "32069629";
			String data = "{\"page\":1,\"pageSize\":20}";
	        // data = URLEncoder.encode(data, "utf-8");
	        String token = _m_h5_tk.split("_")[0];
			
			String text = token + "&" + stamp + "&" + appKey + "&" + data;
			// 加密
			String sign = JsvmJSEngine.getKjsSign(text);
			System.out.println("1bb82711b141dafd68e58030cd0f6411");
			System.out.println(sign); // 不匹配
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		}
	}
	
	/**
	 * test2测试：Md5
	 */
	@Test
	public void test2() {
		try {
			String _m_h5_tk = "55fe78621295706b96fd313a7a68904c_1645779097272";
			long stamp = 1645771265483L;
			String appKey = "32069629";
			String data = "{\"page\":1,\"pageSize\":20}";
			// data = URLEncoder.encode(data, "utf-8");
			String token = _m_h5_tk.split("_")[0];
			
			String text = token + "&" + stamp + "&" + appKey + "&" + data;
			// md5加密
			String sign = Md5.md5(text);
			System.out.println("8b6545531f6e4f8bec4372333ef21f77");
			System.out.println(sign); // 匹配
			
			stamp = 1645775628519L;
			data = "{\"recordId\":497690}";
			System.out.println();
			System.out.println("749f7efaaef048b0373f1222598aad2a");
			System.out.println(Md5.md5("a5434384c733c6db5f39aed4cba20f30" + "&" + stamp + "&" + appKey + "&" + data)); // 匹配
			
			appKey = "12574478";
			stamp = 1645777511631L;
			data = "{\"code\":1}";
			System.out.println();
			System.out.println("d2442c1bd161d018ce5d3f6c11eb7a0f");
			System.out.println(Md5.md5("undefined" + "&" + stamp + "&" + appKey + "&" + data)); // 匹配
			
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	/**
	 * test3测试：
	 */
	@Test
	public void test3() {
		try {
			String _m_h5_tk = "26a2f5e0add0fc25eba3649f88f5e023_1645690937572";
			long stamp = 1645684782712L;
			String appKey = "32069629";
			String data = "{\"page\":1,\"pageSize\":20}";
			// data = URLEncoder.encode(data, "utf-8");
			String token = _m_h5_tk.split("_")[0];
			
			// 加密
			String sign = dpsxSignJSEngine.getSign(stamp + "", appKey, data, token);
			System.out.println("1bb82711b141dafd68e58030cd0f6411");
			System.out.println(sign); // 不匹配
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
}
