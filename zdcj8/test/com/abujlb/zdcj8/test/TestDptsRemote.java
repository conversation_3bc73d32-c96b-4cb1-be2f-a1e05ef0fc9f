package com.abujlb.zdcj8.test;

import java.util.ArrayList;

import javax.net.ssl.SSLContext;

import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.util.EntityUtils;
import org.apache.log4j.Logger;
import org.junit.Test;

import com.abujlb.BaseTest;
import com.abujlb.util.AbujlbTrustStrategy;
import com.abujlb.util.Md5;
import com.abujlb.zdcj8.bean.ShoptsPara;
import com.google.gson.Gson;

public class TestDptsRemote extends BaseTest {
	private static Logger log = Logger.getLogger(TestDptsRemote.class);

	String server = "http://*************/"; // 2024-07-02调整：腾讯云服务器：************，调整IP：*************

	@Test
	public void test() {
		ShoptsPara sp = new ShoptsPara();
		sp.setUserid("702535360");
		sp.setCatelist(new String[] { "50020275" });

		String url = server + "shopts_ts.action";
		SSLContext sslContext;
		HttpPost httppost = null;
		try {
			Gson gson = new Gson();
			String data = gson.toJson(sp);
			String stamp = String.valueOf(System.currentTimeMillis());
			String password = Md5.password(data + stamp);

			sslContext = new SSLContextBuilder().loadTrustMaterial(null, new AbujlbTrustStrategy()).build();

			SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslContext);
			CloseableHttpClient httpClient = HttpClients.custom().setSSLSocketFactory(sslsf).build();

			httppost = new HttpPost(url);
			ArrayList<BasicNameValuePair> pairs = new ArrayList<BasicNameValuePair>();
			pairs.add(new BasicNameValuePair("data", data));
			pairs.add(new BasicNameValuePair("stamp", stamp));
			pairs.add(new BasicNameValuePair("password", password));

			HttpResponse httpresponse;

			UrlEncodedFormEntity urlEncodedFormEntity = new UrlEncodedFormEntity(pairs, "utf-8");
			httppost.setEntity(urlEncodedFormEntity);
			httpresponse = httpClient.execute(httppost);
			HttpEntity entity = httpresponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			System.out.println(body);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		} finally {
			if (httppost != null) {
				httppost.releaseConnection();
			}
		}

	}
}
