package com.abujlb.zdcj8.test;

import com.abujlb.zdcj8.util.FileToJson;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 * @date 2022/3/1 15:55
 */
public class TestYlmfJson {


    public static void main(String[] args) {
        String jsonBefore = FileToJson.toJson("C:\\Users\\<USER>\\Desktop\\耐吉奥引力魔方20220101日 采集.json");
        String jsonAfter = FileToJson.toJson("C:\\Users\\<USER>\\Desktop\\耐吉奥引力魔方20220101 重采.json");

        JSONArray jsonArrayBefore = JSONArray.parseArray(jsonBefore);
        JSONArray jsonArrayAfter = JSONArray.parseArray(jsonAfter);

        System.out.println(jsonArrayAfter.size()==jsonArrayBefore.size());
        for (int i = 0; i < jsonArrayBefore.size(); i++) {

            JSONObject jsonObjectBefore = jsonArrayBefore.getJSONObject(i);

            System.out.println(jsonObjectBefore.getDoubleValue("charge")   +","+jsonObjectBefore.getString("itemId"));
//            for (int j = 0; j < jsonArrayAfter.size(); j++) {
//                JSONObject jsonObjectAfter = jsonArrayAfter.getJSONObject(j);
//                if (jsonObjectBefore.getString("creativePackageId").equals(jsonObjectAfter.getString("creativePackageId"))
//                && jsonObjectBefore.getLongValue("campaignId") == jsonObjectAfter.getLongValue("campaignId")) {
//
//                    if (jsonObjectBefore.getDoubleValue("charge") != jsonObjectAfter.getDoubleValue("charge")) {
//                        System.out.print(jsonObjectBefore.toJSONString());
//                        System.out.print("----------");
//                        System.out.print(jsonObjectAfter.toJSONString());
//                        System.out.println();
//                    }
//
//                }
//            }


        }

        System.out.println("==================================");
        for (int i = 0; i < jsonArrayAfter.size(); i++) {

            JSONObject jsonObjectAfter = jsonArrayAfter.getJSONObject(i);

            System.out.println(jsonObjectAfter.getDoubleValue("charge"));
        }

    }
}
