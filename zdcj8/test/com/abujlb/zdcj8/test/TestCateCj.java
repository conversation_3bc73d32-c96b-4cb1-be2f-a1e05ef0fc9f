package com.abujlb.zdcj8.test;

import com.abujlb.zdcj8.service.HyrqbdService;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.abujlb.BaseTest;
import com.abujlb.ckstore.AbujlbCookieStore;
import com.abujlb.ckstore.Cjzh;
import com.abujlb.jyrb.bean.JysjMsg;
import com.abujlb.jyrb.sjcj.cate.CateMain;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public class TestCateCj extends BaseTest {
	@Autowired
	private AbujlbCookieStore cookieStore;
	@Autowired
	private CateMain cateMain;
	@Autowired
	private HyrqbdService hyrqbdService;
	@Test
	public void test() {
//		String ck = "ck_447e4e4d296a47b6a5036719d7cfe56c";
//		Cjzh cjzh = cookieStore.getCjzhForKey(ck);
//		JysjMsg msg = new JysjMsg();
//		msg.setCookieKey(ck);
//		msg.setType(JysjMsg.TYPE_MANUAL);
//		cateMain.cj(cjzh, null, msg);
//		Cjzh cjzh = cookieStore.getCjzhForCateZtc("16");
//		String keysForCateZtc = cookieStore.getKeysForCateZtc("16");
//		List<String> cookieKeyList = Arrays.stream(keysForCateZtc.split(",")).distinct().collect(Collectors.toList());
//		cookieKeyList.forEach(x->{
//			Cjzh cjzh = cookieStore.getCjzhForKey(x);
//		});
		hyrqbdService.process();
	}
}
