package com.abujlb.zdcj8.test.ts;

import java.util.List;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.abujlb.BaseTest;
import com.abujlb.Result;
import com.abujlb.ckstore.AbujlbCookieStore;
import com.abujlb.ckstore.Cjzh;
import com.abujlb.zdcj8.bean.RscHy;
import com.abujlb.zdcj8.service.GetRscService;
import com.google.gson.Gson;

public class TestRsc extends BaseTest {

	@Autowired
	private AbujlbCookieStore abujlbCookieStore;
	@Autowired
	private GetRscService rscService;

	@Test
	public void test() {
		String topid = "16", cateid = "1623";
		Cjzh cjzh = abujlbCookieStore.nextCjzh(topid);
		String dateRange = "2020-09-20%7C2020-09-26";
		Result result = new Result();
		List<RscHy> ssc = rscService.cjSsc(cjzh, cateid, dateRange, result);
		Gson gson = new Gson();
		System.out.println(gson.toJson(ssc));
	}
}
