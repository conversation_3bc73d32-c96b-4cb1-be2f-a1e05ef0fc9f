package com.abujlb.zdcj8.test;

import java.util.Date;

import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.CookieStore;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.config.RequestConfig.Builder;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.BasicCookieStore;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.cookie.BasicClientCookie;
import org.apache.http.util.EntityUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.abujlb.BaseTest;
import com.abujlb.ckstore.AbujlbCookieStore;
import com.abujlb.ckstore.Cjzh;
import com.abujlb.util.StringUtil;
import com.abujlb.util.SycmDataTojson;
import com.abujlb.util.SycmTransitid;
import com.abujlb.zdcj8.http.SycmHttpClient;
import com.abujlb.zdcj8.script.SycmIsgJSEngine;
import com.abujlb.zdcj8.util.StrUtil;
import com.google.gson.Gson;

import net.sf.json.JSONObject;

public class TestShopTs extends BaseTest {
	@Autowired
	private AbujlbCookieStore cookieStore;
	@Autowired
	private SycmIsgJSEngine sycmIsgJSEngine;
	@Autowired
	private SycmHttpClient sycmHttpClient;

	protected Cjzh getCjzh() {
		String cookieKey = "ck_c78c59750aee4253b94b0a0c2cd0b743";
		return cookieStore.getCjzhForKey(cookieKey);
	}

	@Test
	public void getDataSycm2() {
		String userid = "3524572155";
		String cateid = "50016422";
		String date = "2021-02-03";
		Cjzh cjzh = this.getCjzh();

		BasicClientCookie cookie = new BasicClientCookie("isg", getIsg());
		cookie.setDomain("sycm.taobao.com");
		cookie.setPath("/");
		Date d = new Date(new Date().getTime() + 1800000L);
		cookie.setExpiryDate(d);
		cjzh.getCookieStore().addCookie(cookie);

		Gson gson = new Gson();
		System.out.println(gson.toJson(cjzh.getCookieStore()));

		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cjzh.getCookieStore()).build();
		long t = System.currentTimeMillis();

		HttpGet httpget = new HttpGet("https://sycm.taobao.com/mc/ci/shop/trend.json?dateType=day&dateRange=" + date
				+ "%7C" + date + "&cateId=" + cateid + "&userId=" + userid
				+ "&device=0&sellerType=-1&indexCode=uvIndex%2CpayRateIndex%2CtradeIndex%2CpayByrCntIndex&_=" + t
				+ "&token=" + cjzh.getMicrodata("legalityToken"));

		httpget.setHeader("accept", "*/*");
		httpget.setHeader("accept-encoding", "gzip, deflate, sdch, br");
		httpget.setHeader("accept-language", "zh-CN,zh;q=0.8");
		httpget.setHeader("referer", "https://sycm.taobao.com/mc/ci/item/analysis");
		httpget.setHeader("transit-id", SycmTransitid.getTransitid());
		httpget.setHeader("user-agent",
				"Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/55.0.2883.75 Safari/537.36");
		httpget.setHeader("onetrace-card-id",
				"sycm-mc-mq-market-rank.sycm-mc-mq-shop-sale.sycm-mc-mq-rival-item-2455164953");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000)
				.setConnectionRequestTimeout(20000);
		httpget.setConfig(requestConfig.build());

		HttpResponse httpresponse;
		try {
			httpresponse = httpClient.execute(httpget);
			HttpEntity entity = httpresponse.getEntity();
			String body = EntityUtils.toString(entity);
			System.out.println(body);
			JSONObject jsonObj = JSONObject.fromObject(body);
			if (jsonObj.has("code") && jsonObj.getInt("code") == 0) {
				String data = SycmDataTojson.toJson(jsonObj.getString("data"));
				System.out.println(data);
			}
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			httpget.releaseConnection();
		}
	}

	public static String getIsg() {
		
		String prefix = "BH9_H8gsI62D4hh3_";
		String s2 = StringUtil.getRandomString(64 - prefix.length());
		
		return "BF9fYUlyQw3SD3jXH2rUXraw7rPpxLNmdviB2_GsH45FgH4C-ZaftjcWQhD-GIve";
		//return prefix + s2;
	}
	
	/**
	 * 单元测试：店铺透视接口
	 * 
	 */
	@Test
	public void testDpts() {
		// String zhuzh = "爱视杰旗舰店";
//		String zhuzh = "zupastar旗舰店";
		String zhuzh = "欧雅丽家居"; // String zhuzh = "一欣家具旗舰店";
		Gson gson = new Gson();
		try {
			String ck = cookieStore.getCookieKeyForZhuzh(zhuzh);
			Cjzh cjzh = null;
			if (!StrUtil.isNull(ck)) {
				cjzh = cookieStore.getCjzhForKey(ck);
			} else {
				System.out.println("ck是空！");
				return;
			}
			if (cjzh != null) {
				
			} else {
				System.out.println("cjzh是空！");
				return;
			}
			
			sycmHttpClient.getCna(cjzh.getCookieStore()); // 获取cna
			
//			String userid = "2358518495"; // 目标店铺userid，必须拥有相同类目的店铺
//			String cateid = "28"; // 顶级类目id
			String userid = "2096542875"; // 目标店铺userid，必须拥有相同类目的店铺
			String cateid = "50008164"; // 顶级类目id
			String date = "2021-02-06";

			String domCookie = sycmIsgJSEngine.getDocumentCookie(cjzh.getCookieStore());
			System.out.println(gson.toJson(cjzh.getCookieStore()));
			System.out.println(domCookie);
			// domCookie = domCookie + "; thw=cn; everywhere_tool_welcome=true; xlly_s=1";
			String isg = sycmIsgJSEngine.getIsg(domCookie);
			System.out.println(isg);
			BasicClientCookie cookie = new BasicClientCookie("isg", isg);
			cookie.setDomain(".taobao.com");
			cookie.setPath("/");
			Date d = new Date(new Date().getTime() + 1800000L);
			cookie.setExpiryDate(d);
			cookie.setSecure(false);
			cjzh.getCookieStore().addCookie(cookie);

			// System.out.println(gson.toJson(cjzh.getCookieStore()));
			CookieStore newCookieStore = new BasicCookieStore();
			CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(newCookieStore).build();
//			CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cjzh.getCookieStore()).build();
			long t = System.currentTimeMillis();

			// 页面地址：https://sycm.taobao.com/mc/ci/shop/recognition?activeKey=drain&cateFlag=1&cateId=122928002&dateRange=2021-02-06%7C2021-02-06&dateType=day&device=0&parentCateId=0&sellerType=-1
			// 接口地址：https://sycm.taobao.com/mc/ci/shop/trend.json?dateType=day&dateRange=2021-02-06%7C2021-02-06&cateId=122928002&userId=2200700353140&device=0&sellerType=-1&indexCode=uvIndex%2CpayRateIndex%2CtradeIndex%2CpayByrCntIndex&_=1612666591435&token=7cb96dda6
			HttpGet httpget = new HttpGet("https://sycm.taobao.com/mc/ci/shop/trend.json?dateType=day&dateRange=" + date + "%7C" + date + "&cateId=" + cateid + "&userId=" + userid
					+ "&device=0&sellerType=-1&indexCode=uvIndex%2CpayRateIndex%2CtradeIndex%2CpayByrCntIndex&_=" + t + "&token=" + cjzh.getMicrodata("legalityToken"));
			httpget.setHeader("accept", "*/*");
			httpget.setHeader("accept-encoding", "gzip, deflate, br");
			httpget.setHeader("accept-language", "zh-CN,zh;q=0.9");
			httpget.setHeader("onetrace-card-id", "sycm-mc-ci-shop-recognition.sycm-mc-ci-shop-recognition-trend-analysis");
			httpget.setHeader("referer", "https://sycm.taobao.com/mc/ci/shop/recognition?activeKey=drain&cateFlag=1&cateId=" + cateid + "&dateRange=" + date + "%7C" + date + "&dateType=day&device=0&parentCateId=0&sellerType=-1");
			//httpget.setHeader("Cookie", domCookie + "; isg=" + isg);
			httpget.setHeader("sycm-referer", "/mc/ci/shop/recognition");
			httpget.setHeader("transit-id", SycmTransitid.getTransitid());
			httpget.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Safari/537.36");
			
			Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000).setConnectionRequestTimeout(20000);
			httpget.setConfig(requestConfig.build());
			HttpResponse httpresponse;
			try {
				httpresponse = httpClient.execute(httpget);
				HttpEntity entity = httpresponse.getEntity();
				String body = EntityUtils.toString(entity);
				System.out.println(body);
				JSONObject jsonObj = JSONObject.fromObject(body);
				if (jsonObj.has("code") && jsonObj.getInt("code") == 0) {
					String data = SycmDataTojson.toJson(jsonObj.getString("data"));
					System.out.println(data);
					//cjzh.backCookie(newCookieStore); // 回写cookie
				}
			} catch (Exception e) {
				e.printStackTrace();
			} finally {
				httpget.releaseConnection();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public static void main(String[] args) {
		System.out.println("BH9_H8gsI62D4hh3_8o0vpZQDlMJZNMGVljhexFN8A5XIJyiGTJeV292YvDef6t4".length());
		System.out.println(getIsg());
	}
}
