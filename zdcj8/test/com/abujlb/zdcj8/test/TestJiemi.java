package com.abujlb.zdcj8.test;

import org.junit.Test;

import com.abujlb.BaseTest;
import com.abujlb.util.SycmDataTojson;

public class TestJiemi extends BaseTest {

	@Test
	public void test() {
		String s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
		String json = SycmDataTojson.toJson(s);
		System.out.println(json);
	}
}
