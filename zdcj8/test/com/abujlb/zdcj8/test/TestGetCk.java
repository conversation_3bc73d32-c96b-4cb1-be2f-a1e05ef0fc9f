package com.abujlb.zdcj8.test;

import org.apache.http.client.CookieStore;
import org.junit.Test;

import com.abujlb.BaseTest;
import com.abujlb.util.CookieString;
import com.abujlb.util.ResourceContent;
import com.abujlb.util.ResourceLoader;
import com.google.gson.Gson;

public class TestGetCk extends BaseTest {

	@Test
	public void test() {
		String s = ResourceLoader.loadSource("/com/abujlb/zdcj8/test/ck.txt");
		// String s =
		// ResourceContent.getContent("/zdcj8/test/com/abujlb/zdcj8/test/ck.txt",
		// "utf-8");
		CookieStore cs = CookieString.strToCookies(s);
		Gson gson = new Gson();
		System.out.println(gson.toJson(cs));
	}
}
