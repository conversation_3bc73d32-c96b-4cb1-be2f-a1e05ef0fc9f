package com.abujlb.zdcj8.test.onoff;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.abujlb.BaseTest;
import com.abujlb.ckstore.OnOff;

public class TestIsOnoff extends BaseTest {
	@Autowired
	private OnOff onOff;

	@Test
	public void test() {
		while (true) {
			long l1 = System.currentTimeMillis();
			boolean dpts = onOff.isOn("dpts");
			boolean jzpc = onOff.isOn("jzpc");
			long l2 = System.currentTimeMillis();
			System.out.println("dpts:" + dpts + ",jzpc:" + jzpc + ",time:" + (l2 - l1));
			try {
				Thread.sleep(3000L);
			} catch (InterruptedException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}

	}
}
