package com.abujlb.zdcj8.test;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.abujlb.BaseTest;
import com.abujlb.jyrb.MessageTypeConst;
import com.abujlb.jyrb.bean.JysjMsg;
import com.abujlb.mq.TxyunMq;

/**
 * send发送店铺采集的消息，包含BI店铺
 * 
 * <AUTHOR>
 * @date 2022-04-16 12:43:00
 */
public class TestSendMq2 extends BaseTest {
	
	@Autowired
	private TxyunMq mq;

	@Test
	public void test() {
		JysjMsg msg = new JysjMsg();
		msg.setCookieKey("ck_7d988ef535cb4741b02685397cd95852");
		String s = mq.sendMessage2("jysjQueue", MessageTypeConst.JYSJCJ_ZDCJ8, msg);
		System.out.println(s);
	}

}
