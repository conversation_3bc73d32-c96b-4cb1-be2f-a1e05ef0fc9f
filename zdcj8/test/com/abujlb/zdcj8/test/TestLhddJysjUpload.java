package com.abujlb.zdcj8.test;

import java.lang.reflect.Type;
import java.util.List;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.abujlb.BaseTest;
import com.abujlb.jyrb.bean.Dp;
import com.abujlb.jyrb.bean.Jysj;
import com.abujlb.jyrb.service.DataUploader;
import com.abujlb.util.ResourceLoader;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;

public class TestLhddJysjUpload extends BaseTest {
	@Autowired
	private DataUploader uploader;

	@Test
	public void test() {
		Gson gson = new GsonBuilder().setDateFormat("yyyy-MM-dd").create();
		String json = ResourceLoader.loadSource("/com/abujlb/zdcj8/test/jysj.json", "utf-8");
		Type type = new TypeToken<List<Jysj>>() {
		}.getType();
		Dp dp = new Dp();
		dp.setJlbdpid(2237);
		dp.setLx(1);
		dp.setZt(0);
		List<Jysj> jysjList = gson.fromJson(json, type);
		for (int i = 0; i < jysjList.size(); i++) {
			Jysj jysj = jysjList.get(i);
			jysj.setDp(dp);
			uploader.uploadjysj(jysj);
		}
	}
}
