package com.abujlb.zdcj8.test;

import com.abujlb.jyrb.bean.Dp;
import com.abujlb.jyrb.bean.JysjMsg;
import com.abujlb.jyrb.service.DataUploader;
import com.abujlb.jyrb.sjcj.dsfcj.Dpfw;
import com.abujlb.jyrb.sjcj.dsfcj.DsfMain;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.abujlb.BaseTest;
import com.abujlb.ckstore.AbujlbCookieStore;
import com.abujlb.ckstore.Cjzh;
import com.google.gson.Gson;

import java.util.ArrayList;
import java.util.List;

/**
 * 测试账号
 * 
 * <AUTHOR>
 * @date 2020-10-14 17:15:25
 */
public class TestCjzh extends BaseTest {

	@Autowired
	private AbujlbCookieStore abujlbCookieStore;
	private Dp dp;
	@Autowired
	private DataUploader uploader;
	@Autowired
	private DsfMain dsfMain;

	private JysjMsg jysjMsg;
	@Test
	public void test() {
		String cateid = "50016422";
		Cjzh cjzh = abujlbCookieStore.nextCjzh(cateid);
		dp = uploader.hqdp(cjzh);
		dsfMain.kscj(cjzh,dp,jysjMsg);
	}

	@Test
	public void test2() {
		Dpfw dpfw = new Dpfw();
		dpfw.setUserId("111");
		dpfw.setArticleCode("ceshi");
		List<Dpfw> array = new ArrayList<>();
		array.add(dpfw);
		System.out.println(uploader.updateDpfw(array));
	}
}
