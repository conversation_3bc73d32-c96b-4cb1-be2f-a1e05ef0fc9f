package com.abujlb.zdcj8.test.ali;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;

import javax.net.ssl.SSLContext;

import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.util.EntityUtils;
import org.apache.log4j.Logger;

import com.abujlb.BaseTest;
import com.abujlb.util.AbujlbTrustStrategy;
import com.abujlb.util.Md5;
import com.abujlb.zdcj8.bean.DpsxBb;
import com.abujlb.zdcj8.bean.Tkdd;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import net.sf.json.JSONObject;

/**
 * 单元测试：测试服务
 * 
 * <AUTHOR>
 * @date 2021-03-19 15:18:53
 */
@SuppressWarnings("unused")
public class TestServer extends BaseTest {

	private static Logger log = Logger.getLogger(TestServer.class);
	
	private static final String server_url = "http://localhost/zdcj8/";
	// private static String server_url = "http://*************"; // 2024-07-02调整：腾讯云服务器：************，调整IP：*************
	
	/**
	 * 店铺上线查询接口
	 * 
	 */
	private static boolean dpsx(String userId, long lastTime) {
		String url = server_url + "ali_dpsx.action";
		SSLContext sslContext;
		HttpPost httpPost = null;
		HttpResponse httpResponse;
		try {
			Gson gson = new Gson();
			String data = "{\"userId\":" + userId + ",\"lastTime\":" + lastTime + "}";
			String stamp = String.valueOf(System.currentTimeMillis());
			String password = Md5.password(data + stamp);

			sslContext = new SSLContextBuilder().loadTrustMaterial(null, new AbujlbTrustStrategy()).build();
			SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslContext);
			CloseableHttpClient httpClient = HttpClients.custom().setSSLSocketFactory(sslsf).build();

			httpPost = new HttpPost(url);
			ArrayList<BasicNameValuePair> pairs = new ArrayList<BasicNameValuePair>();
			pairs.add(new BasicNameValuePair("data", data));
			pairs.add(new BasicNameValuePair("stamp", stamp));
			pairs.add(new BasicNameValuePair("password", password));

			UrlEncodedFormEntity urlEncodedFormEntity = new UrlEncodedFormEntity(pairs, "utf-8");
			httpPost.setEntity(urlEncodedFormEntity);
			httpResponse = httpClient.execute(httpPost);
			HttpEntity entity = httpResponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			System.out.println(body);
			JSONObject jsonObj = JSONObject.fromObject(body);
			int code = jsonObj.getInt("code");
			if (code == 0 && jsonObj.has("list")) {
				long _lastTime = jsonObj.getLong("lastTime");
				boolean _isLast = jsonObj.getBoolean("isLast");
				System.out.println("lastTime = " + _lastTime);
				System.out.println("isLast = " + _isLast);
				
				Type type = new TypeToken<ArrayList<DpsxBb>>() {
				}.getType();
				List<DpsxBb> list = gson.fromJson(jsonObj.getString("list"), type);
				if (list != null) {
					System.out.println("总数：" + list.size());
					for (int i = 0; i < list.size() ; i++) {
						System.out.println(list.get(i).toString());
					}
				}
			} else {
				System.out.println(body);
			}
			return true;
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		} finally {
			if (httpPost != null) {
				httpPost.releaseConnection();
			}
		}
		return false;
	}
	
	/**
	 * 淘客订单查询接口
	 * 
	 */
	private static boolean tkdd(String orderId) {
		String url = server_url + "ali_tkdd.action";
		SSLContext sslContext;
		HttpPost httpPost = null;
		HttpResponse httpResponse;
		try {
			Gson gson = new Gson();
			String stamp = String.valueOf(System.currentTimeMillis());
			String password = Md5.password(orderId + stamp);

			sslContext = new SSLContextBuilder().loadTrustMaterial(null, new AbujlbTrustStrategy()).build();
			SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslContext);
			CloseableHttpClient httpClient = HttpClients.custom().setSSLSocketFactory(sslsf).build();

			httpPost = new HttpPost(url);
			ArrayList<BasicNameValuePair> pairs = new ArrayList<BasicNameValuePair>();
			pairs.add(new BasicNameValuePair("data", orderId));
			pairs.add(new BasicNameValuePair("stamp", stamp));
			pairs.add(new BasicNameValuePair("password", password));

			UrlEncodedFormEntity urlEncodedFormEntity = new UrlEncodedFormEntity(pairs, "utf-8");
			httpPost.setEntity(urlEncodedFormEntity);
			httpResponse = httpClient.execute(httpPost);
			HttpEntity entity = httpResponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			System.out.println(body);
			JSONObject jsonObj = JSONObject.fromObject(body);
			int code = jsonObj.getInt("code");
			if (code == 0) {
				System.out.println(body);
			} else {
				System.out.println();
			}
			return true;
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		} finally {
			if (httpPost != null) {
				httpPost.releaseConnection();
			}
		}
		return false;
	}
	
	// main方法
	public static void main(String[] args) {
		// String userId = "2200664650269"; // 觅橘旗舰店，天猫店铺首页：https://mijufs.tmall.com/
		// String userId = "2752004044"; // lp运动旗舰店，天猫店铺首页：https://lpydhw.tmall.com/
		// String userId = "800302085"; // 尔沫旗舰店，天猫店铺首页：https://ermo.m.tmall.com/
		// String userId = "1035301420"; // 米子旗旗舰店，天猫店铺首页：https://miziqi.tmall.com/
		// String userId = "772352677"; // 风驰运动专营店，天猫店铺首页：https://fengchiyundong.tmall.com/shop/view_shop.htm
		String userId = "1579139371"; // 雅羊人旗舰店，天猫店铺首页：https://yayangren.tmall.com/shop/view_shop.htm
		long lastTime = 0L;
		// System.out.println(dpsx(userId, lastTime));
		
		// System.out.println(tkdd("1639106318931458045")); // 非淘客订单(非本人)
		// System.out.println(tkdd("1628973468617458045")); // 淘客订单(非本人)
		// System.out.println(tkdd("2370117924301486466")); // 
		System.out.println(tkdd("2322808023706922327")); // 
		// System.out.println(tkdd("1637810748051458045"));
	}
	
}
