package com.abujlb.zdcj8.test.pl;

import java.io.FileOutputStream;
import java.io.InputStream;

import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.config.RequestConfig.Builder;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.apache.log4j.Logger;
import org.junit.Test;

import com.abujlb.BaseTest;
import com.abujlb.util.ResourceContent;

public class TestPlsjcj extends BaseTest {
	private static Logger log = Logger.getLogger(TestPlsjcj.class);

	@Test
	public void test() {
		String ck = ResourceContent.getContent("/com/abujlb/zdcj8/test/pl/ck2.txt", "utf-8");
		testCore(ck);
		//download(ck);
	}

	public void testCore(String ck) {
		CloseableHttpClient httpClient = HttpClients.custom().build();
		HttpGet httpget = new HttpGet(
				"https://sycm.taobao.com/cc/cockpit/marcro/item/top.json?dateRange=2020-07-04%7C2020-07-04&dateType=day&pageSize=10&page=1&order=desc&orderBy=payAmt&keyword=&follow=false&cateId=50012082&cateLevel=1&guideCateId=&device=0&indexCode=itmUv%2CitemCartCnt%2CpayItmCnt%2CpayAmt%2CpayRate&_=1593939384127&token=3b80fe635");
		httpget.setHeader("accept", "*/*");
		httpget.setHeader("accept-encoding", "gzip, deflate, sdch, br");
		httpget.setHeader("accept-language", "zh-CN,zh;q=0.8");
		httpget.setHeader("referer",
				"https://sycm.taobao.com/cc/macroscopic_monitor");
		httpget.setHeader("user-agent",
				"Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/55.0.2883.75 Safari/537.36");
		httpget.setHeader("cookie", ck);
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000)
				.setConnectionRequestTimeout(20000);
		httpget.setConfig(requestConfig.build());

		HttpResponse httpresponse;
		try {
			httpresponse = httpClient.execute(httpget);
			HttpEntity entity = httpresponse.getEntity();
			String body = EntityUtils.toString(entity);
			System.out.println(body);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		} finally {
			httpget.releaseConnection();
		}
	}

	public void download(String ck) {
		CloseableHttpClient httpClient = HttpClients.custom().build();
		HttpGet httpget = new HttpGet(
				"https://sycm.taobao.com/cc/cockpit/marcro/item/excel/top.json?spm=a21ag.12100459.0.0.65b650a5daIwPc&dateRange=2020-07-04%7C2020-07-04&dateType=day&pageSize=10&page=1&order=desc&orderBy=payAmt&dtUpdateTime=false&keyword=&follow=false&cateId=&cateLevel=&guideCateId=&device=0&indexCode=itmUv%2CitemCartCnt%2CpayItmCnt%2CpayAmt%2CpayRate");
		httpget.setHeader("accept", "*/*");
		httpget.setHeader("accept-encoding", "gzip, deflate, sdch, br");
		httpget.setHeader("accept-language", "zh-CN,zh;q=0.8");
		httpget.setHeader("referer", "https://sycm.taobao.com/cc/macroscopic_monitor");
		httpget.setHeader("user-agent",
				"Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/55.0.2883.75 Safari/537.36");
		httpget.setHeader("cookie", ck);
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000)
				.setConnectionRequestTimeout(20000);
		httpget.setConfig(requestConfig.build());

		HttpResponse httpresponse;
		try {
			httpresponse = httpClient.execute(httpget);
			HttpEntity entity = httpresponse.getEntity();
			InputStream is = entity.getContent();
			FileOutputStream fos = new FileOutputStream("e:\\a.xls");
			byte[] b = new byte[1024];
			int length = 0;
			while ((length = is.read(b)) > 0) {
				fos.write(b, 0, length);
			}
			fos.flush();
			fos.close();
			is.close();
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		} finally {
			httpget.releaseConnection();
		}
	}
}
