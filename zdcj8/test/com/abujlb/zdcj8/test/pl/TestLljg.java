package com.abujlb.zdcj8.test.pl;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.abujlb.BaseTest;
import com.abujlb.Result;
import com.abujlb.zdcj8.bean.Dpts3Para;
import com.abujlb.zdcj8.service.Dpts3Service;

public class TestLljg extends BaseTest {
	@Autowired
	private Dpts3Service service;

	@Test
	public void test() {
		Dpts3Para para = new Dpts3Para();
		para.setBbid("612592812195");
		para.setDevice("2");
		para.setDateType(Dpts3Para.RECENT30);
		//para.setDate("2020-11-07");
		Result result = service.lljg(para);
		System.out.println(result.toString());
	}
	
	@Test
	public void testSsgjc() {
		Dpts3Para para = new Dpts3Para();
		para.setBbid("602332022210");
		para.setDevice("2");
		para.setDateType(Dpts3Para.RECENT30);
		//para.setDate("2020-11-07");
		Result result = service.ssgjc(para);
		System.out.println(result.toString());
	}
}
