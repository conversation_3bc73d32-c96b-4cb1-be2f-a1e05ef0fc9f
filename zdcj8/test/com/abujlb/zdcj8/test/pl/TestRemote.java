package com.abujlb.zdcj8.test.pl;

import java.util.ArrayList;

import javax.net.ssl.SSLContext;

import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.util.EntityUtils;
import org.apache.log4j.Logger;
import org.junit.Test;

import com.abujlb.BaseTest;
import com.abujlb.util.AbujlbTrustStrategy;
import com.abujlb.util.Md5;

public class TestRemote extends BaseTest {
	private static Logger log = Logger.getLogger(TestRemote.class);

	String server = "http://*************/"; // 2024-07-02调整：腾讯云服务器：************，调整IP：*************

	@Test
	public void testLljg() {
		String bbid = "622606361571";
		String device = "2";
		String dateType = "day";
		String date = "2020-07-29";
		String password = Md5.password(bbid + date + dateType + device);

		String url = server + "dpts3_lljg";
		SSLContext sslContext;
		HttpPost httppost = null;
		try {
			sslContext = new SSLContextBuilder().loadTrustMaterial(null, new AbujlbTrustStrategy()).build();

			SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslContext);
			CloseableHttpClient httpClient = HttpClients.custom().setSSLSocketFactory(sslsf).build();

			httppost = new HttpPost(url);
			ArrayList<BasicNameValuePair> pairs = new ArrayList<BasicNameValuePair>();
			pairs.add(new BasicNameValuePair("bbid", bbid));
			pairs.add(new BasicNameValuePair("device", device));
			pairs.add(new BasicNameValuePair("date", date));
			pairs.add(new BasicNameValuePair("dateType", dateType));
			pairs.add(new BasicNameValuePair("password", password));

			HttpResponse httpresponse;

			UrlEncodedFormEntity urlEncodedFormEntity = new UrlEncodedFormEntity(pairs, "utf-8");
			httppost.setEntity(urlEncodedFormEntity);
			httpresponse = httpClient.execute(httppost);
			HttpEntity entity = httpresponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			System.out.println(body);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		} finally {
			if (httppost != null) {
				httppost.releaseConnection();
			}
		}
	}

	@Test
	public void testZtcgjc() {
		String bbid = "621273981294";
		String device = "";
		String dateType = "recent30";
		String password = Md5.password(bbid + dateType + device);

		String url = server + "dpts3_ztcgjc";
		SSLContext sslContext;
		HttpPost httppost = null;
		try {
			sslContext = new SSLContextBuilder().loadTrustMaterial(null, new AbujlbTrustStrategy()).build();

			SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslContext);
			CloseableHttpClient httpClient = HttpClients.custom().setSSLSocketFactory(sslsf).build();

			httppost = new HttpPost(url);
			ArrayList<BasicNameValuePair> pairs = new ArrayList<BasicNameValuePair>();
			pairs.add(new BasicNameValuePair("bbid", bbid));
			pairs.add(new BasicNameValuePair("device", device));
			pairs.add(new BasicNameValuePair("dateType", dateType));
			pairs.add(new BasicNameValuePair("password", password));

			HttpResponse httpresponse;

			UrlEncodedFormEntity urlEncodedFormEntity = new UrlEncodedFormEntity(pairs, "utf-8");
			httppost.setEntity(urlEncodedFormEntity);
			httpresponse = httpClient.execute(httppost);
			HttpEntity entity = httpresponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			System.out.println(body);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		} finally {
			if (httppost != null) {
				httppost.releaseConnection();
			}
		}
	}

}
