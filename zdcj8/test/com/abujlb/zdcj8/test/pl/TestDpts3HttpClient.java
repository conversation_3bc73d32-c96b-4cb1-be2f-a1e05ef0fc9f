package com.abujlb.zdcj8.test.pl;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.abujlb.BaseTest;
import com.abujlb.ckstore.AbujlbCookieStore;
import com.abujlb.ckstore.Cjzh;
import com.abujlb.zdcj8.bean.Dpts3Para;
import com.abujlb.zdcj8.http.Dpts3HttpClient;

public class TestDpts3HttpClient extends BaseTest {
	@Autowired
	private Dpts3HttpClient httpClient;
	@Autowired
	private AbujlbCookieStore cookieStore;

	@Test
	public void testZtcgjc() {
		//https://detail.tmall.com/item.htm?id=570133944073
		String key = "ck_98f2007fcaee489789204f2dca45e9f6";
		Cjzh cjzh = cookieStore.getCjzhForKey(key);
		Dpts3Para para = new Dpts3Para();
		para.setBbid("570133944073");
		para.setDateType("day");
		para.setDate("2020-07-29");
		//para.setUpdateNDay("2020-07-29");
		String result = httpClient.ztcgjc(para, cjzh);
		System.out.println(result);
	}
}
