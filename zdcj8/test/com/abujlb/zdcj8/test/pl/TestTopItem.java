package com.abujlb.zdcj8.test.pl;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.abujlb.BaseTest;
import com.abujlb.ckstore.AbujlbCookieStore;
import com.abujlb.ckstore.Cjzh;
import com.abujlb.ckstore.TopItem;
import com.abujlb.jyrb.bean.Dp;
import com.abujlb.jyrb.bean.JysjMsg;
import com.abujlb.jyrb.service.DataUploader;
import com.abujlb.jyrb.sjcj.top.TopItemMain;

public class TestTopItem extends BaseTest {
	@Autowired
	private TopItemMain topItemMain;
	@Autowired
	private TopItem topItem;
	@Autowired
	private AbujlbCookieStore cookieStore;
	@Autowired
	private DataUploader uploader;

	@Test
	public void test() {
		String key = "ck_b96304978b9f40f8b62fad2fae52c702";
		Cjzh cjzh = cookieStore.getCjzhForKey(key);
		JysjMsg msg = new JysjMsg();
		msg.setCookieKey(key);
		Dp dp = uploader.hqdp(cjzh);
		topItemMain.cj(cjzh, dp, msg);
		System.out.println(topItem.getTopItem(cjzh));
	}
}
