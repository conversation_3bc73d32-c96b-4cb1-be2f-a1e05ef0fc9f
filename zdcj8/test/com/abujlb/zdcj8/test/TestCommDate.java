package com.abujlb.zdcj8.test;

import org.apache.log4j.Logger;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.abujlb.BaseTest;
import com.abujlb.jzts2.http.CommDate;
import com.abujlb.jzts2.pojo.JztsPara;
import com.google.gson.Gson;

/**
 * 单元测试：commDate
 * 
 * <AUTHOR>
 * @date 2021-09-18 14:50:12
 */
public class TestCommDate extends BaseTest {
	
	private static Logger log = Logger.getLogger(TestCommDate.class);

	@Autowired
	private CommDate commDate;
	
	@Test
	public void test() {
		Gson gson = new Gson();
		try {
			// String json = "{\"key\":\"4ae45d012a3b4c65aed9130fafa3e05f\",\"startTime\":1631935100392,\"finished\":0,\"progress\":0,\"para\":{\"lx\":\"dp\",\"vip\":false,\"userid\":\"2842273071\",\"zhuzh\":\"品图图书专营店\",\"catelist\":[\"33\"],\"ltly\":false,\"cateid\":\"33\",\"topid\":\"33\",\"updateDate\":\"2021-09-16\"},\"yjwcsj\":30,\"msgList\":[{\"key\":\"adeaffe84bbb411e9cd7e97a680b0cd7\",\"vip\":false,\"lx\":\"dp\",\"userid\":\"2842273071\",\"zhuzh\":\"品图图书专营店\",\"cateid\":\"33\"}]}";
			// String json = "{\"lx\":\"dp\",\"vip\":false,\"userid\":\"2842273071\",\"zhuzh\":\"品图图书专营店\",\"catelist\":[\"33\"],\"ltly\":false,\"cateid\":\"33\",\"topid\":\"33\",\"updateDate\":\"2021-09-16\"}";
			
			// String json = "{\"lx\":\"dp\",\"vip\":false,\"userid\":\"2138614417\",\"zhuzh\":\"协创优致办公专营店\",\"catelist\":[\"50018004\"],\"ltly\":false,\"cateid\":\"50018004\",\"topid\":\"50018004\",\"updateDate\":\"2022-06-16\"}";
			// String json = "{\"lx\":\"dp\",\"vip\":false,\"userid\":\"2138614417\",\"zhuzh\":\"协创优致办公专营店\",\"catelist\":[\"50007218\"],\"ltly\":false,\"cateid\":\"50007218\",\"topid\":\"50007218\",\"updateDate\":\"2022-06-16\"}";
			String json = "{\"lx\":\"dp\",\"vip\":false,\"userid\":\"2138614417\",\"zhuzh\":\"协创优致办公专营店\",\"catelist\":[\"98\"],\"ltly\":false,\"cateid\":\"98\",\"topid\":\"98\",\"updateDate\":\"2022-06-16\"}";
			JztsPara jztsPara = gson.fromJson(json, JztsPara.class);
			System.out.println(commDate.getUpdate1Date(jztsPara));
			System.out.println(commDate.getUpdateDate2(jztsPara));
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
	}
}
