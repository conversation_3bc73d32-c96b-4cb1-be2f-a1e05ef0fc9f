package com.abujlb.zdcj8.test;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.abujlb.AbujlbBeanFactory;
import com.abujlb.BaseTest;
import com.abujlb.ckstore.AbujlbCookieStore;
import com.abujlb.ckstore.Cjzh;
import com.abujlb.jyrb.bean.Dp;
import com.abujlb.jyrb.bean.JysjMsg;
import com.abujlb.jyrb.service.DataUploader;
import com.abujlb.jyrb.sjcj.ylmf.YlmfCjMain;

/**
* <AUTHOR>
* @date 2022-3-2
*/
public class TestYlmfCjMain extends BaseTest {

	@Autowired
	private AbujlbBeanFactory abujlbBeanFactory ;
	@Autowired
	private AbujlbCookieStore abujlbCookieStore ;
	@Autowired
	private DataUploader dataUploader ;
	
	@Test
	public void test() {
		Cjzh cjzh = abujlbCookieStore.getCjzhForKey("ck_90f3917fb39f4140ab90bdfdc6ef2e8f") ;
		if(cjzh == null) {
			return ;
		}
		// 2.获取到店铺
        Dp  dp = dataUploader.hqdp(cjzh);
        if (dp == null) {
            return ;
        }

        JysjMsg jysjMsg = new JysjMsg() ;
		jysjMsg.setType(JysjMsg.TYPE_LOGIN);
		
		YlmfCjMain ylmfCjMain = abujlbBeanFactory.getBean(YlmfCjMain.class);
        ylmfCjMain.process(cjzh, dp, jysjMsg);
	}
}
