package com.abujlb.zdcj8.test.plw;

/**
 * <AUTHOR>
 * @date 2022/4/22 14:32
 */
public class Test01 {

    public static void main(String[] args) {

        Integer a = new Integer(123);
        Integer b = Integer.valueOf(123);
        System.out.println(a == b);


        Integer c = 123;
        Integer d = Integer.valueOf(123);
        System.out.println(c == d);


        int e = 128;
        byte f = (byte) e;
        System.out.println("f = " + f);
    }
}
