package com.abujlb.zdcj8.test.plw;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.abujlb.BaseTest;
import com.abujlb.ckstore.AbujlbCookieStore;
import com.abujlb.ckstore.Cjzh;
import com.abujlb.jyrb.bean.Dp;
import com.abujlb.jyrb.bean.JysjMsg;
import com.abujlb.jyrb.service.DataUploader;
import com.abujlb.jyrb.sjcj.ztc.Ztccj;
import com.abujlb.jyrb.sjcj.ztc.bean.ZtcReport;

/**
 * <AUTHOR>
 * @date 2022-3-7
 */
public class TestZtccj extends BaseTest {

	@Autowired
	private Ztccj ztccj;
	@Autowired
	private AbujlbCookieStore abujlbCookieStore;
	@Autowired
	private DataUploader uploader;

	@Test
	public void cj() throws InterruptedException {
		String cookieKey = "ck_b0edfa81a472472183224dc412cbb531";
		Cjzh cjzh = abujlbCookieStore.getCjzhForKey(cookieKey);
		if (cjzh == null) {
			return;
		}
		Dp dp = uploader.hqdp(cjzh);
		if (dp == null) {
			return;
		}
		JysjMsg jysjMsg = new JysjMsg();
		jysjMsg.setType(JysjMsg.TYPE_LOGIN);

		ZtcReport ztcreport = null;
		ztcreport = ztccj.kscj(cjzh, dp, jysjMsg);

		Thread.sleep(10*1000);
		
		// 直通车结束采集
		ztccj.jscj(cjzh, dp, jysjMsg, ztcreport);
	}

}
