package com.abujlb.zdcj8.test.plw;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.abujlb.BaseTest;
import com.abujlb.ckstore.AbujlbCookieStore;
import com.abujlb.ckstore.Cjzh;
import com.abujlb.jyrb.bean.Dp;
import com.abujlb.jyrb.bean.JysjMsg;
import com.abujlb.jyrb.service.DataUploader;
import com.abujlb.jyrb.sjcj.lmgc.LmgcCjMain;

/**
 * <AUTHOR>
 * @date 2022-3-8
 */
public class TestLmgcCjMain extends BaseTest {

	@Autowired
	private LmgcCjMain lmgcCjMain;
	@Autowired
	private AbujlbCookieStore abujlbCookieStore;
	@Autowired
	private DataUploader uploader;

	@Test
	public void cj() {
		String cookieKey = "ck_792fddfb2b074fedab40c8c86d4bfe62";
		Cjzh cjzh = abujlbCookieStore.getCjzhForKey(cookieKey);
		if (cjzh == null) {
			return;
		}
		Dp dp = uploader.hqdp(cjzh);
		if (dp == null) {
			return;
		}
		JysjMsg msg = new JysjMsg();
		msg.setType(JysjMsg.TYPE_LOGIN);
		lmgcCjMain.cj(cjzh, dp, msg);

	}

}
