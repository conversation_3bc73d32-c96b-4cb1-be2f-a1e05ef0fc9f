package com.abujlb.zdcj8.test.plw;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.abujlb.BaseTest;
import com.abujlb.ckstore.AbujlbCookieStore;
import com.abujlb.ckstore.Cjzh;
import com.abujlb.jyrb.bean.Dp;
import com.abujlb.jyrb.bean.JysjMsg;
import com.abujlb.jyrb.service.DataUploader;
import com.abujlb.jyrb.sjcj.fblm.FblmCjMain;

/**
 * <AUTHOR>
 * @date 2022-3-7
 */
public class TestFblmCjMain extends BaseTest {

	@Autowired
	private FblmCjMain fblmCjMain;
	@Autowired
	private AbujlbCookieStore abujlbCookieStore;
	@Autowired
	private DataUploader uploader;

	@Test
	public void cj() {
		String cookieKey = "ck_d53bad3e87d34948986f764b3453757d";
		Cjzh cjzh = abujlbCookieStore.getCjzhForKey(cookieKey);
		if (cjzh == null) {
			return;
		}
		Dp dp = uploader.hqdp(cjzh);
		if (dp == null) {
			return;
		}
		JysjMsg msg = new JysjMsg();
		msg.setType(JysjMsg.TYPE_LOGIN);
		fblmCjMain.cj(cjzh, dp, msg);
	}

}
