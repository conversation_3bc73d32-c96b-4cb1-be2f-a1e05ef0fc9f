package com.abujlb.zdcj8.test.plw;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.abujlb.BaseTest;
import com.abujlb.ckstore.AbujlbCookieStore;
import com.abujlb.ckstore.Cjzh;
import com.abujlb.jyrb.bean.Dp;
import com.abujlb.jyrb.bean.JysjMsg;
import com.abujlb.jyrb.service.DataUploader;
import com.abujlb.jyrb.sjcj.fblm.FblmCjMain;
import com.abujlb.jyrb.sjcj.spxg.SpxgCjMain;

/**
* <AUTHOR>
* @date 2022-3-8
*/
public class TestSpxgCjMain extends BaseTest {

	@Autowired
	private FblmCjMain fblmCjMain;
	@Autowired
	private SpxgCjMain spxgCjMain ;
	@Autowired
	private AbujlbCookieStore abujlbCookieStore;
	@Autowired
	private DataUploader uploader;
	@Test
	public void cj() {
		String cookieKey = "ck_4c847f7ea456405ea53d6f7e3780ff24";
		Cjzh cjzh = abujlbCookieStore.getCjzhForKey(cookieKey);
		if (cjzh == null) {
			return;
		}
		Dp dp = uploader.hqdp(cjzh);
		if (dp == null) {
			return;
		}
		JysjMsg msg = new JysjMsg();
		msg.setType(JysjMsg.TYPE_LOGIN);
		
		fblmCjMain.cj(cjzh, dp, msg);
		
		spxgCjMain.cj(cjzh, dp, msg);
	}
}
