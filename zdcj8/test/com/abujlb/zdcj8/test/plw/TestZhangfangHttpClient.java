package com.abujlb.zdcj8.test.plw;

import com.abujlb.BaseTest;
import com.abujlb.ckstore.AbujlbCookieStore;
import com.abujlb.ckstore.Cjzh;
import com.abujlb.jyrb.bean.Jysj;
import com.abujlb.jyrb.http.ZhangfangHttpClient;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
* <AUTHOR>
* @date 2022-3-9
*/
public class TestZhangfangHttpClient extends BaseTest {

	
	@Autowired
	private AbujlbCookieStore abujlbCookieStore ;


    @Test
	public void test2() {
		String cookieKey ="ck_5418d88926a846778b2e6667cdf907cc" ;
		Cjzh cjzh = abujlbCookieStore.getCjzhForKey(cookieKey);
		if(cjzh==null ) return ;
		String rq ="2022-01-31";
		Jysj jysj = new Jysj() ;
		ZhangfangHttpClient.tmjfJysj(cjzh, jysj, rq);
		System.out.println(jysj.getTmjf());
	}
}
