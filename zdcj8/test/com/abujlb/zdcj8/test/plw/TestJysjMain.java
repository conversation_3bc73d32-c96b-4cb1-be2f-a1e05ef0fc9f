package com.abujlb.zdcj8.test.plw;

import java.util.List;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.abujlb.BaseTest;
import com.abujlb.ckstore.AbujlbCookieStore;
import com.abujlb.ckstore.Cjzh;
import com.abujlb.jyrb.bean.Dp;
import com.abujlb.jyrb.bean.JysjMsg;
import com.abujlb.jyrb.service.DataUploader;
import com.abujlb.jyrb.sjcj.jysj.JysjMain;
import com.abujlb.jyrb.sjcj.spxg.SpxgCjMain;
import com.abujlb.jyrb.sjcj.spxg.bean.Spxg;
import com.abujlb.jyrb.util.SpxgXLSReader;
import com.abujlb.util.DateUtil;

/**
* <AUTHOR>
* @date 2022-3-8
*/
public class TestJysjMain extends BaseTest {
	@Autowired
	private JysjMain jysjMain;
	@Autowired
	private SpxgCjMain spxgCjMain;
	@Autowired
	private AbujlbCookieStore abujlbCookieStore;
	@Autowired
	private DataUploader uploader;

	/**
	 * test采集经营数据：生意参谋首页 -> 整体视窗
	 */
	@Test
	public void cj() {
		String cookieKey = "ck_0bc868fb10fd4db49cf59e833ab938ae";
		Cjzh cjzh = abujlbCookieStore.getCjzhForKey(cookieKey);
		if (cjzh == null) {
			return;
		}
		Dp dp = uploader.hqdp(cjzh);
		if (dp == null) {
			return;
		}
		JysjMsg msg = new JysjMsg();
		msg.setType(JysjMsg.TYPE_LOGIN);
		jysjMain.cj(cjzh, dp, msg);
	}
	
	/**
	 * test采集商品效果：生意参谋首页 -> 商品效果
	 */
	@Test
	public void cjspxg() {
		String cookieKey = "ck_0bc868fb10fd4db49cf59e833ab938ae";
		Cjzh cjzh = abujlbCookieStore.getCjzhForKey(cookieKey);
		if (cjzh == null) {
			return;
		}
		Dp dp = uploader.hqdp(cjzh);
		if (dp == null) {
			return;
		}
		JysjMsg msg = new JysjMsg();
		msg.setType(JysjMsg.TYPE_LOGIN);
		spxgCjMain.cj(cjzh, dp, msg);
	}
	
	// main方法
	public static void main(String[] args) {
		try {
			// String itemid = "606973528063";
			
			String xls = "E:\\temp\\spxg\\39f7a514144a4241bb568a01dc348d7a.xls";
			List<Spxg> list = SpxgXLSReader.parseXLS(xls, DateUtil.parseDate("2023-02-28"), null, "0");
			System.out.println(list.toString());
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
}
