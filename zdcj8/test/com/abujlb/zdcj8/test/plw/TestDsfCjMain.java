package com.abujlb.zdcj8.test.plw;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.abujlb.BaseTest;
import com.abujlb.ckstore.AbujlbCookieStore;
import com.abujlb.ckstore.Cjzh;
import com.abujlb.jyrb.bean.Dp;
import com.abujlb.jyrb.bean.JysjMsg;
import com.abujlb.jyrb.service.DataUploader;
import com.abujlb.jyrb.sjcj.dsfcj.DsfMain;

/**
 * <AUTHOR>
 * @date 2022-3-8
 */
public class TestDsfCjMain extends BaseTest {
	@Autowired
	private DsfMain dsfMain;
	@Autowired
	private AbujlbCookieStore abujlbCookieStore;
	@Autowired
	private DataUploader uploader;

	@Test
	public void cj() {
		String cookieKey = "ck_798e22f78165467ebf52ebd4f8c18b30";
		Cjzh cjzh = abujlbCookieStore.getCjzhForKey(cookieKey);
		if (cjzh == null) {
			return;
		}
		Dp dp = uploader.hqdp(cjzh);
		if (dp == null) {
			return;
		}
		JysjMsg msg = new JysjMsg();
		msg.setType(JysjMsg.TYPE_LOGIN);
		dsfMain.kscj(cjzh, dp, msg);
	}

}
