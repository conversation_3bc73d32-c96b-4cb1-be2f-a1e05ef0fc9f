package com.abujlb.zdcj8.test.plw;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.abujlb.BaseTest;
import com.abujlb.ckstore.AbujlbCookieStore;
import com.abujlb.ckstore.Cjzh;
import com.abujlb.jyrb.bean.Dp;
import com.abujlb.jyrb.service.DataUploader;
import com.abujlb.jyrb.sjcj.jzfx.JzfxcjMain;

/**
 * <AUTHOR>
 * @date 2022-3-7
 */
public class TestJzfxcjMain extends BaseTest {

	@Autowired
	private JzfxcjMain jzfxcjMain;
	@Autowired
	private AbujlbCookieStore abujlbCookieStore;
	@Autowired
	private DataUploader uploader;

	@Test
	public void cj() {
		String cookieKey = "ck_b7c84a38ff3a4127a26aa25140e0f462";
		Cjzh cjzh = abujlbCookieStore.getCjzhForKey(cookieKey);
		if (cjzh == null) {
			return;
		}
		Dp dp = uploader.hqdp(cjzh);
		if (dp == null) {
			return;
		}
		jzfxcjMain.cj(cjzh, dp);
	}

}
