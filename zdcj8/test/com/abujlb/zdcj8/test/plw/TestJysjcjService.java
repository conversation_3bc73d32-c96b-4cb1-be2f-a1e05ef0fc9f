package com.abujlb.zdcj8.test.plw;

import com.abujlb.AbujlbBeanFactory;
import com.abujlb.BaseTest;
import com.abujlb.jyrb.bean.JysjMsg;
import com.abujlb.jyrb.service.JysjcjService;
import com.abujlb.util.StringUtil;
import com.abujlb.zdcj8.util.ObjAttrToNumber;
import net.sf.json.JSONObject;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2022-3-7
 */
public class TestJysjcjService extends BaseTest {

	@Autowired
	private AbujlbBeanFactory abujlbBeanFactory;

	@Test
	public void process() {
		JysjMsg jysjMsg = new JysjMsg();
		jysjMsg.setCookieKey("ck_69e57793698c4b3cb97c678f31be93a2");
		jysjMsg.setType(JysjMsg.TYPE_LOGIN);
		JysjcjService service = abujlbBeanFactory.getBean(JysjcjService.class, jysjMsg);
		service.process();
	}
	
	// main方法
	public static void main(String[] args) {
		try {
			String body = "{\"content\":{\"code\":0,\"data\":{\"adStrategyAmt\":{\"value\":0},\"zzExpendAmt\":0.0,\"statDate\":1681660800000,\"uv\":0,\"cartCnt\":0,\"p4pExpendAmt\":{\"value\":0},\"payByrCnt\":0,\"olderPayAmt\":0.0,\"cartByrCnt\":0,\"pv\":0,\"payRate\":0.0,\"payOrdCnt\":0,\"payOrdByrCnt365\":0,\"payAmt\":0.0,\"userId\":0,\"payOldByrCnt\":0,\"cubeAmt\":{\"value\":0},\"tkExpendAmt\":{\"value\":0},\"payItmCnt\":0,\"payPct\":0.0,\"rfdSucAmt\":null,\"feedCharge\":0.0,\"cltItmCnt\":0},\"message\":\"操作成功\",\"traceId\":\"2150464216919831461641034e2531\"},\"hasError\":false}";
			JSONObject jsonData = JSONObject.fromObject(body).getJSONObject("content").getJSONObject("data");
			int fks = 0;
			double ztchf = 0;
			if (jsonData.has("uv")) {
				if (StringUtil.isJson2(jsonData.getString("uv")) && jsonData.getJSONObject("uv").has("value")) {
					fks = ObjAttrToNumber.toInt(jsonData.getJSONObject("uv"), "value");
				} else {
					fks = ObjAttrToNumber.toInt(jsonData, "uv");
				}
            }
			if (jsonData.has("p4pExpendAmt")) {
				if (StringUtil.isJson2(jsonData.getString("p4pExpendAmt")) && jsonData.getJSONObject("p4pExpendAmt").has("value")) {
					ztchf = ObjAttrToNumber.toDouble(jsonData.getJSONObject("p4pExpendAmt"), "value");
				} else {
					ztchf = ObjAttrToNumber.toDouble(jsonData, "p4pExpendAmt");
				}
            }
			System.out.println(fks);
			System.out.println(ztchf);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

}
