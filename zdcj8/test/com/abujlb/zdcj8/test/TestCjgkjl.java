package com.abujlb.zdcj8.test;

import java.time.LocalDate;
import java.util.Random;
import java.util.concurrent.TimeUnit;

import com.abujlb.jyrb.sjcj.ztc.ZtcStatementsCjMain;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.config.RequestConfig.Builder;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.apache.log4j.Logger;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.abujlb.BaseTest;
import com.abujlb.ckstore.AbujlbCookieStore;
import com.abujlb.ckstore.Cjzh;

public class TestCjgkjl extends BaseTest {
	private static final Logger log = Logger.getLogger(TestCjgkjl.class);

	@Autowired
	private AbujlbCookieStore cookieStore;
	@Autowired
	private ZtcStatementsCjMain ztcStatementsCjMain;

	@Test
	public void test() {
		String cookieKey = "ck_c34ec36b669a483497bad44b6f2b2bc2";
		Cjzh cjzh = cookieStore.getCjzhForKey(cookieKey);
		if (cjzh != null) {
			this.cj2(cjzh);
		}
	}

	private void cj2(Cjzh cjzh) {
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cjzh.getCookieStore()).build();
		String url = "https://healthcenter.taobao.com/home/<USER>/get_list_data.do?page=1&_ksTS="
				+ System.currentTimeMillis() + "_" + randomInt() + "&callback=&status=1&dataType=1&entityType=1";
		HttpGet httpget = new HttpGet(url);
		httpget.setHeader("accept",
				"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript, */*; q=0.01");
		httpget.setHeader("accept-encoding", "gzip, deflate, br");
		httpget.setHeader("accept-language", "zh-CN,zh;q=0.9");
		httpget.setHeader("referer",
				"https://healthcenter.taobao.com/home/<USER>");
		httpget.setHeader("user-agent",
				"Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.132 Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(5000).setConnectTimeout(5000)
				.setConnectionRequestTimeout(5000);
		httpget.setConfig(requestConfig.build());

		HttpResponse httpresponse;
		try {
			httpresponse = httpClient.execute(httpget);
			HttpEntity entity = httpresponse.getEntity();
			String body = EntityUtils.toString(entity);
			System.out.println(body);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		} finally {
			httpget.releaseConnection();
		}
	}

	@Test
	public void test3() throws InterruptedException {
		String cookieKey = "ck_f0a52a7c49ca47d6906ca4a2abca18d6";
		Cjzh cjzh = cookieStore.getCjzhForKey(cookieKey);
		String rq = "2023-07-06";
		LocalDate date = LocalDate.parse("2023-07-06");
		// 获取最近1天的日期
		LocalDate endDate = date.minusDays(1);
		// 获取最近7天的起始日期
		LocalDate recent7DaysStart = date.minusDays(7);
		// 获取最近30天的起始日期
		LocalDate recent30DaysStart = date.minusDays(30);
//		ztcStatementsCjMain.cj(cjzh, rq, "1", recent7DaysStart.toString(), endDate.toString(),false);
//		//每次下载完成 等待30s
//		TimeUnit.SECONDS.sleep(30);
//		ztcStatementsCjMain.cj(cjzh, rq, "1", recent30DaysStart.toString(), endDate.toString(),true);
	}
	private int randomInt() {
		return (new Random().nextInt(90) + 10);
	}
}
