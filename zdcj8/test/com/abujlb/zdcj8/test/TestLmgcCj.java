package com.abujlb.zdcj8.test;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.abujlb.BaseTest;
import com.abujlb.ckstore.AbujlbCookieStore;
import com.abujlb.ckstore.Cjzh;
import com.abujlb.jyrb.bean.Dp;
import com.abujlb.jyrb.bean.JysjMsg;
import com.abujlb.jyrb.service.DataUploader;
import com.abujlb.jyrb.sjcj.lmgc.LmgcCjMain;

public class TestLmgcCj extends BaseTest {

	@Autowired
	private AbujlbCookieStore cookieStore;
	@Autowired
	private DataUploader uploader;
	@Autowired
	private LmgcCjMain cjMain;

	@Test
	public void test() {
		String ck = "ck_6659ed4a8ec847a7a7ce2cbdc3f714b7";
		Cjzh cjzh = cookieStore.getCjzhForKey(ck);
		Dp dp = uploader.hqdp(cjzh);
		JysjMsg msg = new JysjMsg();
		msg.setCookieKey(ck);
		msg.setType(JysjMsg.TYPE_MANUAL);
		cjMain.cj(cjzh, dp, msg);
	}
}
