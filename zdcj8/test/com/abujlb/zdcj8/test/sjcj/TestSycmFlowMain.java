package com.abujlb.zdcj8.test.sjcj;

import com.abujlb.BaseTest;
import com.abujlb.ckstore.AbujlbCookieStore;
import com.abujlb.ckstore.Cjzh;
import com.abujlb.jyrb.bean.Dp;
import com.abujlb.jyrb.service.DataUploader;
import com.abujlb.jyrb.sjcj.sycmflow.SycmFlowMain;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class TestSycmFlowMain extends BaseTest {


    @Autowired
    private SycmFlowMain sycmFlowMain;
    @Autowired
    private AbujlbCookieStore abujlbCookieStore;
    @Autowired
    private DataUploader dataUploader;

    @Test
    public void sycmflow() {
        String cookieKey = "ck_d051fdf48e5f4c17a663b80140512227";
        Cjzh cjzh = abujlbCookieStore.getCjzhForKey(cookieKey);
        if (cjzh == null) {
            return;
        }

        // 2.获取到店铺
        Dp dp = dataUploader.hqdp(cjzh);
        if (dp == null) {
            return;
        }

        dp.setMsgType(3);
        sycmFlowMain.process(cjzh, dp);
    }

}
