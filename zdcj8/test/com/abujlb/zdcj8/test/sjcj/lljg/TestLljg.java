package com.abujlb.zdcj8.test.sjcj.lljg;

import org.apache.log4j.Logger;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.abujlb.BaseTest;
import com.abujlb.ckstore.AbujlbCookieStore;
import com.abujlb.ckstore.Cjzh;
import com.abujlb.jyrb.bean.Dp;
import com.abujlb.jyrb.bean.JysjMsg;
import com.abujlb.jyrb.service.DataUploader;
import com.abujlb.jyrb.sjcj.lljg.LljgMain;

/**
 * 单元测试：流量结构
 * 
 * <AUTHOR>
 * @date 2020-12-02 16:23:38
 */
public class TestLljg extends BaseTest {
	
	private static Logger log = Logger.getLogger(TestLljg.class);
	
	@Autowired
	private AbujlbCookieStore cookieStore;
	@Autowired
	private LljgMain lljgMain;
	@Autowired
	private DataUploader uploader;

	/**
	 * 生意参谋，流量结构采集
	 * 
	 */
	@Test
	public void test() {
		// String dpmc = "冠琦家居旗舰店";
		String dpmc = "北乐岛旗舰店";
		try {
			String cookieKey = cookieStore.getCookieKeyForZhuzh(dpmc);
			if (cookieKey == null) {
				return;
			}
			Cjzh cjzh = cookieStore.getCjzhForKey(cookieKey);
			if (cjzh == null) {
				log.info(dpmc + "，获取采集账号失败！");
				return;
			}
			Dp dp = uploader.hqdp(cjzh);
			if (dp == null) {
				log.info(dpmc + "，获取店铺数据失败！");
				return;
			}
			JysjMsg msg = new JysjMsg();
			msg.setCookieKey(cookieKey);
			lljgMain.cj(cjzh, dp, msg);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			e.printStackTrace();
		}
	}
}
