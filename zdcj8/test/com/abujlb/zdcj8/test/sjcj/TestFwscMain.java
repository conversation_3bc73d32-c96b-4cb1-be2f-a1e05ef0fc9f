package com.abujlb.zdcj8.test.sjcj;

import com.abujlb.BaseTest;
import com.abujlb.ckstore.AbujlbCookieStore;
import com.abujlb.ckstore.Cjzh;
import com.abujlb.jyrb.sjcj.fwsc.FwscMain;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2022/8/4 16:44
 */
public class TestFwscMain extends BaseTest {
    @Autowired
    FwscMain fwscMain;
    @Autowired
    private AbujlbCookieStore abujlbCookieStore;

    @Test
    public void test() {
        String cookieKey = "ck_85cec065eef44f7c8b8edcca0c4b7b3a";
        Cjzh cjzhForKey = abujlbCookieStore.getCjzhForKey(cookieKey);
        if (cjzhForKey != null) {
            fwscMain.cj(cjzhForKey);
        }
    }
}
