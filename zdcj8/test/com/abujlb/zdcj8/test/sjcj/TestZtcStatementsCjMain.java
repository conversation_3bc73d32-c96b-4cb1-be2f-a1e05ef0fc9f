package com.abujlb.zdcj8.test.sjcj;

import com.abujlb.BaseTest;
import com.abujlb.ckstore.AbujlbCookieStore;
import com.abujlb.ckstore.Cjzh;
import com.abujlb.jyrb.bean.Dp;
import com.abujlb.jyrb.service.DataUploader;
import com.abujlb.jyrb.sjcj.ztc.ZtcStatementsCjMain;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

public class TestZtcStatementsCjMain extends BaseTest {

    @Autowired
    private ZtcStatementsCjMain ztcStatementsCjMain;

    @Autowired
    private AbujlbCookieStore abujlbCookieStore;
    @Autowired
    private DataUploader dataUploader;

    @Test
    public void test() {
        String cookieKey = "ck_a1979ef0a7dd4434a97f54ea60489ed1";
        Cjzh cjzh = abujlbCookieStore.getCjzhForKey(cookieKey);
        if (cjzh == null) {
            return;
        }

        // 2.获取到店铺
        Dp dp = dataUploader.hqdp(cjzh);
        if (dp == null) {
            return;
        }

        List<String> hqztcstaterq = dataUploader.hqztcstaterq(dp);
        System.out.println("hqztcstaterq = " + hqztcstaterq);
        ztcStatementsCjMain.cj(cjzh, dp);
    }
}
