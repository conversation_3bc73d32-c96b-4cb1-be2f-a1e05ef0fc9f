package com.abujlb.zdcj8.test.sjcj.ztc;

import java.io.UnsupportedEncodingException;
import java.lang.reflect.Type;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.config.RequestConfig.Builder;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.abujlb.BaseTest;
import com.abujlb.Result;
import com.abujlb.TestPara;
import com.abujlb.ckstore.AbujlbCookieStore;
import com.abujlb.ckstore.Cjzh;
import com.abujlb.gson.Json2Object;
import com.abujlb.util.DateUtil;
import com.abujlb.util.Md5;
import com.abujlb.zdcj8.bean.ZtcctHydjl;
import com.abujlb.zdcj8.http.ZtcHttpClient;
import com.abujlb.zdcj8.script.ZtcHashJSEngine;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

/**
 * 直通车测试
 * 
 * <AUTHOR>
 * @date 2020-08-05 10:15:10
 */
public class TestZtc extends BaseTest {

	private String server = "http://*************/"; // 2024-07-02调整：腾讯云服务器：************，调整IP：*************
	// private String server = "http://localhost/zdcj8/";

	@Autowired
	private AbujlbCookieStore cookieStore;
	// @Autowired
	// private ZtcService ztcService;
	@Autowired
	private ZtcHttpClient ztcHttpClient;
	@Autowired
	private ZtcHashJSEngine ztcHashJSEngine;

	/**
	 * 测试：获取直通车采集账号
	 * 
	 */
	@Test
	public void next() {
		Cjzh cjzh = cookieStore.nextCjzhZtc();
		System.out.println(cjzh.getCookieKey() + "," + cjzh.getCookieStore());
		System.out.println(cjzh.getMicrodata("legalityToken"));
	}

	/**
	 * 本地测试：接口请求
	 * 
	 */
	@Test
	public void next1() {
		Gson gson = new Gson();
		String[] aaa = new String[] { "上衣女夏", "雪纺上衣" };
		try {
			String stamp = "" + System.currentTimeMillis();
			String data = gson.toJson(aaa);

			List<TestPara> p = new ArrayList<TestPara>();
			p.add(new TestPara("data", data));
			p.add(new TestPara("stamp", stamp));
			p.add(new TestPara("password", Md5.password(data + stamp)));
			String s = this.post("/ztcct_hydjlv.action", p); // 在不发布项目，不启动tomcat的情况下使用相对链接（非全链接），进行测试
			System.out.println(s);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 本地测试：接口请求2
	 * 
	 */
	@Test
	public void next2() {
		Gson gson = new Gson();
		// String[] aaa = new String[]{"上衣女夏", "雪纺上衣"};
		String[] aaa = new String[] { "上衣女夏" };
		try {
			String stamp = "" + System.currentTimeMillis();
			String data = gson.toJson(aaa);

			List<TestPara> p = new ArrayList<TestPara>();
			p.add(new TestPara("data", data));
			p.add(new TestPara("stamp", stamp));
			p.add(new TestPara("password", Md5.password(data + stamp)));
			String s = this.post("/ztcct_hydjlv.action", p); // 在不发布项目，不启动tomcat的情况下使用相对链接（非全链接），进行测试
			System.out.println(s);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 本地测试：接口请求3
	 * 
	 */
	@Test
	public void next3() {
		Gson gson = new Gson();
		// String[] aaa = new String[]{"上衣女夏", "雪纺上衣"};
		String[] aaa = new String[] { "外套女" };

		CloseableHttpClient httpClient = HttpClients.custom().build();
		HttpPost httpPost = new HttpPost("http://localhost/zdcj8/ztcct_hydjlv.action");
		httpPost.setHeader("accept", "application/json, text/javascript, */*; q=0.01");
		httpPost.setHeader("accept-encoding", "gzip, deflate, br");
		httpPost.setHeader("accept-language", "zh-CN,zh;q=0.9");
		httpPost.setHeader("content-type", "application/x-www-form-urlencoded; charset=UTF-8");
		httpPost.setHeader("referer", "");
		httpPost.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.116 Safari/537.36");
		try {
			String stamp = "" + System.currentTimeMillis();
			String data = gson.toJson(aaa);
			// data = URLEncoder.encode(data, "utf-8");

			Builder requestConfig = RequestConfig.custom().setSocketTimeout(60000).setConnectTimeout(60000).setConnectionRequestTimeout(60000);
			httpPost.setConfig(requestConfig.build());
			ArrayList<BasicNameValuePair> pairs = new ArrayList<BasicNameValuePair>();
			pairs.add(new BasicNameValuePair("data", data));
			pairs.add(new BasicNameValuePair("stamp", stamp));
			pairs.add(new BasicNameValuePair("password", Md5.password(data + stamp)));

			UrlEncodedFormEntity urlEncodedFormEntity = new UrlEncodedFormEntity(pairs, "utf-8");
			httpPost.setEntity(urlEncodedFormEntity);
			HttpResponse httpResponse = httpClient.execute(httpPost);
			HttpEntity entity = httpResponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			System.out.println(body);
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			httpPost.releaseConnection();
		}
	}

	/**
	 * 服务器测试：接口请求3
	 * 
	 */
	@Test
	public void next31() {
		Gson gson = new Gson();
		String[] aaa = new String[] { "上衣女夏", "雪纺上衣" };
		// String[] aaa = new String[]{"上衣女夏"};
		String url = server + "ztcct_hydjlv.action";

		CloseableHttpClient httpClient = HttpClients.custom().build();
		HttpPost httpPost = new HttpPost(url);
		httpPost.setHeader("accept", "application/json, text/javascript, */*; q=0.01");
		httpPost.setHeader("accept-encoding", "gzip, deflate, br");
		httpPost.setHeader("accept-language", "zh-CN,zh;q=0.9");
		httpPost.setHeader("content-type", "application/x-www-form-urlencoded; charset=UTF-8");
		httpPost.setHeader("referer", "");
		httpPost.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.116 Safari/537.36");
		try {
			String stamp = "" + System.currentTimeMillis();
			String data = gson.toJson(aaa);
			// data = URLEncoder.encode(data, "utf-8");

			Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000).setConnectionRequestTimeout(20000);
			httpPost.setConfig(requestConfig.build());
			ArrayList<BasicNameValuePair> pairs = new ArrayList<BasicNameValuePair>();
			pairs.add(new BasicNameValuePair("data", data));
			pairs.add(new BasicNameValuePair("stamp", stamp));
			pairs.add(new BasicNameValuePair("password", Md5.password(data + stamp)));

			UrlEncodedFormEntity urlEncodedFormEntity = new UrlEncodedFormEntity(pairs, "utf-8");
			httpPost.setEntity(urlEncodedFormEntity);
			HttpResponse httpResponse = httpClient.execute(httpPost);
			HttpEntity entity = httpResponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			System.out.println(body);
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			httpPost.releaseConnection();
		}
	}

	/**
	 * 本地测试：接口请求4
	 * 
	 */
	@Test
	public void next4() {
		// ztcHttpClient.getCategory(null, null, null, 0);
		Cjzh cjzh = cookieStore.nextCjzhZtc();
		Result resultTemp = ztcHttpClient.getToken(cjzh, 3);
		if (resultTemp == null || !resultTemp.isSuccess()) {
			return;
		}
		String ztcUser = (String) resultTemp.getKey("ztcUser");
		String token = (String) resultTemp.getKey("token");
		// 获取webOpSessionId
		String webOpSessionId = ztcHashJSEngine.webOpSessionId();
		ztcHttpClient.getCategory(cjzh, token, ztcUser, "上衣", webOpSessionId, 0);
	}

	public static void main(String[] args) throws UnsupportedEncodingException {
		String[] aaa = new String[] { "上衣女夏", "雪纺上衣" };
		Gson gson = new Gson();
		System.out.println(gson.toJson(aaa));

		String bbb = "[\"上衣女夏\",\"雪纺上衣\"]";
		// Type type = new TypeToken<List<String>>() {
		// }.getType();
		// String[] arr = gson.fromJson(bbb, type);

		Type type = new TypeToken<String[]>() {
		}.getType();
		String[] arr = gson.fromJson(bbb, type);
		System.out.println();
		System.out.println(arr);
		System.out.println();

		String[] arr2 = Json2Object.parseObj(bbb, type);
		System.out.println(arr2);

		for (String str : arr) {
			System.out.println(str);
		}
		for (String str : arr2) {
			System.out.println(str);
		}
		System.out.println();
		String temp = "%E8%A1%AC%E8%A1%AB%E5%A5%B3%E5%A4%8F";
		System.out.println(URLDecoder.decode(temp, "utf-8"));
		System.out.println(URLEncoder.encode("雪纺上衣", "utf-8"));

		System.out.println();

		String body = "{\"custId\":null,\"result\":{\"cateForecastNotMatch\":\"0\",\"cateList\":[{\"cateId\":\"162104\",\"cateName\":\"女装/女士精品\\u0002衬衫\"},{\"cateId\":\"16\",\"cateName\":\"女装/女士精品\"}]},\"cause\":null,\"errors\":[],\"analyseTraceId\":null,\"illogic\":null,\"memberId\":null,\"code\":\"200\",\"msg\":null,\"success\":true,\"innerMsg\":null}";
		JSONObject jsonObj = JSONObject.fromObject(body);
		JSONArray dataArray = jsonObj.getJSONObject("result").getJSONArray("cateList");
		Type type2 = new TypeToken<List<ZtcctHydjl>>() {
		}.getType();
		List<ZtcctHydjl> list = Json2Object.parseObj(dataArray.toString(), type2);
		for (ZtcctHydjl zzz : list) {
			System.out.println(zzz.getCateId() + "，" + zzz.getCateName());
		}

		Date lastDate = DateUtil.getLastDay();
		String startDate = DateUtil.formatDate(DateUtil.getDateBefore(lastDate, 6)); // 开始日期：2020-07-31（7天前）
		String endDate = DateUtil.formatDate(lastDate); // 结束日期：2020-08-06（昨天）
		System.out.println("startDate=" + startDate + "，endDate=" + endDate);
	}
}
