package com.abujlb.zdcj8.test.sjcj.ztc;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.abujlb.BaseTest;
import com.abujlb.ckstore.AbujlbCookieStore;
import com.abujlb.ckstore.Cjzh;
import com.abujlb.jyrb.bean.Dp;
import com.abujlb.jyrb.bean.JysjMsg;
import com.abujlb.jyrb.service.DataUploader;
import com.abujlb.jyrb.sjcj.ztc.Ztccj;
import com.abujlb.jyrb.sjcj.ztc.bean.ZtcReport;

public class TestZtcSjcj extends BaseTest {

	@Autowired
	private Ztccj ztccj;
	@Autowired
	private AbujlbCookieStore cookieStore;
	@Autowired
	private DataUploader uploader;

	@Test
	public void test() {
		JysjMsg msg = new JysjMsg();
		msg.setCookieKey("ck_11a9401b44bb440e8f442f246e5d386e");
		msg.setType(JysjMsg.TYPE_LOGIN);
		Cjzh cjzh = cookieStore.getCjzhForKey(msg.getCookieKey());
		Dp dp = uploader.hqdp(cjzh);
		ZtcReport report = ztccj.kscj(cjzh, dp, msg);
		try {
			Thread.sleep(10000L);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
		ztccj.jscj(cjzh, dp, msg, report);
	}
}
