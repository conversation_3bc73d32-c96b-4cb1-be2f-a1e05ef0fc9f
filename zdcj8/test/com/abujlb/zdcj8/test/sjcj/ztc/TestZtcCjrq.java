package com.abujlb.zdcj8.test.sjcj.ztc;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.abujlb.BaseTest;
import com.abujlb.jyrb.bean.Dp;
import com.abujlb.jyrb.service.DataUploader;
import com.google.gson.Gson;

public class TestZtcCjrq extends BaseTest {
	@Autowired
	private DataUploader dataUploader;

	@Test
	public void test() {
		Dp dp = new Dp();
		dp.setJlbdpid(7584);
		System.out.println(dataUploader.hqztccjrq(dp));
	}

	@Test
	public void test2() {
		Dp dp = new Dp();
		dp.setJlbdpid(7584);
		Dp dp2 = dataUploader.hqdpForId(2897);
		Gson gson = new Gson();
		System.out.println(gson.toJson(dp2));
	}
}
