package com.abujlb.zdcj8.test.sjcj.ztc;

import java.util.ArrayList;
import java.util.List;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.abujlb.BaseTest;
import com.abujlb.ckstore.AbujlbCookieStore;
import com.abujlb.ckstore.Cjzh;
import com.abujlb.jyrb.bean.Dp;
import com.abujlb.jyrb.bean.JysjMsg;
import com.abujlb.jyrb.bean.JysjRq;
import com.abujlb.jyrb.service.DataUploader;
import com.abujlb.jyrb.sjcj.ztcct.CtcjMain;
import com.abujlb.jyrb.sjcj.ztcct.ZtcctCj;
import com.abujlb.jyrb.sjcj.ztcct.bean2.Campaign;
import com.abujlb.jyrb.sjcj.ztcct.bean2.CampaignData;
import com.abujlb.jyrb.sjcj.ztcct.bean2.ZtcctDate;
import com.abujlb.util.DateUtil;
import com.google.gson.Gson;

public class TestZtcct extends BaseTest {
	@Autowired
	private ZtcctCj ztcctCj;
	@Autowired
	private AbujlbCookieStore cookieStore;
	@Autowired
	private DataUploader uploader;
	@Autowired
	private CtcjMain ctcjMain;

	@Test
	public void test() {
		Gson gson = new Gson();
		String key = "ck_258a5b7b70be4e02b480d8fcc9b69df6";
		JysjMsg msg = new JysjMsg();
		msg.setCookieKey(key);
		Cjzh cjzh = cookieStore.getCjzhForKey(key);
		Dp dp = uploader.hqdp(cjzh);
		System.out.println(dp.getJlbdpid());

		System.out.println(ztcctCj.getToken(cjzh));
		System.out.println(cjzh.getData("ztc.token"));

		List<Campaign> list = ztcctCj.hqjh(cjzh);

		ZtcctDate date = new ZtcctDate();
		List<Long> campaignIdList = new ArrayList<Long>();
		list.stream().forEach(item -> {
			campaignIdList.add(Long.valueOf(item.getCampaignId()));
		});
		String ids = gson.toJson(campaignIdList);
		List<CampaignData> campaignData = ztcctCj.hqjhdata(cjzh, date, ids);
		System.out.println(gson.toJson(campaignData));
	}

	@Test
	public void test2() {
		String key = "ck_d93ab9428d10486083471d39566aa370";
		JysjMsg msg = new JysjMsg();
		msg.setCookieKey(key);
		Cjzh cjzh = cookieStore.getCjzhForKey(key);
		Dp dp = uploader.hqdp(cjzh);
		ctcjMain.kscj(cjzh, dp, msg);
	}
	
	@Test
	public void test3(){
		String key = "ck_d93ab9428d10486083471d39566aa370";
		JysjMsg msg = new JysjMsg();
		msg.setCookieKey(key);
		Cjzh cjzh = cookieStore.getCjzhForKey(key);
		Dp dp = uploader.hqdp(cjzh);
		
		JysjRq jysjrq = new JysjRq();
		jysjrq.setJlbdpid(dp.getJlbdpid());
		jysjrq.setDp(dp);
		jysjrq.setZtcctrq(DateUtil.parseDate("2020-04-27"));
		uploader.updztcctcjsj(jysjrq);
	}
	
	@Test
	public void test4(){
		Boolean hyrqbdToken = uploader.getHyrqbdToken(Long.valueOf("16"));
		System.out.println(hyrqbdToken);
	}
	
}
