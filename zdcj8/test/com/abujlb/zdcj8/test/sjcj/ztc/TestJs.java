package com.abujlb.zdcj8.test.sjcj.ztc;

import org.apache.log4j.Logger;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.abujlb.BaseTest;
import com.abujlb.zdcj8.script.ZtcHashJSEngine;

import net.sf.json.JSONObject;

/**
 * 单元测试：测试签名get-hash.js
 * 
 * <AUTHOR>
 * @date 2020-11-11 17:12:26
 */
public class TestJs extends BaseTest {

	private static Logger log = Logger.getLogger(TestJs.class);
	
	@Autowired
	private ZtcHashJSEngine ztcHashJSEngine;
	
	/**
	 * 签名测试
	 * 
	 */
	@Test
	public void test() {
		try {
			String sign = "192220216196208192228212428416400204480404220456";
			System.out.println(sign);
			String timestamp = "1605081238839";
			String ztcUser = "{\"loginType\":\"7\",\"nickName\":\"小壮熊旗舰店\",\"onestop\":\"0\",\"mamaclub_myresource_w\":\"1\",\"proxyType\":\"7\",\"oldToken\":\"ShGjA8\",\"isAdMarketingUser\":\"1\",\"mamaclub_myasset_w\":\"1\",\"new_cust_red_packet\":\"1\",\"operId\":\"**********\",\"outsideNumID\":\"**********\",\"token\":\"0cf07614\",\"isMamaClubUser\":\"1\",\"forbidFinance\":\"0\",\"pin\":\"3\",\"tbsellerType\":\"1\",\"tbsellerName\":\"天猫\",\"custId\":\"**********\",\"operName\":\"小壮熊旗舰店:俱乐部老师\",\"accountProxyType\":\"NONE\",\"hash\":\"0cf076140958dea3\",\"memberId\":\"*********\"}";
			// String ztcUser = "{loginType:\"7\",nickName:\"小壮熊旗舰店\",onestop:\"0\",mamaclub_myresource_w:\"1\",proxyType:\"7\",oldToken:\"ShGjA8\",isAdMarketingUser:\"1\",mamaclub_myasset_w:\"1\",new_cust_red_packet:\"1\",operId:\"**********\",outsideNumID:\"**********\",token:\"0cf07614\",isMamaClubUser:\"1\",forbidFinance:\"0\",pin:\"3\",tbsellerType:\"1\",tbsellerName:\"天猫\",custId:\"**********\",operName:\"小壮熊旗舰店:俱乐部老师\",accountProxyType:\"NONE\",hash:\"0cf076140958dea3\",memberId:\"*********\"}";
			
			
			byte[] bstr  =  ztcUser.getBytes("UTF-8");
			String ztcUser2 = new String(bstr);
			System.out.println(ztcUser);
			System.out.println(ztcUser2);
			// String ztcUser = "{'accountProxyType': 'NONE','custId': '**********','forbidFinance': '0','hash': '0cf076140958dea3','isAdMarketingUser': '1','isMamaClubUser': '1','loginType': '7','mamaclub_myasset_w': '1','mamaclub_myresource_w': '1','memberId': '*********','new_cust_red_packet': '1','nickName': '小壮熊旗舰店','oldToken': 'ShGjA8','onestop': '0','operId': '**********','operName': '小壮熊旗舰店:俱乐部老师','outsideNumID': '**********','pin': '3','proxyType': '7','tbsellerName': '天猫','tbsellerType': '1','token': '0cf07614'}";
			// String ztcUser = "{'loginType':'7','nickName':'小壮熊旗舰店','onestop':'0','mamaclub_myresource_w':'1','proxyType':'7','oldToken':'ShGjA8','isAdMarketingUser':'1','mamaclub_myasset_w':'1','new_cust_red_packet':'1','operId':'**********','outsideNumID':'**********','token':'0cf07614','isMamaClubUser':'1','forbidFinance':'0','pin':'3','tbsellerType':'1','tbsellerName':'天猫','custId':'**********','operName':'小壮熊旗舰店:俱乐部老师','accountProxyType':'NONE','hash':'0cf076140958dea3','memberId':'*********'}";
			JSONObject jsonObj = JSONObject.fromObject(ztcUser);
			System.out.println(ztcHashJSEngine.getHash(timestamp, jsonObj.toString()));
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		}
	}
	
}
