package com.abujlb.zdcj8.test.sjcj.ztc;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.abujlb.BaseTest;
import com.abujlb.ckstore.AbujlbCookieStore;
import com.abujlb.ckstore.Cjzh;
import com.abujlb.jyrb.bean.Dp;
import com.abujlb.jyrb.bean.JysjMsg;
import com.abujlb.jyrb.service.DataUploader;
import com.abujlb.jyrb.sjcj.ztcct.CtcjMain;

public class TestZtcctcjMain extends BaseTest {
	@Autowired
	private CtcjMain ctcjMain;
	@Autowired
	private AbujlbCookieStore cookieStore;
	@Autowired
	private DataUploader uploader;

	@Test
	public void test() {
		JysjMsg msg = new JysjMsg();
		msg.setCookieKey("ck_39c9bea3349d45a1b3c33356a13869ea");
		msg.setType(JysjMsg.TYPE_LOGIN);
		Cjzh cjzh = cookieStore.getCjzhForKey(msg.getCookieKey());
		Dp dp = uploader.hqdp(cjzh);
		dp.setMsgType(msg.getType());
		ctcjMain.kscj(cjzh, dp, msg);
	}

}
