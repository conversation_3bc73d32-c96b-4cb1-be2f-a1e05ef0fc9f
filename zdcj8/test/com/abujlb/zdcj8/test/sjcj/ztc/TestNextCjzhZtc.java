package com.abujlb.zdcj8.test.sjcj.ztc;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.abujlb.BaseTest;
import com.abujlb.ckstore.AbujlbCookieStore;
import com.abujlb.ckstore.Cjzh;

public class TestNextCjzhZtc extends BaseTest {
	@Autowired
	private AbujlbCookieStore cookieStore;

	@Test
	public void next() {
		Cjzh cjzh = cookieStore.nextCjzhZtc();
		System.out.println(cjzh.getCookieKey()+","+cjzh.getCookieStore());
	}
}
