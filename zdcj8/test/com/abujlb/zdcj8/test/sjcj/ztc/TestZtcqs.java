package com.abujlb.zdcj8.test.sjcj.ztc;

import java.io.UnsupportedEncodingException;
import java.lang.reflect.Type;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.abujlb.BaseTest;
import com.abujlb.Result;
import com.abujlb.TestPara;
import com.abujlb.util.Md5;
import com.abujlb.zdcj8.bean.ztc.ZtcqsBean;
import com.abujlb.zdcj8.script.ZtcHashJSEngine;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

/**
 * ztc直通车测试
 * 
 * <AUTHOR>
 * @date 2023-03-06 16:31:15
 */
public class TestZtcqs extends BaseTest {

	// private String server = "http://*************/"; // 2024-07-02调整：腾讯云服务器：************，调整IP：*************

	@Autowired
	private ZtcHashJSEngine ztcHashJSEngine;

	/**
	 * test本地测试：接口请求
	 * 
	 */
	@Test
	public void test() {
		Gson gson = new Gson();
		String[] aaa = new String[] { "上衣女夏", "雪纺上衣" };
		try {
			String stamp = "" + System.currentTimeMillis();
			String data = gson.toJson(aaa);

			List<TestPara> p = new ArrayList<TestPara>();
			p.add(new TestPara("data", data));
			p.add(new TestPara("stamp", stamp));
			p.add(new TestPara("password", Md5.password(data + stamp)));
			String s = this.post("/ztcct_hydjlv.action", p); // 在不发布项目，不启动tomcat的情况下使用相对链接（非全链接），进行测试
			System.out.println(s);

			Type type = new TypeToken<String[]>() {
			}.getType();
			String[] arr = Result.getValueFromJson(s, "data", type);
			System.out.println(arr);
			for (String str : arr) {
				System.out.println(str);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * test本地测试：接口请求2
	 * 
	 */
	@Test
	public void test2() {
		try {
			String stamp = "" + System.currentTimeMillis();
			String data = "{\"gjc\":\"儿童保温杯\",\"startDate\":\"2023-02-24\",\"endDate\":\"2023-03-05\"}";

			List<TestPara> p = new ArrayList<TestPara>();
			p.add(new TestPara("data", data));
			p.add(new TestPara("stamp", stamp));
			p.add(new TestPara("password", Md5.password(data + stamp)));
			String s = this.post("/ztcct_gjcqs.action", p); // 在不发布项目，不启动tomcat的情况下使用相对链接（非全链接），进行测试
			System.out.println(s);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * test本地测试：接口请求3
	 * 
	 */
	@Test
	public void test3() {
		double d1 = Math.random();
		System.out.println(d1);
		System.out.println(String.valueOf(d1).substring(2, 6));
		String timeStamp = String.valueOf(System.currentTimeMillis());
		System.out.println(timeStamp);
		// System.out.println("1678092762458");
		String str = String.valueOf(d1).substring(2, 6) + timeStamp;
		System.out.println(str);
		System.out.println(Long.toString(Long.valueOf(str), 36));

		System.out.println();
		System.out.println(ztcHashJSEngine.webOpSessionId());
	}

	public static void main(String[] args) throws UnsupportedEncodingException {
		System.out.println(URLDecoder.decode("%E4%BF%9D%E6%B8%A9%E6%9D%AF%E7%94%B7", "utf-8"));
		System.out.println(URLEncoder.encode("保温杯男", "utf-8"));
		System.out.println("%E4%BF%9D%E6%B8%A9%E6%9D%AF%E7%94%B7");
		System.out.println();

		double d1 = Math.random();
		System.out.println(d1);
		System.out.println(String.valueOf(d1).substring(2, 6));
		String timeStamp = String.valueOf(System.currentTimeMillis());
		System.out.println(timeStamp);
		// System.out.println("1678092762458");
		String str = String.valueOf(d1).substring(2, 6) + timeStamp;
		System.out.println(str);
		System.out.println(Long.toString(Long.valueOf(str), 36));
		
		ZtcqsBean ztcqsBean = new ZtcqsBean();
		ztcqsBean.setGjc("儿童保温杯");
		ztcqsBean.setStartDate("2023-02-24");
		ztcqsBean.setEndDate("2023-03-05");
		System.out.println(ztcqsBean.toString());
	}
}
