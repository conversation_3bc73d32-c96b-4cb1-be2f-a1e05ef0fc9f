package com.abujlb.zdcj8.test.sjcj;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.abujlb.AbujlbBeanFactory;
import com.abujlb.BaseTest;
import com.abujlb.ckstore.AbujlbCookieStore;
import com.abujlb.ckstore.Cjzh;
import com.abujlb.jyrb.bean.Dp;
import com.abujlb.jyrb.bean.Jysj;
import com.abujlb.jyrb.bean.JysjMsg;
import com.abujlb.jyrb.service.DataUploader;
import com.abujlb.jyrb.sjcj.jysj.JysjMain;

/**
 * <AUTHOR>
 * @date 2022/2/28 15:32
 */
public class TestJysjMain extends BaseTest {
    @Autowired
    private AbujlbBeanFactory abujlbBeanFactory;
    @Autowired
    private AbujlbCookieStore abujlbCookieStore;
    @Autowired
    private DataUploader dataUploader;

    @Test
    public void cj() {
        String cookieKey = "ck_a89575f21748427da4f3c483d9f759dc";
        Cjzh cjzh = abujlbCookieStore.getCjzhForKey(cookieKey);
        if (cjzh == null) {
            return;
        }

        // 2.获取到店铺
        Dp dp = dataUploader.hqdp(cjzh);
        if (dp == null) {
            return;
        }
        JysjMsg jysjMsg = new JysjMsg() ;
        // jysjMsg.setType(JysjMsg.TYPE_EVENING);
        jysjMsg.setType(JysjMsg.TYPE_LOGIN);

        dp.setMsgType(jysjMsg.getType());
        JysjMain jysjMain = abujlbBeanFactory.getBean(JysjMain.class);
        jysjMain.cj(cjzh, dp, jysjMsg);
    }
    
    @Test
    public void bzjlp() {
    	 String cookieKey = "ck_add9a90bfc734ceb8e82f5f180e1d7dd";
         Cjzh cjzh = abujlbCookieStore.getCjzhForKey(cookieKey);
         if (cjzh == null) {
             return;
         }

         // 2.获取到店铺
         Dp dp = dataUploader.hqdp(cjzh);
         if (dp == null) {
             return;
         }
         JysjMsg jysjMsg = new JysjMsg() ;
         jysjMsg.setType(JysjMsg.TYPE_EVENING);

         Jysj jysj = new Jysj();
         dp.setMsgType(jysjMsg.getType());
         JysjMain jysjMain = abujlbBeanFactory.getBean(JysjMain.class);
         jysjMain.bzjlp(cjzh, jysj, dp, "2023-08-02");
         System.out.println(jysj.getBzjlphf());
    }
}
