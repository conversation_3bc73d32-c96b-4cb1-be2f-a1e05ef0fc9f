package com.abujlb.zdcj8.test.sjcj.versionnew;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.abujlb.BaseTest;
import com.abujlb.ckstore.AbujlbCookieStore;
import com.abujlb.ckstore.Cjzh;
import com.abujlb.jyrb.bean.Dp;
import com.abujlb.jyrb.bean.JysjMsg;
import com.abujlb.jyrb.service.DataUploader;
import com.abujlb.jyrb.sjcj.version.VersionNewMain;

public class TestVersionNewMain extends BaseTest {
	@Autowired
	private AbujlbCookieStore cookieStore;
	@Autowired
	private VersionNewMain versionNewMain;
	@Autowired
	private DataUploader uploader;

	@Test
	public void test() {
		String key = "ck_118ed63c085e45c0abf2cf5f6ddbe021"; // 新版生意参谋，小孩子母婴专营店:导师助理
		// String key = "ck_a46b68bf0e3849aab69919208c6bbbbc"; // 旧版生意参谋，huanqiu旗舰店:数据分析
		JysjMsg msg = new JysjMsg();
		msg.setCookieKey(key);
		Cjzh cjzh = cookieStore.getCjzhForKey(key);
		Dp dp = uploader.hqdp(cjzh);
		System.out.println(dp.getJlbdpid());
		versionNewMain.cj(cjzh, dp, msg);
	}
}
