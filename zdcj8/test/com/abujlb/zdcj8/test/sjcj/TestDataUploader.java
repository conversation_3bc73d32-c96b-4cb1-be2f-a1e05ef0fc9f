package com.abujlb.zdcj8.test.sjcj;

import com.abujlb.AbujlbBeanFactory;
import com.abujlb.BaseTest;
import com.abujlb.ckstore.AbujlbCookieStore;
import com.abujlb.ckstore.Cjzh;
import com.abujlb.jyrb.bean.Dp;
import com.abujlb.jyrb.bean.JysjMsg;
import com.abujlb.jyrb.bean.JysjRq;
import com.abujlb.jyrb.service.DataUploader;
import com.abujlb.jyrb.sjcj.ylmf.YlmfCjMain;
import com.abujlb.util.DateUtil;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-4-8
 */
public class TestDataUploader extends BaseTest {


    @Autowired
    private DataUploader dataUploader;
    @Autowired
    private AbujlbCookieStore abujlbCookieStore;
    @Autowired
    private AbujlbBeanFactory abujlbBeanFactory;


    @Test
    public void test2() {
        Dp dp = new Dp();
        dp.setJlbdpid(2);
        List<Date> result = new ArrayList<Date>();
        List<String> list = dataUploader.hqspxgcjrq(dp, 3);
        try {
            DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
            for (int i = 0; list != null && i < list.size(); i++) {
                result.add(df.parse(list.get(i)));
            }
            System.out.println("list = " + list);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    @Test
    public void ylmfget() {
        String cookieKey = "ck_2359adaf036346e680dcfa637f2d2bb4";
        Cjzh cjzh = abujlbCookieStore.getCjzhForKey(cookieKey);
        if (cjzh == null) {
            return;
        }

        // 2.获取到店铺
        Dp dp = dataUploader.hqdp(cjzh);
        if (dp == null) {
            return;
        }
        JysjMsg jysjMsg = new JysjMsg();
        jysjMsg.setType(JysjMsg.TYPE_EVENING);

        dp.setMsgType(jysjMsg.getType());

        List<String> list = dataUploader.hqylmfcjrq(dp, jysjMsg.getType());
        System.out.println(list);
    }


    @Test
    public void ylmfupdate() {
        String cookieKey = "ck_2359adaf036346e680dcfa637f2d2bb4";
        Cjzh cjzh = abujlbCookieStore.getCjzhForKey(cookieKey);
        if (cjzh == null) {
            return;
        }

        // 2.获取到店铺
        Dp dp = dataUploader.hqdp(cjzh);
        if (dp == null) {
            return;
        }
        JysjMsg jysjMsg = new JysjMsg();
        jysjMsg.setType(JysjMsg.TYPE_EVENING);

        dp.setMsgType(jysjMsg.getType());

        JysjRq jysjrq = new JysjRq();
        jysjrq.setDp(dp);
        jysjrq.setJlbdpid(dp.getJlbdpid());
        Date date = DateUtil.parseDate("2022-02-19");
        jysjrq.setYlmfrq(date);
        dataUploader.updylmfcjsj(jysjrq);
    }

    @Test
    public void test22() {
        String cookieKey = "ck_2359adaf036346e680dcfa637f2d2bb4";
        Cjzh cjzh = abujlbCookieStore.getCjzhForKey(cookieKey);
        if (cjzh == null) {
            return;
        }

        /*Map<String, String> map = YlmfHttpClient.getCrsfid(cjzh);
        if (map == null || map.isEmpty() || !map.containsKey("csrfID") || !map.containsKey("seedToken")) {
            System.out.println(cjzh.getDpmc() + "俱乐部引力魔方CSRFID获取失败，" + DateUtil.formatTime(new Date()));
            return;
        }*/
        // 2.获取到店铺
        Dp dp = dataUploader.hqdp(cjzh);
        if (dp == null) {
            return;
        }
        JysjMsg jysjMsg = new JysjMsg();
        jysjMsg.setType(JysjMsg.TYPE_EVENING);

        dp.setMsgType(jysjMsg.getType());

        YlmfCjMain yl = abujlbBeanFactory.getBean(YlmfCjMain.class);
        yl.process(cjzh, dp, jysjMsg);
    }


    @Test
    public void u() {

    }
}
