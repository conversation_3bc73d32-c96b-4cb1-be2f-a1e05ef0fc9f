package com.abujlb.zdcj8.test.sjcj;

import com.abujlb.AbujlbBeanFactory;
import com.abujlb.BaseTest;
import com.abujlb.ckstore.AbujlbCookieStore;
import com.abujlb.ckstore.Cjzh;
import com.abujlb.jyrb.sjcj.fwsc.FwscMain;
import com.abujlb.jyrb.sjcj.seller.SellerCentralMsgSubscriptService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2022/8/4 16:44
 */
public class TestMsgSubscript extends BaseTest {
    @Autowired
    FwscMain fwscMain;
    @Autowired
    private AbujlbCookieStore abujlbCookieStore;
    @Autowired
    private AbujlbBeanFactory abujlbBeanFactory;

    @Test
    public void test() throws Exception {


        SellerCentralMsgSubscriptService bean = abujlbBeanFactory.getBean(SellerCentralMsgSubscriptService.class);
        String cookieKey = "ck_c3e226b1da004cf89a947fdc5b7325f1";
        Cjzh cjzhForKey = abujlbCookieStore.getCjzhForKey(cookieKey);
        bean.subscript(cjzhForKey);

    }
}
