package com.abujlb.zdcj8.test;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.abujlb.BaseTest;
import com.abujlb.ckstore.AbujlbCookieStore;
import com.abujlb.ckstore.Cjzh;
import com.abujlb.jyrb.bean.Dp;
import com.abujlb.jyrb.service.DataUploader;
import com.abujlb.jyrb.sjcj.jysj.util.SycmTodoList;

public class TestTodoList extends BaseTest {
	@Autowired
	private AbujlbCookieStore cookieStore;
	@Autowired
	private DataUploader uploader;
	@Autowired
	private SycmTodoList todoList;

	@Test
	public void test() {
		String ck = "ck_a9d43c7abab5474fa1ea64cfa3fe7086";
		Cjzh cjzh = cookieStore.getCjzhForKey(ck);
		Dp dp = uploader.hqdp(cjzh);
		String json = todoList.getJson(cjzh, dp);
		System.out.println(json);
		int spsl = todoList.getSpsl(json);
		System.out.println(spsl);

	}
}
