package com.abujlb.zdcj8.test.sjts;

import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.config.RequestConfig.Builder;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.abujlb.BaseTest;
import com.abujlb.ckstore.AbujlbCookieStore;
import com.abujlb.ckstore.Cjzh;
import com.abujlb.util.SycmTransitid;
import com.abujlb.zdcj8.bean.DptsPara;
import com.abujlb.zdcj8.service.Dpts2Service;
import com.abujlb.zdcj8.service.DptsSpuService;

public class TestDpts2 extends BaseTest {
	@Autowired
	private Dpts2Service dpts2Service;
	@Autowired
	private DptsSpuService dptsSpuService;
	@Autowired
	private AbujlbCookieStore cookieStore;

	@Test
	public void test() {
		DptsPara p = new DptsPara("613570420714", "0");
		String s = dpts2Service.ts(p);
		System.out.println(s);
	}

	@Test
	public void test2() {
		DptsPara p = new DptsPara("1647599615", "0");
		String s = dptsSpuService.ts(p);
		System.out.println(s);
	}
	
	@Test
	public void test3(){
		String key = "ck_bd1b85e0c46042b6941ab2d5a704eebe";
		Cjzh cjzh = cookieStore.getCjzhForKey(key);
		String cateid = "28"; //50008163
		DptsPara p = new DptsPara("626713119583","0");
		p.setDate("2020-11-23");
		this.getSummary(cjzh, cateid, p);
	}

	public String getSummary(Cjzh cjzh, String cateid, DptsPara p) {
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cjzh.getCookieStore()).build();
		long t = System.currentTimeMillis();

		String url = "https://sycm.taobao.com/mc/ci/item/trend.json?dateType=day&dateRange=" + p.getDate() + "%7C"
				+ p.getDate() + "&cateId=" + cateid + "&itemId=" + p.getBbid() + "&device=" + p.getDevice()
				+ "&sellerType=-1&indexCode=uvIndex%2CpayRateIndex%2CtradeIndex%2CpayByrCntIndex&_=" + t + "&token="
				+ cjzh.getMicrodata("legalityToken");
		HttpGet httpget = new HttpGet(url);
		httpget.setHeader("accept", "*/*");
		httpget.setHeader("accept-encoding", "gzip, deflate, sdch, br");
		httpget.setHeader("accept-language", "zh-CN,zh;q=0.8");
		httpget.setHeader("referer", "https://sycm.taobao.com/mc/mq/product_insight");
		httpget.setHeader("transit-id", SycmTransitid.getTransitid());
		httpget.setHeader("user-agent",
				"Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/55.0.2883.75 Safari/537.36");
		Builder requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000)
				.setConnectionRequestTimeout(20000);
		httpget.setConfig(requestConfig.build());

		HttpResponse httpresponse;
		try {
			httpresponse = httpClient.execute(httpget);
			HttpEntity entity = httpresponse.getEntity();
			String body = EntityUtils.toString(entity);
			System.out.println(body);

		} catch (Exception e) {

		} finally {
			httpget.releaseConnection();
		}
		return null;
	}
}
