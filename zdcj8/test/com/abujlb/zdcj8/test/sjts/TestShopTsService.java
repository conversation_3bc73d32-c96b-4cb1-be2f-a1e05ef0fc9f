package com.abujlb.zdcj8.test.sjts;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.abujlb.BaseTest;
import com.abujlb.zdcj8.bean.ShoptsData;
import com.abujlb.zdcj8.service.ShoptsService;
import com.google.gson.Gson;

public class TestShopTsService extends BaseTest {
	@Autowired
	private ShoptsService shoptsService;

	@Test
	public void test() {
		ShoptsData sd = shoptsService.getData("2267305246", "16", "2021-03-21");
		Gson gson = new Gson();
		System.out.println(gson.toJson(sd));
	}

}
