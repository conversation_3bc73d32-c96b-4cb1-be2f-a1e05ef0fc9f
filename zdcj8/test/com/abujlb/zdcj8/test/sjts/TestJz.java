package com.abujlb.zdcj8.test.sjts;

import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.config.RequestConfig.Builder;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.abujlb.BaseTest;
import com.abujlb.ckstore.AbujlbCookieStore;
import com.abujlb.ckstore.Cjzh;
import com.abujlb.util.SycmDataTojson;
import com.abujlb.util.SycmTransitid;

import net.sf.json.JSONObject;

public class TestJz extends BaseTest {
	@Autowired
	private AbujlbCookieStore cookieStore;

	@Test
	public void test() {
		String cookieKey = "ck_cd7c57cbe34c495c83b876310261bb23";
		Cjzh cjzh = cookieStore.getCjzhForKey(cookieKey);
		if (cjzh != null) {
			this.cj2(cjzh);
		}else{
			System.out.println("null");
		}
	}

	private void cj2(Cjzh cjzh) {
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cjzh.getCookieStore()).build();
		String url = "https://sycm.taobao.com/mc/ci/item/trend.json?dateType=month&dateRange=2020-12-01%7C2020-12-30&cateId=50014812&itemId=589031590661&device=0&sellerType=-1&indexCode=uvIndex%2CpayRateIndex%2CtradeIndex%2CpayByrCntIndex&_=1609898517948&token=5979d0c1b";
		HttpGet httpget = new HttpGet(url);
		httpget.setHeader("accept",
				"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript, */*; q=0.01");
		httpget.setHeader("accept-encoding", "gzip, deflate, br");
		httpget.setHeader("accept-language", "zh-CN,zh;q=0.9");
		httpget.setHeader("referer",
				"https://sycm.taobao.com/mc/ci/item/analysis");
		httpget.setHeader("user-agent",
				"Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.132 Safari/537.36");
		httpget.setHeader("transit-id", SycmTransitid.getTransitid());

		Builder requestConfig = RequestConfig.custom().setSocketTimeout(5000).setConnectTimeout(5000)
				.setConnectionRequestTimeout(5000);
		httpget.setConfig(requestConfig.build());

		HttpResponse httpresponse;
		try {
			httpresponse = httpClient.execute(httpget);
			HttpEntity entity = httpresponse.getEntity();
			String body = EntityUtils.toString(entity);
			JSONObject jsonObj = JSONObject.fromObject(body);
			// 记录错误
			if (jsonObj.has("code") && jsonObj.getInt("code") != 0) {
				System.out.println(body);
			}
			if (jsonObj.has("code") && jsonObj.getInt("code") == 0) {
				String data = SycmDataTojson.toJson(jsonObj.getString("data"));
				System.out.println(data);
			} else {
				System.out.println(body);
			}
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			httpget.releaseConnection();
		}
	}
}
