package com.abujlb.zdcj8.test;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.abujlb.BaseTest;
import com.abujlb.ckstore.AbujlbCookieStore;
import com.abujlb.ckstore.Cjzh;
import com.abujlb.jyrb.bean.Dp;
import com.abujlb.jyrb.bean.JysjMsg;
import com.abujlb.jyrb.service.DataUploader;
import com.abujlb.jyrb.sjcj.jzfx.JzfxcjMain;

public class TestJzfx extends BaseTest {
	@Autowired
	private JzfxcjMain jzfxcj;
	@Autowired
	private AbujlbCookieStore cookieStore;
	@Autowired
	private DataUploader uploader;

	@Test
	public void test() {
		String ck = "ck_c34ec36b669a483497bad44b6f2b2bc2";
		Cjzh cjzh = cookieStore.getCjzhForKey(ck);
		Dp dp = uploader.hqdp(cjzh);
		JysjMsg msg = new JysjMsg();
		msg.setCookieKey(ck);
		msg.setType(JysjMsg.TYPE_MANUAL);
		jzfxcj.cj(cjzh,dp);
	}
}
