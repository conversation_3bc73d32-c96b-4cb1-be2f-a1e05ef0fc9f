package com.abujlb.zdcj8.test.consumer;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.abujlb.BaseTest;
import com.abujlb.zdcj8.consumer.ConsumerExecutor;

public class TestConsumer extends BaseTest {

	@Autowired
	private ConsumerExecutor consumerExecutor;

	@Test
	public void test() {
		while (true) {
			String s = consumerExecutor.exec("/zdcj8provider/provider/test", "dataasdflkj");
			System.out.println(s);
			try {
				Thread.sleep(3000L);
			} catch (InterruptedException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
	}

}
