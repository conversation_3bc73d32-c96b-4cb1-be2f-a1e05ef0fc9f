package com.abujlb.zdcj8.test;

import javax.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.abujlb.AbujlbBeanFactory;
import com.google.gson.Gson;

@Component
public class TestA {
	@Autowired
	private AbujlbBeanFactory beanFactory;

	@PostConstruct
	public void init() {
		String[] s = beanFactory.getSpringContext().getBeanDefinitionNames();
		Gson gson = new Gson();
		System.out.println(gson.toJson(s));
	}
}
