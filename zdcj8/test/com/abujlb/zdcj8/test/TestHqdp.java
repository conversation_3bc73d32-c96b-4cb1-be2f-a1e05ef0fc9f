package com.abujlb.zdcj8.test;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.abujlb.BaseTest;
import com.abujlb.jyrb.bean.Dp;
import com.abujlb.jyrb.service.DataUploader;

public class TestHqdp extends BaseTest {
	@Autowired
	private DataUploader uploader;

	@Test
	public void test() {
		int dpid = 1912;
		Dp dp = uploader.hqdpForId(dpid);
		System.out.println(dp.getDpmc());
	}
}
