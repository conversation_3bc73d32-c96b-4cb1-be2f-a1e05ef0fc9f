package com.abujlb.zdcj8.test;

import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.abujlb.BaseTest;
import com.abujlb.ckstore.AbujlbCookieStore;
import com.abujlb.ckstore.Cjzh;
import com.abujlb.util.SycmDataTojson;
import com.abujlb.util.SycmTransitid;

import net.sf.json.JSONObject;

public class TestTnnc extends BaseTest {
	@Autowired
	private AbujlbCookieStore cookieStore;

	@Test
	public void test() {
		String zhuzh = "tnnc旗舰店";
		String ck = cookieStore.getCookieKeyForZhuzh(zhuzh);
		Cjzh cjzh = cookieStore.getCjzhForKey(ck);
		this.cj(cjzh);
	}

	public void cj(Cjzh cjzh) {
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cjzh.getCookieStore()).build();
		HttpGet httpget = new HttpGet(
				"https://sycm.taobao.com/mc/mq/supply/mkt/overview.json?dateType=day&dateRange=2020-01-09%7C2020-01-09&cateId=50011129&device=0&sellerType=-1&_="
						+ System.currentTimeMillis() + "&token=" + cjzh.getMicrodata("legalityToken"));
		httpget.setHeader("accept", "*/*");
		httpget.setHeader("accept-encoding", "gzip, deflate, sdch, br");
		httpget.setHeader("accept-language", "zh-CN,zh;q=0.8");
		httpget.setHeader("referer",
				"https://sycm.taobao.com/mc/mq/overview?cateFlag=2&cateId=50011129&dateRange=2020-01-09%7C2020-01-09&dateType=day&device=0&parentCateId=30&sellerType=-1");
		httpget.setHeader("transit-id", SycmTransitid.getTransitid());
		httpget.setHeader("user-agent",
				"Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/55.0.2883.75 Safari/537.36");

		String body = null;
		HttpResponse httpresponse;
		try {
			httpresponse = httpClient.execute(httpget);
			HttpEntity entity = httpresponse.getEntity();
			body = EntityUtils.toString(entity);
			JSONObject jsonObj = JSONObject.fromObject(body);
			if (jsonObj.has("code") && jsonObj.getInt("code") == 0) {
				String data = SycmDataTojson.toJson(jsonObj.getString("data"));
				System.out.println(data);
			}
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			httpget.releaseConnection();
		}
	}
}
