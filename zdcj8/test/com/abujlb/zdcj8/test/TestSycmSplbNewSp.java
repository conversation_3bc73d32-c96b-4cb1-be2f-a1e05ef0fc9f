package com.abujlb.zdcj8.test;

import java.util.Date;
import java.util.Objects;

import org.apache.log4j.Logger;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.abujlb.BaseTest;
import com.abujlb.ckstore.AbujlbCookieStore;
import com.abujlb.ckstore.Cjzh;
import com.abujlb.jyrb.http.SycmSplbHttpClient;
import com.abujlb.jyrb.oss.BiDataOss;
import com.abujlb.jyrb.oss.OssUploader;
import com.abujlb.jyrb.sjcj.lljg.constant.SycmConst;
import com.abujlb.jyrb.util.BiDateUtil;
import com.abujlb.lock.DistributedLock;
import com.abujlb.lock.LockObj;
import com.alibaba.fastjson.JSONArray;
import com.google.gson.Gson;

/**
 * sycmSplbNewSp生意参谋商品列表采集，仅采集店铺新品（9点之后，每天仅采集，当天的数据）
 * 
 * <AUTHOR>
 * @date 2024-08-23 14:42:52
 */
public class TestSycmSplbNewSp extends BaseTest {

	private static final Logger log = Logger.getLogger(TestSycmSplbNewSp.class);

	@Autowired
	private AbujlbCookieStore cookieStore;
	@Autowired
	private BiDataOss biDataOss;
	@Autowired
	private OssUploader ossUploader;
	@Autowired
	private DistributedLock distributedLock;

	@Test
	public void test() {
		LockObj lockObj = null;
		Gson gson = new Gson();
		try {
			String ck = "ck_335f44644d5d4e67b41b8380e2d8b99b"; // 天猫店铺：尔沫旗舰店
			// String ck = "ck_215260a379c0428dac33d63f9b0078c3"; // 淘宝店铺：贝壳家创意手工diy幼教益智美劳材料批发
			Cjzh cjzh2 = cookieStore.getCjzhForKey(ck);
			if (cjzh2 == null) {
				System.out.println("采集账号为空！！！");
				return;
			}
			
			String userid = String.valueOf(cjzh2.getUserid());
			// 步骤一：工作时间判断，是否已采集完成，是否已经有锁
			/*if (!timeCheck()) { // 9点之后，每天仅采集，当天的数据
				// 在非工作时间段
				return;
			}*/
			String oss_keyprefix = "tx_dp/sp_new";
			String rq = BiDateUtil.format2(new Date()); // yyyyMMdd
			String rq2 = BiDateUtil.format(new Date()); // yyyy-MM-dd
			String key = oss_keyprefix + "/" + rq + "/" + userid + ".json";
			if (biDataOss.existAuth(key)) {
				return;
			}
			lockObj = distributedLock.lockNoWait("zdcj8_sycm_splb_new_cj_" + userid, 60 * 20); // 生意参谋 -> 商品列表，上新采集，锁20分钟
			if (Objects.isNull(lockObj)) {
				return;
			}
			// 步骤二：获取店铺子账号的渠道（天猫、淘宝）（2种方式）
			// 方法1：访问https://myseller.taobao.com/home.htm解析domain:parseConfig里面内容，属性pcUserDomain值：tmallseller是天猫店、taobaoseller是淘宝店
			// 方法2：接口api: mtop.taobao.jdy.resource.shop.info.get，解析返回值属性tmallSeller值：true是天猫店、false是淘宝店
			SycmSplbHttpClient.shopInfo(cjzh2);
			
			
			// 步骤三：采集商品列表
			JSONArray spArr = new JSONArray();
			boolean result = SycmSplbHttpClient.getSplb(cjzh2, spArr, rq2);
            if (!result) {
            	log.info("-------------------************* sycmSplb店铺新品采集失败，子账号：" + cjzh2.getZzh() + "，ck：" + cjzh2.getCookieKey() + "，店铺userid：" + userid + "，是否天猫店：" + cjzh2.isB2c());
                return;
            }
			// 保存（采集成功就保存）
 			ossUploader.upload(key, gson.toJson(spArr));
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			e.printStackTrace();
		} finally {
			distributedLock.unlock(lockObj);
		}
	}
	
	/**
	 * timeCheck 采集时间确认
	 * 
	 * @说明：晚上8点45之后，9点左右
	 */
	@SuppressWarnings("unused")
	private boolean timeCheck() {
		boolean result = false;
		try {
			long time_start = 20 * 3600 * 1000L + 2700 * 1000L; // 晚上8点45之后，9点左右
			// 时间段判断
			long millseconds = (System.currentTimeMillis() - SycmConst.MIN_MILLISECOND) % (24 * 3600 * 1000);
			if (millseconds < time_start) {
				// 在非工作时间段
				result = false;
			} else {
				result = true;
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return result;
	}

	// main方法
	public static void main(String[] args) {
		try {
			String upShelfDate_m = "2024-08-23 09:56";
			System.out.println(upShelfDate_m.substring(0, 10));
			System.out.println(upShelfDate_m.substring(0, 10).compareTo("2024-08-24"));
			System.out.println(upShelfDate_m.substring(0, 10).compareTo("2024-08-23"));
			System.out.println(upShelfDate_m.substring(0, 10).compareTo("2024-08-22"));
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			e.printStackTrace();
		}
	}
}
