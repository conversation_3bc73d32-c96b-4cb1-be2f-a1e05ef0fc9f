package com.abujlb.zdcj8.test;

import com.abujlb.BaseTest;
import com.abujlb.ckstore.AbujlbCookieStore;
import com.abujlb.ckstore.Cjzh;
import com.abujlb.jyrb.bean.Dp;
import com.abujlb.jyrb.bean.JysjMsg;
import com.abujlb.jyrb.bean.JysjRq;
import com.abujlb.jyrb.http.WxtwujHyrqbdHttpClient;
import com.abujlb.jyrb.http.ZtcHyrqbdHttpClient;
import com.abujlb.jyrb.http.ZtcStateHttpClient;
import com.abujlb.jyrb.sjcj.aizt.AiztAuth;
import com.abujlb.util.DateUtil;
import com.abujlb.util.StringUtil;
import com.abujlb.zdcj8.auth.BiAuth;
import com.abujlb.zdcj8.auth.BiAuthResult;
import com.abujlb.zdcj8.script.ZtcHashJSEngine;
import com.abujlb.zdcj8.service.HyrqbdService;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/7/12 11:01
 **/
public class TestZtcHyrqbd extends BaseTest {

    @Autowired
    private AbujlbCookieStore cookieStore;

    @Autowired
    private HyrqbdService hyrqbdService;

    @Test
    public void test() {

        String catId = "122852001";
        String ck = "ck_9c632e5dc1444b7db3ce5a2c8b88a562";
        Cjzh cjzh = cookieStore.getCjzhForKey(ck);
        boolean result = ZtcStateHttpClient.ztc(cjzh);
        if (!result) {
            return;
        }

        JSONArray itemList = ZtcHyrqbdHttpClient.queryForRpt(cjzh);
        for (int i = 0; i < itemList.size(); i++) {
            JSONObject jsonObject = itemList.getJSONObject(i);
            String categoryIdList = jsonObject.getJSONObject("extraAttributes").getString("categoryId");
            if (StringUtils.isNotBlank(categoryIdList) && categoryIdList.contains(catId)) {
                //执行代码逻辑
                String itemId = jsonObject.getString("itemId");
                List<String> itemIdList = Arrays.asList(itemId);
                JSONArray dyList = ZtcHyrqbdHttpClient.dyList(cjzh,itemIdList);
                if (CollectionUtils.isEmpty(dyList)) {
                    continue;
                }
                JSONObject dyJsonObject = dyList.getJSONObject(0);
                String adgroupId = dyJsonObject.getString("adgroupId");
                if ("3860704575".equals(adgroupId)){
                    String campaignId = dyJsonObject.getString("campaignId");
                    JSONArray recSceneEffect = ZtcHyrqbdHttpClient.hyrqbd(cjzh, "recSceneEffect", adgroupId, campaignId, itemId);
                    JSONArray recSceneHeat = ZtcHyrqbdHttpClient.hyrqbd(cjzh, "recSceneHeat", adgroupId, campaignId, itemId);
                    JSONArray cjbd = ZtcHyrqbdHttpClient.cjbd(cjzh, adgroupId, campaignId, itemId);
                    System.out.println();
                }
            }
            System.out.println();
        }

        String rq = "2023-07-13";
        JSONArray bztg = ZtcHyrqbdHttpClient.allBztg(cjzh, rq);
        for (int i = 0; i < bztg.size(); i++) {
            JSONObject bztgJsonObject = bztg.getJSONObject(i);
            String campaignId = bztgJsonObject.getString("id");
            JSONArray tgdy = ZtcHyrqbdHttpClient.allTgdy(cjzh, rq, campaignId);
            for (int j = 0; j < tgdy.size(); j++) {
                JSONObject tgdyJsonObject = tgdy.getJSONObject(j);
                String adgroupId = tgdyJsonObject.getJSONObject("adGroupDTO").getString("adGroupId");
                String itemId = tgdyJsonObject.getJSONObject("itemIndicatorInfo").getString("itemId");
                JSONArray recSceneEffect = ZtcHyrqbdHttpClient.hyrqbd(cjzh, "recSceneEffect", adgroupId, campaignId, itemId);
                JSONArray recSceneHeat = ZtcHyrqbdHttpClient.hyrqbd(cjzh, "recSceneHeat", adgroupId, campaignId, itemId);
                JSONArray cjbd = ZtcHyrqbdHttpClient.cjbd(cjzh, adgroupId, campaignId, itemId);
                System.out.println();
            }
            System.out.println();
        }
//        JSONArray recSceneEffect = ZtcHyrqbdHttpClient.hyrqbd(cjzh, "recSceneEffect", "3860704575", "232259102", "690761381990");
//        JSONArray recSceneHeat = ZtcHyrqbdHttpClient.hyrqbd(cjzh, "recSceneHeat", "3860704575", "232259102", "690761381990");
//        JSONArray cjbd = ZtcHyrqbdHttpClient.cjbd(cjzh,"3860704575", "232259102","690761381990");
    }

    @Test
    public void WxtwujHyrqbdHttpClient() {
        String ck = "ck_3b75b4a7d1154e919f60b86982f279af";
        Cjzh cjzh = cookieStore.getCjzhForKey(ck);
//        boolean result = ZtcStateHttpClient.ztc(cjzh);
//        if (!result) {
//            return;
//        }
//        csrfId
        BiAuthResult biAuthResult = BiAuth.adbrainOne(cjzh);
        if (biAuthResult.getCode() != 1) {
            return;
        }
        JSONArray jh = WxtwujHyrqbdHttpClient.jh(cjzh, "2023-07-13");
        for (int i = 0; i < jh.size(); i++) {
            JSONObject jsonObject = jh.getJSONObject(i);
            String campaignId = jsonObject.getString("campaignId");
        }
    }

    @Test
    public void testP() {
        JysjMsg jysjMsg = new JysjMsg();
        jysjMsg.setCateId("122852001");
        hyrqbdService.process();
    }
}
