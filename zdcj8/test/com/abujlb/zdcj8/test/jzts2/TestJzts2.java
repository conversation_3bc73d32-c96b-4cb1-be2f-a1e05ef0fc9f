package com.abujlb.zdcj8.test.jzts2;

import org.junit.Test;

import com.abujlb.BaseTest;
import com.abujlb.Result;
import com.abujlb.jzts2.pojo.JztsPara;
import com.abujlb.util.StringUtil;

public class TestJzts2 extends BaseTest {

	private static final String APPID = "zdcj";
	private static final String APPKEY = "123123132132132asdfasdf";

	@Test
	public void start() {
		JztsPara para = new JztsPara();
		para.setLx(JztsPara.LX_SP);
		// para.setItemid("547223162754");
		// para.setItemid("680337440003");
		// para.setItemid("677308073232");
		para.setItemid("756141847235");
		para.setCateid("1512");
		String s = this.service("/jzts2/start", APPID, APPKEY, para);
		System.out.println(s);
		String key = Result.getValueFromJson(s, "key", String.class);
		System.out.println(key);
		for (int i = 0; i < 35; i++) {
			System.out.println(this.service("/jzts2/progress", APPID, APPKEY, key));
			try {
				Thread.sleep(1000L);
			} catch (InterruptedException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
	}

	@Test
	public void start2() {
		JztsPara para = new JztsPara();
		para.setLx(JztsPara.LX_DP);
//		para.setUserid("2206480333386");
//		para.setZhuzh("牛小莫旗舰店");
//		para.setLtly(true);
//		para.setCatelist(new String[] { "16" });
		
		para.setUserid("2214553336985");
		para.setZhuzh("勇敢牛牛的潮鞋");
		para.setCatelist(new String[] { "50011740"});
		
		String s = this.service("/jzts2/start", APPID, APPKEY, para);
		System.out.println(s);
		String key = Result.getValueFromJson(s, "key", String.class);
		System.out.println(key);
		for (int i = 0; i < 100; i++) {
			System.out.println(this.service("/jzts2/progress", APPID, APPKEY, key));
			try {
				Thread.sleep(1000L);
			} catch (InterruptedException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
	}

	@Test
	public void progress() {

	}
	
	@Test
	public void test3() {
		JztsPara para = new JztsPara();
		para.setLx(JztsPara.LX_DP);
		para.setLtly(true);
		para.setUserid("2138614417");
		para.setZhuzh("协创优致办公专营店");
		para.setCatelist(new String[] { "50018004", "50007218", "98" });
		String s = this.service("/jzts2/start", APPID, APPKEY, para);
		System.out.println(s);
		String key = Result.getValueFromJson(s, "key", String.class);
		System.out.println(key);
		if (StringUtil.isNull2(key)) {
			System.out.println("没有key" + key);
			return;
		}
		for (int i = 0; i < 65; i++) {
			s = this.service("/jzts2/progress", APPID, APPKEY, key);
			System.out.println(s);
			try {
				Thread.sleep(1000L);
			} catch (InterruptedException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
	}
}
