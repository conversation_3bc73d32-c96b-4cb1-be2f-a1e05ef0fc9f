package com.abujlb.zdcj8.test.jzts2;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.abujlb.BaseTest;
import com.abujlb.ckstore.AbujlbCookieStore;
import com.abujlb.ckstore.ZzhZxsl;
import com.google.gson.Gson;

public class TestZxzhs extends BaseTest {
	@Autowired
	private AbujlbCookieStore cookieStore;

	@Test
	public void test() {
		ZzhZxsl zxsl = cookieStore.zxzhs("16");
		Gson gson = new Gson();
		System.out.println(gson.toJson(zxsl));
	}
}
