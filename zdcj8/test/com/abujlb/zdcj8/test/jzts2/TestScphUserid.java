package com.abujlb.zdcj8.test.jzts2;

import org.apache.log4j.Logger;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.abujlb.BaseTest;
import com.abujlb.jzts2.tsdao.ScphUseridTsDao;

/**
 * test单元测试：表格存储
 * 
 * <AUTHOR>
 * @date 2023-04-13 19:37:29
 */
public class TestScphUserid extends BaseTest {

	private static Logger log = Logger.getLogger(TestScphUserid.class);

	@Autowired
	private ScphUseridTsDao scphUseridTsDao;

	@Test
	public void test() {
		String userid = "57683301";
		try {
			System.out.println(scphUseridTsDao.termQuery(userid));
			System.out.println(scphUseridTsDao.getIndexRows(userid));
			System.out.println("RAzN8HAYcXpVBpAZgHEbU8PJn1RBf");
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			e.printStackTrace();
		}
	}

}
