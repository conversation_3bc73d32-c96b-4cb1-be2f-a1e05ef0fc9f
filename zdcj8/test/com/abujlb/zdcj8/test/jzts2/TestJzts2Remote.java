package com.abujlb.zdcj8.test.jzts2;

import org.junit.Test;

import com.abujlb.BaseTest;
import com.abujlb.Result;
import com.abujlb.jzts2.pojo.JztsPara;
import com.abujlb.service.ServiceClient;
import com.abujlb.util.StringUtil;

public class TestJzts2Remote extends BaseTest {
	// private static final String startUrl = "http://************/jzts2/start";
	// private static final String progressUrl = "http://************/jzts2/progress";
	
	// 2024-07-02调整：腾讯云服务器：************，调整IP：*************
	private static final String startUrl = "http://*************/jzts2/start";
	private static final String progressUrl = "http://*************/jzts2/progress";
	private static final String APPID = "ts";
	private static final String APPKEY = "lkjoiuwqerlkji987234";

	@Test
	public void test() {
		ServiceClient sc = new ServiceClient(APPID, APPKEY);

		JztsPara para = new JztsPara();
		para.setLx(JztsPara.LX_SP);
		para.setItemid("756141847235");
		para.setCateid("1512");
		String s = sc.exec(startUrl, para);
		System.out.println(s);
		String key = Result.getValueFromJson(s, "key", String.class);
		System.out.println(key);
		for (int i = 0; i < 35; i++) {
			s = sc.exec(progressUrl, key);
			System.out.println(s);
			try {
				Thread.sleep(1000L);
			} catch (InterruptedException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
	}

	@Test
	public void test2() {
		ServiceClient sc = new ServiceClient(APPID, APPKEY);
		JztsPara para = new JztsPara();
		para.setLx(JztsPara.LX_DP);
		para.setUserid("3029164940");
		para.setZhuzh("蓝铃旗舰店");
		para.setCatelist(new String[] { "50016348", "122928002", "50016349" });
		String s = sc.exec(startUrl, para);
		System.out.println(s);
		String key = Result.getValueFromJson(s, "key", String.class);
		System.out.println(key);
		for (int i = 0; i < 100; i++) {
			s = sc.exec(progressUrl, key);
			System.out.println(s);
			try {
				Thread.sleep(1000L);
			} catch (InterruptedException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
	}
	
	@Test
	public void test3() {
		ServiceClient sc = new ServiceClient(APPID, APPKEY);
		JztsPara para = new JztsPara();
		para.setLx(JztsPara.LX_DP);
		// para.setLtly(true);
		para.setUserid("2214553336985");
		para.setZhuzh("勇敢牛牛的潮鞋");
		// para.setCatelist(new String[] { "50012029", "50011740"});
		// para.setCatelist(new String[] { "50012029"});
		para.setCatelist(new String[] { "50011740"});
		String s = sc.exec(startUrl, para);
		System.out.println(s);
		String key = Result.getValueFromJson(s, "key", String.class);
		System.out.println(key);
		if (StringUtil.isNull2(key)) {
			System.out.println("没有key" + key);
			return;
		}
		for (int i = 0; i < 100; i++) {
			s = sc.exec(progressUrl, key);
			System.out.println(s);
			try {
				Thread.sleep(2000L);
			} catch (InterruptedException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
	}
	
	@Test
	public void test4() {
		ServiceClient sc = new ServiceClient(APPID, APPKEY);
		String key = "8000557a686741fda0c63ff896945bbb";
		for (int i = 0; i < 100; i++) {
			String s = sc.exec(progressUrl, key);
			System.out.println(s);
			try {
				Thread.sleep(1000L);
			} catch (InterruptedException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
	}
}
