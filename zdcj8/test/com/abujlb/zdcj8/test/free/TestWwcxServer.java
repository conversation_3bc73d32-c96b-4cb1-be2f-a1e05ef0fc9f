package com.abujlb.zdcj8.test.free;

import java.net.URLEncoder;
import java.util.ArrayList;

import javax.net.ssl.SSLContext;

import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.config.RequestConfig.Builder;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.util.EntityUtils;
import org.apache.log4j.Logger;

import com.abujlb.BaseTest;
import com.abujlb.util.AbujlbTrustStrategy;

/**
 * 单元测试：测试服务
 * 
 * <AUTHOR>
 * @date 2020-11-09 11:47:05
 */
public class TestWwcxServer extends BaseTest {

	private static Logger log = Logger.getLogger(TestWwcxServer.class);
	
	/**
	 * 店铺上线查询接口
	 * 
	 */
	private static String wwcx(String staffNick, String buyerNick) {
		String url = "https://acs.m.taobao.com/gw/mtop.taobao.qianniu.airisland.noah.protocol.getbycardkeys/1.0/?&type=originaljson";
		SSLContext sslContext;
		HttpPost httpPost = null;
		HttpResponse httpResponse;
		try {
			// Gson gson = new Gson();
			String data = "{\"staffNick\":\"" + staffNick + "\",\"buyerNick\":\"" + buyerNick + "\",\"cardConfigKeys\":\"[\\\"official_user_module_card@0\\\"]\",\"bizCtx\":\"{}\"}";

			sslContext = new SSLContextBuilder().loadTrustMaterial(null, new AbujlbTrustStrategy()).build();
			SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslContext);
			CloseableHttpClient httpClient = HttpClients.custom().setSSLSocketFactory(sslsf).build();

			httpPost = new HttpPost(url);
			// httpPost.setHeader("accept", "application/json");
			httpPost.setHeader("accept-encoding", "gzip,deflate");
			httpPost.setHeader("host", "acs.m.taobao.com");
			// httpPost.setHeader("accept-language", "zh-CN,zh;q=0.9");
			httpPost.setHeader("content-type", "application/x-www-form-urlencoded;charset=utf-8");
			// httpPost.setHeader("origin", "https://acs.m.taobao.com");
			// httpPost.setHeader("referer", "https://acs.m.taobao.com/");
			
			httpPost.setHeader("a-orange-q", "appKey=23524755&appVersion=7.21.11N&clientAppIndexVersion=1120201102111004080&clientVersionIndexVersion=0");
			httpPost.setHeader("x-act", "50008600326bgEoaPUMkSDJlqZNPiZcGTkHPex14a8279awkYkdrCkuVcfVwEYnPFx");
			httpPost.setHeader("x-appkey", "23524755");
			httpPost.setHeader("x-devid", "7a9ac5e15f8e165313d71b51173f14e4");
			httpPost.setHeader("x-miniapp-id-taobao", "3000000002019401");
			httpPost.setHeader("x-mini-appkey", "23436601");
			httpPost.setHeader("x-open-biz", "mini-app");
			httpPost.setHeader("x-open-biz-data", URLEncoder.encode("{\"appId\":\"3000000002019401\",\"invokerAppId\":\"3000000002019401\",\"viaFusionApp\":true}", "UTF-8"));
			httpPost.setHeader("x-pv", "6.2");
			httpPost.setHeader("x-req-appkey", "23436601");
			httpPost.setHeader("x-reqbiz-ext", "");
			httpPost.setHeader("x-sid", "********************************");
			// httpPost.setHeader("x-sign", "wb0001001032308e37cad98f7951434b9d5a779e5c6c754d9f"); // 签名
			httpPost.setHeader("x-sign", "f5eaf56300afb94c4434fcbf68fe7ab0"); // 签名
			// httpPost.setHeader("x-sign", "wb00010010d23f6349ac5fd1cd5bc5a2c976f210d0b65936d3"); // 签名
			httpPost.setHeader("x-t", "1604915184"); // 改变，时间戳
			httpPost.setHeader("x-ttid", "23524755%40tbsellerworkbench_pc_7.21.11N");
			httpPost.setHeader("x-utdid", "28515978965b16411a7f50cd");
			
			httpPost.setHeader("user-agent", "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/65.0.3325.181 Safari/537.36");
			Builder requestConfig = RequestConfig.custom().setSocketTimeout(60000).setConnectTimeout(60000).setConnectionRequestTimeout(60000);
			httpPost.setConfig(requestConfig.build());
			
			ArrayList<BasicNameValuePair> pairs = new ArrayList<BasicNameValuePair>();
			pairs.add(new BasicNameValuePair("data", URLEncoder.encode(data, "UTF-8")));

			UrlEncodedFormEntity urlEncodedFormEntity = new UrlEncodedFormEntity(pairs, "utf-8");
			httpPost.setEntity(urlEncodedFormEntity);
			httpResponse = httpClient.execute(httpPost);
			HttpEntity entity = httpResponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			
			// JSONObject jsonObj = JSONObject.fromObject(body);
			System.out.println(body);
			return body;
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		} finally {
			if (httpPost != null) {
				httpPost.releaseConnection();
			}
		}
		return null;
	}
	
	// main方法
	public static void main(String[] args) {
		System.out.println(wwcx("尔沫旗舰店:俱乐部老师", "ji8李龙"));
	}
	
}
