package com.abujlb.zdcj8.test.free;

import java.net.URLEncoder;

import org.apache.log4j.Logger;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.abujlb.BaseTest;
import com.abujlb.Result;
import com.abujlb.util.SHA1;
import com.abujlb.zdcj8.bean.DpsxPara;
import com.abujlb.zdcj8.script.DpsxSignJSEngine;
import com.abujlb.zdcj8.service.FreeService;

/**
 * 单元测试：测试签名sign.js
 * 
 * <AUTHOR>
 * @date 2020-11-05 18:35:24
 */
public class TestJs extends BaseTest {

	private static Logger log = Logger.getLogger(TestJs.class);
	
	@Autowired
	private DpsxSignJSEngine dpsxSignJSEngine;
	@Autowired
	private FreeService freeService;
	
	/**
	 * 签名测试
	 * 
	 */
	@Test
	public void test() {
		try {
			String sign = "0cf8159ff155f3d13d505c04e4edfa90";
			System.out.println(sign);
			String timestamp = "1604561861723";
			String appkey = "12574478";
			String data = "{\"userId\":2616970884,\"lastTime\":0,\"tab\":0}";
			String token = "039e5f0bc983053f6df5bed8c15898ae_1604570141633".split(";")[0].split("_")[0];
			System.out.println("token = " + token);
			System.out.println(dpsxSignJSEngine.getSign(timestamp, appkey, data, token));
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		}
	}
	
	/**
	 * 店铺上新测试
	 * 
	 */
	@Test
	public void testDpsx() {
		try {
			DpsxPara dpsxPara = new DpsxPara("2067331160", 0);
			Result result = freeService.dpsx(dpsxPara);
			System.out.println(result.toString());
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		}
	}
	
	/**
	 * 签名测试
	 * 
	 */
	@Test
	public void test3() {
		try {
			String x_sign = "wb00010010" + "32308e37cad98f7951434b9d5a779e5c6c754d9f";
			System.out.println(x_sign);
			// String timestamp = "1604892770";
			String timestamp = String.valueOf(System.currentTimeMillis()/1000);
			String appkey = "23524755";
			String data = "{\"staffNick\":\"尔沫旗舰店:俱乐部老师\",\"buyerNick\":\"ji8李龙\",\"cardConfigKeys\":\"[\\\"official_user_module_card@0\\\"]\",\"bizCtx\":\"{}\"}";
			String token = "13435323996cf2413d8cebd553ec1723";
			System.out.println("token = " + token);
			System.out.println("timestamp = " + timestamp);
			System.out.println(dpsxSignJSEngine.getSign(timestamp, appkey, data, token));
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		}
	}
	
	@SuppressWarnings("unused")
	public static void main(String[] args) {
		/*String x_sign = "wb00010010" + "32308e37cad98f7951434b9d5a779e5c6c754d9f";
		String timestamp = "1604892770";
		String appkey = "23524755";
		String data = "{\"staffNick\":\"尔沫旗舰店:俱乐部老师\",\"buyerNick\":\"ji8李龙\",\"cardConfigKeys\":\"[\\\"official_user_module_card@0\\\"]\",\"bizCtx\":\"{}\"}";
		String token = "13435323996cf2413d8cebd553ec1723";
		// System.out.println(SHA1.password((token + timestamp + appkey + data)).toLowerCase());
		System.out.println(SHA1.password((timestamp + appkey + data + token)).toLowerCase());
		System.out.println(SHA1.password((data + "mtop.taobao.qianniu.airisland.noah.protocol.getbycardkeys" + "1.0" + "originaljson")).toLowerCase());
		System.out.println(SHA1.password(("32308e37cad98f7951434b9d5a779e5c6c754d9f")).toLowerCase());
		// return crateSign(token + "&" + s + "&" + a + "&" + data);
*/		
		
		String app = "eyJjYXJyaWVyIjoi55S15L+hIiwiY2xpZW50SXNwIjoiMTAwMDE3IiwiY29kZSI6MTAwMCwiY3YiOjEsImRucyI6W3siYWlzbGVzIjpbeyJjdG8iOjEwMDAwLCJoZWFydGJlYXQiOjAsInBvcnQiOjQ0MywicHJvdG9jb2wiOiJodHRwcyIsInJldHJ5IjoxLCJydG8iOjEwMDAwfSx7ImN0byI6MTAwMDAsImhlYXJ0YmVhdCI6MCwicG9ydCI6ODAsInByb3RvY29sIjoiaHR0cCIsInJldHJ5IjoxLCJydG8iOjEwMDAwfV0sImhvc3QiOiJ1bnN6YXBpLm0ueWFvLjk1MDk1LmNvbSIsImlwcyI6WyIxMjAuNzcuMTM0LjI1MyJdLCJpc0hvdCI6MSwic3RyYXRlZ2llcyI6W10sInR0bCI6MzAwfSx7ImFpc2xlcyI6W3siY3RvIjoxMDAwMCwiaGVhcnRiZWF0Ijo0NTAwMCwicG9ydCI6NDQzLCJwcm90b2NvbCI6Imh0dHAyIiwicHVibGlja2V5IjoiYWNzIiwicmV0cnkiOjEsInJ0byI6MTAwMDAsInJ0dCI6IjBydHQifSx7ImN0byI6MTAwMDAsImhlYXJ0YmVhdCI6NDUwMDAsInBvcnQiOjgwLCJwcm90b2NvbCI6Imh0dHAyIiwicHVibGlja2V5IjoiYWNzIiwicmV0cnkiOjEsInJ0byI6MTAwMDAsInJ0dCI6IjBydHQifSx7ImN0byI6MTAwMDAsImhlYXJ0YmVhdCI6NDUwMDAsInBvcnQiOjQ0MywicHJvdG9jb2wiOiJzcGR5IiwicHVibGlja2V5IjoiYWNzIiwicmV0cnkiOjEsInJ0byI6MTAwMDAsInJ0dCI6IjBydHQifSx7ImN0byI6MTAwMDAsImhlYXJ0YmVhdCI6NDUwMDAsInBvcnQiOjgwLCJwcm90b2NvbCI6InNwZHkiLCJwdWJsaWNrZXkiOiJhY3MiLCJyZXRyeSI6MSwicnRvIjoxMDAwMCwicnR0IjoiMHJ0dCJ9LHsiY3RvIjoxMDAwMCwiaGVhcnRiZWF0IjowLCJwb3J0Ijo0NDMsInByb3RvY29sIjoiaHR0cHMiLCJyZXRyeSI6MSwicnRvIjoxMDAwMH1dLCJob3N0IjoiYWNzLm0udGFvYmFvLmNvbSIsImlwcyI6WyIyMDMuMTE5LjE0NC42MyIsIjIwMy4xMTkuMTc1LjI1NSJdLCJpc0hvdCI6MSwic2FmZUFpc2xlcyI6Imh0dHBzIiwic3RyYXRlZ2llcyI6W10sInR0bCI6MzAwLCJ2ZXJzaW9uIjoiMCJ9LHsiYWlzbGVzIjpbeyJjdG8iOjEwMDAwLCJoZWFydGJlYXQiOjAsInBvcnQiOjQ0MywicHJvdG9jb2wiOiJodHRwcyIsInJldHJ5IjoxLCJydG8iOjEwMDAwfSx7ImN0byI6MTAwMDAsImhlYXJ0YmVhdCI6MCwicG9ydCI6ODAsInByb3RvY29sIjoiaHR0cCIsInJldHJ5IjoxLCJydG8iOjEwMDAwfV0sImhvc3QiOiJhcGkubS55YW8uOTUwOTUuY29tIiwiaXBzIjpbIjIwMy4xMTkuMTY5LjkiXSwiaXNIb3QiOjEsInN0cmF0ZWdpZXMiOltdLCJ0dGwiOjMwMH1dLCJpcCI6IjYwLjE2Ni44NC45NCJ9";
		String x_sign = "wb00010010" + "6e9f717a19f1b4e73a2a010eaccc646aa1357845";
		String timestamp = "1604916797";
		String appkey = "23524755";
		String x_devid = "7a9ac5e15f8e165313d71b51173f14e4";
		String x_pv = "6.2";
		String data = "{\"userId\":2851597896,\"tokenInfo\":\"{\\n    \\\"appName\\\": \\\"24585574\\\",\\n    \\\"tokenType\\\": \\\"\\\",\\n    \\\"token\\\": \\\"1_idc_1_9c271a5f31a276c76efc8b60a75ead5784e9dcfbe6bfb406d9af51efc066a3abcfa20801f8b4459332eb9e84db2560e820420d57eb5eb6f414f44d6fa79735b2\\\",\\n    \\\"scene\\\": \\\"\\\",\\n    \\\"t\\\": 1604916796,\\n    \\\"sdkVersion\\\": \\\"winpc_2.1.0.0\\\",\\n    \\\"appVersion\\\": \\\"7.21.11N\\\",\\n    \\\"site\\\": 0,\\n    \\\"locale\\\": \\\"zh_CN\\\",\\n    \\\"utdid\\\": \\\"pc_qianniu7.21.11N\\\",\\n    \\\"ext\\\": {}\\n}\",\"riskControlInfo\":\"{\\\"osVersion\\\":\\\"winpc\\\",\\\"umidToken\\\":\\\"ENCODE_3_000000000000000000000000000000_1D4E753C432E0BB987FD06E5C4B6053D208D932E9D6554DAD430FAA1257AB25CD5B3F846DA4B499A455180953970BBE07534FFC598FF376D70A5448A596F0BCF1EC7C1DA841DFED62812703920C576D06E687B2BD4F82EA91195C949CBA2D290D76DF311391300AA49EB8A2C6B8C50C628995256ECB93A1C5C9F8C218097929DB4B8846322006F8063F591077B7769EA36878BB5B3C2450397740F5B68023533EDBE78A52E630EC012174F1A0D1F6A75EFF5FFAD036BCB780E0A6E0E688AC5B01EA7D973081AFDE6\\\"}\",\"ext\":\"{}\"}";
		String x_ttid = "23524755@tbsellerworkbench_pc_7.21.11N";
		// String token = "13435323996cf2413d8cebd553ec1723";
		System.out.println(SHA1.password((timestamp + appkey + data + "wb00010010")).toLowerCase());
		
		
		
		
		String token = "2856604JZ6ODU5NTE=79S";
		System.out.println(token.replace("=", "%3D"));
		
		try {
			System.out.println(URLEncoder.encode("尔沫旗舰店", "UTF-8"));
		} catch (Exception e) {
			e.getSuppressed();
		}
	}
	
}
