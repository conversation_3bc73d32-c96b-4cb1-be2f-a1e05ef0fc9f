package com.abujlb.zdcj8.test.free;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;

import com.abujlb.util.Md5;

/**
* <AUTHOR>
* @date 2020-11-23
*/
public class Test1 {

	@SuppressWarnings("unused")
	private  static String request(String orderId) throws UnsupportedEncodingException {
		long t =  System.currentTimeMillis() ;
		String data ="{\"orderId\":\""+orderId+"\"}" ;
		CloseableHttpClient client = HttpClients.createDefault();
		String entityStr = null;
		CloseableHttpResponse response = null;
		String sign = createTaobaoRequestSign("484f3f2591fc475493989f96034db5ba", "2.4.11", t, data);
		String url ="https://h5api.m.taobao.com/h5/mtop.com.alibaba.cco.ssc.complaint.renderrate/1.0/?jsv=2.4.11&appKey=12574478&t="+t+"&sign="+sign+"&v=1.0&dataType=json&api=mtop.com.alibaba.cco.ssc.complaint.renderRate&type=originaljson&data="+URLEncoder.encode(data, "utf-8");
		try {
				URIBuilder uriBuilder = new URIBuilder(url);
				uriBuilder.addParameter("jsv","2.4.11");
				uriBuilder.addParameter("appKey","12574478");
				uriBuilder.addParameter("t",t+"");
				uriBuilder.addParameter("sign",sign);
				uriBuilder.addParameter("api", "mtop.com.alibaba.cco.ssc.complaint.renderRate");
				uriBuilder.addParameter("v", "1.0");
				uriBuilder.addParameter("dataType", "json");
				uriBuilder.addParameter("type","originaljson");
				uriBuilder.addParameter("data", "{\"orderId\":\""+orderId+"\"}");

				HttpGet get = new HttpGet(uriBuilder.build());
				get.addHeader(":authority", "h5api.m.taobao.com");
				get.addHeader(":method", "GET");
				get.addHeader("cookie", "cookie2=1d96f0d6a10685d25c8a2ab324823264; t=43cf00bbf6ad9690fab73180962807e5; _tb_token_=e7b6d10beebd3; _samesite_flag_=true; xlly_s=1; thw=cn; enc=**************************%2Fd%2F1nDpSaPRGJowJMJOcCaLLtDDMmbbRx7MBBXJfqAX9eb6LrVf3O12bpa2A%3D%3D; _m_h5_tk=484f3f2591fc475493989f96034db5ba_1606129349740; _m_h5_tk_enc=00c1d5faf474946e449df7517f57bcfa; unb=2590426692; sn=qtu%E6%97%97%E8%88%B0%E5%BA%97%3A%E9%98%BF%E4%B8%8D%E4%BF%B1%E4%B9%90%E9%83%A8%E5%90%8E%E7%AB%AF%E6%9C%8D%E5%8A%A1; csg=5526ae30; skt=59634c5a05aace9e; _cc_=W5iHLLyFfA%3D%3D; cna=MfFBGEhool8CAXJmtdbXnziR; uc1=cookie14=Uoe0azfI3X84MA%3D%3D&cookie21=UtASsssmfA%3D%3D; isg=BAEBfJNuOut1k1bfqb5OHvsrBU0bLnUgvtJpfWNW_YhnSiEcq36F8C9ILL4M2Q1Y; l=eBj-Al-POlqb1RDEBOfanurza77OSLAYYuPzaNbMiOCP_kCB5G-hWZ7MGWL6C3GVh6WDR3yKvsqwBeYBqQAonxv9Iugcw_Hmn; tfstk=caLCByq6TJ2CQ5oz8BGNTtyNqQQRwXM1sD6pOHCvEPYFqt1msrWDbK-uITk11; v=0");
				get.addHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/70.0.3538.25 Safari/537.36 Core/1.70.3823.400 QQBrowser/10.7.4307.400");
				response = client.execute(get);
				HttpEntity entity = response.getEntity();
				entityStr = EntityUtils.toString(entity, "UTF-8");
				System.out.println(entityStr);
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (response != null) {
				try {
					response.close();
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
			if (client != null) {
				try {
					client.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
		return entityStr;
	}
	
	public static String createTaobaoRequestSign(String token, String appkey, long timestamp, String data) {
		return Md5.md5(token + "&" + timestamp + "&" + appkey + "&" + data);
	}
	
	public static void main(String[] args) throws IOException {
//		request("1204216013573760232");
		Document doc = Jsoup.connect("https://item.taobao.com/item.htm?id=" + "632844247638").get();
		String html = doc.toString();
		if (html.indexOf("categoryId:") == -1) {
			System.out.println("null");
			return ;
		}
		html = html.substring(html.indexOf("categoryId:") + 11);
		String categoryId = html.substring(0, html.indexOf(","));
		System.out.println(categoryId);
	}
}
