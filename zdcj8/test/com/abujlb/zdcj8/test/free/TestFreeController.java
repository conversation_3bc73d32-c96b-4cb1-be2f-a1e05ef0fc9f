package com.abujlb.zdcj8.test.free;

import java.util.ArrayList;

import javax.net.ssl.SSLContext;

import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.util.EntityUtils;
import org.apache.log4j.Logger;

import com.abujlb.BaseTest;
import com.abujlb.util.AbujlbTrustStrategy;
import com.abujlb.util.Md5;
import com.google.gson.Gson;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

/**
 * 单元测试：测试服务
 * 
 * <AUTHOR>
 * @date 2020-11-06 09:26:17
 */
@SuppressWarnings("unused")
public class TestFreeController extends BaseTest {

	private static Logger log = Logger.getLogger(TestFreeController.class);
	
	private static final String server_url = "http://localhost:8082/zdcj8/";
	
	
	/**
	 * 宝贝类目查询接口
	 * √
	 */
	private static boolean bblm(String bbid) {
		String url = server_url + "free_bblm.action";
		SSLContext sslContext;
		HttpPost httpPost = null;
		HttpResponse httpResponse;
		try {
			Gson gson = new Gson();
			String stamp = String.valueOf(System.currentTimeMillis());
			String password = Md5.password(bbid + stamp);

			sslContext = new SSLContextBuilder().loadTrustMaterial(null, new AbujlbTrustStrategy()).build();
			SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslContext);
			CloseableHttpClient httpClient = HttpClients.custom().setSSLSocketFactory(sslsf).build();

			httpPost = new HttpPost(url);
			ArrayList<BasicNameValuePair> pairs = new ArrayList<BasicNameValuePair>();
			pairs.add(new BasicNameValuePair("data", bbid));
			pairs.add(new BasicNameValuePair("stamp", stamp));
			pairs.add(new BasicNameValuePair("password", password));

			UrlEncodedFormEntity urlEncodedFormEntity = new UrlEncodedFormEntity(pairs, "utf-8");
			httpPost.setEntity(urlEncodedFormEntity);
			httpResponse = httpClient.execute(httpPost);
			HttpEntity entity = httpResponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			System.out.println(body);
			JSONObject jsonObj = JSONObject.fromObject(body);
			int code = jsonObj.getInt("code");
			if (code == 0 && jsonObj.has("categoryId")) {
				String categoryId = jsonObj.getString("categoryId") ;
				System.out.println("categoryId===>"+categoryId);
			} 
			return true;
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		} finally {
			if (httpPost != null) {
				httpPost.releaseConnection();
			}
		}
		return false;
	}
	
	
	/**
	 * 无线详情查询接口
	 * 
	 */
	private static boolean wxxq(String bbid) {
		String url = server_url + "free_wxxq.action";
		SSLContext sslContext;
		HttpPost httpPost = null;
		HttpResponse httpResponse;
		try {
			Gson gson = new Gson();
			String stamp = String.valueOf(System.currentTimeMillis());
			String password = Md5.password(bbid + stamp);

			sslContext = new SSLContextBuilder().loadTrustMaterial(null, new AbujlbTrustStrategy()).build();
			SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslContext);
			CloseableHttpClient httpClient = HttpClients.custom().setSSLSocketFactory(sslsf).build();

			httpPost = new HttpPost(url);
			ArrayList<BasicNameValuePair> pairs = new ArrayList<BasicNameValuePair>();
			pairs.add(new BasicNameValuePair("data", bbid));
			pairs.add(new BasicNameValuePair("stamp", stamp));
			pairs.add(new BasicNameValuePair("password", password));

			UrlEncodedFormEntity urlEncodedFormEntity = new UrlEncodedFormEntity(pairs, "utf-8");
			httpPost.setEntity(urlEncodedFormEntity);
			httpResponse = httpClient.execute(httpPost);
			HttpEntity entity = httpResponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			System.out.println("bbid:"+bbid+",body===>"+body);
			JSONObject jsonObj = JSONObject.fromObject(body);
			int code = jsonObj.getInt("code");
			if (code == 0 && jsonObj.has("records")) {
				//业务逻辑
				JSONArray  jsonArray =JSONArray.fromObject( jsonObj.getString("records")) ;
//				for(int i =0 ;i<jsonArray.size() ;i++) {
//					String imgPath =jsonArray.getString(i);
//					System.out.println(imgPath);
//				}
				return true;
			} else {
//				System.out.println(body);
				return false ;
			}
		
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		} finally {
			if (httpPost != null) {
				httpPost.releaseConnection();
			}
		}
		return false;
	}
	
	/**
	 * 竞品规格销售比例 -生成接口
	 * 
	 */
	private static boolean jpggxsbl(String bbid) {
		String url = server_url + "free_jpggxsbl.action";
		SSLContext sslContext; 
		HttpPost httpPost = null;
		HttpResponse httpResponse;
		try {
			Gson gson = new Gson();
			String stamp = String.valueOf(System.currentTimeMillis());
			String password = Md5.password(bbid + stamp);

			sslContext = new SSLContextBuilder().loadTrustMaterial(null, new AbujlbTrustStrategy()).build();
			SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslContext);
			CloseableHttpClient httpClient = HttpClients.custom().setSSLSocketFactory(sslsf).build();

			httpPost = new HttpPost(url);
			ArrayList<BasicNameValuePair> pairs = new ArrayList<BasicNameValuePair>();
			pairs.add(new BasicNameValuePair("data", bbid));
			pairs.add(new BasicNameValuePair("stamp", stamp));
			pairs.add(new BasicNameValuePair("password", password));

			UrlEncodedFormEntity urlEncodedFormEntity = new UrlEncodedFormEntity(pairs, "utf-8");
			httpPost.setEntity(urlEncodedFormEntity);
			httpResponse = httpClient.execute(httpPost);
			HttpEntity entity = httpResponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			System.out.println(body);
			JSONObject jsonObj = JSONObject.fromObject(body);
			int code = jsonObj.getInt("code");
			if (code == 0 && jsonObj.has("record")) {
				//业务逻辑
				//其中id,nid 和uuid为详情接口查询参数
				JSONObject record = JSONObject.fromObject(jsonObj.getString("record")) ;
				//详情查询参数
				String id  = record.getString("id");
				//详情查询参数
				String nid  = record.getString("nid");
				//详情查询参数
				String uuid  = record.getString("uuid");
				//创建时间
				String cjsj  = record.getString("cjsj");
				System.out.println(id+"===>"+nid+"===>"+uuid+"===>"+cjsj);
			} else {
				System.out.println(body);
			}
			return true;
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		} finally {
			if (httpPost != null) {
				httpPost.releaseConnection();
			}
		}
		return false;
	}
	
	/**
	 * 竞品规格销售比例详情查询接口
	 * @param bbid
	 * @param nid
	 * @param uuid 
	 * @param num 页码
	 * @param id
	 */
	private static boolean jpggxsblxq(String nid , String uuid , int num,String id) {
		String url = server_url + "free_jpggxsblxq.action";
		SSLContext sslContext;
		HttpPost httpPost = null;
		HttpResponse httpResponse;
		try {
			Gson gson = new Gson();
			String stamp = String.valueOf(System.currentTimeMillis());
			String json = "{\"id\":\""+id+"\",\"nid\":\""+nid+"\",\"uuid\":\""+uuid+"\",\"num\":\""+num+"\""+"}";
			String password = Md5.password(json + stamp);

			sslContext = new SSLContextBuilder().loadTrustMaterial(null, new AbujlbTrustStrategy()).build();
			SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslContext);
			CloseableHttpClient httpClient = HttpClients.custom().setSSLSocketFactory(sslsf).build();

			httpPost = new HttpPost(url);
			ArrayList<BasicNameValuePair> pairs = new ArrayList<BasicNameValuePair>();
			pairs.add(new BasicNameValuePair("data", json));
			pairs.add(new BasicNameValuePair("stamp", stamp));
			pairs.add(new BasicNameValuePair("password", password));

			UrlEncodedFormEntity urlEncodedFormEntity = new UrlEncodedFormEntity(pairs, "utf-8");
			httpPost.setEntity(urlEncodedFormEntity);
			httpResponse = httpClient.execute(httpPost);
			HttpEntity entity = httpResponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			System.out.println(body);
			JSONObject jsonObj = JSONObject.fromObject(body);
			int code = jsonObj.getInt("code");
			if (code == 0 && jsonObj.has("records")) {
				//业务逻辑
				JSONArray records = JSONArray.fromObject(jsonObj.getString("records"));
				
				for(int i=0;i<records.size() ;i++) {
					JSONObject record = records.getJSONObject(i) ;
					String key =record.getString("key") ;
					String value = record.getString("value") ;
					System.out.println(key+"===>"+value);
				}
			} else {
				System.out.println(body);
			}
			return true;
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		} finally {
			if (httpPost != null) {
				httpPost.releaseConnection();
			}
		}
		return false;
	}
	
	
	
	/**
	 * 买家秀-印象接口
	 * 
	 */
	private static boolean mjxyx(String bbid) {
		String url = server_url + "free_mjxyx.action";
		SSLContext sslContext; 
		HttpPost httpPost = null;
		HttpResponse httpResponse;
		try {
			Gson gson = new Gson();
			String stamp = String.valueOf(System.currentTimeMillis());
			String password = Md5.password(bbid + stamp);

			sslContext = new SSLContextBuilder().loadTrustMaterial(null, new AbujlbTrustStrategy()).build();
			SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslContext);
			CloseableHttpClient httpClient = HttpClients.custom().setSSLSocketFactory(sslsf).build();

			httpPost = new HttpPost(url);
			ArrayList<BasicNameValuePair> pairs = new ArrayList<BasicNameValuePair>();
			pairs.add(new BasicNameValuePair("data", bbid));
			pairs.add(new BasicNameValuePair("stamp", stamp));
			pairs.add(new BasicNameValuePair("password", password));

			UrlEncodedFormEntity urlEncodedFormEntity = new UrlEncodedFormEntity(pairs, "utf-8");
			httpPost.setEntity(urlEncodedFormEntity);
			httpResponse = httpClient.execute(httpPost);
			HttpEntity entity = httpResponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			System.out.println(body);
			JSONObject jsonObj = JSONObject.fromObject(body);
			int code = jsonObj.getInt("code");
			if (code == 0 ) {
					
				JSONArray skuArray = jsonObj.getJSONArray("sku") ;
				for(int i = 0 ; i<skuArray.size();i++) {
					String key = skuArray.getJSONObject(i).getString("key") ;
					String value = skuArray.getJSONObject(i).getString("value") ;
					System.out.println(key+"===>"+value);
				}
				JSONArray wordArray = jsonObj.getJSONArray("word") ;
				System.out.println("=======================================");
				System.out.println("=======================================");
				System.out.println("=======================================");
				for(int i = 0 ; i<wordArray.size();i++) {
					String key = wordArray.getJSONObject(i).getString("key") ;
					String value = wordArray.getJSONObject(i).getString("value") ;
					System.out.println(key+"===>"+value);
				}
				
			}
			return true;
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		} finally {
			if (httpPost != null) {
				httpPost.releaseConnection();
			}
		}
		return false;
	}
	
	
	/**
	 * 买家秀-确定下载
	 * @param bbid 宝贝id
	 * @param sort 排序1：推荐排序 2：时间排序
	 * @param  图片：picture 视频：video
	 * @param expre 印象词id ==>买家秀印象接口返回
	 * @param sku skuid==>买家秀印象接口返回 sku的value
	 * @param skuCn 买家秀印象接口返回 sku的key
	 * @param expreCn 买家秀印象接口返回 word的value
	 */
	private static boolean mjxqdxz(String bbid,String sort ,String type ,String expre ,String  sku ,String skuCn,String expreCn) {
		String url = server_url + "free_mjx_qdxz.action";
		SSLContext sslContext; 
		HttpPost httpPost = null;
		HttpResponse httpResponse;
		try {
			Gson gson = new Gson();
			String stamp = String.valueOf(System.currentTimeMillis());
			String json = "{\"url\":\""+bbid+"\",\"sort\":\""+sort+"\",\"type\":\""+type+"\",\"expre\":\""+expre+"\",\"sku\":\""+sku+"\",\"skuCn\":\""+skuCn+"\",\"expreCn\":\""+expreCn+"\""+"}";
			String password = Md5.password(json + stamp);

			sslContext = new SSLContextBuilder().loadTrustMaterial(null, new AbujlbTrustStrategy()).build();
			SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslContext);
			CloseableHttpClient httpClient = HttpClients.custom().setSSLSocketFactory(sslsf).build();

			httpPost = new HttpPost(url);
			ArrayList<BasicNameValuePair> pairs = new ArrayList<BasicNameValuePair>();
			pairs.add(new BasicNameValuePair("data", json));
			pairs.add(new BasicNameValuePair("stamp", stamp));
			pairs.add(new BasicNameValuePair("password", password));

			UrlEncodedFormEntity urlEncodedFormEntity = new UrlEncodedFormEntity(pairs, "utf-8");
			httpPost.setEntity(urlEncodedFormEntity);
			httpResponse = httpClient.execute(httpPost);
			HttpEntity entity = httpResponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			System.out.println(body);
			JSONObject jsonObj = JSONObject.fromObject(body);
			int code = jsonObj.getInt("code");
			if (code == 0 ) {
				String pid = jsonObj.getString("pid");
				System.out.println("pid===>"+pid);
			}
			return true;
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		} finally {
			if (httpPost != null) {
				httpPost.releaseConnection();
			}
		}
		return false;
	}
	
	/**
	 * 买家秀-点击查看
	 * @param bbid 宝贝id
	 * @param sort 排序1：推荐排序 2：时间排序
	 * @param  图片：picture 视频：video
	 * @param expre 印象词id ==>买家秀印象接口返回
	 * @param sku skuid==>买家秀印象接口返回 sku的value
	 * @param skuCn 买家秀印象接口返回 sku的key
	 * @param expreCn 买家秀印象接口返回 word的value
	 * @param num 页码
	 * @param pid 买家秀确定下载接口返回的pid
	 */
	private static boolean mjxdjck(String bbid,String sort ,String type ,String expre ,String  sku ,String skuCn,String expreCn,int num,String pid) {
		String url = server_url + "free_mjx_ck.action";
		SSLContext sslContext; 
		HttpPost httpPost = null;
		HttpResponse httpResponse;
		try {
			Gson gson = new Gson();
			String stamp = String.valueOf(System.currentTimeMillis());
			String json = "{\"url\":\""+bbid+"\",\"sort\":\""+sort+"\",\"type\":\""+type+"\",\"expre\":\""+expre+"\",\"sku\":\""+sku+"\",\"skuCn\":\""+skuCn+"\",\"expreCn\":\""+expreCn+"\",\"pid\":\""+pid+"\",\"num\":\""+num+"\"}";
			String password = Md5.password(json + stamp);

			sslContext = new SSLContextBuilder().loadTrustMaterial(null, new AbujlbTrustStrategy()).build();
			SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslContext);
			CloseableHttpClient httpClient = HttpClients.custom().setSSLSocketFactory(sslsf).build();

			httpPost = new HttpPost(url);
			ArrayList<BasicNameValuePair> pairs = new ArrayList<BasicNameValuePair>();
			pairs.add(new BasicNameValuePair("data", json));
			pairs.add(new BasicNameValuePair("stamp", stamp));
			pairs.add(new BasicNameValuePair("password", password));

			UrlEncodedFormEntity urlEncodedFormEntity = new UrlEncodedFormEntity(pairs, "utf-8");
			httpPost.setEntity(urlEncodedFormEntity);
			httpResponse = httpClient.execute(httpPost);
			HttpEntity entity = httpResponse.getEntity();
			String body = EntityUtils.toString(entity, "utf-8");
			System.out.println("body===>"+body);
			JSONObject jsonObj = JSONObject.fromObject(body);
			int code = jsonObj.getInt("code");
			if (code == 0 ) {
				
			}
			return true;
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		} finally {
			if (httpPost != null) {
				httpPost.releaseConnection();
			}
		}
		return false;
	}
	
	// main方法
	public static void main(String[] args) {
//		bblm("616460321632");
//		String [] arr = {"627143186411","626682033781","573112892321","624525303779","628464813406","626728794491","631209242631","630850241093","627955209826","625231869023"};
//		for(int i =0;i<1;i++) {
//			for(int j=0;j<arr.length;j++) {
//				wxxq(arr[j]);
//			}
//			
//		}
		
//		jpggxsbl("632040034863");
//		jpggxsblxq( "632040034863", "a393c950ccf1497e99168bd8f3b377d6", 3, "8198");
		mjxyx("626689625944");
//		mjxqdxz("626689625944","2","picture","40001271-13","","","很薄");
//		mjxdjck("626689625944","2","picture","40001271-13","","","很薄",1,"0dc511c70cf0406aa4438348f6c6a427");
	}
	
}
