package com.abujlb.zdcj8.test.free;

import org.apache.log4j.Logger;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.abujlb.BaseTest;
import com.abujlb.jdwx.TfkUtil;

/**
 * 单元测试：测试旺旺标签查询
 * 
 * <AUTHOR>
 * @date 2020-12-04 11:57:48
 */
public class TestWangTag extends BaseTest {

	private static Logger log = Logger.getLogger(TestWangTag.class);
	
	// private static final String server_url = "http://localhost/zdcj8/";
	// private static final String server_url = "http://*************/"; // 2024-07-02调整：腾讯云服务器：************，调整IP：*************
	
	@Autowired
	private TfkUtil tfkUtil;
	
	/**
	 * 签名测试
	 * 
	 */
	@Test
	public void test() {
		try {
			System.out.println(tfkUtil.getwangtag("叶问"));
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		}
	}
	
	// main方法
	public static void main(String[] args) {
		// String userId = "2200664650269"; // 觅橘旗舰店，天猫店铺首页：https://mijufs.tmall.com/
		// String userId = "2752004044"; // lp运动旗舰店，天猫店铺首页：https://lpydhw.tmall.com/
		// String userId = "800302085"; // 尔沫旗舰店，天猫店铺首页：https://ermo.m.tmall.com/
		// String userId = "1035301420"; // 米子旗旗舰店，天猫店铺首页：https://miziqi.tmall.com/
		// String userId = "772352677"; // 风驰运动专营店，天猫店铺首页：https://fengchiyundong.tmall.com/shop/view_shop.htm
		String userId = "1579139371"; // 雅羊人旗舰店，天猫店铺首页：https://yayangren.tmall.com/shop/view_shop.htm
		System.out.println(userId);
	}
	
}
