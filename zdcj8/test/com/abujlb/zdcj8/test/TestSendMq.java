package com.abujlb.zdcj8.test;

import com.abujlb.BaseTest;
import com.abujlb.jyrb.MessageTypeConst;
import com.abujlb.jyrb.bean.JysjMsg;
import com.abujlb.mq.AliyunMq2;
import com.abujlb.mq.JobMessage;
import com.abujlb.mq.TxyunMq;
import com.aliyun.openservices.ons.api.SendResult;
import com.google.gson.Gson;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class TestSendMq extends BaseTest {
    @Autowired
    private TxyunMq txyunMq;
    @Autowired
    private AliyunMq2 aliyunMq2;

    @Test
    public void test() {
        JysjMsg msg = new JysjMsg();
        msg.setCookieKey("ck_a89575f21748427da4f3c483d9f759dc");
        msg.setType(JysjMsg.TYPE_LOGIN);
        // String s = txyunMq.sendMessage2("jysjQueue", MessageTypeConst.JYSJCJ_ZDCJ8, msg);
        // System.out.println(s);

        SendResult sendResult = aliyunMq2.send("zdcj8", "", new JobMessage("zdcj8processer", msg));
        System.out.println(new Gson().toJson(sendResult));
    }

    @Test
    public void scxse() {
        JysjMsg msg = new JysjMsg();
        msg.setCookieKey("ck_eaaf6ec176a444e5b0fedd8a5bb8fa8e");
        String s = txyunMq.sendMessage2("jysjQueue", MessageTypeConst.JYSJCJ_ZDCJ8, msg);
        System.out.println(s);
    }
}
