package com.abujlb.zdcj8.test.sku;

import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

import org.jsoup.Jsoup;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.abujlb.BaseTest;
import com.abujlb.Config;
import com.abujlb.jdwx.DdxUtil;
import com.abujlb.jdwx.JdwxUtil;
import com.abujlb.util.Md5;

import net.sf.json.JSONObject;

/**
* <AUTHOR>
* @date 2020-11-26
*/
public class TestSku2 extends BaseTest{

	
	@Autowired
	private DdxUtil ddxUtil ;
	@Autowired
	private JdwxUtil jdwxUtil;
	
	@Autowired
	private Config config;
	private String DDX_APIKEY;
	private String DDX_TOKEN;
	
	@Test
	public void item_detailinfo() {
		String id = "629678879892";
		TreeMap<String, Object> paramsMap = new TreeMap<>() ;
		paramsMap.put("id", id) ;
		String paramsUrl = "&id="+id ;
		DDX_APIKEY = "Mtyop45aOH6yK7KUqa2O1y6YNIHvmAHk";
		DDX_TOKEN ="c4817394c5857045fc86418e2df15b3b";
		try {
			String params = "";
			paramsMap.put("token", "c4817394c5857045fc86418e2df15b3b");
			paramsMap.put("apikey", "Mtyop45aOH6yK7KUqa2O1y6YNIHvmAHk");
			for(String key:paramsMap.keySet()) {//按照key的ASCII码从小到大排序取出参数
				if(params.length()==0) {
					params = key+"="+paramsMap.get(key);
				}else {
					params = params+"&"+key+"="+paramsMap.get(key);
				}
			}
			String sign = Md5.md5(params);//生成签名
			String url = "http://api.tbk.dingdanxia.com/tbk/item_detailinfo?apikey="+DDX_APIKEY+"&signature="+sign+paramsUrl;
			String ret =Jsoup.connect(url).execute().body();
			JSONObject json = JSONObject.fromObject(ret);
			if (json.getInt("code")==200 && json.getJSONObject("data") != null
					&& !json.getJSONObject("data").isNullObject()) {
				JSONObject _result = json.getJSONObject("data");
				System.out.println( _result.toString());
			}
		} catch (Exception e) {
			e.printStackTrace();
		} finally {

		}
	}
	
	
	@Test
	public void jdwx() {
		Map<String, Object> data  =new HashMap<>();
		data.put("num_iid", "7391833431") ;
		String result  =jdwxUtil.query("MDetailGetDetail", data) ;
		System.out.println(result);
	}
}
