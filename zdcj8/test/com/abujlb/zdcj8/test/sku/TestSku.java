package com.abujlb.zdcj8.test.sku;

import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.junit.Test;

import com.abujlb.BaseTest;
import com.abujlb.util.ResourceContent;

public class TestSku extends BaseTest {

	@Test
	public void test() {
		String ck = ResourceContent.getContent("/com/abujlb/zdcj8/test/sku/ck.txt", "utf-8");
		get(ck);
	}

	public void get(String ck) {
		CloseableHttpClient httpClient = HttpClients.custom().build();
		HttpGet httpget = new HttpGet(
				"https://sycm.taobao.com/cc/live/item/sale/compete/sku/list.json?dateRange=2020-09-08%7C2020-09-08&dateType=today&pageSize=10&page=1&order=desc&orderBy=tradeIndex&device=0&itemId=619249601003&indexCode=tradeIndex&_="
						+ System.currentTimeMillis() + "&token=e415732dd");
		httpget.setHeader("accept", "*/*");
		httpget.setHeader("accept-encoding", "gzip, deflate, sdch, br");
		httpget.setHeader("accept-language", "zh-CN,zh;q=0.8");
		httpget.setHeader("referer", "https://sycm.taobao.com/cc/item_archives");
		httpget.setHeader("cookie", ck);
		httpget.setHeader("user-agent",
				"Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/55.0.2883.75 Safari/537.36");

		String body = null;
		HttpResponse httpresponse;
		try {
			httpresponse = httpClient.execute(httpget);
			HttpEntity entity = httpresponse.getEntity();
			body = EntityUtils.toString(entity);
			System.out.println(body);
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			httpget.releaseConnection();
		}
	}
}
