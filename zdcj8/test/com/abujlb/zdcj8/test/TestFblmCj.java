package com.abujlb.zdcj8.test;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.abujlb.BaseTest;
import com.abujlb.ckstore.AbujlbCookieStore;
import com.abujlb.ckstore.Cjzh;
import com.abujlb.jyrb.bean.Dp;
import com.abujlb.jyrb.bean.JysjMsg;
import com.abujlb.jyrb.service.DataUploader;
import com.abujlb.jyrb.sjcj.fblm.FblmCjMain;

public class TestFblmCj extends BaseTest {
	@Autowired
	private FblmCjMain fblmCjMain;
	@Autowired
	private AbujlbCookieStore cookieStore;
	@Autowired
	private DataUploader uploader;

	@Test
	public void test() {
		String ck = "ck_a9d8ad01a30b472ca0a4aa7d0f7453ba";
		JysjMsg msg = new JysjMsg();
		msg.setCookieKey(ck);
		msg.setType(JysjMsg.TYPE_MANUAL);

		Cjzh cjzh = cookieStore.getCjzhForKey(ck);
		Dp dp = uploader.hqdp(cjzh);
		dp.setMsgType(msg.getType());

		fblmCjMain.cj(cjzh, dp, msg);
	}
}
