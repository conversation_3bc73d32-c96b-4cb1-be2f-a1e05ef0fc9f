<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE configuration
        PUBLIC "-//mybatis.org//DTD Config 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-config.dtd">
<configuration>
    <settings>
        <setting name="logImpl" value="STDOUT_LOGGING"/>
    </settings>

    <plugins>
        <plugin interceptor="com.github.pagehelper.PageInterceptor">
            <!-- 该参数默认为false，设置为true时，会将RowBounds第一个参数offset当成pageNum页码使用 -->
            <property name="offsetAsPageNum" value="true"/>
            <!-- 该参数默认为false，设置为true时，使用RowBounds分页会进行count查询 -->
            <property name="rowBoundsWithCount" value="true"/>
            <property name="reasonable" value="true"/>
            <property name="offsetAsPageNum" value="true"/>
        </plugin>
    </plugins>
</configuration>