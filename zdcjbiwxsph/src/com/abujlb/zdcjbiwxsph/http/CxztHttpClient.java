package com.abujlb.zdcjbiwxsph.http;

import com.abujlb.util.StringUtil;
import com.abujlb.zdcjbiwxsph.bean.CxztDspDp;
import com.abujlb.zdcjbiwxsph.bean.CxztZbjDp;
import com.abujlb.zdcjbiwxsph.cookie.Cjzh;
import com.abujlb.zdcjbiwxsph.log.CjLog;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.CookieSpecs;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.apache.log4j.Logger;

import java.util.HashMap;
import java.util.Map;


/**
 * 超雄智投http类
 */
public class CxztHttpClient {

    static final Logger LOG = Logger.getLogger(CxztHttpClient.class);

    /**
     * 查询店铺列表信息
     * @return 店铺映射表，key为店铺昵称，value为店铺用户名
     */
    public static Map<String, String> queryDpLists(Cjzh cjzh) {
        // 添加调用日志记录
        CjLog.addTime("spxg_http.queryDpLists");

        CloseableHttpClient httpClient = HttpClients.custom().build();
        HttpPost httpPost = null;

        try {
            String url = "https://sapi.sphcxzt.com/live_order/author_lists";
            httpPost = new HttpPost(url);

            // 设置请求头
            httpPost.setHeader("Accept", "application/json, text/plain, */*");
//            httpPost.setHeader("Accept-Encoding", "gzip, deflate, br, zstd");
//            httpPost.setHeader("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
            httpPost.setHeader("Authorization", "Bearer " + cjzh.getToken());
            httpPost.setHeader("Content-Type", "application/json");
            httpPost.setHeader("Origin", "https://www.sphcxzt.com");
            httpPost.setHeader("Referer", "https://www.sphcxzt.com/");
            httpPost.setHeader("Sec-Ch-Ua", "\"Google Chrome\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"");
            httpPost.setHeader("Sec-Ch-Ua-Mobile", "?0");
            httpPost.setHeader("Sec-Ch-Ua-Platform", "\"Windows\"");
            httpPost.setHeader("Sec-Fetch-Dest", "empty");
            httpPost.setHeader("Sec-Fetch-Mode", "cors");
            httpPost.setHeader("Sec-Fetch-Site", "same-site");
            httpPost.setHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");

            // 构建请求体
            JSONObject requestBody = new JSONObject();
            requestBody.put("platform", 1);
            requestBody.put("version", 1);
            requestBody.put("lever", "");
            requestBody.put("all", "");

            // 设置请求体
            StringEntity entity = new StringEntity(requestBody.toString(), "UTF-8");
            httpPost.setEntity(entity);

            // 配置请求超时参数
            RequestConfig requestConfig = RequestConfig.custom()
                    .setSocketTimeout(20000)
                    .setConnectTimeout(20000)
                    .setConnectionRequestTimeout(20000)
                    .setCookieSpec(CookieSpecs.NETSCAPE)
                    .build();
            httpPost.setConfig(requestConfig);

            // 执行请求并获取响应
            HttpResponse httpResponse = httpClient.execute(httpPost);
            HttpEntity responseEntity = httpResponse.getEntity();
            String body = EntityUtils.toString(responseEntity, "UTF-8");

            // 检查响应是否为有效的JSON格式
            if (!StringUtil.isJson(body)) {
                return null;
            }

            // 解析响应数据
            JSONObject jsonObject = JSONObject.parseObject(body);
            if (jsonObject.getInteger("code") == 0 && jsonObject.containsKey("data")) {
                JSONArray dataArray = jsonObject.getJSONArray("data");
                Map<String, String> dpMap = new HashMap<>();

                // 将数据转换成Map格式
                for (int i = 0; i < dataArray.size(); i++) {
                    JSONObject item = dataArray.getJSONObject(i);
                    String nickname = item.getString("target_nickname");
                    String username = item.getString("target_username");
                    dpMap.put(nickname, username);
                }

                return dpMap;
            }
        } catch (Exception e) {
            LOG.error("查询店铺列表失败", e);
        } finally {
            // 释放资源
            if (httpPost != null) {
                httpPost.releaseConnection();
            }
            try {
                if (httpClient != null) {
                    httpClient.close();
                }
            } catch (Exception e) {
                LOG.error("关闭HTTP客户端失败", e);
            }
        }
        return null;
    }



    /**
     * 查询直播统计数据
     * @param rq 日期 格式：yyyy-MM-dd
     * @return 直播数据统计对象
     */
    public static CxztZbjDp queryLiveStatistics(Cjzh cjzh ,String rq,String authorUsername) {
        // 添加调用日志记录
        CjLog.addTime("spxg_http.queryLiveStatistics");

        // 转换日期格式
        String startTime = rq + " 00:00:00";
        String endTime = rq + " 23:59:59";

        CloseableHttpClient httpClient = HttpClients.custom().build();
        HttpPost httpPost = null;

        try {
            String url = "https://sapi.sphcxzt.com/live_order/statistics";
            httpPost = new HttpPost(url);

            // 设置请求头
            httpPost.setHeader("Accept", "application/json, text/plain, */*");
//            httpPost.setHeader("Accept-Encoding", "gzip, deflate, br, zstd");
//            httpPost.setHeader("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
            httpPost.setHeader("Authorization", "Bearer " + cjzh.getToken());
            httpPost.setHeader("Content-Type", "application/json");
            httpPost.setHeader("Origin", "https://www.sphcxzt.com");
            httpPost.setHeader("Referer", "https://www.sphcxzt.com/");
            httpPost.setHeader("Sec-Ch-Ua", "\"Google Chrome\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"");
            httpPost.setHeader("Sec-Ch-Ua-Mobile", "?0");
            httpPost.setHeader("Sec-Ch-Ua-Platform", "\"Windows\"");
            httpPost.setHeader("Sec-Fetch-Dest", "empty");
            httpPost.setHeader("Sec-Fetch-Mode", "cors");
            httpPost.setHeader("Sec-Fetch-Site", "same-site");
            httpPost.setHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");

            // 构建请求体
            JSONObject requestBody = new JSONObject();
            requestBody.put("author_username", authorUsername);
            requestBody.put("platform", 1);
            requestBody.put("version", 1);
            requestBody.put("promotion", "");
            requestBody.put("state", "");
            requestBody.put("sph_id", "");
            requestBody.put("is_cost", "");
            requestBody.put("is_lever", "");
            requestBody.put("start_time", startTime);
            requestBody.put("end_time", endTime);
            requestBody.put("time_type", 2);
            requestBody.put("lever_id", null);
            requestBody.put("target", "");
            requestBody.put("department_id", "");
            requestBody.put("operate_id", "");

            // 设置请求体
            StringEntity entity = new StringEntity(requestBody.toString(), "UTF-8");
            httpPost.setEntity(entity);

            // 配置请求超时参数
            RequestConfig requestConfig = RequestConfig.custom()
                    .setSocketTimeout(20000)
                    .setConnectTimeout(20000)
                    .setConnectionRequestTimeout(20000)
                    .setCookieSpec(CookieSpecs.NETSCAPE)
                    .build();
            httpPost.setConfig(requestConfig);

            // 执行请求并获取响应
            HttpResponse httpResponse = httpClient.execute(httpPost);
            HttpEntity responseEntity = httpResponse.getEntity();
            String body = EntityUtils.toString(responseEntity, "UTF-8");

            // 检查响应是否为有效的JSON格式
            if (!StringUtil.isJson(body)) {
                return null;
            }

            // 解析响应数据
            JSONObject jsonObject = JSONObject.parseObject(body);
            if (jsonObject.getInteger("code") == 0 && jsonObject.containsKey("data")) {
                JSONObject data = jsonObject.getJSONObject("data");
                CxztZbjDp result = new CxztZbjDp();

                // 设置字段值，处理可能的数据转换异常
                try {
                    result.setCxztzbhf(Double.parseDouble(data.getString("cost")));
                    result.setCxztzbcjje(Double.parseDouble(data.getString("product_pay_amount")));
                    result.setCxztzbcjdd(Integer.parseInt(data.getString("product_pay_num")));
                    result.setCxztzbcjroi(Double.parseDouble(data.getString("order_roi")));

                    return result;
                } catch (NumberFormatException e) {
                    LOG.error("数据格式转换失败", e);
                    return null;
                }
            }
        } catch (Exception e) {
            LOG.error("查询直播统计数据失败", e);
        } finally {
            // 释放资源
            if (httpPost != null) {
                httpPost.releaseConnection();
            }
            try {
                if (httpClient != null) {
                    httpClient.close();
                }
            } catch (Exception e) {
                LOG.error("关闭HTTP客户端失败", e);
            }
        }
        return null;
    }



    /**
     * 查询短视频统计数据
     * @param rq 日期 格式：yyyy-MM-dd
     * @return 短视频数据统计对象
     */
    public static CxztDspDp queryVideoStatistics(Cjzh cjzh, String rq, String authorUsername) {
        // 转换用户名格式
        String basicUsername = convertToBasicUsername(authorUsername);

        // 添加调用日志记录
        CjLog.addTime("spxg_http.queryVideoStatistics");

        // 转换日期格式
        String startTime = rq + " 00:00:00";
        String endTime = rq + " 23:59:59";

        CloseableHttpClient httpClient = HttpClients.custom().build();
        HttpPost httpPost = null;

        try {
            String url = "https://sapi.sphcxzt.com/order/statistics";
            httpPost = new HttpPost(url);

            // 设置请求头
            httpPost.setHeader("Accept", "application/json, text/plain, */*");
//            httpPost.setHeader("Accept-Encoding", "gzip, deflate, br, zstd");
//            httpPost.setHeader("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
            httpPost.setHeader("Authorization", "Bearer " + cjzh.getToken());
            httpPost.setHeader("Content-Type", "application/json");
            httpPost.setHeader("Origin", "https://www.sphcxzt.com");
            httpPost.setHeader("Referer", "https://www.sphcxzt.com/");
            httpPost.setHeader("Sec-Ch-Ua", "\"Google Chrome\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"");
            httpPost.setHeader("Sec-Ch-Ua-Mobile", "?0");
            httpPost.setHeader("Sec-Ch-Ua-Platform", "\"Windows\"");
            httpPost.setHeader("Sec-Fetch-Dest", "empty");
            httpPost.setHeader("Sec-Fetch-Mode", "cors");
            httpPost.setHeader("Sec-Fetch-Site", "same-site");
            httpPost.setHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");

            // 构建请求体
            JSONObject requestBody = new JSONObject();
            requestBody.put("author_username", basicUsername); // 使用转换后的用户名
            requestBody.put("platform", 1);
            requestBody.put("version", 1);
            requestBody.put("promotion", "");
            requestBody.put("state", "");
            requestBody.put("sph_id", "");
            requestBody.put("is_cost", "");
            requestBody.put("is_lever", "");
            requestBody.put("start_time", startTime);
            requestBody.put("end_time", endTime);
            requestBody.put("time_type", 2);
            requestBody.put("lever_id", null);
            requestBody.put("target", "");
            requestBody.put("department_id", "");
            requestBody.put("operate_id", "");

            // 设置请求体
            StringEntity entity = new StringEntity(requestBody.toString(), "UTF-8");
            httpPost.setEntity(entity);

            // 配置请求超时参数
            RequestConfig requestConfig = RequestConfig.custom()
                    .setSocketTimeout(20000)
                    .setConnectTimeout(20000)
                    .setConnectionRequestTimeout(20000)
                    .setCookieSpec(CookieSpecs.NETSCAPE)
                    .build();
            httpPost.setConfig(requestConfig);

            // 执行请求并获取响应
            HttpResponse httpResponse = httpClient.execute(httpPost);
            HttpEntity responseEntity = httpResponse.getEntity();
            String body = EntityUtils.toString(responseEntity, "UTF-8");

            // 检查响应是否为有效的JSON格式
            if (!StringUtil.isJson(body)) {
                return null;
            }

            // 解析响应数据
            JSONObject jsonObject = JSONObject.parseObject(body);
            if (jsonObject.getInteger("code") == 0 && jsonObject.containsKey("data")) {
                JSONObject data = jsonObject.getJSONObject("data");
                CxztDspDp result = new CxztDspDp();

                // 设置字段值，处理可能的数据转换异常
                try {
                    result.setCxztdsphf(Double.parseDouble(data.getString("cost")));
                    result.setCxztdspcjje(Double.parseDouble(data.getString("order_amount")));
                    result.setCxztdspcjdd(Integer.parseInt(data.getString("order_num")));
                    result.setCxztdspcjroi(Double.parseDouble(data.getString("roi")));

                    return result;
                } catch (NumberFormatException e) {
                    LOG.error("数据格式转换失败", e);
                    return null;
                }
            }
        } catch (Exception e) {
            LOG.error("查询短视频统计数据失败", e);
        } finally {
            // 释放资源
            if (httpPost != null) {
                httpPost.releaseConnection();
            }
            try {
                if (httpClient != null) {
                    httpClient.close();
                }
            } catch (Exception e) {
                LOG.error("关闭HTTP客户端失败", e);
            }
        }
        return null;
    }

    /**
     * 将完整用户名转换为基础用户名
     * @param fullUsername 完整用户名（包含@finder后缀）
     * @return 基础用户名
     */
    private static String convertToBasicUsername(String fullUsername) {
        if (fullUsername == null) {
            return null;
        }

        // 如果包含@finder，截取前面的基础用户名部分（去掉32位扩展hash和@finder）
        if (fullUsername.contains("@finder")) {
            // 基础用户名长度为 v2_ + 47位hash = 50位
            return fullUsername.substring(0, 50);
        }

        return fullUsername;
    }

    /**
     * 分页查询商品列表
     * @param cjzh 账号信息
     * @param rq 日期 格式：yyyy-MM-dd
     * @return 商品列表数据
     */
    public static JSONArray queryProductList(Cjzh cjzh, String rq) {
        JSONArray resultArray = new JSONArray();
        int pageNum = 1;
        int pageSize = 10;

        while (true) {
            JSONArray pageData = queryProductList1(cjzh, rq, pageNum, pageSize);

            // 如果查询失败，返回null
            if (pageData == null) {
                return null;
            }
            // 如果没有数据，结束循环
            if (pageData.isEmpty()) {
                break;
            }

            resultArray.addAll(pageData);

            // 如果当前页数据量小于页大小，说明已经是最后一页
            if (pageData.size() < pageSize) {
                break;
            }
            pageNum++;
        }
        return resultArray;
    }

    /**
     * 查询单页商品列表
     * @param cjzh 账号信息
     * @param rq 日期 格式：yyyy-MM-dd
     * @param pageIndex 页码
     * @param pageSize 每页数量
     * @return 商品列表数据
     */
    public static JSONArray queryProductList1(Cjzh cjzh, String rq, int pageIndex, int pageSize) {
        // 添加调用日志
        CjLog.addTime("splb_http.queryProductList1");

        // 构建完整时间
        String startTime = rq + " 00:00:00";
        String endTime = rq + " 23:59:59";

        CloseableHttpClient httpClient = HttpClients.custom().build();
        HttpPost httpPost = null;

        try {
            String url = "https://sapi.sphcxzt.com/product/lists";
            httpPost = new HttpPost(url);

            // 设置请求头
            httpPost.setHeader("Accept", "application/json, text/plain, */*");
//            httpPost.setHeader("Accept-Encoding", "gzip, deflate, br, zstd");
//            httpPost.setHeader("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
            httpPost.setHeader("Authorization", "Bearer " + cjzh.getToken());
            httpPost.setHeader("Content-Type", "application/json");
            httpPost.setHeader("Origin", "https://www.sphcxzt.com");
            httpPost.setHeader("Referer", "https://www.sphcxzt.com/");
            httpPost.setHeader("Sec-Ch-Ua", "\"Google Chrome\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"");
            httpPost.setHeader("Sec-Ch-Ua-Mobile", "?0");
            httpPost.setHeader("Sec-Ch-Ua-Platform", "\"Windows\"");
            httpPost.setHeader("Sec-Fetch-Dest", "empty");
            httpPost.setHeader("Sec-Fetch-Mode", "cors");
            httpPost.setHeader("Sec-Fetch-Site", "same-site");
            httpPost.setHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");

            // 构建请求参数
            JSONObject requestBody = new JSONObject();
            requestBody.put("platform", 1);
            requestBody.put("version", 1);
            requestBody.put("product_keyword", "");
            requestBody.put("promotion_order", "");
            requestBody.put("time_type", 2);
            requestBody.put("start_time", startTime);
            requestBody.put("end_time", endTime);
            requestBody.put("page", pageIndex);
            requestBody.put("limit", pageSize);

            // 设置请求体
            StringEntity entity = new StringEntity(requestBody.toString(), "UTF-8");
            httpPost.setEntity(entity);

            // 配置请求超时参数
            RequestConfig requestConfig = RequestConfig.custom()
                    .setSocketTimeout(20000)
                    .setConnectTimeout(20000)
                    .setConnectionRequestTimeout(20000)
                    .setCookieSpec(CookieSpecs.NETSCAPE)
                    .build();
            httpPost.setConfig(requestConfig);

            // 执行请求并获取响应
            HttpResponse httpResponse = httpClient.execute(httpPost);
            HttpEntity responseEntity = httpResponse.getEntity();
            String body = EntityUtils.toString(responseEntity, "UTF-8");

            // 检查响应是否为有效的JSON格式
            if (!StringUtil.isJson(body)) {
                return null;
            }

             // 解析响应数据
            JSONObject jsonObject = JSONObject.parseObject(body);
            if ((jsonObject.getInteger("code") == 0 || jsonObject.getInteger("code") == 10006) && jsonObject.containsKey("data")) {
                if (jsonObject.getInteger("code") == 10006) {
                    return new JSONArray();
                }
                JSONObject data = jsonObject.getJSONObject("data");
                if (data.containsKey("data")) {
                    return data.getJSONArray("data");
                }
                return new JSONArray();
            }
        } catch (Exception e) {
            LOG.error("查询商品列表失败", e);
        } finally {
            // 释放资源
            if (httpPost != null) {
                httpPost.releaseConnection();
            }
            try {
                if (httpClient != null) {
                    httpClient.close();
                }
            } catch (Exception e) {
                LOG.error("关闭HTTP客户端失败", e);
            }
        }
        return null;
    }
}
