package com.abujlb.service4.service;

import com.abujlb.CommonConfig;
import com.abujlb.Result;
import com.abujlb.service4.bean.BiInfo;
import com.abujlb.service4.bean.QySpkMy;
import com.abujlb.service4.mapper.*;
import com.abujlb.service4.pojo.*;
import com.abujlb.service4.server.Service2Uploader;
import com.abujlb.service4.util.DataUploader;
import com.abujlb.service4.util.JstOssDao;
import com.abujlb.util.DateUtil;
import com.abujlb.util.StringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * bi.bi_yhdp表service
 *
 * <AUTHOR>
 * @date 2023-03-29 10:14:17
 */
@Service
public class BiYhdpService {

    private static final Logger log = Logger.getLogger(BiYhdpService.class);

    @Autowired
    private BiYhdpMapper biYhdpMapper;
    @Autowired
    private BiYhdpOtherMapper biYhdpOtherMapper;
    @Autowired
    private BiYhdpTestMapper biYhdpTestMapper;
    @Autowired
    private BiErrInfoService biErrInfoService;
    @Autowired
    private BiErrInfoMapper biErrInfoMapper;
    @Autowired
    private BiSjhMapper biSjhMapper;
    @Autowired
    private BiFinishTimeService biFinishTimeService;
    @Autowired
    private Service2Uploader service2Uploader;
    @Autowired
    private QySpkMyMapper qySpkMyMapper;
    @Autowired
    private BiDdpushTokenExpireMapper biDdpushTokenExpireMapper;

    /**
     * 根据userid查询榜单店铺信息
     */
    public Result queryExsitDp(List<String> userId) {
        Result result = new Result(Result.DBERROR, "数据库error");
        try {
            List<DpMonthTopInfo> biYhdp = biYhdpMapper.queryExsitDp(userId);
            if (biYhdp != null) {
                result.setCodeContent(Result.SUCCESS, "");
                result.putKey("data", biYhdp);
                return result;
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage(), e);
        }
        return result;
    }

    /**
     * select根据userid查询淘系
     *
     * @param userid
     */
    public Result getByUserid(String userid) {
        Result result = new Result(Result.DBERROR, "数据库error");
        try {
            BiYhdp biYhdp = biYhdpMapper.getByUserid(userid);
            if (biYhdp != null) {
                result.setCodeContent(Result.SUCCESS, "");
                result.putKey("data", biYhdp);
                return result;
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage(), e);
        }
        return result;
    }

    /**
     * select多条件查询
     *
     * @param biYhdp
     */
    public Result getBiYhdpByYhidAndUseridQd(BiYhdp biYhdp) {
        Result result = new Result(Result.DBERROR, "数据库error");
        try {
            BiYhdp temp = biYhdpMapper.getBiYhdpByYhidAndUseridQd2(biYhdp);
            if (temp != null) {
                result.setCodeContent(Result.SUCCESS, "");
                result.putKey("data", temp);
                return result;
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage(), e);
        }
        return result;
    }

    /**
     * select多条件查询list
     *
     * @param biYhdp
     */
    public Result getBiYhdpList(BiYhdp biYhdp) {
        Result result = new Result(Result.DBERROR, "数据库error");
        try {
            List<BiYhdp> list = biYhdpMapper.getBiYhdpList2(biYhdp);
            if (list != null) {
                result.setCodeContent(Result.SUCCESS, "");
                result.putKey("data", list);
                return result;
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage(), e);
        }
        return result;
    }

    /**
     * update更新
     *
     * @param biYhdp
     */
    public Result updateBiYhdpById(BiYhdp biYhdp) {
        Result result = new Result(Result.DBERROR, "数据库error");
        boolean updateFlag = false;
        try {
            // 获取未更新前的sycmcljzrq 以及updaterq的值
            BiYhdp temp = biYhdpMapper.getBiYhdpByYhidAndUseridQd2(biYhdp);
            if (biYhdp.getSycmcljzrq().compareTo(temp.getSycmcljzrq()) <= 0 && biYhdp.getSycmjzrq2().compareTo(temp.getSycmjzrq2()) >= 0) {
                result.setCodeContent(Result.PARAERROR, "参数不合法");
                return result;
            }
            String today = DateUtil.formatDate(new Date());
            if (temp.getSycmcljzrqtime() == null || !DateUtil.formatTime(temp.getSycmcljzrqtime()).equals(today)) {
                biYhdp.setSycmcljzrqtime(new Date());
                updateFlag = true;
            }
            if (StringUtils.isBlank(temp.getSycmclksrq())) {
                BiInfo biInfo = DataUploader.getBiInfo(temp.getUserid(), temp.getQd());
                if (Objects.nonNull(biInfo)) {
                    biYhdp.setSycmclksrq(com.abujlb.service4.util.DateUtil.formatDate(biInfo.getStart()));
                }
            }
            biYhdpMapper.updateBiYhdpById(biYhdp);

            DataUploader.updateBiInfoSycmcljzrq(temp.getUserid(), temp.getQd(), biYhdp.getSycmcljzrq());

            if (updateFlag) {
                biFinishTimeService.addFinishTime(biYhdp.getYhid(), biYhdp.getQd(), biYhdp.getUserid(), biYhdp.getSycmcljzrq(), new String[]{"sycmcljzrqtime"});
            }

            // 查看是否是test yh 如果是 则更新bi_yhdp_test
            Map<String, Object> map = new HashMap<>();
            map.put("qd", biYhdp.getQd());
            map.put("userid", biYhdp.getUserid());
            map.put("yhid", String.valueOf(biYhdp.getYhid()));
            List<BiYhdp> tempList = biYhdpTestMapper.selectByMap(map);
            if (CollectionUtils.isNotEmpty(tempList)) {
                // 更新bitest.bi_yhdp
                Map<String, Object> param = new HashMap<>();
                param.put("qd", biYhdp.getQd());
                param.put("yhid", String.valueOf(biYhdp.getYhid()));
                param.put("userid", biYhdp.getUserid());
                param.put("sycmclksrq", biYhdp.getSycmclksrq());
                param.put("sycmcljzrq", biYhdp.getSycmcljzrq());
                param.put("sycmjzrq2", biYhdp.getSycmjzrq2());
                param.put("sycmcljzrqtime", biYhdp.getSycmcljzrqtime());

                biYhdpTestMapper.updateByMap(param);
            }
            result.setCodeContent(Result.SUCCESS, "");
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage(), e);
        }
        return result;
    }

    /**
     * getBiYhdp多条件查询获得唯一
     *
     * @param biYhdp
     */
    public Result getBiYhdp(BiYhdp biYhdp) {
        Result result = new Result(Result.DBERROR, "数据库error");
        try {
            if (!StringUtil.isNull2(biYhdp.getDpmc())) {
                biYhdp.setDpmc(biYhdp.getDpmc().trim());
            }
            BiYhdp temp = biYhdpMapper.getBiYhdp(biYhdp);
            if (temp != null) {
                result.setCodeContent(Result.SUCCESS, "");
                result.putKey("data", temp);
                return result;
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage(), e);
        }
        return result;
    }

    /**
     * getAllBiYhdp多条件查询list
     *
     * @param biYhdp
     */
    public Result getAllBiYhdp(BiYhdp biYhdp) {
        Result result = new Result(Result.DBERROR, "数据库error");
        try {
            if (!StringUtil.isNull2(biYhdp.getDpmc())) {
                biYhdp.setDpmc(biYhdp.getDpmc().trim());
            }
            List<BiYhdp> list = biYhdpMapper.getAllBiYhdp(biYhdp);
            result.setCodeContent(Result.SUCCESS, "");
            result.putKey("list", list);
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage(), e);
        }
        return result;
    }

    public void updateBiYhdpAlipayrq(BiYhdp data) {
        List<BiYhdp> list = biYhdpMapper.getBiYhdpList2(data);
        try {
            if (CollectionUtils.isEmpty(list)) {
                return;
            }

            // 定义日期格式
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDate currentDate = LocalDate.now(); // 获取当前日期
            // 计算上上个月的第一天
            LocalDate lastLastMonthFirstDay = currentDate.minusMonths(2).withDayOfMonth(1);
            // 格式化上上个月一号为字符串
            String lastLastMonthFirstDayStr = lastLastMonthFirstDay.format(formatter);

            Iterator<BiYhdp> iterator = list.iterator();
            while (iterator.hasNext()) {
                BiYhdp biYhdp = iterator.next();

                if (StringUtils.isBlank(biYhdp.getZdksrq())) {
                    if (!StringUtil.isNull2(data.getZdksrq())) {
                        // 解析字符串为LocalDate
                        LocalDate zdksrq = LocalDate.parse(data.getZdksrq(), formatter);
                        if (zdksrq.isBefore(lastLastMonthFirstDay)) {
                            biYhdp.setZdksrq(lastLastMonthFirstDayStr);
                        } else {
                            biYhdp.setZdksrq(data.getZdksrq());
                        }
                    } else {
                        biYhdp.setZdksrq(data.getZdksrq());
                    }
                }

                if (data.getZdjsrq().compareTo(biYhdp.getZdjsrq()) <= 0) {
                    iterator.remove();
                    continue;
                }

                biYhdp.setZdjsrq(data.getZdjsrq());
            }

            if (CollectionUtils.isEmpty(list)) {
                return;
            }

            biYhdpMapper.updateBiYhdpAlipayrq(list);

            for (BiYhdp biYhdp : list) {
                biFinishTimeService.addFinishTime(biYhdp.getYhid(), biYhdp.getQd(), biYhdp.getUserid(), biYhdp.getZdjsrq(), new String[]{"zdjsrqtime"});
            }

            for (BiYhdp biYhdp : list) {

                Map<String, Object> map = new HashMap<>();
                map.put("qd", data.getQd());
                map.put("userid", data.getUserid());
                map.put("yhid", String.valueOf(data.getYhid()));
                map.put("sfty", 0);
                List<BiYhdp> tempList = biYhdpTestMapper.selectByMap(map);

                if (CollectionUtils.isNotEmpty(tempList)) {
                    // 更新bitest.bi_yhdp的alipay的zdksrq zdjsrq
                    Map<String, Object> param = new HashMap<>();
                    param.put("qd", biYhdp.getQd());
                    param.put("yhid", String.valueOf(biYhdp.getYhid()));
                    param.put("userid", biYhdp.getUserid());
                    if (!StringUtil.isNull2(biYhdp.getSycmclksrq())) {
                        // 解析字符串为LocalDate
                        LocalDate sycmclksrq = LocalDate.parse(biYhdp.getSycmclksrq(), formatter);
                        if (sycmclksrq.isBefore(lastLastMonthFirstDay)) {
                            param.put("zdksrq", lastLastMonthFirstDayStr);
                        } else {
                            param.put("zdksrq", biYhdp.getSycmclksrq());
                        }
                    } else {
                        param.put("zdksrq", biYhdp.getSycmclksrq());
                    }
                    param.put("zdjsrq", biYhdp.getSycmcljzrq());

                    biYhdpTestMapper.updateByMap(param);




                }

            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * upd修改BI店铺名称
     *
     * @param biYhdp
     */
    public Result updateBiShopName(BiYhdp biYhdp) {
        Result result = new Result(Result.DBERROR, "数据库error");
        try {
            if (biYhdp == null || biYhdp.getYhid() < 0 || StringUtil.isNull2(biYhdp.getUserid()) || StringUtil.isNull2(biYhdp.getQd()) || StringUtil.isNull2(biYhdp.getDpmc())) {
                result.setCodeContent(Result.NOLOGIN, "参数错误");
                return result;
            }
            biYhdpMapper.updateBiYhdpForDpmc(biYhdp);
            result.setCodeContent(Result.SUCCESS, "");
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage(), e);
        }
        return result;
    }

    /**
     * selectAllBiYxsl 查询有效数量
     */
    public Result selectAllBiYxsl() {
        Result result = new Result(Result.DBERROR, "数据库error");
        try {
            int yxsl = biYhdpMapper.selectAllBiYxsl();
            int ywcsl = biYhdpMapper.selectAllBiYwcsl();
            result.putKey("yxsl", yxsl);
            result.putKey("ywcsl", ywcsl);
            result.setCodeContent(Result.SUCCESS, "");
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage(), e);
        }
        return result;
    }

    /**
     * update更新
     *
     * @param biYhdp
     */
    public Result updateBiYhdpRq(BiYhdp biYhdp) {
        Result result = new Result(Result.DBERROR, "数据库error");
        try {
            biYhdpMapper.updateBiYhdpRq(biYhdp);
            result.setCodeContent(Result.SUCCESS, "");
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage(), e);
        }
        return result;
    }

    /**
     * 插入存储手机号的异常信息
     */
    public Result updateBiSjhErrInfo(BiSjhErrInfo biSjhErrInfo) {
        Result result = new Result(Result.DBERROR, "数据库error");
        try {
            //2024-4-1---bi_sjh_err_info表废弃
//			biYhdpMapper.updateBiSjhErrInfo(biSjhErrInfo);
            insertBiErrInfo(biSjhErrInfo);
            result.setCodeContent(Result.SUCCESS, "");
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage(), e);
        }
        return result;

    }

    /**
     * data true  是测试用户 false 不是
     *
     * @param data
     * @return
     */
    public Result checkTestYh(BiYhdp data) {
        Result result = new Result(Result.DBERROR, "数据库error");
        try {
            Map<String, Object> map = new HashMap<>();
            map.put("qd", data.getQd());
            map.put("userid", data.getUserid());
            map.put("yhid", String.valueOf(data.getYhid()));
            map.put("sfty", 0);
            List<BiYhdp> list = biYhdpTestMapper.selectByMap(map);
            result.setCodeContent(Result.SUCCESS, "");
            result.putKey("data", CollectionUtils.isNotEmpty(list));
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage(), e);
        }
        return result;
    }

    public Result insertBiYhdpOther(BiYhdpOther biYhdpOther) {
        Result result = new Result(Result.DBERROR, "数据库error");
        try {
            List<BiYhdp> list = biYhdpMapper.getBiYhdpByUserid(biYhdpOther);
            if (list != null && list.size() > 0) {
                for (BiYhdp biYhdp : list) {
                    BiYhdpOther biYhdpOther1 = new BiYhdpOther();
                    biYhdpOther1.setId(biYhdp.getId());
                    BiYhdpOther biYhdpOther2 = biYhdpOtherMapper.getBiYhdpOther(biYhdpOther1);
                    if (biYhdpOther2 != null) {
                        if (biYhdpOther.getAiztone().equals("0") && biYhdpOther2.getAiztone().equals("1")) {
                            biYhdpOther2.setAiztone("1");
                        } else {
                            biYhdpOther2.setAiztone(biYhdpOther.getAiztone());
                        }
                        biYhdpOther2.setYhid(biYhdp.getYhid());
                        biYhdpOther2.setQd(biYhdp.getQd());
                        biYhdpOther2.setUserid(biYhdp.getUserid());
                        biYhdpOther2.setDpmc(biYhdp.getDpmc());
                        biYhdpOther2.setJlbdpid(biYhdpOther.getJlbdpid());
                        if (StringUtils.isNotBlank(biYhdpOther.getRlyqqx())) {
                            biYhdpOther2.setRlyqqx(biYhdpOther.getRlyqqx());
                        }
                        if (StringUtils.isNotBlank(biYhdpOther.getRlyqqy())) {
                            biYhdpOther2.setRlyqqy(biYhdpOther.getRlyqqy());
                        }
                        if (StringUtils.isNotBlank(biYhdpOther.getRlyqsj())) {
                            biYhdpOther2.setRlyqsj(biYhdpOther.getRlyqsj());
                        }
                        biYhdpOtherMapper.updateBiYhdpOther(biYhdpOther2);
                    } else {
                        biYhdpOther1.setYhid(biYhdp.getYhid());
                        biYhdpOther1.setQd(biYhdp.getQd());
                        biYhdpOther1.setUserid(biYhdp.getUserid());
                        biYhdpOther1.setDpmc(biYhdp.getDpmc());
                        biYhdpOther1.setJlbdpid(biYhdpOther.getJlbdpid());
                        biYhdpOther1.setAiztone(biYhdpOther.getAiztone());
                        if (StringUtils.isNotBlank(biYhdpOther.getRlyqqx())) {
                            biYhdpOther1.setRlyqqx(biYhdpOther.getRlyqqx());
                        } else {
                            biYhdpOther1.setRlyqqx("0");
                        }
                        if (StringUtils.isNotBlank(biYhdpOther.getRlyqqy())) {
                            biYhdpOther1.setRlyqqy(biYhdpOther.getRlyqqy());
                        } else {
                            biYhdpOther1.setRlyqqy("0");
                        }
                        if (StringUtils.isNotBlank(biYhdpOther.getRlyqsj())) {
                            biYhdpOther1.setRlyqsj(biYhdpOther.getRlyqsj());
                        }
                        biYhdpOtherMapper.insertBiyhdpOther(biYhdpOther1);
                    }
                }
            }
            result.setCodeContent(Result.SUCCESS, "");
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage(), e);
        }
        return result;
    }

    public Result getBiYhdpOtherByJlbdpid(String jlbdpid) {
        Result result = new Result(Result.DBERROR, "数据库error");
        try {
            BiYhdpOther biYhdpOther = biYhdpOtherMapper.getBiYhdpOtherByJlbdpid(jlbdpid);
            String data = biYhdpOther == null ? "" : JSON.toJSONString(biYhdpOther);
            result.setCodeContent(Result.SUCCESS, data);
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage(), e);
        }
        return result;
    }

    /**
     * updateBiSjhErrInfoForClzt：修改异常手机号状态
     */
    public Result updateBiSjhErrInfoForClzt(BiSjhErrInfo biSjhErrInfo) {
        Result result = new Result(Result.DBERROR, "数据库error");
        try {
            if (biSjhErrInfo == null || StringUtil.isNull2(biSjhErrInfo.getSjh()) || StringUtil.isNull2(biSjhErrInfo.getLxs())) {
                result.setCodeContent(Result.NOLOGIN, "参数错误");
                return result;
            }
            if (StringUtil.isNull2(biSjhErrInfo.getClr())) {
                biSjhErrInfo.setClr("系统自动6");
            }
            //2024-4-1---bi_sjh_err_info表废弃
//			int count = biYhdpMapper.updateBiSjhErrInfoForClzt(biSjhErrInfo);
            updateBiErrInfo(biSjhErrInfo);
            int count = 1;
            result.putKey("count", count);
            result.setCodeContent(Result.SUCCESS, "");
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage(), e);
        }
        return result;
    }

    @SuppressWarnings("unused")
    private void updateBiErrInfo(BiSjhErrInfo biSjhErrInfo) {
        try {
            boolean update = new LambdaUpdateChainWrapper<>(biErrInfoMapper)
                    .set(BiErrInfo::getClzt, 1)
                    .set(BiErrInfo::getClr, biSjhErrInfo.getClr())
                    .set(BiErrInfo::getClsj, new Date())
                    .eq(BiErrInfo::getSjh, biSjhErrInfo.getSjh())
                    .eq(BiErrInfo::getClzt, 0)
                    .eq(BiErrInfo::getType, 1)
                    .in(StringUtils.isNotBlank(biSjhErrInfo.getLxs()), BiErrInfo::getSjhlx, biSjhErrInfo.getLxs().split(","))
                    .update();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    public void insertBiErrInfo(BiSjhErrInfo biSjhErrInfo) {
        try {
            // 查询yhid，表bi.bi_sjh_info
            int yhid = 0;
            if (!StringUtil.isNull2(biSjhErrInfo.getSjh())) {
                BiSjhInfo biSjhInfo = biSjhMapper.getBiSjhInfoBySjh(biSjhErrInfo.getSjh());
                if (biSjhInfo != null && biSjhInfo.getYhid() != null) {
                    yhid = biSjhInfo.getYhid().intValue();
                }
            }

            BiErrInfo biErrInfo = new BiErrInfo();
            biErrInfo.setYhid(yhid);
            biErrInfo.setSjh(biSjhErrInfo.getSjh());
            biErrInfo.setSjhlx(biSjhErrInfo.getLx());
            biErrInfo.setMsg(biSjhErrInfo.getMsg());
            biErrInfo.setClzt(biSjhErrInfo.getZt());
            biErrInfo.setJlsj(new Date());
            biErrInfo.setType(1);
            biErrInfoService.updBiErrInfo(biErrInfo);//2024-6-7修改此手机号的所有未处理手机号异常为系统自动已处理（倪敏提出）
            biErrInfoService.insertBiErrInfo(biErrInfo);
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage(), e);
        }
    }

    /**
     * demo 用户ids
     */
    public Result demoYhids() {
        Result result = new Result(Result.DBERROR, "数据库error");
        try {
            List<String> list = biYhdpMapper.selectBiYhOther();
            result.putKey("list", list);
            result.setCodeContent(Result.SUCCESS, "");
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage(), e);
        }
        return result;
    }

    /**
     * 多条件查询
     *
     * @param biYhdp
     * @return
     */
    public Result getBiYhdpData(BiYhdp biYhdp) {
        Result result = new Result(Result.DBERROR, "数据库error");
        try {
            if (!StringUtil.isNull2(biYhdp.getDpmc())) {
                biYhdp.setDpmc(biYhdp.getDpmc().trim());
            }
            List<BiYhdp> list = biYhdpMapper.getBiYhdpData(biYhdp);
            result.setCodeContent(Result.SUCCESS, "");
            result.putKey("list", list);
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage(), e);
        }
        return result;
    }


    public Result getBiYhdpBean(BiYhdp biYhdp) {
        Result result = new Result(Result.DBERROR, "数据库error");
        try {

            BiYhdp temp = biYhdpMapper.getBiYhdpBean(biYhdp);
            if (temp != null) {
                result.setCodeContent(Result.SUCCESS, "");
                result.putKey("data", temp);
                return result;
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage(), e);
        }
        return result;
    }

    public Result updRqById(BiYhdp biYhdp) {
        Result result = new Result(Result.DBERROR, "数据库error");
        try {
            biYhdpMapper.updRqById(biYhdp);
            result.setCodeContent(Result.SUCCESS, "");
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage(), e);
        }
        return result;
    }

    public Result selBiYhTestCount(BiYhdp biYhdp) {
        Result result = new Result(Result.DBERROR, "数据库error");
        try {

            int count = biYhdpMapper.selBiYhTestCount(biYhdp);
            result.setCodeContent(Result.SUCCESS, "");
            result.putKey("count", count);
            return result;

        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage(), e);
        }
        return result;
    }

    public Result updRqByYhidQdUserid(BiYhdp biYhdp) {
        Result result = new Result(Result.DBERROR, "数据库error");
        try {
            biYhdpMapper.updRqByYhidQdUserid(biYhdp);
            result.setCodeContent(Result.SUCCESS, "");
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage(), e);
        }
        return result;
    }

    public Result delBiErrInfo(BiErrInfo biErrInfo) {
        Result result = new Result(Result.DBERROR, "数据库error");
        try {
            biYhdpMapper.delBiErrInfo(biErrInfo);
            result.setCodeContent(Result.SUCCESS, "");
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage(), e);
        }
        return result;
    }

    public Result addBiErrInfo(BiErrInfo biErrInfo) {
        Result result = new Result(Result.DBERROR, "数据库error");
        try {
            BiErrInfo selbz = new BiErrInfo();
            if (biErrInfo.getType() == 1) {//手机号异常
                selbz = biYhdpMapper.selbz1(biErrInfo);

                //查询bi_sjh_info查询手机号是否异常，如果异常，则不插入
                BiErrInfo sftybiErrInfo = biYhdpMapper.selSjhSfyc(biErrInfo);
                if (sftybiErrInfo == null) {
                    Result result1 = new Result();
                    result1.setCodeContent(101, "手机号不存在");
                    return result1;
                }
                if (sftybiErrInfo.getSfty() == 1) {
                    Result result1 = new Result();
                    result1.setCodeContent(101, "手机号异常");
                    return result1;
                }


                if (selbz != null) {
                    biErrInfo.setBz(selbz.getBz());
                    biErrInfo.setBzsj(selbz.getBzsj());
                }
            } else if (biErrInfo.getType() == 2 || biErrInfo.getType() == 3 || biErrInfo.getType() == 4 || biErrInfo.getType() == 5) {//子账号异常
                selbz = biYhdpMapper.selbz(biErrInfo);
                if (selbz != null) {
                    biErrInfo.setBz(selbz.getBz());
                    biErrInfo.setBzsj(selbz.getBzsj());
                }

                BiYhdp biYhdp = biYhdpMapper.seleceSqjssj(biErrInfo);
                if (biYhdp != null) {
                    biErrInfo.setDpmc(biYhdp.getDpmc());
                    biErrInfo.setSqjssj(biYhdp.getSqjssj());//店铺的授权到期时间
                }
            } else if (biErrInfo.getType() == 6) {
                selbz = biYhdpMapper.selbz6(biErrInfo);
                if (selbz != null) {
                    biErrInfo.setBz(selbz.getBz());
                    biErrInfo.setBzsj(selbz.getBzsj());
                }
            }
            //1.先修改未处理的都改为已处理，2.查询已处理的最大记录时间的id，3.将最大id的clflag保持为0，其他修改clflag为1
            biErrInfoMapper.updBiErrInfoClzt(biErrInfo);
            BiErrInfo maxBiErrInfo = biErrInfoMapper.selBiErrInfoMaxid(biErrInfo);
            if (maxBiErrInfo != null) {
                biErrInfo.setId(maxBiErrInfo.getId());
            }
            biErrInfoMapper.updBiErrInfoClflagall(biErrInfo);
            biYhdpMapper.addBiErrInfo(biErrInfo);
            result.setCodeContent(Result.SUCCESS, "");
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage(), e);
        }
        return result;
    }

    public Result selAuthJrtm(AuthJrtm authJrtm) {
        Result result = new Result(Result.DBERROR, "数据库error");
        try {

            AuthJrtm temp = biYhdpMapper.selAuthJrtm(authJrtm);

            result.setCodeContent(Result.SUCCESS, "");
            result.putKey("data", temp);
            return result;

        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage(), e);
        }
        return result;
    }

    public Result updAuthJrtmByShopid(AuthJrtm authJrtm) {
        Result result = new Result(Result.DBERROR, "数据库error");
        try {
            biYhdpMapper.updAuthJrtmByShopid(authJrtm);
            result.setCodeContent(Result.SUCCESS, "");
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage(), e);
        }
        return result;
    }

    public Result getErr(BiYhdp data) {

        Result result = new Result(Result.DBERROR, "数据库error");
        try {
            int count = biYhdpMapper.getErr(data);
            BiErrInfo biErrInfo = biYhdpMapper.getclzt(data);
            result.setCodeContent(Result.SUCCESS, "");
            result.putKey("BiErrInfo", JSONObject.toJSON(biErrInfo));
            result.putKey("count", count);
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage(), e);
        }
        return result;

    }

    public Result selYhdpBySfbd(BiYhdp biYhdp) {
        Result result = new Result(Result.DBERROR, "数据库error");
        try {
            List<BiYhdp> list = biYhdpMapper.selYhdpBySfbd(biYhdp);
            if (list != null) {
                result.setCodeContent(Result.SUCCESS, "");
                result.putKey("list", list);
                return result;
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage(), e);
        }
        return result;
    }

    public Result updSfbdById(BiYhdp biYhdp) {
        Result result = new Result(Result.DBERROR, "数据库error");
        try {
            biYhdpMapper.updSfbdById(biYhdp);
            result.setCodeContent(Result.SUCCESS, "");
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage(), e);
        }
        return result;
    }

    public Result updZzhyc(BiYhdp biYhdp) {
        Result result = new Result(Result.DBERROR, "数据库error");
        try {
            biYhdpMapper.updZzhyc(biYhdp);
            result.setCodeContent(Result.SUCCESS, "");
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage(), e);
        }
        return result;
    }

    public Result selBiErrInfoDd(BiErrInfo data) {


        Result result = new Result(101, "失败");
        try {
            PageHelper.startPage(data.getPagenow(), data.getPagesize());

            data.setKssj(data.getKssj() + " 00:00:00");
            data.setJssj(data.getJssj() + " 23:59:59");
            List<BiErrInfo> list = biYhdpMapper.queryList(data);

            PageInfo<BiErrInfo> pageInfo = new PageInfo<BiErrInfo>(list);

            result.setCodeContent(0, "成功");
            result.putKey("biErrInfoList", list);
            result.putKey("total", pageInfo.getTotal());

            return result;
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage(), e);
        }
        return result;

    }

    public Result yichuliDd(BiErrInfo data) {

        Result result = new Result(101, "失败");
        try {
            //set库名

            BiErrInfo oldCjyc = biYhdpMapper.selOldCjyc(data);

            //处理之前需要调整clflag值
            biYhdpMapper.clycflag(data);
            if (data.getType() == 2) {
                data.setJlbdpid(oldCjyc.getJlbdpid());
                oldCjyc.setClzt(1);
                oldCjyc.setZt("1");

                String json = service2Uploader.clyc(oldCjyc);

//                String json = client.exec(ServiceUrl + "/biYywh/clyc", oldCjyc);
                if (StringUtil.isJson2(json)) {
                    int code = Result.getValueFromJson(json, "code", Integer.class);
                    String content = Result.getValueFromJson(json, "content", String.class);
                    result.setCodeContent(code, content);
                    if (code == 0) {
                        biYhdpMapper.yichuliDd(data);
                    } else {
                        result.setCodeContent(102, "接口处理失败");
                        return result;
                    }
                } else {
                    result.setCodeContent(102, "返回的不是json");
                    return result;
                }

            } else {
                biYhdpMapper.yichuliDd(data);
            }

            result.setCodeContent(0, "成功");

            return result;
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage(), e);
        }
        return result;
    }

    public Result hfcj(BiErrInfo data) {

        Result result = new Result(101, "失败");
        try {
            //set库名

            BiErrInfo oldCjyc = biYhdpMapper.selOldCjyc(data);

            //处理之前需要调整clflag值
            biYhdpMapper.clycflag(data);
            if (data.getType() == 2) {
                data.setJlbdpid(oldCjyc.getJlbdpid());
                oldCjyc.setClzt(1);
                oldCjyc.setZt("1");
                String json = service2Uploader.clyc(oldCjyc);
//                String json = client.exec(ServiceUrl + "/biYywh/clyc", oldCjyc);
                if (StringUtil.isJson2(json)) {
                    int code = Result.getValueFromJson(json, "code", Integer.class);
                    String content = Result.getValueFromJson(json, "content", String.class);
                    result.setCodeContent(code, content);
                    if (code == 0) {
                        biYhdpMapper.hfcj(data);
                    } else {
                        result.setCodeContent(102, "接口处理失败");
                        return result;
                    }
                } else {
                    result.setCodeContent(102, "返回的不是json");
                    return result;
                }

            } else {
                biYhdpMapper.hfcj(data);
            }

            result.setCodeContent(0, "成功");

            return result;
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage(), e);
        }
        return result;
    }


    public Result bzcjDd(BiErrInfo data) {

        Result result = new Result(101, "失败");
        try {
            if (data.getType() != 2) {
                result.setCodeContent(102, "此数据不能做不在采集操作!");
                return result;
            }

            biYhdpMapper.bzcj(data);

            result.setCodeContent(0, "成功");
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage(), e);
        }
        return result;
    }

    public Result getValidCstDay(BiYhdpOther data) {
        Result result = new Result(101, "失败");
        try {
            BiYhdpOther biYhdpOther = biYhdpOtherMapper.getBiYhdpOtherByUserid(data);
            result.setCodeContent(0, "成功");
            if (biYhdpOther != null) {
                result.putKey("data", biYhdpOther.getSycm_performance_valid_cst_day());
            }
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage(), e);
        }
        return result;
    }

    public void updValidCstDay(BiYhdpOther data) {
        BiYhdpOther biYhdpOther = biYhdpOtherMapper.getBiYhdpOtherByUserid(data);
        if (biYhdpOther == null) {
            biYhdpOtherMapper.insertBiyhdpOther(biYhdpOther);
        } else {
            biYhdpOtherMapper.updateBiYhdpOtherValidCstDay(data);
        }
    }


    @Transactional(rollbackFor = Exception.class)
    public Result cdsc(BiYhdpOther data) {


        Result result = new Result(101, "失败");
        try {

            //update bi.auth_all set sftb = 0 ,sfsc = 0 where id = 2207;
            //
            //update bi.auth_tb set sftb = 0 ,sfsc = 0 where taobao_user_id = '3340893301';
            //
            //# 查询出已绑定的支付宝，清除榜单记录，旧版绑定关系，有生效开始日期，生效结束日期，新版绑定关系可以直接删除
            //
            //# 建议只对接新版支付宝绑定关系
            //
            //update bi.auth_alipay set wwmc = null ,taobao_user_id = null where user_id = '2088721398854827';
            //
            //DELETE FROM bi.`bi_yhdp` WHERE `id` = 2889;
            if (data.getQd().equals("TM") || data.getQd().equals("TB") || data.getQd().equals("MN")) {
                biYhdpMapper.updAuthTb(data);
                biYhdpMapper.delAlipayBind(data);
            } else if (data.getQd().equals("PDD")) {
                biYhdpMapper.updAuthPdd(data);
            } else if (data.getQd().equals("ALBB")) {
                biYhdpMapper.updAuthAlbb(data);
            } else if (data.getQd().equals("JD")) {
                biYhdpMapper.updAuthJd(data);
            }

            biYhdpMapper.updAuthAll(data);
            biYhdpMapper.delBiYhdp(data);

            result.setCodeContent(0, "成功");

            return result;
        } catch (Exception e) {
            e.printStackTrace();
            result.setCodeContent(101, "删除失败");
            //设置异常 手动回滚事务
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }
        return result;


    }


    @SuppressWarnings({"unchecked", "rawtypes"})
    public Result updCategoryKd(BiErrInfo data) {


        Result result = new Result(101, "失败");
        try {

            String data1 = data.getData();


//            List<String> data2 = new ArrayList<String>();
//
//            data2.add("category_dykd");
//            data2.add("category_jdkd_data");
//            data2.add("category_tgckd2");
//            data2.add("category_wxsphkd_data");
//            data2.add("category_xhskd_data");
//            data2.add("category_tmkd");

            List<String> data2 = getKus();


            for (Object o : JSONArray.parseArray(data1)) {

                Map<String, String> map = new HashMap<String, String>();

                map = (Map<String, String>) o;

                if (!data2.contains(map.get("tablename"))) {
                    result.setCodeContent(101, "表名不正确");
                    return result;
                }
                String opt = map.get("opt");
                List<Map> equals1 = new ArrayList<>();
                if (opt.equals("upd")) {
                    if (map.get("equals") == null) {
                        result.setCodeContent(101, "upd语句没有equals");
                        return result;
                    }
                    equals1 = JSONArray.parseArray(String.valueOf(map.get("equals")), Map.class);
                }

                List<Map> cols = JSONArray.parseArray(String.valueOf(map.get("cols")), Map.class);


                String set = "";
                String where = "";
                String insname = "";
                String insvelue = "";

                if (opt.equals("upd")) {
                    for (int i = 0; i < equals1.size(); i++) {
                        Map<String, String> map1 = equals1.get(i);

                        for (String s : map1.keySet()) {
                            if (i != equals1.size() - 1) {
                                where += " " + s + " = '" + String.valueOf(map1.get(s)) + "' and";
                            } else {
                                where += " " + s + " = '" + String.valueOf(map1.get(s)) + "'";
                            }
                        }
                    }
                }


                for (int i = 0; i < cols.size(); i++) {
                    Map<String, String> map1 = cols.get(i);

                    for (String s : map1.keySet()) {
                        if (i != cols.size() - 1) {
                            set += s + "= '" + String.valueOf(map1.get(s)) + "',";
                        } else {
                            set += s + "= '" + String.valueOf(map1.get(s)) + "'";
                        }
                    }
                }

                for (int i = 0; i < cols.size(); i++) {
                    Map<String, String> map1 = cols.get(i);

                    for (String s : map1.keySet()) {
                        if (i != cols.size() - 1) {
                            insname += s + ",";
                            insvelue += "'" + String.valueOf(map1.get(s)) + "',";
                        } else {
                            insname += s;
                            insvelue += "'" + String.valueOf(map1.get(s)) + "'";
                        }
                    }
                }

                Map<String, String> map2 = new HashMap<String, String>();
                map2.put("tablename", map.get("tablename"));

                if (opt.equals("upd")) {
                    map2.put("set", set);
                    map2.put("where", where);
                    biYhdpMapper.updCategory(map2);
                } else {
                    map2.put("insname", insname);
                    map2.put("insvelue", insvelue);
                    biYhdpMapper.insCategory(map2);
                }

            }

            result.setCodeContent(0, "成功");
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            result.setCodeContent(101, e.getMessage());
        }
        return result;

    }

    @Autowired
    private JstOssDao jstOssDao;

    public List<String> getKus() {
        String tempfilepath = CommonConfig.getString("tempdir") + UUID.randomUUID() + ".txt";
        try {
            File jsonFile;
            String key = "biconst/categoryTableList.txt";


            jstOssDao.downloadauth(key, tempfilepath);
            //读取文件
            jsonFile = new File(tempfilepath);
            FileReader fileReader = new FileReader(jsonFile);

            Reader reader = new InputStreamReader(new FileInputStream(jsonFile), StandardCharsets.UTF_8);
            int ch = 0;
            StringBuffer json = new StringBuffer();
            while ((ch = reader.read()) != -1) {
                json.append((char) ch);
            }

            fileReader.close();
            reader.close();


            String string = json.toString();
            String[] split = string.split(",");
            List<String> list = new ArrayList<>();

            Collections.addAll(list, split);

            return list;

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            File file = new File(tempfilepath);
            if (file.exists()) {
                file.delete();
            }
        }
        return null;


    }

    public Result insQySpkMy(QySpkMy data) {

        Result result = new Result();
        result.setCodeContent(0, "正常");
        try {

            Integer count = qySpkMyMapper.selCount(data);

            if (count > 0) {
                result.setCodeContent(101, "该数据已经插入过");
                return result;
            } else {
                qySpkMyMapper.ins(data);
            }


        } catch (Exception e) {
            e.printStackTrace();
            result.setCodeContent(101, "插入失败");
        }

        return result;
    }

    public Object selQySpkMy(QySpkMy data) {

        Result result = new Result();
        result.setCodeContent(0, "正常");
        try {

            List<QySpkMy> qySpkMyList = qySpkMyMapper.selecQySpkMytList(data);

            result.putKey("data", JSONArray.toJSON(qySpkMyList));

        } catch (Exception e) {
            e.printStackTrace();
            result.setCodeContent(101, "查询失败");
        }

        return result;
    }

    /**
     * selectBiDdpushTokenExpireOnlyOne 条件查询仅一个
     */
    public Result selectBiDdpushTokenExpireOnlyOne(BiDdpushTokenExpire biDdpushTokenExpire) {
        Result result = new Result(Result.DBERROR, "数据库error");
        try {
            if (biDdpushTokenExpire == null) {
                result.setCodeContent(Result.DBERROR, "参数错误！");
                return result;
            }
            if (StringUtil.isNull2(biDdpushTokenExpire.getRq())) {
                // 默认是昨天
                biDdpushTokenExpire.setRq(DateUtil.formatDate(DateUtil.getLastDay(), "yyyy-MM-dd"));
            }
            BiDdpushTokenExpire data = biDdpushTokenExpireMapper.selectBiDdpushTokenExpireOnlyOne(biDdpushTokenExpire);
            if (data != null) {
                result.putKey("data", data);
            }
            result.setCodeContent(Result.SUCCESS, "");
            return result;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return result;
    }

    /**
     * selectBiErrInfoOnlyOne 条件查询仅一个
     */
    public Result selectBiErrInfoOnlyOne(BiErrInfo biErrInfo) {
        Result result = new Result(Result.DBERROR, "数据库error");
        try {
            if (biErrInfo == null) {
                result.setCodeContent(Result.DBERROR, "参数错误！");
                return result;
            }
            if (biErrInfo.getType() == null || biErrInfo.getType() == 0) {
                biErrInfo.setType(3);
            }
            if (biErrInfo.getJlsj() != null) {
                biErrInfo.setKssj(DateUtil.formatDate(biErrInfo.getJlsj(), "yyyy-MM-dd HH:mm:ss"));
            }
            if (StringUtil.isNull2(biErrInfo.getKssj())) {
                // 默认是昨天
                biErrInfo.setKssj(DateUtil.formatDate(DateUtil.getLastDay(), "yyyy-MM-dd HH:mm:ss"));
            }
            BiErrInfo data = biErrInfoMapper.selectBiErrInfoOnlyOne(biErrInfo);
            if (data != null) {
                result.putKey("data", data);
            }
            result.setCodeContent(Result.SUCCESS, "");
            return result;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return result;
    }

    public Result getZhZh(ZhZh data) {
        Result result = new Result(Result.DBERROR, "数据库error");
        try {

            List<ZhZh> zhs = biYhdpMapper.getZhZh(data);
            result.putKey("data", JSONArray.toJSON(zhs));
            result.setCodeContent(Result.SUCCESS, "");
            return result;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return result;

    }

    public Result updDpurlByYhidQdUserid(BiYhdp biYhdp) {
        Result result = new Result(Result.DBERROR, "数据库error");
        try {
            biYhdpMapper.updDpurlByYhidQdUserid(biYhdp);
            result.setCodeContent(Result.SUCCESS, "");
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage(), e);
        }
        return result;
    }
}
