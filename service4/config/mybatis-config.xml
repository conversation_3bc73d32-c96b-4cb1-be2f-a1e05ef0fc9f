<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE configuration
PUBLIC "-//mybatis.org//DTD Config 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-config.dtd">
<configuration>
	<!-- configuration配置属性有顺序要求：(properties?,settings?,typeAliases?,typeHandlers?,objectFactory?,objectWrapperFactory?,reflectorFactory?,plugins?,environments?,databaseIdProvider?,mappers?) -->

    <!-- Log4j日志需要在MyBatis核心文件中配置 -->
	<settings>
		<!-- 标准的日志工厂：会打印sql日志，正式服部署时不建议使用 -->
		<setting name="logImpl" value="STDOUT_LOGGING" />
		<!-- LOG4J控制日志输出，需要log4j.properties的配合 -->
		<!-- <setting name="logImpl" value="LOG4J" /> -->
	</settings>

	<!-- <typeAliases>
        <package name="com.abujlb.service4.pojo,com.abujlb.service4.bean"/>
    </typeAliases> -->
    
	<plugins>
		<plugin interceptor="com.github.pagehelper.PageInterceptor">
			<!-- 该参数默认为false，设置为true时，会将RowBounds第一个参数offset当成pageNum页码使用 -->
			<property name="offsetAsPageNum" value="true" />
			<!-- 该参数默认为false，设置为true时，使用RowBounds分页会进行count查询 -->
			<property name="rowBoundsWithCount" value="true" />
			<property name="reasonable" value="true" />
			<property name="offsetAsPageNum" value="true" />
		</plugin>
	</plugins>
</configuration>